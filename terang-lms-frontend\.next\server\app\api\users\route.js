try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="7db2df73-b2a7-4626-8800-790d53091505",e._sentryDebugIdIdentifier="sentry-dbid-7db2df73-b2a7-4626-8800-790d53091505")}catch(e){}(()=>{var e={};e.id=318,e.ids=[318],e.modules={1683:(e,t,i)=>{"use strict";i.d(t,{P:()=>r});var s=i(138);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let u=(0,s.lw)(process.env.DATABASE_URL);async function r(e,...t){return await u(e,...t)}},2242:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>A,routeModule:()=>F,serverHooks:()=>y,workAsyncStorage:()=>D,workUnitAsyncStorage:()=>x});var s={};i.r(s),i.d(s,{DELETE:()=>I,GET:()=>N,HEAD:()=>$,OPTIONS:()=>S,PATCH:()=>T,POST:()=>R,PUT:()=>m});var u=i(3690),r=i(56947),a=i(75250),n=i(63033),o=i(62187),d=i(82446),E=i(1683),l=i(7688);async function c(e){try{let t,i,{searchParams:s}=new URL(e.url),u=s.get("search"),r=s.get("role"),a=s.get("institutionId"),n=s.get("excludeClassId"),d=parseInt(s.get("limit")||"50"),l=parseInt(s.get("offset")||"0");if(u&&r&&"all"!==r&&a&&"all"!==a&&"unassigned"!==a){let e=`%${u.toLowerCase()}%`,s=parseInt(a);if(n){let u=parseInt(n);t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${u}
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
            AND u.institution_id = ${s}
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${u}
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
            AND u.institution_id = ${s}
            AND ce.student_id IS NULL
        `}else t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
            AND u.institution_id = ${s}
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
            AND u.institution_id = ${s}
        `}else if(u&&r&&"all"!==r){let e=`%${u.toLowerCase()}%`;if(n){let s=parseInt(n);t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${s}
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${s}
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
            AND ce.student_id IS NULL
        `}else t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          WHERE (LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e})
            AND u.role = ${r}
        `}else if(u){let e=`%${u.toLowerCase()}%`;t=await (0,E.P)`
        SELECT
          u.id,
          u.name,
          u.email,
          u.role,
          u.institution_id,
          u.created_at,
          u.updated_at,
          i.name as institution_name,
          i.type as institution_type
        FROM users u
        LEFT JOIN institutions i ON u.institution_id = i.id
        WHERE LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e}
        ORDER BY u.created_at DESC
        LIMIT ${d} OFFSET ${l}
      `,i=await (0,E.P)`
        SELECT COUNT(*) as total
        FROM users u
        WHERE LOWER(u.name) LIKE ${e} OR LOWER(u.email) LIKE ${e}
      `}else if(r&&"all"!==r)if(n){let e=parseInt(n);t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${e}
          WHERE u.role = ${r}
            AND u.institution_id IS NOT NULL
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${e}
          WHERE u.role = ${r}
            AND u.institution_id IS NOT NULL
            AND ce.student_id IS NULL
        `}else t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE u.role = ${r}
            AND u.institution_id IS NOT NULL
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          WHERE u.role = ${r}
            AND u.institution_id IS NOT NULL
        `;else if(a&&"unassigned"===a)t=await (0,E.P)`
        SELECT
          u.id,
          u.name,
          u.email,
          u.role,
          u.institution_id,
          u.created_at,
          u.updated_at,
          i.name as institution_name,
          i.type as institution_type
        FROM users u
        LEFT JOIN institutions i ON u.institution_id = i.id
        WHERE u.institution_id IS NULL
        ORDER BY u.created_at DESC
        LIMIT ${d} OFFSET ${l}
      `,i=await (0,E.P)`
        SELECT COUNT(*) as total
        FROM users u
        WHERE u.institution_id IS NULL
      `;else if(a&&"all"!==a){let e=parseInt(a);if(n){let s=parseInt(n);t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${s}
          WHERE u.institution_id = ${e}
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${s}
          WHERE u.institution_id = ${e}
            AND ce.student_id IS NULL
        `}else t=await (0,E.P)`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE u.institution_id = ${e}
          ORDER BY u.created_at DESC
          LIMIT ${d} OFFSET ${l}
        `,i=await (0,E.P)`
          SELECT COUNT(*) as total
          FROM users u
          WHERE u.institution_id = ${e}
        `}else t=await (0,E.P)`
        SELECT
          u.id,
          u.name,
          u.email,
          u.role,
          u.institution_id,
          u.created_at,
          u.updated_at,
          i.name as institution_name,
          i.type as institution_type
        FROM users u
        LEFT JOIN institutions i ON u.institution_id = i.id
        ORDER BY u.created_at DESC
        LIMIT ${d} OFFSET ${l}
      `,i=await (0,E.P)`
        SELECT COUNT(*) as total
        FROM users u
      `;return o.NextResponse.json({success:!0,data:{users:t,total:parseInt(i[0].total),limit:d,offset:l},message:"Users retrieved successfully"})}catch(e){return console.error("Get users error:",e),o.NextResponse.json({success:!1,error:"Failed to retrieve users"},{status:500})}}async function p(e){try{let{name:t,email:i,password:s,role:u,institutionId:r}=await e.json();if(!t||!i||!s||!u)return o.NextResponse.json({success:!1,error:"Name, email, password, and role are required"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(i))return o.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});if((await (0,E.P)`
      SELECT id FROM users WHERE email = ${i}
    `).length>0)return o.NextResponse.json({success:!1,error:"Email already exists"},{status:400});let a=await d.Ay.hash(s,10),n=await (0,E.P)`
      INSERT INTO users (
        name,
        email,
        password,
        role,
        institution_id
      ) VALUES (
        ${t},
        ${i},
        ${a},
        ${u},
        ${r||null}
      ) RETURNING id, name, email, role, institution_id, created_at, updated_at
    `;return o.NextResponse.json({success:!0,data:n[0],message:"User created successfully"})}catch(e){return console.error("Create user error:",e),o.NextResponse.json({success:!1,error:"Failed to create user"},{status:500})}}let _={...n},O="workUnitAsyncStorage"in _?_.workUnitAsyncStorage:"requestAsyncStorage"in _?_.requestAsyncStorage:void 0;function L(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,i,s)=>{let u;try{let e=O?.getStore();u=e?.headers}catch{}return l.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/users",headers:u}).apply(i,s)}})}let N=L(c,"GET"),R=L(p,"POST"),m=L(void 0,"PUT"),T=L(void 0,"PATCH"),I=L(void 0,"DELETE"),$=L(void 0,"HEAD"),S=L(void 0,"OPTIONS"),F=new u.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:D,workUnitAsyncStorage:x,serverHooks:y}=F;function A(){return(0,a.patchFetch)({workAsyncStorage:D,workUnitAsyncStorage:x})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=44725,e.exports=t},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[5250,7688,8036,138,2446],()=>i(2242));module.exports=s})();
//# sourceMappingURL=route.js.map