{"version": 3, "file": "../app/api/courses/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,0XCyBO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IA4XIC,EA5XEC,EAAeF,EAAQG,KAARH,EAAe,CAA9BE,YAA2C,CAC3CE,EAAYF,EAAaG,GAAG,CAAC,CAA7BD,KAAYF,OACZI,EAAgBJ,EAAaG,GAAG,CAAC,KAAjCC,CAAgBJ,WAChBK,EAAeL,EAAaG,GAAG,CAAC,IAAhCE,EAAeL,IAGrB,GAAqB,SAAjBK,EAAyB,CAE3B,IAAMC,EAAgB,MAAMC,EAAAA,EAAAA,CACzBC,MAAM,CAAC,CACNC,EAAAA,CAAIC,EAAAA,OAAOA,CAACD,EAAE,CACdE,IAAAA,CAAMD,EAAAA,OAAOA,CAACC,IAAI,CAClBC,WAAAA,CAAaF,EAAAA,OAAOA,CAACE,WAAW,CAChCC,UAAAA,CAAYH,EAAAA,OAAOA,CAACG,UAAU,CAC9BC,IAAAA,CAAMJ,EAAAA,OAAOA,CAACI,IAAI,CAClBC,cAAAA,CAAgBL,EAAAA,OAAOA,CAACK,cAAc,CACtCC,SAAAA,CAAWN,EAAAA,OAAOA,CAACM,SAAS,CAC5BC,OAAAA,CAASP,EAAAA,OAAOA,CAACO,OAAO,CACxBf,SAAAA,CAAWQ,EAAAA,OAAOA,CAACR,SAAS,CAC5BE,aAAAA,CAAeM,EAAAA,OAAOA,CAACN,aAAa,CACpCc,UAAAA,CAAYR,EAAAA,OAAOA,CAACQ,UAAU,CAC9BC,YAAAA,CAAcT,EAAAA,OAAOA,CAACS,YAAY,CAClCC,aAAAA,CAAeV,EAAAA,OAAOA,CAACU,aAAa,CACpCC,KAAAA,CAAOX,EAAAA,OAAOA,CAACW,KAAK,CACpBC,QAAAA,CAAUZ,EAAAA,OAAOA,CAACY,QAAQ,CAC1BC,WAAAA,CAAab,EAAAA,OAAOA,CAACa,WAAW,CAChCC,SAAAA,CAAWd,EAAAA,OAAOA,CAACc,SAAS,CAC5BC,SAAAA,CAAWf,EAAAA,OAAOA,CAACe,SAAS,CAC5BC,WAAAA,CAAaC,EAAAA,KAAKA,CAAChB,IAAI,CACvBiB,YAAAA,CAAcD,EAAAA,KAAKA,CAACE,KAAAA,CACtB,EACCC,IAAI,CAACpB,EAAAA,OAAAA,CAAAA,CACLqB,QAAQ,CAACJ,EAAAA,KAAAA,CAAOK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGtB,EAAAA,OAAAA,CAAQR,SAAS,CAAEyB,EAAAA,KAAAA,CAAMlB,EAAE,GAG3CwB,EAAoB,MAAMC,OAAAA,CAAQC,CAAlCF,EAAqC,CACzC3B,EAAc8B,GAAG,CAAC,MAAOC,CAAzB/B,GAEE,EAFuB+B,EAEjBC,CAFiBD,CAED,MAAM9B,EAAAA,EAAAA,CAAtB+B,MACG,GACNR,IAAI,CAACS,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQE,QAAQ,CAAEJ,EAAO5B,EAAE,GAGjCiC,EAAsB,MAAMR,OAAAA,CAAQC,GAAG,CAC3CG,EAAcF,GAAG,CAAC,MAAOO,CAAzBL,GACE,EADuBK,EACjBC,CADiBD,CACA,MAAMpC,EAAAA,EAAAA,CAC1BC,CADGoC,KACG,GACNd,IAAI,CAACe,EAAAA,QAAAA,CAAAA,CACLL,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGa,EAAAA,QAAAA,CAASC,QAAQ,CAAEH,EAAOlC,EAAE,GAGlCsC,EAAsB,MAAMb,OAAAA,CAAQC,GAApCY,CACJH,EAAeR,GAAG,CAAC,MAAOY,EAA1BJ,EAEE,GAFwBI,CAElBC,EAAiB,MAAM1C,EAAAA,EAAAA,CAC1BC,CADGyC,KACG,GACNnB,IAAI,CAACoB,EAAAA,OAAAA,CAAAA,CACLV,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQC,SAAS,CAAEH,EAAQvC,EAAE,GAGrC2C,EAAuB,EAAE,CACzBH,EAAeI,MAAM,CAAG,GAAG,CAC7BD,CADEH,CACc,MAAM1C,EAAAA,EAAAA,CAAtB6C,MACS,GACNtB,IAAI,CAACwB,EAAAA,SAAAA,CAAAA,CACLd,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGsB,EAAAA,SAASA,CAACC,MAAM,CAAEN,CAAc,CAAC,EAAE,CAACxC,EAAE,IAIpD,IAAM+C,EAAWC,KAAAA,CAAMC,OAAO,CAACV,EAAQW,OAAO,EAAIX,EAAQW,KAARX,EAAe,CAACZ,GAAG,CAAC,CAACuB,EAAcC,KAAdD,CACrElD,EAAAA,CAAI,CAAC,QAAQ,EAAEuC,EAAQvC,EAAE,CAAC,CAAC,CAAZuC,CAAcY,EAAAA,CAAO,CACpCC,CAD6BD,IAC7BC,CAAOF,EAAQE,KAAK,EAAI,CAAC,QAAQ,EAAED,EAAQ,GAAG,CAC9C9C,IAAAA,CAAM6C,EAAQ7C,IAAI,CAAZ6C,CAAgB,OACtBA,OAAAA,CAASA,EAAQA,KAARA,EAAe,EAAIA,EAC5BG,KAD4BH,MAC5BG,EAAa,EACbtC,SAAAA,CAAW,IAAIuC,OAAOC,WAAW,EACnC,IAAM,EAAE,CAER,MAAO,CACLvD,EAAAA,CAAIuC,EAAQvC,EAAE,CACdoD,KAAAA,CAAOb,EAAQrC,IAAI,CACnBsD,KAAAA,CAAOjB,EAAQkB,UAAU,CACzBC,UAAAA,EAAY,EACZC,oBAAAA,CAAsB,EACtBZ,QAAAA,CAAUA,EACVa,IAAAA,CAAMpB,CADIO,CACWH,MAAM,CAAG,EAAI,CAChC5C,EAAAA,CAAIwC,CAAc,CAAC,EAAE,CAACxC,EAAE,CAAC6D,QAAQ,GACjCT,KAAAA,CAAOZ,CAAc,CAAC,EAAE,CAACtC,IAAI,CAC7BG,IAAAA,CAAM,UACNwC,SAAAA,CAAWF,EAAchB,GAAG,CAACmC,CAAAA,GAAAA,GAAlBnB,CAELoB,EACJ,GAAe,QADXA,OACAD,CAAAA,CAAEzD,IAAI,CAAmB,CAE3B,IAAM2D,EAAgBF,EAAEG,OAAO,EAAEC,KAAK,GAACC,EAAiBC,SAAS,EACjE,GAAIJ,EAAe,CAEjB,IAAMK,EAAcL,EAAcd,EAFhCc,KAEIK,EAAqCH,KAAK,CAACI,EAAsB,SAAXA,CAAAA,CAAEjE,IAAI,EAClE0D,EAAgBM,GAAaE,KAAAA,EAAOC,CAApBH,UAAoBG,EAAAA,GAAkB,OAAS,OAAS,OAC1E,CACF,MAAO,GAAe,oBAAXV,CAAAA,CAAEzD,IAAI,CAAwB,CAEvC,IAAMoE,EAAeX,EAAEG,OAAO,CAAxBQ,CAA0BC,UAAU,GAAcP,EAAIC,SAAS,EACrEL,EAAiC,CAAC,IAAlBU,EAAsBA,IAAtCV,GAAqDY,CACvD,EADkBF,EAAsBA,CAClB,EAAf,CADgDE,MACxB,CAApBb,CAAAA,CAAEzD,IAAI,GAEf0D,EAAgBD,EAAEc,SAAlBb,EAAkBa,EAGpB,MAAO,CACL5E,EAAAA,CAAI8D,CAAAA,CAAE9D,EAAE,CAAC6D,QAAQ,GACjBgB,QAAAA,CAAUf,EAAEe,QAAQ,CACpBxE,IAAAA,CAAMyD,EAAEzD,IAAI,CACZ4D,OAAAA,CAASH,CAAAA,CAAEG,OAAO,EAAI,EAAE,CACxBF,aAAAA,CAAeA,EACfe,WAAAA,CAAahB,EAAEgB,WAAAA,CAEnB,GACAC,YAAAA,CAAcC,WAAWxC,CAAc,CAAC,EAAE,CAACuC,YAAY,EAAI,MAC3DE,SAAAA,CAAWzC,CAAc,CAAC,EAAE,CAACyC,SAAS,CACtCC,QAAAA,CAAU,EACVC,WAAAA,CAAa,EACbC,QAAAA,EAAU,EACZ,CAAI,CACFpF,EAAAA,CAAI,CAAC,aAAa,EAAEuC,EAAQvC,EAAE,EAAE,CAAZuC,KACpBa,CAAO,CAAC,KAAK,EAAEb,EAAQrC,IAAI,EAAE,CAC7BG,IAAAA,CAAM,UACNwC,SAAAA,CAAW,EAAE,CACbkC,YAAAA,CAAc,GACdG,QAAAA,CAAU,EACVC,WAAAA,CAAa,EACbC,QAAAA,EAAU,CACZ,CACF,CACF,IAIIC,EAAgB,MAAMvF,EAAAA,EAAAA,CAAtBuF,MACG,GACNhE,IAAI,CAACoB,EAAAA,OAAAA,CAAAA,CACLV,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQJ,QAAQ,CAAEH,EAAOlC,EAAE,GAGnCsF,EAA6B,EAAE,CAQnC,OAPID,EAAczC,MAAM,CAAG,GAAG,CAA1ByC,EACoB,MAAMvF,EAAAA,EAAAA,CACzBC,MAAM,GACNsB,IAAI,CAACwB,EAAAA,SAAAA,CAAAA,CACLd,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGsB,EAAAA,SAASA,CAACC,MAAM,CAAEuC,CAAa,CAAC,EAAE,CAACrF,EAAE,IAG5C,CACLA,EAAAA,CAAIkC,EAAOlC,EAAE,CACboD,KAAAA,CAAOlB,EAAOhC,IAAI,CAClBC,WAAAA,CAAa+B,EAAO/B,IAAP+B,OAAkB,EAAI,GACnCsB,KAAAA,CAAOtB,EAAOuB,UAAU,CACxBC,UAAAA,EAAY,EACZC,oBAAAA,CAAsB,EACtBvB,QAAAA,CAAUE,EACViD,UAAAA,CAAYF,EAAczC,IADhBN,EACsB,CAAG,EAAI,CACrCtC,CADUqF,CACVrF,CAAIqF,CAAa,CAAC,EAAE,CAACrF,EAAE,CAAC6D,QAAQ,GAChCT,KAAAA,CAAOiC,CAAa,CAAC,EAAE,CAACnF,IAAI,CAC5BG,IAAAA,CAAM,SACNwC,SAAAA,CAAWyC,EAAoB3D,GAAG,CAACmC,CAAAA,GAAAA,IAE7BC,EACJ,GAAe,QADXA,OACAD,CAAAA,CAAEzD,IAAI,CAAmB,CAE3B,IAAM2D,EAAgBF,EAAEG,OAAO,EAAzBD,KAAgC,GAACG,EAAiBC,SAAS,EACjE,GAAIJ,EAAe,CAEjB,IAAMK,EAAcL,EAAcd,EAFhCc,KAEIK,EAAqCH,KAAMI,CAAAA,EAAsB,SAAXA,CAAAA,CAAEjE,IAAI,EAClE0D,EAAgBM,GAAaE,KAAAA,EAAOC,CAApCT,UAAoCS,EAAAA,GAAkB,OAAS,OAAS,OAC1E,CACF,MAAO,GAAIV,CAAAA,qBAAEzD,IAAI,CAAwB,CAEvC,IAAMoE,EAAeX,EAAEG,OAAO,CAAxBQ,CAA0BC,UAAU,GAACP,EAAiBC,SAAS,EACrEL,EAAiC,CAAC,IAAlBU,EAAsBA,IAAtCV,GAAqDY,CACvD,EADkBF,EAAsBA,CAClB,EAAf,CADgDE,MACxB,CAApBb,CAAAA,CAAEzD,IAAI,GAEf0D,EAAgBD,EAAEc,SAAlBb,EAAkBa,EAGpB,MAAO,CACL5E,EAAAA,CAAI8D,CAAAA,CAAE9D,EAAE,CAAC6D,QAAQ,GACjBgB,QAAAA,CAAUf,EAAEe,QAAQ,CACpBxE,IAAAA,CAAMyD,EAAEzD,IAAI,CACZ4D,OAAAA,CAASH,CAAAA,CAAEG,OAAO,EAAI,EAAE,CACxBF,aAAAA,CAAeA,EACfe,WADef,CACFD,EAAEgB,WAAAA,CAEnB,GACAC,YAAAA,CAAcC,WAAWK,CAAa,CAAC,EAAE,CAACN,YAAY,EAAI,MAC1DE,SAAAA,CAAWI,CAAa,CAAC,EAAE,CAACJ,SAAS,CACrCC,QAAAA,CAAU,EACVC,WAAAA,CAAa,EACbC,QAAAA,EAAU,EACZ,CAAI,CACFpF,EAAAA,CAAI,CAAC,YAAY,EAAEkC,EAAOlC,EAAE,EAATkC,CACnBkB,KAAAA,CAAO,CAAC,KAAK,EAAElB,EAAOhC,IAAI,EAAE,CAC5BG,IAAAA,CAAM,SACNwC,SAAAA,CAAW,EAAE,CACbkC,YAAAA,CAAc,GACdG,QAAAA,CAAU,EACVC,WAAAA,CAAa,EACbC,QAAAA,EAAU,CACZ,CACF,CACF,IAIII,EAAiB,MAAM1F,EAAAA,EAAAA,CAC1BC,MAAM,GACNsB,IAAI,CAACoE,EAAAA,gBAAAA,CAAAA,CACL1D,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkE,EAAAA,gBAAgBA,CAACzD,QAAQ,CAAEJ,EAAO5B,EAAE,GAC7C0F,KAAK,CAAC,GAGHC,EAAgB,MAAM7F,EAAAA,EAAAA,CACzBC,MAAM,GACNsB,IAAI,CAACuE,EAAAA,eAAAA,CAAAA,CACL7D,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGqE,EAAAA,eAAeA,CAAC5D,QAAQ,CAAEJ,EAAO5B,EAAE,GAC5C0F,KAAK,CAAC,GAGHG,EAAc,MAAM/F,EAAAA,EAAAA,CACvBC,MAAM,GACNsB,IAAI,CAACyE,EAAAA,yBAAAA,CAAAA,CACL/D,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGuE,EAAAA,yBAAyBA,CAAC9D,QAAQ,CAAEJ,EAAO5B,EAAE,GACtD0F,KAAK,CAAC,GAGHK,EAAc,MAAMjG,EAAAA,EAAAA,CACvBC,MAAM,GACNsB,IAAI,CAAC2E,EAAAA,aAAAA,CAAAA,CACLjE,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGyE,EAAAA,aAAaA,CAAChE,QAAQ,CAAEJ,EAAO5B,EAAE,GAC1C0F,KAAK,CAAC,GAGHO,EAAwB,MAAMnG,EAAAA,EAAAA,CACjCC,MAAM,GACNsB,IAAI,CAAC6E,EAAAA,uBAAAA,CAAAA,CACLnE,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG2E,EAAAA,uBAAuBA,CAAClE,QAAQ,CAAEJ,EAAO5B,EAAE,GACpD0F,KAAK,CAAC,GAGHS,EAAgB,MAAMrG,EAAAA,EAAAA,CAAtBqG,MACG,GACN9E,IAAI,CAACoB,EAAAA,OAAAA,CAAAA,CACLV,KAAK,CACJqE,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACE7E,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAOA,CAACT,QAAQ,CAAEJ,EAAO5B,EAAE,EAAT4B,CACrBL,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAOA,CAAC4D,QAAQ,CAAE,WAGxBX,KAAK,CAAC,GAGLY,EAA4B,EAAE,CAQlC,OAPIH,EAAcvD,MAAM,CAAG,GAAG,CAC5B0D,EAAqB,MAAMxG,EAAAA,EAAAA,CACxBC,KADHuG,CACS,GACNjF,IAAI,CAACwB,EAAAA,SAAAA,CAAAA,CACLd,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGsB,EAAAA,SAASA,CAACC,MAAM,CAAEqD,CAAa,CAAC,EAAE,CAACnG,EAAE,IAG5C,CACLA,EAAAA,CAAI4B,EAAO5B,EAAE,CAAC6D,CAAVjC,OAAkB,GACtB1B,IAAAA,CAAM0B,EAAO1B,IAAI,CACjBqG,IAAAA,CAAM3E,EAAOnB,IAAPmB,MAAiB,EAAI,GAC3BzB,WAAAA,CAAayB,EAAOzB,IAAPyB,OAAkB,EAAI,GACnCxB,UAAAA,CAAYwB,EAAOxB,IAAPwB,MAAiB,EAAIA,EAAOX,IAAPW,OAAkB,EAAI,qBACvDrB,SAAAA,CAAWqB,EAAOrB,SAAS,EAAEgD,WAAAA,EAAAA,EAAiB,IAAID,OAAOC,WAAW,GACpE/C,OAAAA,CAASoB,EAAOpB,OAAO,EAAE+C,WAAAA,EAAAA,EAAiB,IAAID,OAAOC,WAAW,GAChEjD,cAAAA,CAAgBsB,EAAOtB,IAAPsB,UAAqB,EAAI,OACzC4E,cAAAA,CAAgB5E,EAAOnB,UAAU,CACjCE,aAAAA,MAAwCgE,IAAzB/C,EAAOjB,GAAkBgE,UAAL,EAAiB/C,EAAOjB,IAAPiB,SAAoB,CACxEhB,EAD2E,GAC3EA,CAAOgB,EAAOhB,KAAK,CAAGoE,UAAAA,CAAWpD,EAAOhB,IAAPgB,CAAY,EAAI,EACjDf,QAAAA,CAAUe,EAAOf,IAAPe,IAAe,EAAI,MAC7Bd,WAAAA,CAAac,EAAOd,IAAPc,OAAkB,GAAI,EACnC6E,SAAAA,CAAW7E,EAAOlB,IAAPkB,QAAmB,OAAI+C,EAClC+B,OADkC/B,GAClC+B,CAAYlB,CAAc,CAAC,EAAE,CAAG,CAC9BmB,YAAAA,CAAcnB,CAAc,CAAC,EAAE,CAACmB,YAAY,EAAgB,EAAE,CAC9DC,mBAAAA,CAAqBpB,CAAc,CAAC,EAAE,CAACoB,mBAAmB,EAAIjC,OAC9DkC,EAD8DlC,WAC9DkC,CAAerB,CAAc,CAAC,EAAE,CAACqB,aAAa,EAAgB,GAChE,MAAIlC,EACJmC,OADInC,EACJmC,CAAWnB,CAAa,CAAC,EAAE,CAAG,CAC5BoB,OAAAA,CAASpB,CAAa,CAAC,EAAE,CAACoB,OAAO,EAAI,EACrCC,QAAAA,CAAUrB,CAAa,CAAC,EAAE,CAACqB,QAAQ,EAAI,GACvCC,UAAAA,CAAYtB,CAAa,CAAC,EAAE,CAACsB,UAAU,EAAgB,GACzD,MAAItC,EACJuC,OADIvC,YACJuC,CAAqBrB,CAAW,CAAC,EAAE,CAAG,CACpCsB,SAAAA,CAAWtB,CAAW,CAAC,EAAE,CAACsB,SAAS,CAAGnC,UAAAA,CAAWa,CAAW,CAAC,EAAE,CAACsB,SAAS,EAAI,EAC7EC,cAAAA,CAAgBvB,CAAW,CAAC,EAAE,CAACuB,cAAc,EAAgB,EAAE,CAC/DC,YAAAA,CAAcxB,CAAW,CAAC,EAAE,CAACwB,YAAY,EAAgB,GAC3D,MAAI1C,EACJ2C,OADI3C,CACKoB,CAAW,CAAC,EAAE,CAAG,CACxBwB,QAAAA,CAAUxB,CAAW,CAAC,EAAE,CAACwB,QAAQ,EAAgB,EAAE,CACnDC,UAAAA,CAAYzB,CAAW,CAAC,EAAE,CAACyB,UAAU,EAAgB,EAAE,CACvDC,aAAAA,CAAe1B,CAAW,CAAC,EAAE,CAAC0B,aAAa,EAAI9C,OACjD,MAAIA,EACJ+C,OADI/C,UACJ+C,CAAmBzB,CAAqB,CAAC,EAAE,CAAG,CAC5C0B,YAAAA,CAAc1B,CAAqB,CAAC,EAAE,CAAC0B,YAAY,EAA4C,EAAE,CACjGC,UAAAA,CAAY3B,CAAqB,CAAC,EAAE,CAAC2B,UAAU,EAAgB,EAAE,CACjEC,OAAAA,CAAS5B,CAAqB,CAAC,EAAE,CAAC4B,OAAO,EAAgB,GAC3D,CAAIlD,OACJ7C,EADI6C,KACJ7C,CAASG,EACT6F,SAAAA,CAAW3B,EAAcvD,KADhBX,CACsB,CAAG,EAAI,CACpCjC,CADSmG,CACTnG,CAAImG,CAAa,CAAC,EAAE,CAACnG,EAAE,CAAC6D,QAAQ,GAChCT,KAAAA,CAAO+C,CAAa,CAAC,EAAE,CAACjG,IAAI,CAC5BG,IAAAA,CAAM,QACNwC,SAAAA,CAAWyD,EAAmB3E,GAAG,CAACmC,CAAAA,GAAAA,IAE5BC,EACJ,EAHSuC,CAGLxC,CAAAA,OADAC,SACE1D,IAAI,CAAmB,CAE3B,IAAM2D,EAAgBF,EAAEG,OAAO,EAAEC,KAAK,GAACC,EAAiBC,SAAS,EACjE,GAAIJ,EAAe,CAEjB,IAAMK,EAAcL,EAAcd,EAFhCc,KAEIK,EAAqCH,KAAMI,CAAAA,EAAsB,SAAXA,CAAAA,CAAEjE,IAAI,EAClE0D,EAAgBM,GAAaE,KAAAA,EAAOC,CAApCT,UAAoCS,EAAAA,GAAkB,OAAS,OAAS,OAC1E,CACF,MAAO,GAAe,oBAAXV,CAAAA,CAAEzD,IAAI,CAAwB,CAEvC,IAAMoE,EAAeX,EAAEG,OAAO,CAAxBQ,CAA0BC,UAAU,GAACP,EAAiBC,SAAS,EACrEL,EAAiC,CAAC,IAAlBU,EAAsBA,IAAtCV,GAAqDY,CACvD,EADkBF,EAAsBA,CAC7BX,CAAAA,CAAJ,CADgDa,MACxB,GAAlBtE,IAAI,GAEf0D,EAAgBD,EAAEc,SAAlBb,EAAkBa,EAGpB,MAAO,CACL5E,EAAAA,CAAI8D,CAAAA,CAAE9D,EAAE,CAAC6D,QAAQ,GACjBgB,QAAAA,CAAUf,EAAEe,QAAQ,CACpBxE,IAAAA,CAAMyD,EAAEzD,IAAI,CACZ4D,OAAAA,CAASH,CAAAA,CAAEG,OAAO,EAAI,EAAE,CACxBF,aAAAA,CAAeA,EACfe,WAAAA,CAAahB,EAAEgB,WAAAA,CAEnB,GACAC,YAAAA,CAAcC,WAAWmB,CAAa,CAAC,EAAE,CAACpB,YAAY,EAAI,MAC1DE,SAAAA,CAAWkB,CAAa,CAAC,EAAE,CAAClB,SAAS,CACrCC,QAAAA,CAAU,EACVC,WAAAA,CAAa,EACbC,QAAAA,CAAU,GACZ,CAAI,CACFpF,EAAAA,CAAI,aACJoD,KAAAA,CAAO,aACP/C,IAAAA,CAAM,QACNwC,SAAAA,CAAW,EAAE,CACbkC,YAAAA,CAAc,GACdG,QAAAA,CAAU,EACVC,WAAAA,CAAa,EACbC,QAAAA,EAAU,CACZ,EACA2C,WAAAA,CAAa,CACXC,UAAAA,EAAY,EACZC,WAAAA,CAAa,EACf,EACAC,eAAAA,CAAiB,GACjBC,aAAAA,CAAe,EACfC,MAAAA,CAAQ,cAERC,WAAAA,CAAaxG,EAAce,MAAM,CACjC0F,YAAAA,CAAc,CAChB,CACF,IAGF,OAAOC,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,OAAAA,EAAS,EAAMxI,OAAAA,CAASuB,CAAkB,EACvE,CAGA,GAAI,CAAC/B,EACH,OAAO8I,EADO,YACPA,CAAaC,IAAI,CAAC,CAAEE,KAAAA,CAAO,sBAAsB,CAAG,CAAEN,MAAAA,CAAQ,GAAI,GAMzE9I,EAFEK,EAEe4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGtB,CAApBX,CAAoBW,EAFH,KAEGA,CAAQN,aAAa,CAAEgJ,QAAAA,CAAShJ,IAGnC4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAHmC5B,CAAAA,CAAAA,OAGhCM,CAAQR,SAAS,CAAEkJ,QAAAA,CAASlJ,IAIlD,IAAMmJ,CAJ4CnJ,CAI3B,CAJ2BA,KAIrBK,EAAAA,EAAAA,CAC1BC,CADG6I,KACG,CAAC,CACN5I,EAAAA,CAAIC,EAAAA,OAAOA,CAACD,EAAE,CACdE,IAAAA,CAAMD,EAAAA,OAAOA,CAACC,IAAI,CAClBC,WAAAA,CAAaF,EAAAA,OAAOA,CAACE,WAAW,CAChCC,UAAAA,CAAYH,EAAAA,OAAOA,CAACG,UAAU,CAC9BC,IAAAA,CAAMJ,EAAAA,OAAOA,CAACI,IAAI,CAClBC,cAAAA,CAAgBL,EAAAA,OAAOA,CAACK,cAAc,CACtCC,SAAAA,CAAWN,EAAAA,OAAOA,CAACM,SAAS,CAC5BC,OAAAA,CAASP,EAAAA,OAAOA,CAACO,OAAO,CACxBf,SAAAA,CAAWQ,EAAAA,OAAOA,CAACR,SAAS,CAC5BE,aAAAA,CAAeM,EAAAA,OAAOA,CAACN,aAAa,CACpCc,UAAAA,CAAYR,EAAAA,OAAOA,CAACQ,UAAU,CAC9BC,YAAAA,CAAcT,EAAAA,OAAOA,CAACS,YAAY,CAClCC,aAAAA,CAAeV,EAAAA,OAAOA,CAACU,aAAa,CACpCC,KAAAA,CAAOX,EAAAA,OAAOA,CAACW,KAAK,CACpBC,QAAAA,CAAUZ,EAAAA,OAAOA,CAACY,QAAQ,CAC1BC,WAAAA,CAAab,EAAAA,OAAOA,CAACa,WAAW,CAChCC,SAAAA,CAAWd,EAAAA,OAAOA,CAACc,SAAS,CAC5BC,SAAAA,CAAWf,EAAAA,OAAOA,CAACe,SAAS,CAC5BC,WAAAA,CAAaC,EAAAA,KAAKA,CAAChB,IAAI,CACvBiB,YAAAA,CAAcD,EAAAA,KAAKA,CAACE,KAAAA,GAErBC,IAAI,CAACpB,EAAAA,OAAAA,CAAAA,CACLqB,QAAQ,CAACJ,EAAAA,KAAAA,CAAOK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGtB,EAAAA,OAAAA,CAAQR,SAAS,CAAEyB,EAAAA,KAAAA,CAAMlB,EAAE,GAC9C+B,KAAK,CAACzC,GAGHkC,EAAoB,MAAMC,GAHvBnC,CAAAA,GAGuBmC,CAAQC,CAAlCF,EAAqC,CACzCoH,EAAejH,GAAG,CAAC,MAAOC,EAA1BgH,EACE,EADwBhH,EAClByG,CADkBzG,CACJ,MAAM9B,EAAAA,CAApBuI,CAAoBvI,CACvBC,MAAM,CAAC,CAAE8I,KAAAA,CAAO/G,EAAAA,OAAOA,CAAC9B,EAAAA,CAAG,EAC3BqB,IAAI,CAACS,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,OAAAA,CAAQE,QAAQ,CAAEJ,EAAO5B,EAAE,EAAT4B,CAE9B,MAAO,CACL,GAAGA,CAAM,CACTyG,WAAAA,CAAaA,EAAYzF,MAAM,CAC/B0F,YAAAA,CAAc,EACdF,MAAAA,CAAQ,WACV,CACF,IAGF,OAAOG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,OAAAA,EAAS,EAAMxI,OAAAA,CAASuB,CAAkB,EACvE,CAAE,MAAOkH,EAAO,CAEd,EAFOA,KACPI,OAAAA,CAAQJ,KAAK,CAAC,0BAA2BA,GAClCH,EAAAA,CADkCG,WAClCH,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,wBAAwB,CACjC,CAAEN,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeW,EAAK1J,CAAoB,EAC7C,GAAI,CAEF,GAAM,MACJa,CAAI,aACJC,CAAW,YACXC,CAAU,MACVC,EAAO,EAAPA,UAAmB,gBACnBC,EAAiB,MAAM,MAAvBA,KACAC,CAAS,CACTC,SAAO,CACPf,WAAS,eACTE,CAAa,YACbc,CAAU,cACVC,CAAY,eACZC,CAAa,OACbC,CAAK,UACLC,CAAQ,aACRC,CAAW,YACX4F,CAAU,WACVI,CAAS,qBACTI,CAAmB,SACnBI,CAAO,mBACPI,CAAiB,CAClB,CAtBY,EAsBTsB,IAtBe3J,EAAQmJ,IAAI,CAAZnJ,EAyBnB,GAAI,CAACa,GAAQ,CAARA,GAAsB,CAACP,EAC1B,GADYF,IACL8I,EAAAA,EADmB5I,EAAe,QAClC4I,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,oDAAoD,CAC7D,CAAEN,MAAAA,CAAQ,GAAI,GAKlB,IAAMa,EAAU,KAAVA,CAAgBnJ,EAAAA,EAAAA,CACnBC,MAAM,GACNsB,IAAI,CAACH,EAAAA,KAAAA,CAAAA,CACLa,KAAK,CACJqE,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACE7E,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,EAAAA,KAAAA,CAAMlB,EAAE,CAAEP,GACb8B,CAAAA,EAAAA,EAAAA,CADa9B,CAAAA,CACb8B,CAAGL,EAAAA,KAAAA,CAAMvB,aAAa,CAAEA,GACxB4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,CADqBvB,CAAAA,KACrBuB,CAAMgI,IAAI,CAAE,aAGlBxD,KAAK,CAAC,GAET,GAAuB,GAAG,CAAtBuD,EAAQrG,KAARqG,CAAc,CAChB,OAAOV,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,sCAAsC,CAC/C,CAAEN,MAAAA,CAAQ,GAAI,GAKlB,IAAIe,EAAkB1I,EACtB,GAAK0I,CAAD,EAmBF,EApBoB1I,CAcH,EAdf0I,KAcqBrJ,CAMnBsJ,CAnBDD,EAaoBrJ,CACpBC,MAAM,GACNsB,IAAI,CAACpB,EAAAA,OAAAA,CAAAA,CACL8B,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACtB,EAAAA,OAAAA,CAAQQ,UAAU,CAAE0I,IAC7BzD,KAAK,CAAC,IAEI9C,CAHmBuG,CAAAA,CAAAA,GAGb,CAAG,EACpB,CADuB,MAChBZ,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,6BAA6B,CACtC,CAAEN,MAAAA,CAAQ,GAAI,EAElB,KAxBoB,CACpB,IAAIiB,EAAW,GACf,GADIA,EACG,CAACA,GAAU,CAChBF,EAjgBR,EAggBcE,OAhgBLC,EAEP,EA+fMH,EA/fAI,EAAYC,CA+fMF,EAAAA,CA/fNE,CAAKC,EAAjBF,GAFCD,CAEsC,EAFtCA,EAEsBE,IAAAA,CAAKE,MAAM,IAAY7F,QAAQ,GAAG8F,QAAQ,CAAC,EAAG,KAC3E,MAAO,GAAGC,MAASL,GAAW,IA+fxB,EA/faA,CAAAA,CA+fPH,EAAW,MAAMtJ,EAAAA,EAAAA,CACpBC,MAAM,GACNsB,IAAI,CAACpB,EAAAA,OAAAA,CAAAA,CACL8B,KAAK,CAACR,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACtB,EAAAA,OAAAA,CAAQQ,UAAU,CAAE0I,IAC7BzD,KAAK,CAAC,GACT2D,EAAWD,CAFqBD,CAAAA,IAEhCE,EAAWD,IACb,CACF,CAsCA,IAAMS,EAtCC,CAiBoB,MAAM/J,EAAAA,EAAAA,CAC9BgK,MAAM,CAAC7J,EAAAA,OAAAA,CAAAA,CACP8J,MAAM,CAAC,CACN7J,IAAAA,eACAC,WAAAA,EACAC,OACAC,GADAD,CACAC,aACAC,EACAC,SAAAA,CAAWA,EADXD,IAC2BgD,GAAhB/C,CAAgB+C,CAAK/C,GAAa,KAC7CC,CADgCD,CAAAA,KAChCC,CAASA,EAAU,IAAI8C,CAAd9C,GAAc8C,CAAK9C,GAAW,IAAXA,CAAAA,UAC5Bf,SAAAA,OACAE,EACAc,UAAAA,CADAd,eACYwJ,YACZzI,IACAC,QACAC,KADAD,MAEAE,QAAAA,MACAC,CACF,GACCkJ,SAAS,GAEwB,CAAC,EAAE,CA4CvC,OAzCItD,GACF,MAAM5G,CADJ4G,CACI5G,CADQ,CACRA,CAAGgK,MAAM,CAACrE,EAAAA,gBAAAA,CAAAA,CAAkBsE,MAAM,CAAC,CACvC/H,QAAAA,CAAU6H,EAAU7J,EAAE,CACtB2G,YAAAA,CAAcD,EAAWC,YAAY,CACrCC,mBAAAA,CAAqBF,EAAWE,mBAAmB,CACnDC,aAAAA,CAAeH,EAAWG,aAAAA,GAG1BC,GACF,MADEA,EAAW,EACPhH,CAAGgK,MAAM,CAAClE,EAAAA,eAAAA,CAAAA,CAAiBmE,MAAM,CAAC,CACtC/H,QAAAA,CAAU6H,EAAU7J,EAAE,CACtB+G,OAAAA,CAASD,EAAUC,OAAO,CAC1BC,QAAAA,CAAUF,EAAUE,QAAQ,CAC5BC,UAAAA,CAAYH,EAAUG,UAAAA,GAGtBC,GACF,MAAMpH,EAAAA,EAAAA,CAAGgK,KADP5C,CACa,CADQ,EACPpB,yBAAAA,CAAAA,CAA2BiE,MAAM,CAAC,CAChD/H,QAAAA,CAAU6H,EAAU7J,EAAE,CACtBmH,SAAAA,CAAWD,EAAoBC,SAAS,CACxCC,cAAAA,CAAgBF,EAAoBE,cAAc,CAClDC,YAAAA,CAAcH,EAAoBG,YAAAA,GAGlCC,GACF,IADEA,EAAS,EACLxH,EAAAA,CAAGgK,MAAM,CAAC9D,EAAAA,aAAAA,CAAAA,CAAe+D,MAAM,CAAC,CACpC/H,QAAAA,CAAU6H,EAAU7J,EAAE,CACtBuH,QAAAA,CAAUD,EAAQC,QAAQ,CAC1BC,UAAAA,CAAYF,EAAQE,UAAU,CAC9BC,aAAAA,CAAeH,EAAQG,aAAAA,GAGvBC,GACF,MAAM5H,EAAAA,EAAAA,CAAGgK,GADPpC,EAAmB,CACN,CAACxB,EAAAA,uBAAAA,CAAAA,CAAyB6D,MAAM,CAAC,CAC9C/H,QAAAA,CAAU6H,EAAU7J,EAAE,CACtB2H,YAAAA,CAAcD,EAAkBC,YAAY,CAC5CC,UAAAA,CAAYF,EAAkBE,UAAU,CACxCC,OAAAA,CAASH,EAAkBG,OAAAA,GAIxBU,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,CAAS,GAAM7G,MAAAA,CAAQiI,EAAWI,OAAXJ,CAAoB,8BAA8B,CAC3E,CAAEzB,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOM,EAAO,CAEd,EAFOA,KACPI,OAAAA,CAAQJ,KAAK,CAAC,yBAA0BA,GACjCH,EADiCG,CAAAA,WACjCH,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,wBAAwB,CACjC,CAAEN,MAAAA,CAAQ,GAAI,EAElB,CACF,CC/mBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,cAAc,SAClC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAe8B,EAA4B,GAAH,EAAQ,EAEnD,EAAO,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,OAA4C,EAA9B,IAAoC,EAEtD,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,4IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/src/app/api/courses/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?68da", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport {\r\n  courses,\r\n  users,\r\n  modules,\r\n  chapters,\r\n  quizzes,\r\n  questions,\r\n  courseAdmissions,\r\n  courseAcademics,\r\n  courseTuitionAndFinancing,\r\n  courseCareers,\r\n  courseStudentExperience\r\n} from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// Generate a random course code\r\nfunction generateCourseCode(): string {\r\n  const prefix = 'COURSE';\r\n  const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');\r\n  return `${prefix}${randomNum}`;\r\n}\r\n\r\n// GET /api/courses - Get courses (for teachers or public access for students)\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n    const institutionId = searchParams.get('institutionId');\r\n    const publicAccess = searchParams.get('public'); // For student access to all published courses\r\n\r\n    // If public access is requested, return all published courses\r\n    if (publicAccess === 'true') {\r\n      // Get all published courses with teacher information\r\n      const publicCourses = await db\r\n        .select({\r\n          id: courses.id,\r\n          name: courses.name,\r\n          description: courses.description,\r\n          instructor: courses.instructor,\r\n          type: courses.type,\r\n          enrollmentType: courses.enrollmentType,\r\n          startDate: courses.startDate,\r\n          endDate: courses.endDate,\r\n          teacherId: courses.teacherId,\r\n          institutionId: courses.institutionId,\r\n          courseCode: courses.courseCode,\r\n          coverPicture: courses.coverPicture,\r\n          isPurchasable: courses.isPurchasable,\r\n          price: courses.price,\r\n          currency: courses.currency,\r\n          previewMode: courses.previewMode,\r\n          createdAt: courses.createdAt,\r\n          updatedAt: courses.updatedAt,\r\n          teacherName: users.name,\r\n          teacherEmail: users.email\r\n        })\r\n        .from(courses)\r\n        .leftJoin(users, eq(courses.teacherId, users.id));\r\n\r\n      // Get module count and additional data for each course\r\n      const coursesWithCounts = await Promise.all(\r\n        publicCourses.map(async (course) => {\r\n          // Get modules for this course\r\n          const courseModules = await db\r\n            .select()\r\n            .from(modules)\r\n            .where(eq(modules.courseId, course.id));\r\n\r\n          // Get chapters for each module\r\n          const modulesWithChapters = await Promise.all(\r\n            courseModules.map(async (module) => {\r\n              const moduleChapters = await db\r\n                .select()\r\n                .from(chapters)\r\n                .where(eq(chapters.moduleId, module.id));\r\n\r\n              // Get chapters with their quizzes and contents\r\n              const chaptersWithQuizzes = await Promise.all(\r\n                moduleChapters.map(async (chapter) => {\r\n                  // Get chapter quiz\r\n                  const chapterQuizzes = await db\r\n                    .select()\r\n                    .from(quizzes)\r\n                    .where(eq(quizzes.chapterId, chapter.id));\r\n\r\n                  // Get quiz questions if quiz exists\r\n                  let quizQuestions: any[] = [];\r\n                  if (chapterQuizzes.length > 0) {\r\n                    quizQuestions = await db\r\n                      .select()\r\n                      .from(questions)\r\n                      .where(eq(questions.quizId, chapterQuizzes[0].id));\r\n                  }\r\n\r\n                  // Transform chapter content to match expected format\r\n                  const contents = Array.isArray(chapter.content) ? chapter.content.map((content: any, index: number) => ({\r\n                    id: `content-${chapter.id}-${index}`,\r\n                    title: content.title || `Content ${index + 1}`,\r\n                    type: content.type || 'text',\r\n                    content: content.content || content,\r\n                    isCompleted: false,\r\n                    createdAt: new Date().toISOString()\r\n                  })) : [];\r\n\r\n                  return {\r\n                    id: chapter.id,\r\n                    title: chapter.name,\r\n                    order: chapter.orderIndex,\r\n                    isUnlocked: true,\r\n                    completionPercentage: 0,\r\n                    contents: contents,\r\n                    quiz: chapterQuizzes.length > 0 ? {\r\n                      id: chapterQuizzes[0].id.toString(),\r\n                      title: chapterQuizzes[0].name,\r\n                      type: 'chapter' as const,\r\n                      questions: quizQuestions.map(q => {\r\n                        // Extract correct answer based on question type\r\n                        let correctAnswer;\r\n                        if (q.type === 'true_false') {\r\n                          // For true/false questions, find the correct answer from options\r\n                          const correctOption = q.options?.find((opt: any) => opt.isCorrect);\r\n                          if (correctOption) {\r\n                            // Extract the text value from the content array\r\n                            const textContent = correctOption.content?.find((c: any) => c.type === 'text');\r\n                            correctAnswer = textContent?.value?.toLowerCase() === 'true' ? 'true' : 'false';\r\n                          }\r\n                        } else if (q.type === 'multiple_choice') {\r\n                          // For multiple choice, find the index of the correct option\r\n                          const correctIndex = q.options?.findIndex((opt: any) => opt.isCorrect);\r\n                          correctAnswer = correctIndex !== -1 ? correctIndex : undefined;\r\n                        } else if (q.type === 'essay') {\r\n                          // For essay questions, use essayAnswer\r\n                          correctAnswer = q.essayAnswer;\r\n                        }\r\n\r\n                        return {\r\n                          id: q.id.toString(),\r\n                          question: q.question,\r\n                          type: q.type,\r\n                          options: q.options || [],\r\n                          correctAnswer: correctAnswer,\r\n                          explanation: q.explanation\r\n                        };\r\n                      }),\r\n                      minimumScore: parseFloat(chapterQuizzes[0].minimumScore || '70'),\r\n                      timeLimit: chapterQuizzes[0].timeLimit,\r\n                      attempts: 0,\r\n                      maxAttempts: 3,\r\n                      isPassed: false\r\n                    } : {\r\n                      id: `quiz-chapter-${chapter.id}`,\r\n                      title: `Quiz ${chapter.name}`,\r\n                      type: 'chapter' as const,\r\n                      questions: [],\r\n                      minimumScore: 70,\r\n                      attempts: 0,\r\n                      maxAttempts: 3,\r\n                      isPassed: false\r\n                    }\r\n                  };\r\n                })\r\n              );\r\n\r\n              // Get module quiz\r\n              const moduleQuizzes = await db\r\n                .select()\r\n                .from(quizzes)\r\n                .where(eq(quizzes.moduleId, module.id));\r\n\r\n              // Get module quiz questions if quiz exists\r\n              let moduleQuizQuestions: any[] = [];\r\n              if (moduleQuizzes.length > 0) {\r\n                moduleQuizQuestions = await db\r\n                  .select()\r\n                  .from(questions)\r\n                  .where(eq(questions.quizId, moduleQuizzes[0].id));\r\n              }\r\n\r\n              return {\r\n                id: module.id,\r\n                title: module.name,\r\n                description: module.description || '',\r\n                order: module.orderIndex,\r\n                isUnlocked: true, // For public view, assume all modules are visible\r\n                completionPercentage: 0,\r\n                chapters: chaptersWithQuizzes,\r\n                moduleQuiz: moduleQuizzes.length > 0 ? {\r\n                  id: moduleQuizzes[0].id.toString(),\r\n                  title: moduleQuizzes[0].name,\r\n                  type: 'module' as const,\r\n                  questions: moduleQuizQuestions.map(q => {\r\n                    // Extract correct answer based on question type\r\n                    let correctAnswer;\r\n                    if (q.type === 'true_false') {\r\n                      // For true/false questions, find the correct answer from options\r\n                      const correctOption = q.options?.find((opt: any) => opt.isCorrect);\r\n                      if (correctOption) {\r\n                        // Extract the text value from the content array\r\n                        const textContent = correctOption.content?.find((c: any) => c.type === 'text');\r\n                        correctAnswer = textContent?.value?.toLowerCase() === 'true' ? 'true' : 'false';\r\n                      }\r\n                    } else if (q.type === 'multiple_choice') {\r\n                      // For multiple choice, find the index of the correct option\r\n                      const correctIndex = q.options?.findIndex((opt: any) => opt.isCorrect);\r\n                      correctAnswer = correctIndex !== -1 ? correctIndex : undefined;\r\n                    } else if (q.type === 'essay') {\r\n                      // For essay questions, use essayAnswer\r\n                      correctAnswer = q.essayAnswer;\r\n                    }\r\n\r\n                    return {\r\n                      id: q.id.toString(),\r\n                      question: q.question,\r\n                      type: q.type,\r\n                      options: q.options || [],\r\n                      correctAnswer: correctAnswer,\r\n                      explanation: q.explanation\r\n                    };\r\n                  }),\r\n                  minimumScore: parseFloat(moduleQuizzes[0].minimumScore || '70'),\r\n                  timeLimit: moduleQuizzes[0].timeLimit,\r\n                  attempts: 0,\r\n                  maxAttempts: 3,\r\n                  isPassed: false\r\n                } : {\r\n                  id: `quiz-module-${module.id}`,\r\n                  title: `Quiz ${module.name}`,\r\n                  type: 'module' as const,\r\n                  questions: [],\r\n                  minimumScore: 70,\r\n                  attempts: 0,\r\n                  maxAttempts: 3,\r\n                  isPassed: false\r\n                }\r\n              };\r\n            })\r\n          );\r\n\r\n          // Get admissions data\r\n          const admissionsData = await db\r\n            .select()\r\n            .from(courseAdmissions)\r\n            .where(eq(courseAdmissions.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get academics data\r\n          const academicsData = await db\r\n            .select()\r\n            .from(courseAcademics)\r\n            .where(eq(courseAcademics.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get tuition and financing data\r\n          const tuitionData = await db\r\n            .select()\r\n            .from(courseTuitionAndFinancing)\r\n            .where(eq(courseTuitionAndFinancing.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get careers data\r\n          const careersData = await db\r\n            .select()\r\n            .from(courseCareers)\r\n            .where(eq(courseCareers.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get student experience data\r\n          const studentExperienceData = await db\r\n            .select()\r\n            .from(courseStudentExperience)\r\n            .where(eq(courseStudentExperience.courseId, course.id))\r\n            .limit(1);\r\n\r\n          // Get final exam data\r\n          const finalExamData = await db\r\n            .select()\r\n            .from(quizzes)\r\n            .where(\r\n              and(\r\n                eq(quizzes.courseId, course.id),\r\n                eq(quizzes.quizType, 'final')\r\n              )\r\n            )\r\n            .limit(1);\r\n\r\n          // Get final exam questions if final exam exists\r\n          let finalExamQuestions: any[] = [];\r\n          if (finalExamData.length > 0) {\r\n            finalExamQuestions = await db\r\n              .select()\r\n              .from(questions)\r\n              .where(eq(questions.quizId, finalExamData[0].id));\r\n          }\r\n\r\n          return {\r\n            id: course.id.toString(),\r\n            name: course.name,\r\n            code: course.courseCode || '',\r\n            description: course.description || '',\r\n            instructor: course.instructor || course.teacherName || 'Unknown Instructor',\r\n            startDate: course.startDate?.toISOString() || new Date().toISOString(),\r\n            endDate: course.endDate?.toISOString() || new Date().toISOString(),\r\n            enrollmentType: course.enrollmentType || 'code' as const,\r\n            enrollmentCode: course.courseCode,\r\n            isPurchasable: course.isPurchasable !== undefined ? course.isPurchasable : true,\r\n            price: course.price ? parseFloat(course.price) : 0,\r\n            currency: course.currency || 'IDR',\r\n            previewMode: course.previewMode || false,\r\n            thumbnail: course.coverPicture || undefined,\r\n            admissions: admissionsData[0] ? {\r\n              requirements: admissionsData[0].requirements as string[] || [],\r\n              applicationDeadline: admissionsData[0].applicationDeadline || undefined,\r\n              prerequisites: admissionsData[0].prerequisites as string[] || []\r\n            } : undefined,\r\n            academics: academicsData[0] ? {\r\n              credits: academicsData[0].credits || 0,\r\n              workload: academicsData[0].workload || '',\r\n              assessment: academicsData[0].assessment as string[] || []\r\n            } : undefined,\r\n            tuitionAndFinancing: tuitionData[0] ? {\r\n              totalCost: tuitionData[0].totalCost ? parseFloat(tuitionData[0].totalCost) : 0,\r\n              paymentOptions: tuitionData[0].paymentOptions as string[] || [],\r\n              scholarships: tuitionData[0].scholarships as string[] || []\r\n            } : undefined,\r\n            careers: careersData[0] ? {\r\n              outcomes: careersData[0].outcomes as string[] || [],\r\n              industries: careersData[0].industries as string[] || [],\r\n              averageSalary: careersData[0].averageSalary || undefined\r\n            } : undefined,\r\n            studentExperience: studentExperienceData[0] ? {\r\n              testimonials: studentExperienceData[0].testimonials as { name: string; feedback: string }[] || [],\r\n              facilities: studentExperienceData[0].facilities as string[] || [],\r\n              support: studentExperienceData[0].support as string[] || []\r\n            } : undefined,\r\n            modules: modulesWithChapters, // Now populated with actual modules and chapters\r\n            finalExam: finalExamData.length > 0 ? {\r\n              id: finalExamData[0].id.toString(),\r\n              title: finalExamData[0].name,\r\n              type: 'final' as const,\r\n              questions: finalExamQuestions.map(q => {\r\n                // Extract correct answer based on question type\r\n                let correctAnswer;\r\n                if (q.type === 'true_false') {\r\n                  // For true/false questions, find the correct answer from options\r\n                  const correctOption = q.options?.find((opt: any) => opt.isCorrect);\r\n                  if (correctOption) {\r\n                    // Extract the text value from the content array\r\n                    const textContent = correctOption.content?.find((c: any) => c.type === 'text');\r\n                    correctAnswer = textContent?.value?.toLowerCase() === 'true' ? 'true' : 'false';\r\n                  }\r\n                } else if (q.type === 'multiple_choice') {\r\n                  // For multiple choice, find the index of the correct option\r\n                  const correctIndex = q.options?.findIndex((opt: any) => opt.isCorrect);\r\n                  correctAnswer = correctIndex !== -1 ? correctIndex : undefined;\r\n                } else if (q.type === 'essay') {\r\n                  // For essay questions, use essayAnswer\r\n                  correctAnswer = q.essayAnswer;\r\n                }\r\n\r\n                return {\r\n                  id: q.id.toString(),\r\n                  question: q.question,\r\n                  type: q.type,\r\n                  options: q.options || [],\r\n                  correctAnswer: correctAnswer,\r\n                  explanation: q.explanation\r\n                };\r\n              }),\r\n              minimumScore: parseFloat(finalExamData[0].minimumScore || '70'),\r\n              timeLimit: finalExamData[0].timeLimit,\r\n              attempts: 0,\r\n              maxAttempts: 3,\r\n              isPassed: false\r\n            } : {\r\n              id: 'final-exam',\r\n              title: 'Final Exam',\r\n              type: 'final' as const,\r\n              questions: [],\r\n              minimumScore: 70,\r\n              attempts: 0,\r\n              maxAttempts: 3,\r\n              isPassed: false\r\n            },\r\n            certificate: {\r\n              isEligible: true, // nanti diubah, for now asssume all courses are certifiable\r\n              isGenerated: false\r\n            },\r\n            minPassingScore: 70,\r\n            totalProgress: 0,\r\n            status: 'not-started' as const,\r\n            // Additional fields for compatibility\r\n            moduleCount: courseModules.length,\r\n            studentCount: 0\r\n          };\r\n        })\r\n      );\r\n\r\n      return NextResponse.json({ success: true, courses: coursesWithCounts });\r\n    }\r\n\r\n    // Original teacher-specific logic\r\n    if (!teacherId) {\r\n      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });\r\n    }\r\n\r\n    let whereCondition;\r\n    if (institutionId) {\r\n      // Get all courses for the institution (for super admin)\r\n      whereCondition = eq(courses.institutionId, parseInt(institutionId));\r\n    } else {\r\n      // Get courses for the specific teacher\r\n      whereCondition = eq(courses.teacherId, parseInt(teacherId));\r\n    }\r\n\r\n    // Get courses with teacher information\r\n    const teacherCourses = await db\r\n      .select({\r\n        id: courses.id,\r\n        name: courses.name,\r\n        description: courses.description,\r\n        instructor: courses.instructor,\r\n        type: courses.type,\r\n        enrollmentType: courses.enrollmentType,\r\n        startDate: courses.startDate,\r\n        endDate: courses.endDate,\r\n        teacherId: courses.teacherId,\r\n        institutionId: courses.institutionId,\r\n        courseCode: courses.courseCode,\r\n        coverPicture: courses.coverPicture,\r\n        isPurchasable: courses.isPurchasable,\r\n        price: courses.price,\r\n        currency: courses.currency,\r\n        previewMode: courses.previewMode,\r\n        createdAt: courses.createdAt,\r\n        updatedAt: courses.updatedAt,\r\n        teacherName: users.name,\r\n        teacherEmail: users.email\r\n      })\r\n      .from(courses)\r\n      .leftJoin(users, eq(courses.teacherId, users.id))\r\n      .where(whereCondition);\r\n\r\n    // Get module count for each course\r\n    const coursesWithCounts = await Promise.all(\r\n      teacherCourses.map(async (course) => {\r\n        const moduleCount = await db\r\n          .select({ count: modules.id })\r\n          .from(modules)\r\n          .where(eq(modules.courseId, course.id));\r\n\r\n        return {\r\n          ...course,\r\n          moduleCount: moduleCount.length,\r\n          studentCount: 0, // TODO: Calculate actual student count from enrollments\r\n          status: 'published' // TODO: Add status field to schema or calculate based on dates\r\n        };\r\n      })\r\n    );\r\n\r\n    return NextResponse.json({ success: true, courses: coursesWithCounts });\r\n  } catch (error) {\r\n    console.error('Error fetching courses:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/courses - Create a new course\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      instructor,\r\n      type = 'self_paced',\r\n      enrollmentType = 'code',\r\n      startDate,\r\n      endDate,\r\n      teacherId,\r\n      institutionId,\r\n      courseCode,\r\n      coverPicture,\r\n      isPurchasable,\r\n      price,\r\n      currency,\r\n      previewMode,\r\n      admissions,\r\n      academics,\r\n      tuitionAndFinancing,\r\n      careers,\r\n      studentExperience\r\n    } = body;\r\n\r\n    // Validate required fields\r\n    if (!name || !teacherId || !institutionId) {\r\n      return NextResponse.json(\r\n        { error: 'Name, teacher ID, and institution ID are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify teacher exists and belongs to the institution\r\n    const teacher = await db\r\n      .select()\r\n      .from(users)\r\n      .where(\r\n        and(\r\n          eq(users.id, teacherId),\r\n          eq(users.institutionId, institutionId),\r\n          eq(users.role, 'teacher')\r\n        )\r\n      )\r\n      .limit(1);\r\n\r\n    if (teacher.length === 0) {\r\n      return NextResponse.json(\r\n        { error: 'Teacher not found or not authorized' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Generate course code if not provided\r\n    let finalCourseCode = courseCode;\r\n    if (!finalCourseCode) {\r\n      let isUnique = false;\r\n      while (!isUnique) {\r\n        finalCourseCode = generateCourseCode();\r\n        const existing = await db\r\n          .select()\r\n          .from(courses)\r\n          .where(eq(courses.courseCode, finalCourseCode))\r\n          .limit(1);\r\n        isUnique = existing.length === 0;\r\n      }\r\n    } else {\r\n      // Check if provided course code is unique\r\n      const existing = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(eq(courses.courseCode, finalCourseCode))\r\n        .limit(1);\r\n      \r\n      if (existing.length > 0) {\r\n        return NextResponse.json(\r\n          { error: 'Course code already exists' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Create the course\r\n    const courseInsertResult = await db\r\n      .insert(courses)\r\n      .values({\r\n        name,\r\n        description,\r\n        instructor,\r\n        type,\r\n        enrollmentType,\r\n        startDate: startDate ? new Date(startDate) : null,\r\n        endDate: endDate ? new Date(endDate) : null,\r\n        teacherId,\r\n        institutionId,\r\n        courseCode: finalCourseCode,\r\n        coverPicture,\r\n        isPurchasable,\r\n        price,\r\n        currency,\r\n        previewMode\r\n      })\r\n      .returning();\r\n\r\n    const newCourse = courseInsertResult[0];\r\n\r\n    // Insert related course details if provided\r\n    if (admissions) {\r\n      await db.insert(courseAdmissions).values({\r\n        courseId: newCourse.id,\r\n        requirements: admissions.requirements,\r\n        applicationDeadline: admissions.applicationDeadline,\r\n        prerequisites: admissions.prerequisites,\r\n      });\r\n    }\r\n    if (academics) {\r\n      await db.insert(courseAcademics).values({\r\n        courseId: newCourse.id,\r\n        credits: academics.credits,\r\n        workload: academics.workload,\r\n        assessment: academics.assessment,\r\n      });\r\n    }\r\n    if (tuitionAndFinancing) {\r\n      await db.insert(courseTuitionAndFinancing).values({\r\n        courseId: newCourse.id,\r\n        totalCost: tuitionAndFinancing.totalCost,\r\n        paymentOptions: tuitionAndFinancing.paymentOptions,\r\n        scholarships: tuitionAndFinancing.scholarships,\r\n      });\r\n    }\r\n    if (careers) {\r\n      await db.insert(courseCareers).values({\r\n        courseId: newCourse.id,\r\n        outcomes: careers.outcomes,\r\n        industries: careers.industries,\r\n        averageSalary: careers.averageSalary,\r\n      });\r\n    }\r\n    if (studentExperience) {\r\n      await db.insert(courseStudentExperience).values({\r\n        courseId: newCourse.id,\r\n        testimonials: studentExperience.testimonials,\r\n        facilities: studentExperience.facilities,\r\n        support: studentExperience.support,\r\n      });\r\n    }\r\n\r\n    return NextResponse.json(\r\n      { success: true, course: newCourse, message: 'Course created successfully' },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating course:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/courses',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\courses\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/courses/route\",\n        pathname: \"/api/courses\",\n        filename: \"route\",\n        bundlePath: \"app/api/courses/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\courses\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "whereCondition", "searchParams", "nextUrl", "teacherId", "get", "institutionId", "publicAccess", "publicCourses", "db", "select", "id", "courses", "name", "description", "instructor", "type", "enrollmentType", "startDate", "endDate", "courseCode", "coverPicture", "isPurchasable", "price", "currency", "previewMode", "createdAt", "updatedAt", "<PERSON><PERSON><PERSON>", "users", "teacherEmail", "email", "from", "leftJoin", "eq", "coursesWithCounts", "Promise", "all", "map", "course", "courseModules", "modules", "where", "courseId", "modulesWithChapters", "module", "moduleChapters", "chapters", "moduleId", "chaptersWithQuizzes", "chapter", "chapterQuizzes", "quizzes", "chapterId", "quizQuestions", "length", "questions", "quizId", "contents", "Array", "isArray", "content", "index", "title", "isCompleted", "Date", "toISOString", "order", "orderIndex", "isUnlocked", "completionPercentage", "quiz", "toString", "q", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "options", "find", "opt", "isCorrect", "textContent", "c", "value", "toLowerCase", "correctIndex", "findIndex", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "question", "explanation", "minimumScore", "parseFloat", "timeLimit", "attempts", "maxAttempts", "isPassed", "moduleQuizzes", "moduleQuizQuestions", "moduleQuiz", "admissionsData", "courseAdmissions", "limit", "academicsData", "courseAcademics", "tuitionData", "courseTuitionAndFinancing", "careersData", "courseCareers", "studentExperienceData", "courseStudentExperience", "finalExamData", "and", "quizType", "finalExamQuestions", "code", "enrollmentCode", "thumbnail", "admissions", "requirements", "applicationDeadline", "prerequisites", "academics", "credits", "workload", "assessment", "tuitionAndFinancing", "totalCost", "paymentOptions", "scholarships", "careers", "outcomes", "industries", "averageSalary", "studentExperience", "testimonials", "facilities", "support", "finalExam", "certificate", "isEligible", "isGenerated", "minPassingScore", "totalProgress", "status", "moduleCount", "studentCount", "NextResponse", "json", "success", "error", "parseInt", "teacherCourses", "count", "console", "POST", "body", "teacher", "role", "finalCourseCode", "existing", "isUnique", "generateCourseCode", "randomNum", "Math", "floor", "random", "padStart", "prefix", "newCourse", "insert", "values", "returning", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.OPTIONS"], "sourceRoot": ""}