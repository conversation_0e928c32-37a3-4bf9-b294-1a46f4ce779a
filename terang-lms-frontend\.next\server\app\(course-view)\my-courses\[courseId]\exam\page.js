try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="936ae692-33c9-4733-a0c3-873e52d551d1",e._sentryDebugIdIdentifier="sentry-dbid-936ae692-33c9-4733-a0c3-873e52d551d1")}catch(e){}(()=>{var e={};e.id=7190,e.ids=[7190],e.modules={2086:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Cancel01Icon",[["path",{d:"M19 5L5 19M5 5L19 19",stroke:"currentColor",key:"k0"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},9717:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=t(95500),a=t(56947),i=t(26052),n=t(13636),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(s,l);let o={children:["",{children:["(course-view)",{children:["my-courses",{children:["[courseId]",{children:["exam",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,28876)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6294)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(course-view)/my-courses/[courseId]/exam/page",pathname:"/my-courses/[courseId]/exam",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10158:(e,s,t)=>{"use strict";t.d(s,{A:()=>n,q:()=>i});var r=t(93491),a=t(91754);function i(e,s){let t=r.createContext(s),i=e=>{let{children:s,...i}=e,n=r.useMemo(()=>i,Object.values(i));return(0,a.jsx)(t.Provider,{value:n,children:s})};return i.displayName=e+"Provider",[i,function(a){let i=r.useContext(t);if(i)return i;if(void 0!==s)return s;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function n(e,s=[]){let t=[],i=()=>{let s=t.map(e=>r.createContext(e));return function(t){let a=t?.[e]||s;return r.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return i.scopeName=e,[function(s,i){let n=r.createContext(i),l=t.length;t=[...t,i];let o=s=>{let{scope:t,children:i,...o}=s,c=t?.[e]?.[l]||n,d=r.useMemo(()=>o,Object.values(o));return(0,a.jsx)(c.Provider,{value:d,children:i})};return o.displayName=s+"Provider",[o,function(t,a){let o=a?.[e]?.[l]||n,c=r.useContext(o);if(c)return c;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((s,{useScope:t,scopeName:r})=>{let a=t(e)[`__scope${r}`];return{...s,...a}},{});return r.useMemo(()=>({[`__scope${s.scopeName}`]:a}),[a])}};return t.scopeName=s.scopeName,t}(i,...s)]}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15349:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(93491),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"};let i=(e,s)=>{let t=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=1.5,className:n="",children:l,...o},c)=>{let d={ref:c,...a,width:t,height:t,strokeWidth:i,color:e,className:n,...o};return(0,r.createElement)("svg",d,s?.map(([e,s])=>(0,r.createElement)(e,{key:s.id,...s}))??[],...Array.isArray(l)?l:[l])});return t.displayName=`${e}Icon`,t}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22870:(e,s,t)=>{Promise.resolve().then(t.bind(t,36983))},28354:e=>{"use strict";e.exports=require("util")},28876:(e,s,t)=>{"use strict";let r;t.r(s),t.d(s,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>d,generateViewport:()=>m});var a=t(63033),i=t(1472),n=t(7688),l=(0,i.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\page.tsx","default");let o={...a},c="workUnitAsyncStorage"in o?o.workUnitAsyncStorage:"requestAsyncStorage"in o?o.requestAsyncStorage:void 0;r="function"==typeof l?new Proxy(l,{apply:(e,s,t)=>{let r,a,i;try{let e=c?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,i=e?.headers}catch{}return n.wrapServerComponentWithSentry(e,{componentRoute:"/(course-view)/my-courses/[courseId]/exam",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:i}).apply(s,t)}}):l;let d=void 0,u=void 0,m=void 0,x=r},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30010:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("CheckmarkCircle01Icon",[["path",{d:"M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8",stroke:"currentColor",key:"k1"}]])},31421:e=>{"use strict";e.exports=require("node:child_process")},32911:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Rotate01Icon",[["path",{d:"M20.0092 2V5.13219C20.0092 5.42605 19.6418 5.55908 19.4537 5.33333C17.6226 3.2875 14.9617 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12",stroke:"currentColor",key:"k0"}]])},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},36983:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(91754),a=t(93491),i=t(21372),n=t(56682),l=t(5827),o=t(9260),c=t(2086),d=t(37615),u=t(68451),m=t(91828),x=t(99803),p=t(15349);let h=(0,p.A)("Alert01Icon",[["path",{d:"M5.32171 9.68293C7.73539 5.41199 8.94222 3.27651 10.5983 2.72681C11.5093 2.4244 12.4907 2.4244 13.4017 2.72681C15.0578 3.27651 16.2646 5.41199 18.6783 9.68293C21.092 13.9539 22.2988 16.0893 21.9368 17.8293C21.7376 18.7866 21.2469 19.6549 20.535 20.3097C19.241 21.5 16.8274 21.5 12 21.5C7.17265 21.5 4.75897 21.5 3.46496 20.3097C2.75308 19.6549 2.26239 18.7866 2.06322 17.8293C1.70119 16.0893 2.90803 13.9539 5.32171 9.68293Z",stroke:"currentColor",key:"k0"}],["path",{d:"M12.2422 17V13C12.2422 12.5286 12.2422 12.2929 12.0957 12.1464C11.9493 12 11.7136 12 11.2422 12",stroke:"currentColor",key:"k1"}],["path",{d:"M11.992 9H12.001",stroke:"currentColor",key:"k2"}]]);var f=t(30010),g=t(32911);let v=(0,p.A)("Flag01Icon",[["path",{d:"M5.0249 21C5.04385 19.2643 5.04366 17.5541 5.0366 15.9209M5.0366 15.9209C5.01301 10.4614 4.91276 5.86186 5.19475 4.04271C5.5611 1.67939 9.39301 3.82993 13.9703 5.59842L16.0328 6.48729C17.5508 7.1415 19.7187 8.30352 18.7662 9.66084C18.3738 10.22 17.56 10.8596 16.0575 11.567L5.0366 15.9209Z",stroke:"currentColor",key:"k0"}]]);var j=t(74829),b=t(93824);let y=()=>{let e=(0,i.useParams)(),s=(0,i.useRouter)(),t=(0,i.useSearchParams)(),p=e.courseId,y=t.get("type")||"final",N=t.get("examId"),{courseData:w,updateCourseProgress:k}=(0,j.q)(),[C,A]=(0,a.useState)(0),[S,q]=(0,a.useState)({}),[P,$]=(0,a.useState)(null),[M,_]=(0,a.useState)(!1),[E,I]=(0,a.useState)(!1),[L,z]=(0,a.useState)(new Set),[D,G]=(0,a.useState)(!1),[T,R]=(0,a.useState)(null),[K,U]=(0,a.useState)(!1),[W,Q]=(0,a.useState)(!0),[F,O]=(0,a.useState)({fifteenMin:!1,fiveMin:!1,oneMin:!1}),[Z,B]=(0,a.useState)({show:!1,message:"",type:"warning"}),J=(()=>{if("final"===y)return w.finalExam;for(let e of w.modules){if(e.moduleQuiz.id===N)return e.moduleQuiz;for(let s of e.chapters)if(s.quiz.id===N)return s.quiz}return null})();(0,a.useEffect)(()=>{K&&J?.timeLimit&&null===P&&$(60*J.timeLimit)},[K,J,P]),(0,a.useEffect)(()=>{if(K&&null!==P&&P>0&&!D){let e=setInterval(()=>{$(e=>{if(null===e||e<=1)return X(),0;let s=e-1;return 900!==s||F.fifteenMin||(O(e=>({...e,fifteenMin:!0})),B({show:!0,message:"Peringatan: Sisa waktu 15 menit!",type:"warning"}),setTimeout(()=>B(e=>({...e,show:!1})),5e3)),300!==s||F.fiveMin||(O(e=>({...e,fiveMin:!0})),B({show:!0,message:"Peringatan: Sisa waktu 5 menit!",type:"warning"}),setTimeout(()=>B(e=>({...e,show:!1})),5e3)),60!==s||F.oneMin||(O(e=>({...e,oneMin:!0})),B({show:!0,message:"PERINGATAN KRITIS: Sisa waktu 1 menit!",type:"critical"}),setTimeout(()=>B(e=>({...e,show:!1})),8e3)),s})},1e3);return()=>clearInterval(e)}},[K,P,D,F]),(0,a.useEffect)(()=>{if(K&&!D){let e=e=>(e.preventDefault(),e.returnValue="","");return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)}},[K,D]);let V=e=>{if(!J?.timeLimit)return"normal";let s=e/(60*J.timeLimit);return s<=.1?"critical":s<=.25?"warning":"normal"},H=e=>{z(s=>{let t=new Set(s);return t.has(e)?t.delete(e):t.add(e),t})},X=()=>{Y()},Y=(0,a.useCallback)(()=>{if(M||!J)return;_(!0),I(!1);let e=0,t={};J.questions.forEach(s=>{let r=S[s.id]===s.correctAnswer;t[s.id]=r,r&&e++});let r=Math.round(e/J.questions.length*100);sessionStorage.setItem(`exam_answers_${N||"final"}`,JSON.stringify(S)),sessionStorage.setItem(`exam_results_${N||"final"}`,JSON.stringify(t)),sessionStorage.setItem(`exam_flags_${N||"final"}`,JSON.stringify(Array.from(L))),R({score:r,correctAnswers:e,totalQuestions:J.questions.length,results:t}),ee(r);let a=`/my-courses/${p}/exam/results?type=${y}&examId=${N||"final"}&score=${r}&correct=${e}&total=${J.questions.length}`;s.push(a)},[M,J,S]),ee=e=>{if(!J)return;let s=JSON.parse(JSON.stringify(w));if("final"===y)s.finalExam.attempts+=1,s.finalExam.lastScore=e,s.finalExam.isPassed=e>=s.finalExam.minimumScore,s.finalExam.isPassed&&s.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed)&&(s.certificate.isEligible=!0,s.certificate.completionDate=new Date().toISOString().split("T")[0],s.status="completed");else for(let t of s.modules){if(t.moduleQuiz.id===N){t.moduleQuiz.attempts+=1,t.moduleQuiz.lastScore=e,t.moduleQuiz.isPassed=e>=t.moduleQuiz.minimumScore;break}for(let s of t.chapters)if(s.quiz.id===N){s.quiz.attempts+=1,s.quiz.lastScore=e,s.quiz.isPassed=e>=s.quiz.minimumScore;break}}k(s)},es=()=>{s.push(`/my-courses/${p}`)};if(!J)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)(o.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,r.jsx)(c.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Ujian Tidak Ditemukan"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Ujian yang diminta tidak dapat ditemukan."}),(0,r.jsxs)(n.$,{onClick:es,children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]})]})})});let et=(()=>{let e=new Set;return J?.questions.forEach((s,t)=>{void 0!==S[s.id]&&""!==S[s.id]&&e.add(t)}),e})(),er=et.size/J.questions.length*100,ea=et.size>0;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50","data-sentry-component":"ExamPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"bg-white border-b shadow-sm sticky top-0 z-10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:es,className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(d.A,{className:"h-4 w-4","data-sentry-element":"ArrowLeftIcon","data-sentry-source-file":"page.tsx"}),(0,r.jsx)("span",{children:"Kembali"})]}),(0,r.jsx)(u.A,{className:"h-6 w-6 text-[var(--iai-primary)]","data-sentry-element":"TrophyIcon","data-sentry-source-file":"page.tsx"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:J.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:w.name})]})]}),K&&null!==P&&!D&&(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[J?.timeLimit&&(0,r.jsxs)("div",{className:"hidden sm:flex flex-col items-end min-w-[120px]",children:[(0,r.jsx)("div",{className:"text-xs text-gray-500 mb-1",children:"Sisa Waktu"}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full transition-all duration-1000 ${"critical"===V(P)?"bg-red-500":"warning"===V(P)?"bg-amber-500":"bg-blue-500"}`,style:{width:`${P/(60*J.timeLimit)*100}%`}})})]}),(0,r.jsxs)("div",{className:`
                  flex items-center gap-2 px-4 py-2 rounded-lg border-2 font-mono text-lg font-bold transition-all duration-300
                  ${(e=>{switch(e){case"critical":return"border-red-500 text-red-600 bg-red-50";case"warning":return"border-amber-500 text-amber-600 bg-amber-50";default:return"border-blue-500 text-blue-600 bg-blue-50"}})(V(P))}
                  ${"critical"===V(P)?"animate-pulse shadow-lg":""}
                `,children:[(0,r.jsx)(m.A,{className:"h-5 w-5"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("span",{className:"leading-tight",children:(e=>{let s=Math.floor(e/3600),t=Math.floor(e%3600/60),r=e%60;return s>0?`${s.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`})(P)}),J?.timeLimit&&(0,r.jsx)("span",{className:"text-xs opacity-75 leading-tight",children:"critical"===V(P)?"SEGERA HABIS!":"warning"===V(P)?"Perhatian":"Tersisa"})]})]})]}),!K||D?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(n.$,{variant:"outline",onClick:es,children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]}),(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>{s.push("/my-courses")},children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Dashboard"]})]}):null]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[W&&(0,r.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,r.jsx)(o.aR,{children:(0,r.jsx)(o.ZB,{className:"text-center",children:"Instruksi Ujian"})}),(0,r.jsxs)(o.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"1"})}),(0,r.jsx)("p",{className:"text-gray-700",children:"Pastikan koneksi internet Anda stabil"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"2"})}),(0,r.jsxs)("p",{className:"text-gray-700",children:["Anda memiliki waktu ",J.timeLimit?`${J.timeLimit} menit`:"tidak terbatas"," untuk menyelesaikan ujian"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"3"})}),(0,r.jsxs)("p",{className:"text-gray-700",children:["Nilai minimum untuk lulus: ",J.minimumScore,"%"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"4"})}),(0,r.jsxs)("p",{className:"text-gray-700",children:["Total soal: ",J.questions.length]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"5"})}),(0,r.jsxs)("p",{className:"text-gray-700",children:["Maksimal percobaan: ",J.maxAttempts]})]})]}),(0,r.jsxs)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h,{className:"h-5 w-5 text-amber-600"}),(0,r.jsx)("p",{className:"font-medium text-amber-800",children:"Peringatan"})]}),(0,r.jsx)("p",{className:"text-amber-700 text-sm mt-2",children:"Setelah ujian dimulai, jangan menutup browser atau meninggalkan halaman. Ujian akan otomatis diserahkan jika waktu habis."})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(n.$,{size:"lg",variant:"iai",onClick:()=>{U(!0),Q(!1),J?.timeLimit&&$(60*J.timeLimit)},children:[(0,r.jsx)(u.A,{className:"mr-2 h-5 w-5"}),"Mulai Ujian"]})})]})]}),D&&T&&(0,r.jsx)("div",{className:"max-w-2xl mx-auto space-y-6",children:(0,r.jsxs)(o.Zp,{className:`border-2 ${T.score>=J.minimumScore?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:[(0,r.jsxs)(o.aR,{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:T.score>=J.minimumScore?(0,r.jsx)(f.A,{className:"h-16 w-16 text-green-600"}):(0,r.jsx)(c.A,{className:"h-16 w-16 text-red-600"})}),(0,r.jsx)(o.ZB,{className:"text-2xl",children:T.score>=J.minimumScore?"Selamat! Anda Lulus":"Maaf, Anda Belum Lulus"})]}),(0,r.jsxs)(o.Wu,{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:[T.score,"%"]}),(0,r.jsxs)("div",{className:"text-gray-600",children:[T.correctAnswers," dari ",T.totalQuestions," soal dijawab benar"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Nilai minimum untuk lulus: ",J.minimumScore,"%"]}),(0,r.jsxs)("div",{className:"flex justify-center space-x-4 mt-6",children:[T.score<J.minimumScore&&J.attempts<J.maxAttempts&&(0,r.jsxs)(n.$,{onClick:()=>{A(0),q({}),z(new Set),G(!1),R(null),_(!1),I(!1),U(!0),J?.timeLimit&&$(60*J.timeLimit)},children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Ulangi Ujian"]}),(0,r.jsxs)(n.$,{variant:"outline",onClick:es,children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]})]})]})]})}),K&&!D&&(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"xl:col-span-3 space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["Soal ",C+1," dari ",J.questions.length]}),(0,r.jsxs)("span",{children:[Math.round(er),"% Selesai"]})]}),(0,r.jsx)(l.k,{value:er,className:"h-3"})]}),(0,r.jsx)(b.vN,{question:J.questions[C],questionNumber:C+1,totalQuestions:J.questions.length,selectedAnswer:S[J.questions[C].id],onAnswerChange:(e,s)=>{q(t=>({...t,[e]:s}))},disabled:M}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)(n.$,{variant:"outline",onClick:()=>A(e=>Math.max(0,e-1)),disabled:0===C||M,children:"Previous"}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>H(C),disabled:M,className:L.has(C)?"bg-yellow-100 border-yellow-400":"",children:[(0,r.jsx)(v,{className:`h-4 w-4 mr-2 ${L.has(C)?"text-yellow-600":""}`}),"Flag"]})}),(0,r.jsx)(n.$,{variant:"iai",onClick:()=>A(e=>Math.min(J.questions.length-1,e+1)),disabled:C===J.questions.length-1||M,children:"Next"})]})]}),(0,r.jsx)("div",{className:"xl:col-span-1",children:(0,r.jsx)(b.b2,{questions:J.questions,currentQuestion:C,answeredQuestions:et,onQuestionSelect:e=>{A(e)},flaggedQuestions:L,onToggleFlag:H,onSubmit:()=>{I(!0)},canSubmit:ea,isSubmitting:M})})]})]}),E&&(0,r.jsx)("div",{className:"fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50",children:(0,r.jsxs)(o.Zp,{className:"w-full max-w-md mx-4 shadow-2xl border-2",children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center space-x-2 text-amber-700",children:[(0,r.jsx)(h,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:"Konfirmasi Penyerahan"})]})}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600",children:"Apakah Anda yakin ingin menyerahkan ujian? Pastikan semua jawaban sudah benar."}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:et.size}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Terjawab"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-400",children:J.questions.length-et.size}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Belum"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",className:"flex-1",onClick:()=>I(!1),children:"Batal"}),(0,r.jsx)(n.$,{variant:"iai",className:"flex-1",onClick:Y,disabled:M,children:M?"Menyerahkan...":"Ya, Serahkan"})]})]})]})}),Z.show&&(0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2",children:(0,r.jsxs)("div",{className:`flex items-center space-x-3 rounded-lg px-6 py-4 shadow-lg border-2 ${"critical"===Z.type?"bg-red-50 text-red-800 border-red-200":"bg-amber-50 text-amber-800 border-amber-200"} min-w-[320px]`,children:[(0,r.jsx)("div",{className:`flex-shrink-0 ${"critical"===Z.type?"animate-pulse":""}`,children:(0,r.jsx)(m.A,{className:`h-6 w-6 ${"critical"===Z.type?"text-red-600":"text-amber-600"}`})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-sm",children:Z.message}),(0,r.jsx)("p",{className:"text-xs opacity-75 mt-1",children:"critical"===Z.type?"Segera serahkan ujian Anda!":"Pastikan untuk menyerahkan ujian tepat waktu."})]})]})})]})}},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59822:(e,s,t)=>{Promise.resolve().then(t.bind(t,28876))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66536:(e,s,t)=>{"use strict";t.d(s,{C1:()=>y,bL:()=>b});var r=t(93491),a=t(10158),i=t(90604),n=t(91754),l="Progress",[o,c]=(0,a.A)(l),[d,u]=o(l),m=r.forwardRef((e,s)=>{var t,r;let{__scopeProgress:a,value:l=null,max:o,getValueLabel:c=h,...u}=e;(o||0===o)&&!v(o)&&console.error((t=`${o}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=v(o)?o:100;null===l||j(l,m)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=j(l,m)?l:null,p=g(x)?c(x,m):void 0;return(0,n.jsx)(d,{scope:a,value:x,max:m,children:(0,n.jsx)(i.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":g(x)?x:void 0,"aria-valuetext":p,role:"progressbar","data-state":f(x,m),"data-value":x??void 0,"data-max":m,...u,ref:s})})});m.displayName=l;var x="ProgressIndicator",p=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,a=u(x,t);return(0,n.jsx)(i.sG.div,{"data-state":f(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function j(e,s){return g(e)&&!isNaN(e)&&e<=s&&e>=0}p.displayName=x;var b=m,y=p},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},90604:(e,s,t)=>{"use strict";t.d(s,{hO:()=>o,sG:()=>l});var r=t(93491),a=t(52410),i=t(16435),n=t(91754),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,i.TL)(`Primitive.${s}`),a=r.forwardRef((e,r)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?t:s,{...i,ref:r})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function o(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},91828:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Clock01Icon",[["circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",key:"k0"}],["path",{d:"M12 8V12L14 14",stroke:"currentColor",key:"k1"}]])},94735:e=>{"use strict";e.exports=require("events")},99803:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Home01Icon",[["path",{d:"M15.0002 17C14.2007 17.6224 13.1505 18 12.0002 18C10.85 18 9.79977 17.6224 9.00024 17",stroke:"currentColor",key:"k0"}],["path",{d:"M2.35164 13.2135C1.99862 10.9162 1.82211 9.76763 2.25641 8.74938C2.69071 7.73112 3.65427 7.03443 5.58138 5.64106L7.02123 4.6C9.41853 2.86667 10.6172 2 12.0002 2C13.3833 2 14.582 2.86667 16.9793 4.6L18.4191 5.64106C20.3462 7.03443 21.3098 7.73112 21.7441 8.74938C22.1784 9.76763 22.0019 10.9162 21.6489 13.2135L21.3478 15.1724C20.8474 18.4289 20.5972 20.0572 19.4292 21.0286C18.2613 22 16.5539 22 13.1391 22H10.8614C7.44658 22 5.73915 22 4.57124 21.0286C3.40333 20.0572 3.15311 18.4289 2.65267 15.1724L2.35164 13.2135Z",stroke:"currentColor",key:"k1"}]])}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[5250,7688,881,7969,8134,2095,8560,9678],()=>t(9717));module.exports=r})();
//# sourceMappingURL=page.js.map