try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b693612b-d4af-4a80-997b-65d7ee72cc1b",e._sentryDebugIdIdentifier="sentry-dbid-b693612b-d4af-4a80-997b-65d7ee72cc1b")}catch(e){}"use strict";exports.id=3077,exports.ids=[3077],exports.modules={7018:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},18227:(e,t,r)=>{r.d(t,{G$:()=>$,Hs:()=>w,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(93491),o=r(18682),a=r(42014),i=r(10158),l=r(62962),s=r(76322),u=r(43748),c=r(19144),d=r(62253),p=r(55462),f=r(90604),h=r(11806),g=r(78011),y=r(2656),v=r(16435),m=r(91754),x="Dialog",[b,w]=(0,i.A)(x),[C,k]=b(x),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:a??!1,onChange:i,caller:x});return(0,m.jsx)(C,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};R.displayName=x;var T="DialogTrigger",D=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=k(T,r),l=(0,a.s)(t,i.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":V(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});D.displayName=T;var j="DialogPortal",[E,A]=b(j,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=k(j,t);return(0,m.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,m.jsx)(p.C,{present:r||i.open,children:(0,m.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};I.displayName=j;var M="DialogOverlay",F=n.forwardRef((e,t)=>{let r=A(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(M,e.__scopeDialog);return a.modal?(0,m.jsx)(p.C,{present:n||a.open,children:(0,m.jsx)(_,{...o,ref:t})}):null});F.displayName=M;var L=(0,v.TL)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(M,r);return(0,m.jsx)(g.A,{as:L,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":V(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",P=n.forwardRef((e,t)=>{let r=A(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(O,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||a.open,children:a.modal?(0,m.jsx)(N,{...o,ref:t}):(0,m.jsx)(B,{...o,ref:t})})});P.displayName=O;var N=n.forwardRef((e,t)=>{let r=k(O,e.__scopeDialog),i=n.useRef(null),l=(0,a.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,y.Eq)(e)},[]),(0,m.jsx)(G,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=k(O,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,m.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),G=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,d=k(O,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,m.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":V(d.open),...s,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(X,{titleId:d.titleId}),(0,m.jsx)(Y,{contentRef:p,descriptionId:d.descriptionId})]})]})}),H="DialogTitle",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(H,r);return(0,m.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});S.displayName=H;var q="DialogDescription",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(q,r);return(0,m.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});K.displayName=q;var z="DialogClose",U=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=k(z,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function V(e){return e?"open":"closed"}U.displayName=z;var Z="DialogTitleWarning",[$,W]=(0,i.q)(Z,{contentName:O,titleName:H,docsSlug:"dialog"}),X=({titleId:e})=>{let t=W(Z),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Y=({contentRef:e,descriptionId:t})=>{let r=W("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},J=R,Q=D,ee=I,et=F,er=P,en=S,eo=K,ea=U},31619:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},37980:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},41867:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},44331:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},63890:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]])},69622:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},84386:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},85629:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(55732).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},92023:(e,t,r)=>{r.d(t,{RG:()=>w,bL:()=>I,q7:()=>M});var n=r(93491),o=r(18682),a=r(62671),i=r(42014),l=r(10158),s=r(62962),u=r(90604),c=r(62242),d=r(76322),p=r(78283),f=r(91754),h="rovingFocusGroup.onEntryFocus",g={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[v,m,x]=(0,a.N)(y),[b,w]=(0,l.A)(y,[x]),[C,k]=b(y),R=n.forwardRef((e,t)=>(0,f.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(T,{...e,ref:t})})}));R.displayName=y;var T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:s,currentTabStopId:v,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:k=!1,...R}=e,T=n.useRef(null),D=(0,i.s)(t,T),j=(0,p.jH)(s),[E,I]=(0,d.i)({prop:v,defaultProp:x??null,onChange:b,caller:y}),[M,F]=n.useState(!1),L=(0,c.c)(w),_=m(r),O=n.useRef(!1),[P,N]=n.useState(0);return n.useEffect(()=>{let e=T.current;if(e)return e.addEventListener(h,L),()=>e.removeEventListener(h,L)},[L]),(0,f.jsx)(C,{scope:r,orientation:a,dir:j,loop:l,currentTabStopId:E,onItemFocus:n.useCallback(e=>I(e),[I]),onItemShiftTab:n.useCallback(()=>F(!0),[]),onFocusableItemAdd:n.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>N(e=>e-1),[]),children:(0,f.jsx)(u.sG.div,{tabIndex:M||0===P?-1:0,"data-orientation":a,...R,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(h,g);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),k)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>F(!1))})})}),D="RovingFocusGroupItem",j=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:c,...d}=e,p=(0,s.B)(),h=l||p,g=k(D,r),y=g.currentTabStopId===h,x=m(r),{onFocusableItemAdd:b,onFocusableItemRemove:w,currentTabStopId:C}=g;return n.useEffect(()=>{if(a)return b(),()=>w()},[a,b,w]),(0,f.jsx)(v.ItemSlot,{scope:r,id:h,focusable:a,active:i,children:(0,f.jsx)(u.sG.span,{tabIndex:y?0:-1,"data-orientation":g.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?g.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>g.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void g.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,g.orientation,g.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=g.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>A(r))}}),children:"function"==typeof c?c({isCurrentTabStop:y,hasTabStop:null!=C}):c})})});j.displayName=D;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var I=R,M=j},99830:(e,t,r)=>{r.d(t,{Kq:()=>z,UC:()=>$,ZL:()=>Z,bL:()=>U,i3:()=>W,l9:()=>V});var n=r(93491),o=r(18682),a=r(42014),i=r(10158),l=r(43748),s=r(62962),u=r(21370),c=r(62253),d=r(55462),p=r(90604),f=r(16435),h=r(76322),g=r(53291),y=r(91754),[v,m]=(0,i.A)("Tooltip",[u.Bk]),x=(0,u.Bk)(),b="TooltipProvider",w="tooltip.open",[C,k]=v(b),R=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:i}=e,l=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,y.jsx)(C,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:a,children:i})};R.displayName=b;var T="Tooltip",[D,j]=v(T),E=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:a,onOpenChange:i,disableHoverableContent:l,delayDuration:c}=e,d=k(T,e.__scopeTooltip),p=x(t),[f,g]=n.useState(null),v=(0,s.B)(),m=n.useRef(0),b=l??d.disableHoverableContent,C=c??d.delayDuration,R=n.useRef(!1),[j,E]=(0,h.i)({prop:o,defaultProp:a??!1,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),i?.(e)},caller:T}),A=n.useMemo(()=>j?R.current?"delayed-open":"instant-open":"closed",[j]),I=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,R.current=!1,E(!0)},[E]),M=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,E(!1)},[E]),F=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{R.current=!0,E(!0),m.current=0},C)},[C,E]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,y.jsx)(u.bL,{...p,children:(0,y.jsx)(D,{scope:t,contentId:v,open:j,stateAttribute:A,trigger:f,onTriggerChange:g,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?F():I()},[d.isOpenDelayedRef,F,I]),onTriggerLeave:n.useCallback(()=>{b?M():(window.clearTimeout(m.current),m.current=0)},[M,b]),onOpen:I,onClose:M,disableHoverableContent:b,children:r})})};E.displayName=T;var A="TooltipTrigger",I=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,l=j(A,r),s=k(A,r),c=x(r),d=n.useRef(null),f=(0,a.s)(t,d,l.onTriggerChange),h=n.useRef(!1),g=n.useRef(!1),v=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,y.jsx)(u.Mz,{asChild:!0,...c,children:(0,y.jsx)(p.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(g.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),g.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),g.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),h.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});I.displayName=A;var M="TooltipPortal",[F,L]=v(M,{forceMount:void 0}),_=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,a=j(M,t);return(0,y.jsx)(F,{scope:t,forceMount:r,children:(0,y.jsx)(d.C,{present:r||a.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};_.displayName=M;var O="TooltipContent",P=n.forwardRef((e,t)=>{let r=L(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,i=j(O,e.__scopeTooltip);return(0,y.jsx)(d.C,{present:n||i.open,children:i.disableHoverableContent?(0,y.jsx)(S,{side:o,...a,ref:t}):(0,y.jsx)(N,{side:o,...a,ref:t})})}),N=n.forwardRef((e,t)=>{let r=j(O,e.__scopeTooltip),o=k(O,e.__scopeTooltip),i=n.useRef(null),l=(0,a.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=i.current,{onPointerInTransitChange:f}=o,h=n.useCallback(()=>{u(null),f(!1)},[f]),g=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&p){let e=e=>g(e,p),t=e=>g(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,g,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,u=i.y,c=l.x,d=l.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,h]),(0,y.jsx)(S,{...e,ref:l})}),[B,G]=v(T,{isInside:!1}),H=(0,f.Dc)("TooltipContent"),S=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=j(O,r),p=x(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,y.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,y.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,y.jsx)(H,{children:o}),(0,y.jsx)(B,{scope:r,isInside:!0,children:(0,y.jsx)(g.bL,{id:d.contentId,role:"tooltip",children:a||o})})]})})});P.displayName=O;var q="TooltipArrow",K=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=x(r);return G(q,r).isInside?null:(0,y.jsx)(u.i3,{...o,...n,ref:t})});K.displayName=q;var z=R,U=E,V=I,Z=_,$=P,W=K}};
//# sourceMappingURL=3077.js.map