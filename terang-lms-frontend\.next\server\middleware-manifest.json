{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|static|.*\\..*|_static|_vercel).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "h_gaie4GBC2dfdb4QRvtg", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "dcbc69554b81010736ba1623790afdca", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1b515c6405c1d5ca704cc7b82580f55ec55744ee4fc15804d98af402ffe69a77", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0f3a5823f9653daa0ba91a5fbcef49135bb92cacc8d470bec2f6f68f6acc8179"}}}, "functions": {}, "sortedMiddleware": ["/"]}