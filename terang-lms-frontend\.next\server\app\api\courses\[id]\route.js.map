{"version": 3, "file": "../app/api/courses/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,qaCqBO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAWC,EADIH,MACJG,CAASF,EAAAA,CAAAA,GAEtBG,MAAMF,GACR,KADQA,CAAAA,CACDG,CADY,CACZA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,IAAMC,EAAa,MAAMC,EAAAA,EAAAA,CACtBC,MAAM,CAAC,CACNV,EAAAA,CAAIW,EAAAA,OAAOA,CAACX,EAAE,CACdY,IAAAA,CAAMD,EAAAA,OAAOA,CAACC,IAAI,CAClBC,WAAAA,CAAaF,EAAAA,OAAOA,CAACE,WAAW,CAChCC,UAAAA,CAAYH,EAAAA,OAAOA,CAACG,UAAU,CAC9BC,IAAAA,CAAMJ,EAAAA,OAAOA,CAACI,IAAI,CAClBC,cAAAA,CAAgBL,EAAAA,OAAOA,CAACK,cAAc,CACtCC,SAAAA,CAAWN,EAAAA,OAAOA,CAACM,SAAS,CAC5BC,OAAAA,CAASP,EAAAA,OAAOA,CAACO,OAAO,CACxBC,SAAAA,CAAWR,EAAAA,OAAOA,CAACQ,SAAS,CAC5BC,aAAAA,CAAeT,EAAAA,OAAOA,CAACS,aAAa,CACpCC,UAAAA,CAAYV,EAAAA,OAAOA,CAACU,UAAU,CAC9BC,YAAAA,CAAcX,EAAAA,OAAOA,CAACW,YAAY,CAClCC,aAAAA,CAAeZ,EAAAA,OAAOA,CAACY,aAAa,CACpCC,KAAAA,CAAOb,EAAAA,OAAOA,CAACa,KAAK,CACpBC,QAAAA,CAAUd,EAAAA,OAAOA,CAACc,QAAQ,CAC1BC,WAAAA,CAAaf,EAAAA,OAAOA,CAACe,WAAW,CAChCC,SAAAA,CAAWhB,EAAAA,OAAOA,CAACgB,SAAS,CAC5BC,SAAAA,CAAWjB,EAAAA,OAAOA,CAACiB,SAAS,CAC5BC,WAAAA,CAAaC,EAAAA,KAAKA,CAAClB,IAAI,CACvBmB,YAAAA,CAAcD,EAAAA,KAAKA,CAACE,KAAAA,CACtB,EACCC,IAAI,CAACtB,EAAAA,OAAAA,CAAAA,CACLuB,QAAQ,CAACJ,EAAAA,KAAAA,CAAOK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGxB,EAAAA,OAAOA,CAACQ,SAAS,CAAEW,EAAAA,KAAKA,CAAC9B,EAAE,GAC9CoC,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGxB,EAAAA,OAAOA,CAACX,EAAE,CAAEC,IACrBoC,IADqBpC,CAAAA,CAAAA,GAGxB,GAA0B,GAAG,CAAzBO,EAAW8B,MAAM,CACnB,CADE9B,MACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGxE,IAAMgC,EAAS/B,CAAU,CAAC,EAApB+B,CAGA,CAACC,EAAgBC,EAAeC,EAAaC,EAAaC,EAAsB,CAAG,GAAlFJ,CAAgBC,EAA4BE,OAA4CE,CAAQC,GAAG,CAAC,CACzGrC,EAAAA,EAAAA,CAAGC,MAAM,GAAGuB,IAAI,CAACc,EAAAA,gBAAAA,CAAAA,CAAkBX,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGY,EAAAA,gBAAAA,CAAiB9C,QAAQ,CAAEA,IAAWoC,IAAXpC,CAAAA,CAAAA,GACvEQ,EAAAA,EAAAA,CAAGC,MAAM,GAAGuB,IAAI,CAACe,EAAAA,eAAAA,CAAAA,CAAiBZ,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGa,EAAAA,eAAAA,CAAgB/C,QAAQ,CAAEA,IAAWoC,IAAXpC,CAAAA,CAAAA,GACrEQ,EAAAA,EAAAA,CAAGC,MAAM,GAAGuB,IAAI,CAACgB,EAAAA,yBAAAA,CAAAA,CAA2Bb,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGc,EAAAA,yBAAAA,CAA0BhD,QAAQ,CAAEA,IAAWoC,IAAXpC,CAAAA,CAAAA,GACzFQ,EAAAA,EAAAA,CAAGC,MAAM,GAAGuB,IAAI,CAACiB,EAAAA,aAAAA,CAAAA,CAAed,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,aAAAA,CAAcjD,QAAQ,CAAEA,IAAWoC,IAAXpC,CAAAA,CAAAA,GACjEQ,EAAAA,EAAAA,CAAGC,MAAM,GAAGuB,IAAI,CAACkB,EAAAA,uBAAAA,CAAAA,CAAyBf,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGgB,EAAAA,uBAAAA,CAAwBlD,QAAQ,CAAEA,IAAWoC,IAAXpC,CAAgB,CAAC,GACvG,EAGKmD,EAAgB,MAAM3C,EAAAA,EAAAA,CAAtB2C,MACG,GACNnB,IAAI,CAACoB,EAAAA,OAAAA,CAAAA,CACLjB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQpD,QAAQ,CAAEA,IAGxBqD,EAAsB,EAHErD,CAAAA,CAAAA,EAGI4C,OAAAA,CAAQC,GAApCQ,CACJF,EAAcG,GAAG,CAAC,MAAOC,CAAzBJ,GACE,EADuBI,EACjBC,CADiBD,CACA,MAAM/C,EAAAA,EAAAA,CAC1BC,CADG+C,KACG,GACNxB,IAAI,CAACyB,EAAAA,QAAAA,CAAAA,CACLtB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGuB,EAAAA,QAAAA,CAASC,QAAQ,CAAEH,EAAOxD,EAAE,GAGlC4D,EAAsB,MAAMf,OAAAA,CAAQC,GAApCc,CACJH,EAAeF,GAAG,CAAC,MAAOM,EAA1BJ,EACE,GADwBI,CAClBC,EAAiB,MAAMrD,EAAAA,EAAAA,CAC1BC,CADGoD,KACG,GACN7B,IAAI,CAAC8B,EAAAA,OAAAA,CAAAA,CACL3B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG4B,EAAAA,OAAAA,CAAQC,SAAS,CAAEH,EAAQ7D,EAAE,GAGnCiE,EAAuB,MAAMpB,OAAAA,CAAQC,GAAG,CAC5CgB,EAAeP,GAAG,CAAC,MAAOW,EAA1BJ,EAA0BI,GAAAA,CAClBC,EAAgB,MAAM1D,EAAAA,EAAAA,CAAtB0D,MACG,GACNlC,IAAI,CAACmC,EAAAA,SAAAA,CAAAA,CACLhC,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGiC,EAAAA,SAAAA,CAAUC,MAAM,CAAEH,EAAKlE,EAAE,GAErC,MAAO,CACL,GAAGkE,CAAI,CACPE,SAAAA,CAAWD,CACb,CACF,IAGF,MAAO,CACL,GAAGN,CAAO,CACVE,OAAAA,CAASE,CACX,CACF,IAGF,MAAO,CACL,GAAGT,CAAM,CACTE,QAAAA,CAAUE,CACZ,CACF,IAKIU,EAAgB,MAAM7D,EAAAA,EAAAA,CAAtB6D,MACG,CAAC,CACNtE,EAAAA,CAAI+D,EAAAA,OAAOA,CAAC/D,EAAE,CACdY,IAAAA,CAAMmD,EAAAA,OAAOA,CAACnD,IAAI,CAClBC,WAAAA,CAAakD,EAAAA,OAAOA,CAAClD,WAAW,CAChCmD,SAAAA,CAAWD,EAAAA,OAAOA,CAACC,SAAS,CAC5BL,QAAAA,CAAUI,EAAAA,OAAOA,CAACJ,QAAQ,CAC1B1D,QAAAA,CAAU8D,EAAAA,OAAOA,CAAC9D,QAAQ,CAC1BsE,QAAAA,CAAUR,EAAAA,OAAOA,CAACQ,QAAQ,CAC1BC,YAAAA,CAAcT,EAAAA,OAAOA,CAACS,YAAY,CAClCC,SAAAA,CAAWV,EAAAA,OAAOA,CAACU,SAAS,CAC5BxD,SAAAA,CAAW8C,EAAAA,OAAOA,CAAC9C,SAAS,CAC5BC,OAAAA,CAAS6C,EAAAA,OAAOA,CAAC7C,OAAO,CACxBwD,QAAAA,CAAUX,EAAAA,OAAOA,CAACW,QAAQ,CAC1B/C,SAAAA,CAAWoC,EAAAA,OAAOA,CAACpC,SAAS,CAC5BC,SAAAA,CAAWmC,EAAAA,OAAOA,CAACnC,SAAAA,GAEpBK,IAAI,CAAC8B,EAAAA,OAAAA,CAAAA,CACL7B,QAAQ,CAACmB,EAAAA,OAAAA,CAASlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG4B,EAAAA,OAAAA,CAAQJ,QAAQ,CAAEN,EAAAA,OAAAA,CAAQrD,EAAE,GACjDoC,KAAK,CACJuC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG4B,EAAAA,OAAAA,CAAQ9D,QAAQ,CAAEA,GACrB2E,CAAAA,EAAAA,EAAAA,CADqB3E,CACrB2E,CAAAA,CAAIzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACkB,EAAAA,OAAAA,CAAQpD,QAAQ,CAAEA,GAAWkC,CAAAA,EAAAA,EAAAA,CAAXlC,CAAWkC,CAAAA,CAAG4B,EAAAA,OAAAA,CAAQQ,QAAQ,CAAE,aAKzDM,EAA6B,MAAMhC,OAAAA,CAAQC,GAAG,CAClDwB,EAAcf,GAAG,CAAC,MAAOW,CAAzBI,GAAyBJ,GAAAA,CACjBC,EAAgB,MAAM1D,EAAAA,EAAAA,CAAtB0D,MACG,GACNlC,IAAI,CAACmC,EAAAA,SAAAA,CAAAA,CACLhC,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGiC,EAAAA,SAAAA,CAAUC,MAAM,CAAEH,EAAKlE,EAAE,GAErC,MAAO,CACL,GAAGkE,CAAI,CACPE,SAAAA,CAAWD,CACb,CACF,IAIIW,EAAkB,MAAMrE,EAAAA,EAAAA,CAC3BC,EADGoE,IACG,CAAC,CAAEC,KAAAA,CAAOC,EAAAA,iBAAiBA,CAAChF,EAAAA,CAAG,EACrCiC,IAAI,CAAC+C,EAAAA,iBAAAA,CAAAA,CACL5C,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG6C,EAAAA,iBAAAA,CAAkB/E,QAAQ,CAAEA,IAElCgF,EAAe,EAFmBhF,CAAAA,CAAAA,EAEbQ,EAAAA,EAAAA,CACxBC,MAAM,CAAC,CAAEqE,KAAAA,CAAOG,EAAAA,kBAAkBA,CAAClF,EAAAA,CAAG,EACtCiC,IAAI,CAACiD,EAAAA,kBAAAA,CAAAA,CACL9C,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG+C,EAAAA,kBAAAA,CAAmBjF,QAAQ,CAAEA,IAEzC,IAFyCA,CAAAA,CAAAA,CAElCG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvB8E,OAAAA,EAAS,EACT5C,MAAAA,CAAQ,CACN,GAAGA,CAAM,CACTc,OAAAA,CAASC,EACTgB,aAAAA,CAAeO,EACfO,CAFS9B,cAET8B,CAAiBN,EAAgBxC,MADlBuC,CAEfQ,YAAAA,CAAcJ,EAAa3C,MAAM,CAEjCgD,UAAAA,CAAY9C,CAAc,CAAC,EAAE,EAAI,KACjC+C,SAAAA,CAAW9C,CAAa,CAAC,EAAE,EAAI,KAC/B+C,mBAAAA,CAAqB9C,CAAW,CAAC,EAAE,EAAI,KACvC+C,OAAAA,CAAS9C,CAAW,CAAC,EAAE,EAAI,KAC3B+C,iBAAAA,CAAmB9C,CAAqB,CAAC,EAAE,EAAI,IACjD,CACF,EACF,CAAE,MAAOtC,EAAO,CAEd,EAFOA,KACPqF,OAAAA,CAAQrF,KAAK,CAAC,yBAA0BA,GACjCF,EADiCE,CAAAA,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeqF,EACpB9F,CAAoB,CACpB,CAFoB8F,OAElB7F,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAWC,EADIH,MACJG,CAASF,EAAAA,CAAAA,GAEtBG,MAAMF,GACR,KADQA,CAAAA,CACDG,CADY,CACZA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,GAAM,MACJK,CAAI,aACJC,CAAW,MACXE,CAAI,gBACJC,CAAc,OACdQ,CAAK,UACLC,CAAQ,eACRF,CAAa,WACbN,CAAS,SACTC,CAAO,WACPC,CAAS,YACTE,CAAU,cACVC,CAAY,YACZgE,CAAU,CACVC,WAAS,qBACTC,CAAmB,CACnBC,SAAO,mBACPC,CAAiB,CAClB,CAnBY,EAmBTG,IAAAA,EAnBuBxF,IAAI,CAAZP,EAsBbgG,EAAiB,MAAMrF,EAAAA,EAAAA,CAC1BC,MAAM,GACNuB,IAAI,CAACtB,EAAAA,OAAAA,CAAAA,CACLyB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACxB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrBoC,IADqBpC,CAAAA,CACf,GAET,GAA8B,GAAG,CAA7B6F,EAAexD,MAAM,CACvB,KADEwD,EACK1F,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,GAAIY,EAAW,CACb,IAAM4E,EAAU,KAAVA,CAAgBtF,EAAAA,EAAAA,CACnBC,MAAM,GACNuB,IAAI,CAACH,EAAAA,KAAAA,CAAAA,CACLM,KAAK,CACJwC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACL,EAAAA,KAAAA,CAAM9B,EAAE,CAAEmB,GACbgB,CAAAA,EAAAA,EAAAA,CADahB,CACbgB,CAAAA,CAAGL,EAAAA,KAAKA,CAACkE,IAAI,CAAE,aAGlB3D,KAAK,CAAC,GAET,GAAuB,GAAG,CAAtB0D,EAAQzD,KAARyD,CAAc,CAChB,OAAO3F,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,sCAAsC,CAC/C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,GAAIc,GAAcA,IAAeyE,CAAc,CAAC,EAAE,CAACzE,CAAjCA,SAA2C,EAAE,CAC1C,MAAMZ,EAAAA,CAWrBwF,CAXqBxF,CACtBC,MAAM,GACNuB,IAAI,CAACtB,EAAAA,OAAOA,EACZyB,KAAK,CACJwC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGxB,EAAAA,OAAAA,CAAQU,UAAU,CAAEA,GACvBc,CAAAA,EAAAA,EAAAA,EADuBd,CAAAA,CACpBV,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,KAGlBoC,GAHkBpC,CAAAA,CAGb,CAAC,IAEMqC,MAAM,CAAG,EACtB,CADyB,MAClBlC,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,6BAA6B,CACtC,CAAEC,MAAAA,CAAQ,GAAI,GAMpB,IAAM2F,EAAgB,MAAMzF,EAAAA,EAAAA,CACzB0F,MAAM,CAACxF,EAAAA,OAAAA,CAAAA,CACPyF,GAAG,CAAC,CACH,GAAIxF,GAAQ,CAARA,KAAUA,EAAM,CACpB,GAAIC,GAAe,QAAfA,KAAiBA,EAAa,CAClC,GAAIE,GAAQ,CAARA,KAAUA,EAAM,CACpB,GAAIC,GAAkB,WAAlBA,KAAoBA,EAAgB,CACxC,GAAIQ,KAAU6E,OAAa,CAAE7E,CAAf6E,IAAe7E,CAAOA,EAAM8E,QAAQ,GAAI,CACtD,GAAI7E,GAAY,KAAZA,KAAcA,EAAU,CAC5B,QAAsB4E,IAAlB9E,GAA+B,EAAb8E,aAAe9E,EAAe,CACpD,GAAIN,GAAa,CAAEA,KAAfA,IAAeA,CAAW,IAAIsF,IAAAA,CAAKtF,GAAY,CACnD,GAAIC,EADmCD,CACxB,CAAEC,GAAbA,IAAaA,CAAS,IAAIqF,IAAAA,CAAKrF,GAAU,CAC7C,GAAIC,GAAa,MAAbA,KAAeA,EAAW,CAC9B,GAAIE,GAAc,OAAdA,KAAgBA,EAAY,CAChC,GAAIC,GAAgB,SAAhBA,KAAkBA,EAAc,CACpCM,SAAAA,CAAW,IAAI2E,IAAAA,GAEhBnE,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGxB,EAAAA,OAAOA,CAACX,EAAE,CAAEC,IACrBuG,SAAS,GA+DZ,OA5DIlB,IAEF,MAFEA,EAAY,EAER7E,CAAGgG,MAAM,CAAC1D,EAAAA,gBAAAA,CAAAA,CAAkBX,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGY,EAAAA,gBAAAA,CAAiB9C,QAAQ,CAAEA,IAEtE,IAFsEA,CAAAA,CAAAA,EAEhEQ,EAAAA,CAAGiG,MAAM,CAAC3D,EAAAA,gBAAAA,CAAAA,CAAkB4D,MAAM,CAAC,CACvC1G,QAAAA,CAAUA,EACV2G,MADU3G,MACV2G,CAActB,EAAWsB,YAAY,CACrCC,mBAAAA,CAAqBvB,EAAWuB,mBAAmB,CACnDC,aAAAA,CAAexB,EAAWwB,aAAAA,IAI1BvB,IAEF,KAFEA,CAEI9E,CAFO,CAEPA,EAAAA,CAAGgG,MAAM,CAACzD,EAAAA,eAAAA,CAAAA,CAAiBZ,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGa,EAAAA,eAAAA,CAAgB/C,QAAQ,CAAEA,IAEpE,IAFoEA,CAAAA,CAAAA,EAE9DQ,EAAAA,CAAGiG,MAAM,CAAC1D,EAAAA,eAAAA,CAAAA,CAAiB2D,MAAM,CAAC,CACtC1G,QAAAA,CAAUA,EACV8G,MADU9G,CACV8G,CAASxB,EAAUwB,OAAO,CAC1BC,QAAAA,CAAUzB,EAAUyB,QAAQ,CAC5BC,UAAAA,CAAY1B,EAAU0B,UAAAA,IAItBzB,IAEF,MAAM/E,EAAAA,EAAAA,CAAGgG,IAFPjB,EAAqB,CAEPvC,EAAAA,yBAAAA,CAAAA,CAA2Bb,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGc,EAAAA,yBAAAA,CAA0BhD,QAAQ,CAAEA,IAExF,IAFwFA,CAAAA,CAAAA,EAElFQ,EAAAA,CAAGiG,MAAM,CAACzD,EAAAA,yBAAAA,CAAAA,CAA2B0D,MAAM,CAAC,CAChD1G,QAAAA,CAAUA,EACViH,MADUjH,GACViH,CAAW1B,EAAoB0B,SAAS,CACxCC,cAAAA,CAAgB3B,EAAoB2B,cAAc,CAClDC,YAAAA,CAAc5B,EAAoB4B,YAAAA,IAIlC3B,IAEF,GAFEA,EAAS,CAELhF,EAAAA,EAAAA,CAAGgG,MAAM,CAACvD,EAAAA,aAAAA,CAAAA,CAAed,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,aAAAA,CAAcjD,QAAQ,CAAEA,IAEhE,IAFgEA,CAAAA,CAE1DQ,EAAAA,EAAAA,CAAGiG,MAAM,CAACxD,EAAAA,aAAAA,CAAAA,CAAeyD,MAAM,CAAC,CACpC1G,QAAAA,CAAUA,EACVoH,MADUpH,EACVoH,CAAU5B,EAAQ4B,QAAQ,CAC1BC,UAAAA,CAAY7B,EAAQ6B,UAAU,CAC9BC,aAAAA,CAAe9B,EAAQ8B,aACzB,IAGE7B,IAEF,MAAMjF,EAAAA,EAAAA,CAAGgG,EAFPf,EAAmB,EAEN,CAACvC,EAAAA,uBAAAA,CAAAA,CAAyBf,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGgB,EAAAA,uBAAAA,CAAwBlD,QAAQ,CAAEA,IAEpF,IAFoFA,CAAAA,CAE9EQ,EAAAA,EAAAA,CAAGiG,MAAM,CAACvD,EAAAA,uBAAAA,CAAAA,CAAyBwD,MAAM,CAAC,CAC9C1G,QAAAA,CAAUA,EACVuH,MADUvH,MACVuH,CAAc9B,EAAkB8B,YAAY,CAC5CC,UAAAA,CAAY/B,EAAkB+B,UAAU,CACxCC,OAAAA,CAAShC,EAAkBgC,OAAAA,IAIxBtH,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBkC,MAAAA,CAAQ2D,CAAa,CAAC,EAAE,CACxByB,OAAAA,CAAS,6BACX,EACF,CAAE,MAAOrH,EAAO,CAEd,EAFOA,KACPqF,OAAAA,CAAQrF,KAAK,CAAC,yBAA0BA,GACjCF,EADiCE,CAAAA,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeqH,EACpB9H,CAAoB,CACpB,IAFoB8H,IAElB7H,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAWC,EADIH,MACJG,CAASF,EAAAA,CAAAA,GAEtBG,MAAMF,GACR,KADQA,CAAAA,CACDG,CADY,CACZA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,IAAMY,EADerB,EAAQ+H,KAAR/H,EAAe,CAACgI,EACnBA,UAD+B,CAClBC,GAAG,CAAC,aAG7BjC,EAAiB,MAAMrF,EAAAA,EAAAA,CAC1BC,MAAM,GACNuB,IAAI,CAACtB,EAAAA,OAAAA,CAAAA,CACLyB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACxB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrBoC,IADqBpC,CAChB,CAAC,GAET,GAA8B,GAAG,CAA7B6F,EAAexD,MAAM,CACvB,KADEwD,EACK1F,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,GAAIY,GAAa2E,CAAc,CAAC,EAAE,CAAC3E,CAA/BA,QAAwC,GAAKjB,SAASiB,GACxD,MADwDA,CAAAA,EAAY,YAC7Df,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,uCAAuC,CAChD,CAAEC,MAAAA,CAAQ,GAAI,GAMlB,IAAMyH,EAAa,MAAMvH,EAAnBuH,EAAmBvH,CACtBC,MAAM,CAAC,CAAEV,EAAAA,CAAI+D,EAAAA,OAAOA,CAAC/D,EAAAA,CAAG,EACxBiC,IAAI,CAAC8B,EAAAA,OAAAA,CAAAA,CACL3B,KAAK,CACJwC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEzC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG4B,EAAAA,OAAAA,CAAQ9D,QAAQ,CAAEA,KAKrBmD,EAAgB,CALKnD,CAAAA,CAAAA,CAAAA,EAKCQ,EAAAA,EAAAA,CAAtB2C,MACG,CAAC,CAAEpD,EAAAA,CAAIqD,EAAAA,OAAOA,CAACrD,EAAAA,CAAG,EACxBiC,IAAI,CAACoB,EAAAA,OAAAA,CAAAA,CACLjB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQpD,QAAQ,CAAEA,IAExBqE,EAAgB,EAFQrE,CAAAA,CAAAA,EAEFQ,EAAAA,EAAAA,CAAtB6D,MACG,CAAC,CAAEtE,EAAAA,CAAI+D,EAAAA,OAAOA,CAAC/D,EAAAA,GACrBiC,IAAI,CAAC8B,EAAAA,OAAAA,CAAAA,CACLkE,SAAS,CAAC5E,EAAAA,OAAAA,CAASlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,OAAAA,CAAQJ,QAAQ,CAAEN,EAAAA,OAAAA,CAAQrD,EAAE,GAClDoC,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQpD,QAAQ,CAAEA,IAExB6D,EAAiB,EAAE,CACzB,CAH8B7D,GAGzB,IAAMiI,KAAgB9E,EAMzB,IAAK,IAAMS,GANcT,CAAe,CACjB,MAKDK,EALOhD,EAAAA,CAC1BC,MAAM,CAAC,CAAEV,CAIUyD,CAJVzD,CAAI0D,EAAAA,QAAQA,CAAC1D,EAAAA,CAAG,EACzBiC,IAAI,CAACyB,EAAAA,QAAAA,CAAAA,CACLtB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuB,EAAAA,QAAAA,CAASC,QAAQ,CAAEuE,EAAalI,EAAE,IAER,CACpC,GAH6BkI,CAGvBC,EAA2B,MAAM1H,EAAAA,EAAAA,CACpCC,MAAM,CAAC,CAAEV,EAAAA,CAAI+D,EAAAA,OAAOA,CAAC/D,EAAAA,CAAG,EACxBiC,IAAI,CAAC8B,EAAAA,OAAAA,CAAAA,CACL3B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC4B,EAAAA,OAAAA,CAAQC,SAAS,CAAEH,EAAQ7D,EAAE,GACzC8D,EAAesE,IAAI,IAAID,EACzB,CAIF,CALIrE,GAKEuE,EAAa,IACdL,EAAWzE,EADV8E,CACa,CAACC,CAAAA,EAAKA,CAApBN,CAAsBhI,CANAmI,CAME,KACxB7D,EAAcf,GAAG,CAAC+E,CAAAA,EAAKA,EAAEtI,EAAzBsE,KACAR,EAAeP,GAAG,CAAC+E,CAAAA,EAAKA,EAAEtI,EAAE,CAA5B8D,CACJ,CAGD,IAAK,IAAMO,KAAUgE,EAEnB,MAAM5H,EAFa4H,CAAY,CAEzB5H,CAAGgG,MAAM,CAACrC,EAAAA,SAAAA,CAAAA,CAAWhC,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGiC,EAAAA,SAAAA,CAAUC,MAAM,CAAEA,IAEtD,EAFsDA,CAAAA,CAAAA,EAEhD5D,EAAAA,EAAAA,CAAGgG,MAAM,CAAC8B,EAAAA,YAAAA,CAAAA,CAAcnG,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGoG,EAAAA,YAAAA,CAAalE,MAAM,CAAEA,IAI9D,EAJ8DA,CAAAA,CAIzD,IAAMA,KAAUgE,EACnB,MAAM5H,EAAAA,CADyB,CACzBA,CAAGgG,MAAM,CAAC1C,EAAAA,OAAAA,CAAAA,CAAS3B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG4B,EAAAA,OAAAA,CAAQ/D,EAAE,CAAEqE,IAIhD,EAJgDA,CAAAA,CAAAA,IAIrC6D,KAAgB9E,EACzB,MAAM3C,EAAAA,EAAAA,CADmB2C,CAAe,KACzB,CAACM,EAAAA,QAAAA,CAAAA,CAAUtB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGuB,EAAAA,QAAAA,CAASC,QAAQ,CAAEuE,EAAalI,EAAE,GAavE,KAbwDkI,EAIxD,MAAMzH,EAAAA,EAAAA,CAAGgG,MAAM,CAACpD,EAAAA,OAAAA,CAAAA,CAASjB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQpD,QAAQ,CAAEA,IAGpD,IAHoDA,CAAAA,CAAAA,EAG9CQ,EAAAA,CAAGgG,MAAM,CAACzB,EAAAA,iBAAAA,CAAAA,CAAmB5C,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG6C,EAAAA,iBAAAA,CAAkB/E,QAAQ,CAAEA,IACxE,IADwEA,CAAAA,CAAAA,EAClEQ,EAAAA,CAAGgG,MAAM,CAACvB,EAAAA,kBAAAA,CAAAA,CAAoB9C,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG+C,EAAAA,kBAAAA,CAAmBjF,QAAQ,CAAEA,IAG1E,IAH0EA,CAAAA,CAAAA,EAGpEQ,EAAAA,CAAGgG,MAAM,CAAC9F,EAAAA,OAAAA,CAAAA,CAASyB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGxB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IAEvCG,EAAAA,EAFuCH,CAAAA,CAAAA,QAEvCG,CAAaC,IAAI,CAAC,CAAC8E,OAAAA,EAAS,EAAMwC,OAAAA,CAAS,6BAA8B,EAClF,CAAE,MAAOrH,EAAO,CAEd,EAFOA,KACPqF,OAAAA,CAAQrF,KAAK,CAAC,yBAA0BA,GACjCF,EADiCE,CAAAA,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCjfA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,mBAAmB,SACvC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,CAIC,IAAC,EAAM,CAAH,CAAeiI,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,EAAYC,CAAf,MAA6C,EAA/B,KAAsC,EAEzD,EAAS,EAAYC,EAAf,MAA2C,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,+BACA,6BACA,iBACA,uCACA,CAAK,CACL,kJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/src/app/api/courses/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?15e0", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport {\r\n  courses,\r\n  users,\r\n  modules,\r\n  chapters,\r\n  quizzes,\r\n  questions,\r\n  quizAttempts,\r\n  courseEnrollments,\r\n  studentEnrollments,\r\n  courseAdmissions,\r\n  courseAcademics,\r\n  courseTuitionAndFinancing,\r\n  courseCareers,\r\n  courseStudentExperience\r\n} from '@/lib/db/schema';\r\nimport { eq, and, or } from 'drizzle-orm';\r\n\r\n// GET /api/courses/[id] - Get a specific course with details\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const courseId = parseInt(id);\r\n    \r\n    if (isNaN(courseId)) {\r\n      return NextResponse.json({ error: 'Invalid course ID' }, { status: 400 });\r\n    }\r\n\r\n    // Get course with teacher information\r\n    const courseData = await db\r\n      .select({\r\n        id: courses.id,\r\n        name: courses.name,\r\n        description: courses.description,\r\n        instructor: courses.instructor,\r\n        type: courses.type,\r\n        enrollmentType: courses.enrollmentType,\r\n        startDate: courses.startDate,\r\n        endDate: courses.endDate,\r\n        teacherId: courses.teacherId,\r\n        institutionId: courses.institutionId,\r\n        courseCode: courses.courseCode,\r\n        coverPicture: courses.coverPicture,\r\n        isPurchasable: courses.isPurchasable,\r\n        price: courses.price,\r\n        currency: courses.currency,\r\n        previewMode: courses.previewMode,\r\n        createdAt: courses.createdAt,\r\n        updatedAt: courses.updatedAt,\r\n        teacherName: users.name,\r\n        teacherEmail: users.email\r\n      })\r\n      .from(courses)\r\n      .leftJoin(users, eq(courses.teacherId, users.id))\r\n      .where(eq(courses.id, courseId))\r\n      .limit(1);\r\n\r\n    if (courseData.length === 0) {\r\n      return NextResponse.json({ error: 'Course not found' }, { status: 404 });\r\n    }\r\n\r\n    const course = courseData[0];\r\n\r\n    // Get related course data\r\n    const [admissionsData, academicsData, tuitionData, careersData, studentExperienceData] = await Promise.all([\r\n      db.select().from(courseAdmissions).where(eq(courseAdmissions.courseId, courseId)).limit(1),\r\n      db.select().from(courseAcademics).where(eq(courseAcademics.courseId, courseId)).limit(1),\r\n      db.select().from(courseTuitionAndFinancing).where(eq(courseTuitionAndFinancing.courseId, courseId)).limit(1),\r\n      db.select().from(courseCareers).where(eq(courseCareers.courseId, courseId)).limit(1),\r\n      db.select().from(courseStudentExperience).where(eq(courseStudentExperience.courseId, courseId)).limit(1)\r\n    ]);\r\n\r\n    // Get modules for this course\r\n    const courseModules = await db\r\n      .select()\r\n      .from(modules)\r\n      .where(eq(modules.courseId, courseId));\r\n\r\n    // Get chapters for each module\r\n    const modulesWithChapters = await Promise.all(\r\n      courseModules.map(async (module) => {\r\n        const moduleChapters = await db\r\n          .select()\r\n          .from(chapters)\r\n          .where(eq(chapters.moduleId, module.id));\r\n\r\n        // Get quizzes for each chapter with questions\r\n        const chaptersWithQuizzes = await Promise.all(\r\n          moduleChapters.map(async (chapter) => {\r\n            const chapterQuizzes = await db\r\n              .select()\r\n              .from(quizzes)\r\n              .where(eq(quizzes.chapterId, chapter.id));\r\n\r\n            // Get questions for each quiz\r\n            const quizzesWithQuestions = await Promise.all(\r\n              chapterQuizzes.map(async (quiz) => {\r\n                const quizQuestions = await db\r\n                  .select()\r\n                  .from(questions)\r\n                  .where(eq(questions.quizId, quiz.id));\r\n\r\n                return {\r\n                  ...quiz,\r\n                  questions: quizQuestions\r\n                };\r\n              })\r\n            );\r\n\r\n            return {\r\n              ...chapter,\r\n              quizzes: quizzesWithQuestions\r\n            };\r\n          })\r\n        );\r\n\r\n        return {\r\n          ...module,\r\n          chapters: chaptersWithQuizzes\r\n        };\r\n      })\r\n    );\r\n\r\n    // Get module quizzes and final exam\r\n    // Module quizzes have moduleId set, final exams have courseId set\r\n    const moduleQuizzes = await db\r\n      .select({\r\n        id: quizzes.id,\r\n        name: quizzes.name,\r\n        description: quizzes.description,\r\n        chapterId: quizzes.chapterId,\r\n        moduleId: quizzes.moduleId,\r\n        courseId: quizzes.courseId,\r\n        quizType: quizzes.quizType,\r\n        minimumScore: quizzes.minimumScore,\r\n        timeLimit: quizzes.timeLimit,\r\n        startDate: quizzes.startDate,\r\n        endDate: quizzes.endDate,\r\n        isActive: quizzes.isActive,\r\n        createdAt: quizzes.createdAt,\r\n        updatedAt: quizzes.updatedAt\r\n      })\r\n      .from(quizzes)\r\n      .leftJoin(modules, eq(quizzes.moduleId, modules.id))\r\n      .where(\r\n        or(\r\n          eq(quizzes.courseId, courseId), // Final exams\r\n          and(eq(modules.courseId, courseId), eq(quizzes.quizType, 'module')) // Module quizzes\r\n        )\r\n      );\r\n\r\n    // Get questions for module quizzes and final exam\r\n    const moduleQuizzesWithQuestions = await Promise.all(\r\n      moduleQuizzes.map(async (quiz) => {\r\n        const quizQuestions = await db\r\n          .select()\r\n          .from(questions)\r\n          .where(eq(questions.quizId, quiz.id));\r\n\r\n        return {\r\n          ...quiz,\r\n          questions: quizQuestions\r\n        };\r\n      })\r\n    );\r\n\r\n    // Get enrollment statistics\r\n    const enrollmentStats = await db\r\n      .select({ count: courseEnrollments.id })\r\n      .from(courseEnrollments)\r\n      .where(eq(courseEnrollments.courseId, courseId));\r\n\r\n    const studentStats = await db\r\n      .select({ count: studentEnrollments.id })\r\n      .from(studentEnrollments)\r\n      .where(eq(studentEnrollments.courseId, courseId));\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      course: {\r\n        ...course,\r\n        modules: modulesWithChapters,\r\n        moduleQuizzes: moduleQuizzesWithQuestions,\r\n        enrollmentCount: enrollmentStats.length,\r\n        studentCount: studentStats.length,\r\n        // Add related course data\r\n        admissions: admissionsData[0] || null,\r\n        academics: academicsData[0] || null,\r\n        tuitionAndFinancing: tuitionData[0] || null,\r\n        careers: careersData[0] || null,\r\n        studentExperience: studentExperienceData[0] || null\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching course:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/courses/[id] - Update a course\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const courseId = parseInt(id);\r\n    \r\n    if (isNaN(courseId)) {\r\n      return NextResponse.json({ error: 'Invalid course ID' }, { status: 400 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      type,\r\n      enrollmentType,\r\n      price,\r\n      currency,\r\n      isPurchasable,\r\n      startDate,\r\n      endDate,\r\n      teacherId,\r\n      courseCode,\r\n      coverPicture,\r\n      admissions,\r\n      academics,\r\n      tuitionAndFinancing,\r\n      careers,\r\n      studentExperience\r\n    } = body;\r\n\r\n    // Check if course exists\r\n    const existingCourse = await db\r\n      .select()\r\n      .from(courses)\r\n      .where(eq(courses.id, courseId))\r\n      .limit(1);\r\n\r\n    if (existingCourse.length === 0) {\r\n      return NextResponse.json({ error: 'Course not found' }, { status: 404 });\r\n    }\r\n\r\n    // If teacherId is provided, verify the teacher exists and has permission\r\n    if (teacherId) {\r\n      const teacher = await db\r\n        .select()\r\n        .from(users)\r\n        .where(\r\n          and(\r\n            eq(users.id, teacherId),\r\n            eq(users.role, 'teacher')\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (teacher.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Teacher not found or not authorized' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // If courseCode is being updated, check uniqueness\r\n    if (courseCode && courseCode !== existingCourse[0].courseCode) {\r\n      const codeExists = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.courseCode, courseCode),\r\n            eq(courses.id, courseId) // Exclude current course\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (codeExists.length > 0) {\r\n        return NextResponse.json(\r\n          { error: 'Course code already exists' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Update the course\r\n    const updatedCourse = await db\r\n      .update(courses)\r\n      .set({\r\n        ...(name && { name }),\r\n        ...(description && { description }),\r\n        ...(type && { type }),\r\n        ...(enrollmentType && { enrollmentType }),\r\n        ...(price !== undefined && { price: price.toString() }),\r\n        ...(currency && { currency }),\r\n        ...(isPurchasable !== undefined && { isPurchasable }),\r\n        ...(startDate && { startDate: new Date(startDate) }),\r\n        ...(endDate && { endDate: new Date(endDate) }),\r\n        ...(teacherId && { teacherId }),\r\n        ...(courseCode && { courseCode }),\r\n        ...(coverPicture && { coverPicture }),\r\n        updatedAt: new Date()\r\n      })\r\n      .where(eq(courses.id, courseId))\r\n      .returning();\r\n\r\n    // Update related course data\r\n    if (admissions) {\r\n      // Delete existing admissions data\r\n      await db.delete(courseAdmissions).where(eq(courseAdmissions.courseId, courseId));\r\n      // Insert new admissions data\r\n      await db.insert(courseAdmissions).values({\r\n        courseId: courseId,\r\n        requirements: admissions.requirements,\r\n        applicationDeadline: admissions.applicationDeadline,\r\n        prerequisites: admissions.prerequisites,\r\n      });\r\n    }\r\n\r\n    if (academics) {\r\n      // Delete existing academics data\r\n      await db.delete(courseAcademics).where(eq(courseAcademics.courseId, courseId));\r\n      // Insert new academics data\r\n      await db.insert(courseAcademics).values({\r\n        courseId: courseId,\r\n        credits: academics.credits,\r\n        workload: academics.workload,\r\n        assessment: academics.assessment,\r\n      });\r\n    }\r\n\r\n    if (tuitionAndFinancing) {\r\n      // Delete existing tuition data\r\n      await db.delete(courseTuitionAndFinancing).where(eq(courseTuitionAndFinancing.courseId, courseId));\r\n      // Insert new tuition data\r\n      await db.insert(courseTuitionAndFinancing).values({\r\n        courseId: courseId,\r\n        totalCost: tuitionAndFinancing.totalCost,\r\n        paymentOptions: tuitionAndFinancing.paymentOptions,\r\n        scholarships: tuitionAndFinancing.scholarships,\r\n      });\r\n    }\r\n\r\n    if (careers) {\r\n      // Delete existing careers data\r\n      await db.delete(courseCareers).where(eq(courseCareers.courseId, courseId));\r\n      // Insert new careers data\r\n      await db.insert(courseCareers).values({\r\n        courseId: courseId,\r\n        outcomes: careers.outcomes,\r\n        industries: careers.industries,\r\n        averageSalary: careers.averageSalary,\r\n      });\r\n    }\r\n\r\n    if (studentExperience) {\r\n      // Delete existing student experience data\r\n      await db.delete(courseStudentExperience).where(eq(courseStudentExperience.courseId, courseId));\r\n      // Insert new student experience data\r\n      await db.insert(courseStudentExperience).values({\r\n        courseId: courseId,\r\n        testimonials: studentExperience.testimonials,\r\n        facilities: studentExperience.facilities,\r\n        support: studentExperience.support,\r\n      });\r\n    }\r\n\r\n    return NextResponse.json({\r\n      course: updatedCourse[0],\r\n      message: 'Course updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating course:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/courses/[id] - Delete a course\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const courseId = parseInt(id);\r\n    \r\n    if (isNaN(courseId)) {\r\n      return NextResponse.json({ error: 'Invalid course ID' }, { status: 400 });\r\n    }\r\n\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n\r\n    // Check if course exists\r\n    const existingCourse = await db\r\n      .select()\r\n      .from(courses)\r\n      .where(eq(courses.id, courseId))\r\n      .limit(1);\r\n\r\n    if (existingCourse.length === 0) {\r\n      return NextResponse.json({ error: 'Course not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to delete this course\r\n    if (teacherId && existingCourse[0].teacherId !== parseInt(teacherId)) {\r\n      return NextResponse.json(\r\n        { error: 'Not authorized to delete this course' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Delete related data in correct order (due to foreign key constraints)\r\n    // 1. Get all quizzes for this course (chapter, module, and final exam quizzes)\r\n    const allQuizzes = await db\r\n      .select({ id: quizzes.id })\r\n      .from(quizzes)\r\n      .where(\r\n        and(\r\n          eq(quizzes.courseId, courseId)\r\n        )\r\n      );\r\n\r\n    // Also get quizzes from chapters and modules\r\n    const courseModules = await db\r\n      .select({ id: modules.id })\r\n      .from(modules)\r\n      .where(eq(modules.courseId, courseId));\r\n\r\n    const moduleQuizzes = await db\r\n      .select({ id: quizzes.id })\r\n      .from(quizzes)\r\n      .innerJoin(modules, eq(quizzes.moduleId, modules.id))\r\n      .where(eq(modules.courseId, courseId));\r\n\r\n    const chapterQuizzes = [];\r\n    for (const moduleRecord of courseModules) {\r\n      const moduleChapters = await db\r\n        .select({ id: chapters.id })\r\n        .from(chapters)\r\n        .where(eq(chapters.moduleId, moduleRecord.id));\r\n\r\n      for (const chapter of moduleChapters) {\r\n        const chapterQuizzesForChapter = await db\r\n          .select({ id: quizzes.id })\r\n          .from(quizzes)\r\n          .where(eq(quizzes.chapterId, chapter.id));\r\n        chapterQuizzes.push(...chapterQuizzesForChapter);\r\n      }\r\n    }\r\n\r\n    // Combine all quiz IDs\r\n    const allQuizIds = [\r\n      ...allQuizzes.map(q => q.id),\r\n      ...moduleQuizzes.map(q => q.id),\r\n      ...chapterQuizzes.map(q => q.id)\r\n    ];\r\n\r\n    // 2. Delete questions and quiz attempts for all quizzes\r\n    for (const quizId of allQuizIds) {\r\n      // Delete questions first\r\n      await db.delete(questions).where(eq(questions.quizId, quizId));\r\n      // Delete quiz attempts\r\n      await db.delete(quizAttempts).where(eq(quizAttempts.quizId, quizId));\r\n    }\r\n\r\n    // 3. Delete all quizzes\r\n    for (const quizId of allQuizIds) {\r\n      await db.delete(quizzes).where(eq(quizzes.id, quizId));\r\n    }\r\n\r\n    // 4. Delete chapters\r\n    for (const moduleRecord of courseModules) {\r\n      await db.delete(chapters).where(eq(chapters.moduleId, moduleRecord.id));\r\n    }\r\n\r\n    // 5. Delete modules\r\n    await db.delete(modules).where(eq(modules.courseId, courseId));\r\n\r\n    // 6. Delete enrollments\r\n    await db.delete(courseEnrollments).where(eq(courseEnrollments.courseId, courseId));\r\n    await db.delete(studentEnrollments).where(eq(studentEnrollments.courseId, courseId));\r\n\r\n    // 7. Finally delete the course\r\n    await db.delete(courses).where(eq(courses.id, courseId));\r\n\r\n    return NextResponse.json({success: true, message: 'Course deleted successfully' });\r\n  } catch (error) {\r\n    console.error('Error deleting course:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/courses/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\courses\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/courses/[id]/route\",\n        pathname: \"/api/courses/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/courses/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\courses\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "courseId", "parseInt", "isNaN", "NextResponse", "json", "error", "status", "courseData", "db", "select", "courses", "name", "description", "instructor", "type", "enrollmentType", "startDate", "endDate", "teacherId", "institutionId", "courseCode", "coverPicture", "isPurchasable", "price", "currency", "previewMode", "createdAt", "updatedAt", "<PERSON><PERSON><PERSON>", "users", "teacherEmail", "email", "from", "leftJoin", "eq", "where", "limit", "length", "course", "admissionsData", "academicsData", "tuitionData", "careersData", "studentExperienceData", "Promise", "all", "courseAdmissions", "courseAcademics", "courseTuitionAndFinancing", "courseCareers", "courseStudentExperience", "courseModules", "modules", "modulesWithChapters", "map", "module", "moduleChapters", "chapters", "moduleId", "chaptersWithQuizzes", "chapter", "chapterQuizzes", "quizzes", "chapterId", "quizzesWithQuestions", "quiz", "quizQuestions", "questions", "quizId", "moduleQuizzes", "quizType", "minimumScore", "timeLimit", "isActive", "or", "and", "moduleQuizzesWithQuestions", "enrollmentStats", "count", "courseEnrollments", "studentStats", "studentEnrollments", "success", "enrollmentCount", "studentCount", "admissions", "academics", "tuitionAndFinancing", "careers", "studentExperience", "console", "PUT", "body", "existingCourse", "teacher", "role", "codeExists", "updatedCourse", "update", "set", "undefined", "toString", "Date", "returning", "delete", "insert", "values", "requirements", "applicationDeadline", "prerequisites", "credits", "workload", "assessment", "totalCost", "paymentOptions", "scholarships", "outcomes", "industries", "averageSalary", "testimonials", "facilities", "support", "message", "DELETE", "nextUrl", "searchParams", "get", "allQuizzes", "innerJoin", "moduleRecord", "chapterQuizzesForChapter", "push", "allQuizIds", "q", "quizAttempts", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}