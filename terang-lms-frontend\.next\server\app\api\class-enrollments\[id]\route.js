try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="eb0dd992-05d5-4b87-8a80-21ef89508142",e._sentryDebugIdIdentifier="sentry-dbid-eb0dd992-05d5-4b87-8a80-21ef89508142")}catch(e){}"use strict";(()=>{var e={};e.id=4608,e.ids=[4608],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},21945:(e,s,r)=>{r.r(s),r.d(s,{patchFetch:()=>k,routeModule:()=>I,serverHooks:()=>T,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>R});var t={};r.r(t),r.d(t,{DELETE:()=>N,GET:()=>y,HEAD:()=>A,OPTIONS:()=>b,PATCH:()=>v,POST:()=>g,PUT:()=>w});var n=r(3690),o=r(56947),l=r(75250),a=r(63033),i=r(62187),u=r(18621),d=r(32230),c=r(74683),p=r(7688);async function m(e,{params:s}){try{let{id:e}=await s,r=parseInt(e);if(isNaN(r))return i.NextResponse.json({success:!1,error:"Invalid enrollment ID"},{status:400});let t=await u.db.select({id:d.classEnrollments.id,studentId:d.classEnrollments.studentId,classId:d.classEnrollments.classId,enrolledAt:d.classEnrollments.enrolledAt,status:d.classEnrollments.status,createdAt:d.classEnrollments.createdAt,updatedAt:d.classEnrollments.updatedAt,studentName:d.users.name,studentEmail:d.users.email,className:d.classes.name}).from(d.classEnrollments).leftJoin(d.users,(0,c.eq)(d.classEnrollments.studentId,d.users.id)).leftJoin(d.classes,(0,c.eq)(d.classEnrollments.classId,d.classes.id)).where((0,c.eq)(d.classEnrollments.id,r)).limit(1);if(0===t.length)return i.NextResponse.json({success:!1,error:"Class enrollment not found"},{status:404});return i.NextResponse.json({success:!0,data:t[0]})}catch(e){return console.error("Error fetching class enrollment:",e),i.NextResponse.json({success:!1,error:"Failed to fetch class enrollment"},{status:500})}}async function x(e,{params:s}){try{let{id:r}=await s,t=parseInt(r),{status:n}=await e.json();if(isNaN(t))return i.NextResponse.json({success:!1,error:"Invalid enrollment ID"},{status:400});if(!n||!["active","inactive"].includes(n))return i.NextResponse.json({success:!1,error:"Valid status is required (active or inactive)"},{status:400});let o=await u.db.select().from(d.classEnrollments).where((0,c.eq)(d.classEnrollments.id,t)).limit(1);if(0===o.length)return i.NextResponse.json({success:!1,error:"Class enrollment not found"},{status:404});let l=await u.db.update(d.classEnrollments).set({status:n,updatedAt:new Date}).where((0,c.eq)(d.classEnrollments.id,t)).returning();return i.NextResponse.json({success:!0,data:l[0],message:"Class enrollment updated successfully"})}catch(e){return console.error("Error updating class enrollment:",e),i.NextResponse.json({success:!1,error:"Failed to update class enrollment"},{status:500})}}async function f(e,{params:s}){try{let{id:e}=await s,r=parseInt(e);if(isNaN(r))return i.NextResponse.json({success:!1,error:"Invalid enrollment ID"},{status:400});let t=await u.db.select().from(d.classEnrollments).where((0,c.eq)(d.classEnrollments.id,r)).limit(1);if(0===t.length)return i.NextResponse.json({success:!1,error:"Class enrollment not found"},{status:404});return await u.db.delete(d.classEnrollments).where((0,c.eq)(d.classEnrollments.id,r)),i.NextResponse.json({success:!0,message:"Class enrollment deleted successfully"})}catch(e){return console.error("Error deleting class enrollment:",e),i.NextResponse.json({success:!1,error:"Failed to delete class enrollment"},{status:500})}}let q={...a},h="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function E(e,s){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,r,t)=>{let n;try{let e=h?.getStore();n=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:s,parameterizedRoute:"/api/class-enrollments/[id]",headers:n}).apply(r,t)}})}let y=E(m,"GET"),g=E(void 0,"POST"),w=E(x,"PUT"),v=E(void 0,"PATCH"),N=E(f,"DELETE"),A=E(void 0,"HEAD"),b=E(void 0,"OPTIONS"),I=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/class-enrollments/[id]/route",pathname:"/api/class-enrollments/[id]",filename:"route",bundlePath:"app/api/class-enrollments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\class-enrollments\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:j,workUnitAsyncStorage:R,serverHooks:T}=I;function k(){return(0,l.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:R})}},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[5250,7688,8036,138,1617,2957],()=>r(21945));module.exports=t})();
//# sourceMappingURL=route.js.map