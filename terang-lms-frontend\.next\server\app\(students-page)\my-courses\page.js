try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="8d2b051a-a19c-43e9-8017-f2909ff093d0",e._sentryDebugIdIdentifier="sentry-dbid-8d2b051a-a19c-43e9-8017-f2909ff093d0")}catch(e){}(()=>{var e={};e.id=4174,e.ids=[4174],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5827:(e,s,r)=>{"use strict";r.d(s,{k:()=>l});var t=r(91754),a=r(93491),n=r(66536),i=r(82233);let l=a.forwardRef(({className:e,value:s,...r},a)=>(0,t.jsx)(n.bL,{ref:a,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,t.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));l.displayName=n.bL.displayName},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},38946:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var t=r(95500),a=r(56947),n=r(26052),i=r(13636),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let o={children:["",{children:["(students-page)",{children:["my-courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49792)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\my-courses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,97893)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\my-courses\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(students-page)/my-courses/page",pathname:"/my-courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},41400:(e,s,r)=>{Promise.resolve().then(r.bind(r,49792))},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},49792:(e,s,r)=>{"use strict";let t;r.r(s),r.d(s,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=r(63033),n=r(1472),i=r(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\my-courses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\my-courses\\page.tsx","default");let o={...a},d="workUnitAsyncStorage"in o?o.workUnitAsyncStorage:"requestAsyncStorage"in o?o.requestAsyncStorage:void 0;t="function"==typeof l?new Proxy(l,{apply:(e,s,r)=>{let t,a,n;try{let e=d?.getStore();t=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/(students-page)/my-courses",componentType:"Page",sentryTraceHeader:t,baggageHeader:a,headers:n}).apply(s,r)}}):l;let c=void 0,u=void 0,p=void 0,x=t},51128:(e,s,r)=>{Promise.resolve().then(r.bind(r,96659))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66536:(e,s,r)=>{"use strict";r.d(s,{C1:()=>b,bL:()=>j});var t=r(93491),a=r(10158),n=r(90604),i=r(91754),l="Progress",[o,d]=(0,a.A)(l),[c,u]=o(l),p=t.forwardRef((e,s)=>{var r,t;let{__scopeProgress:a,value:l=null,max:o,getValueLabel:d=h,...u}=e;(o||0===o)&&!g(o)&&console.error((r=`${o}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=g(o)?o:100;null===l||y(l,p)||console.error((t=`${l}`,`Invalid prop \`value\` of value \`${t}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=y(l,p)?l:null,m=v(x)?d(x,p):void 0;return(0,i.jsx)(c,{scope:a,value:x,max:p,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":v(x)?x:void 0,"aria-valuetext":m,role:"progressbar","data-state":f(x,p),"data-value":x??void 0,"data-max":p,...u,ref:s})})});p.displayName=l;var x="ProgressIndicator",m=t.forwardRef((e,s)=>{let{__scopeProgress:r,...t}=e,a=u(x,r);return(0,i.jsx)(n.sG.div,{"data-state":f(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...t,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function v(e){return"number"==typeof e}function g(e){return v(e)&&!isNaN(e)&&e>0}function y(e,s){return v(e)&&!isNaN(e)&&e<=s&&e>=0}m.displayName=x;var j=p,b=m},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},91828:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(15349).A)("Clock01Icon",[["circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor",key:"k0"}],["path",{d:"M12 8V12L14 14",stroke:"currentColor",key:"k1"}]])},94735:e=>{"use strict";e.exports=require("events")},96659:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var t=r(91754);r(93491);var a=r(9260),n=r(56682),i=r(80601),l=r(5827);let o=(0,r(15349).A)("Book02Icon",[["path",{d:"M20.5 16.9286V10C20.5 6.22876 20.5 4.34315 19.3284 3.17157C18.1569 2 16.2712 2 12.5 2H11.5C7.72876 2 5.84315 2 4.67157 3.17157C3.5 4.34315 3.5 6.22876 3.5 10V19.5",stroke:"currentColor",key:"k0"}],["path",{d:"M20.5 17H6C4.61929 17 3.5 18.1193 3.5 19.5C3.5 20.8807 4.61929 22 6 22H20.5",stroke:"currentColor",key:"k1"}],["path",{d:"M20.5 22C19.1193 22 18 20.8807 18 19.5C18 18.1193 19.1193 17 20.5 17",stroke:"currentColor",key:"k2"}],["path",{d:"M15 7L9 7",stroke:"currentColor",key:"k3"}],["path",{d:"M12 11L9 11",stroke:"currentColor",key:"k4"}]]);var d=r(79313),c=r(91828),u=r(47594),p=r(16041),x=r.n(p),m=r(74829),h=r(30047);let f=()=>{let{isEnrolled:e,courseData:s,enrolledCourses:r}=(0,m.q)(),p=r.length>0?r:e?[s]:[];return e||0!==p.length?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8","data-sentry-component":"ModulesPage","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)("div",{className:"mx-auto max-w-6xl space-y-6 pb-8",children:[(0,t.jsx)(h.B,{"data-sentry-element":"Breadcrumbs","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o,{className:"h-8 w-8 text-[var(--iai-primary)]","data-sentry-element":"Book02Icon","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Kursus Saya"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Lanjutkan perjalanan belajar Anda"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:[p.map(e=>{let s=e.modules.reduce((e,s)=>e+s.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed).length,0),r=e.modules.reduce((e,s)=>e+s.chapters.length,0),o=r>0?s/r*100:0,p=e.modules.filter(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed).length;return(0,t.jsxs)(a.Zp,{className:"group transition-shadow hover:shadow-lg",children:[(0,t.jsx)(a.aR,{className:"border-b",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsx)(a.ZB,{className:"text-lg transition-colors group-hover:text-blue-600",children:e.name}),(0,t.jsx)(i.E,{variant:"completed"===e.status?"default":"secondary",children:"completed"===e.status?"Selesai":"Sedang Belajar"})]}),(0,t.jsx)("p",{className:"line-clamp-2 text-sm text-gray-600",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(d.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:e.instructor})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(c.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:[e.modules.length," Modul"]})]})]})]})}),(0,t.jsx)(a.Wu,{className:"pt-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Kemajuan"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(o),"%"]})]}),(0,t.jsx)(l.k,{value:o,className:"h-2"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-lg font-bold text-blue-600",children:p}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Modul"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-lg font-bold text-green-600",children:s}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:"Bab"})]})]}),(0,t.jsx)(x(),{href:`/my-courses/${e.id}`,className:"block",children:(0,t.jsxs)(n.$,{variant:"iai",className:"w-full",size:"sm",children:[(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Lanjutkan Belajar"]})})]})})]},e.id)}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)(a.Zp,{className:"border-dashed opacity-50",children:[(0,t.jsx)(a.aR,{className:"border-b border-dashed",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)(a.ZB,{className:"text-lg text-gray-400",children:["Course ",s+2]}),(0,t.jsx)(i.E,{variant:"outline",children:"Coming Soon"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"More courses will be available soon."})]})}),(0,t.jsx)(a.Wu,{className:"pt-4",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-12 animate-pulse rounded bg-gray-100"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsx)("div",{className:"h-8 animate-pulse rounded bg-gray-100"}),(0,t.jsx)("div",{className:"h-8 animate-pulse rounded bg-gray-100"})]}),(0,t.jsxs)(n.$,{className:"w-full",size:"sm",disabled:!0,children:[(0,t.jsx)(o,{className:"mr-2 h-4 w-4"}),"Not Available"]})]})})]},`placeholder-${s}`))]})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"mx-auto max-w-6xl space-y-6 pb-8",children:[(0,t.jsx)(h.B,{}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o,{className:"h-8 w-8 text-[var(--iai-primary)]"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Kursus Saya"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Lanjutkan perjalanan belajar Anda"})]})]}),(0,t.jsxs)("div",{className:"py-16 text-center",children:[(0,t.jsx)(o,{className:"mx-auto mb-6 h-16 w-16 text-gray-400"}),(0,t.jsx)("h3",{className:"mb-4 text-xl font-semibold text-gray-900",children:"Tidak ada kursus tersedia"}),(0,t.jsx)("p",{className:"mb-8 text-gray-600",children:"Anda belum terdaftar dalam kursus apapun."}),(0,t.jsx)(x(),{href:"/courses",children:(0,t.jsxs)(n.$,{variant:"iai",children:[(0,t.jsx)(o,{className:"mr-2 h-4 w-4"}),"Jelajahi Kursus"]})})]})]})})}}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634,2095,7852],()=>r(38946));module.exports=t})();
//# sourceMappingURL=page.js.map