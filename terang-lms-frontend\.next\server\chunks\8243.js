try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="36e645aa-cfa3-472e-95a8-e7ccebb3a5f0",e._sentryDebugIdIdentifier="sentry-dbid-36e645aa-cfa3-472e-95a8-e7ccebb3a5f0")}catch(e){}exports.id=8243,exports.ids=[8243],exports.modules={7375:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var r=a(91754),s=a(9260),n=a(56682),i=a(14908),o=a(96196);let d=(0,a(55732).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var l=a(76328);a(93491);var c=a(51643);function u({children:e,scrollable:t=!0}){return(0,r.jsx)(r.Fragment,{children:t?(0,r.jsx)(c.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,r.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,r.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}function m({userRole:e}){let t=l.qs.getUser(),{title:a,description:c,actionText:m}="teacher"===e?{title:"Akun Teacher Belum Terdaftar di Institusi",description:"Akun Anda sebagai Teacher belum ditugaskan ke institusi manapun. Silakan hubungi Super Admin untuk mendapatkan akses ke institusi.",actionText:"Hubungi Super Admin"}:{title:"Akun Student Belum Terdaftar di Institusi",description:"Akun Anda sebagai Student belum ditugaskan ke institusi manapun. Silakan hubungi Teacher atau Super Admin untuk mendapatkan akses ke institusi.",actionText:"Hubungi Admin"};return(0,r.jsx)(u,{"data-sentry-element":"PageContainer","data-sentry-component":"NotAssignedToInstitution","data-sentry-source-file":"not-assigned-to-institution.tsx",children:(0,r.jsx)("div",{className:"flex w-full min-h-[calc(100vh-200px)] items-center justify-center",children:(0,r.jsxs)(s.Zp,{className:"w-full max-w-md mx-auto","data-sentry-element":"Card","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,r.jsxs)(s.aR,{className:"text-center","data-sentry-element":"CardHeader","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,r.jsx)("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100",children:(0,r.jsx)(i.A,{className:"h-8 w-8 text-orange-600","data-sentry-element":"AlertCircle","data-sentry-source-file":"not-assigned-to-institution.tsx"})}),(0,r.jsx)(s.ZB,{className:"text-xl font-semibold","data-sentry-element":"CardTitle","data-sentry-source-file":"not-assigned-to-institution.tsx",children:a}),(0,r.jsx)(s.BT,{className:"text-sm text-muted-foreground","data-sentry-element":"CardDescription","data-sentry-source-file":"not-assigned-to-institution.tsx",children:c})]}),(0,r.jsxs)(s.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,r.jsx)("div",{className:"rounded-lg border bg-muted/50 p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-muted-foreground","data-sentry-element":"Building2","data-sentry-source-file":"not-assigned-to-institution.tsx"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Status Institusi"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Belum ditugaskan"})]})]})}),(0,r.jsx)("div",{className:"rounded-lg border bg-muted/50 p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(d,{className:"h-5 w-5 text-muted-foreground","data-sentry-element":"Mail","data-sentry-source-file":"not-assigned-to-institution.tsx"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Email Akun"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:t?.email})]})]})}),(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsx)("p",{className:"text-xs text-muted-foreground text-center",children:"Setelah Super Admin menugaskan Anda ke institusi, silakan logout dan login kembali untuk mengakses dashboard."})}),(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)(n.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"not-assigned-to-institution.tsx",children:[(0,r.jsx)(d,{className:"mr-2 h-4 w-4","data-sentry-element":"Mail","data-sentry-source-file":"not-assigned-to-institution.tsx"}),m]}),(0,r.jsx)(n.$,{variant:"ghost",onClick:()=>{l.qs.removeUser(),window.location.href="/auth/sign-in"},className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"not-assigned-to-institution.tsx",children:"Logout"})]})]})]})})})}},9260:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var r=a(91754);a(93491);var s=a(82233);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},11794:(e,t,a)=>{Promise.resolve().then(a.bind(a,7346)),Promise.resolve().then(a.bind(a,21444)),Promise.resolve().then(a.bind(a,3033)),Promise.resolve().then(a.bind(a,84436))},14908:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(55732).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},46940:(e,t,a)=>{"use strict";function r(e,[t,a]){return Math.min(a,Math.max(t,e))}a.d(t,{q:()=>r})},51643:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,ScrollArea:()=>i});var r=a(91754);a(93491);var s=a(43168),n=a(82233);function i({className:e,children:t,...a}){return(0,r.jsxs)(s.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",e),...a,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,r.jsx)(s.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,r.jsx)(o,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,r.jsx)(s.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function o({className:e,orientation:t="vertical",...a}){return(0,r.jsx)(s.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...a,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,r.jsx)(s.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},60290:(e,t,a)=>{"use strict";let r;a.r(t),a.d(t,{default:()=>v,generateImageMetadata:()=>h,generateMetadata:()=>g,generateViewport:()=>p,metadata:()=>m});var s=a(63033),n=a(18188),i=a(5434),o=a(45188),d=a(67999),l=a(4590),c=a(23064),u=a(7688);let m={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function x({children:e}){let t=await (0,c.UL)(),a=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:a,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let f={...s},y="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;r=new Proxy(x,{apply:(e,t,a)=>{let r,s,n;try{let e=y?.getStore();r=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:r,baggageHeader:s,headers:n}).apply(t,a)}});let g=void 0,h=void 0,p=void 0,v=r},76261:(e,t,a)=>{Promise.resolve().then(a.bind(a,5434)),Promise.resolve().then(a.bind(a,45188)),Promise.resolve().then(a.bind(a,67999)),Promise.resolve().then(a.bind(a,4590))},80601:(e,t,a)=>{"use strict";a.d(t,{E:()=>d});var r=a(91754);a(93491);var s=a(16435),n=a(25758),i=a(82233);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:a=!1,...n}){let d=a?s.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}}};
//# sourceMappingURL=8243.js.map