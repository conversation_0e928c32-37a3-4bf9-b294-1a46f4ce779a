{"version": 3, "file": "../app/dashboard/student/page.js", "mappings": "yeAIe,SAASA,EAAc,UACpCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,yCCdA,qLCKA,IAAMC,EAAWC,EAAAA,UAAgB,CAAiH,CAAC,WACjJC,CAAS,OACTC,CAAK,CACL,GAAGC,EACJ,CAAEC,IAAQ,UAACC,EAAAA,EAAsB,EAACD,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEL,GAAa,GAAGE,CAAK,UAC7I,UAACE,EAAAA,EAA2B,EAACJ,UAAU,iDAAiDM,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,IAAON,EAAAA,GAAS,EAAG,EAAE,CAAC,OAGpDH,EAASU,WAAW,CAAGJ,EAAAA,EAAsB,CAACI,WAAW,wBCdzD,oDCAA,qGCAA,mECAA,0GCAA,qDCAA,+CCAA,mDCAA,gDCAA,wGCAA,+DCAA,mDCAA,iECAA,gDCAA,qCAAqK,yBCArK,uDCAA,sDCAA,wDCAA,4ECmBM,MAAQ,cAAiB,SAhBK,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,UAAY,EAAE,OAAQ,CAAoB,sBAAK,SAAU,EAC5D,0BCNA,+DCAA,uCAAmK,yBCAnK,oDCAA,iECAA,0DCAA,uDCAA,8LCUM,EAAgB,WAIhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GAIlE,CAAC,EAAkB,EAJwB,CAK/C,EAA4C,EALuC,CAe/E,EAAiB,QAVoC,IAUpC,CACrB,CAAC,EAAmC,WA4GR,EA3G1B,GAAM,MA2GuC,WA1G3C,EACA,EAyGkE,IAzG3D,EAAY,KACnB,IAAK,gBACL,EAAgB,EAChB,GAAG,EACL,CAAI,CAEJ,EAAK,GAAW,MAAY,EAAM,CAAC,EAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,CA8FQ,EA9FW,GAAG,EAAO,EA+FxC,CA/F4C,CA8FN,CA9FE,QAAc,CAAC,KA8FM,kBAC1B,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EAAiB,GAAW,EAhCxB,EAgCoB,CAEhC,GA0FkK,EA5FpH,GAExB,GAAC,EAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,GAAqB,GAAG,EAAS,EA6F5C,CA7FgD,IAAJ,MAAc,CAAC,yBA6FtB,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,EAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,EAAS,GAAS,EAAJ,EAAyB,GAAG,KAAI,EAEjE,MACE,UAAC,GAAiB,MAAO,QAAiB,EAAc,MACtD,mBAAC,IAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,EAAS,GAAS,EAAJ,KAAY,EACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,EAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAiB,oBAKjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,EAAmB,EAAgB,GACnD,CAF2C,KAGzC,MAFgE,EAEhE,EAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,EAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,EAAiB,EAAkC,GAAiC,OAC3E,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,EAAS,GAChB,MAAO,iBAAO,CAChB,CAEA,SAAS,EAAiB,GAAyB,OAG/C,EAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,EAAmB,EAAY,GAA8B,OAGlE,EAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,EAAkB,YAAc,EAiDhC,IAAM,EAAO,EACP,EAAY,0BCpJlB,8CCAA,uCAAmK,yBCAnK,yVCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAmK,CAoBvL,kIAES,EACF,CACP,CACA,QA1BA,IAAsB,uCAAqK,CA0B3L,qIAGA,CACO,CACP,CACA,QAjCA,IAAsB,uCAA4J,CAiClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAlDA,IAAsB,sCAAiJ,CAkDvK,gHACA,gBAlDA,IAAsB,uCAAuJ,CAkD7K,sHACA,aAlDA,IAAsB,uCAAoJ,CAkD1K,mHACA,WAlDA,IAAsB,4CAAgF,CAkDtG,+CACA,cAlDA,IAAsB,4CAAmF,CAkDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,qIAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,+BACA,8BAEA,cACA,YACA,WACA,CAAK,CACL,UACA,YACA,CACA,CAAC,0BC7FD,wDCAA,gECAA,wDCAA,sDCAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,2DCAA,kPCWe,SAASC,IACtB,GAAM,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACC,EAAAA,EAAWA,CAACC,OAAO,IAC9C,CAACC,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAQ3C,GAAIG,EACF,MAAO,UAACE,MAAAA,CAAIjB,UAAU,yDAAgD,eAIxE,GAAI,CAACU,GAAMQ,cACT,CADwB,KACjB,UAACC,EAAAA,CAAwBA,CAAAA,CAACC,SAAS,YAI5C,IAAMC,EAAQ,CACZC,gBAAiB,EACjBC,iBAAkB,EAClBC,aAAc,EACdC,WAAY,EACd,EAuBA,MAAO,WAACR,MAAAA,CAAIjB,UAAU,YAAY0B,wBAAsB,mBAAmBC,0BAAwB,qBAC/F,WAACV,MAAAA,CAAIjB,UAAU,8CACb,WAACiB,MAAAA,WACC,UAACW,KAAAA,CAAG5B,UAAU,6CAAoC,sBAGlD,UAAC6B,IAAAA,CAAE7B,UAAU,iCAAwB,4DAIvC,UAAC8B,IAAIA,CAACC,KAAK,6BAA6BC,UAAnCF,YAAuD,OAAOH,0BAAwB,oBACzF,WAACM,EAAAA,CAAMA,CAAAA,CAACD,sBAAoB,SAASL,0BAAwB,qBAC3D,UAACO,EAAAA,CAAQA,CAAAA,CAAClC,UAAU,eAAegC,sBAAoB,WAAWL,0BAAwB,aAAa,yBAO7G,WAACV,MAAAA,CAAIjB,UAAU,qDACb,WAACmC,EAAAA,EAAIA,CAAAA,CAACH,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACS,EAAAA,EAAUA,CAAAA,CAACpC,UAAU,4DAA4DgC,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACU,EAAAA,EAASA,CAAAA,CAACrC,UAAU,sBAAsBgC,sBAAoB,YAAYL,0BAAwB,oBAAW,qBAG9G,UAACO,EAAAA,CAAQA,CAAAA,CAAClC,UAAU,gCAAgCgC,sBAAoB,WAAWL,0BAAwB,gBAE7G,WAACW,EAAAA,EAAWA,CAAAA,CAACN,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACV,MAAAA,CAAIjB,UAAU,8BAAsBqB,EAAMC,eAAe,GAC1D,UAACO,IAAAA,CAAE7B,UAAU,yCAAgC,6BAIjD,WAACmC,EAAAA,EAAIA,CAAAA,CAACH,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACS,EAAAA,EAAUA,CAAAA,CAACpC,UAAU,4DAA4DgC,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACU,EAAAA,EAASA,CAAAA,CAACrC,UAAU,sBAAsBgC,sBAAoB,YAAYL,0BAAwB,oBAAW,cAC9G,UAACY,EAAAA,CAAUA,CAAAA,CAACvC,UAAU,gCAAgCgC,sBAAoB,aAAaL,0BAAwB,gBAEjH,WAACW,EAAAA,EAAWA,CAAAA,CAACN,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACV,MAAAA,CAAIjB,UAAU,8BAAsBqB,EAAME,gBAAgB,GAC3D,UAACM,IAAAA,CAAE7B,UAAU,yCAAgC,4BAIjD,WAACmC,EAAAA,EAAIA,CAAAA,CAACH,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACS,EAAAA,EAAUA,CAAAA,CAACpC,UAAU,4DAA4DgC,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACU,EAAAA,EAASA,CAAAA,CAACrC,UAAU,sBAAsBgC,sBAAoB,YAAYL,0BAAwB,oBAAW,iBAC9G,UAACa,EAAAA,CAAKA,CAAAA,CAACxC,UAAU,gCAAgCgC,sBAAoB,QAAQL,0BAAwB,gBAEvG,WAACW,EAAAA,EAAWA,CAAAA,CAACN,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACV,MAAAA,CAAIjB,UAAU,8BAAsBqB,EAAMG,YAAY,GACvD,UAACK,IAAAA,CAAE7B,UAAU,yCAAgC,8BAIjD,WAACmC,EAAAA,EAAIA,CAAAA,CAACH,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACS,EAAAA,EAAUA,CAAAA,CAACpC,UAAU,4DAA4DgC,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACU,EAAAA,EAASA,CAAAA,CAACrC,UAAU,sBAAsBgC,sBAAoB,YAAYL,0BAAwB,oBAAW,gBAC9G,UAACc,EAAAA,CAAKA,CAAAA,CAACzC,UAAU,gCAAgCgC,sBAAoB,QAAQL,0BAAwB,gBAEvG,WAACW,EAAAA,EAAWA,CAAAA,CAACN,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACV,MAAAA,CAAIjB,UAAU,8BAAsBqB,EAAMI,UAAU,GACrD,UAACI,IAAAA,CAAE7B,UAAU,yCAAgC,yBAMnD,WAACmC,EAAAA,EAAIA,CAAAA,CAACH,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACS,EAAAA,EAAUA,CAAAA,CAACJ,sBAAoB,aAAaL,0BAAwB,qBACnE,UAACU,EAAAA,EAASA,CAAAA,CAACL,sBAAoB,YAAYL,0BAAwB,oBAAW,eAC9E,UAACe,EAAAA,EAAeA,CAAAA,CAACV,sBAAoB,kBAAkBL,0BAAwB,oBAAW,oDAI5F,WAACW,EAAAA,EAAWA,CAAAA,CAACN,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACV,MAAAA,CAAIjB,UAAU,qBACZsB,CAnGc,CACvBqB,GAAI,EACJC,KAAM,8BACNC,KAAM,aACNC,SAAU,GACVC,OAAQ,cACRC,QAAS,YACX,EAAG,CACDL,GAAI,EACJC,KAAM,gBACNC,KAAM,WACNC,SAAU,GACVC,OAAQ,cACRC,QAAS,YACX,EAAG,CACDL,GAAI,EACJC,KAAM,yBACNC,KAAM,aACNC,SAAU,IACVC,OAAQ,YACRC,QAAS,YACX,EAAE,CA8EyBC,GAAG,CAACC,GAAU,WAACjC,MAAAA,CAAoBjB,UAAU,oEAC1D,WAACiB,MAAAA,CAAIjB,UAAU,6BACb,WAACiB,MAAAA,CAAIjB,UAAU,8CACb,UAAC6B,IAAAA,CAAE7B,UAAU,uBAAekD,EAAON,IAAI,GACvC,WAAC3B,MAAAA,CAAIjB,UAAU,wCACb,UAACmD,EAAAA,CAAKA,CAAAA,CAACC,QAAyB,aAAhBF,EAAOL,IAAI,CAAkB,UAAY,qBACtDK,EAAOL,IAAI,GAEd,UAACM,EAAAA,CAAKA,CAAAA,CAACC,QAA2B,cAAlBF,EAAOH,MAAM,CAAmB,UAAY,mBACzDG,EAAOH,MAAM,SAIpB,WAAC9B,MAAAA,CAAIjB,UAAU,sBACb,WAACiB,MAAAA,CAAIjB,UAAU,sDACb,UAACqD,OAAAA,UAAK,aACN,WAACA,OAAAA,WAAMH,EAAOJ,QAAQ,CAAC,UAEzB,UAAChD,EAAAA,CAAQA,CAAAA,CAACG,MAAOiD,EAAOJ,QAAQ,CAAE9C,UAAU,WAE9C,WAAC6B,IAAAA,CAAE7B,UAAU,0CAAgC,QACrC,IAAIsD,KAAKJ,EAAOF,OAAO,EAAEO,kBAAkB,SAGrD,UAACtC,MAAAA,CAAIjB,UAAU,gBACb,UAAC8B,IAAIA,CAACC,KAAM,CAAC,2BAA2B,EAAEmB,EAAOP,EAAE,EAAE,GAAhDb,OACH,UAACG,EAAAA,CAAMA,CAAAA,CAACmB,QAAQ,UAAUI,KAAK,cACV,cAAlBN,EAAOH,MAAM,CAAmB,SAAW,mBA3BbG,EAAOP,EAAE,KAiCpD,UAAC1B,MAAAA,CAAIjB,UAAU,gBACb,UAAC8B,IAAIA,CAACC,KAAK,6BAA6BC,UAAnCF,YAAuD,OAAOH,0BAAwB,oBACzF,UAACM,EAAAA,CAAMA,CAAAA,CAACmB,QAAQ,UAAUpD,UAAU,SAASgC,sBAAoB,SAASL,0BAAwB,oBAAW,iCAQ3H,oCChKI,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,CACvB,iBAAiB,GACjB,aAAa,WACb,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BE8B,EAoCnB,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,cCvEtB,GDgF8B,KChF9B,+BAAqK,mCDmBjK,sBAAsB,0rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CA7BLA,EAoCnB,IAAC,OAOF,EAEE,EAOF,KAhBkB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,iBASQ", "sources": ["webpack://terang-lms-ui/./src/app/dashboard/student/layout.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/?8902", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/../../../src/icons/clock.ts", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/?bf3e", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/progress.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/?03be", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/?5471", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/./src/app/dashboard/student/page.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?d67a"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function StudentLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has student role\n    requireRole('student');\n  }, []);\n  return <>{children}</>;\n}", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\n", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', __iconNode);\n\nexport default Clock;\n", "module.exports = require(\"node:diagnostics_channel\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\");\n", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "module.exports = require(\"node:fs\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\");\n", "module.exports = require(\"worker_threads\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'student',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/student/page\",\n        pathname: \"/dashboard/student\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");", "'use client';\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { BookOpen, Award, TrendingUp, Clock } from 'lucide-react';\nimport Link from 'next/link';\nimport { authStorage } from '@/lib/auth';\nimport NotAssignedToInstitution from '@/components/not-assigned-to-institution';\nimport { useEffect, useState } from 'react';\nexport default function StudentDashboard() {\n  const [user, setUser] = useState(authStorage.getUser());\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    const currentUser = authStorage.getUser();\n    setUser(currentUser);\n    setIsLoading(false);\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return <div className='flex items-center justify-center min-h-screen'>Loading...</div>;\n  }\n\n  // Show not assigned message if user has no institution\n  if (!user?.institutionId) {\n    return <NotAssignedToInstitution userRole='student' />;\n  }\n\n  // Mock data - in real app, this would come from API\n  const stats = {\n    enrolledCourses: 3,\n    completedCourses: 1,\n    certificates: 1,\n    totalHours: 24\n  };\n  const enrolledCourses = [{\n    id: 1,\n    name: 'Introduction to Mathematics',\n    type: 'self_paced',\n    progress: 85,\n    status: 'in_progress',\n    dueDate: '2024-12-15'\n  }, {\n    id: 2,\n    name: 'Basic Physics',\n    type: 'verified',\n    progress: 45,\n    status: 'in_progress',\n    dueDate: '2024-11-30'\n  }, {\n    id: 3,\n    name: 'Chemistry Fundamentals',\n    type: 'self_paced',\n    progress: 100,\n    status: 'completed',\n    dueDate: '2024-10-20'\n  }];\n  return <div className='space-y-6' data-sentry-component=\"StudentDashboard\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>\r\n            Student Dashboard\r\n          </h1>\r\n          <p className='text-muted-foreground'>\r\n            Track your learning progress and access your courses\r\n          </p>\r\n        </div>\r\n        <Link href='/dashboard/student/courses' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <BookOpen className='mr-2 h-4 w-4' data-sentry-element=\"BookOpen\" data-sentry-source-file=\"page.tsx\" />\r\n            Browse Courses\r\n          </Button>\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">\r\n              Enrolled Courses\r\n            </CardTitle>\r\n            <BookOpen className='text-muted-foreground h-4 w-4' data-sentry-element=\"BookOpen\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.enrolledCourses}</div>\r\n            <p className='text-muted-foreground text-xs'>Active enrollments</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Completed</CardTitle>\r\n            <TrendingUp className='text-muted-foreground h-4 w-4' data-sentry-element=\"TrendingUp\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.completedCourses}</div>\r\n            <p className='text-muted-foreground text-xs'>Courses completed</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Certificates</CardTitle>\r\n            <Award className='text-muted-foreground h-4 w-4' data-sentry-element=\"Award\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.certificates}</div>\r\n            <p className='text-muted-foreground text-xs'>Earned certificates</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Study Hours</CardTitle>\r\n            <Clock className='text-muted-foreground h-4 w-4' data-sentry-element=\"Clock\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.totalHours}</div>\r\n            <p className='text-muted-foreground text-xs'>Total hours</p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Enrolled Courses */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">My Courses</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\r\n            Your current course enrollments and progress\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <div className='space-y-4'>\r\n            {enrolledCourses.map(course => <div key={course.id} className='flex items-center justify-between rounded-lg border p-4'>\r\n                <div className='flex-1 space-y-2'>\r\n                  <div className='flex items-center justify-between'>\r\n                    <p className='font-medium'>{course.name}</p>\r\n                    <div className='flex items-center space-x-2'>\r\n                      <Badge variant={course.type === 'verified' ? 'default' : 'secondary'}>\r\n                        {course.type}\r\n                      </Badge>\r\n                      <Badge variant={course.status === 'completed' ? 'default' : 'outline'}>\r\n                        {course.status}\r\n                      </Badge>\r\n                    </div>\r\n                  </div>\r\n                  <div className='space-y-1'>\r\n                    <div className='flex items-center justify-between text-sm'>\r\n                      <span>Progress</span>\r\n                      <span>{course.progress}%</span>\r\n                    </div>\r\n                    <Progress value={course.progress} className='h-2' />\r\n                  </div>\r\n                  <p className='text-muted-foreground text-xs'>\r\n                    Due: {new Date(course.dueDate).toLocaleDateString()}\r\n                  </p>\r\n                </div>\r\n                <div className='ml-4'>\r\n                  <Link href={`/dashboard/student/courses/${course.id}`}>\r\n                    <Button variant='outline' size='sm'>\r\n                      {course.status === 'completed' ? 'Review' : 'Continue'}\r\n                    </Button>\r\n                  </Link>\r\n                </div>\r\n              </div>)}\r\n          </div>\r\n          <div className='mt-4'>\r\n            <Link href='/dashboard/student/courses' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n              <Button variant='outline' className='w-full' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                View All Courses\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/student',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/student',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/student',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/student',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\n"], "names": ["StudentLayout", "children", "Progress", "React", "className", "value", "props", "ref", "ProgressPrimitive", "cn", "style", "transform", "displayName", "StudentDashboard", "user", "setUser", "useState", "authStorage", "getUser", "isLoading", "setIsLoading", "div", "institutionId", "NotAssignedToInstitution", "userRole", "stats", "enrolledCourses", "completedCourses", "certificates", "totalHours", "data-sentry-component", "data-sentry-source-file", "h1", "p", "Link", "href", "data-sentry-element", "<PERSON><PERSON>", "BookOpen", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "TrendingUp", "Award", "Clock", "CardDescription", "id", "name", "type", "progress", "status", "dueDate", "map", "course", "Badge", "variant", "span", "Date", "toLocaleDateString", "size", "serverComponentModule.default"], "sourceRoot": ""}