try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="774aa83e-05d2-431b-b944-ae5b408cd2ae",e._sentryDebugIdIdentifier="sentry-dbid-774aa83e-05d2-431b-b944-ae5b408cd2ae")}catch(e){}"use strict";(()=>{var e={};e.id=7618,e.ids=[7618],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},42390:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>R,serverHooks:()=>T,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>N,GET:()=>w,HEAD:()=>v,OPTIONS:()=>j,PATCH:()=>b,POST:()=>g,PUT:()=>y});var o=t(3690),n=t(56947),a=t(75250),i=t(63033),d=t(62187),p=t(18621),u=t(32230),c=t(74683),l=t(7688);async function h(e,{params:r}){try{let{id:e}=await r,t=parseInt(e);if(isNaN(t))return d.NextResponse.json({error:"Invalid chapter ID"},{status:400});let s=await p.db.select().from(u.chapters).where((0,c.eq)(u.chapters.id,t)).limit(1);if(0===s.length)return d.NextResponse.json({error:"Chapter not found"},{status:404});let o=s[0],n=await p.db.select().from(u.quizzes).where((0,c.eq)(u.quizzes.chapterId,t));return d.NextResponse.json({chapter:{...o,content:o.content,quizzes:n}})}catch(e){return console.error("Error fetching chapter:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let{id:t}=await r,s=parseInt(t);if(isNaN(s))return d.NextResponse.json({error:"Invalid chapter ID"},{status:400});let{name:o,content:n,orderIndex:a,teacherId:i}=await e.json(),l=await p.db.select().from(u.chapters).where((0,c.eq)(u.chapters.id,s)).limit(1);if(0===l.length)return d.NextResponse.json({error:"Chapter not found"},{status:404});if(i){let e=await p.db.select({chapterId:u.chapters.id,moduleId:u.chapters.moduleId,courseId:u.modules.courseId,teacherId:u.courses.teacherId}).from(u.chapters).leftJoin(u.modules,(0,c.eq)(u.chapters.moduleId,u.modules.id)).leftJoin(u.courses,(0,c.eq)(u.modules.courseId,u.courses.id)).where((0,c.Uo)((0,c.eq)(u.chapters.id,s),(0,c.eq)(u.courses.teacherId,i))).limit(1);if(0===e.length)return d.NextResponse.json({error:"Not authorized to update this chapter"},{status:403})}let h=await p.db.update(u.chapters).set({...o&&{name:o},...n&&{content:JSON.stringify(n)},...void 0!==a&&{orderIndex:a},updatedAt:new Date}).where((0,c.eq)(u.chapters.id,s)).returning(),x={...h[0],content:h[0].content};return d.NextResponse.json({chapter:x,message:"Chapter updated successfully"})}catch(e){return console.error("Error updating chapter:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function q(e,{params:r}){try{let{id:t}=await r,s=parseInt(t);if(isNaN(s))return d.NextResponse.json({error:"Invalid chapter ID"},{status:400});let o=e.nextUrl.searchParams.get("teacherId"),n=await p.db.select().from(u.chapters).where((0,c.eq)(u.chapters.id,s)).limit(1);if(0===n.length)return d.NextResponse.json({error:"Chapter not found"},{status:404});if(o){let e=await p.db.select({chapterId:u.chapters.id,moduleId:u.chapters.moduleId,courseId:u.modules.courseId,teacherId:u.courses.teacherId}).from(u.chapters).leftJoin(u.modules,(0,c.eq)(u.chapters.moduleId,u.modules.id)).leftJoin(u.courses,(0,c.eq)(u.modules.courseId,u.courses.id)).where((0,c.Uo)((0,c.eq)(u.chapters.id,s),(0,c.eq)(u.courses.teacherId,parseInt(o)))).limit(1);if(0===e.length)return d.NextResponse.json({error:"Not authorized to delete this chapter"},{status:403})}return await p.db.delete(u.quizzes).where((0,c.eq)(u.quizzes.chapterId,s)),await p.db.delete(u.chapters).where((0,c.eq)(u.chapters.id,s)),d.NextResponse.json({message:"Chapter deleted successfully"})}catch(e){return console.error("Error deleting chapter:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let f={...i},m="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function I(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=m?.getStore();o=e?.headers}catch{}return l.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/chapters/[id]",headers:o}).apply(t,s)}})}let w=I(h,"GET"),g=I(void 0,"POST"),y=I(x,"PUT"),b=I(void 0,"PATCH"),N=I(q,"DELETE"),v=I(void 0,"HEAD"),j=I(void 0,"OPTIONS"),R=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/chapters/[id]/route",pathname:"/api/chapters/[id]",filename:"route",bundlePath:"app/api/chapters/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\chapters\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:A,serverHooks:T}=R;function P(){return(0,a.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:A})}},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(42390));module.exports=s})();
//# sourceMappingURL=route.js.map