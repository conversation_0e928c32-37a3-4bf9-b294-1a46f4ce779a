try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="af66fb52-ab3e-4d92-bc72-fc13b57db3d4",e._sentryDebugIdIdentifier="sentry-dbid-af66fb52-ab3e-4d92-bc72-fc13b57db3d4")}catch(e){}(()=>{var e={};e.id=2547,e.ids=[2547],e.modules={2086:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(15349).A)("Cancel01Icon",[["path",{d:"M19 5L5 19M5 5L19 19",stroke:"currentColor",key:"k0"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21626:(e,s,a)=>{"use strict";a.d(s,{J:()=>l});var t=a(91754);a(93491);var r=a(66207),n=a(82233);function l({className:e,...s}){return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},21820:e=>{"use strict";e.exports=require("os")},26331:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>Z});var t=a(91754),r=a(93491),n=a(9260),l=a(56682),i=a(59672),c=a(93438),d=a(30047),o=a(66064),m=a(15349);(0,m.A)("Settings01Icon",[["path",{d:"M21.3175 7.14139L20.8239 6.28479C20.4506 5.63696 20.264 5.31305 19.9464 5.18388C19.6288 5.05472 19.2696 5.15664 18.5513 5.36048L17.3311 5.70418C16.8725 5.80994 16.3913 5.74994 15.9726 5.53479L15.6357 5.34042C15.2766 5.11043 15.0004 4.77133 14.8475 4.37274L14.5136 3.37536C14.294 2.71534 14.1842 2.38533 13.9228 2.19657C13.6615 2.00781 13.3143 2.00781 12.6199 2.00781H11.5051C10.8108 2.00781 10.4636 2.00781 10.2022 2.19657C9.94085 2.38533 9.83106 2.71534 9.61149 3.37536L9.27753 4.37274C9.12465 4.77133 8.84845 5.11043 8.48937 5.34042L8.15249 5.53479C7.73374 5.74994 7.25259 5.80994 6.79398 5.70418L5.57375 5.36048C4.85541 5.15664 4.49625 5.05472 4.17867 5.18388C3.86109 5.31305 3.67445 5.63696 3.30115 6.28479L2.80757 7.14139C2.45766 7.74864 2.2827 8.05227 2.31666 8.37549C2.35061 8.69871 2.58483 8.95918 3.05326 9.48012L4.0843 10.6328C4.3363 10.9518 4.51521 11.5078 4.51521 12.0077C4.51521 12.5078 4.33636 13.0636 4.08433 13.3827L3.05326 14.5354C2.58483 15.0564 2.35062 15.3168 2.31666 15.6401C2.2827 15.9633 2.45766 16.2669 2.80757 16.8741L3.30114 17.7307C3.67443 18.3785 3.86109 18.7025 4.17867 18.8316C4.49625 18.9608 4.85542 18.8589 5.57377 18.655L6.79394 18.3113C7.25263 18.2055 7.73387 18.2656 8.15267 18.4808L8.4895 18.6752C8.84851 18.9052 9.12464 19.2442 9.2775 19.6428L9.61149 20.6403C9.83106 21.3003 9.94085 21.6303 10.2022 21.8191C10.4636 22.0078 10.8108 22.0078 11.5051 22.0078H12.6199C13.3143 22.0078 13.6615 22.0078 13.9228 21.8191C14.1842 21.6303 14.294 21.3003 14.5136 20.6403L14.8476 19.6428C15.0004 19.2442 15.2765 18.9052 15.6356 18.6752L15.9724 18.4808C16.3912 18.2656 16.8724 18.2055 17.3311 18.3113L18.5513 18.655C19.2696 18.8589 19.6288 18.9608 19.9464 18.8316C20.264 18.7025 20.4506 18.3785 20.8239 17.7307L21.3175 16.8741C21.6674 16.2669 21.8423 15.9633 21.8084 15.6401C21.7744 15.3168 21.5402 15.0564 21.0718 14.5354L20.0407 13.3827C19.7887 13.0636 19.6098 12.5078 19.6098 12.0077C19.6098 11.5078 19.7888 10.9518 20.0407 10.6328L21.0718 9.48012C21.5402 8.95918 21.7744 8.69871 21.8084 8.37549C21.8423 8.05227 21.6674 7.74864 21.3175 7.14139Z",stroke:"currentColor",key:"k0"}],["path",{d:"M15.5195 12C15.5195 13.933 13.9525 15.5 12.0195 15.5C10.0865 15.5 8.51953 13.933 8.51953 12C8.51953 10.067 10.0865 8.5 12.0195 8.5C13.9525 8.5 15.5195 10.067 15.5195 12Z",stroke:"currentColor",key:"k1"}]]);var u=a(76130);let x=(0,m.A)("ArrowDown01Icon",[["path",{d:"M18 9.00005C18 9.00005 13.5811 15 12 15C10.4188 15 6 9 6 9",stroke:"currentColor",key:"k0"}]]);var h=a(79313);let p=(0,m.A)("Calendar03Icon",[["path",{d:"M18 2V4M6 2V4",stroke:"currentColor",key:"k0"}],["path",{d:"M11.9955 13H12.0045M11.9955 17H12.0045M15.991 13H16M8 13H8.00897M8 17H8.00897",stroke:"currentColor",key:"k1"}],["path",{d:"M3.5 8H20.5",stroke:"currentColor",key:"k2"}],["path",{d:"M2.5 12.2432C2.5 7.88594 2.5 5.70728 3.75212 4.35364C5.00424 3 7.01949 3 11.05 3H12.95C16.9805 3 18.9958 3 20.2479 4.35364C21.5 5.70728 21.5 7.88594 21.5 12.2432V12.7568C21.5 17.1141 21.5 19.2927 20.2479 20.6464C18.9958 22 16.9805 22 12.95 22H11.05C7.01949 22 5.00424 22 3.75212 20.6464C2.5 19.2927 2.5 17.1141 2.5 12.7568V12.2432Z",stroke:"currentColor",key:"k3"}],["path",{d:"M3 8H21",stroke:"currentColor",key:"k4"}]]),y=(0,m.A)("Key01Icon",[["path",{d:"M15.5 14.5C18.8137 14.5 21.5 11.8137 21.5 8.5C21.5 5.18629 18.8137 2.5 15.5 2.5C12.1863 2.5 9.5 5.18629 9.5 8.5C9.5 9.38041 9.68962 10.2165 10.0303 10.9697L2.5 18.5V21.5H5.5V19.5H7.5V17.5H9.5L13.0303 13.9697C13.7835 14.3104 14.6196 14.5 15.5 14.5Z",stroke:"currentColor",key:"k0"}],["path",{d:"M17.5 6.5L16.5 7.5",stroke:"currentColor",key:"k1"}]]);var f=a(30010),g=a(2086),j=a(74829),b=a(70332),v=a(60618),C=a(54090),N=a(80601),k=a(11607);let w=(0,m.A)("Award05Icon",[["path",{d:"M4.5 9.5C4.5 13.6421 7.85786 17 12 17C16.1421 17 19.5 13.6421 19.5 9.5C19.5 5.35786 16.1421 2 12 2C7.85786 2 4.5 5.35786 4.5 9.5Z",stroke:"currentColor",key:"k0"}],["path",{d:"M9 10.1667C9 10.1667 9.75 10.1667 10.5 11.5C10.5 11.5 12.8824 8.16667 15 7.5",stroke:"currentColor",key:"k1"}],["path",{d:"M16.8825 15L17.5527 18.2099C17.9833 20.2723 18.1986 21.3035 17.7563 21.7923C17.3141 22.281 16.546 21.8606 15.0099 21.0198L12.7364 19.7753C12.3734 19.5766 12.1919 19.4773 12 19.4773C11.8081 19.4773 11.6266 19.5766 11.2636 19.7753L8.99008 21.0198C7.45397 21.8606 6.68592 22.281 6.24365 21.7923C5.80139 21.3035 6.01669 20.2723 6.44731 18.2099L7.11752 15",stroke:"currentColor",key:"k2"}]]),A=(0,m.A)("DollarCircleIcon",[["path",{d:"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M14.7102 10.0611C14.6111 9.29844 13.7354 8.06622 12.1608 8.06619C10.3312 8.06616 9.56136 9.07946 9.40515 9.58611C9.16145 10.2638 9.21019 11.6571 11.3547 11.809C14.0354 11.999 15.1093 12.3154 14.9727 13.956C14.836 15.5965 13.3417 15.951 12.1608 15.9129C10.9798 15.875 9.04764 15.3325 8.97266 13.8733M11.9734 6.99805V8.06982M11.9734 15.9031V16.998",stroke:"currentColor",key:"k1"}]]),D=(0,m.A)("Briefcase01Icon",[["path",{d:"M10 13.3333C10 13.0233 10 12.8683 10.0341 12.7412C10.1265 12.3961 10.3961 12.1265 10.7412 12.0341C10.8683 12 11.0233 12 11.3333 12H12.6667C12.9767 12 13.1317 12 13.2588 12.0341C13.6039 12.1265 13.8735 12.3961 13.9659 12.7412C14 12.8683 14 13.0233 14 13.3333V14C14 15.1046 13.1046 16 12 16C10.8954 16 10 15.1046 10 14V13.3333Z",stroke:"currentColor",key:"k0"}],["path",{d:"M13.9 13.5H15.0826C16.3668 13.5 17.0089 13.5 17.5556 13.3842C19.138 13.049 20.429 12.0207 20.9939 10.6455C21.1891 10.1704 21.2687 9.59552 21.428 8.4457C21.4878 8.01405 21.5177 7.79823 21.489 7.62169C21.4052 7.10754 20.9932 6.68638 20.4381 6.54764C20.2475 6.5 20.0065 6.5 19.5244 6.5H4.47562C3.99351 6.5 3.75245 6.5 3.56187 6.54764C3.00682 6.68638 2.59477 7.10754 2.51104 7.62169C2.48229 7.79823 2.51219 8.01405 2.57198 8.4457C2.73128 9.59552 2.81092 10.1704 3.00609 10.6455C3.571 12.0207 4.86198 13.049 6.44436 13.3842C6.99105 13.5 7.63318 13.5 8.91743 13.5H10.1",stroke:"currentColor",key:"k1"}],["path",{d:"M3.5 11.5V13.5C3.5 17.2712 3.5 19.1569 4.60649 20.3284C5.71297 21.5 7.49383 21.5 11.0556 21.5H12.9444C16.5062 21.5 18.287 21.5 19.3935 20.3284C20.5 19.1569 20.5 17.2712 20.5 13.5V11.5",stroke:"currentColor",key:"k2"}],["path",{d:"M15.5 6.5L15.4227 6.14679C15.0377 4.38673 14.8452 3.50671 14.3869 3.00335C13.9286 2.5 13.3199 2.5 12.1023 2.5H11.8977C10.6801 2.5 10.0714 2.5 9.61309 3.00335C9.15478 3.50671 8.96228 4.38673 8.57727 6.14679L8.5 6.5",stroke:"currentColor",key:"k3"}]]),I=(0,m.A)("StarsIcon",[["path",{d:"M3 12C7.5 12 12 7.5 12 3C12 7.5 16.5 12 21 12C16.5 12 12 16.5 12 21C12 16.5 7.5 12 3 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M2 19.5C2.83333 19.5 4.5 17.8333 4.5 17C4.5 17.8333 6.16667 19.5 7 19.5C6.16667 19.5 4.5 21.1667 4.5 22C4.5 21.1667 2.83333 19.5 2 19.5Z",stroke:"currentColor",key:"k1"}],["path",{d:"M16 5C17 5 19 3 19 2C19 3 21 5 22 5C21 5 19 7 19 8C19 7 17 5 16 5Z",stroke:"currentColor",key:"k2"}]]),P=(0,m.A)("Building01Icon",[["path",{d:"M4 22H20",stroke:"currentColor",key:"k0"}],["path",{d:"M17 9H14M18 13H14M18 17H14",stroke:"currentColor",key:"k1"}],["path",{d:"M6 22V3.2C6 2.42385 6.47098 2 7.2 2C8.87221 2 9.70832 2 10.4079 2.1108C14.2589 2.72075 17.2793 5.74106 17.8892 9.59209C18 10.2917 18 11.1278 18 12.8V22",stroke:"currentColor",key:"k2"}]]),M=(0,m.A)("CustomerSupportIcon",[["path",{d:"M17 10.8045C17 10.4588 17 10.286 17.052 10.132C17.2032 9.68444 17.6018 9.51076 18.0011 9.32888C18.45 9.12442 18.6744 9.02219 18.8968 9.0042C19.1493 8.98378 19.4022 9.03818 19.618 9.15929C19.9041 9.31984 20.1036 9.62493 20.3079 9.87302C21.2513 11.0188 21.7229 11.5918 21.8955 12.2236C22.0348 12.7334 22.0348 13.2666 21.8955 13.7764C21.6438 14.6979 20.8485 15.4704 20.2598 16.1854C19.9587 16.5511 19.8081 16.734 19.618 16.8407C19.4022 16.9618 19.1493 17.0162 18.8968 16.9958C18.6744 16.9778 18.45 16.8756 18.0011 16.6711C17.6018 16.4892 17.2032 16.3156 17.052 15.868C17 15.714 17 15.5412 17 15.1955V10.8045Z",stroke:"currentColor",key:"k0"}],["path",{d:"M7 10.8046C7 10.3694 6.98778 9.97821 6.63591 9.6722C6.50793 9.5609 6.33825 9.48361 5.99891 9.32905C5.55001 9.12458 5.32556 9.02235 5.10316 9.00436C4.43591 8.9504 4.07692 9.40581 3.69213 9.87318C2.74875 11.019 2.27706 11.5919 2.10446 12.2237C1.96518 12.7336 1.96518 13.2668 2.10446 13.7766C2.3562 14.6981 3.15152 15.4705 3.74021 16.1856C4.11129 16.6363 4.46577 17.0475 5.10316 16.996C5.32556 16.978 5.55001 16.8757 5.99891 16.6713C6.33825 16.5167 6.50793 16.4394 6.63591 16.3281C6.98778 16.0221 7 15.631 7 15.1957V10.8046Z",stroke:"currentColor",key:"k1"}],["path",{d:"M5 9C5 5.68629 8.13401 3 12 3C15.866 3 19 5.68629 19 9",stroke:"currentColor",key:"k2"}],["path",{d:"M19 17V17.8C19 19.5673 17.2091 21 15 21H13",stroke:"currentColor",key:"k3"}]]),T=({course:e,activeTab:s,onTabChange:a})=>{let r=(e,s="IDR")=>"IDR"===s?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:s}).format(e);return(0,t.jsxs)(C.tU,{value:s,onValueChange:a,className:"w-full","data-sentry-element":"Tabs","data-sentry-component":"CourseDetailTabs","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsxs)(C.j7,{className:"grid w-full grid-cols-6 mb-6","data-sentry-element":"TabsList","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsxs)(C.Xi,{value:"overview",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(o.A,{className:"h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Ringkasan"]}),(0,t.jsxs)(C.Xi,{value:"admissions",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(k.A,{className:"h-4 w-4","data-sentry-element":"GraduationCapIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Penerimaan"]}),(0,t.jsxs)(C.Xi,{value:"academics",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(w,{className:"h-4 w-4","data-sentry-element":"AwardIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Akademik"]}),(0,t.jsxs)(C.Xi,{value:"tuition",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(A,{className:"h-4 w-4","data-sentry-element":"DollarCircleIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Biaya"]}),(0,t.jsxs)(C.Xi,{value:"careers",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(D,{className:"h-4 w-4","data-sentry-element":"BriefcaseIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Karier"]}),(0,t.jsxs)(C.Xi,{value:"experience",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(I,{className:"h-4 w-4","data-sentry-element":"StarIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Pengalaman"]})]}),(0,t.jsx)(C.av,{value:"overview",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(o.A,{className:"h-5 w-5","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Ringkasan Kursus"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold mb-2",children:"Deskripsi"}),(0,t.jsx)("p",{className:"text-gray-700",children:e.description})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Detail Kursus"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"UsersIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:["Instruktur: ",e.instructor]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"CalendarIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:["Durasi: ",new Date(e.startDate).toLocaleDateString("id-ID")," - "," ",new Date(e.endDate).toLocaleDateString("id-ID")]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:[e.modules.length," modul"]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"mr-2 h-4 w-4 text-gray-500","data-sentry-element":"CheckCircleIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),(0,t.jsxs)("span",{children:["Nilai kelulusan: ",e.minPassingScore,"%"]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Apa yang Akan Anda Pelajari"}),(0,t.jsxs)("div",{className:"space-y-2",children:[e.modules.slice(0,3).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e.title})]},e.id)),e.modules.length>3&&(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["+",e.modules.length-3," modul lagi"]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Sertifikat"}),(0,t.jsx)("div",{className:"flex items-center gap-2",children:e.certificate.isEligible?(0,t.jsxs)(N.E,{variant:"default",className:"bg-green-100 text-green-800",children:[(0,t.jsx)(w,{className:"mr-1 h-3 w-3"}),"Sertifikat Tersedia"]}):(0,t.jsx)(N.E,{variant:"secondary",children:"Tidak Ada Sertifikat"})})]})]})]})}),(0,t.jsx)(C.av,{value:"admissions",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(k.A,{className:"h-5 w-5","data-sentry-element":"GraduationCapIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Informasi Penerimaan"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:[e.admissions?.requirements&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Persyaratan"}),(0,t.jsx)("ul",{className:"space-y-1",children:e.admissions.requirements.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]}),e.admissions?.prerequisites&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Prasyarat"}),(0,t.jsx)("ul",{className:"space-y-1",children:e.admissions.prerequisites.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]}),e.admissions?.applicationDeadline&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Batas Waktu Pendaftaran"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-sm",children:new Date(e.admissions.applicationDeadline).toLocaleDateString("id-ID")})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Opsi Pendaftaran"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:["code"===e.enrollmentType&&(0,t.jsx)(N.E,{variant:"outline",children:"Kode Pendaftaran Diperlukan"}),"invitation"===e.enrollmentType&&(0,t.jsx)(N.E,{variant:"outline",children:"Hanya Undangan"}),"purchase"===e.enrollmentType&&(0,t.jsx)(N.E,{variant:"outline",children:"Pembelian Langsung"}),"both"===e.enrollmentType&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(N.E,{variant:"outline",children:"Kode Pendaftaran"}),(0,t.jsx)(N.E,{variant:"outline",children:"Pembelian Langsung"})]})]})]})]})]})}),(0,t.jsx)(C.av,{value:"academics",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(w,{className:"h-5 w-5","data-sentry-element":"AwardIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Informasi Akademik"]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:[e.academics&&(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Struktur Kursus"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Kredit:"}),(0,t.jsx)(N.E,{variant:"secondary",children:e.academics.credits})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Beban Kerja:"}),(0,t.jsx)("span",{className:"font-medium",children:e.academics.workload})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Metode Penilaian"}),(0,t.jsx)("ul",{className:"space-y-1",children:e.academics.assessment.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Modul Kursus"}),(0,t.jsx)("div",{className:"space-y-2",children:e.modules.map((e,s)=>(0,t.jsx)(n.Zp,{className:"p-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h5",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-gray-500",children:[e.chapters.length," bab"]})]}),(0,t.jsxs)(N.E,{variant:"outline",children:["Modul ",s+1]})]})},e.id))})]})]})]})}),(0,t.jsx)(C.av,{value:"tuition",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(A,{className:"h-5 w-5","data-sentry-element":"DollarCircleIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Biaya & Pembiayaan"]})}),(0,t.jsx)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:e.tuitionAndFinancing?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Biaya Kursus"}),(0,t.jsx)("div",{className:"text-3xl font-bold text-green-600",children:r(e.tuitionAndFinancing.totalCost,e.currency)}),e.price&&e.price<e.tuitionAndFinancing.totalCost&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Penawaran khusus: ",r(e.price,e.currency),(0,t.jsxs)(N.E,{variant:"destructive",className:"ml-2",children:[Math.round((e.tuitionAndFinancing.totalCost-e.price)/e.tuitionAndFinancing.totalCost*100),"% OFF"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Opsi Pembayaran"}),(0,t.jsx)("ul",{className:"space-y-1",children:e.tuitionAndFinancing.paymentOptions.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]}),e.tuitionAndFinancing.scholarships&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Beasiswa Tersedia"}),(0,t.jsx)("ul",{className:"space-y-1",children:e.tuitionAndFinancing.scholarships.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(w,{className:"h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-2",children:"GRATIS"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Kursus ini tersedia tanpa biaya"})]})})]})}),(0,t.jsx)(C.av,{value:"careers",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(D,{className:"h-5 w-5","data-sentry-element":"BriefcaseIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Prospek Karier"]})}),(0,t.jsx)(n.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:e.careers?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Prospek Karier"}),(0,t.jsx)("ul",{className:"space-y-1",children:e.careers.outcomes.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Industri"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:e.careers.industries.map((e,s)=>(0,t.jsx)(N.E,{variant:"outline",children:e},s))})]}),e.careers.averageSalary&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Gaji Rata-rata"}),(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.careers.averageSalary})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(D,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Informasi karier tidak tersedia"})]})})]})}),(0,t.jsx)(C.av,{value:"experience",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"course-detail-tabs.tsx",children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"course-detail-tabs.tsx",children:[(0,t.jsx)(I,{className:"h-5 w-5","data-sentry-element":"StarIcon","data-sentry-source-file":"course-detail-tabs.tsx"}),"Pengalaman Mahasiswa"]})}),(0,t.jsx)(n.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"course-detail-tabs.tsx",children:e.studentExperience?(0,t.jsxs)(t.Fragment,{children:[e.studentExperience.testimonials.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold mb-4",children:"Testimoni Mahasiswa"}),(0,t.jsx)("div",{className:"space-y-4",children:e.studentExperience.testimonials.map((e,s)=>(0,t.jsx)(n.Zp,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"})})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{className:"text-sm italic mb-2",children:['"',e.feedback,'"']}),(0,t.jsxs)("p",{className:"font-medium text-sm",children:["- ",e.name]})]})]})},s))})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[e.studentExperience.facilities.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-semibold mb-2 flex items-center gap-2",children:[(0,t.jsx)(P,{className:"h-4 w-4"}),"Fasilitas"]}),(0,t.jsx)("ul",{className:"space-y-1",children:e.studentExperience.facilities.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-green-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]}),e.studentExperience.support.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-semibold mb-2 flex items-center gap-2",children:[(0,t.jsx)(M,{className:"h-4 w-4"}),"Dukungan Mahasiswa"]}),(0,t.jsx)("ul",{className:"space-y-1",children:e.studentExperience.support.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-sm",children:e})]},s))})]})]})]}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(I,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Informasi pengalaman mahasiswa tidak tersedia"})]})})]})})]})};var S=a(21626),L=a(92501);let E=(0,m.A)("CreditCardIcon",[["path",{d:"M2 12C2 8.46252 2 6.69377 3.0528 5.5129C3.22119 5.32403 3.40678 5.14935 3.60746 4.99087C4.86213 4 6.74142 4 10.5 4H13.5C17.2586 4 19.1379 4 20.3925 4.99087C20.5932 5.14935 20.7788 5.32403 20.9472 5.5129C22 6.69377 22 8.46252 22 12C22 15.5375 22 17.3062 20.9472 18.4871C20.7788 18.676 20.5932 18.8506 20.3925 19.0091C19.1379 20 17.2586 20 13.5 20H10.5C6.74142 20 4.86213 20 3.60746 19.0091C3.40678 18.8506 3.22119 18.676 3.0528 18.4871C2 17.3062 2 15.5375 2 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M10 16H11.5",stroke:"currentColor",key:"k1"}],["path",{d:"M14.5 16L18 16",stroke:"currentColor",key:"k2"}],["path",{d:"M2 9H22",stroke:"currentColor",key:"k3"}]]);var B=a(40053),R=a(34305);let F=({course:e,isOpen:s,onClose:a,onPaymentSuccess:n})=>{let[d,o]=(0,r.useState)("card"),[m,u]=(0,r.useState)(!1),[x,h]=(0,r.useState)("details"),[p,y]=(0,r.useState)({cardNumber:"",expiryDate:"",cvv:"",cardholderName:"",email:"",billingAddress:""}),j=(e,s="IDR")=>"IDR"===s?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:s}).format(e),b=(e,s)=>{y(a=>({...a,[e]:s}))},v=()=>"card"===d?p.cardNumber&&p.expiryDate&&p.cvv&&p.cardholderName&&p.email:p.email,C=async()=>{if(v()){u(!0),h("processing");try{await new Promise(e=>setTimeout(e,3e3)),Math.random()>.1?(h("success"),setTimeout(()=>{n(),a(),k()},2e3)):h("error")}catch(e){h("error")}finally{u(!1)}}},k=()=>{h("details"),y({cardNumber:"",expiryDate:"",cvv:"",cardholderName:"",email:"",billingAddress:""}),u(!1)},w=()=>{m||"success"===x||(a(),k())},D=()=>(0,t.jsxs)("div",{className:"space-y-6","data-sentry-component":"renderPaymentDetails","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-semibold mb-2",children:"Ringkasan Kursus"}),(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["by ",e.instructor]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("p",{className:"text-xl font-bold text-green-600",children:e.price?j(e.price,e.currency):"Free"})})]}),(0,t.jsxs)("div",{className:"flex gap-2 text-sm text-gray-600",children:[(0,t.jsxs)(N.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"payment-modal.tsx",children:[e.modules.length," modul"]}),e.certificate.isEligible&&(0,t.jsx)(N.E,{variant:"outline",children:"Sertifikat disertakan"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{className:"text-base font-semibold mb-3 block","data-sentry-element":"Label","data-sentry-source-file":"payment-modal.tsx",children:"Metode Pembayaran"}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,t.jsxs)(l.$,{variant:"card"===d?"default":"outline",onClick:()=>o("card"),className:"h-16 flex-col gap-1","data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(E,{className:"h-5 w-5","data-sentry-element":"CreditCardIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{className:"text-xs",children:"Kartu Kredit"})]}),(0,t.jsxs)(l.$,{variant:"paypal"===d?"default":"outline",onClick:()=>o("paypal"),className:"h-16 flex-col gap-1","data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(A,{className:"h-5 w-5","data-sentry-element":"DollarCircleIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{className:"text-xs",children:"PayPal"})]}),(0,t.jsxs)(l.$,{variant:"bank"===d?"default":"outline",onClick:()=>o("bank"),className:"h-16 flex-col gap-1","data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(B.A,{className:"h-5 w-5","data-sentry-element":"ShoppingCartIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{className:"text-xs",children:"Transfer Bank"})]})]})]}),"card"===d&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"email",children:"Alamat Email"}),(0,t.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:p.email,onChange:e=>b("email",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"cardholderName",children:"Nama Pemegang Kartu"}),(0,t.jsx)(i.p,{id:"cardholderName",placeholder:"Nama lengkap pada kartu",value:p.cardholderName,onChange:e=>b("cardholderName",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"cardNumber",children:"Nomor Kartu"}),(0,t.jsx)(i.p,{id:"cardNumber",placeholder:"1234 5678 9012 3456",value:p.cardNumber,onChange:e=>b("cardNumber",e.target.value)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"expiryDate",children:"Tanggal Kedaluwarsa"}),(0,t.jsx)(i.p,{id:"expiryDate",placeholder:"BB/TT",value:p.expiryDate,onChange:e=>b("expiryDate",e.target.value)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"cvv",children:"CVV"}),(0,t.jsx)(i.p,{id:"cvv",placeholder:"123",value:p.cvv,onChange:e=>b("cvv",e.target.value)})]})]})]}),"paypal"===d&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"email",children:"Alamat Email"}),(0,t.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:p.email,onChange:e=>b("email",e.target.value)})]}),(0,t.jsx)("div",{className:"text-center py-4",children:(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Anda akan dialihkan ke PayPal untuk menyelesaikan pembayaran"})})]}),"bank"===d&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(S.J,{htmlFor:"email",children:"Alamat Email"}),(0,t.jsx)(i.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:p.email,onChange:e=>b("email",e.target.value)})]}),(0,t.jsx)("div",{className:"text-center py-4",children:(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Instruksi transfer bank akan dikirim ke email Anda"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg",children:[(0,t.jsx)(R.A,{className:"h-4 w-4","data-sentry-element":"LockIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("span",{children:"Informasi pembayaran Anda dienkripsi dan aman"})]}),(0,t.jsx)(L.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:w,className:"flex-1",disabled:m,"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:"Batal"}),(0,t.jsxs)(l.$,{onClick:C,className:"flex-1 bg-green-600 hover:bg-green-700",disabled:!v()||m,"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(B.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ShoppingCartIcon","data-sentry-source-file":"payment-modal.tsx"}),"Selesaikan Pembelian"]})]})]}),I=()=>(0,t.jsxs)("div",{className:"text-center py-8","data-sentry-component":"renderProcessing","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)("div",{className:"animate-spin h-12 w-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),(0,t.jsx)("h4",{className:"text-lg font-semibold mb-2",children:"Memproses Pembayaran"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Harap tunggu sementara kami memproses pembayaran Anda..."})]}),P=()=>(0,t.jsxs)("div",{className:"text-center py-8","data-sentry-component":"renderSuccess","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(f.A,{className:"h-16 w-16 text-green-600 mx-auto mb-4","data-sentry-element":"CheckCircleIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("h4",{className:"text-xl font-semibold mb-2",children:"Pembayaran Berhasil!"}),(0,t.jsxs)("p",{className:"text-gray-600 mb-4",children:["Anda telah berhasil terdaftar di ",e.name]}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Mengalihkan ke konten kursus..."})]}),M=()=>(0,t.jsxs)("div",{className:"text-center py-8","data-sentry-component":"renderError","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(g.A,{className:"h-16 w-16 text-red-600 mx-auto mb-4","data-sentry-element":"XCircleIcon","data-sentry-source-file":"payment-modal.tsx"}),(0,t.jsx)("h4",{className:"text-xl font-semibold mb-2",children:"Pembayaran Gagal"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Terjadi masalah saat memproses pembayaran Anda. Silakan coba lagi."}),(0,t.jsxs)("div",{className:"flex gap-3 justify-center",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:w,"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:"Batal"}),(0,t.jsx)(l.$,{onClick:()=>h("details"),"data-sentry-element":"Button","data-sentry-source-file":"payment-modal.tsx",children:"Coba Lagi"})]})]});return(0,t.jsx)(c.lG,{open:s,onOpenChange:w,"data-sentry-element":"Dialog","data-sentry-component":"PaymentModal","data-sentry-source-file":"payment-modal.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-md max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsxs)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsxs)(c.L3,{className:"flex items-center gap-2","data-sentry-element":"DialogTitle","data-sentry-source-file":"payment-modal.tsx",children:[(0,t.jsx)(B.A,{className:"h-5 w-5","data-sentry-element":"ShoppingCartIcon","data-sentry-source-file":"payment-modal.tsx"}),"success"===x?"Pembelian Selesai":"Beli Kursus"]}),"details"===x&&(0,t.jsx)(c.rr,{children:"Selesaikan pembelian Anda untuk mendapatkan akses instan ke kursus"})]}),(()=>{switch(x){case"processing":return I();case"success":return P();case"error":return M();default:return D()}})()]})})};var H=a(47594),q=a(15854),G=a(16041),K=a.n(G);let _=({course:e,isOpen:s,onClose:a,actionType:r})=>(0,t.jsx)(c.lG,{open:s,onOpenChange:()=>{},"data-sentry-element":"Dialog","data-sentry-component":"CourseSuccessModal","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-2xl w-[95vw] max-h-[85vh] flex flex-col p-0 gap-0","data-sentry-element":"DialogContent","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)("div",{className:"flex-shrink-0 p-6 pb-4",children:(0,t.jsxs)(c.c7,{className:"text-center","data-sentry-element":"DialogHeader","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(f.A,{className:"h-8 w-8 text-green-600","data-sentry-element":"CheckCircleIcon","data-sentry-source-file":"course-success-modal.tsx"})}),(0,t.jsx)(c.L3,{className:"text-2xl font-bold text-green-900 text-center","data-sentry-element":"DialogTitle","data-sentry-source-file":"course-success-modal.tsx",children:"purchase"===r?"Pembelian Berhasil!":"Pendaftaran Berhasil!"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"purchase"===r?"Selamat! Anda telah berhasil membeli dan terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran.":"Selamat! Anda telah berhasil terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran."})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto px-6 min-h-0",children:(0,t.jsx)(n.Zp,{className:"border-2 border-green-200 bg-green-50/30","data-sentry-element":"Card","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(n.Wu,{className:"p-0","data-sentry-element":"CardContent","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsxs)("div",{className:"h-32 rounded-t-lg flex items-center justify-center relative overflow-hidden",children:[e.thumbnail?(0,t.jsx)(q.default,{src:e.thumbnail,alt:e.name,fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}):(0,t.jsxs)("div",{className:"h-32 bg-gradient-to-br from-blue-500 to-purple-600 w-full flex items-center justify-center",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,t.jsxs)("div",{className:"relative z-10 text-center text-white",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 mx-auto mb-2 opacity-80"}),(0,t.jsx)("p",{className:"text-sm font-medium",children:e.code})]})]}),"purchase"===r&&e.price&&(0,t.jsx)(N.E,{className:"absolute top-3 right-3 bg-green-600 hover:bg-green-600",variant:"default",children:((e,s="IDR")=>"IDR"===s?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:s}).format(e))(e.price,e.currency)})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-bold text-gray-900 line-clamp-2",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:e.description})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4","data-sentry-element":"UsersIcon","data-sentry-source-file":"course-success-modal.tsx"}),(0,t.jsxs)("span",{children:["Instruktur: ",e.instructor]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p,{className:"mr-2 h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"course-success-modal.tsx"}),(0,t.jsxs)("span",{children:[new Date(e.startDate).toLocaleDateString("id-ID")," - "," ",new Date(e.endDate).toLocaleDateString("id-ID")]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-success-modal.tsx"}),(0,t.jsxs)("span",{children:[e.modules.length," modul"]})]}),e.certificate.isEligible&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(w,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Sertifikat tersedia"})]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.certificate.isEligible&&(0,t.jsxs)(N.E,{variant:"secondary",children:[(0,t.jsx)(w,{className:"mr-1 h-3 w-3"}),"Sertifikat"]}),e.academics?.workload&&(0,t.jsx)(N.E,{variant:"outline",children:e.academics.workload}),e.academics?.credits&&(0,t.jsxs)(N.E,{variant:"outline",children:[e.academics.credits," kredit"]})]})]})]})})}),(0,t.jsxs)("div",{className:"flex-shrink-0 border-t bg-white p-6 space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsx)(K(),{href:`/my-courses/${e.id}`,className:"flex-1","data-sentry-element":"Link","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(l.$,{className:"w-full bg-green-600 hover:bg-green-700",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)(H.A,{className:"mr-2 h-4 w-4","data-sentry-element":"PlayIcon","data-sentry-source-file":"course-success-modal.tsx"}),"Mulai Belajar Sekarang",(0,t.jsx)(u.A,{className:"ml-2 h-4 w-4","data-sentry-element":"ArrowRightIcon","data-sentry-source-file":"course-success-modal.tsx"})]})}),(0,t.jsx)(K(),{href:"/my-courses",className:"flex-1","data-sentry-element":"Link","data-sentry-source-file":"course-success-modal.tsx",children:(0,t.jsxs)(l.$,{variant:"outline",className:"w-full",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"course-success-modal.tsx",children:[(0,t.jsx)(o.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-success-modal.tsx"}),"Lihat Semua Kursus Saya"]})})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(l.$,{variant:"ghost",onClick:a,className:"text-gray-500 hover:text-gray-700","data-sentry-element":"Button","data-sentry-source-file":"course-success-modal.tsx",children:"Tutup dan kembali ke katalog"})})]})]})}),Z=()=>{let{isEnrolled:e,enrollInCourseWithPurchase:s,isEnrolledInCourse:a}=(0,j.q)(),{flags:m,setFlag:C}=(0,b.h)(),[N,k]=(0,r.useState)(null),[w,A]=(0,r.useState)(""),[D,I]=(0,r.useState)(!1),[P,M]=(0,r.useState)(!1),[S,L]=(0,r.useState)(!1),[E,B]=(0,r.useState)(!1),[R,H]=(0,r.useState)(!1),[q,G]=(0,r.useState)("purchase"),[Z,V]=(0,r.useState)("overview"),[$,z]=(0,r.useState)(""),[O,U]=(0,r.useState)({show:!1,message:"",type:"success"}),[W,X]=(0,r.useState)(!0),[J,Y]=(0,r.useState)(!1),[Q,ee]=(0,r.useState)(!1),es=(0,r.useRef)(null),[ea,et]=(0,r.useState)([]),[er,en]=(0,r.useState)(!0),el=()=>{let e=es.current;if(!e)return;let{scrollTop:s,scrollHeight:a,clientHeight:t}=e;Y(s>5),ee(a-t>100&&s<a-t-5),s>0&&X(!1)};(0,r.useEffect)(()=>{let e=es.current;if(!e)return;el();let s=setTimeout(()=>el(),100),a=setTimeout(()=>el(),500),t=setTimeout(()=>el(),1e3),r=new ResizeObserver(()=>{el()});return r.observe(e),()=>{clearTimeout(s),clearTimeout(a),clearTimeout(t),r.disconnect()}},[P,N]),(0,r.useEffect)(()=>{if(P&&N){let e=setTimeout(()=>el(),200);return()=>clearTimeout(e)}},[Z,P,N]),(0,r.useEffect)(()=>{ei()},[]);let ei=async()=>{try{en(!0);let e=await fetch("/api/courses?public=true"),s=await e.json();s.success?et(s.courses||[]):ec("Failed to fetch courses","error")}catch(e){console.error("Error fetching courses:",e),ec("Failed to fetch courses","error")}finally{en(!1)}},ec=(e,s="success")=>{U({show:!0,message:e,type:s}),setTimeout(()=>{U(e=>({...e,show:!1}))},3e3)},ed=e=>{k(e),V("overview"),M(!0)},eo=e=>{k(e),I(!0)},em=e=>{k(e),L(!0)};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8","data-sentry-component":"AvailableCoursesPage","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)("div",{className:"mx-auto max-w-7xl space-y-6 pb-8",children:[(0,t.jsx)(d.B,{"data-sentry-element":"Breadcrumbs","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(o.A,{className:"h-8 w-8 text-[var(--iai-primary)]","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"page.tsx"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Kursus Tersedia"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Jelajahi dan daftar kursus profesional"})]})]}),!1]}),!1,e&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-green-900",children:"Anda sudah terdaftar!"}),(0,t.jsx)("p",{className:"text-sm text-green-700",children:"Akses kursus yang Anda daftari dan lanjutkan belajar"})]}),(0,t.jsx)(K(),{href:"/my-courses",children:(0,t.jsxs)(l.$,{variant:"iai",children:["Ke Kursus Saya",(0,t.jsx)(u.A,{className:"ml-2 h-4 w-4"})]})})]})}),er?(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[...Array(6)].map((e,s)=>(0,t.jsxs)(n.Zp,{className:"overflow-hidden animate-pulse",children:[(0,t.jsx)("div",{className:"aspect-video bg-gray-200"}),(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3 mb-4"}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]})]})]},s))}):(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:ea.map(e=>(0,t.jsx)(v.A,{course:e,onClick:()=>ed(e),onEnroll:()=>eo(e),onPurchase:()=>em(e),isEnrolled:a(e.id)},e.id))}),!er&&0===ea.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Tidak ada kursus ditemukan"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Tidak ada kursus yang tersedia saat ini"})]}),(0,t.jsx)(c.lG,{open:P,onOpenChange:e=>{M(e),e||k(null)},"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-4xl p-0 gap-0 h-[90vh] flex flex-col","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsx)("div",{className:"p-6 pb-0 flex-shrink-0",children:(0,t.jsx)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:(0,t.jsx)(c.L3,{className:"text-2xl","data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:N?.name})})}),(0,t.jsxs)("div",{ref:es,onScroll:el,className:`flex-1 overflow-y-auto scrollbar-visible min-h-0 relative scroll-container ${W?"scroll-hint":""} ${J?"can-scroll-up":""} ${Q?"can-scroll-down":""}`,children:[(0,t.jsx)("style",{dangerouslySetInnerHTML:{__html:`
                  .scrollbar-visible::-webkit-scrollbar {
                    width: 12px;
                  }
                  .scrollbar-visible::-webkit-scrollbar-track {
                    background: rgb(243 244 246); /* gray-100 */
                    border-radius: 6px;
                  }
                  .scrollbar-visible::-webkit-scrollbar-thumb {
                    background: rgb(209 213 219); /* gray-300 */
                    border-radius: 6px;
                  }
                  .scrollbar-visible::-webkit-scrollbar-thumb:hover {
                    background: rgb(156 163 175); /* gray-400 */
                  }
                  /* For Firefox */
                  .scrollbar-visible {
                    scrollbar-width: thin;
                    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
                  }
                  
                  /* Scroll Indicators */
                  .scroll-container::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 20px;
                    background: linear-gradient(to bottom, rgba(255,255,255,0.9), transparent);
                    pointer-events: none;
                    z-index: 10;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                  }
                  
                  .scroll-container::after {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 20px;
                    background: linear-gradient(to top, rgba(255,255,255,0.9), transparent);
                    pointer-events: none;
                    z-index: 10;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                  }
                  
                  .scroll-container.can-scroll-up::before {
                    opacity: 1;
                  }
                  
                  .scroll-container.can-scroll-down::after {
                    opacity: 1;
                  }
                  
                  /* Pulsing scrollbar animation for initial hint */
                  @keyframes pulse-scrollbar {
                    0%, 100% { opacity: 0.6; }
                    50% { opacity: 1; }
                  }
                  
                  .scroll-hint .scrollbar-visible::-webkit-scrollbar-thumb {
                    animation: pulse-scrollbar 2s infinite;
                  }
                  
                  /* Scroll instruction overlay */
                  .scroll-instruction {
                    position: absolute;
                    top: 50%;
                    right: 20px;
                    transform: translateY(-50%);
                    background: rgba(59, 130, 246, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 8px;
                    font-size: 12px;
                    z-index: 20;
                    animation: fadeInOut 4s ease-in-out;
                    pointer-events: none;
                  }
                  
                  @keyframes fadeInOut {
                    0%, 100% { opacity: 0; transform: translateY(-50%) translateX(10px); }
                    10%, 90% { opacity: 1; transform: translateY(-50%) translateX(0); }
                  }
                `}}),(0,t.jsx)("div",{className:"p-6 pt-4",children:N&&(0,t.jsx)(T,{course:N,activeTab:Z,onTabChange:V})})]}),Q&&(0,t.jsx)("div",{className:"flex justify-center py-2 bg-gray-50 border-t border-gray-100",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-500 text-sm animate-bounce",children:[(0,t.jsx)(x,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Gulir ke bawah untuk melihat semua detail"}),(0,t.jsx)(x,{className:"h-4 w-4"})]})}),N&&(0,t.jsx)("div",{className:"p-6 pt-4 border-t bg-white flex-shrink-0",children:(0,t.jsxs)("div",{className:"flex gap-4",children:[N.isPurchasable&&m.enableCoursePurchase&&(0,t.jsxs)(l.$,{onClick:()=>em(N),variant:"iai",className:"flex-1",children:["Beli seharga ",N.price?"IDR"===N.currency?"Rp"+new Intl.NumberFormat("id-ID").format(N.price):new Intl.NumberFormat("id-ID",{style:"currency",currency:N.currency||"IDR"}).format(N.price):"Gratis"]}),m.enableEnrollmentCode&&N.enrollmentCode&&(0,t.jsx)(l.$,{onClick:()=>eo(N),variant:"outline",className:"flex-1",children:"Gunakan Kode Pendaftaran"})]})})]})}),N&&(0,t.jsx)(F,{course:N,isOpen:S,onClose:()=>L(!1),onPaymentSuccess:()=>{N&&(s(N),L(!1),M(!1),G("purchase"),H(!0))}}),N&&(0,t.jsx)(_,{course:N,isOpen:R,onClose:()=>{H(!1),k(null)},actionType:q}),(0,t.jsx)(c.lG,{open:D,onOpenChange:e=>{I(e),e||(z(""),A(""),k(null))},"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,t.jsxs)(c.Cf,{className:"sm:max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:[(0,t.jsxs)(c.L3,{className:"text-xl","data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:["Daftar di ",N?.name]}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:"Masukkan kode pendaftaran yang diberikan oleh instruktur Anda"})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2 rounded-lg bg-gray-50 p-4",children:[(0,t.jsxs)("p",{className:"flex items-center text-gray-700",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4","data-sentry-element":"UsersIcon","data-sentry-source-file":"page.tsx"}),"Instruktur: ",N?.instructor]}),N&&(0,t.jsxs)("p",{className:"flex items-center text-gray-700",children:[(0,t.jsx)(p,{className:"mr-2 h-4 w-4"}),"Durasi: ",new Date(N.startDate).toLocaleDateString("id-ID")," - ",new Date(N.endDate).toLocaleDateString("id-ID")]})]}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y,{className:`absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform ${$?"text-red-500":"text-gray-400"}`,"data-sentry-element":"KeyIcon","data-sentry-source-file":"page.tsx"}),(0,t.jsx)(i.p,{placeholder:N?`Masukkan kode (contoh: ${N.enrollmentCode})`:"Masukkan kode pendaftaran",value:w,onChange:e=>A(e.target.value),className:`pl-10 ${$?"border-red-500 focus:border-red-500 focus:ring-red-500":""}`,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),$&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:$})]}),(0,t.jsx)(l.$,{onClick:()=>{if(!N)return;let e=N.enrollmentCode;if(a(N.id))return void z("You are already enrolled in this course.");e===w?(s(N),I(!1),M(!1),z(""),A(""),G("enrollment"),H(!0)):z("Invalid enrollment code. Please try again.")},className:"w-full",size:"lg","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Selesaikan Pendaftaran"})]})]})}),!1,O.show&&(0,t.jsx)("div",{className:"animate-in slide-in-from-bottom-2 fixed right-4 bottom-4 z-50",children:(0,t.jsxs)("div",{className:`flex min-w-[300px] items-center space-x-3 rounded-lg px-6 py-4 shadow-lg ${"success"===O.type?"bg-[var(--iai-primary)] text-white":"loading"===O.type?"bg-blue-600 text-white":"bg-red-600 text-white"} `,children:["success"===O.type?(0,t.jsx)(f.A,{className:"h-5 w-5 flex-shrink-0"}):"loading"===O.type?(0,t.jsx)("div",{className:"h-5 w-5 flex-shrink-0 animate-spin border-2 border-white border-t-transparent rounded-full"}):(0,t.jsx)(g.A,{className:"h-5 w-5 flex-shrink-0"}),(0,t.jsx)("p",{className:"font-medium",children:O.message})]})})]})})}},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30010:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(15349).A)("CheckmarkCircle01Icon",[["path",{d:"M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8",stroke:"currentColor",key:"k1"}]])},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},34305:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(15349).A)("LockIcon",[["path",{d:"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M12 13C13.1046 13 14 12.1046 14 11C14 9.89543 13.1046 9 12 9C10.8954 9 10 9.89543 10 11C10 12.1046 10.8954 13 12 13ZM12 13L12 16",stroke:"currentColor",key:"k1"}]])},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},36991:(e,s,a)=>{"use strict";a.d(s,{B8:()=>P,UC:()=>T,bL:()=>I,l9:()=>M});var t=a(93491),r=a(18682),n=a(10158),l=a(92023),i=a(55462),c=a(90604),d=a(78283),o=a(76322),m=a(62962),u=a(91754),x="Tabs",[h,p]=(0,n.A)(x,[l.RG]),y=(0,l.RG)(),[f,g]=h(x),j=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,onValueChange:r,defaultValue:n,orientation:l="horizontal",dir:i,activationMode:h="automatic",...p}=e,y=(0,d.jH)(i),[g,j]=(0,o.i)({prop:t,onChange:r,defaultProp:n??"",caller:x});return(0,u.jsx)(f,{scope:a,baseId:(0,m.B)(),value:g,onValueChange:j,orientation:l,dir:y,activationMode:h,children:(0,u.jsx)(c.sG.div,{dir:y,"data-orientation":l,...p,ref:s})})});j.displayName=x;var b="TabsList",v=t.forwardRef((e,s)=>{let{__scopeTabs:a,loop:t=!0,...r}=e,n=g(b,a),i=y(a);return(0,u.jsx)(l.bL,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:t,children:(0,u.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:s})})});v.displayName=b;var C="TabsTrigger",N=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:t,disabled:n=!1,...i}=e,d=g(C,a),o=y(a),m=A(d.baseId,t),x=D(d.baseId,t),h=t===d.value;return(0,u.jsx)(l.q7,{asChild:!0,...o,focusable:!n,active:h,children:(0,u.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":x,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...i,ref:s,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(t)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(t)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||n||!e||d.onValueChange(t)})})})});N.displayName=C;var k="TabsContent",w=t.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,forceMount:n,children:l,...d}=e,o=g(k,a),m=A(o.baseId,r),x=D(o.baseId,r),h=r===o.value,p=t.useRef(h);return t.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(i.C,{present:n||h,children:({present:a})=>(0,u.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:x,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&l})})});function A(e,s){return`${e}-trigger-${s}`}function D(e,s){return`${e}-content-${s}`}w.displayName=k;var I=j,P=v,M=N,T=w},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40053:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(15349).A)("ShoppingCart01Icon",[["path",{d:"M8 16H15.2632C19.7508 16 20.4333 13.1808 21.261 9.06908C21.4998 7.88311 21.6192 7.29013 21.3321 6.89507C21.045 6.5 20.4947 6.5 19.3941 6.5H6",stroke:"currentColor",key:"k0"}],["path",{d:"M8 16L5.37873 3.51493C5.15615 2.62459 4.35618 2 3.43845 2H2.5",stroke:"currentColor",key:"k1"}],["path",{d:"M8.88 16H8.46857C7.10522 16 6 17.1513 6 18.5714C6 18.8081 6.1842 19 6.41143 19H17.5",stroke:"currentColor",key:"k2"}],["circle",{cx:"10.5",cy:"20.5",r:"1.5",stroke:"currentColor",key:"k3"}],["circle",{cx:"17.5",cy:"20.5",r:"1.5",stroke:"currentColor",key:"k4"}]])},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},45801:(e,s,a)=>{Promise.resolve().then(a.bind(a,26331))},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54090:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>i});var t=a(91754),r=a(93491),n=a(36991),l=a(82233);let i=n.bL,c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));c.displayName=n.B8.displayName;let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",e),...s}));d.displayName=n.l9.displayName;let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));o.displayName=n.UC.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55529:(e,s,a)=>{Promise.resolve().then(a.bind(a,87315))},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60618:(e,s,a)=>{"use strict";a.d(s,{A:()=>h});var t=a(91754);a(93491);var r=a(9260),n=a(56682),l=a(80601),i=a(66064),c=a(76130),d=a(40053);let o=(0,a(15349).A)("DoorLockIcon",[["path",{d:"M3 22H21",stroke:"currentColor",key:"k0"}],["path",{d:"M4 22V6C4 2.69067 4.78933 2 8.57143 2H15.4286C19.2107 2 20 2.69067 20 6V22",stroke:"currentColor",key:"k1"}],["path",{d:"M13.92 11.759V9.85411C13.92 8.83227 13.0604 8.00391 12 8.00391C10.9396 8.00391 10.08 8.83227 10.08 9.85411V11.759M15 14.0841C15 15.695 13.6462 17.0039 12 17.0039C10.3538 17.0039 9 15.695 9 14.0841C9 12.3739 10.3538 11.0738 12 11.0738C13.6462 11.0738 15 12.3739 15 14.0841Z",stroke:"currentColor",key:"k2"}]]);var m=a(79313),u=a(15854),x=a(70332);let h=({course:e,onEnroll:s,onPurchase:a,onClick:h,isEnrolled:p=!1})=>{let{canPurchase:y,canEnrollWithCode:f}=(0,x.h)(),g=(e,s="IDR")=>"IDR"===s?"Rp"+new Intl.NumberFormat("id-ID").format(e):new Intl.NumberFormat("id-ID",{style:"currency",currency:s}).format(e);return(0,t.jsx)(r.Zp,{className:"group cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] overflow-hidden p-0 h-full",onClick:h,"data-sentry-element":"Card","data-sentry-component":"CoursePreviewCard","data-sentry-source-file":"course-preview-card.tsx",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"aspect-[4/3] relative overflow-hidden bg-gray-100 flex-shrink-0",children:[e.thumbnail?(0,t.jsx)(u.default,{src:e.thumbnail,alt:e.name,fill:!0,className:"object-cover w-full h-full",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",priority:!1}):(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,t.jsxs)("div",{className:"relative z-10 text-center text-white",children:[(0,t.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-1 opacity-80"}),(0,t.jsx)("p",{className:"text-xs font-medium",children:e.code})]})]})}),e.isPurchasable&&(0,t.jsx)(l.E,{className:"absolute top-3 right-3 bg-[var(--iai-primary)] hover:bg-[var(--iai-primary)]",variant:"default",children:e.price?g(e.price,e.currency):"Gratis"})]}),(0,t.jsxs)("div",{className:"flex flex-col flex-grow p-4",children:[(0,t.jsxs)("div",{className:"space-y-1 mb-3 min-h-[60px]",children:[(0,t.jsx)("h3",{className:"text-lg font-bold group-hover:text-blue-600 transition-colors line-clamp-2",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 text-xs line-clamp-2",children:e.description})]}),(0,t.jsxs)("div",{className:"space-y-1 mb-4 text-xs text-gray-600 min-h-[32px]",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(m.A,{className:"mr-1 h-3 w-3 flex-shrink-0","data-sentry-element":"UsersIcon","data-sentry-source-file":"course-preview-card.tsx"}),(0,t.jsx)("span",{className:"line-clamp-1",children:e.instructor})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.A,{className:"mr-1 h-3 w-3 flex-shrink-0","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-preview-card.tsx"}),(0,t.jsxs)("span",{children:[e.modules.length," modul"]})]})]}),(0,t.jsx)("div",{className:"flex-grow"}),(0,t.jsx)("div",{className:"mt-auto",children:p?(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),h?.()},variant:"outline",className:"w-full bg-green-50 border-green-200 text-green-700 hover:bg-green-100",disabled:!0,children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Sudah Terdaftar"]}):e.previewMode?(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),h?.()},variant:"outline",className:"w-full",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Lihat Detail",(0,t.jsx)(c.A,{className:"ml-2 h-4 w-4"})]}):e.isPurchasable&&y?(0,t.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("span",{className:"text-2xl font-bold text-[var(--iai-primary)]",children:e.price?g(e.price,e.currency):"Gratis"})}),(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),a?.()},variant:"iai",className:"w-full",children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Beli Kursus"]}),f&&e.enrollmentCode&&(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),s?.()},variant:"outline",className:"w-full",children:[(0,t.jsx)(o,{className:"mr-2 h-4 w-4"}),"Gunakan Kode Pendaftaran"]})]}):f&&e.enrollmentCode?(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),s?.()},className:"w-full",children:[(0,t.jsx)(o,{className:"mr-2 h-4 w-4"}),"Daftar Sekarang"]}):(0,t.jsxs)(n.$,{onClick:e=>{e.stopPropagation(),h?.()},variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-component":"renderActionButton","data-sentry-source-file":"course-preview-card.tsx",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"course-preview-card.tsx"}),"Lihat Kursus"]})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66064:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(15349).A)("BookOpen01Icon",[["path",{d:"M12 6L12 20",stroke:"currentColor",key:"k0"}],["path",{d:"M5.98056 3.28544C9.32175 3.9216 11.3131 5.25231 12 6.01628C12.6869 5.25231 14.6782 3.9216 18.0194 3.28544C19.7121 2.96315 20.5584 2.80201 21.2792 3.41964C22 4.03727 22 5.04022 22 7.04612V14.255C22 16.0891 22 17.0061 21.5374 17.5787C21.0748 18.1512 20.0564 18.3451 18.0194 18.733C16.2037 19.0787 14.7866 19.6295 13.7608 20.1831C12.7516 20.7277 12.247 21 12 21C11.753 21 11.2484 20.7277 10.2392 20.1831C9.21344 19.6295 7.79633 19.0787 5.98056 18.733C3.94365 18.3451 2.9252 18.1512 2.4626 17.5787C2 17.0061 2 16.0891 2 14.255V7.04612C2 5.04022 2 4.03727 2.72078 3.41964C3.44157 2.80201 4.2879 2.96315 5.98056 3.28544Z",stroke:"currentColor",key:"k1"}]])},66207:(e,s,a)=>{"use strict";a.d(s,{b:()=>i});var t=a(93491),r=a(90604),n=a(91754),l=t.forwardRef((e,s)=>(0,n.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));l.displayName="Label";var i=l},68577:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.default,__next_app__:()=>o,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=a(95500),r=a(56947),n=a(26052),l=a(13636),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);a.d(s,i);let c={children:["",{children:["(students-page)",{children:["courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,87315)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,97893)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e),async e=>(await Promise.resolve().then(a.bind(a,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e),async e=>(await Promise.resolve().then(a.bind(a,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(students-page)/courses/page",pathname:"/courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},70332:(e,s,a)=>{"use strict";a.d(s,{h:()=>n});let t={enableCoursePurchase:!0,enableEnrollmentCode:!0,enableCoursePreview:!0,enablePaymentIntegration:!1};function r(e,s){}function n(){return{flags:t,setFlag:r,canPurchase:t.enableCoursePurchase,canEnrollWithCode:t.enableEnrollmentCode,canPreviewCourse:t.enableCoursePreview,hasPaymentIntegration:t.enablePaymentIntegration}}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76130:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(15349).A)("ArrowRight01Icon",[["path",{d:"M9.00005 6C9.00005 6 15 10.4189 15 12C15 13.5812 9 18 9 18",stroke:"currentColor",key:"k0"}]])},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87315:(e,s,a)=>{"use strict";let t;a.r(s),a.d(s,{default:()=>x,generateImageMetadata:()=>m,generateMetadata:()=>o,generateViewport:()=>u});var r=a(63033),n=a(1472),l=a(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\courses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(students-page)\\courses\\page.tsx","default");let c={...r},d="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;t="function"==typeof i?new Proxy(i,{apply:(e,s,a)=>{let t,r,n;try{let e=d?.getStore();t=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return l.wrapServerComponentWithSentry(e,{componentRoute:"/(students-page)/courses",componentType:"Page",sentryTraceHeader:t,baggageHeader:r,headers:n}).apply(s,a)}}):i;let o=void 0,m=void 0,u=void 0,x=t},93438:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>m,Es:()=>x,L3:()=>h,c7:()=>u,lG:()=>i,rr:()=>p,zM:()=>c});var t=a(91754);a(93491);var r=a(18227),n=a(31619),l=a(82233);function i({...e}){return(0,t.jsx)(r.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function c({...e}){return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function o({className:e,...s}){return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...s,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m({className:e,children:s,...a}){return(0,t.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,t.jsx)(o,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...a,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[s,(0,t.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,t.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...s,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...s,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function h({className:e,...s}){return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...s,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...s}){return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...s,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634,2095,7852],()=>a(68577));module.exports=t})();
//# sourceMappingURL=page.js.map