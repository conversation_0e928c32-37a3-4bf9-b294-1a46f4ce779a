try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="8fe677ed-9ae6-4ed5-91eb-6c787a559291",e._sentryDebugIdIdentifier="sentry-dbid-8fe677ed-9ae6-4ed5-91eb-6c787a559291")}catch(e){}"use strict";(()=>{var e={};e.id=4693,e.ids=[4693],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4637:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>T,routeModule:()=>R,serverHooks:()=>P,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>v,GET:()=>g,HEAD:()=>N,OPTIONS:()=>j,PATCH:()=>b,POST:()=>y,PUT:()=>I});var o=t(3690),n=t(56947),i=t(75250),d=t(63033),u=t(62187),a=t(18621),l=t(32230),p=t(74683),c=t(7688);async function h(e,{params:r}){try{let{id:e}=await r,t=parseInt(e);if(isNaN(t))return u.NextResponse.json({error:"Invalid module ID"},{status:400});let s=await a.db.select().from(l.modules).where((0,p.eq)(l.modules.id,t)).limit(1);if(0===s.length)return u.NextResponse.json({error:"Module not found"},{status:404});let o=s[0],n=await a.db.select().from(l.chapters).where((0,p.eq)(l.chapters.moduleId,t)),i=await Promise.all(n.map(async e=>{let r=await a.db.select().from(l.quizzes).where((0,p.eq)(l.quizzes.chapterId,e.id));return{...e,quizzes:r}}));return u.NextResponse.json({module:{...o,chapters:i}})}catch(e){return console.error("Error fetching module:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let{id:t}=await r,s=parseInt(t);if(isNaN(s))return u.NextResponse.json({error:"Invalid module ID"},{status:400});let{name:o,description:n,orderIndex:i,teacherId:d}=await e.json(),c=await a.db.select().from(l.modules).where((0,p.eq)(l.modules.id,s)).limit(1);if(0===c.length)return u.NextResponse.json({error:"Module not found"},{status:404});if(d){let e=await a.db.select().from(l.courses).where((0,p.Uo)((0,p.eq)(l.courses.id,c[0].courseId),(0,p.eq)(l.courses.teacherId,d))).limit(1);if(0===e.length)return u.NextResponse.json({error:"Not authorized to update this module"},{status:403})}let h=await a.db.update(l.modules).set({...o&&{name:o},...n&&{description:n},...void 0!==i&&{orderIndex:i},updatedAt:new Date}).where((0,p.eq)(l.modules.id,s)).returning();return u.NextResponse.json({module:h[0],message:"Module updated successfully"})}catch(e){return console.error("Error updating module:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let{id:t}=await r,s=parseInt(t);if(isNaN(s))return u.NextResponse.json({error:"Invalid module ID"},{status:400});let o=e.nextUrl.searchParams.get("teacherId"),n=await a.db.select().from(l.modules).where((0,p.eq)(l.modules.id,s)).limit(1);if(0===n.length)return u.NextResponse.json({error:"Module not found"},{status:404});if(o){let e=await a.db.select().from(l.courses).where((0,p.Uo)((0,p.eq)(l.courses.id,n[0].courseId),(0,p.eq)(l.courses.teacherId,parseInt(o)))).limit(1);if(0===e.length)return u.NextResponse.json({error:"Not authorized to delete this module"},{status:403})}for(let e of(await a.db.select({id:l.chapters.id}).from(l.chapters).where((0,p.eq)(l.chapters.moduleId,s))))await a.db.delete(l.quizzes).where((0,p.eq)(l.quizzes.chapterId,e.id));return await a.db.delete(l.chapters).where((0,p.eq)(l.chapters.moduleId,s)),await a.db.delete(l.modules).where((0,p.eq)(l.modules.id,s)),u.NextResponse.json({message:"Module deleted successfully"})}catch(e){return console.error("Error deleting module:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...d},f="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function w(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=f?.getStore();o=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/modules/[id]",headers:o}).apply(t,s)}})}let g=w(h,"GET"),y=w(void 0,"POST"),I=w(x,"PUT"),b=w(void 0,"PATCH"),v=w(m,"DELETE"),N=w(void 0,"HEAD"),j=w(void 0,"OPTIONS"),R=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/modules/[id]/route",pathname:"/api/modules/[id]",filename:"route",bundlePath:"app/api/modules/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\modules\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:A,serverHooks:P}=R;function T(){return(0,i.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:A})}},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(4637));module.exports=s})();
//# sourceMappingURL=route.js.map