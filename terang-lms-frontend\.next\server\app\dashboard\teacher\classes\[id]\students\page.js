try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f73e4e49-6167-47a5-afad-e20a3bed6bf3",e._sentryDebugIdIdentifier="sentry-dbid-f73e4e49-6167-47a5-afad-e20a3bed6bf3")}catch(e){}(()=>{var e={};e.id=8,e.ids=[8],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4978:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8086:e=>{"use strict";e.exports=require("module")},8969:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var a=s(95500),r=s(56947),n=s(26052),o=s(13636),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let i={children:["",{children:["dashboard",{children:["teacher",{children:["classes",{children:["[id]",{children:["students",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49692)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\students\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,64755)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\students\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/teacher/classes/[id]/students/page",pathname:"/dashboard/teacher/classes/[id]/students",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},9260:(e,t,s)=>{"use strict";s.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>c});var a=s(91754);s(93491);var r=s(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,s)=>{Promise.resolve().then(s.bind(s,7346)),Promise.resolve().then(s.bind(s,21444)),Promise.resolve().then(s.bind(s,3033)),Promise.resolve().then(s.bind(s,84436))},14621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(91754);function r({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}s(93491),s(76328)},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26466:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(91754),r=s(93491),n=s(21372),o=s(9260),l=s(56682),i=s(59672),d=s(40636),c=s(93438),u=s(69122),m=s(88373),p=s(4978),x=s(5574),f=s(26711),y=s(93626),g=s(84795),h=s(16041),v=s.n(h),b=s(76328),j=s(81012);function w(){(0,n.useRouter)();let e=(0,n.useParams)().id,[t,s]=(0,r.useState)(""),[h,w]=(0,r.useState)([]),[S,A]=(0,r.useState)([]),[N,C]=(0,r.useState)(null),[P,T]=(0,r.useState)(!0),[D,q]=(0,r.useState)(!1),[_,k]=(0,r.useState)(null),[R,E]=(0,r.useState)(""),[I,L]=(0,r.useState)(!1),G=async()=>{try{if(!b.qs.getUser())return void j.oR.error("Please log in to view students");let t=await fetch(`/api/class-enrollments?classId=${e}`),s=await t.json();if(s.success){let e=s.data.map(e=>({id:e.studentId,name:e.studentName,email:e.studentEmail,enrolledAt:e.enrolledAt}));w(e||[])}else j.oR.error(s.error||"Failed to fetch students")}catch(e){console.error("Error fetching students:",e),j.oR.error("Failed to fetch students")}finally{T(!1)}},U=async()=>{try{let t=b.qs.getUser();if(!t)return;let s=`/api/users?role=student&institutionId=${t.institutionId}&excludeClassId=${e}`;console.log("\uD83D\uDD0D Fetching available students from:",s),console.log("\uD83D\uDC64 Current user:",{id:t.id,institutionId:t.institutionId});let a=await fetch(s),r=await a.json();if(console.log("\uD83D\uDCE5 API Response:",r),console.log("✅ Response success:",r.success),console.log("\uD83D\uDC65 Users in response:",r.users),r.success){let e=r.data?.users||[];console.log("\uD83C\uDFAF Setting availableStudents to:",e),A(e),console.log("\uD83D\uDCCA Available students count:",e.length)}else console.error("❌ API returned error:",r.error)}catch(e){console.error("\uD83D\uDCA5 Error fetching available students:",e)}},z=async()=>{if(!R)return void j.oR.error("Please select a student to add");q(!0);try{if(!b.qs.getUser())return void j.oR.error("Please log in to add students");let t=await fetch("/api/class-enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:parseInt(R),classId:parseInt(e),status:"active"})}),s=await t.json();s.success?(j.oR.success("Student added to class successfully!"),L(!1),E(""),G(),U()):j.oR.error(s.error||"Failed to add student to class")}catch(e){console.error("Error adding student:",e),j.oR.error("Failed to add student to class")}finally{q(!1)}},M=async t=>{if(confirm("Are you sure you want to remove this student from the class?")){k(t);try{if(!b.qs.getUser())return void j.oR.error("Please log in to remove students");let s=await fetch(`/api/class-enrollments?studentId=${t}&classId=${e}`),a=await s.json();if(a.success&&a.data.length>0){let e=a.data[0].id,t=await fetch(`/api/class-enrollments/${e}`,{method:"DELETE"}),s=await t.json();s.success?(j.oR.success("Student removed from class successfully!"),G(),U()):j.oR.error(s.error||"Failed to remove student from class")}else j.oR.error("Enrollment not found")}catch(e){console.error("Error removing student:",e),j.oR.error("Failed to remove student from class")}finally{k(null)}}},F=h.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase()));return P?(0,a.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading students..."})]}):(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"ClassStudentsPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(v(),{href:`/dashboard/teacher/classes/${e}`,"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Manage Students"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:N?`Students in ${N.name}`:"Loading class..."})]})]}),(0,a.jsxs)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Class Students"}),(0,a.jsx)(o.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Manage students enrolled in this class"})]}),(0,a.jsxs)(c.lG,{open:I,onOpenChange:L,"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(c.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4","data-sentry-element":"UserPlus","data-sentry-source-file":"page.tsx"}),"Add Student"]})}),(0,a.jsxs)(c.Cf,{"data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(c.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(c.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:"Add Student to Class"}),(0,a.jsx)(c.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"page.tsx",children:"Select a student from your institution to add to this class."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(u.l6,{value:R,onValueChange:E,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(u.yv,{placeholder:"Select a student","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:(console.log("\uD83C\uDFA8 Rendering dropdown with availableStudents:",S),console.log("\uD83D\uDCDD Available students length:",S.length),S.map(e=>(console.log("\uD83D\uDC64 Rendering student:",e),(0,a.jsxs)(u.eb,{value:e.id.toString(),children:[e.name," (",e.email,")"]},e.id))))})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>{L(!1),E("")},"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"}),(0,a.jsxs)(l.$,{onClick:z,disabled:D,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[D?(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),D?"Adding...":"Add Student"]})]})]})]})]})]})}),(0,a.jsxs)(o.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"mb-4 flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(f.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(i.p,{placeholder:"Search students...",value:t,onChange:e=>s(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(d.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(d.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(d.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(d.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Student Name"}),(0,a.jsx)(d.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Email"}),(0,a.jsx)(d.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Enrolled Date"}),(0,a.jsx)(d.TableHead,{className:"w-[70px]","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Actions"})]})}),(0,a.jsx)(d.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:F.map(e=>(0,a.jsxs)(d.TableRow,{children:[(0,a.jsx)(d.TableCell,{children:(0,a.jsx)("div",{className:"font-medium",children:e.name})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsx)("div",{className:"text-muted-foreground",children:e.email})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsx)("span",{className:"text-sm",children:new Date(e.enrolledAt).toLocaleDateString()})}),(0,a.jsx)(d.TableCell,{children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>M(e.id),disabled:_===e.id,children:_===e.id?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(y.A,{className:"h-4 w-4"})})})]},e.id))})]})}),0===F.length&&(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(g.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No students found"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:t?"Try adjusting your search terms.":"No students are enrolled in this class yet."}),!t&&(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)(l.$,{onClick:()=>L(!0),children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Add First Student"]})})]})]})]})]})}},26711:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(55732).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40636:(e,t,s)=>{"use strict";s.d(t,{Table:()=>n,TableBody:()=>l,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>o,TableRow:()=>i});var a=s(91754);s(93491);var r=s(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...t,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function l({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...t,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},49692:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var r=s(63033),n=s(1472),o=s(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\[id]\\\\students\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\classes\\[id]\\students\\page.tsx","default");let i={...r},d="workUnitAsyncStorage"in i?i.workUnitAsyncStorage:"requestAsyncStorage"in i?i.requestAsyncStorage:void 0;a="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let a,r,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher/classes/[id]/students",componentType:"Page",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}}):l;let c=void 0,u=void 0,m=void 0,p=a},52377:(e,t,s)=>{Promise.resolve().then(s.bind(s,14621))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>v,generateImageMetadata:()=>g,generateMetadata:()=>y,generateViewport:()=>h,metadata:()=>m});var r=s(63033),n=s(18188),o=s(5434),l=s(45188),i=s(67999),d=s(4590),c=s(23064),u=s(7688);let m={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function p({children:e}){let t=await (0,c.UL)(),s=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(o.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(d.SidebarProvider,{defaultOpen:s,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(l.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(i.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...r},f="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;a=new Proxy(p,{apply:(e,t,s)=>{let a,r,n;try{let e=f?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}});let y=void 0,g=void 0,h=void 0,v=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64755:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var r=s(63033),n=s(1472),o=s(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx","default");let i={...r},d="workUnitAsyncStorage"in i?i.workUnitAsyncStorage:"requestAsyncStorage"in i?i.requestAsyncStorage:void 0;a="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let a,r,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}}):l;let c=void 0,u=void 0,m=void 0,p=a},67801:(e,t,s)=>{Promise.resolve().then(s.bind(s,49692))},69122:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var a=s(91754);s(93491);var r=s(97543),n=s(33093),o=s(87435),l=s(20388),i=s(82233);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function u({className:e,size:t="default",children:s,...o}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[s,(0,a.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)(x,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,a.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:t}),(0,a.jsx)(f,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function p({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(o.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,a.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function f({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(n.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,s)=>{Promise.resolve().then(s.bind(s,5434)),Promise.resolve().then(s.bind(s,45188)),Promise.resolve().then(s.bind(s,67999)),Promise.resolve().then(s.bind(s,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80953:(e,t,s)=>{Promise.resolve().then(s.bind(s,26466))},81753:(e,t,s)=>{Promise.resolve().then(s.bind(s,64755))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},93438:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>p,L3:()=>x,c7:()=>m,lG:()=>l,rr:()=>f,zM:()=>i});var a=s(91754);s(93491);var r=s(18227),n=s(31619),o=s(82233);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function i({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u({className:e,children:t,...s}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function f({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8134,8634],()=>s(8969));module.exports=a})();
//# sourceMappingURL=page.js.map