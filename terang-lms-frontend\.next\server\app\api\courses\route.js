try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1a62ba54-093a-43dc-a033-7fe086bb4a8f",e._sentryDebugIdIdentifier="sentry-dbid-1a62ba54-093a-43dc-a033-7fe086bb4a8f")}catch(e){}"use strict";(()=>{var e={};e.id=8648,e.ids=[8648],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},82529:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>T,routeModule:()=>A,serverHooks:()=>D,workAsyncStorage:()=>P,workUnitAsyncStorage:()=>C});var s={};r.r(s),r.d(s,{DELETE:()=>v,GET:()=>w,HEAD:()=>b,OPTIONS:()=>S,PATCH:()=>I,POST:()=>x,PUT:()=>g});var i=r(3690),o=r(56947),n=r(75250),a=r(63033),u=r(62187),c=r(18621),d=r(32230),l=r(74683),p=r(7688);async function m(e){try{let t,r=e.nextUrl.searchParams,s=r.get("teacherId"),i=r.get("institutionId"),o=r.get("public");if("true"===o){let e=await c.db.select({id:d.courses.id,name:d.courses.name,description:d.courses.description,instructor:d.courses.instructor,type:d.courses.type,enrollmentType:d.courses.enrollmentType,startDate:d.courses.startDate,endDate:d.courses.endDate,teacherId:d.courses.teacherId,institutionId:d.courses.institutionId,courseCode:d.courses.courseCode,coverPicture:d.courses.coverPicture,isPurchasable:d.courses.isPurchasable,price:d.courses.price,currency:d.courses.currency,previewMode:d.courses.previewMode,createdAt:d.courses.createdAt,updatedAt:d.courses.updatedAt,teacherName:d.users.name,teacherEmail:d.users.email}).from(d.courses).leftJoin(d.users,(0,l.eq)(d.courses.teacherId,d.users.id)),t=await Promise.all(e.map(async e=>{let t=await c.db.select().from(d.modules).where((0,l.eq)(d.modules.courseId,e.id)),r=await Promise.all(t.map(async e=>{let t=await c.db.select().from(d.chapters).where((0,l.eq)(d.chapters.moduleId,e.id)),r=await Promise.all(t.map(async e=>{let t=await c.db.select().from(d.quizzes).where((0,l.eq)(d.quizzes.chapterId,e.id)),r=[];t.length>0&&(r=await c.db.select().from(d.questions).where((0,l.eq)(d.questions.quizId,t[0].id)));let s=Array.isArray(e.content)?e.content.map((t,r)=>({id:`content-${e.id}-${r}`,title:t.title||`Content ${r+1}`,type:t.type||"text",content:t.content||t,isCompleted:!1,createdAt:new Date().toISOString()})):[];return{id:e.id,title:e.name,order:e.orderIndex,isUnlocked:!0,completionPercentage:0,contents:s,quiz:t.length>0?{id:t[0].id.toString(),title:t[0].name,type:"chapter",questions:r.map(e=>{let t;if("true_false"===e.type){let r=e.options?.find(e=>e.isCorrect);if(r){let e=r.content?.find(e=>"text"===e.type);t=e?.value?.toLowerCase()==="true"?"true":"false"}}else if("multiple_choice"===e.type){let r=e.options?.findIndex(e=>e.isCorrect);t=-1!==r?r:void 0}else"essay"===e.type&&(t=e.essayAnswer);return{id:e.id.toString(),question:e.question,type:e.type,options:e.options||[],correctAnswer:t,explanation:e.explanation}}),minimumScore:parseFloat(t[0].minimumScore||"70"),timeLimit:t[0].timeLimit,attempts:0,maxAttempts:3,isPassed:!1}:{id:`quiz-chapter-${e.id}`,title:`Quiz ${e.name}`,type:"chapter",questions:[],minimumScore:70,attempts:0,maxAttempts:3,isPassed:!1}}})),s=await c.db.select().from(d.quizzes).where((0,l.eq)(d.quizzes.moduleId,e.id)),i=[];return s.length>0&&(i=await c.db.select().from(d.questions).where((0,l.eq)(d.questions.quizId,s[0].id))),{id:e.id,title:e.name,description:e.description||"",order:e.orderIndex,isUnlocked:!0,completionPercentage:0,chapters:r,moduleQuiz:s.length>0?{id:s[0].id.toString(),title:s[0].name,type:"module",questions:i.map(e=>{let t;if("true_false"===e.type){let r=e.options?.find(e=>e.isCorrect);if(r){let e=r.content?.find(e=>"text"===e.type);t=e?.value?.toLowerCase()==="true"?"true":"false"}}else if("multiple_choice"===e.type){let r=e.options?.findIndex(e=>e.isCorrect);t=-1!==r?r:void 0}else"essay"===e.type&&(t=e.essayAnswer);return{id:e.id.toString(),question:e.question,type:e.type,options:e.options||[],correctAnswer:t,explanation:e.explanation}}),minimumScore:parseFloat(s[0].minimumScore||"70"),timeLimit:s[0].timeLimit,attempts:0,maxAttempts:3,isPassed:!1}:{id:`quiz-module-${e.id}`,title:`Quiz ${e.name}`,type:"module",questions:[],minimumScore:70,attempts:0,maxAttempts:3,isPassed:!1}}})),s=await c.db.select().from(d.courseAdmissions).where((0,l.eq)(d.courseAdmissions.courseId,e.id)).limit(1),i=await c.db.select().from(d.courseAcademics).where((0,l.eq)(d.courseAcademics.courseId,e.id)).limit(1),o=await c.db.select().from(d.courseTuitionAndFinancing).where((0,l.eq)(d.courseTuitionAndFinancing.courseId,e.id)).limit(1),n=await c.db.select().from(d.courseCareers).where((0,l.eq)(d.courseCareers.courseId,e.id)).limit(1),a=await c.db.select().from(d.courseStudentExperience).where((0,l.eq)(d.courseStudentExperience.courseId,e.id)).limit(1),u=await c.db.select().from(d.quizzes).where((0,l.Uo)((0,l.eq)(d.quizzes.courseId,e.id),(0,l.eq)(d.quizzes.quizType,"final"))).limit(1),p=[];return u.length>0&&(p=await c.db.select().from(d.questions).where((0,l.eq)(d.questions.quizId,u[0].id))),{id:e.id.toString(),name:e.name,code:e.courseCode||"",description:e.description||"",instructor:e.instructor||e.teacherName||"Unknown Instructor",startDate:e.startDate?.toISOString()||new Date().toISOString(),endDate:e.endDate?.toISOString()||new Date().toISOString(),enrollmentType:e.enrollmentType||"code",enrollmentCode:e.courseCode,isPurchasable:void 0===e.isPurchasable||e.isPurchasable,price:e.price?parseFloat(e.price):0,currency:e.currency||"IDR",previewMode:e.previewMode||!1,thumbnail:e.coverPicture||void 0,admissions:s[0]?{requirements:s[0].requirements||[],applicationDeadline:s[0].applicationDeadline||void 0,prerequisites:s[0].prerequisites||[]}:void 0,academics:i[0]?{credits:i[0].credits||0,workload:i[0].workload||"",assessment:i[0].assessment||[]}:void 0,tuitionAndFinancing:o[0]?{totalCost:o[0].totalCost?parseFloat(o[0].totalCost):0,paymentOptions:o[0].paymentOptions||[],scholarships:o[0].scholarships||[]}:void 0,careers:n[0]?{outcomes:n[0].outcomes||[],industries:n[0].industries||[],averageSalary:n[0].averageSalary||void 0}:void 0,studentExperience:a[0]?{testimonials:a[0].testimonials||[],facilities:a[0].facilities||[],support:a[0].support||[]}:void 0,modules:r,finalExam:u.length>0?{id:u[0].id.toString(),title:u[0].name,type:"final",questions:p.map(e=>{let t;if("true_false"===e.type){let r=e.options?.find(e=>e.isCorrect);if(r){let e=r.content?.find(e=>"text"===e.type);t=e?.value?.toLowerCase()==="true"?"true":"false"}}else if("multiple_choice"===e.type){let r=e.options?.findIndex(e=>e.isCorrect);t=-1!==r?r:void 0}else"essay"===e.type&&(t=e.essayAnswer);return{id:e.id.toString(),question:e.question,type:e.type,options:e.options||[],correctAnswer:t,explanation:e.explanation}}),minimumScore:parseFloat(u[0].minimumScore||"70"),timeLimit:u[0].timeLimit,attempts:0,maxAttempts:3,isPassed:!1}:{id:"final-exam",title:"Final Exam",type:"final",questions:[],minimumScore:70,attempts:0,maxAttempts:3,isPassed:!1},certificate:{isEligible:!0,isGenerated:!1},minPassingScore:70,totalProgress:0,status:"not-started",moduleCount:t.length,studentCount:0}}));return u.NextResponse.json({success:!0,courses:t})}if(!s)return u.NextResponse.json({error:"Teacher ID required"},{status:400});t=i?(0,l.eq)(d.courses.institutionId,parseInt(i)):(0,l.eq)(d.courses.teacherId,parseInt(s));let n=await c.db.select({id:d.courses.id,name:d.courses.name,description:d.courses.description,instructor:d.courses.instructor,type:d.courses.type,enrollmentType:d.courses.enrollmentType,startDate:d.courses.startDate,endDate:d.courses.endDate,teacherId:d.courses.teacherId,institutionId:d.courses.institutionId,courseCode:d.courses.courseCode,coverPicture:d.courses.coverPicture,isPurchasable:d.courses.isPurchasable,price:d.courses.price,currency:d.courses.currency,previewMode:d.courses.previewMode,createdAt:d.courses.createdAt,updatedAt:d.courses.updatedAt,teacherName:d.users.name,teacherEmail:d.users.email}).from(d.courses).leftJoin(d.users,(0,l.eq)(d.courses.teacherId,d.users.id)).where(t),a=await Promise.all(n.map(async e=>{let t=await c.db.select({count:d.modules.id}).from(d.modules).where((0,l.eq)(d.modules.courseId,e.id));return{...e,moduleCount:t.length,studentCount:0,status:"published"}}));return u.NextResponse.json({success:!0,courses:a})}catch(e){return console.error("Error fetching courses:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let{name:t,description:r,instructor:s,type:i="self_paced",enrollmentType:o="code",startDate:n,endDate:a,teacherId:p,institutionId:m,courseCode:h,coverPicture:q,isPurchasable:f,price:y,currency:w,previewMode:x,admissions:g,academics:I,tuitionAndFinancing:v,careers:b,studentExperience:S}=await e.json();if(!t||!p||!m)return u.NextResponse.json({error:"Name, teacher ID, and institution ID are required"},{status:400});let A=await c.db.select().from(d.users).where((0,l.Uo)((0,l.eq)(d.users.id,p),(0,l.eq)(d.users.institutionId,m),(0,l.eq)(d.users.role,"teacher"))).limit(1);if(0===A.length)return u.NextResponse.json({error:"Teacher not found or not authorized"},{status:403});let P=h;if(P){if((await c.db.select().from(d.courses).where((0,l.eq)(d.courses.courseCode,P)).limit(1)).length>0)return u.NextResponse.json({error:"Course code already exists"},{status:400})}else{let e=!1;for(;!e;){P=function(){let e=Math.floor(1e4*Math.random()).toString().padStart(4,"0");return`COURSE${e}`}();let t=await c.db.select().from(d.courses).where((0,l.eq)(d.courses.courseCode,P)).limit(1);e=0===t.length}}let C=(await c.db.insert(d.courses).values({name:t,description:r,instructor:s,type:i,enrollmentType:o,startDate:n?new Date(n):null,endDate:a?new Date(a):null,teacherId:p,institutionId:m,courseCode:P,coverPicture:q,isPurchasable:f,price:y,currency:w,previewMode:x}).returning())[0];return g&&await c.db.insert(d.courseAdmissions).values({courseId:C.id,requirements:g.requirements,applicationDeadline:g.applicationDeadline,prerequisites:g.prerequisites}),I&&await c.db.insert(d.courseAcademics).values({courseId:C.id,credits:I.credits,workload:I.workload,assessment:I.assessment}),v&&await c.db.insert(d.courseTuitionAndFinancing).values({courseId:C.id,totalCost:v.totalCost,paymentOptions:v.paymentOptions,scholarships:v.scholarships}),b&&await c.db.insert(d.courseCareers).values({courseId:C.id,outcomes:b.outcomes,industries:b.industries,averageSalary:b.averageSalary}),S&&await c.db.insert(d.courseStudentExperience).values({courseId:C.id,testimonials:S.testimonials,facilities:S.facilities,support:S.support}),u.NextResponse.json({success:!0,course:C,message:"Course created successfully"},{status:201})}catch(e){return console.error("Error creating course:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...a},f="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function y(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,r,s)=>{let i;try{let e=f?.getStore();i=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/courses",headers:i}).apply(r,s)}})}let w=y(m,"GET"),x=y(h,"POST"),g=y(void 0,"PUT"),I=y(void 0,"PATCH"),v=y(void 0,"DELETE"),b=y(void 0,"HEAD"),S=y(void 0,"OPTIONS"),A=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/courses/route",pathname:"/api/courses",filename:"route",bundlePath:"app/api/courses/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\courses\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:P,workUnitAsyncStorage:C,serverHooks:D}=A;function T(){return(0,n.patchFetch)({workAsyncStorage:P,workUnitAsyncStorage:C})}},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5250,7688,8036,138,1617,2957],()=>r(82529));module.exports=s})();
//# sourceMappingURL=route.js.map