{"version": 3, "file": "../app/dashboard/teacher/page.js", "mappings": "qbAAA,6GCAA,mDCAA,yJCIe,SAASA,EAAc,UACpCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,2CCdA,kECAA,2GCAA,qDCAA,+CCAA,mDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,sPCUe,SAASC,IACtB,GAAM,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACC,EAAAA,EAAWA,CAACC,OAAO,IAC9C,CAACC,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAQ3C,GAAIG,EACF,MAAO,GADM,EACN,KAACE,MAAAA,CAAIC,UAAU,yDAAgD,eAIxE,GAAI,CAACR,GAAMS,cACT,CADwB,KACjB,UAACC,EAAAA,CAAwBA,CAAAA,CAACC,SAAS,YAI5C,IAAMC,EAAQ,CACZC,aAAc,EACdC,aAAc,GACdC,cAAe,IACfC,eAAgB,EAClB,EAuBA,MAAO,WAACT,MAAAA,CAAIC,UAAU,YAAYS,wBAAsB,mBAAmBC,0BAAwB,qBAC/F,WAACX,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,UAACY,KAAAA,CAAGX,UAAU,6CAAoC,sBAGlD,UAACY,IAAAA,CAAEZ,UAAU,iCAAwB,gEAIvC,WAACD,MAAAA,CAAIC,UAAU,2BACb,UAACa,IAAIA,CAACC,KAAK,sCAAsCC,CAA5CF,qBAAgE,OAAOH,0BAAwB,oBAClG,WAACM,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUF,sBAAoB,SAASL,0BAAwB,qBAC7E,UAACQ,EAAAA,CAAGA,CAAAA,CAAClB,UAAU,eAAee,sBAAoB,MAAML,0BAAwB,aAAa,oBAIjG,UAACG,IAAIA,CAACC,KAAK,iCAAiCC,MAAvCF,gBAA2D,OAAOH,0BAAwB,oBAC7F,WAACM,EAAAA,CAAMA,CAAAA,CAACD,sBAAoB,SAASL,0BAAwB,qBAC3D,UAACS,EAAAA,CAAIA,CAAAA,CAACnB,UAAU,eAAee,sBAAoB,OAAOL,0BAAwB,aAAa,2BAQvG,WAACX,MAAAA,CAAIC,UAAU,qDACb,WAACoB,EAAAA,EAAIA,CAAAA,CAACL,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACW,EAAAA,EAAUA,CAAAA,CAACrB,UAAU,4DAA4De,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACY,EAAAA,EAASA,CAAAA,CAACtB,UAAU,sBAAsBe,sBAAoB,YAAYL,0BAAwB,oBAAW,kBAC9G,UAACa,EAAAA,CAAKA,CAAAA,CAACvB,UAAU,gCAAgCe,sBAAoB,QAAQL,0BAAwB,gBAEvG,WAACc,EAAAA,EAAWA,CAAAA,CAACT,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACX,MAAAA,CAAIC,UAAU,8BAAsBI,EAAMC,YAAY,GACvD,UAACO,IAAAA,CAAEZ,UAAU,yCAAgC,yBAIjD,WAACoB,EAAAA,EAAIA,CAAAA,CAACL,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACW,EAAAA,EAAUA,CAAAA,CAACrB,UAAU,4DAA4De,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACY,EAAAA,EAASA,CAAAA,CAACtB,UAAU,sBAAsBe,sBAAoB,YAAYL,0BAAwB,oBAAW,kBAC9G,UAACe,EAAAA,CAAQA,CAAAA,CAACzB,UAAU,gCAAgCe,sBAAoB,WAAWL,0BAAwB,gBAE7G,WAACc,EAAAA,EAAWA,CAAAA,CAACT,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACX,MAAAA,CAAIC,UAAU,8BAAsBI,EAAME,YAAY,GACvD,UAACM,IAAAA,CAAEZ,UAAU,yCAAgC,4BAIjD,WAACoB,EAAAA,EAAIA,CAAAA,CAACL,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACW,EAAAA,EAAUA,CAAAA,CAACrB,UAAU,4DAA4De,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACY,EAAAA,EAASA,CAAAA,CAACtB,UAAU,sBAAsBe,sBAAoB,YAAYL,0BAAwB,oBAAW,mBAG9G,UAACa,EAAAA,CAAKA,CAAAA,CAACvB,UAAU,gCAAgCe,sBAAoB,QAAQL,0BAAwB,gBAEvG,WAACc,EAAAA,EAAWA,CAAAA,CAACT,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACX,MAAAA,CAAIC,UAAU,8BAAsBI,EAAMG,aAAa,GACxD,UAACK,IAAAA,CAAEZ,UAAU,yCAAgC,4BAIjD,WAACoB,EAAAA,EAAIA,CAAAA,CAACL,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACW,EAAAA,EAAUA,CAAAA,CAACrB,UAAU,4DAA4De,sBAAoB,aAAaL,0BAAwB,qBACzI,UAACY,EAAAA,EAASA,CAAAA,CAACtB,UAAU,sBAAsBe,sBAAoB,YAAYL,0BAAwB,oBAAW,oBAG9G,UAACgB,EAAAA,CAAUA,CAAAA,CAAC1B,UAAU,gCAAgCe,sBAAoB,aAAaL,0BAAwB,gBAEjH,WAACc,EAAAA,EAAWA,CAAAA,CAACT,sBAAoB,cAAcL,0BAAwB,qBACrE,WAACX,MAAAA,CAAIC,UAAU,+BAAsBI,EAAMI,cAAc,CAAC,OAC1D,UAACI,IAAAA,CAAEZ,UAAU,yCAAgC,gCAMnD,WAACoB,EAAAA,EAAIA,CAAAA,CAACL,sBAAoB,OAAOL,0BAAwB,qBACvD,WAACW,EAAAA,EAAUA,CAAAA,CAACN,sBAAoB,aAAaL,0BAAwB,qBACnE,UAACY,EAAAA,EAASA,CAAAA,CAACP,sBAAoB,YAAYL,0BAAwB,oBAAW,mBAC9E,UAACiB,EAAAA,EAAeA,CAAAA,CAACZ,sBAAoB,kBAAkBL,0BAAwB,oBAAW,qCAE5F,WAACc,EAAAA,EAAWA,CAAAA,CAACT,sBAAoB,cAAcL,0BAAwB,qBACrE,UAACX,MAAAA,CAAIC,UAAU,qBACZ4B,CA3GY,CACrBC,GAAI,EACJC,KAAM,8BACNC,KAAM,aACNC,SAAU,GACVC,WAAY,GACZC,OAAQ,QACV,EAAG,CACDL,GAAI,EACJC,KAAM,gBACNC,KAAM,WACNC,SAAU,GACVC,WAAY,GACZC,OAAQ,QACV,EAAG,CACDL,GAAI,EACJC,KAAM,yBACNC,KAAM,aACNC,SAAU,GACVC,WAAY,GACZC,OAAQ,WACV,EAAE,CAsFuBC,GAAG,CAACC,GAAU,WAACrC,MAAAA,CAAoBC,UAAU,oEACxD,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACY,IAAAA,CAAEZ,UAAU,uBAAeoC,EAAON,IAAI,GACvC,WAAC/B,MAAAA,CAAIC,UAAU,wCACb,UAACqC,EAAAA,CAAKA,CAAAA,CAACpB,QAAyB,aAAhBmB,EAAOL,IAAI,CAAkB,UAAY,qBACtDK,EAAOL,IAAI,GAEd,UAACM,EAAAA,CAAKA,CAAAA,CAACpB,QAA2B,WAAlBmB,EAAOF,MAAM,CAAgB,UAAY,mBACtDE,EAAOF,MAAM,GAEhB,WAACI,OAAAA,CAAKtC,UAAU,0CACboC,EAAOJ,QAAQ,CAAC,qBAIvB,WAACjC,MAAAA,CAAIC,UAAU,wCACb,WAACD,MAAAA,CAAIC,UAAU,uBACb,WAACY,IAAAA,CAAEZ,UAAU,gCAAuBoC,EAAOH,UAAU,CAAC,OACtD,UAACrB,IAAAA,CAAEZ,UAAU,yCAAgC,kBAE/C,UAACa,IAAIA,CAACC,KAAM,CAAC,2BAA2B,EAAEsB,EAAOP,EAAE,EAAE,GAAhDhB,OACH,UAACG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUsB,KAAK,cAAK,gBArBLH,EAAOP,EAAE,KA4BlD,UAAC9B,MAAAA,CAAIC,UAAU,gBACb,UAACa,IAAIA,CAACC,KAAK,6BAA6BC,UAAnCF,YAAuD,OAAOH,0BAAwB,oBACzF,UAACM,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUjB,UAAU,SAASe,sBAAoB,SAASL,0BAAwB,oBAAW,iCAQ3H,oCClKI,sBAAsB,0rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAIH,CALS,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAAgB,GAAmB,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAExE,CAAE,KAAM,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CA/BoB8B,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,8CCAA,uCAAqK,yBCArK,sECAA,oDCAA,kECAA,wDCAA,wDCAA,uHRmBI,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CA/BoBA,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,cSvEtB,GTgF8B,KShF9B,+BAAmK,yBCAnK,qDCAA,4DCAA,uDCAA,kECAA,uDCAA,sDCAA,8UCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAmK,CAoBvL,kIAES,EACF,CACP,CACA,QA1BA,IAAsB,uCAAqK,CA0B3L,qIAGA,CACO,CACP,CACA,QAjCA,IAAsB,uCAA4J,CAiClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAlDA,IAAsB,sCAAiJ,CAkDvK,gHACA,gBAlDA,IAAsB,uCAAuJ,CAkD7K,sHACA,aAlDA,IAAsB,uCAAoJ,CAkD1K,mHACA,WAlDA,IAAsB,4CAAgF,CAkDtG,+CACA,cAlDA,IAAsB,4CAAmF,CAkDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,qIAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,+BACA,8BAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC7FD,0DCAA,qDCAA,uCAAqK,yBCArK,iDCAA,yDCAA,4DCAA,6CCAA,uCAAmK", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/./src/app/dashboard/teacher/layout.tsx", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./src/app/dashboard/teacher/page.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/?15fa", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/?47d8", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/?4236", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/?69ba", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/?697d"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function TeacherLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has teacher role\n    requireRole('teacher');\n  }, []);\n  return <>{children}</>;\n}", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "'use client';\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { BookOpen, Users, Bot, TrendingUp, Plus } from 'lucide-react';\nimport Link from 'next/link';\nimport { authStorage } from '@/lib/auth';\nimport NotAssignedToInstitution from '@/components/not-assigned-to-institution';\nimport { useEffect, useState } from 'react';\nexport default function TeacherDashboard() {\n  const [user, setUser] = useState(authStorage.getUser());\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    const currentUser = authStorage.getUser();\n    setUser(currentUser);\n    setIsLoading(false);\n  }, []);\n\n  // Show loading state\n  if (isLoading) {\n    return <div className='flex items-center justify-center min-h-screen'>Loading...</div>;\n  }\n\n  // Show not assigned message if user has no institution\n  if (!user?.institutionId) {\n    return <NotAssignedToInstitution userRole='teacher' />;\n  }\n\n  // Mock data - in real app, this would come from API\n  const stats = {\n    totalClasses: 5,\n    totalCourses: 12,\n    totalStudents: 150,\n    completionRate: 78\n  };\n  const recentCourses = [{\n    id: 1,\n    name: 'Introduction to Mathematics',\n    type: 'self_paced',\n    students: 45,\n    completion: 85,\n    status: 'active'\n  }, {\n    id: 2,\n    name: 'Basic Physics',\n    type: 'verified',\n    students: 32,\n    completion: 72,\n    status: 'active'\n  }, {\n    id: 3,\n    name: 'Chemistry Fundamentals',\n    type: 'self_paced',\n    students: 28,\n    completion: 90,\n    status: 'completed'\n  }];\n  return <div className='space-y-6' data-sentry-component=\"TeacherDashboard\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>\r\n            Teacher Dashboard\r\n          </h1>\r\n          <p className='text-muted-foreground'>\r\n            Manage your classes, courses, and track student progress\r\n          </p>\r\n        </div>\r\n        <div className='flex space-x-2'>\r\n          <Link href='/dashboard/teacher/courses/generate' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n            <Button variant='outline' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n              <Bot className='mr-2 h-4 w-4' data-sentry-element=\"Bot\" data-sentry-source-file=\"page.tsx\" />\r\n              AI Generator\r\n            </Button>\r\n          </Link>\r\n          <Link href='/dashboard/teacher/courses/new' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n            <Button data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n              <Plus className='mr-2 h-4 w-4' data-sentry-element=\"Plus\" data-sentry-source-file=\"page.tsx\" />\r\n              Create Course\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Total Classes</CardTitle>\r\n            <Users className='text-muted-foreground h-4 w-4' data-sentry-element=\"Users\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.totalClasses}</div>\r\n            <p className='text-muted-foreground text-xs'>Active classes</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Total Courses</CardTitle>\r\n            <BookOpen className='text-muted-foreground h-4 w-4' data-sentry-element=\"BookOpen\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.totalCourses}</div>\r\n            <p className='text-muted-foreground text-xs'>Published courses</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">\r\n              Total Students\r\n            </CardTitle>\r\n            <Users className='text-muted-foreground h-4 w-4' data-sentry-element=\"Users\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.totalStudents}</div>\r\n            <p className='text-muted-foreground text-xs'>Enrolled students</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">\r\n              Completion Rate\r\n            </CardTitle>\r\n            <TrendingUp className='text-muted-foreground h-4 w-4' data-sentry-element=\"TrendingUp\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.completionRate}%</div>\r\n            <p className='text-muted-foreground text-xs'>Average completion</p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Recent Courses */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Recent Courses</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">Your latest course activities</CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <div className='space-y-4'>\r\n            {recentCourses.map(course => <div key={course.id} className='flex items-center justify-between rounded-lg border p-4'>\r\n                <div className='space-y-1'>\r\n                  <p className='font-medium'>{course.name}</p>\r\n                  <div className='flex items-center space-x-2'>\r\n                    <Badge variant={course.type === 'verified' ? 'default' : 'secondary'}>\r\n                      {course.type}\r\n                    </Badge>\r\n                    <Badge variant={course.status === 'active' ? 'default' : 'outline'}>\r\n                      {course.status}\r\n                    </Badge>\r\n                    <span className='text-muted-foreground text-sm'>\r\n                      {course.students} students\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div className='flex items-center space-x-4'>\r\n                  <div className='text-right'>\r\n                    <p className='text-sm font-medium'>{course.completion}%</p>\r\n                    <p className='text-muted-foreground text-xs'>completion</p>\r\n                  </div>\r\n                  <Link href={`/dashboard/teacher/courses/${course.id}`}>\r\n                    <Button variant='outline' size='sm'>\r\n                      View\r\n                    </Button>\r\n                  </Link>\r\n                </div>\r\n              </div>)}\r\n          </div>\r\n          <div className='mt-4'>\r\n            <Link href='/dashboard/teacher/courses' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n              <Button variant='outline' className='w-full' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                View All Courses\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/teacher',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/teacher',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/teacher',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/teacher',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"node:os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\page.tsx\");\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'teacher',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/teacher/page\",\n        pathname: \"/dashboard/teacher\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\page.tsx\");\n"], "names": ["TeacherLayout", "children", "TeacherDashboard", "user", "setUser", "useState", "authStorage", "getUser", "isLoading", "setIsLoading", "div", "className", "institutionId", "NotAssignedToInstitution", "userRole", "stats", "totalClasses", "totalCourses", "totalStudents", "completionRate", "data-sentry-component", "data-sentry-source-file", "h1", "p", "Link", "href", "data-sentry-element", "<PERSON><PERSON>", "variant", "Bot", "Plus", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Users", "<PERSON><PERSON><PERSON><PERSON>", "BookOpen", "TrendingUp", "CardDescription", "recentCourses", "id", "name", "type", "students", "completion", "status", "map", "course", "Badge", "span", "size", "serverComponentModule.default"], "sourceRoot": ""}