{"version": 3, "file": "3163.js", "mappings": "0vBAuBO,SAASA,EAAc,MAC5BC,CAAI,UACJC,CAAQ,CACW,EACnB,GAAM,CAACC,EAAkBC,EAAoB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACC,EAAkBC,EAAoB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAACG,EAAQP,EAAKQ,SAAS,EAAIR,EAAKS,OAAO,GACzFC,EAAeC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAmB,MAkE9C,MAAO,WAACC,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,gBAAgBC,0BAAwB,gCAE5F,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAaC,sBAAoB,QAAQH,0BAAwB,+BAAsB,kBACtG,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,aAAaC,YAAY,uBAAuBC,MAAOtB,EAAKuB,IAAI,CAAEC,SAAUC,GAAKxB,EAAS,CACpGsB,KAAME,EAAEC,MAAM,CAACJ,KAAK,GAClBJ,sBAAoB,QAAQH,0BAAwB,2BAIxD,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAaC,sBAAoB,QAAQH,0BAAwB,+BAAsB,sBACtG,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,aAAaC,YAAY,2BAA2BC,MAAOtB,EAAK2B,UAAU,CAAEH,SAAUC,GAAKxB,EAAS,CAC9G0B,WAAYF,EAAEC,MAAM,CAACJ,KACvB,GAAIJ,sBAAoB,QAAQH,0BAAwB,2BAIxD,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAaC,sBAAoB,QAAQH,0BAAwB,+BAAsB,kBACtG,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,aAAaC,YAAY,yBAAyBC,MAAOtB,EAAK4B,UAAU,CAAEJ,SAAUC,GAAKxB,EAAS,CAC5G2B,WAAYH,EAAEC,MAAM,CAACJ,KAAK,CAACO,WAAW,EACxC,GAAIhB,UAAU,SAASK,sBAAoB,QAAQH,0BAAwB,wBACzE,WAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,UAAUC,QAzFrB,CAyF8BC,IAxFvD/B,GAAoB,GAEpBgC,WAAW,KAETlC,EAAS,CACP2B,WAFWQ,CAECC,IAFIC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,GAAGX,WAAW,EAGnE,GACA1B,GAAoB,GACpBsC,EAAAA,EAAKA,CAACC,OAAO,CAAC,8BAChB,EAAG,IACL,EA8E6EC,SAAUzC,EAAkBgB,sBAAoB,SAASH,0BAAwB,gCACpJ,UAAC6B,EAAAA,CAAOA,CAAAA,CAAC/B,UAAU,eAAeK,sBAAoB,UAAUH,0BAAwB,wBACvFb,EAAmB,aAAe,iBAGvC,UAAC2C,IAAAA,CAAEhC,UAAU,yCAAgC,+DAM/C,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,cAAcC,sBAAoB,QAAQH,0BAAwB,+BAAsB,uBACvG,UAAC+B,EAAAA,CAAQA,CAAAA,CAAC1B,GAAG,cAAcC,YAAY,iCAAiCC,MAAOtB,EAAK+C,WAAW,CAAEvB,SAAUC,GAAKxB,EAAS,CACzH8C,YAAatB,EAAEC,MAAM,CAACJ,KACxB,GAAI0B,KAAM,EAAG9B,sBAAoB,WAAWH,0BAAwB,2BAIpE,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACE,sBAAoB,QAAQH,0BAAwB,+BAAsB,gBAChFf,EAAKiD,iBAAiB,CAAG,WAACrC,MAAAA,CAAIC,UAAU,qBACrC,UAACqC,MAAAA,CAAIC,IAAKnD,EAAKiD,iBAAiB,CAAEG,IAAI,eAAevC,UAAU,uDAC/D,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,cAAcqB,KAAK,KAAKxC,UAAU,yBAAyBoB,QA5E1E,CA4EmFqB,IA3EtGtD,EAAKiD,iBAAiB,EAAE,IACtBM,eAAe,CAACvD,EAAKiD,iBAAiB,EAE5ChD,EAAS,CACPuD,gBAAYC,EACZR,uBAAmBQ,CACrB,EACF,WAqEY,UAACC,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,iBAER,WAACD,MAAAA,CAAIC,UAAU,wMAAwMoB,QAAS,IAAMvB,EAAaiD,OAAO,EAAEC,kBACnQ,UAACC,EAAAA,CAAMA,CAAAA,CAAChD,UAAU,+CAClB,UAACgC,IAAAA,CAAEhC,UAAU,yCAAgC,kCAG7C,UAACgC,IAAAA,CAAEhC,UAAU,8CAAqC,2BAItD,UAACiD,QAAAA,CAAMC,IAAKrD,EAAcqB,KAAK,OAAOiC,OAAO,UAAUxC,SAhHlCyC,CAgH4CC,GA/GrE,IAAMC,EAAOF,EAAMvC,MAAM,CAAC0C,KAAK,EAAE,CAAC,EAAE,CACpC,GAAI,CAACD,EAAM,OAGX,GAAI,CAACA,EAAKpC,IAAI,CAACsC,UAAU,CAAC,UAAW,YACnC5B,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,4BAKd,GAAIH,EAAKd,IAAI,CAAG,IAAI,IAAa,GAAN,SACzBZ,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,4BAKd,IAAMC,EAAaC,IAAIC,eAAe,CAACN,GACvClE,EAAS,CACPuD,WAAYW,EACZlB,kBAAmBsB,CACrB,GACA9B,EAAAA,EAAKA,CAACC,OAAO,CAAC,2BAChB,EAyF0F7B,UAAU,cAIhG,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACG,EAAAA,CAAKA,CAAAA,CAACE,sBAAoB,QAAQH,0BAAwB,+BAAsB,kBACjF,WAAC2D,EAAAA,EAAOA,CAAAA,CAACxD,sBAAoB,UAAUH,0BAAwB,gCAC7D,UAAC4D,EAAAA,EAAcA,CAAAA,CAACC,OAAO,IAAC1D,sBAAoB,iBAAiBH,0BAAwB,+BACnF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,aAAaK,sBAAoB,SAASH,0BAAwB,+BAC5G,UAAC8D,EAAAA,CAAIA,CAAAA,CAAChE,UAAU,gCAAgCK,sBAAoB,OAAOH,0BAAwB,4BAGvG,UAAC+D,EAAAA,EAAcA,CAAAA,CAACjE,UAAU,OAAOkE,MAAM,QAAQ7D,sBAAoB,iBAAiBH,0BAAwB,+BAC1G,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACmE,KAAAA,CAAGnE,UAAU,+BAAsB,0BACpC,WAACD,MAAAA,CAAIC,UAAU,mCACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACD,MAAAA,CAAIC,UAAU,uCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,YAAYd,sBAAoB,QAAQH,0BAAwB,+BAAsB,iBAEvG,WAACmE,KAAAA,CAAGrE,UAAU,oDACZ,UAACsE,KAAAA,UAAG,6CACJ,UAACA,KAAAA,UAAG,+BACJ,UAACA,KAAAA,UAAG,yCACJ,UAACA,KAAAA,UAAG,6CAGR,WAACvE,MAAAA,CAAIC,UAAU,sBACb,UAACD,MAAAA,CAAIC,UAAU,uCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUd,sBAAoB,QAAQH,0BAAwB,+BAAsB,eAErG,WAACmE,KAAAA,CAAGrE,UAAU,oDACZ,UAACsE,KAAAA,UAAG,wCACJ,UAACA,KAAAA,UAAG,uCACJ,UAACA,KAAAA,UAAG,sCACJ,UAACA,KAAAA,UAAG,0DAQlB,WAACC,EAAAA,EAAMA,CAAAA,CAAC9D,MAAOtB,EAAK+B,IAAI,CAAEsD,cAAe,GAAsCpF,EAAS,CACxF8B,KAAMT,CACR,GAAIJ,sBAAoB,SAASH,0BAAwB,gCACrD,UAACuE,EAAAA,EAAaA,CAAAA,CAACpE,sBAAoB,gBAAgBH,0BAAwB,+BACzE,UAACwE,EAAAA,EAAWA,CAAAA,CAACrE,sBAAoB,cAAcH,0BAAwB,0BAEzE,WAACyE,EAAAA,EAAaA,CAAAA,CAACtE,sBAAoB,gBAAgBH,0BAAwB,gCACzE,UAAC0E,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,aAAaJ,sBAAoB,aAAaH,0BAAwB,+BACtF,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,YAAYd,sBAAoB,QAAQH,0BAAwB,+BAAsB,eACrG,UAAC2E,OAAAA,UAAK,gDAGV,UAACD,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,WAAWJ,sBAAoB,aAAaH,0BAAwB,+BACpF,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUd,sBAAoB,QAAQH,0BAAwB,+BAAsB,aACnG,UAAC2E,OAAAA,UAAK,oDAQhB,WAAC9E,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACE,sBAAoB,QAAQH,0BAAwB,+BAAsB,uBACjF,WAACqE,EAAAA,EAAMA,CAAAA,CAAC9D,MAAOtB,EAAK2F,cAAc,CAAEN,cArJP,CAqJsBO,GApJvD,IAAMC,EAA+B,CACnCF,eAAgBrE,CAClB,CAGI,CAAW,aAAVA,GAAkC,MAAK,GAAfA,EAAqB,CAACtB,EAAK8F,QAAQ,EAAE,CAChED,EAAQC,QAAQ,CAAG,OAErB7F,EAAS4F,EACX,EA2IqF3E,sBAAoB,SAASH,0BAAwB,gCAClI,UAACuE,EAAAA,EAAaA,CAAAA,CAACpE,sBAAoB,gBAAgBH,0BAAwB,+BACzE,UAACwE,EAAAA,EAAWA,CAAAA,CAACrE,sBAAoB,cAAcH,0BAAwB,0BAEzE,WAACyE,EAAAA,EAAaA,CAAAA,CAACtE,sBAAoB,gBAAgBH,0BAAwB,gCACzE,UAAC0E,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,OAAOJ,sBAAoB,aAAaH,0BAAwB,+BAChF,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUd,sBAAoB,QAAQH,0BAAwB,+BAAsB,SACnG,UAAC2E,OAAAA,UAAK,qCAGV,UAACD,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,aAAaJ,sBAAoB,aAAaH,0BAAwB,+BACtF,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUd,sBAAoB,QAAQH,0BAAwB,+BAAsB,aACnG,UAAC2E,OAAAA,UAAK,+BAGV,UAACD,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,OAAOJ,sBAAoB,aAAaH,0BAAwB,+BAChF,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUd,sBAAoB,QAAQH,0BAAwB,+BAAsB,aACnG,UAAC2E,OAAAA,UAAK,4BAGV,UAACD,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,WAAWJ,sBAAoB,aAAaH,0BAAwB,+BACpF,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUd,sBAAoB,QAAQH,0BAAwB,+BAAsB,aACnG,UAAC2E,OAAAA,UAAK,sCAQd1F,CAAwB,aAAxBA,EAAK2F,cAAc,EAA2C,SAAxB3F,EAAK2F,cAAc,CAAU,EAAM,WAAC/E,MAAAA,CAAIC,UAAU,kDACtF,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,iBAAQ,YACvB,UAACE,EAAAA,CAAKA,CAAAA,CAACC,GAAG,QAAQW,KAAK,SAASV,YAAY,IAAIC,MAAOtB,EAAK+F,KAAK,EAAI,GAAIvE,SAAUC,GAAKxB,EAAS,CACnG8F,MAAOC,WAAWvE,EAAEC,MAAM,CAACJ,KAAK,GAAK,CACvC,GAAI2E,IAAI,IAAIC,KAAK,YAEf,WAACtF,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,UAAC,gBACP,WAACoE,EAAAA,EAAMA,CAAAA,CAAC9D,MAAOtB,EAAK8F,QAAQ,EAAI,MAAOT,cAAe/D,GAASrB,EAAS,CAC1E6F,SAAUxE,CACZ,aACM,UAACgE,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAAAA,KAEd,WAACC,EAAAA,EAAaA,CAAAA,WACZ,UAACC,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,eAAM,iBACxB,UAACmE,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,eAAM,iBACxB,UAACmE,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,eAAM,2BAOlC,WAACV,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACsF,EAAAA,CAAQA,CAAAA,CAAC/E,GAAG,kBAAkBgF,QAAS/F,EAAkBgG,gBAvMpC,CAuMqDC,GAtMjFhG,EAAoB8F,GAChB,GACFnG,EAAS,CACPO,GAFU,OAEC,KACXC,QAAS,IACX,EAEJ,EA+L0GS,sBAAoB,WAAWH,0BAAwB,wBACzJ,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,kBAAkBC,sBAAoB,QAAQH,0BAAwB,+BAAsB,oCAE5GV,GAAoB,WAACO,MAAAA,CAAIC,UAAU,kDAChC,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,UAAC,kBACP,WAAC0D,EAAAA,EAAOA,CAAAA,WACN,UAACC,EAAAA,EAAcA,CAAAA,CAACC,OAAO,aACrB,WAAC9C,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUnB,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6CAA8C,CAACvG,EAAKQ,SAAS,EAAI,mCACvG,UAACgG,EAAAA,CAAYA,CAAAA,CAAC3F,UAAU,iBACvBb,EAAKQ,SAAS,CAAGiG,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAACzG,EAAKQ,SAAS,CAAE,MAAO,CAClDkG,OAAQtF,EAAAA,EAAEA,GACP,2BAGL,UAAC0D,EAAAA,EAAcA,CAAAA,CAACjE,UAAU,aAAakE,MAAM,iBAC3C,UAAC4B,EAAAA,CAAQA,CAAAA,CAACC,KAAK,SAASC,SAAU7G,EAAKQ,SAAS,OAAIiD,EAAWqD,SAAUC,GAAQ9G,EAAS,CAC5FO,UAAWuG,CACb,GAAIpE,SAAUoE,GAAQA,EAAO,IAAIC,KAAQC,YAAY,cAKvD,WAACrG,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,UAAC,oBACP,WAAC0D,EAAAA,EAAOA,CAAAA,WACN,UAACC,EAAAA,EAAcA,CAAAA,CAACC,OAAO,aACrB,WAAC9C,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUnB,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6CAA8C,CAACvG,EAAKS,OAAO,EAAI,mCACrG,UAAC+F,EAAAA,CAAYA,CAAAA,CAAC3F,UAAU,iBACvBb,EAAKS,OAAO,CAAGgG,CAAAA,EAAAA,EAAAA,EAAAA,CAAMA,CAACzG,EAAKS,OAAO,CAAE,MAAO,CAC9CiG,OAAQtF,EAAAA,EAAEA,GACP,6BAGL,UAAC0D,EAAAA,EAAcA,CAAAA,CAACjE,UAAU,aAAakE,MAAM,iBAC3C,UAAC4B,EAAAA,CAAQA,CAAAA,CAACC,KAAK,SAASC,SAAU7G,EAAKS,OAAO,OAAIgD,EAAWqD,SAAUC,GAAQ9G,EAAS,CAC1FQ,QAASsG,CACX,GAAIpE,SAAUoE,IAAQxG,EAAQwG,EAAO,IAAIC,MAAUhH,EAAKQ,SAAS,EAAIuG,GAAQ/G,EAAKQ,SAAAA,EAAYyG,YAAY,sBASxH,gBCtUA,SAASC,EAAO,WACdrG,CAAS,CACT,GAAGsG,EAC+C,EAClD,MAAO,UAACC,EAAAA,EAAoB,EAACC,YAAU,SAASxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4WAA6W1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,uBAAuBJ,wBAAsB,SAASC,0BAAwB,sBAChiB,UAACqG,EAAAA,EAAqB,EAACC,YAAU,eAAexG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4QAA6QrF,sBAAoB,wBAAwBH,0BAAwB,gBAErZ,mHCQO,SAASuG,EAAoB,MAClCtH,CAAI,UACJC,CAAQ,CACiB,EACzB,GAAM,CAACsH,EAAiBC,EAAmB,CAAGpH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,IAAIqH,KAClE,CAACC,EAAeC,EAAiB,CAAGvH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAoB,MAChE,CAACwH,EAAgBC,EAAkB,CAAGzH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAGjD,CACD0H,SAAU,GACVC,QAAS,IACX,GACM,CAACC,EAAoBC,EAAsB,CAAG7H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvD,CAAC8H,EAAqBC,EAAuB,CAAG/H,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzDgI,EAAwB,IAC5B,IAAMC,EAAc,IAAIZ,IAAIF,GACxBc,EAAYC,GAAG,CAACR,GAClBO,EAAYE,MADiB,CACVT,GAEnBO,EAAYG,GAAG,CAACV,GAElBN,EAAmBa,EACrB,EACMI,EAAkB,KAStBd,EAR8B,CAC5BvG,GAAI,CAAC,OAAO,EAAE4F,CAOC0B,IAPIC,GAAG,IAAI,CAC1BpH,KAAM,GACNwB,YAAa,GACb6F,WAAY5I,EAAK6I,OAAO,CAACC,MAAM,CAC/BC,SAAU,EAAE,CACZC,eAAe,CACjB,GAEAf,GAAsB,EACxB,EACMgB,EAAa,IACjBtB,EAAiB,CACf,GAAGuB,CAAU,GAEfjB,GAAsB,EACxB,EAqBMkB,EAAe,IAKnBlJ,EAAS,CACP4I,QALqB7I,CAKZoJ,CALiBP,OAAO,CAACQ,MAAM,CAACC,GAAKA,EAAElI,EAAE,GAAK0G,GAAUyB,GAAG,CAAC,CAACD,EAAGE,IAAW,EACpF,EADoF,CACjFF,CAAC,CACJV,WAAYY,EACd,EAGA,GACA/G,EAAAA,EAAKA,CAACC,OAAO,CAAC,yBAChB,EACM+G,EAAmB,IACvB,IAAMP,EAAalJ,EAAK6I,OAAO,CAACa,IAAI,CAACJ,GAAKA,EAAElI,EAAE,GAAK0G,GAC9CoB,IAQLrB,EAAkB,MARD,IASfC,EACAC,QAT8B,CASrB4B,GARL,CAAC,QAAQ,EAAE3C,KAAK2B,GAAG,IAAI,CAC3BpH,KAAM,GACNqI,QAAS,EAAE,CACXhB,WAAYM,EAAWH,QAAQ,CAACD,MAAM,CACtCe,gBAAgB,CAClB,CAIA,GACA1B,GAAuB,GACzB,EACM2B,EAAc,CAAChC,EAAkBC,KACrCF,EAAkB,CAChBC,WACAC,QAAS,CACP,GAAGA,CAAO,CAEd,GACAI,GAAuB,EACzB,EAgCM4B,EAAgB,CAACjC,EAAkBkC,KAcvC/J,EAAS,CACP4I,QAdqB7I,CAcZoJ,CAdiBP,OAAO,CAACU,GAAG,CAACL,IACtC,GAAIA,EAAW9H,EAAE,GAAK0G,EAAU,CAC9B,IAAMmC,EAAkBf,EAAWH,QAAQ,CAACM,MAAM,CAACa,GAAKA,EAAE9I,EAAE,GAAK4I,GAAWT,GAAG,CAAC,CAACW,EAAGV,IAAW,EAC7F,EAD6F,CAC1FU,CAAC,CACJtB,WAAYY,EACd,GACA,MAAO,CACL,GAAGN,CAAU,CACbH,SAAUkB,CACZ,CACF,CACA,OAAOf,CACT,EAGA,GACAzG,EAAAA,EAAKA,CAACC,OAAO,CAAC,2BAChB,EACMyH,EAAa,CAACrC,EAAkBsC,KACpC,IAAMC,EAAerK,EAAK6I,OAAO,CAACyB,SAAS,CAAChB,GAAKA,EAAElI,EAAE,GAAK0G,GAC1D,GAAqB,CAAC,IAAlBuC,EAAqB,OACzB,IAAME,EAAyB,OAAdH,EAAqBC,EAAe,EAAIA,EAAe,EACxE,GAAIE,EAAW,GAAKA,GAAYvK,EAAK6I,OAAO,CAACC,MAAM,CAAE,OACrD,IAAMM,EAAiB,IAAIpJ,EAAK6I,OAAO,CAAC,EACvCO,CAAc,CAACiB,EAAa,CAAEjB,CAAc,CAACmB,EAAS,CAAC,CAAG,CAACnB,CAAc,CAACmB,EAAS,CAAEnB,CAAc,CAACiB,EAAa,CAAC,CAGnHjB,EAAeoB,OAAO,CAAC,CAACtB,EAAYM,KAClCN,EAAWN,UAAU,CAAGY,CAC1B,GACAvJ,EAAS,CACP4I,QAASO,CACX,EACF,EACA,MAAO,WAACxI,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,sBAAsBC,0BAAwB,sCAElG,WAACH,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,UAAC6J,KAAAA,CAAG5J,UAAU,iCAAwB,0BACtC,UAACgC,IAAAA,CAAEhC,UAAU,yCAAgC,gEAI/C,WAACiB,EAAAA,CAAMA,CAAAA,CAACG,QAASwG,EAAiBvH,sBAAoB,SAASH,0BAAwB,sCACrF,UAAC2J,EAAAA,CAAIA,CAAAA,CAAC7J,UAAU,eAAeK,sBAAoB,OAAOH,0BAAwB,8BAA8B,qBAMnHf,MAAK6I,OAAO,CAACC,MAAM,CAAS,UAAC6B,EAAAA,EAAIA,CAAAA,UAC9B,WAACC,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,4DACrB,UAACgK,EAAAA,CAAQA,CAAAA,CAAChK,UAAU,yCACpB,UAAC4J,KAAAA,CAAG5J,UAAU,sCAA6B,oBAC3C,UAACgC,IAAAA,CAAEhC,UAAU,kDAAyC,yDAGtD,WAACiB,EAAAA,CAAMA,CAAAA,CAACG,QAASwG,YACf,UAACiC,EAAAA,CAAIA,CAAAA,CAAC7J,UAAU,iBAAiB,6BAI7B,UAACD,MAAAA,CAAIC,UAAU,qBACtBb,EAAK6I,OAAO,CAACU,GAAG,CAAC,CAACL,EAAY4B,KACjC,IAAMC,EAAaxD,EAAgBe,GAAG,CAACY,EAAW9H,EAAE,EACpD,MAAO,WAACuJ,EAAAA,EAAIA,CAAAA,CAAqB9J,UAAU,4BACnC,UAACmK,EAAAA,EAAUA,CAAAA,CAACnK,UAAU,gBACpB,WAACD,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACoK,EAAAA,CAAYA,CAAAA,CAACpK,UAAU,8CACxB,WAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,oBAAU,SAAO8I,EAAc,QAEhD,WAAClK,MAAAA,WACC,UAACsK,EAAAA,EAASA,CAAAA,CAACrK,UAAU,qBAAaqI,EAAW3H,IAAI,EAAI,qBACpD2H,EAAWnG,WAAW,EAAI,UAACoI,EAAAA,EAAeA,CAAAA,CAACtK,UAAU,gBACjDqI,EAAWnG,WAAW,SAK/B,WAACnC,MAAAA,CAAIC,UAAU,wCACZqI,EAAWF,aAAa,EAAI,WAAC/D,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,sBACxC,UAACoJ,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,gBAG3C,WAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,oBACZkH,EAAWH,QAAQ,CAACD,MAAM,CAAC,cAG9B,WAAClI,MAAAA,CAAIC,UAAU,wCACb,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMkI,EAAWjB,EAAW9H,EAAE,CAAE,MAAOuB,SAA0B,IAAhBmI,WAAmB,MAG/G,UAAChJ,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMkI,EAAWjB,EAAW9H,EAAE,CAAE,QAASuB,SAAUmI,IAAgB9K,EAAK6I,OAAO,CAACC,MAAM,CAAG,WAAG,MAGvI,UAAChH,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMgH,EAAWC,YAC1D,UAACmC,EAAAA,CAAIA,CAAAA,CAACxK,UAAU,cAElB,WAACyK,EAAAA,EAAWA,CAAAA,WACV,UAACC,EAAAA,EAAkBA,CAAAA,CAAC3G,OAAO,aACzB,UAAC9C,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,cAC3B,UAACmI,EAAAA,CAAMA,CAAAA,CAAC3K,UAAU,gBAGtB,WAAC4K,EAAAA,EAAkBA,CAAAA,WACjB,WAACC,EAAAA,EAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAgBA,CAAAA,UAAC,gBAClB,WAACC,EAAAA,EAAsBA,CAAAA,WAAC,4CAC0B1C,EAAW3H,IAAI,CAAC,gEAIpE,WAACsK,EAAAA,EAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAiBA,CAAAA,UAAC,UACnB,UAACC,EAAAA,EAAiBA,CAAAA,CAAC9J,QAAS,IAAMkH,EAAaD,EAAW9H,EAAE,WAAG,mBAMrE,UAACU,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMmG,EAAsBc,EAAW9H,EAAE,WACjF2J,EAAa,UAACiB,EAAAA,CAAWA,CAAAA,CAACnL,UAAU,YAAe,UAACoL,EAAAA,CAAYA,CAAAA,CAACpL,UAAU,yBAOrFkK,GAAc,UAACH,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,gBAClC,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,8CACb,UAACmE,KAAAA,CAAGnE,UAAU,+BAAsB,aACpC,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,IAAMwH,EAAiBP,EAAW9H,EAAE,YAC/E,UAACsJ,EAAAA,CAAIA,CAAAA,CAAC7J,UAAU,iBAAiB,uBAKL,MAApBkI,QAAQ,CAACD,MAAM,CAAS,WAAClI,MAAAA,CAAIC,UAAU,mDAC/C,UAACqL,EAAAA,CAAQA,CAAAA,CAACrL,UAAU,yBACpB,UAACgC,IAAAA,CAAEhC,UAAU,mBAAU,yBAChB,UAACD,MAAAA,CAAIC,UAAU,qBACrBqI,EAAWH,QAAQ,CAACQ,GAAG,CAAC,CAACxB,EAASoE,IAAiB,WAACvL,MAAAA,CAAqBC,UAAU,yEAChF,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACoK,EAAAA,CAAYA,CAAAA,CAACpK,UAAU,8CACxB,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,UAAUnB,UAAU,mBAChCsL,EAAe,IAElB,WAACvL,MAAAA,WACC,UAACiC,IAAAA,CAAEhC,UAAU,+BACVkH,EAAQxG,IAAI,EAAI,uBAElBwG,EAAQ8B,cAAc,EAAI,WAAC5E,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,YAAYnB,UAAU,yBAC5D,UAACuK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,gBAM/C,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAM6H,EAAYZ,EAAW9H,EAAE,CAAE2G,YAC1E,UAACsD,EAAAA,CAAIA,CAAAA,CAACxK,UAAU,cAElB,WAACyK,EAAAA,EAAWA,CAAAA,WACV,UAACC,EAAAA,EAAkBA,CAAAA,CAAC3G,OAAO,aACzB,UAAC9C,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,cAC3B,UAACmI,EAAAA,CAAMA,CAAAA,CAAC3K,UAAU,gBAGtB,WAAC4K,EAAAA,EAAkBA,CAAAA,WACjB,WAACC,EAAAA,EAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAgBA,CAAAA,UAAC,kBAClB,WAACC,EAAAA,EAAsBA,CAAAA,WAAC,8CAC4B7D,EAAQxG,IAAI,CAAC,WAGnE,WAACsK,EAAAA,EAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAiBA,CAAAA,UAAC,UACnB,UAACC,EAAAA,EAAiBA,CAAAA,CAAC9J,QAAS,IAAM8H,EAAcb,EAAW9H,EAAE,CAAE2G,EAAQ3G,EAAE,WAAG,wBApC1B2G,EAAQ3G,EAAE,YA/ExE8H,EAAW9H,EAAE,CA+HjC,KAIA,UAACgL,EAAAA,EAAMA,CAAAA,CAACC,KAAMrE,EAAoBsE,aAAcrE,EAAuB/G,sBAAoB,SAASH,0BAAwB,qCAC1H,WAACwL,EAAAA,EAAaA,CAAAA,CAAC1L,UAAU,cAAcK,sBAAoB,gBAAgBH,0BAAwB,sCACjG,WAACyL,EAAAA,EAAYA,CAAAA,CAACtL,sBAAoB,eAAeH,0BAAwB,sCACvE,UAAC0L,EAAAA,EAAWA,CAAAA,CAACvL,sBAAoB,cAAcH,0BAAwB,qCACpE2G,GAAenG,KAAO,aAAe,sBAExC,UAACmL,EAAAA,EAAiBA,CAAAA,CAACxL,sBAAoB,oBAAoBH,0BAAwB,qCAA4B,2CAKjH,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAaC,sBAAoB,QAAQH,0BAAwB,qCAA4B,iBAC5G,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,aAAaC,YAAY,sBAAsBC,MAAOoG,GAAenG,MAAQ,GAAIC,SAAUC,GAAKkG,EAAiBgF,GAAQA,EAAO,CAC1I,GAAGA,CAAI,CACPpL,KAAME,EAAEC,MAAM,CAACJ,KAAK,EAClB,MAAOJ,sBAAoB,QAAQH,0BAAwB,iCAG/D,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,oBAAoBC,sBAAoB,QAAQH,0BAAwB,qCAA4B,cACnH,UAAC+B,EAAAA,CAAQA,CAAAA,CAAC1B,GAAG,oBAAoBC,YAAY,gCAAgCC,MAAOoG,GAAe3E,aAAe,GAAIvB,SAAUC,GAAKkG,EAAiBgF,GAAQA,EAAO,CACrK,GAAGA,CAAI,CACP5J,YAAatB,EAAEC,MAAM,CAACJ,KAAK,EACzB,MAAO0B,KAAM,EAAG9B,sBAAoB,WAAWH,0BAAwB,iCAG3E,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACqG,EAAMA,CAAC9F,GAAD8F,gBAAoBd,QAASsB,GAAesB,gBAAiB,EAAO3C,gBAAiBD,GAAWuB,EAAiBgF,GAAQA,EAAO,CACvI,GAAGA,CAAI,CACP3D,cAAe5C,CACjB,EAAI,MAAOlF,sBAAoB,SAASH,0BAAwB,8BAC9D,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,gBAAgBC,sBAAoB,QAAQH,0BAAwB,qCAA4B,wCAInH,WAAC6L,EAAAA,EAAYA,CAAAA,CAAC1L,sBAAoB,eAAeH,0BAAwB,sCACvE,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMgG,GAAsB,GAAQ/G,sBAAoB,SAASH,0BAAwB,qCAA4B,UAGxJ,WAACe,EAAAA,CAAMA,CAAAA,CAACG,QApUC,CAoUQ4K,IAnUzB,GAAI,CAACnF,GAAiB,CAACA,EAAcnG,IAAI,CAACuL,IAAI,GAAI,YAChDrK,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,0BAGd,IAAM8E,EAAiB,IAAIpJ,EAAK6I,OAAO,CAAC,CAClCkE,EAAgB3D,EAAekB,SAAS,CAAChB,GAAKA,EAAElI,EAAE,GAAKsG,EAActG,EAAE,EACzE2L,GAAiB,GACnB3D,CAAc,CAAC2D,EAAc,CAAGrF,EAChCjF,EAAAA,EAAKA,CAACC,OAAO,CAAC,+BAEd0G,EAAe4D,IAAI,CAACtF,GACpBjF,EAAAA,EAAKA,CAACC,OAAO,CAAC,+BAEhBzC,EAAS,CACP4I,QAASO,CACX,GACAnB,GAAsB,GACtBN,EAAiB,KACnB,EAiTuCzG,sBAAoB,SAASH,0BAAwB,sCAC/E2G,GAAenG,KAAO,WAAa,SAAS,oBAOrD,UAAC6K,EAAAA,EAAMA,CAAAA,CAACC,KAAMnE,EAAqBoE,aAAcnE,EAAwBjH,sBAAoB,SAASH,0BAAwB,qCAC5H,WAACwL,EAAAA,EAAaA,CAAAA,CAAC1L,UAAU,cAAcK,sBAAoB,gBAAgBH,0BAAwB,sCACjG,WAACyL,EAAAA,EAAYA,CAAAA,CAACtL,sBAAoB,eAAeH,0BAAwB,sCACvE,UAAC0L,EAAAA,EAAWA,CAAAA,CAACvL,sBAAoB,cAAcH,0BAAwB,qCACpE6G,EAAeG,OAAO,EAAExG,KAAO,eAAiB,wBAEnD,UAACmL,EAAAA,EAAiBA,CAAAA,CAACxL,sBAAoB,oBAAoBH,0BAAwB,qCAA4B,6CAKjH,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,cAAcC,sBAAoB,QAAQH,0BAAwB,qCAA4B,mBAC7G,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,cAAcC,YAAY,wBAAwBC,MAAOsG,EAAeG,OAAO,EAAExG,MAAQ,GAAIC,SAAUC,GAAKoG,EAAkB8E,GAAS,EACjJ,EADiJ,CAC9IA,CAAI,CACP5E,QAAS4E,EAAK5E,OAAO,CAAG,CACtB,GAAG4E,EAAK5E,OAAO,CACfxG,KAAME,EAAEC,MAAM,CAACJ,KAAK,EAClB,KACN,GAAKJ,sBAAoB,QAAQH,0BAAwB,iCAGzD,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACqG,EAAMA,CAAC9F,GAAD8F,iBAAqBd,QAASwB,EAAeG,OAAO,EAAE8B,iBAAkB,EAAOxD,gBAAiBD,GAAWyB,EAAkB8E,GAAS,EAC7I,EAD6I,CAC1IA,CAAI,CACP5E,QAAS4E,EAAK5E,OAAO,CAAG,CACtB,GAAG4E,EAAK5E,OAAO,CACf8B,eAAgBzD,CAClB,EAAI,KACN,GAAKlF,sBAAoB,SAASH,0BAAwB,8BACxD,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,iBAAiBC,sBAAoB,QAAQH,0BAAwB,qCAA4B,2CAIpH,WAAC6L,EAAAA,EAAYA,CAAAA,CAAC1L,sBAAoB,eAAeH,0BAAwB,sCACvE,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMkG,EAAuB,IAAQjH,sBAAoB,SAASH,0BAAwB,qCAA4B,UAGzJ,WAACe,EAAAA,CAAMA,CAAAA,CAACG,QA5TE,CA4TOgL,IA3TzB,GAAI,CAACrF,EAAeG,OAAO,EAAI,CAACH,EAAeG,OAAO,CAACxG,IAAI,CAACuL,IAAI,GAAI,YAClErK,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,4BAmBdrE,EAAS,CACP4I,QAjBqB7I,CAiBZoJ,CAjBiBP,OAAO,CAACU,GAAG,CAACL,IACtC,GAAIA,EAAW9H,EAAE,GAAKwG,EAAeE,QAAQ,CAAE,CAC7C,IAAMmC,EAAkB,IAAIf,EAAWH,QAAQ,CAAC,CAC1CgE,EAAgB9C,EAAgBK,SAAS,CAACJ,GAAKA,EAAE9I,EAAE,GAAKwG,EAAeG,OAAO,CAAE3G,EAAE,EAMxF,OALI2L,GAAiB,EACnB9C,CADsB,CACN8C,EAAc,CAAGnF,EAAeG,OAAO,CAEvDkC,EAAgB+C,IAAI,CAACpF,EAAeG,OAAO,EAEtC,CACL,GAAGmB,CAAU,CACbH,SAAUkB,CACZ,CACF,CACA,OAAOf,CACT,EAGA,GACAf,GAAuB,GACvBN,EAAkB,CAChBC,SAAU,GACVC,QAAS,IACX,GACAtF,EAAAA,EAAKA,CAACC,OAAO,CAAC,4BAChB,EA8RwCxB,sBAAoB,SAASH,0BAAwB,sCAChF6G,EAAeG,OAAO,EAAExG,KAAO,WAAa,SAAS,sBAO7DvB,EAAK6I,OAAO,CAACC,MAAM,CAAG,GAAK,WAAC6B,EAAAA,EAAIA,CAAAA,WAC7B,UAACK,EAAAA,EAAUA,CAAAA,UACT,UAACE,EAAAA,EAASA,CAAAA,CAACrK,UAAU,mBAAU,yBAEjC,UAAC+J,EAAAA,EAAWA,CAAAA,UACV,WAAChK,MAAAA,CAAIC,UAAU,8DACb,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,2CAAmCb,EAAK6I,OAAO,CAACC,MAAM,GACrE,UAAClI,MAAAA,CAAIC,UAAU,yCAAgC,aAEjD,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,2CACZb,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKjE,IAAeiE,EAAMjE,EAAWH,QAAQ,CAACD,MAAM,CAAE,KAE9E,UAAClI,MAAAA,CAAIC,UAAU,yCAAgC,eAEjD,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,2CACZb,EAAK6I,OAAO,CAACQ,MAAM,CAACC,GAAKA,EAAEN,aAAa,EAAEF,MAAM,GAEnD,UAAClI,MAAAA,CAAIC,UAAU,yCAAgC,kBAEjD,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,2CACZb,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKjE,IAAeiE,EAAMjE,EAAWH,QAAQ,CAACM,MAAM,CAACa,GAAKA,EAAEL,cAAc,EAAEf,MAAM,CAAE,KAE5G,UAAClI,MAAAA,CAAIC,UAAU,yCAAgC,8BAM/D,yTCvcO,IAAMuM,GAA4C,CAAC,SACxDxD,CAAO,CACPpI,UAAQ,aACRH,EAAc,iBAAiB,CAChC,IACC,IAAMgM,EAASC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAAC,CACvBC,WAAY,CAACC,EAAAA,CAAUA,CAACC,SAAS,CAAC,CAChCC,QAAS,CACPC,OAAQ,CAAC,EAAG,EAAG,EACjB,CACF,GAAIC,EAAAA,EAAIA,CAACH,SAAS,CAAC,CACjBI,aAAa,EACbC,eAAgB,CACdC,MAAO,wCACT,CACF,GAAIC,EAAAA,EAAKA,CAACP,SAAS,CAAC,CAClBK,eAAgB,CACdC,MAAO,8BACT,CACF,GAAIE,GAAAA,EAAKA,CAACR,SAAS,CAAC,CAClBS,WAAW,CACb,GAAIC,GAAAA,CAAQA,CAAEC,GAAAA,CAAWA,CAAEC,GAAAA,CAASA,CAAC,CACrCzE,QAASA,EACT3J,SAAU,CAAC,QACToN,CAAM,CACP,IAEC7L,EADa6L,EAAOiB,KACXC,EADkB,GAE7B,EACAC,YAAa,CACXC,WAAY,CACVV,MAAO,+FACT,CACF,CACF,UACA,EAgCO,EAhCH,CAgCG,GAhCM,GAgCN,EAACnN,MAAAA,CAAIC,UAAU,oDAAoDC,wBAAsB,eAAeC,0BAAwB,8BAEnI,WAACH,MAAAA,CAAIC,UAAU,yEACb,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGC,IAAI,GAAGC,GAAG,GAAIlM,SAAU,CAAC0K,EAAOyB,GAAG,GAAGF,IAAI,GAAI1N,sBAAoB,SAASH,0BAAwB,6BACzK,UAACgO,GAAAA,CAAIA,CAAAA,CAAClO,UAAU,UAAUK,sBAAoB,OAAOH,0BAAwB,wBAE/E,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGK,IAAI,GAAGH,GAAG,GAAIlM,SAAU,CAAC0K,EAAOyB,GAAG,GAAGE,IAAI,GAAI9N,sBAAoB,SAASH,0BAAwB,6BACzK,UAACkO,GAAAA,CAAIA,CAAAA,CAACpO,UAAU,UAAUK,sBAAoB,OAAOH,0BAAwB,wBAG/E,UAACH,MAAAA,CAAIC,UAAU,8BAEf,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGO,UAAU,GAAGL,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,QAAU,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BACxM,UAACqO,GAAAA,CAAIA,CAAAA,CAACvO,UAAU,UAAUK,sBAAoB,OAAOH,0BAAwB,wBAE/E,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGU,YAAY,GAAGR,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,UAAY,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BAC5M,UAACuO,GAAAA,CAAMA,CAAAA,CAACzO,UAAU,UAAUK,sBAAoB,SAASH,0BAAwB,wBAEnF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGY,YAAY,GAAGV,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,UAAY,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BAC5M,UAACyO,GAAAA,CAAaA,CAAAA,CAAC3O,UAAU,UAAUK,sBAAoB,gBAAgBH,0BAAwB,wBAGjG,UAACH,MAAAA,CAAIC,UAAU,8BAEf,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGc,aAAa,CAAC,CACtFC,MAAO,CACT,GAAGb,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,UAAW,CAC9CO,MAAO,CACT,GAAK,cAAgB,GAAIxO,sBAAoB,SAASH,0BAAwB,6BAC1E,UAAC4O,GAAAA,CAAQA,CAAAA,CAAC9O,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,wBAEvF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGc,aAAa,CAAC,CACtFC,MAAO,CACT,GAAGb,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,UAAW,CAC9CO,MAAO,CACT,GAAK,cAAgB,GAAIxO,sBAAoB,SAASH,0BAAwB,6BAC1E,UAAC6O,GAAAA,CAAQA,CAAAA,CAAC/O,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,wBAEvF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGc,aAAa,CAAC,CACtFC,MAAO,CACT,GAAGb,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,UAAW,CAC9CO,MAAO,CACT,GAAK,cAAgB,GAAIxO,sBAAoB,SAASH,0BAAwB,6BAC1E,UAAC8O,GAAAA,CAAQA,CAAAA,CAAChP,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,wBAGvF,UAACH,MAAAA,CAAIC,UAAU,8BAEf,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGmB,gBAAgB,GAAGjB,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,cAAgB,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BACpN,UAACgP,GAAAA,CAAIA,CAAAA,CAAClP,UAAU,UAAUK,sBAAoB,OAAOH,0BAAwB,wBAE/E,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGqB,iBAAiB,GAAGnB,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,eAAiB,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BACtN,UAACkP,GAAAA,CAAWA,CAAAA,CAACpP,UAAU,UAAUK,sBAAoB,cAAcH,0BAAwB,wBAE7F,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGuB,gBAAgB,GAAGrB,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,cAAgB,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BACpN,UAACoP,GAAAA,CAAKA,CAAAA,CAACtP,UAAU,UAAUK,sBAAoB,QAAQH,0BAAwB,wBAEjF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMoL,EAAOqB,KAAK,GAAGC,KAAK,GAAGyB,eAAe,GAAGvB,GAAG,GAAIhO,UAAWwM,EAAO8B,QAAQ,CAAC,aAAe,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BAClN,UAACsP,GAAAA,CAAIA,CAAAA,CAACxP,UAAU,UAAUK,sBAAoB,OAAOH,0BAAwB,wBAG/E,UAACH,MAAAA,CAAIC,UAAU,8BAEf,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QApFxB,CAoFiCqO,IAnF/C,IAAMC,EAAclD,EAAOmD,aAAa,CAAC,QAAQC,IAAI,CAC/CC,EAAMC,OAAOC,MAAM,CAAC,aAAcL,GACxC,GAAY,MAAM,CAAdG,GAGJ,GAAY,KAARA,EAAY,YACdrD,EAAOqB,KAAK,GAAGC,KAAK,GAAGkC,eAAe,CAAC,QAAQC,SAAS,GAAGjC,GAAG,GAGhExB,EAAOqB,KAAK,GAAGC,KAAK,GAAGkC,eAAe,CAAC,QAAQE,OAAO,CAAC,CACrDN,KAAMC,CACR,GAAG7B,GAAG,GACR,EAuE0DhO,UAAWwM,EAAO8B,QAAQ,CAAC,QAAU,cAAgB,GAAIjO,sBAAoB,SAASH,0BAAwB,6BAChK,UAACiQ,EAAAA,CAAQA,CAAAA,CAACnQ,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,wBAEvF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QA/FvB,CA+FgCgP,IA9F/C,IAAMP,EAAMC,OAAOC,MAAM,CAAC,oBACtBF,GACFrD,EADO,KACK,GAAGsB,KAAK,GAAGuC,QAAQ,CAAC,CAC9B/N,IAAKuN,CACP,GAAG7B,GAAG,EAEV,EAwF2D3N,sBAAoB,SAASH,0BAAwB,6BACxG,UAACoQ,EAAAA,CAASA,CAAAA,CAACtQ,UAAU,UAAUK,sBAAoB,YAAYH,0BAAwB,wBAEzF,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QA5EvB,CA4EgCmP,IA3E/C/D,EAAOqB,KAAK,GAAGC,KAAK,GAAG0C,WAAW,CAAC,CACjCrO,KAAM,EACNsO,KAAM,EACNC,eAAe,CACjB,GAAG1C,GAAG,EACR,EAsE2D3N,sBAAoB,SAASH,0BAAwB,6BACxG,UAACyQ,GAAAA,CAASA,CAAAA,CAAC3Q,UAAU,UAAUK,sBAAoB,YAAYH,0BAAwB,2BAK3F,UAACH,MAAAA,CAAIC,UAAU,kCACb,UAAC4Q,EAAAA,EAAaA,CAAAA,CAACpE,OAAQA,EAAQxM,UAAU,gBAAgBK,sBAAoB,gBAAgBH,0BAAwB,2BA3GlH,IA8GX,EAAE,SCvIc2Q,GAAqB,gBACnCC,CAAc,iBACdC,CAAe,aACfC,GAAc,CAAI,aAClBxQ,CAAW,aACXyQ,CAAW,CACe,EAC1B,GAAM,CAAClI,EAASmI,EAAW,CAAG3R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiBuR,GACjD,CAACK,EAAgBC,EAAkB,CAAG7R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,CAAC8R,EAAkBC,EAAoB,CAAG/R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA+C,SACjG,CAACgS,EAASC,EAAW,CAAGjS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAIjCkS,EAAW,IAMf,IAAMC,EAAiB,IAAI3I,EALI,CAC7BxI,GAAI,CAAC,MAAM,EAAE4F,KAAK2B,GAAG,GAAG,CAAC,EAAEvG,KAAKE,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAAI,MACvET,EACAT,MAAO,EACT,EAC6C,CAC7CyQ,EAAWQ,GACXX,EAAgBW,EAClB,EACMC,EAAqB,IACzBL,EAAoBpQ,GACpBkQ,EAAkB,IAClBI,EAAW,GACb,EAsBMI,EAAc,CAACrR,EAAYsR,KAC/B,IAAMH,EAAiB3I,EAAQL,GAAG,CAACoJ,GAASA,EAAMvR,EAAE,GAAKA,EAAK,CAC5D,GAAGuR,CAAK,CACRrR,MAAOoR,CACT,EAAIC,GACJZ,EAAWQ,GACXX,EAAgBW,EAClB,EACMK,EAAc,IAClB,IAAML,EAAiB3I,EAAQP,MAAM,CAACsJ,GAASA,EAAMvR,EAAE,GAAKA,GAC5D2Q,EAAWQ,GACXX,EAAgBW,EAClB,EACMM,EAAmBC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,MAAO1O,EAAe2O,EAAiBC,KAC1E,GAAI,CAAC5O,GAA0B,IAAjBA,EAAM0E,MAAM,CAAQ,YAChCrG,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,gCAGd,IAAMH,EAAOC,CAAK,CAAC,EAAE,CACrB3B,EAAAA,EAAKA,CAACwQ,IAAI,CAAC,CAAC,UAAU,EAAE9O,EAAK5C,IAAI,CAAC,GAAG,CAAC,EACtC,GAAI,CACF,IAAM2R,EAAW,MAAMC,MAAM,CAAC,qBAAqB,EAAEhP,EAAK5C,IAAI,EAAE,CAAE,CAChE6R,OAAQ,OACRC,KAAMlP,CACR,GACA,GAAI,CAAC+O,EAASI,EAAE,CACd,CADgB,KACV,MAAU,CAAC,eAAe,EAAEJ,EAASK,UAAU,EAAE,EAEzD,IAAMC,EAAU,MAAMN,EAASO,IAAI,GACnChB,EAAYM,EAASS,EAAQ9C,GAAG,EAChCjO,EAAAA,EAAKA,CAACC,OAAO,CAAC,GAAGsQ,EAASU,MAAM,CAAC,GAAG7R,WAAW,GAAKmR,EAASW,KAAK,CAAC,GAAG,uBAAuB,CAAC,CAChG,CAAE,MAAOrP,EAAO,CACdsP,QAAQtP,KAAK,CAAC,CAAC,gBAAgB,EAAE0O,EAAS,CAAC,CAAC,CAAE1O,GAC9C7B,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,CAAC,iBAAiB,EAAE0O,EAAS,EAAE,EAAE,EAAiBa,OAAO,EAAE,CACzE,CACF,EAAG,CAACpB,EAAY,EAChB,MAAO,WAAC7R,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,uBAAuBC,0BAAwB,uCAClG6I,EAAQL,GAAG,CAAC,CAACoJ,EAAOnJ,IAAU,WAACmB,EAAAA,EAAIA,CAAAA,CAAgB9J,UAAU,gCAAgCkD,IAAK+P,IAC/FhC,IACFA,EAAYnO,OADG,CACKgP,EAAMvR,EAAE,EAAI,CAAC,MAAM,EAAEoI,EAAAA,CAAO,CAAC,CAAGsK,CAAAA,CAExD,EAAG1S,GAAIuR,EAAMvR,EAAE,EAAI,CAAC,MAAM,EAAEoI,EAAAA,CAAO,WACb,SAAfmJ,EAAM5Q,IAAI,CAAc,UAACnB,MAAAA,CAAIC,UAAU,0BACpC,UAACuM,GAAYA,CAACxD,QAADwD,EAAgB9L,KAAK,CAAEE,SAAU,GAAqBiR,EAAYE,EAAMvR,EAAE,CAAEwI,GAAUvI,YAAY,mCACzF,UAAfsR,EAAM5Q,IAAI,CAAe,UAACnB,MAAAA,CAAIC,UAAU,qBAC9C8R,EAAMrR,KAAK,CAAG,UAACV,MAAAA,CAAIC,UAAU,kEAC1B,UAACmN,EAAAA,OAAKA,CAAAA,CAAC7K,IAAKwP,EAAMrR,KAAK,CAAE8B,IAAI,mBAAmB2Q,OAAO,OAAOC,UAAU,UAAUnT,UAAU,iBACrF,WAACD,MAAAA,CAAIC,UAAU,sBACtB,UAACD,MAAAA,CAAIC,UAAU,8CAAqC,mCACpD,WAACD,MAAAA,CAAIC,UAAU,uBACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAM6B,EAAQmQ,SAASC,aAAa,CAAC,SACrCpQ,EAAM/B,IAAI,CAAG,OACb+B,EAAME,MAAM,CAAG,UACfF,EAAMqQ,QAAQ,CAAG1S,IACf,IAAM2C,EAAQ,EAAG1C,MAAM,CAAsB0C,KAAK,CAC9CA,GAAOyO,EAAiBuB,MAAMC,IAAI,CAACjQ,GAAQuO,EAAMvR,EAAE,CAAE,QAC3D,EACA0C,EAAMF,KAAK,EACb,YACU,UAACC,EAAAA,CAAMA,CAAAA,CAAChD,UAAU,iBAAiB,iBAGrC,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAMyO,EAAME,OAAO,wBACfF,GAAK+B,EAAYE,EAAMvR,EAAE,CAAEsP,EACjC,YACU,UAAC9C,EAAAA,CAAIA,CAAAA,CAAC/M,UAAU,iBAAiB,uBAKnB,YAATkB,IAAI,CAAe,UAACnB,MAAAA,CAAIC,UAAU,qBAC9C8R,EAAMrR,KAAK,CAAG,UAACgT,QAAAA,CAAMC,QAAQ,IAACpR,IAAKwP,EAAMrR,KAAK,CAAET,UAAU,sCAAyC,WAACD,MAAAA,CAAIC,UAAU,sBAC/G,UAACD,MAAAA,CAAIC,UAAU,8CAAqC,kCACpD,WAACD,MAAAA,CAAIC,UAAU,uBACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAM6B,EAAQmQ,SAASC,aAAa,CAAC,SACrCpQ,EAAM/B,IAAI,CAAG,OACb+B,EAAME,MAAM,CAAG,UACfF,EAAMqQ,QAAQ,CAAG1S,IACf,IAAM2C,EAAQ,EAAG1C,MAAM,CAAsB0C,KAAK,CAC9CA,GAAOyO,EAAiBuB,MAAMC,IAAI,CAACjQ,GAAQuO,EAAMvR,EAAE,CAAE,QAC3D,EACA0C,EAAMF,KAAK,EACb,YACU,UAACC,EAAAA,CAAMA,CAAAA,CAAChD,UAAU,iBAAiB,iBAGrC,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAMyO,EAAME,OAAO,uBACfF,GAAK+B,EAAYE,EAAMvR,EAAE,CAAEsP,EACjC,YACU,UAAC9C,EAAAA,CAAIA,CAAAA,CAAC/M,UAAU,iBAAiB,uBAKnB,QAAf8R,EAAM5Q,IAAI,CAAa,UAACnB,MAAAA,CAAIC,UAAU,qBAC5C8R,EAAMrR,KAAK,CAAG,UAACkT,SAAAA,CAAOrR,IAAKwP,EAAMrR,KAAK,CAAET,UAAU,2BAA8B,WAACD,MAAAA,CAAIC,UAAU,sBAC5F,UAACD,MAAAA,CAAIC,UAAU,8CAAqC,gCACpD,WAACD,MAAAA,CAAIC,UAAU,uBACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAM6B,EAAQmQ,SAASC,aAAa,CAAC,SACrCpQ,EAAM/B,IAAI,CAAG,OACb+B,EAAME,MAAM,CAAG,kBACfF,EAAMqQ,QAAQ,CAAG1S,IACf,IAAM2C,EAAQ,EAAG1C,MAAM,CAAsB0C,KAAK,CAC9CA,GAAOyO,EAAiBuB,MAAMC,IAAI,CAACjQ,GAAQuO,EAAMvR,EAAE,CAAE,MAC3D,EACA0C,EAAMF,KAAK,EACb,YACU,UAACC,EAAAA,CAAMA,CAAAA,CAAChD,UAAU,iBAAiB,iBAGrC,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAMyO,EAAME,OAAO,qBACfF,GAAK+B,EAAYE,EAAMvR,EAAE,CAAEsP,EACjC,YACU,UAAC9C,EAAAA,CAAIA,CAAAA,CAAC/M,UAAU,iBAAiB,uBAKnB,mBAAf8R,EAAM5Q,IAAI,CAAwB,UAACnB,MAAAA,CAAIC,UAAU,qBACvD8R,EAAMrR,KAAK,CAAG,UAACkT,SAAAA,CAAOrR,IAAKwP,EAAMrR,KAAK,CAAET,UAAU,2BAA8B,WAACD,MAAAA,CAAIC,UAAU,sBAC5F,UAACD,MAAAA,CAAIC,UAAU,8CAAqC,2CACpD,WAACD,MAAAA,CAAIC,UAAU,uBACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAM6B,EAAQmQ,SAASC,aAAa,CAAC,SACrCpQ,EAAM/B,IAAI,CAAG,OACb+B,EAAME,MAAM,CAAG,UACfF,EAAMqQ,QAAQ,CAAG1S,IACf,IAAM2C,EAAQ,EAAG1C,MAAM,CAAsB0C,KAAK,CAC9CA,GAAOyO,EAAiBuB,MAAMC,IAAI,CAACjQ,GAAQuO,EAAMvR,EAAE,CAAE,iBAC3D,EACA0C,EAAMF,KAAK,EACb,YACU,UAACC,EAAAA,CAAMA,CAAAA,CAAChD,UAAU,iBAAiB,iBAGrC,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KACnD,IAAMyO,EAAME,OAAO,gCACfF,GAAK+B,EAAYE,EAAMvR,EAAE,CAAEsP,EACjC,YACU,UAAC9C,EAAAA,CAAIA,CAAAA,CAAC/M,UAAU,iBAAiB,uBAKlC,UAACiC,EAAAA,CAAQA,CAAAA,CAACzB,YAAa,CAAC,MAAM,EAAEsR,EAAM5Q,IAAI,CAAC,IAAI,CAAC,CAAET,MAAOqR,EAAMrR,KAAK,CAAEE,SAAUC,GAAKgR,EAAYE,EAAMvR,EAAE,CAAEK,EAAEC,MAAM,CAACJ,KAAK,EAAG0B,KAAM,IAC7I,UAAClB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,OAAOxC,UAAU,sEAAsEoB,QAAS,IAAM2Q,EAAYD,EAAMvR,EAAE,WACrJ,UAACoK,EAAAA,CAAMA,CAAAA,CAAC3K,UAAU,gBAnHkB8R,EAAMvR,EAAE,GAsHlD,WAACR,MAAAA,CAAIC,UAAU,sCACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMqQ,EAAS,QAASjP,KAAK,KAAKnC,sBAAoB,SAASH,0BAAwB,uCACxH,UAAC0T,EAAAA,CAAQA,CAAAA,CAAC5T,UAAU,eAAeK,sBAAoB,WAAWH,0BAAwB,+BAA+B,qBAE1H8Q,GAAe,iCACZ,WAAC/P,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMuQ,EAAmB,SAAUnP,KAAK,eACzE,UAAC8N,EAAAA,CAASA,CAAAA,CAACtQ,UAAU,iBAAiB,sBAExC,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMuQ,EAAmB,SAAUnP,KAAK,eACzE,UAACqR,EAAAA,CAAeA,CAAAA,CAAC7T,UAAU,iBAAiB,sBAE9C,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMuQ,EAAmB,OAAQnP,KAAK,eACvE,UAACsR,EAAAA,CAAYA,CAAAA,CAAC9T,UAAU,iBAAiB,oBAE3C,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMuQ,EAAmB,kBAAmBnP,KAAK,eAClF,UAACuR,EAAAA,CAASA,CAAAA,CAAC/T,UAAU,iBAAiB,qCAM9C,UAACuL,EAAAA,EAAMA,CAAAA,CAACC,KAAM2F,EAAgB1F,aAAc2F,EAAmB/Q,sBAAoB,SAASH,0BAAwB,sCAClH,WAACwL,EAAAA,EAAaA,CAAAA,CAACrL,sBAAoB,gBAAgBH,0BAAwB,uCACzE,WAACyL,EAAAA,EAAYA,CAAAA,CAACtL,sBAAoB,eAAeH,0BAAwB,uCACvE,WAAC0L,EAAAA,EAAWA,CAAAA,CAACvL,sBAAoB,cAAcH,0BAAwB,uCAA6B,UAAQmR,EAAiBwB,MAAM,CAAC,GAAG7R,WAAW,GAAKqQ,EAAiByB,KAAK,CAAC,GAAG,YACjL,WAACjH,EAAAA,EAAiBA,CAAAA,CAACxL,sBAAoB,oBAAoBH,0BAAwB,uCAA6B,0BACtFmR,EAAiB,0CAG7C,UAACtR,MAAAA,CAAIC,UAAU,qBACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,WAAWC,sBAAoB,QAAQH,0BAAwB,uCAA6B,OAAKmR,EAAiBwB,MAAM,CAAC,GAAG7R,WAAW,GAAKqQ,EAAiByB,KAAK,CAAC,MAClL,UAACxS,EAAAA,CAAKA,CAAAA,CAACC,GAAG,WAAWC,YAAa,CAAC,aAAa,EAAE6Q,EAAiB,GAAG,CAAC,CAAE5Q,MAAO8Q,EAAS5Q,SAAUC,GAAK4Q,EAAW5Q,EAAEC,MAAM,CAACJ,KAAK,EAAGJ,sBAAoB,QAAQH,0BAAwB,oCAG5L,WAAC6L,EAAAA,EAAYA,CAAAA,CAAC/L,UAAU,aAAaK,sBAAoB,eAAeH,0BAAwB,uCAC9F,WAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAnMR,CAmMiB4S,IAlM3CvC,EAASJ,GACTD,GAAkB,EACpB,EAgMkE/Q,sBAAoB,SAASH,0BAAwB,uCAC3G,UAAC8C,EAAAA,CAAMA,CAAAA,CAAChD,UAAU,eAAeK,sBAAoB,SAASH,0BAAwB,+BAA+B,iBAGvH,WAACe,EAAAA,CAAMA,CAAAA,CAACG,QAxNQ,CAwNC6S,IAvNzB,GAAI1C,EAAQtF,IAAI,GAAI,CAMlB,IAAMyF,EAAiB,IAAI3I,EALI,CAC7BxI,GAAI,CAAC,MAAM,EAAE4F,KAAK2B,GAAG,GAAG,CAAC,EAAEvG,KAAKE,MAAM,GAAGC,QAAQ,CAAC,IAAIC,SAAS,CAAC,EAAG,IAAI,CACvET,KAAMmQ,EACN5Q,MAAO8Q,EAAQtF,IAAI,EACrB,EAC6C,CAC7CiF,EAAWQ,GACXX,EAAgBW,GAChBN,GAAkB,GAClBI,EAAW,IACX5P,EAAAA,EAAKA,CAACC,OAAO,CAAC,wCAChB,MACED,CADK,CACLA,EAAKA,CAAC6B,KAAK,CAAC,kCAEhB,EAwM8C3B,SAAU,CAACyP,EAAQtF,IAAI,GAAI5L,sBAAoB,SAASH,0BAAwB,uCAClH,UAAC6M,EAAAA,CAAIA,CAAAA,CAAC/M,UAAU,eAAeK,sBAAoB,OAAOH,0BAAwB,+BAA+B,4BAO/H,iHCjQO,SAASgU,GAAoB,MAClC/U,CAAI,UACJC,CAAQ,CACiB,EACzB,GAAM,CAAC+U,EAAgBC,EAAkB,CAAG7U,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASJ,EAAK6I,OAAO,CAAC,EAAE,EAAEzH,IAAM,IAC9E,CAAC8T,EAAiBC,EAAmB,CAAG/U,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzD,CAACgV,EAAaC,EAAe,CAAGjV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAG3C,CACD2B,KAAM,UACNuT,KAAM,IACR,GACM,CAACC,EAAkBC,EAAoB,CAAGpV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACqV,EAAiBC,EAAmB,CAAGtV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAsB,MACtE,CAACuV,EAAsBC,EAAwB,CAAGxV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3D,CAACyV,EAAaC,EAAe,CAAG1V,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAGzC0R,EAAcnR,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAEvB,CAAC,GACEoV,EAAgB/V,EAAK6I,OAAO,CAACa,IAAI,CAACJ,GAAKA,EAAElI,EAAE,GAAK4T,GAChDgB,EAAiBD,GAAehN,SAASW,KAAKQ,GAAKA,EAAE9I,EAAE,GAAK8T,GAG5De,EAAkB,IACtB,IAAMC,EAAUpE,EAAYnO,OAAO,CAACoP,EAAQ,IAE1CmD,EAAQC,GADG,WACW,CAAC,CACrBC,SAAU,SACVzD,MAAO,QACP0D,OAAQ,SACV,EAEJ,EAGMC,EAAqB,IACzB,OAAQvU,GACN,IAAK,OACH,MAAO,UAACwU,GAAAA,CAAIA,CAAAA,CAAC1V,UAAU,WACzB,KAAK,QACH,MAAO,UAACmN,EAAAA,CAAKA,CAAAA,CAACnN,UAAU,WAC1B,KAAK,QAIL,IAAK,iBAHH,MAAO,UAAC2V,EAAAA,CAAKA,CAAAA,CAAC3V,UAAU,WAC1B,KAAK,MACH,MAAO,UAACqL,EAAAA,CAAQA,CAAAA,CAACrL,UAAU,WAG7B,SACE,MAAO,UAAC4V,GAAAA,CAAQA,CAAAA,CAAC5V,UAAU,WAC/B,CACF,EAGM6V,EAAoB,GACxB,QAA2B,CAAvB/D,EAAM5Q,IAAI,CACL4Q,EAAMrR,KAAK,EAAEqS,MAAM,EAAG,KAAOhB,CAAAA,CAAMrR,KAAK,EAAIqR,EAAMrR,KAAK,CAACwH,MAAM,CAAG,GAAK,MAAQ,GAAC,EAAM,aAEvF6J,EAAM5Q,IAAI,CAAC2R,MAAM,CAAC,GAAG7R,WAAW,GAAK8Q,EAAM5Q,IAAI,CAAC4R,KAAK,CAAC,GA0BzDgD,EAAc5U,IAClB,IAAM6U,EAAoB,CACxBxV,GAAI,CAAC,KAAK,EAAE4F,KAAK2B,GAAG,IAAI,CACxBpH,KAAe,YAATQ,EAAqB,CAAC,KAAK,EAAEiU,GAAgBzU,KAAAA,CAAM,CAAGQ,aAAoB,CAAC,KAAK,EAAEgU,GAAexU,KAAAA,CAAM,CAAG,CAAC,aAAa,EAAEvB,EAAKuB,IAAI,EAAE,CAC3IwB,YAAa,GACb8T,UAAW,EAAE,CACbC,aAAc,GACdC,UAAoB,UAAThV,EAAmB,SAAM0B,CACtC,EACA4R,EAAe,KAFiC,CAG9CtT,EACAuT,KAAMsB,CACR,GACApB,GAAoB,EACtB,EACMwB,EAAW,CAACjV,EAAsCuT,KACtDD,EAAe,EATkE,IAU/EtT,EACAuT,KAAM,CACJ,GAAGA,CAAI,CAEX,GACAE,GAAoB,EACtB,EAoGMyB,EAAe,IACnBvB,EAAmB,CACjB,GAAGwB,CACL,GACAtB,EAAwB,GAC1B,EAyBMuB,EAAkBC,IACtB,GAAI,CAAChC,EAAYE,IAAI,CAAE,OACvB,IAAM+B,EAAmBjC,EAAYE,IAAI,CAACuB,SAAS,CAACxN,MAAM,CAACiO,GAAKA,EAAElW,EAAE,GAAKgW,GAAY7N,GAAG,CAAC,CAAC+N,EAAG9N,IAAW,EACtG,EADsG,CACnG8N,CAAC,CACJ1O,WAAYY,EACd,GACA6L,EAAe1I,GAAS,EACtB,EADsB,CACnBA,CAAI,CACP2I,KAAM3I,EAAK2I,IAAI,CAAG,CAChB,GAAG3I,EAAK2I,IAAI,CACZuB,UAAWQ,CACb,EAAI,KACN,GACA5U,EAAAA,EAAKA,CAACC,OAAO,CAAC,8BAChB,EAUM6U,EAAmBC,CATG,KAC1B,IAAMC,EAAgBzX,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACD,MAAM,CAAE,GACnF6O,EAAoB3X,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACM,MAAM,CAACtB,GAAWA,EAAQ6B,OAAO,EAAI7B,EAAQ6B,OAAO,CAACd,MAAM,CAAG,GAAGA,MAAM,CAAE,GAC9J,MAAO,CACL8O,MAAOH,EACPI,UAAWF,EACXG,WAAYL,EAAgB,EAAIrV,KAAK2V,KAAK,CAACJ,EAAoBF,EAAgB,KAAO,CACxF,EACF,WAEA,GAA+B,CAA3BzX,EAAK6I,OAAO,CAACC,MAAM,CACd,WAAClI,MAAAA,CAAIC,UAAU,8BAClB,UAACgK,EAAAA,CAAQA,CAAAA,CAAChK,UAAU,iDACpB,UAAC4J,KAAAA,CAAG5J,UAAU,sCAA6B,oBAC3C,UAACgC,IAAAA,CAAEhC,UAAU,iCAAwB,kFAKpC,WAACD,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,sBAAsBC,0BAAwB,sCAElG,WAACH,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,UAAC6J,KAAAA,CAAG5J,UAAU,iCAAwB,qBACtC,UAACgC,IAAAA,CAAEhC,UAAU,yCAAgC,sDAI/C,WAACD,MAAAA,CAAIC,UAAU,wCACb,WAACD,MAAAA,CAAIC,UAAU,uBACb,WAACD,MAAAA,CAAIC,UAAU,gCACZ0W,EAAiBM,SAAS,CAAC,MAAIN,EAAiBK,KAAK,CAAC,cAEzD,WAAChX,MAAAA,CAAIC,UAAU,0CACZ0W,EAAiBO,UAAU,CAAC,kBAGjC,UAAClX,MAAAA,CAAIC,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0DAA2F,MAAhCgR,EAAiBO,UAAU,CAAW,8BAAgC,2CACjJP,QAAiBO,UAAU,CAAW,UAACE,GAAAA,CAAWA,CAAAA,CAACnX,UAAU,YAAe,UAACoX,GAAAA,CAAKA,CAAAA,CAACpX,UAAU,oBAKpG,WAACD,MAAAA,CAAIC,UAAU,kDAEb,UAACD,MAAAA,CAAIC,UAAU,yBACb,WAAC8J,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOH,0BAAwB,sCACvD,UAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,qCACnE,UAACmK,EAAAA,EAASA,CAAAA,CAACrK,UAAU,YAAYK,sBAAoB,YAAYH,0BAAwB,qCAA4B,sBAEvH,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,yCAAyCK,sBAAoB,cAAcH,0BAAwB,sCAExH,UAACH,MAAAA,CAAIC,UAAU,qBACb,WAACD,MAAAA,CAAIC,UAAU,iFACb,WAACD,MAAAA,CAAIC,UAAU,mDACb,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,4CAAmC,eAClD,UAACD,MAAAA,CAAIC,UAAU,yCAAgC,wCAIhDb,EAAKkY,SAAS,EAAI,UAACF,GAAAA,CAAWA,CAAAA,CAACnX,UAAU,8BAE5C,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAShC,EAAKkY,SAAS,CAAG,UAAY,UAAW7U,KAAK,KAAKxC,UAAU,SAASoB,QAAS,KAC3FjC,EAAKkY,SAAS,CAChBlB,CADkB,CACT,QAAShX,EAAKkY,SAAS,EAEhCvB,EAAW,QAEf,EAAGzV,sBAAoB,SAASH,0BAAwB,sCACpD,UAACqK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,eAAeK,sBAAoB,aAAaH,0BAAwB,8BAC7Ff,EAAKkY,SAAS,CAAG,kBAAoB,0BAM3ClY,EAAK6I,OAAO,CAACU,GAAG,CAACmO,GAAU,WAAC9W,MAAAA,CAAoBC,UAAU,sBACvD,UAACD,MAAAA,CAAIC,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kDAAmDyO,IAAmB0C,EAAOtW,EAAE,CAAG,qCAAuC,8BAA+Ba,QAAS,KACtLgT,EAAkByC,EAAOtW,EAAE,EAC3B+T,EAAmB,GACrB,WACM,WAACvU,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,+BAAuB6W,EAAOnW,IAAI,GACjD,WAACX,MAAAA,CAAIC,UAAU,+BACZ6W,EAAO3O,QAAQ,CAACD,MAAM,CAAC,kBAG3B4O,EAAOS,UAAU,EAAI,WAAClT,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,YAAYnB,UAAU,oBACvD,UAACuK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,eAM9CmU,IAAmB0C,EAAOtW,EAAE,EAAI,WAACR,MAAAA,CAAIC,UAAU,2BAE5C,UAACD,MAAAA,CAAIC,UAAU,uCACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAS0V,EAAOS,UAAU,CAAG,UAAY,YAAa9U,KAAK,KAAKxC,UAAU,iBAAiBoB,QAAS,KAC5GyV,EAAOS,UAAU,CACnBnB,CADqB,CACZ,SAAUU,EAAOS,UAAU,EAEpCxB,EAAW,SAEf,YACQ,UAACvL,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBACrB6W,EAAOS,UAAU,CAAG,mBAAqB,wBAK7CT,EAAO3O,QAAQ,CAACQ,GAAG,CAACxB,IACzB,IAAMqQ,EAAarQ,EAAQ6B,OAAO,EAAI7B,EAAQ6B,OAAO,CAACd,MAAM,CAAG,EAC/D,MAAO,WAAClI,MAAAA,CAAqBC,UAAU,sBAC7B,WAACD,MAAAA,CAAIC,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yFAA0F2O,IAAoBnN,EAAQ3G,EAAE,CAAG,6BAA+B,qBAAsBa,QAAS,IAAMkT,EAAmBpN,EAAQ3G,EAAE,YAC7O,UAACsE,OAAAA,UAAMqC,EAAQxG,IAAI,GAClB6W,GAAc,UAACJ,GAAAA,CAAWA,CAAAA,CAACnX,UAAU,8BAIvCqU,IAAoBnN,EAAQ3G,EAAE,EAAIgX,GAAcrQ,EAAQ6B,OAAO,EAAI,WAAChJ,MAAAA,CAAIC,UAAU,2BAC/E,WAACD,MAAAA,CAAIC,UAAU,2EACb,UAACwX,GAAAA,CAAUA,CAAAA,CAACxX,UAAU,YACtB,UAAC6E,OAAAA,UAAK,sBAEPqC,EAAQ6B,OAAO,CAACL,GAAG,CAAC,CAACoJ,EAAOnJ,IAAU,WAAC8O,SAAAA,CAA+BrW,QAAS,IAAMgU,EAAgBtD,EAAMvR,EAAE,EAAI,CAAC,MAAM,EAAEoI,EAAAA,CAAO,EAAG3I,UAAU,qHAC1IyV,EAAmB3D,EAAM5Q,IAAI,EAC9B,WAAC2D,OAAAA,CAAK7E,UAAU,4BACb2I,EAAQ,EAAE,KAAGkN,EAAkB/D,QAHcA,EAAMvR,EAAE,EAAIoI,SAZ7DzB,EAAQ3G,EAAE,CAoB7B,QAzDoCsW,EAAOtW,EAAE,WAiErD,UAACR,MAAAA,CAAIC,UAAU,yBACZ,EA+FU,WAACD,MAAAA,CAAIC,UAAU,sBAEtB,UAAC8J,EAAAA,EAAIA,CAAAA,UACH,UAACK,EAAAA,EAAUA,CAAAA,UACT,WAACpK,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,WAACsK,EAAAA,EAASA,CAAAA,CAACrK,UAAU,wCACnB,UAAC6E,OAAAA,UAAMsQ,GAAgBzU,OACtByU,GAAgBnM,gBAAkB,WAAC5E,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,sBAC9C,UAACoJ,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,kBAG1CkV,GAAeoC,YAAc,WAAClT,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,oBACzC,UAACoJ,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,oBAI7C,WAACsK,EAAAA,EAAeA,CAAAA,WAAC,UACP4K,GAAexU,KACtBwU,GAAeoC,YAAc,UAACzS,OAAAA,CAAK7E,UAAU,qCAA4B,qCAK9E,WAACD,MAAAA,CAAIC,UAAU,wCACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,IAAM6T,EAAe,CAACD,aACjE,UAAC0C,GAAAA,CAAGA,CAAAA,CAAC1X,UAAU,iBACdgV,EAAc,OAAS,aAEzBE,GAAeoC,YAAc,WAACrW,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,IAAM+U,EAAS,SAAUjB,EAAcoC,UAAU,YACxH,UAAC/M,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,sBAG1CmV,GAAgBnM,gBAAkB,WAAC/H,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUqB,KAAK,KAAKpB,QAAS,KAC9E+T,EAAewC,WAAW,CAC5BxB,CAD8B,CACrB,UAAWhB,EAAewC,WAAW,EAE9C7B,EAAW,UAEf,YACQ,UAACvL,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBACrBmV,EAAewC,WAAW,CAAG,oBAAsB,iCAQhE,WAAC7N,EAAAA,EAAIA,CAAAA,WACH,WAACK,EAAAA,EAAUA,CAAAA,WACT,UAACE,EAAAA,EAASA,CAAAA,CAACrK,UAAU,qBAAY,mBACjC,UAACsK,EAAAA,EAAeA,CAAAA,UACb0K,EAAc,iDAAmD,+CAGtE,UAACjL,EAAAA,EAAWA,CAAAA,UACTiL,EAAc,UAACjV,MAAAA,CAAIC,UAAU,8DACzBmV,GAAgBpM,SAAWoM,EAAepM,OAAO,CAACd,MAAM,CAAG,EAAI,+BAC3DkN,EAAepM,OAAO,CAACL,GAAG,CAAC,CAACoJ,EAAYnJ,IAAkB,WAAC5I,MAAAA,CAA4BmD,IAAK+P,IACnGhC,EAAYnO,OAAO,CAACgP,EAAMvR,EAAE,EAAI,CAAC,MAAM,EAAEoI,EAAAA,CAAO,CAAC,CAAGsK,CACtD,EAAGjT,UAAU,mBAAmBO,GAAIuR,EAAMvR,EAAE,EAAI,CAAC,MAAM,EAAEoI,EAAAA,CAAO,WAEpD,WAAC5I,MAAAA,CAAIC,UAAU,yGACZyV,EAAmB3D,EAAM5Q,IAAI,EAC9B,WAAC2D,OAAAA,WAAK,WAAS8D,EAAQ,KACvB,WAAC9D,OAAAA,WAAK,IAAEiN,EAAM5Q,IAAI,CAAC,UAGL,SAAf4Q,EAAM5Q,IAAI,CAAc,UAAC0W,GAAAA,EAAaA,CAAAA,CAACC,cAAe,CAACC,GAAAA,CAASA,CAAC,CAAEC,WAAY,CACxFC,GAAI,CAAC,MACHC,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAAC0R,KAAAA,CAAGhY,UAAU,wCAAyC,GAAGsG,CAAK,GACrE4R,GAAI,CAAC,MACHD,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAAC4R,KAAAA,CAAGlY,UAAU,2CAA4C,GAAGsG,CAAK,GACxEsD,GAAI,CAAC,MACHqO,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACsD,KAAAA,CAAG5J,UAAU,2CAA4C,GAAGsG,CAAK,GACxEnC,GAAI,CAAC,MACH8T,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACnC,KAAAA,CAAGnE,UAAU,6CAA8C,GAAGsG,CAAK,GAC1EtE,EAAG,CAAC,MACFiW,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACtE,IAAAA,CAAEhC,UAAU,uBAAwB,GAAGsG,CAAK,GACnDjC,GAAI,CAAC,MACH4T,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACjC,KAAAA,CAAGrE,UAAU,sBAAuB,GAAGsG,CAAK,GACnD6R,GAAI,CAAC,MACHF,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAAC6R,KAAAA,CAAGnY,UAAU,yBAA0B,GAAGsG,CAAK,GACtDhC,GAAI,CAAC,MACH2T,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAAChC,KAAAA,CAAGtE,UAAU,OAAQ,GAAGsG,CAAK,GACpC8R,WAAY,CAAC,MACXH,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAAC8R,aAAAA,CAAWpY,UAAU,wEAAyE,GAAGsG,CAAK,GAC7G9E,KAAM,CAAC,CACLyW,MAAI,WACJjY,CAAS,UACTqY,CAAQ,CACR,GAAG/R,EACJ,GACe,iBAAiBgS,IAAI,CAACtY,GAAa,IAIvB,UAACwB,OAAAA,CAAKxB,UAAU,gFAAiF,GAAGsG,CAAK,UAChH+R,IAHD,UAAC7W,OAAAA,CAAKxB,UAAU,oDAAqD,GAAGsG,CAAK,UAC5E+R,IAKrBE,IAAK,CAAC,MACJN,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACiS,MAAAA,CAAIvY,UAAU,OAAQ,GAAGsG,CAAK,GACrCkS,MAAO,CAAC,MACNP,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACvG,MAAAA,CAAIC,UAAU,gCACH,UAACwY,QAAAA,CAAMxY,UAAU,4CAA6C,GAAGsG,CAAK,KAExFmS,MAAO,CAAC,MACNR,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACmS,QAAAA,CAAMzY,UAAU,aAAc,GAAGsG,CAAK,GAC7CoS,GAAI,CAAC,MACHT,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACoS,KAAAA,CAAG1Y,UAAU,2DAA4D,GAAGsG,CAAK,GACxFqS,GAAI,CAAC,MACHV,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACqS,KAAAA,CAAG3Y,UAAU,mCAAoC,GAAGsG,CAAK,GAChEsS,GAAI,CAAC,MACHX,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACsS,KAAAA,CAAG5Y,UAAU,uBAAwB,GAAGsG,CAAK,GACpDuS,OAAQ,CAAC,MACPZ,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACuS,SAAAA,CAAO7Y,UAAU,8BAA+B,GAAGsG,CAAK,GAC/DwS,GAAI,CAAC,MACHb,CAAI,CACJ,GAAG3R,EACJ,GAAK,UAACwS,KAAAA,CAAG9Y,UAAU,SAAU,GAAGsG,CAAK,EACxC,WACewL,EAAMrR,KAAK,GACoB,UAAfqR,EAAM5Q,IAAI,CAAe,UAACnB,MAAAA,CAAIC,UAAU,gBACzD,UAACqC,MAAAA,CAAIC,IAAKwP,EAAMrR,KAAK,CAAE8B,IAAI,UAAUvC,UAAU,mCACzB,UAAf8R,EAAM5Q,IAAI,CAAe,UAACnB,MAAAA,CAAIC,UAAU,gBAC/C,UAACyT,QAAAA,CAAMnR,IAAKwP,EAAMrR,KAAK,CAAEiT,QAAQ,IAAC1T,UAAU,4BACtB,QAAf8R,EAAM5Q,IAAI,CAAa,UAACnB,MAAAA,CAAIC,UAAU,gBAC7C,UAAC2T,SAAAA,CAAOrR,IAAKwP,EAAMrR,KAAK,CAAET,UAAU,yBAAyB+Y,MAAM,kBAC5D,UAAChZ,MAAAA,CAAIC,UAAU,wCACtB,WAACgC,IAAAA,CAAEhC,UAAU,0CACK,mBAAf8R,EAAM5Q,IAAI,CAAwB,mBAAqB,SACxD,UAAC8X,IAAAA,CAAEpJ,KAAMkC,EAAMrR,KAAK,CAAEI,OAAO,SAASoY,IAAI,sBAAsBjZ,UAAU,wCACvE8R,EAAMrR,KAAK,UA3G6CqR,EAAMvR,EAAE,EAAIoI,MAgH7E,UAAC3G,IAAAA,CAAEhC,UAAU,wCAA+B,yCAG7C,WAACD,MAAAA,CAAIC,UAAU,sBACtB,UAACD,MAAAA,CAAIC,UAAU,6CACb,UAAC6Q,GAAoBA,CAACC,eAAgBqE,CAAjBtE,EAAiC9H,SAAW,EAAE,CAAEgI,gBA9lB9D,CA8lB+EmI,GA7lBrGhE,GAAkBC,GAmBvB/V,EAAS,CACP4I,QApBoB,CAoBXO,CApB4B,OACJ,CAACG,GAAG,CAACmO,IACtC,GAAIA,EAAOtW,EAAE,GAAK4T,EAAgB,CAChC,IAAM/K,EAAkByN,EAAO3O,QAAQ,CAACQ,GAAG,CAACxB,GAC1C,EAAY3G,EAAE,GAAK8T,EACV,CACL,GAAGnN,CAAO,SACV6B,CACF,EAEK7B,GAET,MAAO,CACL,GAAG2P,CAAM,CACT3O,SAAUkB,CACZ,CACF,CACA,OAAOyN,CACT,EAGA,EACF,EAukBkI5F,YAAaA,MAE3H,WAAClR,MAAAA,CAAIC,UAAU,4EACb,UAAC6E,OAAAA,UAAK,kCACN,WAACA,OAAAA,WACEsQ,GAAgBpM,SAASd,QAAU,EAAE,gCApRlC,WAAClI,MAAAA,CAAIC,UAAU,sBAE/B,WAAC8J,EAAAA,EAAIA,CAAAA,WACH,WAACK,EAAAA,EAAUA,CAAAA,WACT,WAACE,EAAAA,EAASA,CAAAA,CAACrK,UAAU,wCACnB,UAACuK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,yBACtB,UAAC6E,OAAAA,UAAK,eACL1F,EAAKkY,SAAS,EAAI,UAACjT,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,qBAAY,oBAEhD,UAACmJ,EAAAA,EAAeA,CAAAA,UAAC,gFAInB,UAACP,EAAAA,EAAWA,CAAAA,UACT5K,EAAKkY,SAAS,CAAG,WAACtX,MAAAA,CAAIC,UAAU,sBAC7B,WAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,gDACb,UAACD,MAAAA,CAAIC,UAAU,2CACZb,EAAKkY,SAAS,CAACrB,SAAS,CAAC/N,MAAM,GAElC,UAAClI,MAAAA,CAAIC,UAAU,yCAAgC,kBAEjD,WAACD,MAAAA,CAAIC,UAAU,gDACb,WAACD,MAAAA,CAAIC,UAAU,4CACZb,EAAKkY,SAAS,CAACpB,YAAY,CAAC,OAE/B,UAAClW,MAAAA,CAAIC,UAAU,yCAAgC,qBAEjD,WAACD,MAAAA,CAAIC,UAAU,gDACb,UAACD,MAAAA,CAAIC,UAAU,2CACZb,EAAKkY,SAAS,CAACrB,SAAS,CAAC3J,MAAM,CAAC,CAAC8M,EAAK1C,IAAM0C,EAAM1C,EAAE2C,MAAM,CAAE,KAE/D,UAACrZ,MAAAA,CAAIC,UAAU,yCAAgC,kBAEhDb,EAAKkY,SAAS,CAACnB,SAAS,EAAI,WAACnW,MAAAA,CAAIC,UAAU,gDACxC,UAACD,MAAAA,CAAIC,UAAU,2CACZb,EAAKkY,SAAS,CAACnB,SAAS,GAE3B,UAACnW,MAAAA,CAAIC,UAAU,yCAAgC,gBAGrD,UAACD,MAAAA,CAAIC,UAAU,0BACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACG,QAAS,IAAM+U,EAAS,QAAShX,EAAKkY,SAAS,EAAIrX,UAAU,mBACnE,UAACwK,EAAAA,CAAIA,CAAAA,CAACxK,UAAU,iBAAiB,0BAI9B,WAACD,MAAAA,CAAIC,UAAU,6BACtB,UAACuK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iDACtB,UAAC4J,KAAAA,CAAG5J,UAAU,sCAA6B,yBAC3C,UAACgC,IAAAA,CAAEhC,UAAU,sCAA6B,8FAG1C,WAACiB,EAAAA,CAAMA,CAAAA,CAACG,QAAS,IAAM0U,EAAW,mBAChC,UAACjM,EAAAA,CAAIA,CAAAA,CAAC7J,UAAU,iBAAiB,6BAQ3C,WAAC8J,EAAAA,EAAIA,CAAAA,WACH,WAACK,EAAAA,EAAUA,CAAAA,WACT,UAACE,EAAAA,EAASA,CAAAA,UAAC,mBACX,UAACC,EAAAA,EAAeA,CAAAA,UAAC,kFAInB,UAACP,EAAAA,EAAWA,CAAAA,UACV,UAAChK,MAAAA,CAAIC,UAAU,iDACZb,EAAK6I,OAAO,CAACU,GAAG,CAACmO,GAAU,WAAC9W,MAAAA,CAAoBC,UAAU,kCACvD,WAACD,MAAAA,CAAIC,UAAU,mDACb,UAACmE,KAAAA,CAAGnE,UAAU,uBAAe6W,EAAOnW,IAAI,GACvCmW,EAAOS,UAAU,EAAI,WAAClT,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,sBACjC,UAACoJ,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,iBAAiB,aAI7C,WAACD,MAAAA,CAAIC,UAAU,+CACZ6W,EAAO3O,QAAQ,CAACD,MAAM,CAAC,eAE1B,UAAClI,MAAAA,CAAIC,UAAU,qBACZ6W,EAAO3O,QAAQ,CAACQ,GAAG,CAACxB,IACzB,IAAMqQ,EAAarQ,EAAQ6B,OAAO,EAAI7B,EAAQ6B,OAAO,CAACd,MAAM,CAAG,EAC/D,MAAO,WAAClI,MAAAA,CAAqBC,UAAU,sDAC7B,UAAC6E,OAAAA,UAAMqC,EAAQxG,IAAI,GAClB6W,EAAa,UAACJ,GAAAA,CAAWA,CAAAA,CAACnX,UAAU,2BAA8B,UAACoX,GAAAA,CAAKA,CAAAA,CAACpX,UAAU,oCAF7EkH,EAAQ3G,EAAE,CAI7B,OAlBsCsW,EAAOtW,EAAE,kBAwN7D,UAACgL,EAAAA,EAAMA,CAAAA,CAACC,KAAMkJ,EAAkBjJ,aAAckJ,EAAqBtU,sBAAoB,SAASH,0BAAwB,qCACtH,WAACwL,EAAAA,EAAaA,CAAAA,CAAC1L,UAAU,gDAAgDK,sBAAoB,gBAAgBH,0BAAwB,sCACnI,WAACyL,EAAAA,EAAYA,CAAAA,CAACtL,sBAAoB,eAAeH,0BAAwB,sCACvE,UAAC0L,EAAAA,EAAWA,CAAAA,CAACvL,sBAAoB,cAAcH,0BAAwB,qCACpEqU,EAAYE,IAAI,EAAEuB,UAAU/N,OAAS,YAAc,mBAEtD,UAAC4D,EAAAA,EAAiBA,CAAAA,CAACxL,sBAAoB,oBAAoBH,0BAAwB,qCAA4B,qDAKjH,WAACH,MAAAA,CAAIC,UAAU,sBAEb,WAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,WAAWC,sBAAoB,QAAQH,0BAAwB,qCAA4B,gBAC1G,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,WAAWC,YAAY,qBAAqBC,MAAO8T,EAAYE,IAAI,EAAE/T,MAAQ,GAAIC,SAAUC,GAAK4T,EAAe1I,GAAS,EAClI,EADkI,CAC/HA,CAAI,CACP2I,KAAM3I,EAAK2I,IAAI,CAAG,CAChB,GAAG3I,EAAK2I,IAAI,CACZ/T,KAAME,EAAEC,MAAM,CAACJ,KAAK,EAClB,KACN,GAAKJ,sBAAoB,QAAQH,0BAAwB,iCAGzD,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAeC,sBAAoB,QAAQH,0BAAwB,qCAA4B,sBAC9G,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,eAAeW,KAAK,SAASkE,IAAI,IAAIiU,IAAI,MAAM5Y,MAAO8T,EAAYE,IAAI,EAAEwB,cAAgB,GAAItV,SAAUC,GAAK4T,EAAe1I,GAAS,EAC7I,EAD6I,CAC1IA,CAAI,CACP2I,KAAM3I,EAAK2I,IAAI,CAAG,CAChB,GAAG3I,EAAK2I,IAAI,CACZwB,aAAcqD,SAAS1Y,EAAEC,MAAM,CAACJ,KAAK,CACvC,EAAI,KACN,GAAKJ,sBAAoB,QAAQH,0BAAwB,iCAGzD,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYC,sBAAoB,QAAQH,0BAAwB,qCAA4B,wBAC3G,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,YAAYW,KAAK,SAASkE,IAAI,IAAI3E,MAAO8T,EAAYE,IAAI,EAAEyB,WAAa,GAAIvV,SAAUC,GAAK4T,EAAe1I,GAAS,EAC7H,EAD6H,CAC1HA,CAAI,CACP2I,KAAM3I,EAAK2I,IAAI,CAAG,CAChB,GAAG3I,EAAK2I,IAAI,CACZyB,UAAWtV,EAAEC,MAAM,CAACJ,KAAK,CAAG6Y,SAAS1Y,EAAEC,MAAM,CAACJ,KAAK,OAAImC,CACzD,EAAI,KACN,GAAKpC,YAAY,oBAAoBH,sBAAoB,QAAQH,0BAAwB,oCAI3F,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,kBAAkBC,sBAAoB,QAAQH,0BAAwB,qCAA4B,cACjH,UAAC+B,EAAAA,CAAQA,CAAAA,CAAC1B,GAAG,kBAAkBC,YAAY,+BAA+BC,MAAO8T,EAAYE,IAAI,EAAEvS,aAAe,GAAIvB,SAAUC,GAAK4T,EAAe1I,GAAS,EAC7J,EAD6J,CAC1JA,CAAI,CACP2I,KAAM3I,EAAK2I,IAAI,CAAG,CAChB,GAAG3I,EAAK2I,IAAI,CACZvS,YAAatB,EAAEC,MAAM,CAACJ,KAAK,EACzB,KACN,GAAK0B,KAAM,EAAG9B,sBAAoB,WAAWH,0BAAwB,iCAIrE,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,8CACb,UAACmE,KAAAA,CAAGnE,UAAU,iCAAwB,eACtC,WAACiB,EAAAA,CAAMA,CAAAA,CAACG,QA/kBC,CA+kBQmY,IA7hB7B1E,EAjDkC,CAChCtU,GAAIqU,GAAiBrU,IAAM,CAAC,KAgDXiZ,IAhDoB,EAAErT,KAAK2B,GAAG,IAAI,CACnD5G,KAAM,kBACNmV,SAAU,CAAC,CACTnV,KAAM,OACNT,MAAO,EACT,EAAE,CACFgZ,QAAS7E,GAAiB1T,OAAS,aAAe,CAAC,CACjD6H,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,MACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,OACT,EAAE,CACFiZ,WAAW,CACb,EAAE,CAAG,CAAC,CACJ3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAE,CACFC,YAAa,GACbC,YAAa,EAAE,CACfR,OAAQ,EACRrR,WAAYwM,EAAYE,IAAI,EAAEuB,UAAU/N,QAAU,CACpD,GAEA8M,GAAwB,EAC1B,EA2hB+C1U,sBAAoB,SAASH,0BAAwB,sCACpF,UAAC2J,EAAAA,CAAIA,CAAAA,CAAC7J,UAAU,eAAeK,sBAAoB,OAAOH,0BAAwB,8BAA8B,0BAKnHqU,EAAYE,IAAI,EAAEuB,UAAU/N,SAAW,EAAI,WAAClI,MAAAA,CAAIC,UAAU,mDACvD,UAACuK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,yBACtB,UAACgC,IAAAA,CAAEhC,UAAU,mBAAU,4BAChB,UAACD,MAAAA,CAAIC,UAAU,qBACrBuU,EAAYE,IAAI,EAAEuB,UAAUtN,IAAI,CAAC2N,EAAU1N,IAAU,UAACmB,EAAAA,EAAIA,CAAAA,UACvD,UAACC,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,eACrB,WAACD,MAAAA,CAAIC,UAAU,6CACb,WAACD,MAAAA,CAAIC,UAAU,mBACb,WAACD,MAAAA,CAAIC,UAAU,6CACb,UAACoE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,mBAAWwH,EAAQ,IAClC,UAACvE,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,qBACM,oBAAlBkV,EAASnV,IAAI,CAAyB,gBAAkBmV,iBAASnV,IAAI,CAAoB,cAAgB,UAE5G,WAAC2D,OAAAA,CAAK7E,UAAU,0CACbqW,EAAS+C,MAAM,CAAC,cAGrB,UAACrZ,MAAAA,CAAIC,UAAU,mBACZqW,EAASA,QAAQ,CAAC3N,GAAG,CAAC,CAACoJ,EAAO+H,IAAe,WAACC,IAAAA,QAAc,YACzC,SAAfhI,EAAM5Q,IAAI,EAAe,UAACc,IAAAA,UAAG8P,EAAMrR,KAAK,GACzB,UAAfqR,EAAM5Q,IAAI,EAAgB4Q,EAAMrR,KAAK,EAAI,UAAC4B,MAAAA,CAAIC,IAAKwP,EAAMrR,KAAK,CAAE8B,IAAK,CAAC,eAAe,EAAEsX,EAAAA,CAAY,CAAE7Z,UAAU,4CAFjD6Z,MAKlD,oBAAlBxD,EAASnV,IAAI,EAA0BmV,EAASoD,OAAO,EAAI,UAAC1Z,MAAAA,CAAIC,UAAU,0BACtEqW,EAASoD,OAAO,CAAC/Q,GAAG,CAAC,CAACqR,EAAQC,IAAa,WAACja,MAAAA,CAAmBC,UAAU,0CACrEia,OAAOC,YAAY,CAAC,GAAKF,GAAU,IACnCD,EAAOhR,OAAO,CAACL,GAAG,CAAC,CAACoJ,EAAOqI,IAAqB,WAACL,IAAAA,QAAc,YAC5C,SAAfhI,EAAM5Q,IAAI,EAAe,UAAC2D,OAAAA,UAAMiN,EAAMrR,KAAK,GAC5B,UAAfqR,EAAM5Q,IAAI,EAAgB4Q,EAAMrR,KAAK,EAAI,UAAC4B,MAAAA,CAAIC,IAAKwP,EAAMrR,KAAK,CAAE8B,IAAK,CAAC,aAAa,EAAE4X,EAAAA,CAAkB,CAAEna,UAAU,+CAFlDma,MAFpBH,SAS5D,WAACja,MAAAA,CAAIC,UAAU,wCACb,UAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMgV,EAAaC,YAC5D,UAAC7L,EAAAA,CAAIA,CAAAA,CAACxK,UAAU,cAElB,WAACyK,EAAAA,EAAWA,CAAAA,WACV,UAACC,EAAAA,EAAkBA,CAAAA,CAAC3G,OAAO,aACzB,UAAC9C,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,cAC3B,UAACmI,EAAAA,CAAMA,CAAAA,CAAC3K,UAAU,gBAGtB,WAAC4K,EAAAA,EAAkBA,CAAAA,WACjB,WAACC,EAAAA,EAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAgBA,CAAAA,UAAC,qBAClB,UAACC,EAAAA,EAAsBA,CAAAA,UAAC,yDAI1B,WAACC,EAAAA,EAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAiBA,CAAAA,UAAC,UACnB,UAACC,EAAAA,EAAiBA,CAAAA,CAAC9J,QAAS,IAAMkV,EAAeD,EAAS9V,EAAE,WAAG,4BAhDd8V,EAAS9V,EAAE,WA8DpF,WAACwL,EAAAA,EAAYA,CAAAA,CAAC1L,sBAAoB,eAAeH,0BAAwB,sCACvE,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAMuT,GAAoB,GAAQtU,sBAAoB,SAASH,0BAAwB,qCAA4B,UAGtJ,WAACe,EAAAA,CAAMA,CAAAA,CAACG,QAzsBD,CAysBUgZ,IAxsBzB,GAAI,CAAC7F,EAAYE,IAAI,EAAI,CAACF,EAAYE,IAAI,CAAC/T,IAAI,CAACuL,IAAI,GAAI,YACtDrK,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,yBAGW,SAAS,CAA9B8Q,EAAYrT,IAAI,CAClB9B,EAAS,CACPiY,UAAW9C,EAAYE,IAAI,GA4B7BrV,EAAS,CACP4I,QA1BqB7I,CA0BZoJ,CA1BiBP,OAAO,CAACU,GAAG,CAACmO,IACtC,GAAIA,EAAOtW,EAAE,GAAK4T,EAChB,GAAyB,UAAU,CADH,EAChBjT,IAAI,CAClB,MAAO,CACL,GAAG2V,CAAM,CACTS,WAAY/C,EAAYE,IAAI,MAEzB,CACL,IAAMrL,EAAkByN,EAAO3O,QAAQ,CAACQ,GAAG,CAACxB,GAC1C,EAAY3G,EAAE,GAAK8T,EACV,CACL,GAAGnN,CAAO,CACVyQ,SAHgC,GAGnBpD,EAAYE,IAAI,EAG1BvN,GAET,MAAO,CACL,GAAG2P,CAAM,CACT3O,SAAUkB,CACZ,CACF,CAEF,OAAOyN,CACT,EAGA,GAEFlC,GAAoB,GACpBH,EAAe,CACbtT,KAAM,UACNuT,KAAM,IACR,GACA7S,EAAAA,EAAKA,CAACC,OAAO,CAAC,yBAChB,EA4pBqCxB,sBAAoB,SAASH,0BAAwB,sCAC9E,UAACma,GAAAA,CAAIA,CAAAA,CAACra,UAAU,eAAeK,sBAAoB,OAAOH,0BAAwB,8BAA8B,yBAQxH,UAACqL,EAAAA,EAAMA,CAAAA,CAACC,KAAMsJ,EAAsBrJ,aAAcsJ,EAAyB1U,sBAAoB,SAASH,0BAAwB,qCAC9H,WAACwL,EAAAA,EAAaA,CAAAA,CAAC1L,UAAU,gDAAgDK,sBAAoB,gBAAgBH,0BAAwB,sCACnI,UAACyL,EAAAA,EAAYA,CAAAA,CAACtL,sBAAoB,eAAeH,0BAAwB,qCACvE,UAAC0L,EAAAA,EAAWA,CAAAA,CAACvL,sBAAoB,cAAcH,0BAAwB,qCACpE0U,GAAiByB,SAAW,kBAAoB,6BAIrD,WAACtW,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAeC,sBAAoB,QAAQH,0BAAwB,qCAA4B,oBAC9G,WAACqE,EAAAA,EAAMA,CAAAA,CAAC9D,MAAOmU,GAAiB1T,MAAQ,kBAAmBsD,cAAe,IAC1EqQ,EAAmB/I,IACjB,GAAI,CAACA,EAAM,OAAO,KAClB,IAAM0N,EAAc,CAClB,GAAG1N,CAAI,CACP5K,KAAMT,CACR,EA4CA,MA3Cc,cAAc,CAAxBA,EACF+Y,EAAYC,OAAO,CAAG,CAAC,CACrB1Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,MACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,OACT,EAAE,CACFiZ,WAAW,CACb,EAAE,CACiB,mBAAmB,CAA7BjZ,EACT+Y,EAAYC,OAAO,CAAG,CAAC,CACrB1Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAG,CACD3Q,QAAS,CAAC,CACR7H,KAAM,OACNT,MAAO,EACT,EAAE,CACFiZ,WAAW,CACb,EAAE,CAEFF,EAAYC,OAAO,CAAG7W,OAEjB4W,CACT,EACF,CAJuC,CAIpCnZ,sBAAoB,GAJ0C,MAIjCH,0BAAwB,sCACpD,UAACuE,EAAAA,EAAaA,CAAAA,CAACpE,sBAAoB,gBAAgBH,0BAAwB,qCACzE,UAACwE,EAAAA,EAAWA,CAAAA,CAACrE,sBAAoB,cAAcH,0BAAwB,gCAEzE,WAACyE,EAAAA,EAAaA,CAAAA,CAACtE,sBAAoB,gBAAgBH,0BAAwB,sCACzE,UAAC0E,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,kBAAkBJ,sBAAoB,aAAaH,0BAAwB,qCAA4B,kBACzH,UAAC0E,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,aAAaJ,sBAAoB,aAAaH,0BAAwB,qCAA4B,gBACpH,UAAC0E,EAAAA,EAAUA,CAAAA,CAACnE,MAAM,QAAQJ,sBAAoB,aAAaH,0BAAwB,qCAA4B,mBAKrH,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,iBAAiBC,sBAAoB,QAAQH,0BAAwB,qCAA4B,SAChH,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,iBAAiBW,KAAK,SAASkE,IAAI,IAAI3E,MAAOmU,GAAiBwE,QAAU,EAAGzY,SAAUC,GAAKiU,EAAmB/I,GAAQA,EAAO,CACvI,GAAGA,CAAI,CACPsN,OAAQE,SAAS1Y,EAAEC,MAAM,CAACJ,KAAK,CACjC,EAAI,MAAOJ,sBAAoB,QAAQH,0BAAwB,oCAIjE,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAeC,sBAAoB,QAAQH,0BAAwB,qCAA4B,iBAC9G,UAAC2Q,GAAoBA,CAACC,eAAgB8D,CAAjB/D,EAAkCwF,UAAY,EAAE,CAAEtF,gBAAiBhI,GAAW8L,EAAmB/I,GAAQA,EAAO,CACrI,GAAGA,CAAI,CACPuK,SAAUtN,CACZ,EAAI,MAAOiI,aAAa,EACxB3Q,sBAAoB,uBAAuBH,0BAAwB,iCAGjE0U,CAAAA,GAAiB1T,OAAS,mBAAqB0T,GAAiB1T,OAAS,aAAW,EAAM,WAACnB,MAAAA,CAAIC,UAAU,sBACvG,UAACG,EAAAA,CAAKA,CAAAA,UAAC,oBACNyU,EAAgB6E,OAAO,EAAE/Q,IAAI,CAACqR,EAAQpR,IAAU,WAAC5I,MAAAA,CAAgBC,UAAU,0DACxE,WAACD,MAAAA,CAAIC,UAAU,wCACZ4U,sBAAgB1T,IAAI,EAA0B,WAAC2D,OAAAA,CAAK7E,UAAU,oCAC1Dia,OAAOC,YAAY,CAAC,GAAKvR,GAAO,OAEX,oBAAzBiM,EAAgB1T,IAAI,CAAyB,UAAC2P,GAAoBA,CAACC,eAAgBiJ,CAAjBlJ,CAAwB9H,OAAO,EAAI,EAAE,CAAEgI,gBAAiBhI,IAC/H,IAAMuR,EAAa,IAAK1F,EAAgB6E,OAAO,EAAI,EAAE,CAAE,CACvDa,CAAU,CAAC3R,EAAM,CAAG,CAClB,GAAG2R,CAAU,CAAC3R,EAAM,CACpBI,QAASA,CACX,EACA8L,EAAmB/I,GAAQA,EAAO,CAChC,GAAGA,CAAI,CACP2N,QAASa,CACX,EAAI,KACN,EAAGtJ,YAAa,KACX,UAACnM,OAAAA,CAAK7E,UAAU,iCAAyB+Z,EAAOhR,OAAO,CAAC,EAAE,CAACtI,KAAK,MAEjE,WAACV,MAAAA,CAAIC,UAAU,6CACb,UAACsF,EAAAA,CAAQA,CAAAA,CAAC/E,GAAI,CAAC,eAAe,EAAEoI,EAAAA,CAAO,CAAEpD,QAASwU,EAAOL,SAAS,CAAElU,gBAAiB,IACzF,IAAM8U,EAAa,IAAK1F,EAAgB6E,OAAO,EAAI,EAAE,CAAE,CACvDa,CAAU,CAAC3R,EAAM,CAAG,CAClB,GAAG2R,CAAU,CAAC3R,EAAM,CACpB+Q,UAAWnU,CACb,EACAsP,EAAmB/I,GAAQA,EAAO,CAChC,GAAGA,CAAI,CACP2N,QAASa,CACX,EAAI,KACN,IACM,UAACna,EAAAA,CAAKA,CAAAA,CAACC,QAAS,CAAC,eAAe,EAAEuI,EAAAA,CAAO,UAAE,uBA9BUA,OAoC9DiM,GAA4C,UAAzBA,EAAgB1T,IAAI,EAAgB,WAACnB,MAAAA,CAAIC,UAAU,sBACnE,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,wBAAe,iBAC9B,UAAC6B,EAAAA,CAAQA,CAAAA,CAAC1B,GAAG,eAAeC,YAAY,6CAA6CC,MAAOmU,EAAgB+E,WAAW,EAAI,GAAIhZ,SAAUC,GAAKiU,EAAmB/I,GAAQA,EAAO,CAClL,GAAGA,CAAI,CACP6N,YAAa/Y,EAAEC,MAAM,CAACJ,KAAK,EACzB,MAAO0B,KAAM,OAGhByS,GAAmB,WAAC7U,MAAAA,CAAIC,UAAU,sBAC/B,UAACG,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,uBAAc,kCAC7B,UAACyQ,GAAoBA,CAACC,eAAgB8D,CAAjB/D,EAAkC+I,aAAe,EAAE,CAAE7I,gBAAiBhI,IAC7F8L,EAAmB/I,GAAQA,EAAO,CAChC,GAAGA,CAAI,CACP8N,YAAa7Q,CACf,EAAI,KACN,EAAGvI,YAAY,8DAA8DwQ,aAAa,UAI5F,WAACjF,EAAAA,EAAYA,CAAAA,CAAC1L,sBAAoB,eAAeH,0BAAwB,sCACvE,UAACe,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAAS,IAAM2T,GAAwB,GAAQ1U,sBAAoB,SAASH,0BAAwB,qCAA4B,UAG1J,WAACe,EAAAA,CAAMA,CAAAA,CAACG,QApwBG,CAowBMmZ,IAnwBzB,GAAI,CAAC3F,GAAuD,IAApCA,EAAgByB,QAAQ,CAACpO,MAAM,EAA+C,SAArC2M,EAAgByB,QAAQ,CAAC,EAAE,CAACnV,IAAI,EAAe,CAAC0T,EAAgByB,QAAQ,CAAC,EAAE,CAAC5V,KAAK,CAACwL,IAAI,GAAI,YACzJrK,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,0BAGd,GAAI,CAAC8Q,EAAYE,IAAI,CAAE,OACvB,IAAM+B,EAAmB,IAAIjC,EAAYE,IAAI,CAACuB,SAAS,CAAC,CAClD9J,EAAgBsK,EAAiB/M,SAAS,CAACgN,GAAKA,EAAElW,EAAE,GAAKqU,EAAgBrU,EAAE,EAC7E2L,GAAiB,EACnBsK,CADsB,CACLtK,EAAc,CAAG0I,EAElC4B,EAAiBrK,IAAI,CAACyI,GAExBJ,EAAe1I,GAAS,EACtB,EADsB,CACnBA,CAAI,CACP2I,KAAM3I,EAAK2I,IAAI,CAAG,CAChB,GAAG3I,EAAK2I,IAAI,CACZuB,UAAWQ,CACb,EAAI,KACN,GACAzB,GAAwB,GACxBF,EAAmB,MACnBjT,EAAAA,EAAKA,CAACC,OAAO,CAAC,+BAChB,EA6uByCxB,sBAAoB,SAASH,0BAAwB,sCACjF0U,GAAiByB,SAAW,WAAa,SAAS,2BAMjE,oDCl+BO,SAASmE,GAAe,MAC7Brb,CAAI,WACJsb,CAAS,cACTC,CAAY,CACQ,EACpB,GAAM,CAACC,EAAaC,EAAe,CAAGrb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GA0FzCsb,EAzFqB,MACzB,IAAMC,EAA0B,EAAE,CAGlCA,EAAM3O,IAAI,CAAC,CACT5L,GAAI,cACJwa,MAAO,cACPC,OAAQ7b,EAAKuB,IAAI,CAACuL,IAAI,GAAK,WAAa,aACxC/J,YAAa/C,EAAKuB,IAAI,CAACuL,IAAI,GAAK,CAAC,CAAC,EAAE9M,EAAKuB,IAAI,CAAC,CAAC,CAAC,CAAG,0BACnDua,UAAU,CACZ,GACAH,EAAM3O,IAAI,CAAC,CACT5L,GAAI,qBACJwa,MAAO,mBACPC,OAAQ7b,EAAK+C,WAAW,CAAC+J,IAAI,GAAK,WAAa,aAC/C/J,YAAa/C,EAAK+C,WAAW,CAAC+J,IAAI,GAAK,GAAG9M,EAAK+C,WAAW,CAAC+F,MAAM,CAAC,SAAS,CAAC,CAAG,+BAC/EgT,SAAU,EACZ,GACAH,EAAM3O,IAAI,CAAC,CACT5L,GAAI,cACJwa,MAAO,cACPC,OAAQ7b,EAAK4B,UAAU,CAACkL,IAAI,GAAK,WAAa,aAC9C/J,YAAa/C,EAAK4B,UAAU,CAACkL,IAAI,GAAK9M,EAAK4B,UAAU,CAAG,0BACxDka,SAAU,EACZ,GACAH,EAAM3O,IAAI,CAAC,CACT5L,GAAI,cACJwa,MAAO,cACPC,OAAQ7b,EAAKwD,UAAU,CAAG,WAAa,UACvCT,YAAa/C,EAAKwD,UAAU,CAAG,6BAA+B,qCAC9DsY,UAAU,CACZ,GACAH,EAAM3O,IAAI,CAAC,CACT5L,GAAI,eACJwa,MAAO,iBACPC,OAAQ7b,EAAKQ,SAAS,EAAIR,EAAKS,OAAO,CAAG,WAAa,UACtDsC,YAAa/C,EAAKQ,SAAS,EAAIR,EAAKS,OAAO,CAAG,GAAG,IAAIuG,KAAKhH,EAAKQ,SAAS,EAAEub,kBAAkB,GAAG,GAAG,EAAE,IAAI/U,KAAKhH,EAAKS,OAAO,EAAEsb,kBAAkB,IAAI,CAAG,yCACpJD,UAAU,CACZ,GAGA,IAAME,EAAchc,EAAK6I,OAAO,CAACC,MAAM,CACvC6S,EAAM3O,IAAI,CAAC,CACT5L,GAAI,UACJwa,MAAO,iBACPC,OAAQG,EAAc,EAAI,WAAa,aACvCjZ,YAAaiZ,EAAc,EAAI,GAAGA,EAAY,mBAAmB,CAAC,CAAG,+BACrEF,UAAU,CACZ,GACA,IAAMrE,EAAgBzX,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACD,MAAM,CAAE,GACzF6S,EAAM3O,IAAI,CAAC,CACT5L,GAAI,WACJwa,MAAO,UACPC,OAAQpE,EAAgB,EAAI,WAAa,aACzC1U,YAAa0U,EAAgB,EAAI,GAAGA,EAAc,qBAAqB,CAAC,CAAG,iCAC3EqE,UAAU,CACZ,GAGA,IAAMG,EAAsBjc,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACM,MAAM,CAACtB,GAAWA,EAAQ6B,OAAO,EAAI7B,EAAQ6B,OAAO,CAACd,MAAM,CAAG,GAAGA,MAAM,CAAE,GAChK6S,EAAM3O,IAAI,CAAC,CACT5L,GAAI,UACJwa,MAAO,iBACPC,OAAQI,IAAwBxE,EAAgB,WAAawE,EAAsB,EAAI,UAAY,aACnGlZ,YAAa,GAAGkZ,EAAoB,MAAM,EAAExE,EAAc,wBAAwB,CAAC,CACnFqE,UAAU,CACZ,GAGA,IAAMI,EAAmBlc,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACM,MAAM,CAACtB,GAAWA,EAAQ8B,cAAc,EAAI9B,EAAQyQ,WAAW,EAAE1P,MAAM,CAAE,GACvJqT,EAAkBnc,EAAK6I,OAAO,CAACQ,MAAM,CAACqO,GAAUA,EAAO1O,aAAa,EAAI0O,EAAOS,UAAU,EAAErP,MAAM,CAiBvG,OAhBA6S,EAAM3O,IAAI,CAAC,CACT5L,GAAI,UACJwa,MAAO,OACPC,OAAQK,EAAmB,GAAKC,EAAkB,EAAI,WAAa,UACnEpZ,YAAa,GAAGmZ,EAAiB,eAAe,EAAEC,EAAgB,YAAY,CAAC,CAC/EL,UAAU,CACZ,GAGAH,EAAM3O,IAAI,CAAC,CACT5L,GAAI,aACJwa,MAAO,aACPC,OAAQ7b,EAAKkY,SAAS,CAAG,WAAa,UACtCnV,YAAa/C,EAAKkY,SAAS,CAAG,GAAGlY,EAAKkY,SAAS,CAACrB,SAAS,CAAC/N,MAAM,CAAC,WAAW,CAAC,CAAG,0BAChFgT,UAAU,CACZ,GACOH,EACT,IAEMS,EAAgBV,EAAgBrS,MAAM,CAACgT,GAAQA,EAAKP,QAAQ,EAC5DQ,EAAoBF,EAAc/S,MAAM,CAACgT,GAAwB,aAAhBA,EAAKR,MAAM,EAAiB/S,MAAM,CACnFyT,EAAaD,IAAsBF,EAActT,MAAM,CACvD0T,EAAed,EAAgBrS,MAAM,CAACgT,GAAwB,aAAhBA,EAAKR,MAAM,EAAiB/S,MAAM,CAChF2T,EAAuBra,KAAK2V,KAAK,CAACyE,EAAed,EAAgB5S,MAAM,CAAG,KAgB1E4T,EAAQC,CAfS,KACrB,IAAMlF,EAAgBzX,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACD,MAAM,CAAE,GACnF8T,EAAe5c,EAAK6I,OAAO,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAGtCvK,EAFgBuK,EAAO3O,EAEjB8T,MAFyB,CAACxT,MAAM,CAACa,GAAKA,EAAEL,cAAc,EAAEf,MAAM,KACxD4O,EAAO1O,aAAa,CAEtC,EAFyC,GAEnChJ,CAFuC,CAEvCA,EAAKkY,SAAS,CACjB4E,EAAoB9c,EAAK6I,EADD,KACQ,CAACqE,MAAM,CAAC,CAACC,EAAKuK,IAAWvK,EAAMuK,EAAO3O,QAAQ,CAACmE,MAAM,CAAC,CAAC6P,EAAYhV,IAAYgV,EAA+J,EAAlJ3a,KAAK4a,IAAI,CAAC,EAASpT,OAAO,CAAWP,MAAM,CAACsJ,GAAwB,SAAfA,EAAM5Q,IAAI,EAAamL,MAAM,CAAC,CAAC+P,EAAStK,IAAUsK,EAAUtK,EAAMrR,KAAK,CAACwH,MAAM,CAAE,GAAK,KAAW,GAAI,GAC3R,MAAO,CACLD,QAAS7I,EAAK6I,OAAO,CAACC,MAAM,CAC5BC,SAAU0O,EACVyF,QAASN,EACTE,kBAAmB1a,KAAK8X,GAAG,CAAC4C,EAAmB,GACjD,CADqD,CAEvD,IAEMK,EAAgB,UACpB,GAAI,CALsE,EAKzD,YACf1a,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,wDAGd,GAAI,CACF,MAAMgX,IACN7Y,EAAAA,EAAKA,CAACC,OAAO,CAAC,+BAChB,CAAE,MAAO4B,EAAO,CACd7B,EAAAA,EAAKA,CAAC6B,KAAK,CAAC,4BACd,CACF,EACA,MAAO,WAAC1D,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,iBAAiBC,0BAAwB,gCAE7F,WAACH,MAAAA,CAAIC,UAAU,kCACb,UAACD,MAAAA,CAAIC,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uEAAwEgW,EAAa,8BAAgC,0CACrIA,EAAa,UAACa,GAAAA,CAAMA,CAAAA,CAACvc,UAAU,YAAe,UAACwc,GAAAA,CAAWA,CAAAA,CAACxc,UAAU,cAExE,UAAC4J,KAAAA,CAAG5J,UAAU,8BACX0b,EAAa,0BAA4B,mBAE5C,UAAC1Z,IAAAA,CAAEhC,UAAU,iCACV0b,EAAa,wEAA0E,gEAK5F,WAAC5R,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOH,0BAAwB,gCACvD,UAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,+BACnE,WAACH,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,WAACsK,EAAAA,EAASA,CAAAA,CAACrK,UAAU,8BAA8BK,sBAAoB,YAAYH,0BAAwB,gCACzG,UAACuc,GAAAA,CAAMA,CAAAA,CAACzc,UAAU,UAAUK,sBAAoB,SAASH,0BAAwB,wBACjF,UAAC2E,OAAAA,UAAK,4BAER,WAACyF,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,gCAC5Eyb,EAAa,SAAOd,EAAgB5S,MAAM,CAAC,sBAGhD,WAAClI,MAAAA,CAAIC,UAAU,uBACb,WAACD,MAAAA,CAAIC,UAAU,+BAAsB4b,EAAqB,OAC1D,UAAC7b,MAAAA,CAAIC,UAAU,yCAAgC,oBAIrD,WAAC+J,EAAAA,EAAWA,CAAAA,CAAC1J,sBAAoB,cAAcH,0BAAwB,gCACrE,UAACwc,EAAAA,CAAQA,CAAAA,CAACjc,MAAOmb,EAAsB5b,UAAU,OAAOK,sBAAoB,WAAWH,0BAAwB,wBAE/G,WAACH,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,0GACb,UAACgK,EAAAA,CAAQA,CAAAA,CAAChK,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,0BAEvF,UAACH,MAAAA,CAAIC,UAAU,+BAAuB6b,EAAM7T,OAAO,GACnD,UAACjI,MAAAA,CAAIC,UAAU,yCAAgC,aAGjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,4GACb,UAACqL,EAAAA,CAAQA,CAAAA,CAACrL,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,0BAEvF,UAACH,MAAAA,CAAIC,UAAU,+BAAuB6b,EAAM3T,QAAQ,GACpD,UAACnI,MAAAA,CAAIC,UAAU,yCAAgC,eAGjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8GACb,UAACuK,EAAAA,CAAUA,CAAAA,CAACvK,UAAU,UAAUK,sBAAoB,aAAaH,0BAAwB,0BAE3F,UAACH,MAAAA,CAAIC,UAAU,+BAAuB6b,EAAMQ,OAAO,GACnD,UAACtc,MAAAA,CAAIC,UAAU,yCAAgC,YAGjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8GACb,UAACoX,GAAAA,CAAKA,CAAAA,CAACpX,UAAU,UAAUK,sBAAoB,QAAQH,0BAAwB,0BAEjF,UAACH,MAAAA,CAAIC,UAAU,+BAAuB6b,EAAMI,iBAAiB,GAC7D,UAAClc,MAAAA,CAAIC,UAAU,yCAAgC,sBAOvD,WAAC8J,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOH,0BAAwB,gCACvD,UAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,+BACnE,WAACH,MAAAA,CAAIC,UAAU,8CACb,WAACqK,EAAAA,EAASA,CAAAA,CAACrK,UAAU,8BAA8BK,sBAAoB,YAAYH,0BAAwB,gCACzG,UAACiX,GAAAA,CAAWA,CAAAA,CAACnX,UAAU,UAAUK,sBAAoB,cAAcH,0BAAwB,wBAC3F,UAAC2E,OAAAA,UAAK,2BAER,WAAC5D,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,QAAQqB,KAAK,KAAKpB,QAAS,IAAMwZ,EAAe,CAACD,GAActa,sBAAoB,SAASH,0BAAwB,gCACjIya,EAAc,cAAgB,QAAQ,kBAI7C,UAAC5Q,EAAAA,EAAWA,CAAAA,CAAC1J,sBAAoB,cAAcH,0BAAwB,+BACrE,UAACH,MAAAA,CAAIC,UAAU,qBACZ6a,EAAgBnS,GAAG,CAAC8S,GAAQ,WAACzb,MAAAA,CAAkBC,UAAU,uCACtD,UAACD,MAAAA,CAAIC,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6EAA8F,aAAhB8V,EAAKR,MAAM,CAAkB,8BAAgD,YAAhBQ,EAAKR,MAAM,CAAiB,gCAAkC,sCACzM,aAAhBQ,EAAKR,MAAM,CAAkB,UAAC7D,GAAAA,CAAWA,CAAAA,CAACnX,UAAU,YAA+B,YAAhBwb,EAAKR,MAAM,CAAiB,UAACwB,GAAAA,CAAWA,CAAAA,CAACxc,UAAU,YAAe,UAACD,MAAAA,CAAIC,UAAU,sCAGvJ,WAACD,MAAAA,CAAIC,UAAU,2BACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAAC6E,OAAAA,CAAK7E,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sBAAuC,aAAhB8V,EAAKR,MAAM,CAAkB,iBAAmC,YAAhBQ,EAAKR,MAAM,CAAiB,kBAAoB,0BACxIQ,EAAKT,KAAK,GAEZS,EAAKP,QAAQ,EAAI,UAAC7W,EAAAA,CAAKA,CAAAA,CAACjD,QAAQ,cAAcnB,UAAU,6BAAoB,aAK9E2a,GAAe,UAAC3Y,IAAAA,CAAEhC,UAAU,8CACxBwb,EAAKtZ,WAAW,QAhBYsZ,EAAKjb,EAAE,UAyBpD,WAACuJ,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOH,0BAAwB,gCACvD,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,gCACnE,WAACmK,EAAAA,EAASA,CAAAA,CAACrK,UAAU,8BAA8BK,sBAAoB,YAAYH,0BAAwB,gCACzG,UAACwX,GAAAA,CAAGA,CAAAA,CAAC1X,UAAU,UAAUK,sBAAoB,MAAMH,0BAAwB,wBAC3E,UAAC2E,OAAAA,UAAK,sBAER,UAACyF,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,+BAAsB,+CAIvG,UAAC6J,EAAAA,EAAWA,CAAAA,CAAC1J,sBAAoB,cAAcH,0BAAwB,+BACrE,WAACH,MAAAA,CAAIC,UAAU,4CAEb,WAACD,MAAAA,CAAIC,UAAU,uCACZb,EAAKwD,UAAU,CAAG,UAACN,MAAAA,CAAIC,IAAgC,UAA3B,OAAOnD,EAAKwD,UAAU,CAAgBxD,EAAKwD,UAAU,CAAGgB,IAAIC,eAAe,CAACzE,EAAKwD,UAAU,EAAGJ,IAAKpD,EAAKuB,IAAI,CAAEV,UAAU,sCAAyC,UAACD,MAAAA,CAAIC,UAAU,0EACzM,UAACmN,EAAAA,CAAKA,CAAAA,CAACnN,UAAU,oCAGrB,WAACD,MAAAA,CAAIC,UAAU,mBACb,UAACmE,KAAAA,CAAGnE,UAAU,iCAAyBb,EAAKuB,IAAI,EAAI,gBACpD,UAACsB,IAAAA,CAAEhC,UAAU,8CACVb,EAAK+C,WAAW,EAAI,qBAGvB,WAACnC,MAAAA,CAAIC,UAAU,sEACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACwP,GAAAA,CAAIA,CAAAA,CAACxP,UAAU,UAAUK,sBAAoB,OAAOH,0BAAwB,wBAC7E,UAAC2E,OAAAA,UAAM1F,EAAK4B,UAAU,EAAI,mBAE5B,WAAChB,MAAAA,CAAIC,UAAU,wCACb,UAACgK,EAAAA,CAAQA,CAAAA,CAAChK,UAAU,UAAUK,sBAAoB,WAAWH,0BAAwB,wBACrF,WAAC2E,OAAAA,WAAMgX,EAAM7T,OAAO,CAAC,eAEvB,WAACjI,MAAAA,CAAIC,UAAU,wCACb,UAACoX,GAAAA,CAAKA,CAAAA,CAACpX,UAAU,UAAUK,sBAAoB,QAAQH,0BAAwB,wBAC/E,WAAC2E,OAAAA,WAAK,IAAEgX,EAAMI,iBAAiB,CAAC,eAEjC9c,EAAKQ,SAAS,EAAI,WAACI,MAAAA,CAAIC,UAAU,wCAC9B,UAAC8F,EAAAA,CAAQA,CAAAA,CAAC9F,UAAU,YACpB,UAAC6E,OAAAA,UAAM,IAAIsB,KAAKhH,EAAKQ,SAAS,EAAEub,kBAAkB,iBAM5D,UAACyB,EAAAA,SAASA,CAAAA,CAACtc,sBAAoB,YAAYH,0BAAwB,wBAGnE,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAAC4c,KAAAA,CAAG5c,UAAU,+BAAsB,qBACnCb,EAAK6I,OAAO,CAACC,MAAM,CAAG,EAAI,WAAClI,MAAAA,CAAIC,UAAU,sBACrCb,EAAK6I,OAAO,CAAC8K,KAAK,CAAC,EAAG,GAAGpK,GAAG,CAAC,CAACmO,EAAQlO,IAAU,WAAC5I,MAAAA,CAAoBC,UAAU,oBAC5E,WAACD,MAAAA,CAAIC,UAAU,wBACZ2I,EAAQ,EAAE,KAAGkO,EAAOnW,IAAI,IAE3B,WAACX,MAAAA,CAAIC,UAAU,+CACZ6W,EAAO3O,QAAQ,CAACD,MAAM,CAAC,WACvB4O,EAAO1O,aAAa,EAAI,qBAN4B0O,EAAOtW,EAAE,GASnEpB,EAAK6I,OAAO,CAACC,MAAM,CAAG,GAAK,WAAClI,MAAAA,CAAIC,UAAU,0CAAgC,WAC9Db,EAAK6I,OAAO,CAACC,MAAM,CAAG,EAAE,uBAE9B,UAACjG,IAAAA,CAAEhC,UAAU,gDAAuC,+BAStE,CAAC0b,GAAc,WAACmB,GAAAA,EAAKA,CAAAA,WAClB,UAACL,GAAAA,CAAWA,CAAAA,CAACxc,UAAU,YACvB,WAAC8c,GAAAA,EAAgBA,CAAAA,WACf,UAACjE,SAAAA,UAAO,eAAmB,iHAMjC,WAAC9Y,MAAAA,CAAIC,UAAU,mDACb,UAACD,MAAAA,CAAIC,UAAU,yCACZ0b,EAAa,kDAAoD,GAAGD,EAAkB,CAAC,EAAEF,EAActT,MAAM,CAAC,mBAAmB,CAAC,GAGrI,WAAClI,MAAAA,CAAIC,UAAU,wCACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUW,SAAU4Y,EAAcra,sBAAoB,SAASH,0BAAwB,gCACrG,UAACwX,GAAAA,CAAGA,CAAAA,CAAC1X,UAAU,eAAeK,sBAAoB,MAAMH,0BAAwB,wBAAwB,aAI1G,UAACe,EAAAA,CAAMA,CAAAA,CAACG,QAASkb,EAAexa,SAAU,CAAC4Z,GAAchB,EAAc1a,UAAU,gBAAgBK,sBAAoB,SAASH,0BAAwB,+BACnJwa,EAAe,iCACZ,UAAC3a,MAAAA,CAAIC,UAAU,sFAAsF,mBAEjG,iCACJ,UAACuc,GAAAA,CAAMA,CAAAA,CAACvc,UAAU,iBAAiB,gCAOnD,6BCtWO,SAAS+c,GAAe,MAC7B5d,CAAI,UACJC,CAAQ,CACY,EACpB,IAAM4d,EAAa7d,EAAK6d,UAAU,EAAI,CACpCC,aAAc,EAAE,CAChBC,oBAAqB,GACrBC,cAAe,EAAE,EAEb,CAACC,EAAgBC,EAAkB,CAAG9d,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC/C,CAAC+d,EAAiBC,EAAmB,CAAGhe,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjDie,EAAe,CAACC,EAA6Bhd,KACjDrB,EAAS,CACP4d,WAAY,CACV,GAAGA,CAAU,CACb,CAACS,EAAM,CAAEhd,CACX,CACF,EACF,EACMid,EAAiB,KACS,KAA1BN,CAAgC,CAAjBnR,IAAI,IAAc+Q,EAAWC,YAAY,CAACU,QAAQ,CAACP,EAAenR,IAAI,KAAK,CAC5FuR,EAAa,eAAgB,IAAIR,EAAWC,YAAY,CAAEG,EAAenR,IAAI,GAAG,EAChFoR,EAAkB,IAEtB,EACMO,EAAoB,IAExBJ,EAAa,eADeR,CACCa,CADUZ,YAAY,CAACzU,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAE7E,EACMqV,EAAkB,KACS,KAA3BV,CAAiC,CAAjBrR,IAAI,IAAc+Q,EAAWG,aAAa,CAACQ,QAAQ,CAACL,EAAgBrR,IAAI,KAAK,CAC/FuR,EAAa,gBAAiB,IAAIR,EAAWG,aAAa,CAAEG,EAAgBrR,IAAI,GAAG,EACnFsR,EAAmB,IAEvB,EACMU,EAAqB,IAEzBT,EAAa,gBADgBR,CACCkB,CADUf,aAAa,CAAC3U,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAE/E,EACA,MAAO,WAACmB,EAAAA,EAAIA,CAAAA,CAAC9J,UAAU,SAASK,sBAAoB,OAAOJ,wBAAsB,iBAAiBC,0BAAwB,gCACtH,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,gCACnE,UAACmK,EAAAA,EAASA,CAAAA,CAAChK,sBAAoB,YAAYH,0BAAwB,+BAAsB,0BACzF,UAACoK,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,+BAAsB,oEAEvG,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,YAAYK,sBAAoB,cAAcH,0BAAwB,gCAC3F,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACme,GAAAA,CAAaA,CAAAA,CAACne,UAAU,wBAAwBK,sBAAoB,gBAAgBH,0BAAwB,wBAC7G,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,iBAAiBC,sBAAoB,QAAQH,0BAAwB,+BAAsB,mBAE5G,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,iBAAiBE,MAAO2c,EAAgBzc,SAAUC,GAAKyc,EAAkBzc,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,6BAA6B4d,WAAYxd,IAC3I,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChBZ,IAEJ,EAAGrd,sBAAoB,QAAQH,0BAAwB,wBACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAASsc,EAAgBrd,sBAAoB,SAASH,0BAAwB,+BAAsB,cAE5H,UAACH,MAAAA,CAAIC,UAAU,qCACZgd,EAAWC,YAAY,CAACvU,GAAG,CAAC,CAAC6V,EAAK5V,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACzFue,EACD,UAACtd,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAMwc,EAAkBjV,YACnH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHsC2I,SAS7D,WAAC5I,MAAAA,CAAIC,UAAU,wCACb,UAAC8F,EAAAA,CAAQA,CAAAA,CAAC9F,UAAU,wBAAwBK,sBAAoB,WAAWH,0BAAwB,wBACnG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,sBAAsBC,sBAAoB,QAAQH,0BAAwB,+BAAsB,+BAEjH,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,sBAAsBW,KAAK,OAAO,MACvC8b,EAAWE,mBAAmB,CAAEvc,SAAUC,EADoC,CAC/B4c,EAAa,sBAAuB5c,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,qBAAqBH,sBAAoB,QAAQH,0BAAwB,wBAE9L,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACgK,EAAAA,CAAQA,CAAAA,CAAChK,UAAU,wBAAwBK,sBAAoB,WAAWH,0BAAwB,wBACnG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,kBAAkBC,sBAAoB,QAAQH,0BAAwB,+BAAsB,iBAE7G,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,kBAAkBE,MAAO6c,EAAiB3c,SAAUC,GAAK2c,EAAmB3c,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,2BAA2B4d,WAAYxd,IAC5I,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChBN,IAEJ,EAAG3d,sBAAoB,QAAQH,0BAAwB,wBACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAAS4c,EAAiB3d,sBAAoB,SAASH,0BAAwB,+BAAsB,cAE7H,UAACH,MAAAA,CAAIC,UAAU,qCACZgd,EAAWG,aAAa,CAACzU,GAAG,CAAC,CAAC6V,EAAK5V,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBAC1Fue,EACD,UAACtd,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAM6c,EAAmBtV,YACpH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHuC2I,cAUtE,yCCrGO,SAAS6V,GAAc,MAC5Brf,CAAI,UACJC,CAAQ,CACW,EACnB,IAAMqf,EAAYtf,EAAKsf,SAAS,EAAI,CAClCC,QAAS,EACTC,SAAU,GACVC,WAAY,EAAE,EAEV,CAACC,EAAeC,EAAiB,CAAGvf,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC7Cie,EAAe,CAACC,EAA4Bhd,KAChDrB,EAAS,CACPqf,UAAW,CACT,GAAGA,CAAS,CACZ,CAAChB,EAAM,CAAEhd,CACX,CACF,EACF,EACMse,EAAgB,KACS,KAAzBF,CAA+B,CAAjB5S,IAAI,IAAcwS,EAAUG,UAAU,CAACjB,QAAQ,CAACkB,EAAc5S,IAAI,KAAK,CACvFuR,EAAa,aAAc,IAAIiB,EAAUG,UAAU,CAAEC,EAAc5S,IAAI,GAAG,EAC1E6S,EAAiB,IAErB,EACME,EAAmB,IAEvBxB,EAAa,aADaiB,CACCQ,CADSL,UAAU,CAACpW,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAExE,EACA,MAAO,WAACmB,EAAAA,EAAIA,CAAAA,CAAC9J,UAAU,SAASK,sBAAoB,OAAOJ,wBAAsB,gBAAgBC,0BAAwB,+BACrH,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,+BACnE,UAACmK,EAAAA,EAASA,CAAAA,CAAChK,sBAAoB,YAAYH,0BAAwB,8BAAqB,uBACxF,UAACoK,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,8BAAqB,8DAEtG,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,YAAYK,sBAAoB,cAAcH,0BAAwB,+BAC3F,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACkf,GAAAA,CAAIA,CAAAA,CAAClf,UAAU,wBAAwBK,sBAAoB,OAAOH,0BAAwB,uBAC3F,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,UAAUC,sBAAoB,QAAQH,0BAAwB,8BAAqB,cAEpG,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,UAAUW,KAAK,SAAST,MAAOge,EAAUC,OAAO,CAAE/d,SAAUC,GAAK4c,EAAa,UAAWlE,SAAS1Y,EAAEC,MAAM,CAACJ,KAAK,GAAID,YAAY,aAAaH,sBAAoB,QAAQH,0BAAwB,uBAE3M,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACmf,GAAAA,CAASA,CAAAA,CAACnf,UAAU,wBAAwBK,sBAAoB,YAAYH,0BAAwB,uBACrG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,WAAWC,sBAAoB,QAAQH,0BAAwB,8BAAqB,mBAErG,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,WAAWW,KAAK,OAAOT,MAAOge,EAAUE,QAAQ,CAAEhe,SAAUC,GAAK4c,EAAa,WAAY5c,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,2BAA2BH,sBAAoB,QAAQH,0BAAwB,uBAEhN,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACof,GAAAA,CAAKA,CAAAA,CAACpf,UAAU,wBAAwBK,sBAAoB,QAAQH,0BAAwB,uBAC7F,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,gBAAgBC,sBAAoB,QAAQH,0BAAwB,8BAAqB,iBAE1G,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,gBAAgBE,MAAOoe,EAAele,SAAUC,GAAKke,EAAiBle,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,kCAAkC4d,WAAYxd,IAC7I,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChBS,IAEJ,EAAG1e,sBAAoB,QAAQH,0BAAwB,uBACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAAS2d,EAAe1e,sBAAoB,SAASH,0BAAwB,8BAAqB,cAE1H,UAACH,MAAAA,CAAIC,UAAU,qCACZye,EAAUG,UAAU,CAAClW,GAAG,CAAC,CAAC8S,EAAM7S,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACvFwb,EACD,UAACva,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAM4d,EAAiBrW,YAClH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHoC2I,cAUnE,yCCvEO,SAAS0W,GAAqB,CACnClgB,MAAI,UACJC,CAAQ,CACkB,EAC1B,IAAMkgB,EAAsBngB,EAAKmgB,mBAAmB,EAAI,CACtDC,UAAW,EACXC,eAAgB,EAAE,CAClBC,aAAc,EAAE,EAEZ,CAACC,EAAkBC,EAAoB,CAAGpgB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACnD,CAACqgB,EAAgBC,EAAkB,CAAGtgB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC/Cie,EAAe,CAACC,EAAsChd,KAC1DrB,EAAS,CACPkgB,oBAAqB,CACnB,GAAGA,CAAmB,CACtB,CAAC7B,EAAM,CAAEhd,CACX,CACF,EACF,EACMqf,EAAmB,KACS,KAA5BJ,CAAkC,CAAjBzT,IAAI,IAAcqT,EAAoBE,cAAc,CAAC7B,QAAQ,CAAC+B,EAAiBzT,IAAI,KAAK,CAC3GuR,EAAa,iBAAkB,IAAI8B,EAAoBE,cAAc,CAAEE,EAAiBzT,IAAI,GAAG,EAC/F0T,EAAoB,IAExB,EACMI,EAAsB,IAE1BvC,EAAa,iBADU8B,CACQU,CADYR,cAAc,CAAChX,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAEnF,EACMsX,EAAiB,KACS,KAA1BL,CAAgC,CAAjB3T,IAAI,IAAcqT,EAAoBG,YAAY,CAAC9B,QAAQ,CAACiC,EAAe3T,IAAI,KAAK,CACrGuR,EAAa,eAAgB,IAAI8B,EAAoBG,YAAY,CAAEG,EAAe3T,IAAI,GAAG,EACzF4T,EAAkB,IAEtB,EACMK,EAAoB,IAExB1C,EAAa,eADe8B,CACCa,CADmBV,YAAY,CAACjX,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAEtF,EACA,MAAO,WAACmB,EAAAA,EAAIA,CAAAA,CAAC9J,UAAU,SAASK,sBAAoB,OAAOJ,wBAAsB,uBAAuBC,0BAAwB,uCAC5H,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,uCACnE,UAACmK,EAAAA,EAASA,CAAAA,CAAChK,sBAAoB,YAAYH,0BAAwB,sCAA6B,uBAChG,UAACoK,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,sCAA6B,2EAE9G,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,YAAYK,sBAAoB,cAAcH,0BAAwB,uCAC3F,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACogB,GAAAA,CAAUA,CAAAA,CAACpgB,UAAU,wBAAwBK,sBAAoB,aAAaH,0BAAwB,+BACvG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYC,sBAAoB,QAAQH,0BAAwB,sCAA6B,mBAE9G,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,YAAYW,KAAK,SAAST,MAAO6e,EAAoBC,SAAS,CAAE5e,SAAUC,GAAK4c,EAAa,YAAarY,WAAWvE,EAAEC,MAAM,CAACJ,KAAK,GAAID,YAAY,kBAAkBH,sBAAoB,QAAQH,0BAAwB,+BAElO,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACqgB,GAAAA,CAAUA,CAAAA,CAACrgB,UAAU,wBAAwBK,sBAAoB,aAAaH,0BAAwB,+BACvG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,mBAAmBC,sBAAoB,QAAQH,0BAAwB,sCAA6B,uBAErH,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,mBAAmBE,MAAOif,EAAkB/e,SAAUC,GAAK+e,EAAoB/e,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,iCAAiC4d,WAAYxd,IACrJ,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChBwB,IAEJ,EAAGzf,sBAAoB,QAAQH,0BAAwB,+BACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAAS0e,EAAkBzf,sBAAoB,SAASH,0BAAwB,sCAA6B,cAErI,UAACH,MAAAA,CAAIC,UAAU,qCACZsf,EAAoBE,cAAc,CAAC9W,GAAG,CAAC,CAACqR,EAAQpR,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACvG+Z,EACD,UAAC9Y,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAM2e,EAAoBpX,YACrH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHoD2I,SAS3E,WAAC5I,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACsgB,GAAAA,CAAIA,CAAAA,CAACtgB,UAAU,wBAAwBK,sBAAoB,OAAOH,0BAAwB,+BAC3F,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,iBAAiBC,sBAAoB,QAAQH,0BAAwB,sCAA6B,gBAEnH,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,iBAAiBE,MAAOmf,EAAgBjf,SAAUC,GAAKif,EAAkBjf,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,0BAA0B4d,WAAYxd,IACxI,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChB2B,IAEJ,EAAG5f,sBAAoB,QAAQH,0BAAwB,+BACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAAS6e,EAAgB5f,sBAAoB,SAASH,0BAAwB,sCAA6B,cAEnI,UAACH,MAAAA,CAAIC,UAAU,qCACZsf,EAAoBG,YAAY,CAAC/W,GAAG,CAAC,CAAC6X,EAAa5X,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBAC1GugB,EACD,UAACtf,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAM8e,EAAkBvX,YACnH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHuD2I,cAUtF,6BCpGO,SAAS6X,GAAY,CAC1BrhB,MAAI,UACJC,CAAQ,CACS,EACjB,IAAMqhB,EAAUthB,EAAKshB,OAAO,EAAI,CAC9BC,SAAU,EAAE,CACZC,WAAY,EAAE,CACdC,cAAe,EACjB,EACM,CAACC,EAAYC,EAAc,CAAGvhB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACwhB,EAAaC,EAAe,CAAGzhB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACzCie,EAAe,CAACC,EAA0Bhd,KAC9CrB,EAAS,CACPqhB,QAAS,CACP,GAAGA,CAAO,CACV,CAAChD,EAAM,CAAEhd,CACX,CACF,EACF,EACMwgB,EAAa,KACS,KAAtBJ,CAA4B,CAAjB5U,IAAI,IAAcwU,EAAQC,QAAQ,CAAC/C,QAAQ,CAACkD,EAAW5U,IAAI,KAAK,CAC7EuR,EAAa,WAAY,IAAIiD,EAAQC,QAAQ,CAAEG,EAAW5U,IAAI,GAAG,EACjE6U,EAAc,IAElB,EACMI,EAAiBvY,IAErB6U,EAAa,WADWiD,CACCU,CADOT,QAAQ,CAAClY,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAElE,EACMyY,EAAc,KACS,KAAvBL,CAA6B,CAAjB9U,IAAI,IAAcwU,EAAQE,UAAU,CAAChD,QAAQ,CAACoD,EAAY9U,IAAI,KAAK,CACjFuR,EAAa,aAAc,IAAIiD,EAAQE,UAAU,CAAEI,EAAY9U,IAAI,GAAG,EACtE+U,EAAe,IAEnB,EACMK,EAAiB,IAErB7D,EAAa,aADaiD,CACCa,CADOX,UAAU,CAACnY,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAEtE,EACA,MAAO,WAACmB,EAAAA,EAAIA,CAAAA,CAAC9J,UAAU,SAASK,sBAAoB,OAAOJ,wBAAsB,cAAcC,0BAAwB,6BACnH,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,6BACnE,UAACmK,EAAAA,EAASA,CAAAA,CAAChK,sBAAoB,YAAYH,0BAAwB,4BAAmB,kBACtF,UAACoK,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,4BAAmB,4EAEpG,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,YAAYK,sBAAoB,cAAcH,0BAAwB,6BAC3F,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACuhB,GAAAA,CAASA,CAAAA,CAACvhB,UAAU,wBAAwBK,sBAAoB,YAAYH,0BAAwB,qBACrG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAaC,sBAAoB,QAAQH,0BAAwB,4BAAmB,aAErG,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,aAAaE,MAAOogB,EAAYlgB,SAAUC,GAAKkgB,EAAclgB,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,6BAA6B4d,WAAYxd,IAC/H,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChB2C,IAEJ,EAAG5gB,sBAAoB,QAAQH,0BAAwB,qBACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAAS6f,EAAY5gB,sBAAoB,SAASH,0BAAwB,4BAAmB,cAErH,UAACH,MAAAA,CAAIC,UAAU,qCACZygB,EAAQC,QAAQ,CAAChY,GAAG,CAAC,CAAC8Y,EAAS7Y,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACtFwhB,EACD,UAACvgB,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAM8f,EAAcvY,YAC/G,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHmC2I,SAS1D,WAAC5I,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACyhB,GAAAA,CAAQA,CAAAA,CAACzhB,UAAU,wBAAwBK,sBAAoB,WAAWH,0BAAwB,qBACnG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,cAAcC,sBAAoB,QAAQH,0BAAwB,4BAAmB,gBAEtG,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,cAAcE,MAAOsgB,EAAapgB,SAAUC,GAAKogB,EAAepgB,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,0BAA0B4d,WAAYxd,IAC/H,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChB8C,IAEJ,EAAG/gB,sBAAoB,QAAQH,0BAAwB,qBACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAASggB,EAAa/gB,sBAAoB,SAASH,0BAAwB,4BAAmB,cAEtH,UAACH,MAAAA,CAAIC,UAAU,qCACZygB,EAAQE,UAAU,CAACjY,GAAG,CAAC,CAACgZ,EAAU/Y,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACzF0hB,EACD,UAACzgB,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAMigB,EAAe1Y,YAChH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHsC2I,SAS7D,WAAC5I,MAAAA,CAAIC,UAAU,wCACb,UAACogB,GAAAA,CAAUA,CAAAA,CAACpgB,UAAU,wBAAwBK,sBAAoB,aAAaH,0BAAwB,qBACvG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,gBAAgBC,sBAAoB,QAAQH,0BAAwB,4BAAmB,sBAExG,UAACI,EAAAA,CAAKA,CAAAA,CAACC,GAAG,gBAAgBW,KAAK,OAAOT,MAAOggB,EAAQG,aAAa,CAAEjgB,SAAUC,GAAK4c,EAAa,gBAAiB5c,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,oDAAoDH,sBAAoB,QAAQH,0BAAwB,0BAG9P,yCCnGO,SAASyhB,GAAsB,MACpCxiB,CAAI,UACJC,CAAQ,CACmB,EAC3B,IAAMwiB,EAAoBziB,EAAKyiB,iBAAiB,EAAI,CAClDC,aAAc,EAAE,CAChBC,WAAY,EAAE,CACdC,QAAS,EAAE,EAEP,CAACC,EAAaC,EAAe,CAAG1iB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACzC,CAAC2iB,EAAYC,EAAc,CAAG5iB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvCie,EAAe,CAACC,EAAoChd,KAIxDrB,EAAS,CACPwiB,kBAAmB,CACjB,GAAGA,CAAiB,CACpB,CAACnE,EAAM,CAAEhd,CACX,CACF,EACF,EAOM2hB,EAAoB,CAACzZ,EAAe8U,EAA4Bhd,KACpE,IAAM4hB,EAAsB,IAAIT,EAAkBC,YAAY,CAAC,CAC/DQ,CAAmB,CAAC1Z,EAAM,CAAG,CAC3B,GAAG0Z,CAAmB,CAAC1Z,EAAM,CAC7B,CAAC8U,EAAM,CAAEhd,CACX,EACA+c,EAAa,eAAgB6E,EAC/B,EACMC,EAAoB,IAExB9E,EAAa,eADeoE,CACCS,CADiBR,YAAY,CAACrZ,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAEpF,EACM4Z,EAAc,KACS,KAAvBP,CAA6B,CAAjB/V,IAAI,IAAc2V,EAAkBE,UAAU,CAACnE,QAAQ,CAACqE,EAAY/V,IAAI,KAAK,CAC3FuR,EAAa,aAAc,IAAIoE,EAAkBE,UAAU,CAAEE,EAAY/V,IAAI,GAAG,EAChFgW,EAAe,IAEnB,EACMO,EAAkB7Z,IAEtB6U,EAAa,aADaoE,CACCa,CADiBX,UAAU,CAACtZ,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAEhF,EACM+Z,EAAa,KACS,KAAtBR,CAA4B,CAAjBjW,IAAI,IAAc2V,EAAkBG,OAAO,CAACpE,QAAQ,CAACuE,EAAWjW,IAAI,KAAK,CACtFuR,EAAa,UAAW,IAAIoE,EAAkBG,OAAO,CAAEG,EAAWjW,IAAI,GAAG,EACzEkW,EAAc,IAElB,EACMQ,EAAgB,IAEpBnF,EAAa,UADUoE,CACCgB,CADiBb,OAAO,CAACvZ,MAAM,CAAC,CAACsV,EAAGC,IAAMA,IAAMpV,GAE1E,EACA,MAAO,WAACmB,EAAAA,EAAIA,CAAAA,CAAC9J,UAAU,SAASK,sBAAoB,OAAOJ,wBAAsB,wBAAwBC,0BAAwB,wCAC7H,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,wCACnE,UAACmK,EAAAA,EAASA,CAAAA,CAAChK,sBAAoB,YAAYH,0BAAwB,uCAA8B,yBACjG,UAACoK,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,uCAA8B,uFAE/G,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,YAAYK,sBAAoB,cAAcH,0BAAwB,wCAC3F,WAACH,MAAAA,WACC,WAACA,MAAAA,CAAIC,UAAU,6CACb,UAAC6iB,GAAAA,CAAaA,CAAAA,CAAC7iB,UAAU,wBAAwBK,sBAAoB,gBAAgBH,0BAAwB,gCAC7G,UAACC,EAAAA,CAAKA,CAAAA,CAACE,sBAAoB,QAAQH,0BAAwB,uCAA8B,iBAE1F0hB,EAAkBC,YAAY,CAACnZ,GAAG,CAAC,CAACoa,EAAana,IAAU,WAAC5I,MAAAA,CAAgBC,UAAU,0CACnF,WAACD,MAAAA,CAAIC,UAAU,gCACb,UAACM,EAAAA,CAAKA,CAAAA,CAACE,YAAY,OAAOC,MAAOqiB,EAAYpiB,IAAI,CAAEC,SAAUC,GAAKwhB,EAAkBzZ,EAAO,OAAQ/H,EAAEC,MAAM,CAACJ,KAAK,IACjH,UAACwB,EAAAA,CAAQA,CAAAA,CAACzB,YAAY,cAAcC,MAAOqiB,EAAYC,QAAQ,CAAEpiB,SAAUC,GAAKwhB,EAAkBzZ,EAAO,WAAY/H,EAAEC,MAAM,CAACJ,KAAK,OAErI,UAACQ,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,cAAcqB,KAAK,OAAOpB,QAAS,IAAMkhB,EAAkB3Z,YACzE,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBANmD2I,IAStE,WAAC1H,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QA1DX,CA0DoB4hB,IAzDzCxF,EAAa,eAAgB,IAAIoE,EAAkBC,YAAY,CAAE,CAC/DnhB,KAAM,GACNqiB,SAAU,EACZ,EAAE,CACJ,EAqD2D1iB,sBAAoB,SAASH,0BAAwB,wCACtG,UAAC2J,EAAAA,CAAIA,CAAAA,CAAC7J,UAAU,eAAeK,sBAAoB,OAAOH,0BAAwB,gCAAgC,0BAItH,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACijB,GAAAA,CAAOA,CAAAA,CAACjjB,UAAU,wBAAwBK,sBAAoB,UAAUH,0BAAwB,gCACjG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,cAAcC,sBAAoB,QAAQH,0BAAwB,uCAA8B,iBAEjH,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,cAAcE,MAAOuhB,EAAarhB,SAAUC,GAAKqhB,EAAerhB,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,2BAA2B4d,WAAYxd,IAChI,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChBiE,IAEJ,EAAGliB,sBAAoB,QAAQH,0BAAwB,gCACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAASmhB,EAAaliB,sBAAoB,SAASH,0BAAwB,uCAA8B,cAEjI,UAACH,MAAAA,CAAIC,UAAU,qCACZ4hB,EAAkBE,UAAU,CAACpZ,GAAG,CAAC,CAACwa,EAAUva,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACnGkjB,EACD,UAACjiB,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAMohB,EAAe7Z,YAChH,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHgD2I,SASvE,WAAC5I,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACmjB,GAAAA,CAAQA,CAAAA,CAACnjB,UAAU,wBAAwBK,sBAAoB,WAAWH,0BAAwB,gCACnG,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAaC,sBAAoB,QAAQH,0BAAwB,uCAA8B,gBAEhH,WAACH,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAKA,CAAAA,CAACC,GAAG,aAAaE,MAAOyhB,EAAYvhB,SAAUC,GAAKuhB,EAAcvhB,EAAEC,MAAM,CAACJ,KAAK,EAAGD,YAAY,0BAA0B4d,WAAYxd,IAC5H,SAAS,CAAnBA,EAAEyd,GAAG,GACPzd,EAAE0d,cAAc,GAChBoE,IAEJ,EAAGriB,sBAAoB,QAAQH,0BAAwB,gCACrD,UAACe,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASE,QAASshB,EAAYriB,sBAAoB,SAASH,0BAAwB,uCAA8B,cAEhI,UAACH,MAAAA,CAAIC,UAAU,qCACZ4hB,EAAkBG,OAAO,CAACrZ,GAAG,CAAC,CAAC0a,EAAaza,IAAU,WAACvE,EAAAA,CAAKA,CAAAA,CAAajD,QAAQ,YAAYnB,UAAU,iBACnGojB,EACD,UAACniB,EAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQqB,KAAK,KAAKxC,UAAU,0BAA0BoB,QAAS,IAAMuhB,EAAcha,YAC/G,UAAC9F,EAAAA,CAACA,CAAAA,CAAC7C,UAAU,gBAHgD2I,cAU/E,CCtIO,SAAS0a,GAAkB,CAChClkB,MAAI,UACJC,CAAQ,CACe,EACvB,MAAO,WAAC0K,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOJ,wBAAsB,oBAAoBC,0BAAwB,oCACtG,WAACiK,EAAAA,EAAUA,CAAAA,CAAC9J,sBAAoB,aAAaH,0BAAwB,oCACnE,UAACmK,EAAAA,EAASA,CAAAA,CAAChK,sBAAoB,YAAYH,0BAAwB,mCAA0B,kBAC7F,UAACoK,EAAAA,EAAeA,CAAAA,CAACjK,sBAAoB,kBAAkBH,0BAAwB,mCAA0B,oFAI3G,UAAC6J,EAAAA,EAAWA,CAAAA,CAAC1J,sBAAoB,cAAcH,0BAAwB,mCACrE,WAACojB,GAAAA,EAAIA,CAAAA,CAACC,aAAa,aAAavjB,UAAU,SAASK,sBAAoB,OAAOH,0BAAwB,oCACpG,WAACsjB,GAAAA,EAAQA,CAAAA,CAACxjB,UAAU,0BAA0BK,sBAAoB,WAAWH,0BAAwB,oCACnG,UAACujB,GAAAA,EAAWA,CAAAA,CAAChjB,MAAM,aAAaJ,sBAAoB,cAAcH,0BAAwB,mCAA0B,eACpH,UAACujB,GAAAA,EAAWA,CAAAA,CAAChjB,MAAM,YAAYJ,sBAAoB,cAAcH,0BAAwB,mCAA0B,aACnH,UAACujB,GAAAA,EAAWA,CAAAA,CAAChjB,MAAM,oBAAoBJ,sBAAoB,cAAcH,0BAAwB,mCAA0B,uBAC3H,UAACujB,GAAAA,EAAWA,CAAAA,CAAChjB,MAAM,UAAUJ,sBAAoB,cAAcH,0BAAwB,mCAA0B,UACjH,UAACujB,GAAAA,EAAWA,CAAAA,CAAChjB,MAAM,qBAAqBJ,sBAAoB,cAAcH,0BAAwB,mCAA0B,wBAE9H,WAACH,MAAAA,CAAIC,UAAU,2CAAiC,IAC9C,UAAC0jB,GAAAA,EAAWA,CAAAA,CAACjjB,MAAM,aAAaJ,sBAAoB,cAAcH,0BAAwB,mCACxF,UAAC6c,GAAcA,CAAC5d,KAAMA,EAAMC,GAAb2d,MAAuB3d,EAAUiB,sBAAoB,iBAAiBH,0BAAwB,8BAE/G,UAACwjB,GAAAA,EAAWA,CAAAA,CAACjjB,MAAM,YAAYJ,sBAAoB,cAAcH,0BAAwB,mCACvF,UAACse,GAAaA,CAACrf,KAAMA,EAAMC,EAAbof,OAAuBpf,EAAUiB,sBAAoB,gBAAgBH,0BAAwB,8BAE7G,UAACwjB,GAAAA,EAAWA,CAAAA,CAACjjB,MAAM,oBAAoBJ,sBAAoB,cAAcH,0BAAwB,mCAC/F,UAACmf,GAAoBA,CAAClgB,KAAMA,EAAMC,SAAbigB,EAAiChf,sBAAoB,uBAAuBH,0BAAwB,8BAE3H,UAACwjB,GAAAA,EAAWA,CAAAA,CAACjjB,MAAM,UAAUJ,sBAAoB,cAAcH,0BAAwB,mCACrF,UAACsgB,GAAWA,CAACrhB,KAAMA,EAAPqhB,SAAuBphB,EAAUiB,sBAAoB,cAAcH,0BAAwB,8BAEzG,UAACwjB,GAAAA,EAAWA,CAAAA,CAACjjB,MAAM,qBAAqBJ,sBAAoB,cAAcH,0BAAwB,mCAChG,UAACyhB,GAAqBA,CAACxiB,KAAMA,EAAMC,SAAUA,CAAvBuiB,CAAiCthB,sBAAoB,wBAAwBH,0BAAwB,wCAMzI,CC6DA,IAAMyjB,GAAQ,CAAC,CACbpjB,GAAI,aACJwY,MAAO,kBACP7W,YAAa,oCACf,EAAG,CACD3B,GAAI,mBACJwY,MAAO,iBACP7W,YAAa,qCACf,EAAG,CACD3B,GAAI,mBACJwY,MAAO,mBACP7W,YAAa,gDACf,EAAG,CACD3B,GAAI,iBACJwY,MAAO,qBACP7W,YAAa,sEACf,EAAG,CACD3B,GAAI,aACJwY,MAAO,YACP7W,YAAa,gCACf,EAAE,CAMK,SAAS0hB,GAAqB,YACnCC,CAAU,UACVC,CAAQ,aACRC,CAAW,CACe,EAC1B,GAAM,CAACC,EAAaC,EAAe,CAAG1kB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACzC,CAAC2kB,EAAYC,EAAc,CAAG5kB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAa,CACvDmB,KAAMqjB,GAAarjB,MAAQ,GAC3BwB,YAAa6hB,GAAa7hB,aAAe,GACzCpB,WAAYijB,GAAajjB,YAAc,GACvCC,WAAYgjB,GAAahjB,YAAc,GACvCG,KAAM6iB,GAAa7iB,MAAQ,aAC3B4D,eAAgBif,GAAajf,gBAAkB,OAC/CnF,UAAWokB,GAAapkB,UACxBC,QAASmkB,GAAankB,QACtB+C,WAAYohB,GAAaphB,WACzBP,kBAAmB2hB,GAAa3hB,kBAChCgiB,cAAeL,GAAaK,gBAAiB,EAC7Clf,MAAO6e,GAAa7e,MACpBD,SAAU8e,GAAa9e,UAAY,GACnC+P,YAAa+O,GAAa/O,cAAe,EACzChN,QAAS+b,GAAa/b,SAAW,EAAE,CACnCqc,YAAaN,GAAaM,cAAe,EACzCC,gBAAiBP,GAAaO,iBAAmB,EAAE,CACnDjN,UAAW0M,GAAa1M,UACxB2F,WAAY+G,GAAa/G,YAAc,CACrCC,aAAc,EAAE,CAChBC,oBAAqB,GACrBC,cAAe,EAAE,EAEnBsB,UAAWsF,GAAatF,WAAa,CACnCC,QAAS,EACTC,SAAU,GACVC,WAAY,EAAE,EAEhBU,oBAAqByE,GAAazE,qBAAuB,CACvDC,UAAW,EACXC,eAAgB,EAAE,CAClBC,aAAc,EAAE,EAElBgB,QAASsD,GAAatD,SAAW,CAC/BC,SAAU,EAAE,CACZC,WAAY,EAAE,CACdC,cAAe,EACjB,EACAgB,kBAAmBmC,GAAanC,mBAAqB,CACnDC,aAAc,EAAE,CAChBC,WAAY,EAAE,CACdC,QAAS,EAAE,CAEf,GACM,CAACwC,EAAcC,EAAgB,CAAGjlB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAmC3CklB,EAAmB,IACvBN,EAAcrY,GAAS,EACrB,EADqB,CAClBA,CAAI,CACP,GAAG9G,CAAO,CACZ,EACF,EACM0f,EAAmB,IACvB,OAAQrf,GACN,KAAK,EAEH,IAAMsf,EAAkB,CAAC,CAACT,EAAWxjB,IAAI,EAAI,CAAC,CAACwjB,EAAWhiB,WAAW,EAAI,CAAC,CAACgiB,EAAWpjB,UAAU,EAAI,CAAC,CAACojB,EAAWnjB,UAAU,CAE3H,GAAkC,YAAY,CAA1CmjB,EAAWpf,cAAc,CAC3B,OAAO6f,GAAmB,CAAC,CAACT,EAAWhf,KAAK,EAAIgf,EAAWhf,KAAK,CAAG,GAAK,CAAC,CAACgf,EAAWjf,QAAQ,CAE/F,OAAO0f,CACT,MAAK,EAEH,OAAOT,EAAWlc,OAAO,CAACC,MAAM,CAAG,GAAKic,EAAWlc,OAAO,CAAC4c,KAAK,CAAC/N,GAAU,CAAC,CAACA,EAAOnW,IAAI,EAAImW,EAAO3O,QAAQ,CAACD,MAAM,CAAG,EACvH,MAAK,EAEH,OAAOic,EAAWlc,OAAO,CAAC4c,KAAK,CAAC/N,GAAUA,EAAO3O,QAAQ,CAAC0c,KAAK,CAAC1d,GAAW,CAAC,CAACA,EAAQ6B,OAAO,EAC9F,MAAK,EAGH,IAAM8b,EAAkB,CAAC,CAACX,EAAWlH,UAAU,GAAKkH,CAAAA,CAAWlH,UAAU,CAACC,YAAY,CAAChV,MAAM,CAAG,GAAK,CAAC,CAACic,EAAWlH,UAAU,CAACE,mBAAmB,EAAIgH,EAAWlH,UAAU,CAACG,aAAa,CAAClV,MAAM,EAAG,EAC3L6c,EAAiB,CAAC,CAACZ,EAAWzF,SAAS,GAAKyF,CAAAA,CAAWzF,SAAS,CAACC,OAAO,CAAG,GAAK,CAAC,CAACwF,EAAWzF,SAAS,CAACE,QAAQ,EAAIuF,EAAWzF,SAAS,CAACG,UAAU,CAAC3W,MAAM,EAAG,EAC5J8c,EAAwB,CAAC,CAACb,EAAW5E,mBAAmB,EAAK,GAAE4E,EAAW5E,mBAAmB,CAACC,SAAS,EAAI2E,EAAW5E,mBAAmB,CAACE,cAAc,CAACvX,MAAM,CAAG,GAAKic,EAAW5E,mBAAmB,CAACG,YAAY,CAACxX,MAAM,CAAG,GAC5N+c,EAAe,CAAC,CAACd,EAAWzD,OAAO,GAAKyD,CAAAA,CAAWzD,OAAO,CAACC,QAAQ,CAACzY,MAAM,CAAG,GAAKic,EAAWzD,OAAO,CAACE,UAAU,CAAC1Y,MAAM,CAAG,GAAK,CAAC,CAACic,EAAWzD,OAAO,CAACG,aAAAA,EACnJqE,EAAyB,CAAC,CAACf,EAAWtC,iBAAiB,EAAKsC,EAAAA,CAAWtC,iBAAiB,CAACC,YAAY,CAAC5Z,MAAM,CAAG,GAAKic,EAAWtC,iBAAiB,CAACE,UAAU,CAAC7Z,MAAM,CAAG,GAAKic,EAAWtC,iBAAiB,CAACG,OAAO,CAAC9Z,MAAM,EAAG,EAC9N,OAAO4c,GAAmBC,GAAkBC,GAAyBC,GAAgBC,CACvF,MAAK,EAEH,OAAO,CAET,SACE,OAAO,CACX,CACF,EACMC,EAAmB,IAChBR,EAAiBV,GAYpBmB,EAAiB,UACrBX,GAAgB,GAChB,GAAI,CACF,MAAMX,EAAWK,EACnB,CAAE,MAAOzgB,EAAO,CACdsP,QAAQtP,KAAK,CAAC,yBAA0BA,EAC1C,QAAU,CACR+gB,EAAgB,GAClB,CACF,EAiBMY,EAAqB,IAAe,EAAKzB,GAAM1b,MAAM,CAAG,IAC9D,MAAO,WAAClI,MAAAA,CAAIC,UAAU,uBAAuBC,wBAAsB,uBAAuBC,0BAAwB,uCAE9G,UAAC4J,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOH,0BAAwB,sCACvD,WAAC6J,EAAAA,EAAWA,CAAAA,CAAC/J,UAAU,OAAOK,sBAAoB,cAAcH,0BAAwB,uCACtF,UAACH,MAAAA,CAAIC,UAAU,+EACZ2jB,GAAMjb,GAAG,CAAC,CAACrD,EAAMsD,IAAU,WAAC5I,MAAAA,CAAkBC,UAAU,iDACrD,UAACD,MAAAA,CAAIC,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6GAA8GiD,IAAUqb,EAAc,qCAAuC,iCAAkCrb,EAAQqb,GAAe,0BAA0B,UAEhRrb,EAAQqb,EAAc,GAF2Q,EAE3Q,KAACqB,EAAAA,CAAKA,CAAAA,CAACrlB,UAAU,YAAe2I,EAAQ,IAEjE,UAAC9D,OAAAA,CAAK7E,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6CAA8CiD,IAAUqb,EAAc,2BAA6B,kCACpH3e,EAAK0T,KAAK,KANqB1T,EAAK9E,EAAE,KAU/C,UAACoc,EAAAA,SAASA,CAAAA,CAAC3c,UAAU,OAAOK,sBAAoB,YAAYH,0BAAwB,+BACpF,WAACH,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,yCACb,WAAC6E,OAAAA,WAAK,WAASmf,EAAc,EAAE,SAAOL,GAAM1b,MAAM,IAClD,WAACpD,OAAAA,WAAMtD,KAAK2V,KAAK,CAACkO,GAAoB,kBAExC,UAAC1I,EAAAA,CAAQA,CAAAA,CAACjc,MAAO2kB,EAAoBplB,UAAU,MAAMK,sBAAoB,WAAWH,0BAAwB,uCAMlH,UAAC4J,EAAAA,EAAIA,CAAAA,CAACzJ,sBAAoB,OAAOH,0BAAwB,sCACvD,UAAC6J,EAAAA,EAAWA,CAAAA,CAAC1J,sBAAoB,cAAcH,0BAAwB,sCACpEolB,CA9CiB,KACxB,OAAQtB,GACN,KAAK,EACH,MAAO,UAAC9kB,EAAaA,CAACC,KAAM+kB,EAAY9kB,GAAnBF,MAA6BulB,GACpD,MAAK,EACH,MAAO,UAAChe,EAAmBA,CAACtH,KAAM+kB,EAAY9kB,SAAnBqH,GAC7B,MAAK,EACH,MAAO,UAACyN,GAAmBA,CAAC/U,KAAM+kB,EAAY9kB,QAAnB8U,CAA6BuQ,GAC1D,MAAK,EACH,MAAO,UAACpB,GAAiBA,CAAClkB,KAAM+kB,EAAY9kB,MAAnBikB,GAA6BoB,GACxD,MAAK,EACH,MAAO,UAACjK,GAAcA,CAACrb,KAAM+kB,EAAYzJ,GAAnBD,OAA8B2K,EAAgBzK,aAAc6J,GACpF,SACE,OAAO,IACX,EACF,QAoCI,WAACxkB,MAAAA,CAAIC,UAAU,iCACb,WAACiB,EAAAA,CAAMA,CAAAA,CAACE,QAAQ,UAAUC,QAnET,CAmEkBmkB,IAlEnCvB,EAAc,GAAG,EACJA,EAAc,EAEjC,EA+DyDliB,SAA0B,IAAhBkiB,EAAmB3jB,sBAAoB,SAASH,0BAAwB,uCACnI,UAACslB,EAAAA,CAAWA,CAAAA,CAACxlB,UAAU,eAAeK,sBAAoB,cAAcH,0BAAwB,+BAA+B,gBAIjI,UAACH,MAAAA,CAAIC,UAAU,0BACZgkB,IAAgBL,GAAM1b,MAAM,CAAG,EAAI,UAAChH,EAAAA,CAAMA,CAAAA,CAACG,QAAS+jB,EAAgBrjB,SAAU,CAACojB,KAAsBX,WACjGA,EAAe,oBAAsB,0BAC5B,WAACtjB,EAAAA,CAAMA,CAAAA,CAACG,QAhFX,CAgFoBqkB,IA/EjCzB,EAAcL,GAAM1b,MAAM,CAAG,GAAG,EACnB+b,EAAc,EAEjC,EA4EmDliB,SAAU,CAACojB,cAAoB,cAEtE,UAAC9Z,EAAAA,CAAYA,CAAAA,CAACpL,UAAU,2BAKtC,gGC9VA,IAAM0c,EAAW5C,EAAAA,UAAgB,CAAiH,CAAC,WACjJ9Z,CAAS,OACTS,CAAK,CACL,GAAG6F,EACJ,CAAEpD,IAAQ,UAACwiB,EAAAA,EAAsB,EAACxiB,IAAKA,EAAKlD,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiE1F,GAAa,GAAGsG,CAAK,UAC7I,UAACof,EAAAA,EAA2B,EAAC1lB,UAAU,iDAAiD2lB,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,KAAOnlB,CAAAA,EAAS,GAAG,EAAE,CAAC,OAGpDic,EAASmJ,WAAW,CAAGH,EAAAA,EAAsB,CAACG,WAAW,oHCXzD,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,oOAAqO,CAC7PC,SAAU,CACR7kB,QAAS,CACP8kB,QAAS,+BACTC,YAAa,mGACf,CACF,EACAC,gBAAiB,CACfhlB,QAAS,SACX,CACF,GACA,SAAS0b,EAAM,WACb7c,CAAS,SACTmB,CAAO,CACP,GAAGmF,EAC8D,EACjE,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,QAAQ4f,KAAK,QAAQpmB,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACogB,EAAc,SACrE3kB,CACF,GAAInB,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,QAAQC,0BAAwB,aACnF,CACA,SAASmmB,EAAW,WAClBrmB,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,cAAcxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8DAA+D1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,aAAaC,0BAAwB,aACrM,CACA,SAAS4c,EAAiB,WACxB9c,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,oBAAoBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iGAAkG1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,mBAAmBC,0BAAwB,aACpP,qIChCA,SAAS4J,EAAK,WACZ9J,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,OAAOxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqF1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASiK,EAAW,WAClBnK,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,cAAcxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8J1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASmK,EAAU,WACjBrK,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,aAAaxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8B1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASoK,EAAgB,CACvBtK,WAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,mBAAmBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiC1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAAS6J,EAAY,WACnB/J,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,eAAexG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQ1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASomB,EAAW,WAClBtmB,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,cAAcxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2C1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,aAAaC,0BAAwB,YACjL,6ECvCe,SAASqmB,EAAc,UACpClO,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,oHCTA,SAASlY,EAAM,WACbH,CAAS,CACT,GAAGsG,EAC8C,EACjD,MAAO,UAACkgB,EAAAA,CAAmB,EAAChgB,YAAU,QAAQxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sNAAuN1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,sBAAsBJ,wBAAsB,QAAQC,0BAAwB,aAC5Y,mBCVA,uCAAqK,4HCKrK,IAAMojB,EAAOmD,EAAAA,EAAkB,CACzBjD,EAAW1J,EAAAA,UAAgB,CAAyG,CAAC,WACzI9Z,CAAS,CACT,GAAGsG,EACJ,CAAEpD,IAAQ,UAACujB,EAAAA,EAAkB,EAACvjB,IAAKA,EAAKlD,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6FAA8F1F,GAAa,GAAGsG,CAAK,IAC1Kkd,EAASqC,WAAW,CAAGY,EAAAA,EAAkB,CAACZ,WAAW,CACrD,IAAMpC,EAAc3J,EAAAA,UAAgB,CAA+G,CAAC,WAClJ9Z,CAAS,CACT,GAAGsG,EACJ,CAAEpD,IAAQ,UAACujB,EAAAA,EAAqB,EAACvjB,IAAKA,EAAKlD,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qZAAsZ1F,GAAa,GAAGsG,CAAK,IACremd,EAAYoC,WAAW,CAAGY,EAAAA,EAAqB,CAACZ,WAAW,CAC3D,IAAMnC,EAAc5J,EAAAA,UAAgB,CAA+G,CAAC,WAClJ9Z,CAAS,CACT,GAAGsG,EACJ,CAAEpD,IAAQ,UAACujB,EAAAA,EAAqB,EAACvjB,IAAKA,EAAKlD,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kIAAmI1F,GAAa,GAAGsG,CAAK,IAClNod,EAAYmC,WAAW,CAAGY,EAAAA,EAAqB,CAACZ,WAAW,uFClB3D,SAAS5jB,EAAS,WAChBjC,CAAS,CACT,GAAGsG,EAC8B,EACjC,MAAO,UAACogB,WAAAA,CAASlgB,YAAU,WAAWxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAuc1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,WAAWC,0BAAwB,gBAC7kB,oCCYI,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,CACvB,iBAAiB,iBACjB,UACA,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CA7BLymB,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,EAOF,KA7DiD,EA+D/C,EAA2B,CAlBN,IASL,iBASQ,wFC1E9B,SAASrhB,EAAS,WAChBtF,CAAS,CACT,GAAGsG,EACiD,EACpD,MAAO,UAACsgB,EAAAA,EAAsB,EAACpgB,YAAU,WAAWxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8eAA+e1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,yBAAyBJ,wBAAsB,WAAWC,0BAAwB,wBAC1qB,UAAC0mB,EAAAA,EAA2B,EAACpgB,YAAU,qBAAqBxG,UAAU,gEAAgEK,sBAAoB,8BAA8BH,0BAAwB,wBAC9M,UAAC2mB,EAAAA,CAASA,CAAAA,CAAC7mB,UAAU,WAAWK,sBAAoB,YAAYH,0BAAwB,oBAGhG,mBCfA,uCAAqK,sLCMrK,SAASuK,EAAY,CACnB,GAAGnE,EACoD,EACvD,MAAO,UAACwgB,EAAAA,EAAyB,EAACtgB,YAAU,eAAgB,GAAGF,CAAK,CAAEjG,sBAAoB,4BAA4BJ,wBAAsB,cAAcC,0BAAwB,oBACpL,CACA,SAASwK,EAAmB,CAC1B,GAAGpE,EACuD,EAC1D,MAAO,UAACwgB,EAAAA,EAA4B,EAACtgB,YAAU,uBAAwB,GAAGF,CAAK,CAAEjG,sBAAoB,+BAA+BJ,wBAAsB,qBAAqBC,0BAAwB,oBACzM,CACA,SAAS6mB,EAAkB,CACzB,GAAGzgB,EACsD,EACzD,MAAO,UAACwgB,EAAAA,EAA2B,EAACtgB,YAAU,sBAAuB,GAAGF,CAAK,CAAEjG,sBAAoB,8BAA8BJ,wBAAsB,oBAAoBC,0BAAwB,oBACrM,CACA,SAAS8mB,EAAmB,WAC1BhnB,CAAS,CACT,GAAGsG,EACuD,EAC1D,MAAO,UAACwgB,EAAAA,EAA4B,EAACtgB,YAAU,uBAAuBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0J1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,+BAA+BJ,wBAAsB,qBAAqBC,0BAAwB,oBAC7X,CACA,SAAS0K,EAAmB,WAC1B5K,CAAS,CACT,GAAGsG,EACuD,EAC1D,MAAO,WAACygB,EAAAA,CAAkB1mB,sBAAoB,oBAAoBJ,wBAAsB,qBAAqBC,0BAAwB,6BACjI,UAAC8mB,EAAAA,CAAmB3mB,sBAAoB,qBAAqBH,0BAAwB,qBACrF,UAAC4mB,EAAAA,EAA4B,EAACtgB,YAAU,uBAAuBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+W1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,+BAA+BH,0BAAwB,uBAEpiB,CACA,SAAS2K,EAAkB,WACzB7K,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,sBAAsBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgD1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,oBAAoBC,0BAAwB,oBACrM,CACA,SAAS8K,EAAkB,WACzBhL,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,sBAAsBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0D1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,oBAAoBC,0BAAwB,oBAC/M,CACA,SAAS4K,EAAiB,WACxB9K,CAAS,CACT,GAAGsG,EACqD,EACxD,MAAO,UAACwgB,EAAAA,EAA0B,EAACtgB,YAAU,qBAAqBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,wBAAyB1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,6BAA6BJ,wBAAsB,mBAAmBC,0BAAwB,oBACpP,CACA,SAAS6K,EAAuB,CAC9B/K,WAAS,CACT,GAAGsG,EAC2D,EAC9D,MAAO,UAACwgB,EAAAA,EAAgC,EAACtgB,YAAU,2BAA2BxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiC1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,mCAAmCJ,wBAAsB,yBAAyBC,0BAAwB,oBACpR,CACA,SAASgL,EAAkB,CACzBlL,WAAS,CACT,GAAGsG,EACsD,EACzD,MAAO,UAACwgB,EAAAA,EAA2B,EAAC9mB,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuhB,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,GAAIjnB,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,8BAA8BJ,wBAAsB,oBAAoBC,0BAAwB,oBACjN,CACA,SAAS+K,EAAkB,WACzBjL,CAAS,CACT,GAAGsG,EACsD,EACzD,MAAO,UAACwgB,EAAAA,EAA2B,EAAC9mB,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuhB,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC,CAC/D9lB,QAAS,SACX,GAAInB,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,8BAA8BJ,wBAAsB,oBAAoBC,0BAAwB,oBACjJ,qKCnEA,SAASqL,EAAO,CACd,GAAGjF,EAC+C,EAClD,MAAO,UAAC4gB,EAAAA,EAAoB,EAAC1gB,YAAU,SAAU,GAAGF,CAAK,CAAEjG,sBAAoB,uBAAuBJ,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASinB,EAAc,CACrB,GAAG7gB,EACkD,EACrD,MAAO,UAAC4gB,EAAAA,EAAuB,EAAC1gB,YAAU,iBAAkB,GAAGF,CAAK,CAAEjG,sBAAoB,0BAA0BJ,wBAAsB,gBAAgBC,0BAAwB,cACpL,CACA,SAASknB,EAAa,CACpB,GAAG9gB,EACiD,EACpD,MAAO,UAAC4gB,EAAAA,EAAsB,EAAC1gB,YAAU,gBAAiB,GAAGF,CAAK,CAAEjG,sBAAoB,yBAAyBJ,wBAAsB,eAAeC,0BAAwB,cAChL,CAMA,SAASmnB,EAAc,WACrBrnB,CAAS,CACT,GAAGsG,EACkD,EACrD,MAAO,UAAC4gB,EAAAA,EAAuB,EAAC1gB,YAAU,iBAAiBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0J1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,0BAA0BJ,wBAAsB,gBAAgBC,0BAAwB,cACxW,CACA,SAASwL,EAAc,WACrB1L,CAAS,UACTqY,CAAQ,CACR,GAAG/R,EACkD,EACrD,MAAO,WAAC8gB,EAAAA,CAAa5gB,YAAU,gBAAgBnG,sBAAoB,eAAeJ,wBAAsB,gBAAgBC,0BAAwB,uBAC5I,UAACmnB,EAAAA,CAAchnB,sBAAoB,gBAAgBH,0BAAwB,eAC3E,WAACgnB,EAAAA,EAAuB,EAAC1gB,YAAU,iBAAiBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+W1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,0BAA0BH,0BAAwB,uBAC3gBmY,EACD,WAAC6O,EAAAA,EAAqB,EAAClnB,UAAU,oWAAoWK,sBAAoB,wBAAwBH,0BAAwB,uBACvc,UAAConB,EAAAA,CAAKA,CAAAA,CAACjnB,sBAAoB,QAAQH,0BAAwB,eAC3D,UAAC2E,OAAAA,CAAK7E,UAAU,mBAAU,kBAIpC,CACA,SAAS2L,EAAa,WACpB3L,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,gBAAgBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgD1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,eAAeC,0BAAwB,cAC1L,CACA,SAAS6L,EAAa,WACpB/L,CAAS,CACT,GAAGsG,EACyB,EAC5B,MAAO,UAACvG,MAAAA,CAAIyG,YAAU,gBAAgBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0D1F,GAAa,GAAGsG,CAAK,CAAErG,wBAAsB,eAAeC,0BAAwB,cACpM,CACA,SAAS0L,EAAY,WACnB5L,CAAS,CACT,GAAGsG,EACgD,EACnD,MAAO,UAAC4gB,EAAAA,EAAqB,EAAC1gB,YAAU,eAAexG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsC1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,wBAAwBJ,wBAAsB,cAAcC,0BAAwB,cAC5O,CACA,SAAS2L,EAAkB,WACzB7L,CAAS,CACT,GAAGsG,EACsD,EACzD,MAAO,UAAC4gB,EAAAA,EAA2B,EAAC1gB,YAAU,qBAAqBxG,UAAW0F,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiC1F,GAAa,GAAGsG,CAAK,CAAEjG,sBAAoB,8BAA8BJ,wBAAsB,oBAAoBC,0BAAwB,cAC/P", "sources": ["webpack://terang-lms-ui/./src/components/course/steps/basic-info-step.tsx", "webpack://terang-lms-ui/./src/components/ui/switch.tsx", "webpack://terang-lms-ui/./src/components/course/steps/module-structure-step.tsx", "webpack://terang-lms-ui/./src/components/tiptap-editor.tsx", "webpack://terang-lms-ui/./src/components/dynamic-content-editor.tsx", "webpack://terang-lms-ui/./src/components/course/steps/content-creation-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/publishing-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/admissions-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/academics-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/tuition-financing-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/careers-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/student-experience-step.tsx", "webpack://terang-lms-ui/./src/components/course/steps/course-details-step.tsx", "webpack://terang-lms-ui/./src/components/course/course-creation-wizard.tsx", "webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/./src/components/ui/alert.tsx", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/./src/app/dashboard/teacher/layout.tsx", "webpack://terang-lms-ui/./src/components/ui/label.tsx", "webpack://terang-lms-ui/?15fa", "webpack://terang-lms-ui/./src/components/ui/tabs.tsx", "webpack://terang-lms-ui/./src/components/ui/textarea.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/./src/components/ui/checkbox.tsx", "webpack://terang-lms-ui/?69ba", "webpack://terang-lms-ui/./src/components/ui/alert-dialog.tsx", "webpack://terang-lms-ui/./src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { format } from 'date-fns';\nimport { id } from 'date-fns/locale';\nimport { CalendarIcon, Upload, X, Shuffle, Info } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { CourseData } from '../course-creation-wizard';\nimport { toast } from 'sonner';\ninterface BasicInfoStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function BasicInfoStep({\n  data,\n  onUpdate\n}: BasicInfoStepProps) {\n  const [isGeneratingCode, setIsGeneratingCode] = useState(false);\n  const [dateRangeEnabled, setDateRangeEnabled] = useState(Boolean(data.startDate || data.endDate));\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const generateCourseCode = () => {\n    setIsGeneratingCode(true);\n    // Simulate API call\n    setTimeout(() => {\n      const code = Math.random().toString(36).substring(2, 8).toUpperCase();\n      onUpdate({\n        courseCode: code\n      });\n      setIsGeneratingCode(false);\n      toast.success('Kode course berhasil dibuat');\n    }, 1000);\n  };\n  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    // Validate file type\n    if (!file.type.startsWith('image/')) {\n      toast.error('File harus berupa gambar');\n      return;\n    }\n\n    // Validate file size (max 5MB)\n    if (file.size > 5 * 1024 * 1024) {\n      toast.error('Ukuran file maksimal 5MB');\n      return;\n    }\n\n    // Create preview URL\n    const previewUrl = URL.createObjectURL(file);\n    onUpdate({\n      coverImage: file,\n      coverImagePreview: previewUrl\n    });\n    toast.success('Gambar berhasil diunggah');\n  };\n  const removeCoverImage = () => {\n    if (data.coverImagePreview) {\n      URL.revokeObjectURL(data.coverImagePreview);\n    }\n    onUpdate({\n      coverImage: undefined,\n      coverImagePreview: undefined\n    });\n  };\n  const handleEnrollmentTypeChange = (value: 'code' | 'invitation' | 'both' | 'purchase') => {\n    const updates: Partial<CourseData> = {\n      enrollmentType: value\n    };\n\n    // Auto-set default currency when switching to purchase/both\n    if ((value === 'purchase' || value === 'both') && !data.currency) {\n      updates.currency = 'IDR';\n    }\n    onUpdate(updates);\n  };\n  const handleDateRangeToggle = (checked: boolean) => {\n    setDateRangeEnabled(checked);\n    if (!checked) {\n      onUpdate({\n        startDate: null,\n        endDate: null\n      });\n    }\n  };\n  return <div className=\"space-y-6\" data-sentry-component=\"BasicInfoStep\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n      {/* Course Name */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"courseName\" data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Nama Course *</Label>\r\n        <Input id=\"courseName\" placeholder=\"Masukkan nama course\" value={data.name} onChange={e => onUpdate({\n        name: e.target.value\n      })} data-sentry-element=\"Input\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n      </div>\r\n\r\n      {/* Instructor */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"instructor\" data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Nama Instruktur *</Label>\r\n        <Input id=\"instructor\" placeholder=\"Masukkan nama instruktur\" value={data.instructor} onChange={e => onUpdate({\n        instructor: e.target.value\n      })} data-sentry-element=\"Input\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n      </div>\r\n\r\n      {/* Course Code */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"courseCode\" data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Kode Course *</Label>\r\n        <div className=\"flex space-x-2\">\r\n          <Input id=\"courseCode\" placeholder=\"Kode unik untuk course\" value={data.courseCode} onChange={e => onUpdate({\n          courseCode: e.target.value.toUpperCase()\n        })} className=\"flex-1\" data-sentry-element=\"Input\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n          <Button type=\"button\" variant=\"outline\" onClick={generateCourseCode} disabled={isGeneratingCode} data-sentry-element=\"Button\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n            <Shuffle className=\"w-4 h-4 mr-2\" data-sentry-element=\"Shuffle\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n            {isGeneratingCode ? 'Membuat...' : 'Generate'}\r\n          </Button>\r\n        </div>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Kode ini akan digunakan siswa untuk mendaftar ke course\r\n        </p>\r\n      </div>\r\n\r\n      {/* Description */}\r\n      <div className=\"space-y-2\">\r\n        <Label htmlFor=\"description\" data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Deskripsi Course *</Label>\r\n        <Textarea id=\"description\" placeholder=\"Jelaskan tentang course ini...\" value={data.description} onChange={e => onUpdate({\n        description: e.target.value\n      })} rows={4} data-sentry-element=\"Textarea\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n      </div>\r\n\r\n      {/* Cover Image */}\r\n      <div className=\"space-y-2\">\r\n        <Label data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Cover Image</Label>\r\n        {data.coverImagePreview ? <div className=\"relative\">\r\n            <img src={data.coverImagePreview} alt=\"Course cover\" className=\"w-full h-auto object-cover rounded-md aspect-video\" />\r\n            <Button type=\"button\" variant=\"destructive\" size=\"sm\" className=\"absolute top-2 right-2\" onClick={removeCoverImage}>\r\n              <X className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div> : <div className=\"border-2 border-dashed border-muted-foreground/25 rounded-md p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors aspect-video flex flex-col items-center justify-center\" onClick={() => fileInputRef.current?.click()}>\r\n            <Upload className=\"w-8 h-8 mx-auto mb-2 text-muted-foreground\" />\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Klik untuk upload cover image\r\n            </p>\r\n            <p className=\"text-xs text-muted-foreground mt-1\">\r\n              PNG, JPG hingga 5MB\r\n            </p>\r\n          </div>}\r\n        <input ref={fileInputRef} type=\"file\" accept=\"image/*\" onChange={handleImageUpload} className=\"hidden\" />\r\n      </div>\r\n\r\n      {/* Course Type */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Label data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Tipe Course *</Label>\r\n          <Popover data-sentry-element=\"Popover\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n            <PopoverTrigger asChild data-sentry-element=\"PopoverTrigger\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-auto p-1\" data-sentry-element=\"Button\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n                <Info className=\"h-4 w-4 text-muted-foreground\" data-sentry-element=\"Info\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n              </Button>\r\n            </PopoverTrigger>\r\n            <PopoverContent className=\"w-96\" align=\"start\" data-sentry-element=\"PopoverContent\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"space-y-4\">\r\n                <h4 className=\"font-medium text-sm\">Informasi Tipe Course</h4>\r\n                <div className=\"grid grid-cols-1 gap-4\">\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Badge variant=\"secondary\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Self-paced</Badge>\r\n                    </div>\r\n                    <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                      <li>• Siswa belajar dengan kecepatan sendiri</li>\r\n                      <li>• Tidak ada deadline ketat</li>\r\n                      <li>• Akses selamanya setelah enrollment</li>\r\n                      <li>• Cocok untuk pembelajaran mandiri</li>\r\n                    </ul>\r\n                  </div>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Badge variant=\"default\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Verified</Badge>\r\n                    </div>\r\n                    <ul className=\"text-xs text-muted-foreground space-y-1\">\r\n                      <li>• Course dengan jadwal dan deadline</li>\r\n                      <li>• Sertifikat resmi setelah selesai</li>\r\n                      <li>• Monitoring progress lebih ketat</li>\r\n                      <li>• Cocok untuk pembelajaran formal</li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </PopoverContent>\r\n          </Popover>\r\n        </div>\r\n        <Select value={data.type} onValueChange={(value: 'self_paced' | 'verified') => onUpdate({\n        type: value\n      })} data-sentry-element=\"Select\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n          <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n            <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n          </SelectTrigger>\r\n          <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n            <SelectItem value=\"self_paced\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"secondary\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Self-paced</Badge>\r\n                <span>Siswa belajar dengan kecepatan sendiri</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"verified\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"default\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Verified</Badge>\r\n                <span>Course dengan jadwal dan deadline</span>\r\n              </div>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Enrollment Type */}\r\n      <div className=\"space-y-2\">\r\n        <Label data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Tipe Pendaftaran *</Label>\r\n        <Select value={data.enrollmentType} onValueChange={handleEnrollmentTypeChange} data-sentry-element=\"Select\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n          <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n            <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n          </SelectTrigger>\r\n          <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n            <SelectItem value=\"code\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Kode</Badge>\r\n                <span>Siswa mendaftar dengan kode</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"invitation\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Undangan</Badge>\r\n                <span>Hanya dengan undangan</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"both\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"outline\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Keduanya</Badge>\r\n                <span>Kode atau undangan</span>\r\n              </div>\r\n            </SelectItem>\r\n            <SelectItem value=\"purchase\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"basic-info-step.tsx\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Badge variant=\"default\" data-sentry-element=\"Badge\" data-sentry-source-file=\"basic-info-step.tsx\">Berbayar</Badge>\r\n                <span>Siswa harus membeli</span>\r\n              </div>\r\n            </SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      {/* Price and Currency (only for purchase/both enrollment type) */}\r\n      {(data.enrollmentType === 'purchase' || data.enrollmentType === 'both') && <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <div className=\"space-y-2 md:col-span-2\">\r\n            <Label htmlFor=\"price\">Harga *</Label>\r\n            <Input id=\"price\" type=\"number\" placeholder=\"0\" value={data.price || ''} onChange={e => onUpdate({\n          price: parseFloat(e.target.value) || 0\n        })} min=\"0\" step=\"1000\" />\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label>Mata Uang *</Label>\r\n            <Select value={data.currency || 'IDR'} onValueChange={value => onUpdate({\n          currency: value\n        })}>\r\n              <SelectTrigger>\r\n                <SelectValue />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"IDR\">IDR (Rupiah)</SelectItem>\r\n                <SelectItem value=\"USD\">USD (Dollar)</SelectItem>\r\n                <SelectItem value=\"EUR\">EUR (Euro)</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>}\r\n\r\n      {/* Date Range */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Checkbox id=\"enableDateRange\" checked={dateRangeEnabled} onCheckedChange={handleDateRangeToggle} data-sentry-element=\"Checkbox\" data-sentry-source-file=\"basic-info-step.tsx\" />\r\n          <Label htmlFor=\"enableDateRange\" data-sentry-element=\"Label\" data-sentry-source-file=\"basic-info-step.tsx\">Atur Tanggal Mulai & Selesai</Label>\r\n        </div>\r\n        {dateRangeEnabled && <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label>Tanggal Mulai</Label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button variant=\"outline\" className={cn(\"w-full justify-start text-left font-normal\", !data.startDate && \"text-muted-foreground\")}>\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {data.startDate ? format(data.startDate, \"PPP\", {\n                  locale: id\n                }) : \"Pilih tanggal mulai\"}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                  <Calendar mode=\"single\" selected={data.startDate || undefined} onSelect={date => onUpdate({\n                startDate: date\n              })} disabled={date => date < new Date()} initialFocus />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <Label>Tanggal Selesai</Label>\r\n              <Popover>\r\n                <PopoverTrigger asChild>\r\n                  <Button variant=\"outline\" className={cn(\"w-full justify-start text-left font-normal\", !data.endDate && \"text-muted-foreground\")}>\r\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n                    {data.endDate ? format(data.endDate, \"PPP\", {\n                  locale: id\n                }) : \"Pilih tanggal selesai\"}\r\n                  </Button>\r\n                </PopoverTrigger>\r\n                <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                  <Calendar mode=\"single\" selected={data.endDate || undefined} onSelect={date => onUpdate({\n                endDate: date\n              })} disabled={date => Boolean(date < new Date() || data.startDate && date <= data.startDate)} initialFocus />\r\n                </PopoverContent>\r\n              </Popover>\r\n            </div>\r\n          </div>}\r\n      </div>\r\n\r\n\r\n    </div>;\n}", "'use client';\n\nimport * as React from 'react';\nimport * as SwitchPrimitive from '@radix-ui/react-switch';\nimport { cn } from '@/lib/utils';\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return <SwitchPrimitive.Root data-slot='switch' className={cn('peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50', className)} {...props} data-sentry-element=\"SwitchPrimitive.Root\" data-sentry-component=\"Switch\" data-sentry-source-file=\"switch.tsx\">\r\n      <SwitchPrimitive.Thumb data-slot='switch-thumb' className={cn('bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0')} data-sentry-element=\"SwitchPrimitive.Thumb\" data-sentry-source-file=\"switch.tsx\" />\r\n    </SwitchPrimitive.Root>;\n}\nexport { Switch };", "'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Switch } from '@/components/ui/switch';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';\nimport { Plus, GripVertical, Edit, Trash2, <PERSON><PERSON><PERSON>, FileText, HelpCircle, ChevronDown, ChevronRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { CourseData, ModuleData, ChapterData } from '../course-creation-wizard';\nimport { toast } from 'sonner';\ninterface ModuleStructureStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function ModuleStructureStep({\n  data,\n  onUpdate\n}: ModuleStructureStepProps) {\n  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());\n  const [editingModule, setEditingModule] = useState<ModuleData | null>(null);\n  const [editingChapter, setEditingChapter] = useState<{\n    moduleId: string;\n    chapter: ChapterData | null;\n  }>({\n    moduleId: '',\n    chapter: null\n  });\n  const [isModuleDialogOpen, setIsModuleDialogOpen] = useState(false);\n  const [isChapterDialogOpen, setIsChapterDialogOpen] = useState(false);\n  const toggleModuleExpansion = (moduleId: string) => {\n    const newExpanded = new Set(expandedModules);\n    if (newExpanded.has(moduleId)) {\n      newExpanded.delete(moduleId);\n    } else {\n      newExpanded.add(moduleId);\n    }\n    setExpandedModules(newExpanded);\n  };\n  const createNewModule = () => {\n    const newModule: ModuleData = {\n      id: `module-${Date.now()}`,\n      name: '',\n      description: '',\n      orderIndex: data.modules.length,\n      chapters: [],\n      hasModuleQuiz: false\n    };\n    setEditingModule(newModule);\n    setIsModuleDialogOpen(true);\n  };\n  const editModule = (moduleItem: ModuleData) => {\n    setEditingModule({\n      ...moduleItem\n    });\n    setIsModuleDialogOpen(true);\n  };\n  const saveModule = () => {\n    if (!editingModule || !editingModule.name.trim()) {\n      toast.error('Nama modul harus diisi');\n      return;\n    }\n    const updatedModules = [...data.modules];\n    const existingIndex = updatedModules.findIndex(m => m.id === editingModule.id);\n    if (existingIndex >= 0) {\n      updatedModules[existingIndex] = editingModule;\n      toast.success('Modul berhasil diperbarui');\n    } else {\n      updatedModules.push(editingModule);\n      toast.success('Modul berhasil ditambahkan');\n    }\n    onUpdate({\n      modules: updatedModules\n    });\n    setIsModuleDialogOpen(false);\n    setEditingModule(null);\n  };\n  const deleteModule = (moduleId: string) => {\n    const updatedModules = data.modules.filter(m => m.id !== moduleId).map((m, index) => ({\n      ...m,\n      orderIndex: index\n    }));\n    onUpdate({\n      modules: updatedModules\n    });\n    toast.success('Modul berhasil dihapus');\n  };\n  const createNewChapter = (moduleId: string) => {\n    const moduleItem = data.modules.find(m => m.id === moduleId);\n    if (!moduleItem) return;\n    const newChapter: ChapterData = {\n      id: `chapter-${Date.now()}`,\n      name: '',\n      content: [],\n      orderIndex: moduleItem.chapters.length,\n      hasChapterQuiz: false\n    };\n    setEditingChapter({\n      moduleId,\n      chapter: newChapter\n    });\n    setIsChapterDialogOpen(true);\n  };\n  const editChapter = (moduleId: string, chapter: ChapterData) => {\n    setEditingChapter({\n      moduleId,\n      chapter: {\n        ...chapter\n      }\n    });\n    setIsChapterDialogOpen(true);\n  };\n  const saveChapter = () => {\n    if (!editingChapter.chapter || !editingChapter.chapter.name.trim()) {\n      toast.error('Nama chapter harus diisi');\n      return;\n    }\n    const updatedModules = data.modules.map(moduleItem => {\n      if (moduleItem.id === editingChapter.moduleId) {\n        const updatedChapters = [...moduleItem.chapters];\n        const existingIndex = updatedChapters.findIndex(c => c.id === editingChapter.chapter!.id);\n        if (existingIndex >= 0) {\n          updatedChapters[existingIndex] = editingChapter.chapter!;\n        } else {\n          updatedChapters.push(editingChapter.chapter!);\n        }\n        return {\n          ...moduleItem,\n          chapters: updatedChapters\n        };\n      }\n      return moduleItem;\n    });\n    onUpdate({\n      modules: updatedModules\n    });\n    setIsChapterDialogOpen(false);\n    setEditingChapter({\n      moduleId: '',\n      chapter: null\n    });\n    toast.success('Chapter berhasil disimpan');\n  };\n  const deleteChapter = (moduleId: string, chapterId: string) => {\n    const updatedModules = data.modules.map(moduleItem => {\n      if (moduleItem.id === moduleId) {\n        const updatedChapters = moduleItem.chapters.filter(c => c.id !== chapterId).map((c, index) => ({\n          ...c,\n          orderIndex: index\n        }));\n        return {\n          ...moduleItem,\n          chapters: updatedChapters\n        };\n      }\n      return moduleItem;\n    });\n    onUpdate({\n      modules: updatedModules\n    });\n    toast.success('Chapter berhasil dihapus');\n  };\n  const moveModule = (moduleId: string, direction: 'up' | 'down') => {\n    const currentIndex = data.modules.findIndex(m => m.id === moduleId);\n    if (currentIndex === -1) return;\n    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;\n    if (newIndex < 0 || newIndex >= data.modules.length) return;\n    const updatedModules = [...data.modules];\n    [updatedModules[currentIndex], updatedModules[newIndex]] = [updatedModules[newIndex], updatedModules[currentIndex]];\n\n    // Update order indices\n    updatedModules.forEach((moduleItem, index) => {\n      moduleItem.orderIndex = index;\n    });\n    onUpdate({\n      modules: updatedModules\n    });\n  };\n  return <div className=\"space-y-6\" data-sentry-component=\"ModuleStructureStep\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold\">Struktur Modul Course</h3>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Buat modul dan chapter untuk mengorganisir konten course\r\n          </p>\r\n        </div>\r\n        <Button onClick={createNewModule} data-sentry-element=\"Button\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n          <Plus className=\"w-4 h-4 mr-2\" data-sentry-element=\"Plus\" data-sentry-source-file=\"module-structure-step.tsx\" />\r\n          Tambah Modul\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Modules List */}\r\n      {data.modules.length === 0 ? <Card>\r\n          <CardContent className=\"flex flex-col items-center justify-center py-12\">\r\n            <BookOpen className=\"w-12 h-12 text-muted-foreground mb-4\" />\r\n            <h3 className=\"text-lg font-semibold mb-2\">Belum ada modul</h3>\r\n            <p className=\"text-muted-foreground text-center mb-4\">\r\n              Mulai dengan membuat modul pertama untuk course Anda\r\n            </p>\r\n            <Button onClick={createNewModule}>\r\n              <Plus className=\"w-4 h-4 mr-2\" />\r\n              Buat Modul Pertama\r\n            </Button>\r\n          </CardContent>\r\n        </Card> : <div className=\"space-y-4\">\r\n          {data.modules.map((moduleItem, moduleIndex) => {\n        const isExpanded = expandedModules.has(moduleItem.id);\n        return <Card key={moduleItem.id} className=\"overflow-hidden\">\r\n                <CardHeader className=\"pb-3\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <GripVertical className=\"w-4 h-4 text-muted-foreground cursor-move\" />\r\n                        <Badge variant=\"outline\">Modul {moduleIndex + 1}</Badge>\r\n                      </div>\r\n                      <div>\r\n                        <CardTitle className=\"text-base\">{moduleItem.name || 'Modul Tanpa Nama'}</CardTitle>\r\n                        {moduleItem.description && <CardDescription className=\"mt-1\">\r\n                            {moduleItem.description}\r\n                          </CardDescription>}\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {moduleItem.hasModuleQuiz && <Badge variant=\"secondary\">\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          Quiz Modul\r\n                        </Badge>}\r\n                      <Badge variant=\"outline\">\r\n                        {moduleItem.chapters.length} Chapter\r\n                      </Badge>\r\n                      \r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => moveModule(moduleItem.id, 'up')} disabled={moduleIndex === 0}>\r\n                          ↑\r\n                        </Button>\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => moveModule(moduleItem.id, 'down')} disabled={moduleIndex === data.modules.length - 1}>\r\n                          ↓\r\n                        </Button>\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => editModule(moduleItem)}>\r\n                          <Edit className=\"w-4 h-4\" />\r\n                        </Button>\r\n                        <AlertDialog>\r\n                          <AlertDialogTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"sm\">\r\n                              <Trash2 className=\"w-4 h-4\" />\r\n                            </Button>\r\n                          </AlertDialogTrigger>\r\n                          <AlertDialogContent>\r\n                            <AlertDialogHeader>\r\n                              <AlertDialogTitle>Hapus Modul</AlertDialogTitle>\r\n                              <AlertDialogDescription>\r\n                                Apakah Anda yakin ingin menghapus modul &ldquo;{moduleItem.name}&rdquo;? \r\n                                Semua chapter di dalam modul ini juga akan terhapus.\r\n                              </AlertDialogDescription>\r\n                            </AlertDialogHeader>\r\n                            <AlertDialogFooter>\r\n                              <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                              <AlertDialogAction onClick={() => deleteModule(moduleItem.id)}>\r\n                                Hapus\r\n                              </AlertDialogAction>\r\n                            </AlertDialogFooter>\r\n                          </AlertDialogContent>\r\n                        </AlertDialog>\r\n                        <Button variant=\"ghost\" size=\"sm\" onClick={() => toggleModuleExpansion(moduleItem.id)}>\r\n                          {isExpanded ? <ChevronDown className=\"w-4 h-4\" /> : <ChevronRight className=\"w-4 h-4\" />}\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardHeader>\r\n                \r\n                {isExpanded && <CardContent className=\"pt-0\">\r\n                    <div className=\"space-y-3\">\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <h4 className=\"text-sm font-medium\">Chapters</h4>\r\n                        <Button variant=\"outline\" size=\"sm\" onClick={() => createNewChapter(moduleItem.id)}>\r\n                          <Plus className=\"w-4 h-4 mr-2\" />\r\n                          Tambah Chapter\r\n                        </Button>\r\n                      </div>\r\n                      \r\n                      {moduleItem.chapters.length === 0 ? <div className=\"text-center py-8 text-muted-foreground\">\r\n                          <FileText className=\"w-8 h-8 mx-auto mb-2\" />\r\n                          <p className=\"text-sm\">Belum ada chapter</p>\r\n                        </div> : <div className=\"space-y-2\">\r\n                          {moduleItem.chapters.map((chapter, chapterIndex) => <div key={chapter.id} className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\r\n                              <div className=\"flex items-center space-x-3\">\r\n                                <GripVertical className=\"w-4 h-4 text-muted-foreground cursor-move\" />\r\n                                <Badge variant=\"outline\" className=\"text-xs\">\r\n                                  {chapterIndex + 1}\r\n                                </Badge>\r\n                                <div>\r\n                                  <p className=\"text-sm font-medium\">\r\n                                    {chapter.name || 'Chapter Tanpa Nama'}\r\n                                  </p>\r\n                                  {chapter.hasChapterQuiz && <Badge variant=\"secondary\" className=\"text-xs mt-1\">\r\n                                      <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                                      Quiz\r\n                                    </Badge>}\r\n                                </div>\r\n                              </div>\r\n                              \r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <Button variant=\"ghost\" size=\"sm\" onClick={() => editChapter(moduleItem.id, chapter)}>\r\n                                  <Edit className=\"w-4 h-4\" />\r\n                                </Button>\r\n                                <AlertDialog>\r\n                                  <AlertDialogTrigger asChild>\r\n                                    <Button variant=\"ghost\" size=\"sm\">\r\n                                      <Trash2 className=\"w-4 h-4\" />\r\n                                    </Button>\r\n                                  </AlertDialogTrigger>\r\n                                  <AlertDialogContent>\r\n                                    <AlertDialogHeader>\r\n                                      <AlertDialogTitle>Hapus Chapter</AlertDialogTitle>\r\n                                      <AlertDialogDescription>\r\n                                        Apakah Anda yakin ingin menghapus chapter &ldquo;{chapter.name}&rdquo;?\r\n                                      </AlertDialogDescription>\r\n                                    </AlertDialogHeader>\r\n                                    <AlertDialogFooter>\r\n                                      <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                                      <AlertDialogAction onClick={() => deleteChapter(moduleItem.id, chapter.id)}>\r\n                                        Hapus\r\n                                      </AlertDialogAction>\r\n                                    </AlertDialogFooter>\r\n                                  </AlertDialogContent>\r\n                                </AlertDialog>\r\n                              </div>\r\n                            </div>)}\r\n                        </div>}\r\n                    </div>\r\n                  </CardContent>}\r\n              </Card>;\n      })}\r\n        </div>}\r\n\r\n      {/* Module Dialog */}\r\n      <Dialog open={isModuleDialogOpen} onOpenChange={setIsModuleDialogOpen} data-sentry-element=\"Dialog\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n        <DialogContent className=\"sm:max-w-md\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n          <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n            <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              {editingModule?.name ? 'Edit Modul' : 'Tambah Modul Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              Isi informasi dasar untuk modul ini\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"moduleName\" data-sentry-element=\"Label\" data-sentry-source-file=\"module-structure-step.tsx\">Nama Modul *</Label>\r\n              <Input id=\"moduleName\" placeholder=\"Masukkan nama modul\" value={editingModule?.name || ''} onChange={e => setEditingModule(prev => prev ? {\n              ...prev,\n              name: e.target.value\n            } : null)} data-sentry-element=\"Input\" data-sentry-source-file=\"module-structure-step.tsx\" />\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"moduleDescription\" data-sentry-element=\"Label\" data-sentry-source-file=\"module-structure-step.tsx\">Deskripsi</Label>\r\n              <Textarea id=\"moduleDescription\" placeholder=\"Jelaskan tentang modul ini...\" value={editingModule?.description || ''} onChange={e => setEditingModule(prev => prev ? {\n              ...prev,\n              description: e.target.value\n            } : null)} rows={3} data-sentry-element=\"Textarea\" data-sentry-source-file=\"module-structure-step.tsx\" />\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-2\">\r\n              <Switch id=\"hasModuleQuiz\" checked={editingModule?.hasModuleQuiz || false} onCheckedChange={checked => setEditingModule(prev => prev ? {\n              ...prev,\n              hasModuleQuiz: checked\n            } : null)} data-sentry-element=\"Switch\" data-sentry-source-file=\"module-structure-step.tsx\" />\r\n              <Label htmlFor=\"hasModuleQuiz\" data-sentry-element=\"Label\" data-sentry-source-file=\"module-structure-step.tsx\">Tambahkan quiz di akhir modul</Label>\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n            <Button variant=\"outline\" onClick={() => setIsModuleDialogOpen(false)} data-sentry-element=\"Button\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveModule} data-sentry-element=\"Button\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              {editingModule?.name ? 'Perbarui' : 'Tambah'} Modul\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Chapter Dialog */}\r\n      <Dialog open={isChapterDialogOpen} onOpenChange={setIsChapterDialogOpen} data-sentry-element=\"Dialog\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n        <DialogContent className=\"sm:max-w-md\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n          <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n            <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              {editingChapter.chapter?.name ? 'Edit Chapter' : 'Tambah Chapter Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              Isi informasi dasar untuk chapter ini\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"chapterName\" data-sentry-element=\"Label\" data-sentry-source-file=\"module-structure-step.tsx\">Nama Chapter *</Label>\r\n              <Input id=\"chapterName\" placeholder=\"Masukkan nama chapter\" value={editingChapter.chapter?.name || ''} onChange={e => setEditingChapter(prev => ({\n              ...prev,\n              chapter: prev.chapter ? {\n                ...prev.chapter,\n                name: e.target.value\n              } : null\n            }))} data-sentry-element=\"Input\" data-sentry-source-file=\"module-structure-step.tsx\" />\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-2\">\r\n              <Switch id=\"hasChapterQuiz\" checked={editingChapter.chapter?.hasChapterQuiz || false} onCheckedChange={checked => setEditingChapter(prev => ({\n              ...prev,\n              chapter: prev.chapter ? {\n                ...prev.chapter,\n                hasChapterQuiz: checked\n              } : null\n            }))} data-sentry-element=\"Switch\" data-sentry-source-file=\"module-structure-step.tsx\" />\r\n              <Label htmlFor=\"hasChapterQuiz\" data-sentry-element=\"Label\" data-sentry-source-file=\"module-structure-step.tsx\">Tambahkan quiz untuk chapter ini</Label>\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n            <Button variant=\"outline\" onClick={() => setIsChapterDialogOpen(false)} data-sentry-element=\"Button\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveChapter} data-sentry-element=\"Button\" data-sentry-source-file=\"module-structure-step.tsx\">\r\n              {editingChapter.chapter?.name ? 'Perbarui' : 'Tambah'} Chapter\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Summary */}\r\n      {data.modules.length > 0 && <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-lg\">Ringkasan Struktur</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">{data.modules.length}</div>\r\n                <div className=\"text-sm text-muted-foreground\">Modul</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">\r\n                  {data.modules.reduce((acc, moduleItem) => acc + moduleItem.chapters.length, 0)}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Chapter</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">\r\n                  {data.modules.filter(m => m.hasModuleQuiz).length}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Quiz Modul</div>\r\n              </div>\r\n              <div>\r\n                <div className=\"text-2xl font-bold text-primary\">\r\n                  {data.modules.reduce((acc, moduleItem) => acc + moduleItem.chapters.filter(c => c.hasChapterQuiz).length, 0)}\r\n                </div>\r\n                <div className=\"text-sm text-muted-foreground\">Quiz Chapter</div>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>}\r\n    </div>;\n}", "'use client';\n\nimport React from 'react';\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport Link from '@tiptap/extension-link';\nimport Image from '@tiptap/extension-image';\nimport { Table } from '@tiptap/extension-table';\nimport TableRow from '@tiptap/extension-table-row';\nimport TableHeader from '@tiptap/extension-table-header';\nimport TableCell from '@tiptap/extension-table-cell';\nimport { Button } from '@/components/ui/button';\nimport { Bold, Italic, Underline, Strikethrough, Heading1, Heading2, Heading3, List, ListOrdered, Quote, Code, Link as LinkIcon, Image as ImageIcon, Table as TableIcon, Undo, Redo } from 'lucide-react';\ninterface TiptapEditorProps {\n  content: string;\n  onChange: (content: string) => void;\n  placeholder?: string;\n}\nexport const TiptapEditor: React.FC<TiptapEditorProps> = ({\n  content,\n  onChange,\n  placeholder = \"Start typing...\"\n}) => {\n  const editor = useEditor({\n    extensions: [StarterKit.configure({\n      heading: {\n        levels: [1, 2, 3]\n      }\n    }), Link.configure({\n      openOnClick: false,\n      HTMLAttributes: {\n        class: 'text-blue-600 underline cursor-pointer'\n      }\n    }), Image.configure({\n      HTMLAttributes: {\n        class: 'max-w-full h-auto rounded-md'\n      }\n    }), Table.configure({\n      resizable: true\n    }), TableRow, TableHeader, TableCell],\n    content: content,\n    onUpdate: ({\n      editor\n    }) => {\n      const html = editor.getHTML();\n      onChange(html);\n    },\n    editorProps: {\n      attributes: {\n        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4'\n      }\n    }\n  });\n  if (!editor) {\n    return null;\n  }\n  const addImage = () => {\n    const url = window.prompt('Enter image URL:');\n    if (url) {\n      editor.chain().focus().setImage({\n        src: url\n      }).run();\n    }\n  };\n  const addLink = () => {\n    const previousUrl = editor.getAttributes('link').href;\n    const url = window.prompt('Enter URL:', previousUrl);\n    if (url === null) {\n      return;\n    }\n    if (url === '') {\n      editor.chain().focus().extendMarkRange('link').unsetLink().run();\n      return;\n    }\n    editor.chain().focus().extendMarkRange('link').setLink({\n      href: url\n    }).run();\n  };\n  const addTable = () => {\n    editor.chain().focus().insertTable({\n      rows: 3,\n      cols: 3,\n      withHeaderRow: true\n    }).run();\n  };\n  return <div className=\"border border-gray-200 rounded-lg overflow-hidden\" data-sentry-component=\"TiptapEditor\" data-sentry-source-file=\"tiptap-editor.tsx\">\n      {/* Toolbar */}\n      <div className=\"border-b border-gray-200 bg-gray-50 p-2 flex flex-wrap gap-1\">\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().undo().run()} disabled={!editor.can().undo()} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Undo className=\"h-4 w-4\" data-sentry-element=\"Undo\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().redo().run()} disabled={!editor.can().redo()} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Redo className=\"h-4 w-4\" data-sentry-element=\"Redo\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        \n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n        \n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleBold().run()} className={editor.isActive('bold') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Bold className=\"h-4 w-4\" data-sentry-element=\"Bold\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleItalic().run()} className={editor.isActive('italic') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Italic className=\"h-4 w-4\" data-sentry-element=\"Italic\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleStrike().run()} className={editor.isActive('strike') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Strikethrough className=\"h-4 w-4\" data-sentry-element=\"Strikethrough\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        \n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n        \n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleHeading({\n        level: 1\n      }).run()} className={editor.isActive('heading', {\n        level: 1\n      }) ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Heading1 className=\"h-4 w-4\" data-sentry-element=\"Heading1\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleHeading({\n        level: 2\n      }).run()} className={editor.isActive('heading', {\n        level: 2\n      }) ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Heading2 className=\"h-4 w-4\" data-sentry-element=\"Heading2\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleHeading({\n        level: 3\n      }).run()} className={editor.isActive('heading', {\n        level: 3\n      }) ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Heading3 className=\"h-4 w-4\" data-sentry-element=\"Heading3\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        \n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n        \n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleBulletList().run()} className={editor.isActive('bulletList') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <List className=\"h-4 w-4\" data-sentry-element=\"List\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleOrderedList().run()} className={editor.isActive('orderedList') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <ListOrdered className=\"h-4 w-4\" data-sentry-element=\"ListOrdered\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleBlockquote().run()} className={editor.isActive('blockquote') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Quote className=\"h-4 w-4\" data-sentry-element=\"Quote\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => editor.chain().focus().toggleCodeBlock().run()} className={editor.isActive('codeBlock') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <Code className=\"h-4 w-4\" data-sentry-element=\"Code\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        \n        <div className=\"w-px h-6 bg-gray-300 mx-1\" />\n        \n        <Button variant=\"ghost\" size=\"sm\" onClick={addLink} className={editor.isActive('link') ? 'bg-gray-200' : ''} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <LinkIcon className=\"h-4 w-4\" data-sentry-element=\"LinkIcon\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={addImage} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <ImageIcon className=\"h-4 w-4\" data-sentry-element=\"ImageIcon\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n        <Button variant=\"ghost\" size=\"sm\" onClick={addTable} data-sentry-element=\"Button\" data-sentry-source-file=\"tiptap-editor.tsx\">\n          <TableIcon className=\"h-4 w-4\" data-sentry-element=\"TableIcon\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n        </Button>\n      </div>\n      \n      {/* Editor Content */}\n      <div className=\"min-h-[200px] bg-white\">\n        <EditorContent editor={editor} className=\"tiptap-editor\" data-sentry-element=\"EditorContent\" data-sentry-source-file=\"tiptap-editor.tsx\" />\n      </div>\n    </div>;\n};", "'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card } from '@/components/ui/card';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Trash2, Image as ImageIcon, TextIcon, VideoIcon, FileTextIcon, MonitorPlayIcon, Link, Upload } from 'lucide-react';\nimport Image from 'next/image';\nimport { toast } from 'sonner';\nimport { TiptapEditor } from './tiptap-editor';\n\n// Define the types for content blocks\nexport type ContentBlock = {\n  id: string;\n  type: 'text' | 'image' | 'video' | 'pdf' | 'zoom-recording';\n  value: string; // For text content, image URL, video URL, PDF URL, Zoom recording URL\n};\ninterface DynamicContentEditorProps {\n  initialContent: ContentBlock[];\n  onContentChange: (content: ContentBlock[]) => void;\n  allowImages?: boolean;\n  placeholder?: string;\n  contentRefs?: React.MutableRefObject<{\n    [key: string]: HTMLDivElement | null;\n  }>;\n}\nexport function DynamicContentEditor({\n  initialContent,\n  onContentChange,\n  allowImages = true,\n  placeholder,\n  contentRefs\n}: DynamicContentEditorProps) {\n  const [content, setContent] = useState<ContentBlock[]>(initialContent);\n  const [showFileDialog, setShowFileDialog] = useState(false);\n  const [selectedFileType, setSelectedFileType] = useState<'image' | 'video' | 'pdf' | 'zoom-recording'>('image');\n  const [linkUrl, setLinkUrl] = useState('');\n  React.useEffect(() => {\n    setContent(initialContent);\n  }, [initialContent]);\n  const addBlock = (type: ContentBlock['type']) => {\n    const newBlock: ContentBlock = {\n      id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,\n      type,\n      value: ''\n    };\n    const updatedContent = [...content, newBlock];\n    setContent(updatedContent);\n    onContentChange(updatedContent);\n  };\n  const handleFileBlockAdd = (type: 'image' | 'video' | 'pdf' | 'zoom-recording') => {\n    setSelectedFileType(type);\n    setShowFileDialog(true);\n    setLinkUrl('');\n  };\n  const handleAddFromLink = () => {\n    if (linkUrl.trim()) {\n      const newBlock: ContentBlock = {\n        id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,\n        type: selectedFileType,\n        value: linkUrl.trim()\n      };\n      const updatedContent = [...content, newBlock];\n      setContent(updatedContent);\n      onContentChange(updatedContent);\n      setShowFileDialog(false);\n      setLinkUrl('');\n      toast.success('Block berhasil ditambahkan dari link!');\n    } else {\n      toast.error('Silakan masukkan URL yang valid');\n    }\n  };\n  const handleAddFromUpload = () => {\n    addBlock(selectedFileType);\n    setShowFileDialog(false);\n  };\n  const updateBlock = (id: string, newValue: string) => {\n    const updatedContent = content.map(block => block.id === id ? {\n      ...block,\n      value: newValue\n    } : block);\n    setContent(updatedContent);\n    onContentChange(updatedContent);\n  };\n  const removeBlock = (id: string) => {\n    const updatedContent = content.filter(block => block.id !== id);\n    setContent(updatedContent);\n    onContentChange(updatedContent);\n  };\n  const handleFileUpload = useCallback(async (files: File[], blockId: string, fileType: 'image' | 'video' | 'pdf' | 'zoom-recording') => {\n    if (!files || files.length === 0) {\n      toast.error('No file selected for upload.');\n      return;\n    }\n    const file = files[0];\n    toast.info(`Uploading ${file.name}...`);\n    try {\n      const response = await fetch(`/api/upload?filename=${file.name}`, {\n        method: 'POST',\n        body: file\n      });\n      if (!response.ok) {\n        throw new Error(`Upload failed: ${response.statusText}`);\n      }\n      const newBlob = await response.json();\n      updateBlock(blockId, newBlob.url);\n      toast.success(`${fileType.charAt(0).toUpperCase() + fileType.slice(1)} uploaded successfully!`);\n    } catch (error) {\n      console.error(`Error uploading ${fileType}:`, error);\n      toast.error(`Failed to upload ${fileType}: ${(error as Error).message}`);\n    }\n  }, [updateBlock]);\n  return <div className=\"space-y-4\" data-sentry-component=\"DynamicContentEditor\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n      {content.map((block, index) => <Card key={block.id} className=\"relative p-4 mb-4 scroll-mt-4\" ref={el => {\n      if (contentRefs) {\n        contentRefs.current[block.id || `block-${index}`] = el;\n      }\n    }} id={block.id || `block-${index}`}>\r\n          {block.type === 'text' ? <div className=\"wysiwyg-editor\">\r\n              <TiptapEditor content={block.value} onChange={(content: string) => updateBlock(block.id, content)} placeholder=\"Start typing your content...\" />\r\n            </div> : block.type === 'image' ? <div className=\"space-y-2\">\r\n              {block.value ? <div className=\"relative w-full h-48 border rounded-md overflow-hidden\">\r\n                  <Image src={block.value} alt=\"Uploaded content\" layout=\"fill\" objectFit=\"contain\" className=\"rounded-md\" />\r\n                </div> : <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan gambar:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const input = document.createElement('input');\n              input.type = 'file';\n              input.accept = 'image/*';\n              input.onchange = e => {\n                const files = (e.target as HTMLInputElement).files;\n                if (files) handleFileUpload(Array.from(files), block.id, 'image');\n              };\n              input.click();\n            }}>\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const url = prompt('Masukkan URL gambar:');\n              if (url) updateBlock(block.id, url);\n            }}>\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>}\r\n            </div> : block.type === 'video' ? <div className=\"space-y-2\">\r\n              {block.value ? <video controls src={block.value} className=\"w-full h-auto max-h-96 rounded-md\" /> : <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan video:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const input = document.createElement('input');\n              input.type = 'file';\n              input.accept = 'video/*';\n              input.onchange = e => {\n                const files = (e.target as HTMLInputElement).files;\n                if (files) handleFileUpload(Array.from(files), block.id, 'video');\n              };\n              input.click();\n            }}>\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const url = prompt('Masukkan URL video:');\n              if (url) updateBlock(block.id, url);\n            }}>\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>}\r\n            </div> : block.type === 'pdf' ? <div className=\"space-y-2\">\r\n              {block.value ? <iframe src={block.value} className=\"w-full h-96 rounded-md\" /> : <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan PDF:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const input = document.createElement('input');\n              input.type = 'file';\n              input.accept = 'application/pdf';\n              input.onchange = e => {\n                const files = (e.target as HTMLInputElement).files;\n                if (files) handleFileUpload(Array.from(files), block.id, 'pdf');\n              };\n              input.click();\n            }}>\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const url = prompt('Masukkan URL PDF:');\n              if (url) updateBlock(block.id, url);\n            }}>\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>}\r\n            </div> : block.type === 'zoom-recording' ? <div className=\"space-y-2\">\r\n              {block.value ? <iframe src={block.value} className=\"w-full h-96 rounded-md\" /> : <div className=\"space-y-2\">\r\n                  <div className=\"text-sm text-muted-foreground mb-2\">Pilih cara menambahkan Zoom Recording:</div>\r\n                  <div className=\"flex gap-2\">\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const input = document.createElement('input');\n              input.type = 'file';\n              input.accept = 'video/*';\n              input.onchange = e => {\n                const files = (e.target as HTMLInputElement).files;\n                if (files) handleFileUpload(Array.from(files), block.id, 'zoom-recording');\n              };\n              input.click();\n            }}>\r\n                      <Upload className=\"h-4 w-4 mr-2\" />\r\n                      Upload File\r\n                    </Button>\r\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => {\n              const url = prompt('Masukkan URL Zoom Recording:');\n              if (url) updateBlock(block.id, url);\n            }}>\r\n                      <Link className=\"h-4 w-4 mr-2\" />\r\n                      Dari Link\r\n                    </Button>\r\n                  </div>\r\n                </div>}\r\n            </div> : <Textarea placeholder={`Enter ${block.type} URL`} value={block.value} onChange={e => updateBlock(block.id, e.target.value)} rows={3} />}\r\n          <Button variant=\"ghost\" size=\"icon\" className=\"absolute top-2 right-2 text-muted-foreground hover:text-destructive\" onClick={() => removeBlock(block.id)}>\r\n            <Trash2 className=\"h-4 w-4\" />\r\n          </Button>\r\n        </Card>)}\r\n      <div className=\"flex flex-wrap gap-2 pt-2\">\r\n        <Button variant=\"outline\" onClick={() => addBlock('text')} size=\"sm\" data-sentry-element=\"Button\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n          <TextIcon className=\"h-4 w-4 mr-2\" data-sentry-element=\"TextIcon\" data-sentry-source-file=\"dynamic-content-editor.tsx\" /> Add Text Block\r\n        </Button>\r\n        {allowImages && <>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('image')} size=\"sm\">\r\n              <ImageIcon className=\"h-4 w-4 mr-2\" /> Add Image Block\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('video')} size=\"sm\">\r\n              <MonitorPlayIcon className=\"h-4 w-4 mr-2\" /> Add Video Block\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('pdf')} size=\"sm\">\r\n              <FileTextIcon className=\"h-4 w-4 mr-2\" /> Add PDF Block\r\n            </Button>\r\n            <Button variant=\"outline\" onClick={() => handleFileBlockAdd('zoom-recording')} size=\"sm\">\r\n              <VideoIcon className=\"h-4 w-4 mr-2\" /> Add Zoom Recording Block\r\n            </Button>\r\n          </>}\r\n      </div>\r\n\r\n      {/* File Addition Dialog */}\r\n      <Dialog open={showFileDialog} onOpenChange={setShowFileDialog} data-sentry-element=\"Dialog\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n        <DialogContent data-sentry-element=\"DialogContent\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n          <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n            <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"dynamic-content-editor.tsx\">Tambah {selectedFileType.charAt(0).toUpperCase() + selectedFileType.slice(1)} Block</DialogTitle>\r\n            <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n              Pilih cara menambahkan {selectedFileType}: upload file atau masukkan link.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"link-url\" data-sentry-element=\"Label\" data-sentry-source-file=\"dynamic-content-editor.tsx\">URL {selectedFileType.charAt(0).toUpperCase() + selectedFileType.slice(1)}</Label>\r\n              <Input id=\"link-url\" placeholder={`Masukkan URL ${selectedFileType}...`} value={linkUrl} onChange={e => setLinkUrl(e.target.value)} data-sentry-element=\"Input\" data-sentry-source-file=\"dynamic-content-editor.tsx\" />\r\n            </div>\r\n          </div>\r\n          <DialogFooter className=\"flex gap-2\" data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n            <Button variant=\"outline\" onClick={handleAddFromUpload} data-sentry-element=\"Button\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n              <Upload className=\"h-4 w-4 mr-2\" data-sentry-element=\"Upload\" data-sentry-source-file=\"dynamic-content-editor.tsx\" />\r\n              Upload File\r\n            </Button>\r\n            <Button onClick={handleAddFromLink} disabled={!linkUrl.trim()} data-sentry-element=\"Button\" data-sentry-source-file=\"dynamic-content-editor.tsx\">\r\n              <Link className=\"h-4 w-4 mr-2\" data-sentry-element=\"Link\" data-sentry-source-file=\"dynamic-content-editor.tsx\" />\r\n              Gunakan Link\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>;\n}", "'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea'; // Keep for other uses\nimport { DynamicContentEditor, ContentBlock } from '@/components/dynamic-content-editor';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';\nimport { FileText, HelpCircle, Plus, Edit, Trash2, Eye, Save, BookOpen, CheckCircle, Clock, Type, Image, Video, FileIcon, Navigation } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '../course-creation-wizard';\nimport { toast } from 'sonner';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\ninterface ContentCreationStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function ContentCreationStep({\n  data,\n  onUpdate\n}: ContentCreationStepProps) {\n  const [selectedModule, setSelectedModule] = useState<string>(data.modules[0]?.id || '');\n  const [selectedChapter, setSelectedChapter] = useState<string>('');\n  const [editingQuiz, setEditingQuiz] = useState<{\n    type: 'chapter' | 'module' | 'final';\n    quiz: QuizData | null;\n  }>({\n    type: 'chapter',\n    quiz: null\n  });\n  const [isQuizDialogOpen, setIsQuizDialogOpen] = useState(false);\n  const [editingQuestion, setEditingQuestion] = useState<QuestionData | null>(null);\n  const [isQuestionDialogOpen, setIsQuestionDialogOpen] = useState(false);\n  const [previewMode, setPreviewMode] = useState(false);\n\n  // Add ref for content scrolling\n  const contentRefs = useRef<{\n    [key: string]: HTMLDivElement | null;\n  }>({});\n  const currentModule = data.modules.find(m => m.id === selectedModule);\n  const currentChapter = currentModule?.chapters.find(c => c.id === selectedChapter);\n\n  // Function to scroll to specific content block\n  const scrollToContent = (blockId: string) => {\n    const element = contentRefs.current[blockId];\n    if (element) {\n      element.scrollIntoView({\n        behavior: 'smooth',\n        block: 'start',\n        inline: 'nearest'\n      });\n    }\n  };\n\n  // Function to get content type icon\n  const getContentTypeIcon = (type: string) => {\n    switch (type) {\n      case 'text':\n        return <Type className=\"w-3 h-3\" />;\n      case 'image':\n        return <Image className=\"w-3 h-3\" />;\n      case 'video':\n        return <Video className=\"w-3 h-3\" />;\n      case 'pdf':\n        return <FileText className=\"w-3 h-3\" />;\n      case 'zoom-recording':\n        return <Video className=\"w-3 h-3\" />;\n      default:\n        return <FileIcon className=\"w-3 h-3\" />;\n    }\n  };\n\n  // Function to get short content preview\n  const getContentPreview = (block: ContentBlock) => {\n    if (block.type === 'text') {\n      return block.value?.slice(0, 30) + (block.value && block.value.length > 30 ? '...' : '') || 'Empty text';\n    }\n    return block.type.charAt(0).toUpperCase() + block.type.slice(1);\n  };\n  const updateChapterContent = (content: ContentBlock[]) => {\n    if (!currentModule || !currentChapter) return;\n    const updatedModules = data.modules.map(module => {\n      if (module.id === selectedModule) {\n        const updatedChapters = module.chapters.map(chapter => {\n          if (chapter.id === selectedChapter) {\n            return {\n              ...chapter,\n              content\n            };\n          }\n          return chapter;\n        });\n        return {\n          ...module,\n          chapters: updatedChapters\n        };\n      }\n      return module;\n    });\n    onUpdate({\n      modules: updatedModules\n    });\n  };\n  const createQuiz = (type: 'chapter' | 'module' | 'final') => {\n    const newQuiz: QuizData = {\n      id: `quiz-${Date.now()}`,\n      name: type === 'chapter' ? `Quiz ${currentChapter?.name}` : type === 'module' ? `Quiz ${currentModule?.name}` : `Final Exam - ${data.name}`,\n      description: '',\n      questions: [],\n      minimumScore: 70,\n      timeLimit: type === 'final' ? 120 : undefined // Default 2 hours for final exam\n    };\n    setEditingQuiz({\n      type,\n      quiz: newQuiz\n    });\n    setIsQuizDialogOpen(true);\n  };\n  const editQuiz = (type: 'chapter' | 'module' | 'final', quiz: QuizData) => {\n    setEditingQuiz({\n      type,\n      quiz: {\n        ...quiz\n      }\n    });\n    setIsQuizDialogOpen(true);\n  };\n  const saveQuiz = () => {\n    if (!editingQuiz.quiz || !editingQuiz.quiz.name.trim()) {\n      toast.error('Nama quiz harus diisi');\n      return;\n    }\n    if (editingQuiz.type === 'final') {\n      onUpdate({\n        finalExam: editingQuiz.quiz!\n      });\n    } else {\n      const updatedModules = data.modules.map(module => {\n        if (module.id === selectedModule) {\n          if (editingQuiz.type === 'module') {\n            return {\n              ...module,\n              moduleQuiz: editingQuiz.quiz!\n            };\n          } else {\n            const updatedChapters = module.chapters.map(chapter => {\n              if (chapter.id === selectedChapter) {\n                return {\n                  ...chapter,\n                  chapterQuiz: editingQuiz.quiz!\n                };\n              }\n              return chapter;\n            });\n            return {\n              ...module,\n              chapters: updatedChapters\n            };\n          }\n        }\n        return module;\n      });\n      onUpdate({\n        modules: updatedModules\n      });\n    }\n    setIsQuizDialogOpen(false);\n    setEditingQuiz({\n      type: 'chapter',\n      quiz: null\n    });\n    toast.success('Quiz berhasil disimpan');\n  };\n  const createQuestion = () => {\n    const newQuestion: QuestionData = {\n      id: editingQuestion?.id || `question-${Date.now()}`,\n      type: 'multiple_choice',\n      question: [{\n        type: 'text',\n        value: ''\n      }],\n      options: editingQuestion?.type === 'true_false' ? [{\n        content: [{\n          type: 'text',\n          value: 'True'\n        }],\n        isCorrect: false\n      }, {\n        content: [{\n          type: 'text',\n          value: 'False'\n        }],\n        isCorrect: false\n      }] : [{\n        content: [{\n          type: 'text',\n          value: ''\n        }],\n        isCorrect: false\n      }, {\n        content: [{\n          type: 'text',\n          value: ''\n        }],\n        isCorrect: false\n      }, {\n        content: [{\n          type: 'text',\n          value: ''\n        }],\n        isCorrect: false\n      }, {\n        content: [{\n          type: 'text',\n          value: ''\n        }],\n        isCorrect: false\n      }],\n      essayAnswer: '',\n      explanation: [],\n      points: 1,\n      orderIndex: editingQuiz.quiz?.questions.length || 0\n    };\n    setEditingQuestion(newQuestion);\n    setIsQuestionDialogOpen(true);\n  };\n  const editQuestion = (question: QuestionData) => {\n    setEditingQuestion({\n      ...question\n    });\n    setIsQuestionDialogOpen(true);\n  };\n  const saveQuestion = () => {\n    if (!editingQuestion || editingQuestion.question.length === 0 || editingQuestion.question[0].type === 'text' && !editingQuestion.question[0].value.trim()) {\n      toast.error('Pertanyaan harus diisi');\n      return;\n    }\n    if (!editingQuiz.quiz) return;\n    const updatedQuestions = [...editingQuiz.quiz.questions];\n    const existingIndex = updatedQuestions.findIndex(q => q.id === editingQuestion.id);\n    if (existingIndex >= 0) {\n      updatedQuestions[existingIndex] = editingQuestion;\n    } else {\n      updatedQuestions.push(editingQuestion);\n    }\n    setEditingQuiz(prev => ({\n      ...prev,\n      quiz: prev.quiz ? {\n        ...prev.quiz,\n        questions: updatedQuestions\n      } : null\n    }));\n    setIsQuestionDialogOpen(false);\n    setEditingQuestion(null);\n    toast.success('Pertanyaan berhasil disimpan');\n  };\n  const deleteQuestion = (questionId: string) => {\n    if (!editingQuiz.quiz) return;\n    const updatedQuestions = editingQuiz.quiz.questions.filter(q => q.id !== questionId).map((q, index) => ({\n      ...q,\n      orderIndex: index\n    }));\n    setEditingQuiz(prev => ({\n      ...prev,\n      quiz: prev.quiz ? {\n        ...prev.quiz,\n        questions: updatedQuestions\n      } : null\n    }));\n    toast.success('Pertanyaan berhasil dihapus');\n  };\n  const getCompletionStatus = () => {\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\n    const completedChapters = data.modules.reduce((acc, module) => acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0);\n    return {\n      total: totalChapters,\n      completed: completedChapters,\n      percentage: totalChapters > 0 ? Math.round(completedChapters / totalChapters * 100) : 0\n    };\n  };\n  const completionStatus = getCompletionStatus();\n  if (data.modules.length === 0) {\n    return <div className=\"text-center py-12\">\r\n        <BookOpen className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\r\n        <h3 className=\"text-lg font-semibold mb-2\">Belum ada modul</h3>\r\n        <p className=\"text-muted-foreground\">\r\n          Kembali ke langkah sebelumnya untuk membuat struktur modul terlebih dahulu\r\n        </p>\r\n      </div>;\n  }\n  return <div className=\"space-y-6\" data-sentry-component=\"ContentCreationStep\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n      {/* Header with Progress */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold\">Pembuatan Konten</h3>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Tambahkan konten dan quiz untuk setiap chapter\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-4\">\r\n          <div className=\"text-right\">\r\n            <div className=\"text-sm font-medium\">\r\n              {completionStatus.completed} / {completionStatus.total} Chapter\r\n            </div>\r\n            <div className=\"text-xs text-muted-foreground\">\r\n              {completionStatus.percentage}% selesai\r\n            </div>\r\n          </div>\r\n          <div className={cn(\"w-12 h-12 rounded-full flex items-center justify-center\", completionStatus.percentage === 100 ? \"bg-green-100 text-green-600\" : \"bg-muted text-muted-foreground\")}>\r\n            {completionStatus.percentage === 100 ? <CheckCircle className=\"w-6 h-6\" /> : <Clock className=\"w-6 h-6\" />}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n        {/* Module/Chapter Navigation */}\r\n        <div className=\"lg:col-span-1\">\r\n          <Card data-sentry-element=\"Card\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n            <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              <CardTitle className=\"text-base\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"content-creation-step.tsx\">Navigasi Konten</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4 max-h-[70vh] overflow-y-auto\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              {/* Final Exam Section */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"p-3 rounded-lg border-2 border-dashed border-primary/30 bg-primary/5\">\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <div>\r\n                      <div className=\"font-medium text-sm text-primary\">Final Exam</div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        Ujian akhir untuk seluruh course\r\n                      </div>\r\n                    </div>\r\n                    {data.finalExam && <CheckCircle className=\"w-4 h-4 text-green-600\" />}\r\n                  </div>\r\n                  <Button variant={data.finalExam ? \"outline\" : \"default\"} size=\"sm\" className=\"w-full\" onClick={() => {\n                  if (data.finalExam) {\n                    editQuiz('final', data.finalExam);\n                  } else {\n                    createQuiz('final');\n                  }\n                }} data-sentry-element=\"Button\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n                    <HelpCircle className=\"w-4 h-4 mr-2\" data-sentry-element=\"HelpCircle\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n                    {data.finalExam ? 'Edit Final Exam' : 'Buat Final Exam'}\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modules */}\r\n              {data.modules.map(module => <div key={module.id} className=\"space-y-2\">\r\n                  <div className={cn(\"p-2 rounded-lg cursor-pointer transition-colors\", selectedModule === module.id ? \"bg-primary text-primary-foreground\" : \"bg-muted hover:bg-muted/80\")} onClick={() => {\n                setSelectedModule(module.id);\n                setSelectedChapter('');\n              }}>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <div className=\"font-medium text-sm\">{module.name}</div>\r\n                        <div className=\"text-xs opacity-75\">\r\n                          {module.chapters.length} chapters\r\n                        </div>\r\n                      </div>\r\n                      {module.moduleQuiz && <Badge variant=\"secondary\" className=\"text-xs\">\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          Quiz\r\n                        </Badge>}\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  {selectedModule === module.id && <div className=\"ml-4 space-y-2\">\r\n                      {/* Module Quiz Button */}\r\n                      <div className=\"p-2 rounded bg-secondary/50\">\r\n                        <Button variant={module.moduleQuiz ? \"outline\" : \"secondary\"} size=\"sm\" className=\"w-full text-xs\" onClick={() => {\n                    if (module.moduleQuiz) {\n                      editQuiz('module', module.moduleQuiz);\n                    } else {\n                      createQuiz('module');\n                    }\n                  }}>\r\n                          <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                          {module.moduleQuiz ? 'Edit Module Quiz' : 'Buat Module Quiz'}\r\n                        </Button>\r\n                      </div>\r\n                      \r\n                      {/* Chapters */}\r\n                      {module.chapters.map(chapter => {\n                  const hasContent = chapter.content && chapter.content.length > 0;\n                  return <div key={chapter.id} className=\"space-y-1\">\r\n                            <div className={cn(\"p-2 rounded text-xs cursor-pointer transition-colors flex items-center justify-between\", selectedChapter === chapter.id ? \"bg-primary/20 text-primary\" : \"hover:bg-muted/50\")} onClick={() => setSelectedChapter(chapter.id)}>\r\n                              <span>{chapter.name}</span>\r\n                              {hasContent && <CheckCircle className=\"w-3 h-3 text-green-600\" />}\r\n                            </div>\r\n                            \r\n                            {/* Content Block Navigation - show when chapter is selected and has content */}\r\n                            {selectedChapter === chapter.id && hasContent && chapter.content && <div className=\"ml-4 space-y-1\">\r\n                                <div className=\"flex items-center space-x-1 text-xs text-muted-foreground mb-1\">\r\n                                  <Navigation className=\"w-3 h-3\" />\r\n                                  <span>Content Blocks</span>\r\n                                </div>\r\n                                {chapter.content.map((block, index) => <button key={block.id || index} onClick={() => scrollToContent(block.id || `block-${index}`)} className=\"w-full text-left p-1.5 rounded text-xs hover:bg-primary/10 transition-colors flex items-center space-x-2\">\r\n                                    {getContentTypeIcon(block.type)}\r\n                                    <span className=\"truncate flex-1\">\r\n                                      {index + 1}. {getContentPreview(block)}\r\n                                    </span>\r\n                                  </button>)}\r\n                              </div>}\r\n                          </div>;\n                })}\r\n                    </div>}\r\n                </div>)}\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Content Editor */}\r\n        <div className=\"lg:col-span-3\">\r\n          {!selectedChapter ? <div className=\"space-y-6\">\r\n              {/* Final Exam Info */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center space-x-2\">\r\n                    <HelpCircle className=\"w-5 h-5 text-primary\" />\r\n                    <span>Final Exam</span>\r\n                    {data.finalExam && <Badge variant=\"secondary\">Sudah dibuat</Badge>}\r\n                  </CardTitle>\r\n                  <CardDescription>\r\n                    Ujian akhir untuk menguji pemahaman siswa terhadap seluruh materi course\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {data.finalExam ? <div className=\"space-y-4\">\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.questions.length}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Pertanyaan</div>\r\n                        </div>\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.minimumScore}%\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Nilai Minimum</div>\r\n                        </div>\r\n                        <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                          <div className=\"text-2xl font-bold text-primary\">\r\n                            {data.finalExam.questions.reduce((sum, q) => sum + q.points, 0)}\r\n                          </div>\r\n                          <div className=\"text-sm text-muted-foreground\">Total Poin</div>\r\n                        </div>\r\n                        {data.finalExam.timeLimit && <div className=\"text-center p-4 bg-muted rounded-lg\">\r\n                            <div className=\"text-2xl font-bold text-primary\">\r\n                              {data.finalExam.timeLimit}\r\n                            </div>\r\n                            <div className=\"text-sm text-muted-foreground\">Menit</div>\r\n                          </div>}\r\n                      </div>\r\n                      <div className=\"flex space-x-2\">\r\n                        <Button onClick={() => editQuiz('final', data.finalExam!)} className=\"flex-1\">\r\n                          <Edit className=\"w-4 h-4 mr-2\" />\r\n                          Edit Final Exam\r\n                        </Button>\r\n                      </div>\r\n                    </div> : <div className=\"text-center py-8\">\r\n                      <HelpCircle className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\r\n                      <h3 className=\"text-lg font-semibold mb-2\">Belum ada Final Exam</h3>\r\n                      <p className=\"text-muted-foreground mb-4\">\r\n                        Final Exam adalah ujian akhir yang menguji pemahaman siswa terhadap seluruh materi course\r\n                      </p>\r\n                      <Button onClick={() => createQuiz('final')}>\r\n                        <Plus className=\"w-4 h-4 mr-2\" />\r\n                        Buat Final Exam\r\n                      </Button>\r\n                    </div>}\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Module Overview */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle>Overview Modul</CardTitle>\r\n                  <CardDescription>\r\n                    Pilih chapter dari navigasi di sebelah kiri untuk mulai menambahkan konten\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                    {data.modules.map(module => <div key={module.id} className=\"p-4 border rounded-lg\">\r\n                        <div className=\"flex items-center justify-between mb-2\">\r\n                          <h4 className=\"font-medium\">{module.name}</h4>\r\n                          {module.moduleQuiz && <Badge variant=\"secondary\">\r\n                              <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                              Quiz\r\n                            </Badge>}\r\n                        </div>\r\n                        <div className=\"text-sm text-muted-foreground mb-3\">\r\n                          {module.chapters.length} chapters\r\n                        </div>\r\n                        <div className=\"space-y-1\">\r\n                          {module.chapters.map(chapter => {\n                      const hasContent = chapter.content && chapter.content.length > 0;\n                      return <div key={chapter.id} className=\"flex items-center justify-between text-xs\">\r\n                                <span>{chapter.name}</span>\r\n                                {hasContent ? <CheckCircle className=\"w-3 h-3 text-green-600\" /> : <Clock className=\"w-3 h-3 text-muted-foreground\" />}\r\n                              </div>;\n                    })}\r\n                        </div>\r\n                      </div>)}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div> : <div className=\"space-y-6\">\r\n              {/* Chapter Header */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <CardTitle className=\"flex items-center space-x-2\">\r\n                        <span>{currentChapter?.name}</span>\r\n                        {currentChapter?.hasChapterQuiz && <Badge variant=\"secondary\">\r\n                            <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                            Chapter Quiz\r\n                          </Badge>}\r\n                        {currentModule?.moduleQuiz && <Badge variant=\"outline\">\r\n                            <HelpCircle className=\"w-3 h-3 mr-1\" />\r\n                            Module Quiz\r\n                          </Badge>}\r\n                      </CardTitle>\r\n                      <CardDescription>\r\n                        Modul: {currentModule?.name}\r\n                        {currentModule?.moduleQuiz && <span className=\"ml-2 text-xs text-primary\">\r\n                            • Module ini memiliki quiz\r\n                          </span>}\r\n                      </CardDescription>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <Button variant=\"outline\" size=\"sm\" onClick={() => setPreviewMode(!previewMode)}>\r\n                        <Eye className=\"w-4 h-4 mr-2\" />\r\n                        {previewMode ? 'Edit' : 'Preview'}\r\n                      </Button>\r\n                      {currentModule?.moduleQuiz && <Button variant=\"outline\" size=\"sm\" onClick={() => editQuiz('module', currentModule.moduleQuiz!)}>\r\n                          <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                          Edit Module Quiz\r\n                        </Button>}\r\n                      {currentChapter?.hasChapterQuiz && <Button variant=\"outline\" size=\"sm\" onClick={() => {\n                    if (currentChapter.chapterQuiz) {\n                      editQuiz('chapter', currentChapter.chapterQuiz);\n                    } else {\n                      createQuiz('chapter');\n                    }\n                  }}>\r\n                          <HelpCircle className=\"w-4 h-4 mr-2\" />\r\n                          {currentChapter.chapterQuiz ? 'Edit Chapter Quiz' : 'Buat Chapter Quiz'}\r\n                        </Button>}\r\n                    </div>\r\n                  </div>\r\n                </CardHeader>\r\n              </Card>\r\n\r\n              {/* Content Editor/Preview with Scrollable Container */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"text-base\">Konten Chapter</CardTitle>\r\n                  <CardDescription>\r\n                    {previewMode ? 'Preview konten seperti yang akan dilihat siswa' : 'Gunakan Markdown untuk memformat konten'}\r\n                  </CardDescription>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  {previewMode ? <div className=\"max-h-[60vh] overflow-y-auto prose max-w-none pr-4\">\r\n                      {currentChapter?.content && currentChapter.content.length > 0 ? <>\r\n                          {currentChapter.content.map((block: any, index: number) => <div key={block.id || index} ref={el => {\n                    contentRefs.current[block.id || `block-${index}`] = el;\n                  }} className=\"mb-6 scroll-mt-4\" id={block.id || `block-${index}`}>\r\n                              {/* Content Block Header for easier identification */}\r\n                              <div className=\"flex items-center space-x-2 mb-2 py-1 px-2 bg-muted/30 rounded text-xs text-muted-foreground\">\r\n                                {getContentTypeIcon(block.type)}\r\n                                <span>Content-{index + 1}</span>\r\n                                <span>({block.type})</span>\r\n                              </div>\r\n                              \r\n                              {block.type === 'text' ? <ReactMarkdown remarkPlugins={[remarkGfm]} components={{\n                      h1: ({\n                        node,\n                        ...props\n                      }) => <h1 className=\"mb-4 text-2xl font-bold text-gray-900\" {...props} />,\n                      h2: ({\n                        node,\n                        ...props\n                      }) => <h2 className=\"mb-3 text-xl font-semibold text-gray-800\" {...props} />,\n                      h3: ({\n                        node,\n                        ...props\n                      }) => <h3 className=\"mb-2 text-lg font-semibold text-gray-800\" {...props} />,\n                      h4: ({\n                        node,\n                        ...props\n                      }) => <h4 className=\"mb-2 text-base font-semibold text-gray-700\" {...props} />,\n                      p: ({\n                        node,\n                        ...props\n                      }) => <p className=\"mb-3 leading-relaxed\" {...props} />,\n                      ul: ({\n                        node,\n                        ...props\n                      }) => <ul className=\"mb-3 ml-4 list-disc\" {...props} />,\n                      ol: ({\n                        node,\n                        ...props\n                      }) => <ol className=\"mb-3 ml-4 list-decimal\" {...props} />,\n                      li: ({\n                        node,\n                        ...props\n                      }) => <li className=\"mb-1\" {...props} />,\n                      blockquote: ({\n                        node,\n                        ...props\n                      }) => <blockquote className=\"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic\" {...props} />,\n                      code: ({\n                        node,\n                        className,\n                        children,\n                        ...props\n                      }) => {\n                        const match = /language-(\\w+)/.exec(className || '');\n                        const isInline = !match;\n                        return isInline ? <code className=\"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm\" {...props}>\r\n                                          {children}\r\n                                        </code> : <code className=\"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100\" {...props}>\r\n                                          {children}\r\n                                        </code>;\n                      },\n                      pre: ({\n                        node,\n                        ...props\n                      }) => <pre className=\"mb-4\" {...props} />,\n                      table: ({\n                        node,\n                        ...props\n                      }) => <div className=\"mb-4 overflow-x-auto\">\r\n                                        <table className=\"min-w-full rounded border border-gray-200\" {...props} />\r\n                                      </div>,\n                      thead: ({\n                        node,\n                        ...props\n                      }) => <thead className=\"bg-gray-50\" {...props} />,\n                      th: ({\n                        node,\n                        ...props\n                      }) => <th className=\"border border-gray-200 px-3 py-2 text-left font-semibold\" {...props} />,\n                      td: ({\n                        node,\n                        ...props\n                      }) => <td className=\"border border-gray-200 px-3 py-2\" {...props} />,\n                      hr: ({\n                        node,\n                        ...props\n                      }) => <hr className=\"my-6 border-gray-300\" {...props} />,\n                      strong: ({\n                        node,\n                        ...props\n                      }) => <strong className=\"font-semibold text-gray-900\" {...props} />,\n                      em: ({\n                        node,\n                        ...props\n                      }) => <em className=\"italic\" {...props} />\n                    }}>\r\n                                  {block.value}\r\n                                </ReactMarkdown> : block.type === 'image' ? <div className=\"my-4\">\r\n                                  <img src={block.value} alt=\"Content\" className=\"max-w-full h-auto rounded-md\" />\r\n                                </div> : block.type === 'video' ? <div className=\"my-4\">\r\n                                  <video src={block.value} controls className=\"max-w-full rounded-md\" />\r\n                                </div> : block.type === 'pdf' ? <div className=\"my-4\">\r\n                                  <iframe src={block.value} className=\"w-full h-96 rounded-md\" title=\"PDF Content\" />\r\n                                </div> : <div className=\"my-4 p-4 bg-muted rounded-md\">\r\n                                  <p className=\"text-sm text-muted-foreground\">\r\n                                    {block.type === 'zoom-recording' ? 'Zoom Recording: ' : 'File: '}\r\n                                    <a href={block.value} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-primary hover:underline\">\r\n                                      {block.value}\r\n                                    </a>\r\n                                  </p>\r\n                                </div>}\r\n                            </div>)}\r\n                        </> : <p className=\"text-muted-foreground italic\">\r\n                          Belum ada konten untuk chapter ini\r\n                        </p>}\r\n                    </div> : <div className=\"space-y-4\">\r\n                      <div className=\"max-h-[60vh] overflow-y-auto pr-4\">\r\n                        <DynamicContentEditor initialContent={currentChapter?.content || []} onContentChange={updateChapterContent} contentRefs={contentRefs} />\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center text-sm text-muted-foreground\">\r\n                        <span>Mendukung Markdown formatting</span>\r\n                        <span>\r\n                          {currentChapter?.content?.length || 0} blok konten\r\n                        </span>\r\n                      </div>\r\n                    </div>}\r\n                </CardContent>\r\n              </Card>\r\n            </div>}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quiz Dialog */}\r\n      <Dialog open={isQuizDialogOpen} onOpenChange={setIsQuizDialogOpen} data-sentry-element=\"Dialog\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n        <DialogContent className=\"sm:max-w-4xl max-h-[80vh] overflow-y-auto p-6\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n          <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n            <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              {editingQuiz.quiz?.questions.length ? 'Edit Quiz' : 'Buat Quiz Baru'}\r\n            </DialogTitle>\r\n            <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              Buat pertanyaan untuk menguji pemahaman siswa\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-6\">\r\n            {/* Quiz Info */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"quizName\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Nama Quiz *</Label>\r\n                <Input id=\"quizName\" placeholder=\"Masukkan nama quiz\" value={editingQuiz.quiz?.name || ''} onChange={e => setEditingQuiz(prev => ({\n                ...prev,\n                quiz: prev.quiz ? {\n                  ...prev.quiz,\n                  name: e.target.value\n                } : null\n              }))} data-sentry-element=\"Input\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"minimumScore\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Nilai Minimum (%)</Label>\r\n                <Input id=\"minimumScore\" type=\"number\" min=\"0\" max=\"100\" value={editingQuiz.quiz?.minimumScore || 70} onChange={e => setEditingQuiz(prev => ({\n                ...prev,\n                quiz: prev.quiz ? {\n                  ...prev.quiz,\n                  minimumScore: parseInt(e.target.value)\n                } : null\n              }))} data-sentry-element=\"Input\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"timeLimit\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Batas Waktu (menit)</Label>\r\n                <Input id=\"timeLimit\" type=\"number\" min=\"1\" value={editingQuiz.quiz?.timeLimit || ''} onChange={e => setEditingQuiz(prev => ({\n                ...prev,\n                quiz: prev.quiz ? {\n                  ...prev.quiz,\n                  timeLimit: e.target.value ? parseInt(e.target.value) : undefined\n                } : null\n              }))} placeholder=\"Tanpa batas waktu\" data-sentry-element=\"Input\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"quizDescription\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Deskripsi</Label>\r\n              <Textarea id=\"quizDescription\" placeholder=\"Jelaskan tentang quiz ini...\" value={editingQuiz.quiz?.description || ''} onChange={e => setEditingQuiz(prev => ({\n              ...prev,\n              quiz: prev.quiz ? {\n                ...prev.quiz,\n                description: e.target.value\n              } : null\n            }))} rows={2} data-sentry-element=\"Textarea\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n            </div>\r\n\r\n            {/* Questions */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <h4 className=\"text-lg font-semibold\">Pertanyaan</h4>\r\n                <Button onClick={createQuestion} data-sentry-element=\"Button\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n                  <Plus className=\"w-4 h-4 mr-2\" data-sentry-element=\"Plus\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n                  Tambah Pertanyaan\r\n                </Button>\r\n              </div>\r\n              \r\n              {editingQuiz.quiz?.questions.length === 0 ? <div className=\"text-center py-8 text-muted-foreground\">\r\n                  <HelpCircle className=\"w-8 h-8 mx-auto mb-2\" />\r\n                  <p className=\"text-sm\">Belum ada pertanyaan</p>\r\n                </div> : <div className=\"space-y-3\">\r\n                  {editingQuiz.quiz?.questions.map((question, index) => <Card key={question.id}>\r\n                      <CardContent className=\"p-4\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                          <div className=\"flex-1\">\r\n                            <div className=\"flex items-center space-x-2 mb-2\">\r\n                              <Badge variant=\"outline\">{index + 1}</Badge>\r\n                              <Badge variant=\"secondary\">\r\n                                {question.type === 'multiple_choice' ? 'Pilihan Ganda' : question.type === 'true_false' ? 'Benar/Salah' : 'Essay'}\r\n                              </Badge>\r\n                              <span className=\"text-sm text-muted-foreground\">\r\n                                {question.points} poin\r\n                              </span>\r\n                            </div>\r\n                            <div className=\"text-sm\">\r\n                              {question.question.map((block, blockIndex) => <React.Fragment key={blockIndex}>\r\n                                  {block.type === 'text' && <p>{block.value}</p>}\r\n                                  {block.type === 'image' && block.value && <img src={block.value} alt={`Question image ${blockIndex}`} className=\"max-w-xs max-h-32 object-contain mt-2\" />}\r\n                                </React.Fragment>)}\r\n                            </div>\r\n                            {question.type === 'multiple_choice' && question.options && <div className=\"mt-2 space-y-1\">\r\n                                {question.options.map((option, optIndex) => <div key={optIndex} className=\"text-xs text-muted-foreground\">\r\n                                    {String.fromCharCode(65 + optIndex)}.\r\n                                    {option.content.map((block, optionBlockIndex) => <React.Fragment key={optionBlockIndex}>\r\n                                        {block.type === 'text' && <span>{block.value}</span>}\r\n                                        {block.type === 'image' && block.value && <img src={block.value} alt={`Option image ${optionBlockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />}\r\n                                      </React.Fragment>)}\r\n                                  </div>)}\r\n                              </div>}\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-1\">\r\n                            <Button variant=\"ghost\" size=\"sm\" onClick={() => editQuestion(question)}>\r\n                              <Edit className=\"w-4 h-4\" />\r\n                            </Button>\r\n                            <AlertDialog>\r\n                              <AlertDialogTrigger asChild>\r\n                                <Button variant=\"ghost\" size=\"sm\">\r\n                                  <Trash2 className=\"w-4 h-4\" />\r\n                                </Button>\r\n                              </AlertDialogTrigger>\r\n                              <AlertDialogContent>\r\n                                <AlertDialogHeader>\r\n                                  <AlertDialogTitle>Hapus Pertanyaan</AlertDialogTitle>\r\n                                  <AlertDialogDescription>\r\n                                    Apakah Anda yakin ingin menghapus pertanyaan ini?\r\n                                  </AlertDialogDescription>\r\n                                </AlertDialogHeader>\r\n                                <AlertDialogFooter>\r\n                                  <AlertDialogCancel>Batal</AlertDialogCancel>\r\n                                  <AlertDialogAction onClick={() => deleteQuestion(question.id)}>\r\n                                    Hapus\r\n                                  </AlertDialogAction>\r\n                                </AlertDialogFooter>\r\n                              </AlertDialogContent>\r\n                            </AlertDialog>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>)}\r\n                </div>}\r\n            </div>\r\n          </div>\r\n          \r\n          <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n            <Button variant=\"outline\" onClick={() => setIsQuizDialogOpen(false)} data-sentry-element=\"Button\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveQuiz} data-sentry-element=\"Button\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              <Save className=\"w-4 h-4 mr-2\" data-sentry-element=\"Save\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n              Simpan Quiz\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Question Dialog */}\r\n      <Dialog open={isQuestionDialogOpen} onOpenChange={setIsQuestionDialogOpen} data-sentry-element=\"Dialog\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n        <DialogContent className=\"sm:max-w-2xl max-h-[80vh] overflow-y-auto p-6\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n          <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n            <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              {editingQuestion?.question ? 'Edit Pertanyaan' : 'Tambah Pertanyaan Baru'}\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"space-y-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"questionType\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Tipe Pertanyaan</Label>\r\n                <Select value={editingQuestion?.type || 'multiple_choice'} onValueChange={(value: 'multiple_choice' | 'true_false' | 'essay') => {\n                setEditingQuestion(prev => {\n                  if (!prev) return null;\n                  const newQuestion = {\n                    ...prev,\n                    type: value\n                  };\n                  if (value === 'true_false') {\n                    newQuestion.options = [{\n                      content: [{\n                        type: 'text',\n                        value: 'True'\n                      }],\n                      isCorrect: false\n                    }, {\n                      content: [{\n                        type: 'text',\n                        value: 'False'\n                      }],\n                      isCorrect: false\n                    }];\n                  } else if (value === 'multiple_choice') {\n                    newQuestion.options = [{\n                      content: [{\n                        type: 'text',\n                        value: ''\n                      }],\n                      isCorrect: false\n                    }, {\n                      content: [{\n                        type: 'text',\n                        value: ''\n                      }],\n                      isCorrect: false\n                    }, {\n                      content: [{\n                        type: 'text',\n                        value: ''\n                      }],\n                      isCorrect: false\n                    }, {\n                      content: [{\n                        type: 'text',\n                        value: ''\n                      }],\n                      isCorrect: false\n                    }];\n                  } else {\n                    newQuestion.options = undefined; // Clear options for essay\n                  }\n                  return newQuestion;\n                });\n              }} data-sentry-element=\"Select\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n                    <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n                    <SelectItem value=\"multiple_choice\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"content-creation-step.tsx\">Pilihan Ganda</SelectItem>\r\n                    <SelectItem value=\"true_false\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"content-creation-step.tsx\">Benar/Salah</SelectItem>\r\n                    <SelectItem value=\"essay\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"content-creation-step.tsx\">Essay</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"questionPoints\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Poin</Label>\r\n                <Input id=\"questionPoints\" type=\"number\" min=\"1\" value={editingQuestion?.points || 1} onChange={e => setEditingQuestion(prev => prev ? {\n                ...prev,\n                points: parseInt(e.target.value)\n              } : null)} data-sentry-element=\"Input\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"questionText\" data-sentry-element=\"Label\" data-sentry-source-file=\"content-creation-step.tsx\">Pertanyaan *</Label>\r\n              <DynamicContentEditor initialContent={editingQuestion?.question || []} onContentChange={content => setEditingQuestion(prev => prev ? {\n              ...prev,\n              question: content\n            } : null)} allowImages={true} // Allow images in questions\n            data-sentry-element=\"DynamicContentEditor\" data-sentry-source-file=\"content-creation-step.tsx\" />\r\n            </div>\r\n            \r\n            {(editingQuestion?.type === 'multiple_choice' || editingQuestion?.type === 'true_false') && <div className=\"space-y-4\">\r\n                <Label>Pilihan Jawaban</Label>\r\n                {editingQuestion.options?.map((option, index) => <div key={index} className=\"flex flex-col space-y-2 border p-3 rounded-md\">\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      {editingQuestion.type === 'multiple_choice' && <span className=\"text-sm font-medium w-6\">\r\n                          {String.fromCharCode(65 + index)}.\r\n                        </span>}\r\n                      {editingQuestion.type === 'multiple_choice' ? <DynamicContentEditor initialContent={option.content || []} onContentChange={content => {\n                  const newOptions = [...(editingQuestion.options || [])];\n                  newOptions[index] = {\n                    ...newOptions[index],\n                    content: content\n                  };\n                  setEditingQuestion(prev => prev ? {\n                    ...prev,\n                    options: newOptions\n                  } : null);\n                }} allowImages={true} // Allow images in options\n                /> : <span className=\"text-base font-medium\">{option.content[0].value}</span>}\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2 mt-2\">\r\n                      <Checkbox id={`option-correct-${index}`} checked={option.isCorrect} onCheckedChange={(checked: boolean) => {\n                  const newOptions = [...(editingQuestion.options || [])];\n                  newOptions[index] = {\n                    ...newOptions[index],\n                    isCorrect: checked as boolean\n                  };\n                  setEditingQuestion(prev => prev ? {\n                    ...prev,\n                    options: newOptions\n                  } : null);\n                }} />\r\n                      <Label htmlFor={`option-correct-${index}`}>Jawaban Benar</Label>\r\n                    </div>\r\n                  </div>)}\r\n              </div>}\r\n            \r\n            \r\n            {editingQuestion && editingQuestion.type === 'essay' && <div className=\"space-y-2\">\r\n                <Label htmlFor=\"essay-answer\">Jawaban Esai</Label>\r\n                <Textarea id=\"essay-answer\" placeholder=\"Masukkan jawaban esai untuk pertanyaan ini\" value={editingQuestion.essayAnswer || ''} onChange={e => setEditingQuestion(prev => prev ? {\n              ...prev,\n              essayAnswer: e.target.value\n            } : null)} rows={4} />\r\n              </div>}\r\n\r\n            {editingQuestion && <div className=\"space-y-2\">\r\n                <Label htmlFor=\"explanation\">Penjelasan Jawaban (Opsional)</Label>\r\n                <DynamicContentEditor initialContent={editingQuestion?.explanation || []} onContentChange={content => {\n              setEditingQuestion(prev => prev ? {\n                ...prev,\n                explanation: content\n              } : null);\n            }} placeholder=\"Jelaskan jawaban yang benar atau berikan informasi tambahan\" allowImages={true} />\r\n              </div>}\r\n          </div>\r\n          \r\n          <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n            <Button variant=\"outline\" onClick={() => setIsQuestionDialogOpen(false)} data-sentry-element=\"Button\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              Batal\r\n            </Button>\r\n            <Button onClick={saveQuestion} data-sentry-element=\"Button\" data-sentry-source-file=\"content-creation-step.tsx\">\r\n              {editingQuestion?.question ? 'Perbarui' : 'Tambah'} Pertanyaan\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>;\n}", "'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Separator } from '@/components/ui/separator';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { CheckCircle, AlertCircle, BookOpen, Users, HelpCircle, Calendar, Code, Image, FileText, Clock, Target, Rocket, Eye, Share2 } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { CourseData } from '../course-creation-wizard';\nimport { toast } from 'sonner';\ninterface PublishingStepProps {\n  data: CourseData;\n  onPublish: () => Promise<void>;\n  isPublishing: boolean;\n}\ninterface ValidationItem {\n  id: string;\n  label: string;\n  status: 'complete' | 'incomplete' | 'warning';\n  description: string;\n  required: boolean;\n}\nexport function PublishingStep({\n  data,\n  onPublish,\n  isPublishing\n}: PublishingStepProps) {\n  const [showDetails, setShowDetails] = useState(false);\n  const getValidationItems = (): ValidationItem[] => {\n    const items: ValidationItem[] = [];\n\n    // Basic course info validation\n    items.push({\n      id: 'course-name',\n      label: 'Nama Course',\n      status: data.name.trim() ? 'complete' : 'incomplete',\n      description: data.name.trim() ? `\"${data.name}\"` : 'Nama course harus diisi',\n      required: true\n    });\n    items.push({\n      id: 'course-description',\n      label: 'Deskripsi Course',\n      status: data.description.trim() ? 'complete' : 'incomplete',\n      description: data.description.trim() ? `${data.description.length} karakter` : 'Deskripsi course harus diisi',\n      required: true\n    });\n    items.push({\n      id: 'course-code',\n      label: 'Kode Course',\n      status: data.courseCode.trim() ? 'complete' : 'incomplete',\n      description: data.courseCode.trim() ? data.courseCode : 'Kode course harus diisi',\n      required: true\n    });\n    items.push({\n      id: 'cover-image',\n      label: 'Cover Image',\n      status: data.coverImage ? 'complete' : 'warning',\n      description: data.coverImage ? 'Cover image telah diupload' : 'Disarankan menambahkan cover image',\n      required: false\n    });\n    items.push({\n      id: 'course-dates',\n      label: 'Tanggal Course',\n      status: data.startDate && data.endDate ? 'complete' : 'warning',\n      description: data.startDate && data.endDate ? `${new Date(data.startDate).toLocaleDateString()} - ${new Date(data.endDate).toLocaleDateString()}` : 'Tanggal mulai dan selesai belum diatur',\n      required: false\n    });\n\n    // Module structure validation\n    const moduleCount = data.modules.length;\n    items.push({\n      id: 'modules',\n      label: 'Struktur Modul',\n      status: moduleCount > 0 ? 'complete' : 'incomplete',\n      description: moduleCount > 0 ? `${moduleCount} modul telah dibuat` : 'Minimal 1 modul harus dibuat',\n      required: true\n    });\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\n    items.push({\n      id: 'chapters',\n      label: 'Chapter',\n      status: totalChapters > 0 ? 'complete' : 'incomplete',\n      description: totalChapters > 0 ? `${totalChapters} chapter telah dibuat` : 'Minimal 1 chapter harus dibuat',\n      required: true\n    });\n\n    // Content validation\n    const chaptersWithContent = data.modules.reduce((acc, module) => acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0);\n    items.push({\n      id: 'content',\n      label: 'Konten Chapter',\n      status: chaptersWithContent === totalChapters ? 'complete' : chaptersWithContent > 0 ? 'warning' : 'incomplete',\n      description: `${chaptersWithContent} dari ${totalChapters} chapter memiliki konten`,\n      required: true\n    });\n\n    // Quiz validation\n    const chaptersWithQuiz = data.modules.reduce((acc, module) => acc + module.chapters.filter(chapter => chapter.hasChapterQuiz && chapter.chapterQuiz).length, 0);\n    const modulesWithQuiz = data.modules.filter(module => module.hasModuleQuiz && module.moduleQuiz).length;\n    items.push({\n      id: 'quizzes',\n      label: 'Quiz',\n      status: chaptersWithQuiz > 0 || modulesWithQuiz > 0 ? 'complete' : 'warning',\n      description: `${chaptersWithQuiz} chapter quiz, ${modulesWithQuiz} module quiz`,\n      required: false\n    });\n\n    // Final exam validation\n    items.push({\n      id: 'final-exam',\n      label: 'Final Exam',\n      status: data.finalExam ? 'complete' : 'warning',\n      description: data.finalExam ? `${data.finalExam.questions.length} pertanyaan` : 'Final exam belum dibuat',\n      required: false\n    });\n    return items;\n  };\n  const validationItems = getValidationItems();\n  const requiredItems = validationItems.filter(item => item.required);\n  const completedRequired = requiredItems.filter(item => item.status === 'complete').length;\n  const canPublish = completedRequired === requiredItems.length;\n  const allCompleted = validationItems.filter(item => item.status === 'complete').length;\n  const completionPercentage = Math.round(allCompleted / validationItems.length * 100);\n  const getCourseStats = () => {\n    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);\n    const totalQuizzes = data.modules.reduce((acc, module) => {\n      const chapterQuizzes = module.chapters.filter(c => c.hasChapterQuiz).length;\n      const moduleQuiz = module.hasModuleQuiz ? 1 : 0;\n      return acc + chapterQuizzes + moduleQuiz;\n    }, 0) + (data.finalExam ? 1 : 0);\n    const estimatedDuration = data.modules.reduce((acc, module) => acc + module.chapters.reduce((chapterAcc, chapter) => chapterAcc + Math.ceil((chapter.content as any[]).filter(block => block.type === 'text').reduce((textAcc, block) => textAcc + block.value.length, 0) / 1000) * 5, 0), 0);\n    return {\n      modules: data.modules.length,\n      chapters: totalChapters,\n      quizzes: totalQuizzes,\n      estimatedDuration: Math.max(estimatedDuration, 30) // minimum 30 minutes\n    };\n  };\n  const stats = getCourseStats();\n  const handlePublish = async () => {\n    if (!canPublish) {\n      toast.error('Lengkapi semua item yang wajib diisi terlebih dahulu');\n      return;\n    }\n    try {\n      await onPublish();\n      toast.success('Course berhasil dipublikasi!');\n    } catch (error) {\n      toast.error('Gagal mempublikasi course');\n    }\n  };\n  return <div className=\"space-y-6\" data-sentry-component=\"PublishingStep\" data-sentry-source-file=\"publishing-step.tsx\">\r\n      {/* Header */}\r\n      <div className=\"text-center space-y-2\">\r\n        <div className={cn(\"w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\", canPublish ? \"bg-green-100 text-green-600\" : \"bg-orange-100 text-orange-600\")}>\r\n          {canPublish ? <Rocket className=\"w-8 h-8\" /> : <AlertCircle className=\"w-8 h-8\" />}\r\n        </div>\r\n        <h3 className=\"text-2xl font-bold\">\r\n          {canPublish ? 'Siap untuk Dipublikasi!' : 'Hampir Selesai'}\r\n        </h3>\r\n        <p className=\"text-muted-foreground\">\r\n          {canPublish ? 'Course Anda sudah siap untuk dipublikasi dan dapat diakses oleh siswa' : 'Lengkapi beberapa item berikut untuk mempublikasi course'}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Progress Overview */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"publishing-step.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"publishing-step.tsx\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <CardTitle className=\"flex items-center space-x-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"publishing-step.tsx\">\r\n                <Target className=\"w-5 h-5\" data-sentry-element=\"Target\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n                <span>Progress Kelengkapan</span>\r\n              </CardTitle>\r\n              <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"publishing-step.tsx\">\r\n                {allCompleted} dari {validationItems.length} item selesai\r\n              </CardDescription>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <div className=\"text-2xl font-bold\">{completionPercentage}%</div>\r\n              <div className=\"text-sm text-muted-foreground\">Selesai</div>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"publishing-step.tsx\">\r\n          <Progress value={completionPercentage} className=\"mb-4\" data-sentry-element=\"Progress\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n          \r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full mx-auto mb-2\">\r\n                <BookOpen className=\"w-5 h-5\" data-sentry-element=\"BookOpen\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.modules}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Modul</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-full mx-auto mb-2\">\r\n                <FileText className=\"w-5 h-5\" data-sentry-element=\"FileText\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.chapters}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Chapter</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-purple-100 text-purple-600 rounded-full mx-auto mb-2\">\r\n                <HelpCircle className=\"w-5 h-5\" data-sentry-element=\"HelpCircle\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.quizzes}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Quiz</div>\r\n            </div>\r\n            \r\n            <div className=\"text-center\">\r\n              <div className=\"flex items-center justify-center w-10 h-10 bg-orange-100 text-orange-600 rounded-full mx-auto mb-2\">\r\n                <Clock className=\"w-5 h-5\" data-sentry-element=\"Clock\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n              </div>\r\n              <div className=\"text-sm font-medium\">{stats.estimatedDuration}</div>\r\n              <div className=\"text-xs text-muted-foreground\">Menit</div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Validation Checklist */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"publishing-step.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"publishing-step.tsx\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <CardTitle className=\"flex items-center space-x-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"publishing-step.tsx\">\r\n              <CheckCircle className=\"w-5 h-5\" data-sentry-element=\"CheckCircle\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n              <span>Checklist Publikasi</span>\r\n            </CardTitle>\r\n            <Button variant=\"ghost\" size=\"sm\" onClick={() => setShowDetails(!showDetails)} data-sentry-element=\"Button\" data-sentry-source-file=\"publishing-step.tsx\">\r\n              {showDetails ? 'Sembunyikan' : 'Lihat'} Detail\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"publishing-step.tsx\">\r\n          <div className=\"space-y-3\">\r\n            {validationItems.map(item => <div key={item.id} className=\"flex items-start space-x-3\">\r\n                <div className={cn(\"w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\", item.status === 'complete' ? \"bg-green-100 text-green-600\" : item.status === 'warning' ? \"bg-orange-100 text-orange-600\" : \"bg-gray-100 text-gray-400\")}>\r\n                  {item.status === 'complete' ? <CheckCircle className=\"w-3 h-3\" /> : item.status === 'warning' ? <AlertCircle className=\"w-3 h-3\" /> : <div className=\"w-2 h-2 bg-current rounded-full\" />}\r\n                </div>\r\n                \r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <span className={cn(\"text-sm font-medium\", item.status === 'complete' ? \"text-green-700\" : item.status === 'warning' ? \"text-orange-700\" : \"text-gray-500\")}>\r\n                      {item.label}\r\n                    </span>\r\n                    {item.required && <Badge variant=\"destructive\" className=\"text-xs px-1 py-0\">\r\n                        Wajib\r\n                      </Badge>}\r\n                  </div>\r\n                  \r\n                  {showDetails && <p className=\"text-xs text-muted-foreground mt-1\">\r\n                      {item.description}\r\n                    </p>}\r\n                </div>\r\n              </div>)}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Course Preview */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"publishing-step.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"publishing-step.tsx\">\r\n          <CardTitle className=\"flex items-center space-x-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"publishing-step.tsx\">\r\n            <Eye className=\"w-5 h-5\" data-sentry-element=\"Eye\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n            <span>Preview Course</span>\r\n          </CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"publishing-step.tsx\">\r\n            Begini tampilan course Anda untuk siswa\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"publishing-step.tsx\">\r\n          <div className=\"border rounded-lg p-4 space-y-4\">\r\n            {/* Course Header */}\r\n            <div className=\"flex items-start space-x-4\">\r\n              {data.coverImage ? <img src={typeof data.coverImage === 'string' ? data.coverImage : URL.createObjectURL(data.coverImage)} alt={data.name} className=\"w-20 h-20 object-cover rounded-lg\" /> : <div className=\"w-20 h-20 bg-muted rounded-lg flex items-center justify-center\">\r\n                  <Image className=\"w-8 h-8 text-muted-foreground\" />\r\n                </div>}\r\n              \r\n              <div className=\"flex-1\">\r\n                <h4 className=\"font-semibold text-lg\">{data.name || 'Nama Course'}</h4>\r\n                <p className=\"text-sm text-muted-foreground mb-2\">\r\n                  {data.description || 'Deskripsi course'}\r\n                </p>\r\n                \r\n                <div className=\"flex items-center space-x-4 text-xs text-muted-foreground\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Code className=\"w-3 h-3\" data-sentry-element=\"Code\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n                    <span>{data.courseCode || 'COURSE-CODE'}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <BookOpen className=\"w-3 h-3\" data-sentry-element=\"BookOpen\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n                    <span>{stats.modules} Modul</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Clock className=\"w-3 h-3\" data-sentry-element=\"Clock\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n                    <span>~{stats.estimatedDuration} Menit</span>\r\n                  </div>\r\n                  {data.startDate && <div className=\"flex items-center space-x-1\">\r\n                      <Calendar className=\"w-3 h-3\" />\r\n                      <span>{new Date(data.startDate).toLocaleDateString()}</span>\r\n                    </div>}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n            \r\n            {/* Module Structure Preview */}\r\n            <div className=\"space-y-2\">\r\n              <h5 className=\"font-medium text-sm\">Struktur Course:</h5>\r\n              {data.modules.length > 0 ? <div className=\"space-y-2\">\r\n                  {data.modules.slice(0, 3).map((module, index) => <div key={module.id} className=\"text-sm\">\r\n                      <div className=\"font-medium\">\r\n                        {index + 1}. {module.name}\r\n                      </div>\r\n                      <div className=\"ml-4 text-xs text-muted-foreground\">\r\n                        {module.chapters.length} chapter\r\n                        {module.hasModuleQuiz && ' • Quiz modul'}\r\n                      </div>\r\n                    </div>)}\r\n                  {data.modules.length > 3 && <div className=\"text-xs text-muted-foreground\">\r\n                      ... dan {data.modules.length - 3} modul lainnya\r\n                    </div>}\r\n                </div> : <p className=\"text-sm text-muted-foreground italic\">\r\n                  Belum ada modul\r\n                </p>}\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Warnings */}\r\n      {!canPublish && <Alert>\r\n          <AlertCircle className=\"h-4 w-4\" />\r\n          <AlertDescription>\r\n            <strong>Perhatian:</strong> Beberapa item wajib belum lengkap. \r\n            Course tidak dapat dipublikasi sampai semua item wajib diselesaikan.\r\n          </AlertDescription>\r\n        </Alert>}\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-between pt-6\">\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          {canPublish ? 'Course siap dipublikasi dan dapat diakses siswa' : `${completedRequired}/${requiredItems.length} item wajib selesai`}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center space-x-3\">\r\n          <Button variant=\"outline\" disabled={isPublishing} data-sentry-element=\"Button\" data-sentry-source-file=\"publishing-step.tsx\">\r\n            <Eye className=\"w-4 h-4 mr-2\" data-sentry-element=\"Eye\" data-sentry-source-file=\"publishing-step.tsx\" />\r\n            Preview\r\n          </Button>\r\n          \r\n          <Button onClick={handlePublish} disabled={!canPublish || isPublishing} className=\"min-w-[120px]\" data-sentry-element=\"Button\" data-sentry-source-file=\"publishing-step.tsx\">\r\n            {isPublishing ? <>\r\n                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\" />\r\n                Publishing...\r\n              </> : <>\r\n                <Rocket className=\"w-4 h-4 mr-2\" />\r\n                Publikasi Course\r\n              </>}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>;\n}", "import React, { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Calendar, ClipboardList, BookOpen, X } from 'lucide-react';\nimport { CourseData, AdmissionsData } from '../course-creation-wizard';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\ninterface AdmissionsStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function AdmissionsStep({\n  data,\n  onUpdate\n}: AdmissionsStepProps) {\n  const admissions = data.admissions || {\n    requirements: [],\n    applicationDeadline: '',\n    prerequisites: []\n  };\n  const [newRequirement, setNewRequirement] = useState('');\n  const [newPrerequisite, setNewPrerequisite] = useState('');\n  const handleUpdate = (field: keyof AdmissionsData, value: string | string[]) => {\n    onUpdate({\n      admissions: {\n        ...admissions,\n        [field]: value\n      }\n    });\n  };\n  const addRequirement = () => {\n    if (newRequirement.trim() !== '' && !admissions.requirements.includes(newRequirement.trim())) {\n      handleUpdate('requirements', [...admissions.requirements, newRequirement.trim()]);\n      setNewRequirement('');\n    }\n  };\n  const removeRequirement = (index: number) => {\n    const updatedRequirements = admissions.requirements.filter((_, i) => i !== index);\n    handleUpdate('requirements', updatedRequirements);\n  };\n  const addPrerequisite = () => {\n    if (newPrerequisite.trim() !== '' && !admissions.prerequisites.includes(newPrerequisite.trim())) {\n      handleUpdate('prerequisites', [...admissions.prerequisites, newPrerequisite.trim()]);\n      setNewPrerequisite('');\n    }\n  };\n  const removePrerequisite = (index: number) => {\n    const updatedPrerequisites = admissions.prerequisites.filter((_, i) => i !== index);\n    handleUpdate('prerequisites', updatedPrerequisites);\n  };\n  return <Card className=\"w-full\" data-sentry-element=\"Card\" data-sentry-component=\"AdmissionsStep\" data-sentry-source-file=\"admissions-step.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"admissions-step.tsx\">\r\n        <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"admissions-step.tsx\">Informasi Pendaftaran</CardTitle>\r\n        <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"admissions-step.tsx\">Detail terkait persyaratan pendaftaran dan prasyarat kursus.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"admissions-step.tsx\">\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <ClipboardList className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"ClipboardList\" data-sentry-source-file=\"admissions-step.tsx\" />\r\n            <Label htmlFor=\"newRequirement\" data-sentry-element=\"Label\" data-sentry-source-file=\"admissions-step.tsx\">Persyaratan</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newRequirement\" value={newRequirement} onChange={e => setNewRequirement(e.target.value)} placeholder=\"Tambahkan persyaratan baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addRequirement();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"admissions-step.tsx\" />\r\n            <Button type=\"button\" onClick={addRequirement} data-sentry-element=\"Button\" data-sentry-source-file=\"admissions-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {admissions.requirements.map((req, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {req}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeRequirement(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Calendar className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Calendar\" data-sentry-source-file=\"admissions-step.tsx\" />\r\n          <Label htmlFor=\"applicationDeadline\" data-sentry-element=\"Label\" data-sentry-source-file=\"admissions-step.tsx\">Batas Waktu Pendaftaran</Label>\r\n        </div>\r\n        <Input id=\"applicationDeadline\" type=\"text\" // Could be a date picker in a real app\n      value={admissions.applicationDeadline} onChange={e => handleUpdate('applicationDeadline', e.target.value)} placeholder=\"Contoh: 2024-12-31\" data-sentry-element=\"Input\" data-sentry-source-file=\"admissions-step.tsx\" />\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <BookOpen className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"BookOpen\" data-sentry-source-file=\"admissions-step.tsx\" />\r\n            <Label htmlFor=\"newPrerequisite\" data-sentry-element=\"Label\" data-sentry-source-file=\"admissions-step.tsx\">Prasyarat</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newPrerequisite\" value={newPrerequisite} onChange={e => setNewPrerequisite(e.target.value)} placeholder=\"Tambahkan prasyarat baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addPrerequisite();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"admissions-step.tsx\" />\r\n            <Button type=\"button\" onClick={addPrerequisite} data-sentry-element=\"Button\" data-sentry-source-file=\"admissions-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {admissions.prerequisites.map((req, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {req}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removePrerequisite(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n}", "import React, { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Book, Hourglass, Award, X } from 'lucide-react';\nimport { CourseData, AcademicsData } from '../course-creation-wizard';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\ninterface AcademicsStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function AcademicsStep({\n  data,\n  onUpdate\n}: AcademicsStepProps) {\n  const academics = data.academics || {\n    credits: 0,\n    workload: '',\n    assessment: []\n  };\n  const [newAssessment, setNewAssessment] = useState('');\n  const handleUpdate = (field: keyof AcademicsData, value: string | number | string[]) => {\n    onUpdate({\n      academics: {\n        ...academics,\n        [field]: value\n      }\n    });\n  };\n  const addAssessment = () => {\n    if (newAssessment.trim() !== '' && !academics.assessment.includes(newAssessment.trim())) {\n      handleUpdate('assessment', [...academics.assessment, newAssessment.trim()]);\n      setNewAssessment('');\n    }\n  };\n  const removeAssessment = (index: number) => {\n    const updatedAssessment = academics.assessment.filter((_, i) => i !== index);\n    handleUpdate('assessment', updatedAssessment);\n  };\n  return <Card className=\"w-full\" data-sentry-element=\"Card\" data-sentry-component=\"AcademicsStep\" data-sentry-source-file=\"academics-step.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"academics-step.tsx\">\r\n        <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"academics-step.tsx\">Informasi Akademik</CardTitle>\r\n        <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"academics-step.tsx\">Detail terkait struktur akademik dan penilaian kursus.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"academics-step.tsx\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Book className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Book\" data-sentry-source-file=\"academics-step.tsx\" />\r\n          <Label htmlFor=\"credits\" data-sentry-element=\"Label\" data-sentry-source-file=\"academics-step.tsx\">Kredit</Label>\r\n        </div>\r\n        <Input id=\"credits\" type=\"number\" value={academics.credits} onChange={e => handleUpdate('credits', parseInt(e.target.value))} placeholder=\"Contoh: 12\" data-sentry-element=\"Input\" data-sentry-source-file=\"academics-step.tsx\" />\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Hourglass className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Hourglass\" data-sentry-source-file=\"academics-step.tsx\" />\r\n          <Label htmlFor=\"workload\" data-sentry-element=\"Label\" data-sentry-source-file=\"academics-step.tsx\">Beban Kerja</Label>\r\n        </div>\r\n        <Input id=\"workload\" type=\"text\" value={academics.workload} onChange={e => handleUpdate('workload', e.target.value)} placeholder=\"Contoh: 12-15 jam/minggu\" data-sentry-element=\"Input\" data-sentry-source-file=\"academics-step.tsx\" />\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Award className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Award\" data-sentry-source-file=\"academics-step.tsx\" />\r\n            <Label htmlFor=\"newAssessment\" data-sentry-element=\"Label\" data-sentry-source-file=\"academics-step.tsx\">Penilaian</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newAssessment\" value={newAssessment} onChange={e => setNewAssessment(e.target.value)} placeholder=\"Tambahkan metode penilaian baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addAssessment();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"academics-step.tsx\" />\r\n            <Button type=\"button\" onClick={addAssessment} data-sentry-element=\"Button\" data-sentry-source-file=\"academics-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {academics.assessment.map((item, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {item}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeAssessment(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n}", "import React, { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { DollarSign, CreditCard, Gift, X } from 'lucide-react';\nimport { CourseData, TuitionAndFinancingData } from '../course-creation-wizard';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\ninterface TuitionFinancingStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function TuitionFinancingStep({\n  data,\n  onUpdate\n}: TuitionFinancingStepProps) {\n  const tuitionAndFinancing = data.tuitionAndFinancing || {\n    totalCost: 0,\n    paymentOptions: [],\n    scholarships: []\n  };\n  const [newPaymentOption, setNewPaymentOption] = useState('');\n  const [newScholarship, setNewScholarship] = useState('');\n  const handleUpdate = (field: keyof TuitionAndFinancingData, value: string | number | string[]) => {\n    onUpdate({\n      tuitionAndFinancing: {\n        ...tuitionAndFinancing,\n        [field]: value\n      }\n    });\n  };\n  const addPaymentOption = () => {\n    if (newPaymentOption.trim() !== '' && !tuitionAndFinancing.paymentOptions.includes(newPaymentOption.trim())) {\n      handleUpdate('paymentOptions', [...tuitionAndFinancing.paymentOptions, newPaymentOption.trim()]);\n      setNewPaymentOption('');\n    }\n  };\n  const removePaymentOption = (index: number) => {\n    const updatedOptions = tuitionAndFinancing.paymentOptions.filter((_, i) => i !== index);\n    handleUpdate('paymentOptions', updatedOptions);\n  };\n  const addScholarship = () => {\n    if (newScholarship.trim() !== '' && !tuitionAndFinancing.scholarships.includes(newScholarship.trim())) {\n      handleUpdate('scholarships', [...tuitionAndFinancing.scholarships, newScholarship.trim()]);\n      setNewScholarship('');\n    }\n  };\n  const removeScholarship = (index: number) => {\n    const updatedScholarships = tuitionAndFinancing.scholarships.filter((_, i) => i !== index);\n    handleUpdate('scholarships', updatedScholarships);\n  };\n  return <Card className=\"w-full\" data-sentry-element=\"Card\" data-sentry-component=\"TuitionFinancingStep\" data-sentry-source-file=\"tuition-financing-step.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"tuition-financing-step.tsx\">\r\n        <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"tuition-financing-step.tsx\">Biaya & Pembiayaan</CardTitle>\r\n        <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"tuition-financing-step.tsx\">Detail terkait biaya kursus, opsi pembayaran, dan peluang beasiswa.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"tuition-financing-step.tsx\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <DollarSign className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"DollarSign\" data-sentry-source-file=\"tuition-financing-step.tsx\" />\r\n          <Label htmlFor=\"totalCost\" data-sentry-element=\"Label\" data-sentry-source-file=\"tuition-financing-step.tsx\">Total Biaya</Label>\r\n        </div>\r\n        <Input id=\"totalCost\" type=\"number\" value={tuitionAndFinancing.totalCost} onChange={e => handleUpdate('totalCost', parseFloat(e.target.value))} placeholder=\"Contoh: 6000000\" data-sentry-element=\"Input\" data-sentry-source-file=\"tuition-financing-step.tsx\" />\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <CreditCard className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"CreditCard\" data-sentry-source-file=\"tuition-financing-step.tsx\" />\r\n            <Label htmlFor=\"newPaymentOption\" data-sentry-element=\"Label\" data-sentry-source-file=\"tuition-financing-step.tsx\">Opsi Pembayaran</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newPaymentOption\" value={newPaymentOption} onChange={e => setNewPaymentOption(e.target.value)} placeholder=\"Tambahkan opsi pembayaran baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addPaymentOption();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"tuition-financing-step.tsx\" />\r\n            <Button type=\"button\" onClick={addPaymentOption} data-sentry-element=\"Button\" data-sentry-source-file=\"tuition-financing-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {tuitionAndFinancing.paymentOptions.map((option, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {option}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removePaymentOption(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Gift className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Gift\" data-sentry-source-file=\"tuition-financing-step.tsx\" />\r\n            <Label htmlFor=\"newScholarship\" data-sentry-element=\"Label\" data-sentry-source-file=\"tuition-financing-step.tsx\">Beasiswa</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newScholarship\" value={newScholarship} onChange={e => setNewScholarship(e.target.value)} placeholder=\"Tambahkan beasiswa baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addScholarship();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"tuition-financing-step.tsx\" />\r\n            <Button type=\"button\" onClick={addScholarship} data-sentry-element=\"Button\" data-sentry-source-file=\"tuition-financing-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {tuitionAndFinancing.scholarships.map((scholarship, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {scholarship}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeScholarship(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n}", "import React, { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Briefcase, Building, DollarSign, X } from 'lucide-react';\nimport { CourseData, CareersData } from '../course-creation-wizard';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\ninterface CareersStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function CareersStep({\n  data,\n  onUpdate\n}: CareersStepProps) {\n  const careers = data.careers || {\n    outcomes: [],\n    industries: [],\n    averageSalary: ''\n  };\n  const [newOutcome, setNewOutcome] = useState('');\n  const [newIndustry, setNewIndustry] = useState('');\n  const handleUpdate = (field: keyof CareersData, value: string | string[]) => {\n    onUpdate({\n      careers: {\n        ...careers,\n        [field]: value\n      }\n    });\n  };\n  const addOutcome = () => {\n    if (newOutcome.trim() !== '' && !careers.outcomes.includes(newOutcome.trim())) {\n      handleUpdate('outcomes', [...careers.outcomes, newOutcome.trim()]);\n      setNewOutcome('');\n    }\n  };\n  const removeOutcome = (index: number) => {\n    const updatedOutcomes = careers.outcomes.filter((_, i) => i !== index);\n    handleUpdate('outcomes', updatedOutcomes);\n  };\n  const addIndustry = () => {\n    if (newIndustry.trim() !== '' && !careers.industries.includes(newIndustry.trim())) {\n      handleUpdate('industries', [...careers.industries, newIndustry.trim()]);\n      setNewIndustry('');\n    }\n  };\n  const removeIndustry = (index: number) => {\n    const updatedIndustries = careers.industries.filter((_, i) => i !== index);\n    handleUpdate('industries', updatedIndustries);\n  };\n  return <Card className=\"w-full\" data-sentry-element=\"Card\" data-sentry-component=\"CareersStep\" data-sentry-source-file=\"careers-step.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"careers-step.tsx\">\r\n        <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"careers-step.tsx\">Peluang Karir</CardTitle>\r\n        <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"careers-step.tsx\">Detail terkait hasil karir dan industri yang relevan setelah kursus.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"careers-step.tsx\">\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Briefcase className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Briefcase\" data-sentry-source-file=\"careers-step.tsx\" />\r\n            <Label htmlFor=\"newOutcome\" data-sentry-element=\"Label\" data-sentry-source-file=\"careers-step.tsx\">Hasil</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newOutcome\" value={newOutcome} onChange={e => setNewOutcome(e.target.value)} placeholder=\"Tambahkan hasil karir baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addOutcome();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"careers-step.tsx\" />\r\n            <Button type=\"button\" onClick={addOutcome} data-sentry-element=\"Button\" data-sentry-source-file=\"careers-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {careers.outcomes.map((outcome, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {outcome}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeOutcome(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Building className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"Building\" data-sentry-source-file=\"careers-step.tsx\" />\r\n            <Label htmlFor=\"newIndustry\" data-sentry-element=\"Label\" data-sentry-source-file=\"careers-step.tsx\">Industri</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newIndustry\" value={newIndustry} onChange={e => setNewIndustry(e.target.value)} placeholder=\"Tambahkan industri baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addIndustry();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"careers-step.tsx\" />\r\n            <Button type=\"button\" onClick={addIndustry} data-sentry-element=\"Button\" data-sentry-source-file=\"careers-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {careers.industries.map((industry, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {industry}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeIndustry(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <DollarSign className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"DollarSign\" data-sentry-source-file=\"careers-step.tsx\" />\r\n          <Label htmlFor=\"averageSalary\" data-sentry-element=\"Label\" data-sentry-source-file=\"careers-step.tsx\">Rata-rata Gaji</Label>\r\n        </div>\r\n        <Input id=\"averageSalary\" type=\"text\" value={careers.averageSalary} onChange={e => handleUpdate('averageSalary', e.target.value)} placeholder=\"Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun\" data-sentry-element=\"Input\" data-sentry-source-file=\"careers-step.tsx\" />\r\n      </CardContent>\r\n    </Card>;\n}", "import React, { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Plus, X, MessageSquare, HardHat, LifeBuoy } from 'lucide-react';\nimport { CourseData, StudentExperienceData } from '../course-creation-wizard';\nimport { Badge } from '@/components/ui/badge';\ninterface StudentExperienceStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function StudentExperienceStep({\n  data,\n  onUpdate\n}: StudentExperienceStepProps) {\n  const studentExperience = data.studentExperience || {\n    testimonials: [],\n    facilities: [],\n    support: []\n  };\n  const [newFacility, setNewFacility] = useState('');\n  const [newSupport, setNewSupport] = useState('');\n  const handleUpdate = (field: keyof StudentExperienceData, value: string | string[] | {\n    name: string;\n    feedback: string;\n  }[]) => {\n    onUpdate({\n      studentExperience: {\n        ...studentExperience,\n        [field]: value\n      }\n    });\n  };\n  const addTestimonial = () => {\n    handleUpdate('testimonials', [...studentExperience.testimonials, {\n      name: '',\n      feedback: ''\n    }]);\n  };\n  const updateTestimonial = (index: number, field: 'name' | 'feedback', value: string) => {\n    const updatedTestimonials = [...studentExperience.testimonials];\n    updatedTestimonials[index] = {\n      ...updatedTestimonials[index],\n      [field]: value\n    };\n    handleUpdate('testimonials', updatedTestimonials);\n  };\n  const removeTestimonial = (index: number) => {\n    const updatedTestimonials = studentExperience.testimonials.filter((_, i) => i !== index);\n    handleUpdate('testimonials', updatedTestimonials);\n  };\n  const addFacility = () => {\n    if (newFacility.trim() !== '' && !studentExperience.facilities.includes(newFacility.trim())) {\n      handleUpdate('facilities', [...studentExperience.facilities, newFacility.trim()]);\n      setNewFacility('');\n    }\n  };\n  const removeFacility = (index: number) => {\n    const updatedFacilities = studentExperience.facilities.filter((_, i) => i !== index);\n    handleUpdate('facilities', updatedFacilities);\n  };\n  const addSupport = () => {\n    if (newSupport.trim() !== '' && !studentExperience.support.includes(newSupport.trim())) {\n      handleUpdate('support', [...studentExperience.support, newSupport.trim()]);\n      setNewSupport('');\n    }\n  };\n  const removeSupport = (index: number) => {\n    const updatedSupport = studentExperience.support.filter((_, i) => i !== index);\n    handleUpdate('support', updatedSupport);\n  };\n  return <Card className=\"w-full\" data-sentry-element=\"Card\" data-sentry-component=\"StudentExperienceStep\" data-sentry-source-file=\"student-experience-step.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"student-experience-step.tsx\">\r\n        <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"student-experience-step.tsx\">Pengalaman Mahasiswa</CardTitle>\r\n        <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"student-experience-step.tsx\">Detail terkait pengalaman, fasilitas, dan dukungan yang akan didapat mahasiswa.</CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"student-experience-step.tsx\">\r\n        <div>\r\n          <div className=\"flex items-center space-x-2 mb-2\">\r\n            <MessageSquare className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"MessageSquare\" data-sentry-source-file=\"student-experience-step.tsx\" />\r\n            <Label data-sentry-element=\"Label\" data-sentry-source-file=\"student-experience-step.tsx\">Testimoni</Label>\r\n          </div>\r\n          {studentExperience.testimonials.map((testimonial, index) => <div key={index} className=\"flex items-end space-x-2 mb-4\">\r\n              <div className=\"flex-grow space-y-2\">\r\n                <Input placeholder=\"Nama\" value={testimonial.name} onChange={e => updateTestimonial(index, 'name', e.target.value)} />\r\n                <Textarea placeholder=\"Umpan Balik\" value={testimonial.feedback} onChange={e => updateTestimonial(index, 'feedback', e.target.value)} />\r\n              </div>\r\n              <Button variant=\"destructive\" size=\"icon\" onClick={() => removeTestimonial(index)}>\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>)}\r\n          <Button variant=\"outline\" onClick={addTestimonial} data-sentry-element=\"Button\" data-sentry-source-file=\"student-experience-step.tsx\">\r\n            <Plus className=\"h-4 w-4 mr-2\" data-sentry-element=\"Plus\" data-sentry-source-file=\"student-experience-step.tsx\" /> Tambah Testimoni\r\n          </Button>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <HardHat className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"HardHat\" data-sentry-source-file=\"student-experience-step.tsx\" />\r\n            <Label htmlFor=\"newFacility\" data-sentry-element=\"Label\" data-sentry-source-file=\"student-experience-step.tsx\">Fasilitas</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newFacility\" value={newFacility} onChange={e => setNewFacility(e.target.value)} placeholder=\"Tambahkan fasilitas baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addFacility();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"student-experience-step.tsx\" />\r\n            <Button type=\"button\" onClick={addFacility} data-sentry-element=\"Button\" data-sentry-source-file=\"student-experience-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {studentExperience.facilities.map((facility, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {facility}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeFacility(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <LifeBuoy className=\"h-5 w-5 text-gray-500\" data-sentry-element=\"LifeBuoy\" data-sentry-source-file=\"student-experience-step.tsx\" />\r\n            <Label htmlFor=\"newSupport\" data-sentry-element=\"Label\" data-sentry-source-file=\"student-experience-step.tsx\">Dukungan</Label>\r\n          </div>\r\n          <div className=\"flex space-x-2\">\r\n            <Input id=\"newSupport\" value={newSupport} onChange={e => setNewSupport(e.target.value)} placeholder=\"Tambahkan dukungan baru\" onKeyPress={e => {\n            if (e.key === 'Enter') {\n              e.preventDefault();\n              addSupport();\n            }\n          }} data-sentry-element=\"Input\" data-sentry-source-file=\"student-experience-step.tsx\" />\r\n            <Button type=\"button\" onClick={addSupport} data-sentry-element=\"Button\" data-sentry-source-file=\"student-experience-step.tsx\">Tambah</Button>\r\n          </div>\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">\r\n            {studentExperience.support.map((supportItem, index) => <Badge key={index} variant=\"secondary\" className=\"pr-1\">\r\n                {supportItem}\r\n                <Button type=\"button\" variant=\"ghost\" size=\"sm\" className=\"ml-1 h-auto px-1 py-0.5\" onClick={() => removeSupport(index)}>\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>)}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n}", "import React from 'react';\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';\nimport { CourseData } from '../course-creation-wizard';\nimport { AdmissionsStep } from './admissions-step';\nimport { AcademicsStep } from './academics-step';\nimport { TuitionFinancingStep } from './tuition-financing-step';\nimport { CareersStep } from './careers-step';\nimport { StudentExperienceStep } from './student-experience-step';\ninterface CourseDetailsStepProps {\n  data: CourseData;\n  onUpdate: (updates: Partial<CourseData>) => void;\n}\nexport function CourseDetailsStep({\n  data,\n  onUpdate\n}: CourseDetailsStepProps) {\n  return <Card data-sentry-element=\"Card\" data-sentry-component=\"CourseDetailsStep\" data-sentry-source-file=\"course-details-step.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"course-details-step.tsx\">\r\n        <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"course-details-step.tsx\">Detail Course</CardTitle>\r\n        <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"course-details-step.tsx\">\r\n          Kelola detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa.\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"course-details-step.tsx\">\r\n        <Tabs defaultValue=\"admissions\" className=\"w-full\" data-sentry-element=\"Tabs\" data-sentry-source-file=\"course-details-step.tsx\">\r\n          <TabsList className=\"grid w-full grid-cols-5\" data-sentry-element=\"TabsList\" data-sentry-source-file=\"course-details-step.tsx\">\r\n            <TabsTrigger value=\"admissions\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"course-details-step.tsx\">Penerimaan</TabsTrigger>\r\n            <TabsTrigger value=\"academics\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"course-details-step.tsx\">Akademik</TabsTrigger>\r\n            <TabsTrigger value=\"tuition-financing\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"course-details-step.tsx\">Biaya & Pembiayaan</TabsTrigger>\r\n            <TabsTrigger value=\"careers\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"course-details-step.tsx\">Karir</TabsTrigger>\r\n            <TabsTrigger value=\"student-experience\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"course-details-step.tsx\">Pengalaman Siswa</TabsTrigger>\r\n          </TabsList>\r\n          <div className=\"h-[400px] overflow-y-auto pr-4\"> {/* Fixed height with scroll */}\r\n            <TabsContent value=\"admissions\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"course-details-step.tsx\">\r\n              <AdmissionsStep data={data} onUpdate={onUpdate} data-sentry-element=\"AdmissionsStep\" data-sentry-source-file=\"course-details-step.tsx\" />\r\n            </TabsContent>\r\n            <TabsContent value=\"academics\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"course-details-step.tsx\">\r\n              <AcademicsStep data={data} onUpdate={onUpdate} data-sentry-element=\"AcademicsStep\" data-sentry-source-file=\"course-details-step.tsx\" />\r\n            </TabsContent>\r\n            <TabsContent value=\"tuition-financing\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"course-details-step.tsx\">\r\n              <TuitionFinancingStep data={data} onUpdate={onUpdate} data-sentry-element=\"TuitionFinancingStep\" data-sentry-source-file=\"course-details-step.tsx\" />\r\n            </TabsContent>\r\n            <TabsContent value=\"careers\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"course-details-step.tsx\">\r\n              <CareersStep data={data} onUpdate={onUpdate} data-sentry-element=\"CareersStep\" data-sentry-source-file=\"course-details-step.tsx\" />\r\n            </TabsContent>\r\n            <TabsContent value=\"student-experience\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"course-details-step.tsx\">\r\n              <StudentExperienceStep data={data} onUpdate={onUpdate} data-sentry-element=\"StudentExperienceStep\" data-sentry-source-file=\"course-details-step.tsx\" />\r\n            </TabsContent>\r\n          </div>\r\n        </Tabs>\r\n      </CardContent>\r\n    </Card>;\n}", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Progress } from '@/components/ui/progress';\nimport { Separator } from '@/components/ui/separator';\nimport { ChevronLeft, ChevronRight, Check } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// Step components\nimport { BasicInfoStep } from './steps/basic-info-step';\nimport { ModuleStructureStep } from './steps/module-structure-step';\nimport { ContentCreationStep } from './steps/content-creation-step';\nimport { PublishingStep } from './steps/publishing-step';\nimport { CourseDetailsStep } from './steps/course-details-step';\nexport interface CourseData {\n  // Basic Info\n  name: string;\n  description: string;\n  instructor: string;\n  courseCode: string;\n  type: 'self_paced' | 'verified';\n  enrollmentType: 'code' | 'invitation' | 'both' | 'purchase';\n  startDate?: Date | null;\n  endDate?: Date | null;\n  coverImage?: File;\n  coverImagePreview?: string;\n  isPurchasable?: boolean;\n  price?: number;\n  currency?: string;\n  previewMode?: boolean;\n\n  // Module Structure\n  modules: ModuleData[];\n\n  // Publishing\n  isPublished: boolean;\n  assignedClasses: number[];\n  finalExam?: QuizData;\n\n  // Course Details\n  admissions?: AdmissionsData;\n  academics?: AcademicsData;\n  tuitionAndFinancing?: TuitionAndFinancingData;\n  careers?: CareersData;\n  studentExperience?: StudentExperienceData;\n}\nexport interface ModuleData {\n  id: string;\n  name: string;\n  description: string;\n  orderIndex: number;\n  chapters: ChapterData[];\n  hasModuleQuiz: boolean;\n  moduleQuiz?: QuizData;\n}\nexport interface ChapterData {\n  id: string;\n  name: string;\n  content: any[]; // Changed to any[] to accommodate JSON structure\n  orderIndex: number;\n  hasChapterQuiz: boolean;\n  chapterQuiz?: QuizData;\n}\nexport interface QuizData {\n  id: string;\n  name: string;\n  description: string;\n  questions: QuestionData[];\n  timeLimit?: number;\n  minimumScore: number;\n}\nexport interface AdmissionsData {\n  requirements: string[];\n  applicationDeadline: string;\n  prerequisites: string[];\n}\nexport interface AcademicsData {\n  credits: number;\n  workload: string;\n  assessment: string[];\n}\nexport interface TuitionAndFinancingData {\n  totalCost: number;\n  paymentOptions: string[];\n  scholarships: string[];\n}\nexport interface CareersData {\n  outcomes: string[];\n  industries: string[];\n  averageSalary: string;\n}\nexport interface StudentExperienceData {\n  testimonials: {\n    name: string;\n    feedback: string;\n  }[];\n  facilities: string[];\n  support: string[];\n}\nexport interface QuestionData {\n  id: string;\n  type: 'multiple_choice' | 'true_false' | 'essay';\n  question: any[]; // Changed to any[] to accommodate JSON structure\n  options?: {\n    content: any[];\n    isCorrect: boolean;\n  }[]; // Updated for new JSON structure\n  essayAnswer?: string | null; // Renamed from correctAnswer, can be null\n  explanation?: any[] | null; // New column, can be null\n  points: number;\n  orderIndex: number;\n}\nconst STEPS = [{\n  id: 'basic-info',\n  title: 'Informasi Dasar',\n  description: 'Detail course dan pengaturan dasar'\n}, {\n  id: 'module-structure',\n  title: 'Struktur Modul',\n  description: 'Buat modul dan chapter untuk course'\n}, {\n  id: 'content-creation',\n  title: 'Pembuatan Konten',\n  description: 'Tambahkan konten dan quiz untuk setiap chapter'\n}, {\n  id: 'course-details',\n  title: 'Informasi Tambahan',\n  description: 'Detail penerimaan, akademik, pembiayaan, karir, dan pengalaman siswa'\n}, {\n  id: 'publishing',\n  title: 'Publikasi',\n  description: 'Review dan publikasikan course'\n}];\ninterface CourseCreationWizardProps {\n  onComplete: (courseData: CourseData) => Promise<void>;\n  onCancel: () => void;\n  initialData?: Partial<CourseData>;\n}\nexport function CourseCreationWizard({\n  onComplete,\n  onCancel,\n  initialData\n}: CourseCreationWizardProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [courseData, setCourseData] = useState<CourseData>({\n    name: initialData?.name || '',\n    description: initialData?.description || '',\n    instructor: initialData?.instructor || '',\n    courseCode: initialData?.courseCode || '',\n    type: initialData?.type || 'self_paced',\n    enrollmentType: initialData?.enrollmentType || 'code',\n    startDate: initialData?.startDate,\n    endDate: initialData?.endDate,\n    coverImage: initialData?.coverImage,\n    coverImagePreview: initialData?.coverImagePreview,\n    isPurchasable: initialData?.isPurchasable ?? false,\n    price: initialData?.price,\n    currency: initialData?.currency || '',\n    previewMode: initialData?.previewMode ?? false,\n    modules: initialData?.modules || [],\n    isPublished: initialData?.isPublished ?? false,\n    assignedClasses: initialData?.assignedClasses || [],\n    finalExam: initialData?.finalExam,\n    admissions: initialData?.admissions || {\n      requirements: [],\n      applicationDeadline: '',\n      prerequisites: []\n    },\n    academics: initialData?.academics || {\n      credits: 0,\n      workload: '',\n      assessment: []\n    },\n    tuitionAndFinancing: initialData?.tuitionAndFinancing || {\n      totalCost: 0,\n      paymentOptions: [],\n      scholarships: []\n    },\n    careers: initialData?.careers || {\n      outcomes: [],\n      industries: [],\n      averageSalary: ''\n    },\n    studentExperience: initialData?.studentExperience || {\n      testimonials: [],\n      facilities: [],\n      support: []\n    }\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Load AI generated data from sessionStorage on component mount\n  useEffect(() => {\n    const loadAIGeneratedData = () => {\n      try {\n        const aiGeneratedData = sessionStorage.getItem('ai_generated_course_data');\n        if (aiGeneratedData) {\n          const parsedData = JSON.parse(aiGeneratedData);\n\n          // Merge AI generated data with existing course data\n          setCourseData(prev => ({\n            ...prev,\n            name: parsedData.name || prev.name,\n            description: parsedData.description || prev.description,\n            courseCode: parsedData.courseCode || prev.courseCode,\n            modules: parsedData.modules || prev.modules,\n            finalExam: parsedData.finalExam || prev.finalExam,\n            ...initialData // initialData takes precedence\n          }));\n\n          // Clear the session storage after loading\n          sessionStorage.removeItem('ai_generated_course_data');\n\n          // If we have AI generated modules, skip to content creation step\n          if (parsedData.modules && parsedData.modules.length > 0) {\n            setCurrentStep(2); // Skip to Content Creation step\n          }\n        }\n      } catch (error) {\n        console.error('Error loading AI generated data:', error);\n      }\n    };\n    loadAIGeneratedData();\n  }, [initialData]);\n  const updateCourseData = (updates: Partial<CourseData>) => {\n    setCourseData(prev => ({\n      ...prev,\n      ...updates\n    }));\n  };\n  const validateStepData = (step: number): boolean => {\n    switch (step) {\n      case 0:\n        // Basic Info\n        const basicValidation = !!courseData.name && !!courseData.description && !!courseData.instructor && !!courseData.courseCode;\n        // Additional validation for purchase type\n        if (courseData.enrollmentType === 'purchase') {\n          return basicValidation && !!courseData.price && courseData.price > 0 && !!courseData.currency;\n        }\n        return basicValidation;\n      case 1:\n        // Module Structure\n        return courseData.modules.length > 0 && courseData.modules.every(module => !!module.name && module.chapters.length > 0);\n      case 2:\n        // Content Creation\n        return courseData.modules.every(module => module.chapters.every(chapter => !!chapter.content));\n      case 3:\n        // Course Details (combining Admissions, Academics, Tuition & Financing, Careers, Student Experience)\n        // Validation for the combined step: at least one field in any of the sub-sections should have data.\n        const admissionsValid = !!courseData.admissions && (courseData.admissions.requirements.length > 0 || !!courseData.admissions.applicationDeadline || courseData.admissions.prerequisites.length > 0);\n        const academicsValid = !!courseData.academics && (courseData.academics.credits > 0 || !!courseData.academics.workload || courseData.academics.assessment.length > 0);\n        const tuitionFinancingValid = !!courseData.tuitionAndFinancing && (!!courseData.tuitionAndFinancing.totalCost || courseData.tuitionAndFinancing.paymentOptions.length > 0 || courseData.tuitionAndFinancing.scholarships.length > 0);\n        const careersValid = !!courseData.careers && (courseData.careers.outcomes.length > 0 || courseData.careers.industries.length > 0 || !!courseData.careers.averageSalary);\n        const studentExperienceValid = !!courseData.studentExperience && (courseData.studentExperience.testimonials.length > 0 || courseData.studentExperience.facilities.length > 0 || courseData.studentExperience.support.length > 0);\n        return admissionsValid || academicsValid || tuitionFinancingValid || careersValid || studentExperienceValid;\n      case 4:\n        // Publishing\n        return true;\n      // Publishing step doesn't have its own data to validate\n      default:\n        return false;\n    }\n  };\n  const canProceedToNext = () => {\n    return validateStepData(currentStep);\n  };\n  const handleNext = () => {\n    if (currentStep < STEPS.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleComplete = async () => {\n    setIsSubmitting(true);\n    try {\n      await onComplete(courseData);\n    } catch (error) {\n      console.error('Error creating course:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 0:\n        return <BasicInfoStep data={courseData} onUpdate={updateCourseData} />;\n      case 1:\n        return <ModuleStructureStep data={courseData} onUpdate={updateCourseData} />;\n      case 2:\n        return <ContentCreationStep data={courseData} onUpdate={updateCourseData} />;\n      case 3:\n        return <CourseDetailsStep data={courseData} onUpdate={updateCourseData} />;\n      case 4:\n        return <PublishingStep data={courseData} onPublish={handleComplete} isPublishing={isSubmitting} />;\n      default:\n        return null;\n    }\n  };\n  const progressPercentage = (currentStep + 1) / STEPS.length * 100;\n  return <div className=\"w-full p-6 space-y-6\" data-sentry-component=\"CourseCreationWizard\" data-sentry-source-file=\"course-creation-wizard.tsx\">\r\n      {/* Step Indicator */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"course-creation-wizard.tsx\">\r\n        <CardContent className=\"pt-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"course-creation-wizard.tsx\">\r\n          <div className=\"flex items-center justify-between gap-x-4 overflow-x-auto pb-4 px-4\">\r\n            {STEPS.map((step, index) => <div key={step.id} className=\"flex flex-col items-center flex-grow\">\r\n                <div className={cn(\"w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors duration-200\", index === currentStep ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground\", index < currentStep && \"bg-green-500 text-white\" // Completed step\n            )}>\r\n                  {index < currentStep ? <Check className=\"w-4 h-4\" /> : index + 1}\r\n                </div>\r\n                <span className={cn(\"mt-1 text-xs text-center whitespace-nowrap\", index === currentStep ? \"text-primary font-medium\" : \"text-muted-foreground\")}>\r\n                  {step.title}\r\n                </span>\r\n              </div>)}\r\n          </div>\r\n          <Separator className=\"my-4\" data-sentry-element=\"Separator\" data-sentry-source-file=\"course-creation-wizard.tsx\" />\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between text-sm\">\r\n              <span>Langkah {currentStep + 1} dari {STEPS.length}</span>\r\n              <span>{Math.round(progressPercentage)}% selesai</span>\r\n            </div>\r\n            <Progress value={progressPercentage} className=\"h-2\" data-sentry-element=\"Progress\" data-sentry-source-file=\"course-creation-wizard.tsx\" />\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Step Content */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"course-creation-wizard.tsx\">\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"course-creation-wizard.tsx\">\r\n          {renderStepContent()}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Navigation Buttons */}\r\n      <div className=\"flex justify-between\">\r\n        <Button variant=\"outline\" onClick={handlePrevious} disabled={currentStep === 0} data-sentry-element=\"Button\" data-sentry-source-file=\"course-creation-wizard.tsx\">\r\n          <ChevronLeft className=\"w-4 h-4 mr-2\" data-sentry-element=\"ChevronLeft\" data-sentry-source-file=\"course-creation-wizard.tsx\" />\r\n          Sebelumnya\r\n        </Button>\r\n        \r\n        <div className=\"flex space-x-2\">\r\n          {currentStep === STEPS.length - 1 ? <Button onClick={handleComplete} disabled={!canProceedToNext() || isSubmitting}>\r\n              {isSubmitting ? 'Membuat Course...' : 'Selesai & Buat Course'}\r\n            </Button> : <Button onClick={handleNext} disabled={!canProceedToNext()}>\r\n              Selanjutnya\r\n              <ChevronRight className=\"w-4 h-4 ml-2\" />\r\n            </Button>}\r\n        </div>\r\n      </div>\r\n    </div>;\n}", "\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst alertVariants = cva('relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current', {\n  variants: {\n    variant: {\n      default: 'bg-card text-card-foreground',\n      destructive: 'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\n  return <div data-slot='alert' role='alert' className={cn(alertVariants({\n    variant\n  }), className)} {...props} data-sentry-component=\"Alert\" data-sentry-source-file=\"alert.tsx\" />;\n}\nfunction AlertTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-title' className={cn('col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight', className)} {...props} data-sentry-component=\"AlertTitle\" data-sentry-source-file=\"alert.tsx\" />;\n}\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-description' className={cn('text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed', className)} {...props} data-sentry-component=\"AlertDescription\" data-sentry-source-file=\"alert.tsx\" />;\n}\nexport { Alert, AlertTitle, AlertDescription };", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function TeacherLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has teacher role\n    requireRole('teacher');\n  }, []);\n  return <>{children}</>;\n}", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cn } from '@/lib/utils';\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return <LabelPrimitive.Root data-slot='label' className={cn('flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50', className)} {...props} data-sentry-element=\"LabelPrimitive.Root\" data-sentry-component=\"Label\" data-sentry-source-file=\"label.tsx\" />;\n}\nexport { Label };", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport { cn } from \"@/lib/utils\";\nconst Tabs = TabsPrimitive.Root;\nconst TabsList = React.forwardRef<React.ElementRef<typeof TabsPrimitive.List>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.List ref={ref} className={cn(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className)} {...props} />);\nTabsList.displayName = TabsPrimitive.List.displayName;\nconst TabsTrigger = React.forwardRef<React.ElementRef<typeof TabsPrimitive.Trigger>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.Trigger ref={ref} className={cn(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer\", className)} {...props} />);\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\nconst TabsContent = React.forwardRef<React.ElementRef<typeof TabsPrimitive.Content>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.Content ref={ref} className={cn(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className)} {...props} />);\nTabsContent.displayName = TabsPrimitive.Content.displayName;\nexport { Tabs, TabsList, TabsTrigger, TabsContent };", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/teacher',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/teacher',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/teacher',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/teacher',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "'use client';\n\nimport * as React from 'react';\nimport * as CheckboxPrimitive from '@radix-ui/react-checkbox';\nimport { CheckIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return <CheckboxPrimitive.Root data-slot='checkbox' className={cn('peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50', className)} {...props} data-sentry-element=\"CheckboxPrimitive.Root\" data-sentry-component=\"Checkbox\" data-sentry-source-file=\"checkbox.tsx\">\r\n      <CheckboxPrimitive.Indicator data-slot='checkbox-indicator' className='flex items-center justify-center text-current transition-none' data-sentry-element=\"CheckboxPrimitive.Indicator\" data-sentry-source-file=\"checkbox.tsx\">\r\n        <CheckIcon className='size-3.5' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"checkbox.tsx\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>;\n}\nexport { Checkbox };", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "'use client';\n\nimport * as React from 'react';\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\nimport { cn } from '@/lib/utils';\nimport { buttonVariants } from '@/components/ui/button';\nfunction AlertDialog({\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\n  return <AlertDialogPrimitive.Root data-slot='alert-dialog' {...props} data-sentry-element=\"AlertDialogPrimitive.Root\" data-sentry-component=\"AlertDialog\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogTrigger({\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\n  return <AlertDialogPrimitive.Trigger data-slot='alert-dialog-trigger' {...props} data-sentry-element=\"AlertDialogPrimitive.Trigger\" data-sentry-component=\"AlertDialogTrigger\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogPortal({\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\n  return <AlertDialogPrimitive.Portal data-slot='alert-dialog-portal' {...props} data-sentry-element=\"AlertDialogPrimitive.Portal\" data-sentry-component=\"AlertDialogPortal\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\n  return <AlertDialogPrimitive.Overlay data-slot='alert-dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Overlay\" data-sentry-component=\"AlertDialogOverlay\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\n  return <AlertDialogPortal data-sentry-element=\"AlertDialogPortal\" data-sentry-component=\"AlertDialogContent\" data-sentry-source-file=\"alert-dialog.tsx\">\r\n      <AlertDialogOverlay data-sentry-element=\"AlertDialogOverlay\" data-sentry-source-file=\"alert-dialog.tsx\" />\r\n      <AlertDialogPrimitive.Content data-slot='alert-dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Content\" data-sentry-source-file=\"alert-dialog.tsx\" />\r\n    </AlertDialogPortal>;\n}\nfunction AlertDialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"AlertDialogHeader\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"AlertDialogFooter\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\n  return <AlertDialogPrimitive.Title data-slot='alert-dialog-title' className={cn('text-lg font-semibold', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Title\" data-sentry-component=\"AlertDialogTitle\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\n  return <AlertDialogPrimitive.Description data-slot='alert-dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Description\" data-sentry-component=\"AlertDialogDescription\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogAction({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\n  return <AlertDialogPrimitive.Action className={cn(buttonVariants(), className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Action\" data-sentry-component=\"AlertDialogAction\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nfunction AlertDialogCancel({\n  className,\n  ...props\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\n  return <AlertDialogPrimitive.Cancel className={cn(buttonVariants({\n    variant: 'outline'\n  }), className)} {...props} data-sentry-element=\"AlertDialogPrimitive.Cancel\" data-sentry-component=\"AlertDialogCancel\" data-sentry-source-file=\"alert-dialog.tsx\" />;\n}\nexport { AlertDialog, AlertDialogPortal, AlertDialogOverlay, AlertDialogTrigger, AlertDialogContent, AlertDialogHeader, AlertDialogFooter, AlertDialogTitle, AlertDialogDescription, AlertDialogAction, AlertDialogCancel };", "'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot='dialog' {...props} data-sentry-element=\"DialogPrimitive.Root\" data-sentry-component=\"Dialog\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} data-sentry-element=\"DialogPrimitive.Trigger\" data-sentry-component=\"DialogTrigger\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} data-sentry-element=\"DialogPrimitive.Portal\" data-sentry-component=\"DialogPortal\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} data-sentry-element=\"DialogPrimitive.Close\" data-sentry-component=\"DialogClose\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return <DialogPrimitive.Overlay data-slot='dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"DialogPrimitive.Overlay\" data-sentry-component=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return <DialogPortal data-slot='dialog-portal' data-sentry-element=\"DialogPortal\" data-sentry-component=\"DialogContent\" data-sentry-source-file=\"dialog.tsx\">\r\n      <DialogOverlay data-sentry-element=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />\r\n      <DialogPrimitive.Content data-slot='dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"DialogPrimitive.Content\" data-sentry-source-file=\"dialog.tsx\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\" data-sentry-element=\"DialogPrimitive.Close\" data-sentry-source-file=\"dialog.tsx\">\r\n          <XIcon data-sentry-element=\"XIcon\" data-sentry-source-file=\"dialog.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>;\n}\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"DialogHeader\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"DialogFooter\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return <DialogPrimitive.Title data-slot='dialog-title' className={cn('text-lg leading-none font-semibold', className)} {...props} data-sentry-element=\"DialogPrimitive.Title\" data-sentry-component=\"DialogTitle\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return <DialogPrimitive.Description data-slot='dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"DialogPrimitive.Description\" data-sentry-component=\"DialogDescription\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nexport { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger };"], "names": ["BasicInfoStep", "data", "onUpdate", "isGeneratingCode", "setIsGeneratingCode", "useState", "dateR<PERSON><PERSON><PERSON><PERSON>bled", "setDateRangeEnabled", "Boolean", "startDate", "endDate", "fileInputRef", "useRef", "div", "className", "data-sentry-component", "data-sentry-source-file", "Label", "htmlFor", "data-sentry-element", "Input", "id", "placeholder", "value", "name", "onChange", "e", "target", "instructor", "courseCode", "toUpperCase", "<PERSON><PERSON>", "type", "variant", "onClick", "generateCourseCode", "setTimeout", "Math", "code", "random", "toString", "substring", "toast", "success", "disabled", "Shuffle", "p", "Textarea", "description", "rows", "coverImagePreview", "img", "src", "alt", "size", "removeCoverImage", "revokeObjectURL", "coverImage", "undefined", "X", "current", "click", "Upload", "input", "ref", "accept", "event", "handleImageUpload", "file", "files", "startsWith", "error", "previewUrl", "URL", "createObjectURL", "Popover", "PopoverTrigger", "<PERSON><PERSON><PERSON><PERSON>", "Info", "PopoverC<PERSON>nt", "align", "h4", "Badge", "ul", "li", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "span", "enrollmentType", "handleEnrollmentTypeChange", "updates", "currency", "price", "parseFloat", "min", "step", "Checkbox", "checked", "onCheckedChange", "handleDateRangeToggle", "cn", "CalendarIcon", "format", "locale", "Calendar", "mode", "selected", "onSelect", "date", "Date", "initialFocus", "Switch", "props", "SwitchPrimitive", "data-slot", "ModuleStructureStep", "expandedModules", "setExpandedModules", "Set", "editingModule", "setEditingModule", "editing<PERSON><PERSON>pter", "setEditingChapter", "moduleId", "chapter", "isModuleDialogOpen", "setIsModuleDialogOpen", "isChapterDialogOpen", "setIsChapterDialogOpen", "toggleModuleExpansion", "newExpanded", "has", "delete", "add", "createNewModule", "newModule", "now", "orderIndex", "modules", "length", "chapters", "hasModuleQuiz", "editModule", "moduleItem", "deleteModule", "updatedModules", "filter", "m", "map", "index", "createNewChapter", "find", "newChapter", "content", "hasChapterQuiz", "edit<PERSON><PERSON><PERSON><PERSON>", "deleteChapter", "chapterId", "updatedChapters", "c", "moveModule", "direction", "currentIndex", "findIndex", "newIndex", "for<PERSON>ach", "h3", "Plus", "Card", "<PERSON><PERSON><PERSON><PERSON>", "BookOpen", "moduleIndex", "isExpanded", "<PERSON><PERSON><PERSON><PERSON>", "GripVertical", "CardTitle", "CardDescription", "HelpCircle", "Edit", "AlertDialog", "AlertDialogTrigger", "Trash2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "ChevronDown", "ChevronRight", "FileText", "chapterIndex", "Dialog", "open", "onOpenChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "prev", "<PERSON><PERSON><PERSON><PERSON>er", "saveModule", "trim", "existingIndex", "push", "saveChapter", "reduce", "acc", "TiptapEditor", "editor", "useEditor", "extensions", "StarterKit", "configure", "heading", "levels", "Link", "openOnClick", "HTMLAttributes", "class", "Image", "Table", "resizable", "TableRow", "TableHeader", "TableCell", "getHTML", "html", "editorProps", "attributes", "chain", "focus", "undo", "run", "can", "Undo", "redo", "Redo", "toggleBold", "isActive", "Bold", "toggleItalic", "Italic", "toggleStrike", "Strikethrough", "toggleHeading", "level", "Heading1", "Heading2", "Heading3", "toggleBulletList", "List", "toggleOrderedList", "ListOrdered", "toggleBlockquote", "Quote", "toggleCodeBlock", "Code", "addLink", "previousUrl", "getAttributes", "href", "url", "window", "prompt", "extendMarkRange", "unsetLink", "setLink", "LinkIcon", "addImage", "setImage", "ImageIcon", "addTable", "insertTable", "cols", "withHeaderRow", "TableIcon", "Editor<PERSON><PERSON><PERSON>", "DynamicContentEditor", "initialContent", "onContentChange", "allowImages", "contentRefs", "<PERSON><PERSON><PERSON><PERSON>", "showFileDialog", "setShowFileDialog", "selectedFileType", "setSelectedFileType", "linkUrl", "setLinkUrl", "addBlock", "updatedContent", "handleFileBlockAdd", "updateBlock", "newValue", "block", "removeBlock", "handleFileUpload", "useCallback", "blockId", "fileType", "info", "response", "fetch", "method", "body", "ok", "statusText", "newBlob", "json", "char<PERSON>t", "slice", "console", "message", "el", "layout", "objectFit", "document", "createElement", "onchange", "Array", "from", "video", "controls", "iframe", "TextIcon", "MonitorPlayIcon", "FileTextIcon", "VideoIcon", "handleAddFromUpload", "handleAddFromLink", "ContentCreationStep", "selectedModule", "setSelectedModule", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedChapter", "editingQuiz", "setEditingQuiz", "quiz", "isQuizDialogOpen", "setIsQuizDialogOpen", "editingQuestion", "setEditingQuestion", "isQuestionDialogOpen", "setIsQuestionDialogOpen", "previewMode", "setPreviewMode", "currentModule", "currentChapter", "scroll<PERSON>o<PERSON>ontent", "element", "scrollIntoView", "behavior", "inline", "getContentTypeIcon", "Type", "Video", "FileIcon", "getContentPreview", "createQuiz", "newQuiz", "questions", "minimumScore", "timeLimit", "editQuiz", "editQuestion", "question", "deleteQuestion", "questionId", "updatedQuestions", "q", "completionStatus", "getCompletionStatus", "totalChapters", "module", "completedChapters", "total", "completed", "percentage", "round", "CheckCircle", "Clock", "finalExam", "moduleQuiz", "<PERSON><PERSON><PERSON><PERSON>", "Navigation", "button", "Eye", "chapterQuiz", "ReactMarkdown", "remarkPlugins", "remarkGfm", "components", "h1", "node", "h2", "ol", "blockquote", "children", "exec", "pre", "table", "thead", "th", "td", "hr", "strong", "em", "title", "a", "rel", "updateChapterContent", "sum", "points", "max", "parseInt", "createQuestion", "newQuestion", "options", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "blockIndex", "React", "option", "optIndex", "String", "fromCharCode", "optionBlockIndex", "saveQuiz", "Save", "newOptions", "saveQuestion", "PublishingStep", "onPublish", "isPublishing", "showDetails", "setShowDetails", "validationItems", "items", "label", "status", "required", "toLocaleDateString", "moduleCount", "chaptersWithContent", "chaptersWithQuiz", "modulesWithQuiz", "requiredItems", "item", "completedRequired", "canPublish", "allCompleted", "completionPercentage", "stats", "getCourseStats", "totalQuizzes", "chapterQuizzes", "estimatedDuration", "chapterAcc", "ceil", "textAcc", "quizzes", "handlePublish", "Rocket", "AlertCircle", "Target", "Progress", "Separator", "h5", "<PERSON><PERSON>", "AlertDescription", "AdmissionsStep", "admissions", "requirements", "applicationDeadline", "prerequisites", "newRequirement", "setNewRequirement", "newPrerequisite", "setNewPrerequisite", "handleUpdate", "field", "addRequirement", "includes", "removeRequirement", "updatedRequirements", "_", "i", "addPrerequisite", "removePrerequisite", "updatedPrerequisites", "ClipboardList", "onKeyPress", "key", "preventDefault", "req", "AcademicsStep", "academics", "credits", "workload", "assessment", "newAssessment", "set<PERSON>ewAssessment", "addAssessment", "removeAssessment", "updatedAssessment", "Book", "Hourglass", "Award", "TuitionFinancingStep", "tuitionAndFinancing", "totalCost", "paymentOptions", "scholarships", "newPaymentOption", "setNewPaymentOption", "newScholarship", "setNewScholarship", "addPaymentOption", "removePaymentOption", "updatedOptions", "addScholarship", "removeScholarship", "updatedScholarships", "DollarSign", "CreditCard", "Gift", "scholarship", "CareersStep", "careers", "outcomes", "industries", "averageSalary", "newOutcome", "setNewOutcome", "newIndustry", "setNewIndustry", "addOutcome", "removeOutcome", "updatedOutcomes", "addIndustry", "removeIndustry", "updatedIndustries", "Briefcase", "outcome", "Building", "industry", "StudentExperienceStep", "studentExperience", "testimonials", "facilities", "support", "newFacility", "setNewFacility", "newSupport", "setNewSupport", "updateTestimonial", "updatedTestimonials", "removeTestimonial", "addFacility", "removeFacility", "updatedFacilities", "addSupport", "removeSupport", "updatedSupport", "MessageSquare", "testimonial", "feedback", "addTestimonial", "HardHat", "facility", "LifeBuoy", "supportItem", "CourseDetailsStep", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STEPS", "CourseCreationWizard", "onComplete", "onCancel", "initialData", "currentStep", "setCurrentStep", "courseData", "setCourseData", "isPurchasable", "isPublished", "assignedClasses", "isSubmitting", "setIsSubmitting", "updateCourseData", "validateStepData", "basicValidation", "every", "admissionsValid", "<PERSON><PERSON><PERSON><PERSON>", "tuitionFinancingValid", "<PERSON><PERSON><PERSON><PERSON>", "studentExperienceValid", "canProceedToNext", "handleComplete", "progressPercentage", "Check", "renderStepContent", "handlePrevious", "ChevronLeft", "handleNext", "ProgressPrimitive", "style", "transform", "displayName", "alertVariants", "cva", "variants", "default", "destructive", "defaultVariants", "role", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "TeacherLayout", "LabelPrimitive", "TabsPrimitive", "textarea", "serverComponentModule.default", "CheckboxPrimitive", "CheckIcon", "AlertDialogPrimitive", "AlertDialogPortal", "AlertDialogOverlay", "buttonVariants", "DialogPrimitive", "DialogTrigger", "DialogPortal", "DialogOverlay", "XIcon"], "sourceRoot": ""}