import { ContentBlock } from '@/components/dynamic-content-editor';

export interface Quiz {
  id: string;
  title: string;
  type: 'chapter' | 'module' | 'final';
  questions: Question[];
  minimumScore: number;
  timeLimit?: number; // in minutes
  attempts: number;
  maxAttempts: number;
  isPassed: boolean;
  lastScore?: number;
}

export interface Question {
  id: string;
  question: string | ContentBlock[];
  type: 'multiple_choice' | 'multiple-choice' | 'true_false' | 'true-false' | 'essay';
  options?: string[] | { content: ContentBlock[]; isCorrect: boolean }[];
  correctAnswer?: number | string;
  essayAnswer?: string | null;
  explanation?: string | ContentBlock[] | null;
}

export interface Content {
  id: string;
  title: string;
  type: 'text' | 'image' | 'video' | 'pdf' | 'zoom-recording'; // Updated to reflect new content types
  content: string | ContentBlock[];
  duration?: number; // for videos
  isCompleted: boolean;
  createdAt: string;
}

export interface Chapter {
  id: string;
  title: string;
  order: number;
  contents: Content[];
  quiz: Quiz;
  isUnlocked: boolean;
  completionPercentage: number;
}

export interface Module {
  id: string;
  title: string;
  description: string;
  order: number;
  chapters: Chapter[];
  moduleQuiz: Quiz;
  isUnlocked: boolean;
  completionPercentage: number;
}

export interface Course {
  id: string;
  name: string;
  code: string;
  description: string;
  instructor: string;
  startDate: string;
  endDate: string;
  enrollmentType: 'code' | 'invitation' | 'both' | 'purchase';
  enrollmentCode?: string;
  isPurchasable?: boolean;
  price?: number;
  currency?: string;
  previewMode?: boolean;
  thumbnail?: string;
  admissions?: {
    requirements: string[];
    applicationDeadline?: string;
    prerequisites?: string[];
  };
  academics?: {
    credits: number;
    workload: string;
    assessment: string[];
  };
  tuitionAndFinancing?: {
    totalCost: number;
    paymentOptions: string[];
    scholarships?: string[];
  };
  careers?: {
    outcomes: string[];
    industries: string[];
    averageSalary?: string;
  };
  studentExperience?: {
    testimonials: { name: string; feedback: string; }[];
    facilities: string[];
    support: string[];
  };
  modules: Module[];
  finalExam: Quiz;
  certificate: {
    isEligible: boolean;
    isGenerated: boolean;
    certificateUrl?: string;
    completionDate?: string;
    type?: 'completion' | 'professional' | 'achievement';
    issuedBy?: string;
    validityPeriod?: string;
    credentialId?: string;
    description?: string;
  };
  minPassingScore: number;
  totalProgress: number;
  status: 'not-started' | 'in-progress' | 'completed' | 'failed';
}

export interface Institution {
  id: string;
  name: string;
  shortName: string;
  logo?: string;
  website?: string;
  certificateTemplate?: {
    primaryColor: string;
    secondaryColor: string;
    logoUrl?: string;
    signatoryName: string;
    signatoryTitle: string;
  };
}

export interface StudentProgress {
  courseId: string;
  completedContents: string[];
  completedQuizzes: string[];
  currentModule: number;
  currentChapter: number;
  overallScore: number;
  timeSpent: number; // in minutes
}

export interface CoursePreviewProps {
  course: Course;
  onEnroll?: () => void;
  onPurchase?: () => void;
  onClick?: () => void;
  isEnrolled?: boolean;
}

export interface CourseDetailTabsProps {
  course: Course;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export interface TreeNodeProps {
  id: string;
  title: string;
  icon: React.ReactNode;
  isUnlocked: boolean;
  isCompleted?: boolean;
  children?: React.ReactNode;
  level: number;
  isExpanded?: boolean;
  onToggle?: () => void;
  onClick?: () => void;
  hasChildren?: boolean;
  isActive?: boolean;
}

export interface ContentItemProps {
  content: Content;
  onToggleComplete: () => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
}

export interface QuizCardProps {
  quiz: Quiz;
  isUnlocked: boolean;
  onStartQuiz: () => void;
}

export interface QuizModalProps {
  quiz: Quiz;
  isOpen: boolean;
  onClose: () => void;
  onComplete: (score: number) => void;
}

export interface ChapterSectionProps {
  chapter: Chapter;
  expandedContents: { [key: string]: boolean };
  onToggleContent: (contentId: string) => void;
  onToggleContentComplete: (contentId: string) => void;
  onStartQuiz: (quizId: string) => void;
  isExpanded: boolean;
  onToggleExpanded: () => void;
}

export interface ModuleSectionProps {
  module: Module;
  expandedContents: { [key: string]: boolean };
  expandedChapters: { [key: string]: boolean };
  onToggleContent: (contentId: string) => void;
  onToggleContentComplete: (contentId: string) => void;
  onStartQuiz: (quizId: string) => void;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  onToggleChapter: (chapterId: string) => void;
  onExpandAllChapters: () => void;
  onCollapseAllChapters: () => void;
}

export interface TableOfContentsProps {
  course: Course;
  onNavigate: (moduleId: string, chapterId?: string, contentId?: string) => void;
  expandedModules: { [key: string]: boolean };
  expandedChapters: { [key: string]: boolean };
  onToggleModule: (moduleId: string) => void;
  onToggleChapter: (chapterId: string) => void;
  currentModuleIndex?: number;
}

export interface CertificateTemplateProps {
  institution: Institution;
  course: Course;
  studentName: string;
  completionDate: string;
}
