try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="ceb88331-d0b2-4faf-b8a6-e4c0dfcd85c3",e._sentryDebugIdIdentifier="sentry-dbid-ceb88331-d0b2-4faf-b8a6-e4c0dfcd85c3")}catch(e){}(()=>{var e={};e.id=8887,e.ids=[8887],e.modules={1683:(e,r,t)=>{"use strict";t.d(r,{P:()=>o});var s=t(138);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let i=(0,s.lw)(process.env.DATABASE_URL);async function o(e,...r){return await i(e,...r)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17020:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>A,serverHooks:()=>S,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{DELETE:()=>v,GET:()=>f,HEAD:()=>w,OPTIONS:()=>E,PATCH:()=>m,POST:()=>y,PUT:()=>g});var i=t(3690),o=t(56947),u=t(75250),n=t(63033),a=t(62187),c=t(82446),d=t(1683),p=t(7688);async function l(e){try{let{name:r,email:t,password:s,role:i,institutionId:o}=await e.json();if(!r||!t||!s||!i)return a.NextResponse.json({success:!1,error:"Name, email, password, and role are required"},{status:400});if((await (0,d.P)`SELECT id FROM users WHERE email = ${t.toLowerCase()}`)[0])return a.NextResponse.json({success:!1,error:"User with this email already exists"},{status:409});let u=await c.Ay.hash(s,12),n=await (0,d.P)`
      INSERT INTO users (name, email, password, role, institution_id)
      VALUES (${r}, ${t.toLowerCase()}, ${u}, ${i}, ${"teacher"===i||"student"===i?null:o||null})
      RETURNING id, name, email, role, institution_id
    `;if(!n[0])return a.NextResponse.json({success:!1,error:"Failed to create user"},{status:500});let p={id:n[0].id,name:n[0].name,email:n[0].email,role:n[0].role,institutionId:n[0].institutionId||void 0};return a.NextResponse.json({success:!0,data:{user:p},message:"User created successfully"})}catch(e){return console.error("Sign up error:",e),a.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let x={...n},q="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;function h(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let i;try{let e=q?.getStore();i=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/auth/signup",headers:i}).apply(t,s)}})}let f=h(void 0,"GET"),y=h(l,"POST"),g=h(void 0,"PUT"),m=h(void 0,"PATCH"),v=h(void 0,"DELETE"),w=h(void 0,"HEAD"),E=h(void 0,"OPTIONS"),A=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\signup\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:T,workUnitAsyncStorage:_,serverHooks:S}=A;function b(){return(0,u.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:_})}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=44725,e.exports=r},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,2446],()=>t(17020));module.exports=s})();
//# sourceMappingURL=route.js.map