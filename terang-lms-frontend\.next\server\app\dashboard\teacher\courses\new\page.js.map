{"version": 3, "file": "../app/dashboard/teacher/courses/new/page.js", "mappings": "ubAAA,yDCAA,0QCUe,SAASA,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,MACJC,CAAI,CACL,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,GACL,CAACC,EAAaC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzCC,EAAoB,MAAOC,IAC/B,GAAI,CACFH,GAAe,GACE,IAAII,WACZC,MAAM,CAAC,OAAQF,GACxB,IAAMG,EAAW,MAAMC,MAAM,CAAC,qBAAqB,EAAEJ,EAAKK,IAAI,EAAE,CAAE,CAChEC,OAAQ,OACRC,KAAMP,CACR,GACA,GAAI,CAACG,EAASK,EAAE,CACd,CADgB,KACV,MAAU,0BAGlB,MAAOC,CADM,MAAMN,EAASO,IAAI,IACpBC,GAAG,CACf,MAAOC,EAAO,CAGd,OAFAC,QAAQD,KAAK,CAAC,yBAA0BA,GACxCE,EAAAA,EAAKA,CAACF,KAAK,CAAC,gCACL,IACT,QAAU,CACRf,GAAe,EACjB,CACF,EACMkB,EAAqB,MAAOC,IAChC,GAAI,CAACtB,EAAM,YACToB,EAAAA,EAAKA,CAACF,KAAK,CAAC,mCAGd,GAAI,CACF,IAAIK,EAAkB,KAGtB,GAAID,EAAWE,UAAU,EAAE,CACzBJ,EAAAA,EAAKA,CAACK,OAAO,CAAC,2BAA4B,CACxCC,GAAI,iBACN,GAEI,CAACH,CADLA,EAAkB,MAAMlB,EAAkBiB,EAAWE,IAC/B,OADyC,GAE7D,OAKJJ,CALY,CAKZA,EAAKA,CAACK,OAAO,CAAC,qBAAsB,CAClCC,GAAI,CAN4C,gBAOlD,GACA,IAAMC,EAAiB,MAAMjB,MAAM,eAAgB,CACjDE,OAAQ,OACRgB,QAAS,CACP,eAAgB,kBAClB,EACAf,KAAMgB,KAAKC,SAAS,CAAC,CACnBnB,KAAMW,EAAWX,IAAI,CACrBoB,YAAaT,EAAWS,WAAW,CACnCC,WAAYV,EAAWU,UAAU,CACjCC,WAAYX,EAAWW,UAAU,CACjCC,KAAMZ,EAAWY,IAAI,CACrBC,eAAgBb,EAAWa,cAAc,CACzCC,UAAWd,EAAWc,SAAS,CAC/BC,QAASf,EAAWe,OAAO,CAC3BC,UAAWtC,EAAK0B,EAAE,CAClBa,cAAevC,EAAKuC,aAAa,CACjCC,aAAcjB,EACdkB,cAA6C,aAA9BnB,EAAWa,cAAc,EAAiD,SAA9Bb,EAAWa,cAAc,CACpFO,MAAOpB,EAAWoB,KAAK,CACvBC,SAAUrB,EAAWqB,QAAQ,CAC7BC,YAAatB,EAAWsB,WAAW,CACnCC,WAAYvB,EAAWuB,UAAU,CACjCC,UAAWxB,EAAWwB,SAAS,CAC/BC,oBAAqBzB,EAAWyB,mBAAmB,CACnDC,QAAS1B,EAAW0B,OAAO,CAC3BC,kBAAmB3B,EAAW2B,iBAChC,EACF,GACMC,EAAe,MAAMvB,EAAeX,IAAI,GAC9C,GAAI,CAACkC,EAAaC,OAAO,CACvB,CADyB,KACnB,MAAUD,EAAahC,KAAK,EAAI,2BAExC,IAAMkC,EAAgBF,EAAaG,MAAM,CACzCjC,EAAAA,EAAKA,CAACK,OAAO,CAAC,sBAAuB,CACnCC,GAAI,iBACN,GAGA,IAAM4B,EAAiB,EAAE,CACzB,IAAK,IAAIC,EAAI,EAAGA,EAAIjC,EAAWkC,OAAO,CAACC,MAAM,CAAEF,IAAK,CAClD,IAAMG,EAAapC,EAAWkC,OAAO,CAACD,EAAE,CAClCI,EAAiB,MAAMjD,MAAM,eAAgB,CACjDE,OAAQ,OACRgB,QAAS,CACP,eAAgB,kBAClB,EACAf,KAAMgB,KAAKC,SAAS,CAAC,CACnBnB,KAAM+C,EAAW/C,IAAI,CACrBoB,YAAa2B,EAAW3B,WAAW,CACnC6B,SAAUR,EAAc1B,EAAE,CAC1BY,UAAWtC,EAAK0B,EAAE,CAClBmC,WAAYH,EAAWG,UACzB,EACF,GACMC,EAAe,MAAMH,EAAe3C,IAAI,GAC9C,GAAI,CAAC8C,EAAaC,MAAM,CACtB,CADwB,KAClB,MAAU,CAAC,yBAAyB,EAAEL,EAAW/C,IAAI,EAAE,EAE/D2C,EAAeU,IAAI,CAAC,CAClB,GAAGF,EAAaC,MAAM,CACtBE,aAAcP,CAChB,EACF,CACAtC,EAAAA,EAAKA,CAACK,OAAO,CAAC,uBAAwB,CACpCC,GAAI,iBACN,GAGA,IAAMwC,EAAkB,EAAE,CAC1B,IAAK,IAAMC,KAAiBb,EAAgB,CAC1C,IAAMI,EAAaS,EAAcF,YAAY,CAC7C,IAAK,IAAIG,EAAI,EAAGA,EAAIV,EAAWW,QAAQ,CAACZ,MAAM,CAAEW,IAAK,CACnD,IAAME,EAAcZ,EAAWW,QAAQ,CAACD,EAAE,CACpCG,EAAkB,MAAM7D,MAAM,gBAAiB,CACnDE,OAAQ,OACRgB,QAAS,CACP,eAAgB,kBAClB,EACAf,KAAMgB,KAAKC,SAAS,CAAC,CACnBnB,KAAM2D,EAAY3D,IAAI,CACtB6D,QAASF,EAAYE,OAAO,CAC5BC,SAAUN,EAAczC,EAAE,CAC1BY,UAAWtC,EAAK0B,EAAE,CAClBmC,WAAYS,EAAYT,UAAU,EAEtC,GACMa,EAAgB,MAAMH,EAAgBvD,IAAI,GAChD,GAAI,CAAC0D,EAAcC,OAAO,CACxB,CAD0B,KACpB,MAAU,CAAC,0BAA0B,EAAEL,EAAY3D,IAAI,EAAE,EAEjEuD,EAAgBF,IAAI,CAAC,CACnB,GAAGU,EAAcC,OAAO,CACxBV,aAAcK,CAChB,EACF,CACF,CAGA,GAAIhD,EAAWkC,OAAO,CAACoB,IAAI,CAACC,GAAKA,EAAER,QAAQ,CAACO,IAAI,CAACE,GAAKA,EAAEC,cAAc,EAAID,EAAEE,WAAW,GAIrF,CAJyF,GAIpF,IAAMC,KAHX7D,EAAAA,EAAKA,CAACK,OAAO,CAAC,sBAAuB,CACnCC,GAAI,iBACN,GAC6BwC,GAAiB,CAC5C,IAAMI,EAAcW,EAAehB,YAAY,CAC/C,GAAIK,EAAYS,cAAc,EAAIT,EAAYU,WAAW,CAAE,CACzD,IAAME,EAAWZ,EAAYU,WAAW,CAClCG,EAAe,MAAMzE,MAAM,eAAgB,CAC/CE,OAAQ,OACRgB,QAAS,CACP,eAAgB,kBAClB,EACAf,KAAMgB,KAAKC,SAAS,CAAC,CACnBnB,KAAMuE,EAASvE,IAAI,CACnBoB,YAAamD,EAASnD,WAAW,CACjCqD,SAAU,UACVC,UAAWH,EAASG,SAAS,CAC7BC,aAAcJ,EAASI,YAAY,CACnCC,UAAWN,EAAevD,EAAE,CAC5BY,UAAWtC,EAAK0B,EAAE,CAClB8D,UAAWN,EAASM,SAAS,CAACC,GAAG,CAAC,GAAa,EAC7CvD,KAAMwD,EAAExD,IAAI,CACZyD,SAAUD,EAAEC,QAAQ,CACpBC,QAASF,EAAEE,OAAO,CAClBC,YAAaH,EAAEG,WAAW,CAE1BC,YAAaJ,EAAEI,WAAW,CAE1BC,OAAQL,EAAEK,MAAM,CAChBlC,WAAY6B,EAAE7B,UAAU,CAC1B,EACF,EACF,EAEI,EADe,MAAMsB,EAAanE,IAAI,IAC1BgF,IAAI,EAClB7E,QAAQ8E,IAAI,CAAC,CAAC,mCAAmC,EAAE3B,EAAY3D,IAAI,EAAE,CAEzE,CACF,CAIF,GAAIW,EAAWkC,OAAO,CAACoB,IAAI,CAACC,GAAKA,EAAEqB,aAAa,EAAIrB,EAAEsB,UAAU,EAI9D,CAJiE,GAI5D,IAAMhC,KAHX/C,EAAAA,EAAKA,CAACK,OAAO,CAAC,6BAA8B,CAC1CC,GAAI,iBACN,GAC4B4B,GAAgB,CAC1C,IAAMI,EAAaS,EAAcF,YAAY,CAC7C,GAAIP,EAAWwC,aAAa,EAAIxC,EAAWyC,UAAU,CAAE,CACrD,IAAMjB,EAAWxB,EAAWyC,UAAU,CAChChB,EAAe,MAAMzE,MAAM,eAAgB,CAC/CE,OAAQ,OACRgB,QAAS,CACP,eAAgB,kBAClB,EACAf,KAAMgB,KAAKC,SAAS,CAAC,CACnBnB,KAAMuE,EAASvE,IAAI,CACnBoB,YAAamD,EAASnD,WAAW,CACjCqD,SAAU,SACVC,UAAWH,EAASG,SAAS,CAC7BC,aAAcJ,EAASI,YAAY,CACnCb,SAAUN,EAAczC,EAAE,CAE1BY,UAAWtC,EAAK0B,EAAE,CAClB8D,UAAWN,EAASM,SAAS,CAACC,GAAG,CAAC,GAAa,EAC7CvD,KAAMwD,EAAExD,IAAI,CACZyD,SAAUD,EAAEC,QAAQ,CACpBC,QAASF,EAAEE,OAAO,CAClBC,YAAaH,EAAEG,WAAW,CAE1BC,YAAaJ,EAAEI,WAAW,CAE1BC,OAAQL,EAAEK,MAAM,CAChBlC,WAAY6B,EAAE7B,UAAU,CAC1B,EACF,EACF,EAEI,EADe,MAAMsB,EAAanE,IAAI,IAC1BgF,IAAI,EAAE,QACZC,IAAI,CAAC,CAAC,8BAA8B,EAAEf,EAASvE,IAAI,EAAE,CAEjE,CACF,CAIF,GAAIW,EAAW8E,SAAS,CAAE,CACxBhF,EAAAA,EAAKA,CAACK,OAAO,CAAC,yBAA0B,CACtCC,GAAI,iBACN,GACA,IAAM0E,EAAY9E,EAAW8E,SAAS,CAChCC,EAAe,MAAM3F,MAAM,eAAgB,CAC/CE,OAAQ,OACRgB,QAAS,CACP,eAAgB,kBAClB,EACAf,KAAMgB,KAAKC,SAAS,CAAC,CACnBnB,KAAMyF,EAAUzF,IAAI,CACpBoB,YAAaqE,EAAUrE,WAAW,CAClCqD,SAAU,QACVC,UAAWe,EAAUf,SAAS,CAC9BC,aAAcc,EAAUd,YAAY,CACpC1B,SAAUR,EAAc1B,EAAE,CAE1BY,UAAWtC,EAAK0B,EAAE,CAClB8D,UAAWY,EAAUZ,SAAS,CAACC,GAAG,CAAC,GAAa,EAC9CvD,KAAMwD,EAAExD,IAAI,CACZyD,SAAUD,EAAEC,QAAQ,CACpBC,QAASF,EAAEE,OAAO,CAClBC,YAAaH,EAAEG,WAAW,CAE1BC,YAAaJ,EAAEI,WAAW,CAE1BC,OAAQL,EAAEK,MAAM,CAChBlC,WAAY6B,EAAE7B,UAAU,CAC1B,EACF,EACF,EAEI,EADe,MAAMwC,EAAarF,IAAI,IAC1BgF,IAAI,EAClB7E,QAAQ8E,IAAI,CAAC,8BAEjB,CACA7E,EAAAA,EAAKA,CAAC+B,OAAO,CAAC,uEAAwE,CACpFzB,GAAI,iBACN,GACA5B,EAAOkE,IAAI,CAAC,6BACd,CAAE,MAAO9C,EAAO,CACdC,QAAQD,KAAK,CAAC,yBAA0BA,GACxCE,EAAAA,EAAKA,CAACF,KAAK,CAACA,aAAiBoF,MAAQpF,EAAMqF,OAAO,CAAG,0BAA2B,CAC9E7E,GAAI,iBACN,EACF,CACF,EACA,MAAO,WAAC8E,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,gBAAgBC,0BAAwB,qBAC5F,WAACH,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACG,IAAIA,CAACC,KAAK,6BAA6BC,UAAnCF,YAAuD,OAAOD,0BAAwB,oBACzF,WAACI,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUC,KAAK,KAAKH,sBAAoB,SAASH,0BAAwB,qBACvF,UAACO,EAAAA,CAASA,CAAAA,CAACT,UAAU,eAAeK,sBAAoB,YAAYH,0BAAwB,aAAa,YAI7G,WAACH,MAAAA,WACC,UAACW,KAAAA,CAAGV,UAAU,6CAAoC,sBAGlD,UAACW,IAAAA,CAAEX,UAAU,iCAAwB,qEAMzC,UAACG,IAAIA,CAACC,KAAK,sCAAsCC,CAA5CF,qBAAgE,OAAOD,0BAAwB,oBAClG,WAACI,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUF,sBAAoB,SAASH,0BAAwB,qBAC7E,UAACU,EAAAA,CAAQA,CAAAA,CAACZ,UAAU,eAAeK,sBAAoB,WAAWH,0BAAwB,aAAa,2BAM7G,UAACW,EAAAA,CAAoBA,CAAAA,CAACC,WAAYlG,EAAoBmG,SAAU,IAAM1H,EAAOkE,IAAI,CAAC,8BAA+B8C,sBAAoB,uBAAuBH,0BAAwB,eAE1L,yBCnUA,oDCAA,8FCAA,uCAAiL,yBCAjL,kECAA,2GCAA,qDCAA,gDCAA,kDCAA,+CCAA,yGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,qDCAA,yDCAA,oDCAA,8FC4BM,MAAW,cAAiB,YAzBE,CAClC,CACE,OACA,CACE,CAAG,+PACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EAC1C,yBCfA,qDCAA,kECAA,yDCAA,uDCAA,6GCAA,qDCAA,qDCAA,6DCAA,wDCAA,gECAA,wDCAA,gECmBI,sBAAsB,0tBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,gCAAgC,CAChD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CA7BLc,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,iBASQ,GChF9B,kDCAA,2DCAA,oDCAA,sCAAiL,yBCAjL,gDCAA,0DCAA,yVCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,UACA,CACA,UACA,MACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,uCAAiL,CA0BrM,gJAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA5CA,IAAsB,uCAAqK,CA4C3L,qIAGA,CACO,CACP,CACA,QAnDA,IAAsB,uCAA4J,CAmDlL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApEA,IAAsB,sCAAiJ,CAoEvK,gHACA,gBApEA,IAAsB,uCAAuJ,CAoE7K,sHACA,aApEA,IAAsB,uCAAoJ,CAoE1K,mHACA,WApEA,IAAsB,4CAAgF,CAoEtG,+CACA,cApEA,IAAsB,4CAAmF,CAoEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,mJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,2CACA,0CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC/GD", "sources": ["webpack://terang-lms-ui/external node-commonjs \"node:process\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/./src/app/dashboard/teacher/courses/new/page.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?9cf2", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/../../../src/icons/sparkles.ts", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external node-commonjs \"node:url\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/?aa62", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/?962e", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"node:process\");", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { ArrowLef<PERSON>, Sparkles } from 'lucide-react';\nimport { toast } from 'sonner';\nimport { useAuth } from '@/hooks/use-auth';\nimport Link from 'next/link';\nimport { CourseCreationWizard, CourseData } from '@/components/course/course-creation-wizard';\nimport { useState } from 'react';\nexport default function NewCoursePage() {\n  const router = useRouter();\n  const {\n    user\n  } = useAuth();\n  const [isUploading, setIsUploading] = useState(false);\n  const uploadImageToBlob = async (file: File): Promise<string | null> => {\n    try {\n      setIsUploading(true);\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await fetch(`/api/upload?filename=${file.name}`, {\n        method: 'POST',\n        body: file\n      });\n      if (!response.ok) {\n        throw new Error('Failed to upload image');\n      }\n      const data = await response.json();\n      return data.url;\n    } catch (error) {\n      console.error('Error uploading image:', error);\n      toast.error('Failed to upload cover image');\n      return null;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleCourseCreate = async (courseData: CourseData) => {\n    if (!user) {\n      toast.error('Please log in to create courses');\n      return;\n    }\n    try {\n      let coverPictureUrl = null;\n\n      // Step 1: Upload cover image if exists\n      if (courseData.coverImage) {\n        toast.loading('Uploading cover image...', {\n          id: 'course-creation'\n        });\n        coverPictureUrl = await uploadImageToBlob(courseData.coverImage);\n        if (!coverPictureUrl) {\n          return; // Upload failed, stop course creation\n        }\n      }\n\n      // Step 2: Create the basic course\n      toast.loading('Creating course...', {\n        id: 'course-creation'\n      });\n      const courseResponse = await fetch('/api/courses', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name: courseData.name,\n          description: courseData.description,\n          instructor: courseData.instructor,\n          courseCode: courseData.courseCode,\n          type: courseData.type,\n          enrollmentType: courseData.enrollmentType,\n          startDate: courseData.startDate,\n          endDate: courseData.endDate,\n          teacherId: user.id,\n          institutionId: user.institutionId,\n          coverPicture: coverPictureUrl,\n          isPurchasable: courseData.enrollmentType === 'purchase' || courseData.enrollmentType === 'both',\n          price: courseData.price,\n          currency: courseData.currency,\n          previewMode: courseData.previewMode,\n          admissions: courseData.admissions,\n          academics: courseData.academics,\n          tuitionAndFinancing: courseData.tuitionAndFinancing,\n          careers: courseData.careers,\n          studentExperience: courseData.studentExperience\n        })\n      });\n      const courseResult = await courseResponse.json();\n      if (!courseResult.success) {\n        throw new Error(courseResult.error || 'Failed to create course');\n      }\n      const createdCourse = courseResult.course;\n      toast.loading('Creating modules...', {\n        id: 'course-creation'\n      });\n\n      // Step 3: Create modules sequentially\n      const createdModules = [];\n      for (let i = 0; i < courseData.modules.length; i++) {\n        const moduleData = courseData.modules[i];\n        const moduleResponse = await fetch('/api/modules', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            name: moduleData.name,\n            description: moduleData.description,\n            courseId: createdCourse.id,\n            teacherId: user.id,\n            orderIndex: moduleData.orderIndex\n          })\n        });\n        const moduleResult = await moduleResponse.json();\n        if (!moduleResult.module) {\n          throw new Error(`Failed to create module: ${moduleData.name}`);\n        }\n        createdModules.push({\n          ...moduleResult.module,\n          originalData: moduleData\n        });\n      }\n      toast.loading('Creating chapters...', {\n        id: 'course-creation'\n      });\n\n      // Step 4: Create chapters for each module\n      const createdChapters = [];\n      for (const createdModule of createdModules) {\n        const moduleData = createdModule.originalData;\n        for (let j = 0; j < moduleData.chapters.length; j++) {\n          const chapterData = moduleData.chapters[j];\n          const chapterResponse = await fetch('/api/chapters', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              name: chapterData.name,\n              content: chapterData.content,\n              moduleId: createdModule.id,\n              teacherId: user.id,\n              orderIndex: chapterData.orderIndex\n            })\n          });\n          const chapterResult = await chapterResponse.json();\n          if (!chapterResult.chapter) {\n            throw new Error(`Failed to create chapter: ${chapterData.name}`);\n          }\n          createdChapters.push({\n            ...chapterResult.chapter,\n            originalData: chapterData\n          });\n        }\n      }\n\n      // Step 5: Create quizzes if they exist\n      if (courseData.modules.some(m => m.chapters.some(c => c.hasChapterQuiz && c.chapterQuiz))) {\n        toast.loading('Creating quizzes...', {\n          id: 'course-creation'\n        });\n        for (const createdChapter of createdChapters) {\n          const chapterData = createdChapter.originalData;\n          if (chapterData.hasChapterQuiz && chapterData.chapterQuiz) {\n            const quizData = chapterData.chapterQuiz;\n            const quizResponse = await fetch('/api/quizzes', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                name: quizData.name,\n                description: quizData.description,\n                quizType: 'chapter',\n                timeLimit: quizData.timeLimit,\n                minimumScore: quizData.minimumScore,\n                chapterId: createdChapter.id,\n                teacherId: user.id,\n                questions: quizData.questions.map((q: any) => ({\n                  type: q.type,\n                  question: q.question,\n                  options: q.options,\n                  essayAnswer: q.essayAnswer,\n                  // Changed from correctAnswer\n                  explanation: q.explanation,\n                  // Added explanation\n                  points: q.points,\n                  orderIndex: q.orderIndex\n                }))\n              })\n            });\n            const quizResult = await quizResponse.json();\n            if (!quizResult.quiz) {\n              console.warn(`Failed to create quiz for chapter: ${chapterData.name}`);\n            }\n          }\n        }\n      }\n\n      // Step 6: Create module quizzes if they exist\n      if (courseData.modules.some(m => m.hasModuleQuiz && m.moduleQuiz)) {\n        toast.loading('Creating module quizzes...', {\n          id: 'course-creation'\n        });\n        for (const createdModule of createdModules) {\n          const moduleData = createdModule.originalData;\n          if (moduleData.hasModuleQuiz && moduleData.moduleQuiz) {\n            const quizData = moduleData.moduleQuiz;\n            const quizResponse = await fetch('/api/quizzes', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                name: quizData.name,\n                description: quizData.description,\n                quizType: 'module',\n                timeLimit: quizData.timeLimit,\n                minimumScore: quizData.minimumScore,\n                moduleId: createdModule.id,\n                // Use moduleId for module quiz\n                teacherId: user.id,\n                questions: quizData.questions.map((q: any) => ({\n                  type: q.type,\n                  question: q.question,\n                  options: q.options,\n                  essayAnswer: q.essayAnswer,\n                  // Changed from correctAnswer\n                  explanation: q.explanation,\n                  // Added explanation\n                  points: q.points,\n                  orderIndex: q.orderIndex\n                }))\n              })\n            });\n            const quizResult = await quizResponse.json();\n            if (!quizResult.quiz) {\n              console.warn(`Failed to create module quiz: ${quizData.name}`);\n            }\n          }\n        }\n      }\n\n      // Step 7: Create final exam if it exists\n      if (courseData.finalExam) {\n        toast.loading('Creating final exam...', {\n          id: 'course-creation'\n        });\n        const finalExam = courseData.finalExam;\n        const examResponse = await fetch('/api/quizzes', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            name: finalExam.name,\n            description: finalExam.description,\n            quizType: 'final',\n            timeLimit: finalExam.timeLimit,\n            minimumScore: finalExam.minimumScore,\n            courseId: createdCourse.id,\n            // Use courseId for final exam\n            teacherId: user.id,\n            questions: finalExam.questions.map((q: any) => ({\n              type: q.type,\n              question: q.question,\n              options: q.options,\n              essayAnswer: q.essayAnswer,\n              // Changed from correctAnswer\n              explanation: q.explanation,\n              // Added explanation\n              points: q.points,\n              orderIndex: q.orderIndex\n            }))\n          })\n        });\n        const examResult = await examResponse.json();\n        if (!examResult.quiz) {\n          console.warn('Failed to create final exam');\n        }\n      }\n      toast.success('Course created successfully with all modules, chapters, and quizzes!', {\n        id: 'course-creation'\n      });\n      router.push('/dashboard/teacher/courses');\n    } catch (error) {\n      console.error('Error creating course:', error);\n      toast.error(error instanceof Error ? error.message : 'Failed to create course', {\n        id: 'course-creation'\n      });\n    }\n  };\n  return <div className='space-y-6' data-sentry-component=\"NewCoursePage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center justify-between'>\r\n        <div className='flex items-center space-x-4'>\r\n          <Link href='/dashboard/teacher/courses' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n            <Button variant='outline' size='sm' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n              <ArrowLeft className='mr-2 h-4 w-4' data-sentry-element=\"ArrowLeft\" data-sentry-source-file=\"page.tsx\" />\r\n              Back\r\n            </Button>\r\n          </Link>\r\n          <div>\r\n            <h1 className='text-3xl font-bold tracking-tight'>\r\n              Create New Course\r\n            </h1>\r\n            <p className='text-muted-foreground'>\r\n              Create a comprehensive course with our step-by-step wizard\r\n            </p>\r\n          </div>\r\n        </div>\r\n        \r\n        <Link href='/dashboard/teacher/courses/generate' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button variant='outline' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <Sparkles className='mr-2 h-4 w-4' data-sentry-element=\"Sparkles\" data-sentry-source-file=\"page.tsx\" />\r\n            Try AI Generator\r\n          </Button>\r\n        </Link>\r\n      </div>\r\n\r\n      <CourseCreationWizard onComplete={handleCourseCreate} onCancel={() => router.push('/dashboard/teacher/courses')} data-sentry-element=\"CourseCreationWizard\" data-sentry-source-file=\"page.tsx\" />\r\n    </div>;\n}", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\new\\\\page.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z',\n      key: '4pj2yx',\n    },\n  ],\n  ['path', { d: 'M20 3v4', key: '1olli1' }],\n  ['path', { d: 'M22 5h-4', key: '1gvqau' }],\n  ['path', { d: 'M4 17v2', key: 'vumght' }],\n  ['path', { d: 'M5 18H3', key: 'zchphs' }],\n];\n\n/**\n * @component @name Sparkles\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS45MzcgMTUuNUEyIDIgMCAwIDAgOC41IDE0LjA2M2wtNi4xMzUtMS41ODJhLjUuNSAwIDAgMSAwLS45NjJMOC41IDkuOTM2QTIgMiAwIDAgMCA5LjkzNyA4LjVsMS41ODItNi4xMzVhLjUuNSAwIDAgMSAuOTYzIDBMMTQuMDYzIDguNUEyIDIgMCAwIDAgMTUuNSA5LjkzN2w2LjEzNSAxLjU4MWEuNS41IDAgMCAxIDAgLjk2NEwxNS41IDE0LjA2M2EyIDIgMCAwIDAtMS40MzcgMS40MzdsLTEuNTgyIDYuMTM1YS41LjUgMCAwIDEtLjk2MyAweiIgLz4KICA8cGF0aCBkPSJNMjAgM3Y0IiAvPgogIDxwYXRoIGQ9Ik0yMiA1aC00IiAvPgogIDxwYXRoIGQ9Ik00IDE3djIiIC8+CiAgPHBhdGggZD0iTTUgMThIMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/sparkles\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sparkles = createLucideIcon('Sparkles', __iconNode);\n\nexport default Sparkles;\n", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"node:url\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/teacher/courses/new',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/teacher/courses/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/teacher/courses/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/teacher/courses/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\new\\\\page.tsx\");\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\new\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'teacher',\n        {\n        children: [\n        'courses',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\new\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/teacher/courses/new/page\",\n        pathname: \"/dashboard/teacher/courses/new\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"events\");"], "names": ["NewCoursePage", "router", "useRouter", "user", "useAuth", "isUploading", "setIsUploading", "useState", "uploadImageToBlob", "file", "FormData", "append", "response", "fetch", "name", "method", "body", "ok", "data", "json", "url", "error", "console", "toast", "handleCourseCreate", "courseData", "coverPictureUrl", "coverImage", "loading", "id", "courseResponse", "headers", "JSON", "stringify", "description", "instructor", "courseCode", "type", "enrollmentType", "startDate", "endDate", "teacherId", "institutionId", "coverPicture", "isPurchasable", "price", "currency", "previewMode", "admissions", "academics", "tuitionAndFinancing", "careers", "studentExperience", "courseResult", "success", "createdCourse", "course", "createdModules", "i", "modules", "length", "moduleData", "moduleResponse", "courseId", "orderIndex", "moduleResult", "module", "push", "originalData", "createdChapters", "createdModule", "j", "chapters", "chapterData", "chapterResponse", "content", "moduleId", "chapterResult", "chapter", "some", "m", "c", "hasChapterQuiz", "chapterQuiz", "created<PERSON><PERSON>pter", "quizData", "quizResponse", "quizType", "timeLimit", "minimumScore", "chapterId", "questions", "map", "q", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "points", "quiz", "warn", "hasModuleQuiz", "moduleQuiz", "finalExam", "examResponse", "Error", "message", "div", "className", "data-sentry-component", "data-sentry-source-file", "Link", "href", "data-sentry-element", "<PERSON><PERSON>", "variant", "size", "ArrowLeft", "h1", "p", "<PERSON><PERSON><PERSON>", "CourseCreationWizard", "onComplete", "onCancel", "serverComponentModule.default"], "sourceRoot": ""}