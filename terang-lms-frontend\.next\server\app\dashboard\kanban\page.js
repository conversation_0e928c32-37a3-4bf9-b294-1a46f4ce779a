try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="72d0ac2b-860c-49c7-b084-c309f8c486d9",e._sentryDebugIdIdentifier="sentry-dbid-72d0ac2b-860c-49c7-b084-c309f8c486d9")}catch(e){}(()=>{var e={};e.id=3793,e.ids=[3793],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>s,Zp:()=>l,aR:()=>i,wL:()=>c});var n=r(91754);r(93491);var a=r(82233);function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},13358:(e,t,r)=>{"use strict";r.d(t,{O:()=>y});var n=r(93491);let a=e=>{let t,r=new Set,n=(e,n)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=n?n:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,l={setState:n,getState:a,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(n,a,l);return l},l=e=>e?a(e):a,i=e=>e,s=e=>{let t=l(e),r=e=>(function(e,t=i){let r=n.useSyncExternalStore(e.subscribe,n.useCallback(()=>t(e.getState()),[e,t]),n.useCallback(()=>t(e.getInitialState()),[e,t]));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r};var o=r(55511);let d={randomUUID:o.randomUUID},c=new Uint8Array(256),u=c.length,f=[];for(let e=0;e<256;++e)f.push((e+256).toString(16).slice(1));let g=function(e,t,r){if(d.randomUUID&&!t&&!e)return d.randomUUID();let n=(e=e||{}).random??e.rng?.()??(u>c.length-16&&((0,o.randomFillSync)(c),u=0),c.slice(u,u+=16));if(n.length<16)throw Error("Random bytes length must be >= 16");if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return(f[e[t+0]]+f[e[t+1]]+f[e[t+2]]+f[e[t+3]]+"-"+f[e[t+4]]+f[e[t+5]]+"-"+f[e[t+6]]+f[e[t+7]]+"-"+f[e[t+8]]+f[e[t+9]]+"-"+f[e[t+10]]+f[e[t+11]]+f[e[t+12]]+f[e[t+13]]+f[e[t+14]]+f[e[t+15]]).toLowerCase()}(n)},m=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>m(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>m(t)(e)}}},p=[{id:"TODO",title:"Todo"}],h=[{id:"task1",status:"TODO",title:"Project initiation and planning"},{id:"task2",status:"TODO",title:"Gather requirements from stakeholders"}],y=(e=>e?s(e):s)()(((e,t)=>(r,n,a)=>{let l,i={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),a=null!=(t=r.getItem(e))?t:null;return a instanceof Promise?a.then(n):n(a)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1,o=new Set,d=new Set,c=i.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...e)},n,a);let u=()=>{let e=i.partialize({...n()});return c.setItem(i.name,{state:e,version:i.version})},f=a.setState;a.setState=(e,t)=>{f(e,t),u()};let g=e((...e)=>{r(...e),u()},n,a);a.getInitialState=()=>g;let p=()=>{var e,t;if(!c)return;s=!1,o.forEach(e=>{var t;return e(null!=(t=n())?t:g)});let a=(null==(t=i.onRehydrateStorage)?void 0:t.call(i,null!=(e=n())?e:g))||void 0;return m(c.getItem.bind(c))(i.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===i.version)return[!1,e.state];else{if(i.migrate){let t=i.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[a,s]=e;if(r(l=i.merge(s,null!=(t=n())?t:g),!0),a)return u()}).then(()=>{null==a||a(l,void 0),l=n(),s=!0,d.forEach(e=>e(l))}).catch(e=>{null==a||a(void 0,e)})};return a.persist={setOptions:e=>{i={...i,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>p(),hasHydrated:()=>s,onHydrate:e=>(o.add(e),()=>{o.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},i.skipHydration||p(),l||g})(e=>({tasks:h,columns:p,draggedTask:null,addTask:(t,r)=>e(e=>({tasks:[...e.tasks,{id:g(),title:t,description:r,status:"TODO"}]})),updateCol:(t,r)=>e(e=>({columns:e.columns.map(e=>e.id===t?{...e,title:r}:e)})),addCol:t=>e(e=>({columns:[...e.columns,{title:t,id:e.columns.length?t.toUpperCase():"TODO"}]})),dragTask:t=>e({draggedTask:t}),removeTask:t=>e(e=>({tasks:e.tasks.filter(e=>e.id!==t)})),removeCol:t=>e(e=>({columns:e.columns.filter(e=>e.id!==t)})),setTasks:t=>e({tasks:t}),setCols:t=>e({columns:t})}),{name:"task-store",skipHydration:!0}))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},28991:(e,t,r)=>{"use strict";r.d(t,{KanbanBoard:()=>n});let n=(0,r(1472).registerClientReference)(function(){throw Error("Attempted to call KanbanBoard() from the server but KanbanBoard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\kanban-board.tsx","KanbanBoard")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},33924:(e,t,r)=>{Promise.resolve().then(r.bind(r,99781)),Promise.resolve().then(r.bind(r,28991)),Promise.resolve().then(r.bind(r,98732))},34652:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var n=r(18188);let a=({title:e,description:t})=>(0,n.jsxs)("div",{"data-sentry-component":"Heading","data-sentry-source-file":"heading.tsx",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:e}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:t})]})},35590:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>v,generateImageMetadata:()=>h,generateMetadata:()=>p,generateViewport:()=>y,metadata:()=>f});var a=r(63033),l=r(18188),i=r(53891),s=r(34652),o=r(28991),d=r(98732);function c(){return(0,l.jsx)(i.A,{"data-sentry-element":"PageContainer","data-sentry-component":"KanbanViewPage","data-sentry-source-file":"kanban-view-page.tsx",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsx)(s.D,{title:"Kanban",description:"Manage tasks by dnd","data-sentry-element":"Heading","data-sentry-source-file":"kanban-view-page.tsx"}),(0,l.jsx)(d.default,{"data-sentry-element":"NewTaskDialog","data-sentry-source-file":"kanban-view-page.tsx"})]}),(0,l.jsx)(o.KanbanBoard,{"data-sentry-element":"KanbanBoard","data-sentry-source-file":"kanban-view-page.tsx"})]})})}var u=r(7688);let f={title:"Dashboard : Kanban view"},g={...a},m="workUnitAsyncStorage"in g?g.workUnitAsyncStorage:"requestAsyncStorage"in g?g.requestAsyncStorage:void 0;n=new Proxy(function(){return(0,l.jsx)(c,{"data-sentry-element":"KanbanViewPage","data-sentry-component":"page","data-sentry-source-file":"page.tsx"})},{apply:(e,t,r)=>{let n,a,l;try{let e=m?.getStore();n=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,l=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/kanban",componentType:"Page",sentryTraceHeader:n,baggageHeader:a,headers:l}).apply(t,r)}});let p=void 0,h=void 0,y=void 0,v=n},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46940:(e,t,r)=>{"use strict";function n(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{q:()=>n})},48161:e=>{"use strict";e.exports=require("node:os")},48163:(e,t,r)=>{"use strict";let n;r.d(t,{KanbanBoard:()=>tq});var a,l,i,s,o,d,c,u,f,g,m=r(91754),p=r(93491),h=r.n(p),y=r(52410),v=r(13358);function x(e){if(!e)return!1;let t=e.data.current;return t?.type==="Column"||t?.type==="Task"}let b="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function w(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function D(e){return"nodeType"in e}function C(e){var t,r;return e?w(e)?e:D(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function j(e){let{Document:t}=C(e);return e instanceof t}function k(e){return!w(e)&&e instanceof C(e).HTMLElement}function S(e){return e instanceof C(e).SVGElement}function A(e){return e?w(e)?e.document:D(e)?j(e)?e:k(e)||S(e)?e.ownerDocument:document:document:document}let E=b?p.useLayoutEffect:p.useEffect;function N(e){let t=(0,p.useRef)(e);return E(()=>{t.current=e}),(0,p.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function T(e,t){void 0===t&&(t=[e]);let r=(0,p.useRef)(e);return E(()=>{r.current!==e&&(r.current=e)},t),r}function R(e,t){let r=(0,p.useRef)();return(0,p.useMemo)(()=>{let t=e(r.current);return r.current=t,t},[...t])}function M(e){let t=N(e),r=(0,p.useRef)(null),n=(0,p.useCallback)(e=>{e!==r.current&&(null==t||t(e,r.current)),r.current=e},[]);return[r,n]}function O(e){let t=(0,p.useRef)();return(0,p.useEffect)(()=>{t.current=e},[e]),t.current}let P={};function I(e,t){return(0,p.useMemo)(()=>{if(t)return t;let r=null==P[e]?0:P[e]+1;return P[e]=r,e+"-"+r},[e,t])}function L(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>{for(let[n,a]of Object.entries(r)){let r=t[n];null!=r&&(t[n]=r+e*a)}return t},{...t})}}let B=L(1),z=L(-1);function $(e){if(!e)return!1;let{KeyboardEvent:t}=C(e.target);return t&&e instanceof t}function q(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=C(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let F=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:r}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(r?Math.round(r):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:r}=e;return"scaleX("+t+") scaleY("+r+")"}},Transform:{toString(e){if(e)return[F.Translate.toString(e),F.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:r,easing:n}=e;return t+" "+r+"ms "+n}}}),U="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",_={display:"none"};function K(e){let{id:t,value:r}=e;return h().createElement("div",{id:t,style:_},r)}function G(e){let{id:t,announcement:r,ariaLiveType:n="assertive"}=e;return h().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},r)}let W=(0,p.createContext)(null),H={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},Y={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was moved over droppable area "+r.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was dropped over droppable area "+r.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function V(e){let{announcements:t=Y,container:r,hiddenTextDescribedById:n,screenReaderInstructions:a=H}=e,{announce:l,announcement:i}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),s=I("DndLiveRegion"),[o,d]=(0,p.useState)(!1);(0,p.useEffect)(()=>{d(!0)},[]);var c=(0,p.useMemo)(()=>({onDragStart(e){let{active:r}=e;l(t.onDragStart({active:r}))},onDragMove(e){let{active:r,over:n}=e;t.onDragMove&&l(t.onDragMove({active:r,over:n}))},onDragOver(e){let{active:r,over:n}=e;l(t.onDragOver({active:r,over:n}))},onDragEnd(e){let{active:r,over:n}=e;l(t.onDragEnd({active:r,over:n}))},onDragCancel(e){let{active:r,over:n}=e;l(t.onDragCancel({active:r,over:n}))}}),[l,t]);let u=(0,p.useContext)(W);if((0,p.useEffect)(()=>{if(!u)throw Error("useDndMonitor must be used within a children of <DndContext>");return u(c)},[c,u]),!o)return null;let f=h().createElement(h().Fragment,null,h().createElement(K,{id:n,value:a.draggable}),h().createElement(G,{id:s,announcement:i}));return r?(0,y.createPortal)(f,r):f}function X(){}function J(e,t){return(0,p.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let Z=Object.freeze({x:0,y:0});function Q(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return n-r}let ee=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=[];for(let e of n){let{id:n}=e,l=r.get(n);if(l){let r=function(e,t){let r=Math.max(t.top,e.top),n=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),l=Math.min(t.top+t.height,e.top+e.height);if(n<a&&r<l){let i=t.width*t.height,s=e.width*e.height,o=(a-n)*(l-r);return Number((o/(i+s-o)).toFixed(4))}return 0}(l,t);r>0&&a.push({id:n,data:{droppableContainer:e,value:r}})}}return a.sort(Q)};function et(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:Z}let er=function(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>({...t,top:t.top+e*r.y,bottom:t.bottom+e*r.y,left:t.left+e*r.x,right:t.right+e*r.x}),{...t})}}(1);function en(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let ea={ignoreTransform:!1};function el(e,t){void 0===t&&(t=ea);let r=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:n}=C(e).getComputedStyle(e);t&&(r=function(e,t,r){let n=en(t);if(!n)return e;let{scaleX:a,scaleY:l,x:i,y:s}=n,o=e.left-i-(1-a)*parseFloat(r),d=e.top-s-(1-l)*parseFloat(r.slice(r.indexOf(" ")+1)),c=a?e.width/a:e.width,u=l?e.height/l:e.height;return{width:c,height:u,top:d,right:o+c,bottom:d+u,left:o}}(r,t,n))}let{top:n,left:a,width:l,height:i,bottom:s,right:o}=r;return{top:n,left:a,width:l,height:i,bottom:s,right:o}}function ei(e){return el(e,{ignoreTransform:!0})}function es(e,t){let r=[];return e?function n(a){var l;if(null!=t&&r.length>=t||!a)return r;if(j(a)&&null!=a.scrollingElement&&!r.includes(a.scrollingElement))return r.push(a.scrollingElement),r;if(!k(a)||S(a)||r.includes(a))return r;let i=C(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=C(e).getComputedStyle(e));let r=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let n=t[e];return"string"==typeof n&&r.test(n)})}(a,i)&&r.push(a),void 0===(l=i)&&(l=C(a).getComputedStyle(a)),"fixed"===l.position)?r:n(a.parentNode)}(e):r}function eo(e){let[t]=es(e,1);return null!=t?t:null}function ed(e){return b&&e?w(e)?e:D(e)?j(e)||e===A(e).scrollingElement?window:k(e)?e:null:null:null}function ec(e){return w(e)?e.scrollX:e.scrollLeft}function eu(e){return w(e)?e.scrollY:e.scrollTop}function ef(e){return{x:ec(e),y:eu(e)}}function eg(e){return!!b&&!!e&&e===document.scrollingElement}function em(e){let t={x:0,y:0},r=eg(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-r.width,y:e.scrollHeight-r.height},a=e.scrollTop<=t.y,l=e.scrollLeft<=t.x;return{isTop:a,isLeft:l,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(l||(l={}));let ep={x:.2,y:.2};function eh(e){return e.reduce((e,t)=>B(e,ef(t)),Z)}function ey(e,t){if(void 0===t&&(t=el),!e)return;let{top:r,left:n,bottom:a,right:l}=t(e);eo(e)&&(a<=0||l<=0||r>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let ev=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+ec(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+eu(t),0)}]];class ex{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=es(t),n=eh(r);for(let[t,a,l]of(this.rect={...e},this.width=e.width,this.height=e.height,ev))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=l(r),i=n[t]-a;return this.rect[e]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class eb{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,r){var n;null==(n=this.target)||n.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function ew(e,t){let r=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+n**2)>t:"x"in t&&"y"in t?r>t.x&&n>t.y:"x"in t?r>t.x:"y"in t&&n>t.y}function eD(e){e.preventDefault()}function eC(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(i||(i={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(s||(s={}));let ej={start:[s.Space,s.Enter],cancel:[s.Esc],end:[s.Space,s.Enter,s.Tab]},ek=(e,t)=>{let{currentCoordinates:r}=t;switch(e.code){case s.Right:return{...r,x:r.x+25};case s.Left:return{...r,x:r.x-25};case s.Down:return{...r,y:r.y+25};case s.Up:return{...r,y:r.y-25}}};class eS{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new eb(A(t)),this.windowListeners=new eb(C(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(i.Resize,this.handleCancel),this.windowListeners.add(i.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(i.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,r=e.node.current;r&&ey(r),t(Z)}handleKeyDown(e){if($(e)){let{active:t,context:r,options:n}=this.props,{keyboardCodes:a=ej,coordinateGetter:l=ek,scrollBehavior:i="smooth"}=n,{code:o}=e;if(a.end.includes(o))return void this.handleEnd(e);if(a.cancel.includes(o))return void this.handleCancel(e);let{collisionRect:d}=r.current,c=d?{x:d.left,y:d.top}:Z;this.referenceCoordinates||(this.referenceCoordinates=c);let u=l(e,{active:t,context:r.current,currentCoordinates:c});if(u){let t=z(u,c),n={x:0,y:0},{scrollableAncestors:a}=r.current;for(let r of a){let a=e.code,{isTop:l,isRight:o,isLeft:d,isBottom:c,maxScroll:f,minScroll:g}=em(r),m=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:r,right:n,bottom:a}=e.getBoundingClientRect();return{top:t,left:r,right:n,bottom:a,width:e.clientWidth,height:e.clientHeight}}(r),p={x:Math.min(a===s.Right?m.right-m.width/2:m.right,Math.max(a===s.Right?m.left:m.left+m.width/2,u.x)),y:Math.min(a===s.Down?m.bottom-m.height/2:m.bottom,Math.max(a===s.Down?m.top:m.top+m.height/2,u.y))},h=a===s.Right&&!o||a===s.Left&&!d,y=a===s.Down&&!c||a===s.Up&&!l;if(h&&p.x!==u.x){let e=r.scrollLeft+t.x,l=a===s.Right&&e<=f.x||a===s.Left&&e>=g.x;if(l&&!t.y)return void r.scrollTo({left:e,behavior:i});l?n.x=r.scrollLeft-e:n.x=a===s.Right?r.scrollLeft-f.x:r.scrollLeft-g.x,n.x&&r.scrollBy({left:-n.x,behavior:i});break}if(y&&p.y!==u.y){let e=r.scrollTop+t.y,l=a===s.Down&&e<=f.y||a===s.Up&&e>=g.y;if(l&&!t.x)return void r.scrollTo({top:e,behavior:i});l?n.y=r.scrollTop-e:n.y=a===s.Down?r.scrollTop-f.y:r.scrollTop-g.y,n.y&&r.scrollBy({top:-n.y,behavior:i});break}}this.handleMove(e,B(z(u,this.referenceCoordinates),n))}}}handleMove(e,t){let{onMove:r}=this.props;e.preventDefault(),r(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eA(e){return!!(e&&"distance"in e)}function eE(e){return!!(e&&"delay"in e)}eS.activators=[{eventName:"onKeyDown",handler:(e,t,r)=>{let{keyboardCodes:n=ej,onActivation:a}=t,{active:l}=r,{code:i}=e.nativeEvent;if(n.start.includes(i)){let t=l.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class eN{constructor(e,t,r){var n;void 0===r&&(r=function(e){let{EventTarget:t}=C(e);return e instanceof t?e:A(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:l}=a;this.props=e,this.events=t,this.document=A(l),this.documentListeners=new eb(this.document),this.listeners=new eb(r),this.windowListeners=new eb(C(l)),this.initialCoordinates=null!=(n=q(a))?n:Z,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(i.Resize,this.handleCancel),this.windowListeners.add(i.DragStart,eD),this.windowListeners.add(i.VisibilityChange,this.handleCancel),this.windowListeners.add(i.ContextMenu,eD),this.documentListeners.add(i.Keydown,this.handleKeydown),t){if(null!=r&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eE(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eA(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:r,onPending:n}=this.props;n(r,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(i.Click,eC,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(i.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:r,initialCoordinates:n,props:a}=this,{onMove:l,options:{activationConstraint:i}}=a;if(!n)return;let s=null!=(t=q(e))?t:Z,o=z(n,s);if(!r&&i){if(eA(i)){if(null!=i.tolerance&&ew(o,i.tolerance))return this.handleCancel();if(ew(o,i.distance))return this.handleStart()}return eE(i)&&ew(o,i.tolerance)?this.handleCancel():void this.handlePending(i,o)}e.cancelable&&e.preventDefault(),l(s)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===s.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let eT={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eR extends eN{constructor(e){let{event:t}=e;super(e,eT,A(t.target))}}eR.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return!!r.isPrimary&&0===r.button&&(null==n||n({event:r}),!0)}}];let eM={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(o||(o={}));class eO extends eN{constructor(e){super(e,eM,A(e.event.target))}}eO.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return r.button!==o.RightClick&&(null==n||n({event:r}),!0)}}];let eP={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eI extends eN{constructor(e){super(e,eP)}static setup(){return window.addEventListener(eP.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eP.move.name,e)};function e(){}}}eI.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t,{touches:a}=r;return!(a.length>1)&&(null==n||n({event:r}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(d||(d={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let eL={x:{[l.Backward]:!1,[l.Forward]:!1},y:{[l.Backward]:!1,[l.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(u||(u={})),(f||(f={})).Optimized="optimized";let eB=new Map;function ez(e,t){return R(r=>e?r||("function"==typeof t?t(e):e):null,[t,e])}function e$(e){let{callback:t,disabled:r}=e,n=N(t),a=(0,p.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(n)},[r]);return(0,p.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function eq(e){return new ex(el(e),e)}function eF(e,t,r){void 0===t&&(t=eq);let[n,a]=(0,p.useState)(null);function l(){a(n=>{if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=n?n:r)?a:null}let l=t(e);return JSON.stringify(n)===JSON.stringify(l)?n:l})}let i=function(e){let{callback:t,disabled:r}=e,n=N(t),a=(0,p.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(n)},[n,r]);return(0,p.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let r of t){let{type:t,target:n}=r;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){l();break}}}}),s=e$({callback:l});return E(()=>{l(),e?(null==s||s.observe(e),null==i||i.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==i||i.disconnect())},[e]),n}let eU=[];function e_(e,t){void 0===t&&(t=[]);let r=(0,p.useRef)(null);return(0,p.useEffect)(()=>{r.current=null},t),(0,p.useEffect)(()=>{let t=e!==Z;t&&!r.current&&(r.current=e),!t&&r.current&&(r.current=null)},[e]),r.current?z(e,r.current):Z}function eK(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,r=e.innerHeight;return{top:0,left:0,right:t,bottom:r,width:t,height:r}}(e):null,[e])}let eG=[];function eW(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return k(t)?t:e}let eH=[{sensor:eR,options:{}},{sensor:eS,options:{}}],eY={current:{}},eV={draggable:{measure:ei},droppable:{measure:ei,strategy:u.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:el}};class eX extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,r;return null!=(t=null==(r=this.get(e))?void 0:r.node.current)?t:void 0}}let eJ={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eX,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:X},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eV,measureDroppableContainers:X,windowRect:null,measuringScheduled:!1},eZ={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:X,draggableNodes:new Map,over:null,measureDroppableContainers:X},eQ=(0,p.createContext)(eZ),e0=(0,p.createContext)(eJ);function e1(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eX}}}function e2(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:r}=t,{id:n}=r,a=new eX(e.droppable.containers);return a.set(n,r),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:r,key:n,disabled:a}=t,l=e.droppable.containers.get(r);if(!l||n!==l.key)return e;let i=new eX(e.droppable.containers);return i.set(r,{...l,disabled:a}),{...e,droppable:{...e.droppable,containers:i}}}case a.UnregisterDroppable:{let{id:r,key:n}=t,a=e.droppable.containers.get(r);if(!a||n!==a.key)return e;let l=new eX(e.droppable.containers);return l.delete(r),{...e,droppable:{...e.droppable,containers:l}}}default:return e}}function e5(e){let{disabled:t}=e,{active:r,activatorEvent:n,draggableNodes:a}=(0,p.useContext)(eQ),l=O(n),i=O(null==r?void 0:r.id);return(0,p.useEffect)(()=>{if(!t&&!n&&l&&null!=i){if(!$(l)||document.activeElement===l.target)return;let e=a.get(i);if(!e)return;let{activatorNode:t,node:r}=e;(t.current||r.current)&&requestAnimationFrame(()=>{for(let e of[t.current,r.current]){if(!e)continue;let t=e.matches(U)?e:e.querySelector(U);if(t){t.focus();break}}})}},[n,t,a,i,l]),null}function e3(e,t){let{transform:r,...n}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...n}),r):r}let e4=(0,p.createContext)({...Z,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(g||(g={}));let e9=(0,p.memo)(function(e){var t,r,n,i,s,o;let{id:f,accessibility:m,autoScroll:v=!0,children:x,sensors:w=eH,collisionDetection:D=ee,measuring:j,modifiers:S,...A}=e,[N,P]=(0,p.useReducer)(e2,void 0,e1),[L,z]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:r,event:n}=t;e.forEach(e=>{var t;return null==(t=e[r])?void 0:t.call(e,n)})},[e]),t]}(),[$,F]=(0,p.useState)(g.Uninitialized),U=$===g.Initialized,{draggable:{active:_,nodes:K,translate:G},droppable:{containers:H}}=N,Y=null!=_?K.get(_):null,X=(0,p.useRef)({initial:null,translated:null}),J=(0,p.useMemo)(()=>{var e;return null!=_?{id:_,data:null!=(e=null==Y?void 0:Y.data)?e:eY,rect:X}:null},[_,Y]),Q=(0,p.useRef)(null),[en,ea]=(0,p.useState)(null),[ei,ec]=(0,p.useState)(null),eu=T(A,Object.values(A)),ey=I("DndDescribedBy",f),ev=(0,p.useMemo)(()=>H.getEnabled(),[H]),eb=(0,p.useMemo)(()=>({draggable:{...eV.draggable,...null==j?void 0:j.draggable},droppable:{...eV.droppable,...null==j?void 0:j.droppable},dragOverlay:{...eV.dragOverlay,...null==j?void 0:j.dragOverlay}}),[null==j?void 0:j.draggable,null==j?void 0:j.droppable,null==j?void 0:j.dragOverlay]),{droppableRects:ew,measureDroppableContainers:eD,measuringScheduled:eC}=function(e,t){let{dragging:r,dependencies:n,config:a}=t,[l,i]=(0,p.useState)(null),{frequency:s,measure:o,strategy:d}=a,c=(0,p.useRef)(e),f=function(){switch(d){case u.Always:return!1;case u.BeforeDragging:return r;default:return!r}}(),g=T(f),m=(0,p.useCallback)(function(e){void 0===e&&(e=[]),g.current||i(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[g]),h=(0,p.useRef)(null),y=R(t=>{if(f&&!r)return eB;if(!t||t===eB||c.current!==e||null!=l){let t=new Map;for(let r of e){if(!r)continue;if(l&&l.length>0&&!l.includes(r.id)&&r.rect.current){t.set(r.id,r.rect.current);continue}let e=r.node.current,n=e?new ex(o(e),e):null;r.rect.current=n,n&&t.set(r.id,n)}return t}return t},[e,l,r,f,o]);return(0,p.useEffect)(()=>{c.current=e},[e]),(0,p.useEffect)(()=>{f||m()},[r,f]),(0,p.useEffect)(()=>{l&&l.length>0&&i(null)},[JSON.stringify(l)]),(0,p.useEffect)(()=>{f||"number"!=typeof s||null!==h.current||(h.current=setTimeout(()=>{m(),h.current=null},s))},[s,f,m,...n]),{droppableRects:y,measureDroppableContainers:m,measuringScheduled:null!=l}}(ev,{dragging:U,dependencies:[G.x,G.y],config:eb.droppable}),ej=function(e,t){let r=null!=t?e.get(t):void 0,n=r?r.node.current:null;return R(e=>{var r;return null==t?null:null!=(r=null!=n?n:e)?r:null},[n,t])}(K,_),ek=(0,p.useMemo)(()=>ei?q(ei):null,[ei]),eS=function(){let e=(null==en?void 0:en.autoScrollEnabled)===!1,t="object"==typeof v?!1===v.enabled:!1===v,r=U&&!e&&!t;return"object"==typeof v?{...v,enabled:r}:{enabled:r}}(),eA=ez(ej,eb.draggable.measure);!function(e){let{activeNode:t,measure:r,initialRect:n,config:a=!0}=e,l=(0,p.useRef)(!1),{x:i,y:s}="boolean"==typeof a?{x:a,y:a}:a;E(()=>{if(!i&&!s||!t){l.current=!1;return}if(l.current||!n)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=et(r(e),n);if(i||(a.x=0),s||(a.y=0),l.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=eo(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,i,s,n,r])}({activeNode:null!=_?K.get(_):null,config:eS.layoutShiftCompensation,initialRect:eA,measure:eb.draggable.measure});let eE=eF(ej,eb.draggable.measure,eA),eN=eF(ej?ej.parentElement:null),eT=(0,p.useRef)({activatorEvent:null,active:null,activeNode:ej,collisionRect:null,collisions:null,droppableRects:ew,draggableNodes:K,draggingNode:null,draggingNodeRect:null,droppableContainers:H,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eR=H.getNodeFor(null==(t=eT.current.over)?void 0:t.id),eM=function(e){let{measure:t}=e,[r,n]=(0,p.useState)(null),a=e$({callback:(0,p.useCallback)(e=>{for(let{target:r}of e)if(k(r)){n(e=>{let n=t(r);return e?{...e,width:n.width,height:n.height}:n});break}},[t])}),[l,i]=M((0,p.useCallback)(e=>{let r=eW(e);null==a||a.disconnect(),r&&(null==a||a.observe(r)),n(r?t(r):null)},[t,a]));return(0,p.useMemo)(()=>({nodeRef:l,rect:r,setRef:i}),[r,l,i])}({measure:eb.dragOverlay.measure}),eO=null!=(r=eM.nodeRef.current)?r:ej,eP=U?null!=(n=eM.rect)?n:eE:null,eI=!!(eM.nodeRef.current&&eM.rect),eq=function(e){let t=ez(e);return et(e,t)}(eI?null:eE),eX=eK(eO?C(eO):null),eJ=function(e){let t=(0,p.useRef)(e),r=R(r=>e?r&&r!==eU&&e&&t.current&&e.parentNode===t.current.parentNode?r:es(e):eU,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),r}(U?null!=eR?eR:ej:null),eZ=function(e,t){void 0===t&&(t=el);let[r]=e,n=eK(r?C(r):null),[a,l]=(0,p.useState)(eG);function i(){l(()=>e.length?e.map(e=>eg(e)?n:new ex(t(e),e)):eG)}let s=e$({callback:i});return E(()=>{null==s||s.disconnect(),i(),e.forEach(e=>null==s?void 0:s.observe(e))},[e]),a}(eJ),e9=e3(S,{transform:{x:G.x-eq.x,y:G.y-eq.y,scaleX:1,scaleY:1},activatorEvent:ei,active:J,activeNodeRect:eE,containerNodeRect:eN,draggingNodeRect:eP,over:eT.current.over,overlayNodeRect:eM.rect,scrollableAncestors:eJ,scrollableAncestorRects:eZ,windowRect:eX}),e8=ek?B(ek,G):null,e6=function(e){let[t,r]=(0,p.useState)(null),n=(0,p.useRef)(e),a=(0,p.useCallback)(e=>{let t=ed(e.target);t&&r(e=>e?(e.set(t,ef(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=n.current;if(e!==t){l(t);let i=e.map(e=>{let t=ed(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,ef(t)]):null}).filter(e=>null!=e);r(i.length?new Map(i):null),n.current=e}return()=>{l(e),l(t)};function l(e){e.forEach(e=>{let t=ed(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>B(e,t),Z):eh(e):Z,[e,t])}(eJ),e7=e_(e6),te=e_(e6,[eE]),tt=B(e9,e7),tr=eP?er(eP,e9):null,tn=J&&tr?D({active:J,collisionRect:tr,droppableRects:ew,droppableContainers:ev,pointerCoordinates:e8}):null,ta=function(e,t){if(!e||0===e.length)return null;let[r]=e;return r.id}(tn,"id"),[tl,ti]=(0,p.useState)(null),ts=(s=eI?e9:B(e9,te),o=null!=(i=null==tl?void 0:tl.rect)?i:null,{...s,scaleX:o&&eE?o.width/eE.width:1,scaleY:o&&eE?o.height/eE.height:1}),to=(0,p.useRef)(null),td=(0,p.useCallback)((e,t)=>{let{sensor:r,options:n}=t;if(null==Q.current)return;let l=K.get(Q.current);if(!l)return;let i=e.nativeEvent,s=new r({active:Q.current,activeNode:l,event:i,options:n,context:eT,onAbort(e){if(!K.get(e))return;let{onDragAbort:t}=eu.current,r={id:e};null==t||t(r),L({type:"onDragAbort",event:r})},onPending(e,t,r,n){if(!K.get(e))return;let{onDragPending:a}=eu.current,l={id:e,constraint:t,initialCoordinates:r,offset:n};null==a||a(l),L({type:"onDragPending",event:l})},onStart(e){let t=Q.current;if(null==t)return;let r=K.get(t);if(!r)return;let{onDragStart:n}=eu.current,l={activatorEvent:i,active:{id:t,data:r.data,rect:X}};(0,y.unstable_batchedUpdates)(()=>{null==n||n(l),F(g.Initializing),P({type:a.DragStart,initialCoordinates:e,active:t}),L({type:"onDragStart",event:l}),ea(to.current),ec(i)})},onMove(e){P({type:a.DragMove,coordinates:e})},onEnd:o(a.DragEnd),onCancel:o(a.DragCancel)});function o(e){return async function(){let{active:t,collisions:r,over:n,scrollAdjustedTranslate:l}=eT.current,s=null;if(t&&l){let{cancelDrop:o}=eu.current;s={activatorEvent:i,active:t,collisions:r,delta:l,over:n},e===a.DragEnd&&"function"==typeof o&&await Promise.resolve(o(s))&&(e=a.DragCancel)}Q.current=null,(0,y.unstable_batchedUpdates)(()=>{P({type:e}),F(g.Uninitialized),ti(null),ea(null),ec(null),to.current=null;let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(s){let e=eu.current[t];null==e||e(s),L({type:t,event:s})}})}}to.current=s},[K]),tc=(0,p.useCallback)((e,t)=>(r,n)=>{let a=r.nativeEvent,l=K.get(n);null!==Q.current||!l||a.dndKit||a.defaultPrevented||!0===e(r,t.options,{active:l})&&(a.dndKit={capturedBy:t.sensor},Q.current=n,td(r,t))},[K,td]),tu=(0,p.useMemo)(()=>w.reduce((e,t)=>{let{sensor:r}=t;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:tc(e.handler,t)}))]},[]),[w,tc]);(0,p.useEffect)(()=>{if(!b)return;let e=w.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},w.map(e=>{let{sensor:t}=e;return t})),E(()=>{eE&&$===g.Initializing&&F(g.Initialized)},[eE,$]),(0,p.useEffect)(()=>{let{onDragMove:e}=eu.current,{active:t,activatorEvent:r,collisions:n,over:a}=eT.current;if(!t||!r)return;let l={active:t,activatorEvent:r,collisions:n,delta:{x:tt.x,y:tt.y},over:a};(0,y.unstable_batchedUpdates)(()=>{null==e||e(l),L({type:"onDragMove",event:l})})},[tt.x,tt.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:r,droppableContainers:n,scrollAdjustedTranslate:a}=eT.current;if(!e||null==Q.current||!t||!a)return;let{onDragOver:l}=eu.current,i=n.get(ta),s=i&&i.rect.current?{id:i.id,rect:i.rect.current,data:i.data,disabled:i.disabled}:null,o={active:e,activatorEvent:t,collisions:r,delta:{x:a.x,y:a.y},over:s};(0,y.unstable_batchedUpdates)(()=>{ti(s),null==l||l(o),L({type:"onDragOver",event:o})})},[ta]),E(()=>{eT.current={activatorEvent:ei,active:J,activeNode:ej,collisionRect:tr,collisions:tn,droppableRects:ew,draggableNodes:K,draggingNode:eO,draggingNodeRect:eP,droppableContainers:H,over:tl,scrollableAncestors:eJ,scrollAdjustedTranslate:tt},X.current={initial:eP,translated:tr}},[J,ej,tn,tr,K,eO,eP,ew,H,tl,eJ,tt]),function(e){let{acceleration:t,activator:r=d.Pointer,canScroll:n,draggingRect:a,enabled:i,interval:s=5,order:o=c.TreeOrder,pointerCoordinates:u,scrollableAncestors:f,scrollableAncestorRects:g,delta:m,threshold:h}=e,y=function(e){let{delta:t,disabled:r}=e,n=O(t);return R(e=>{if(r||!n||!e)return eL;let a={x:Math.sign(t.x-n.x),y:Math.sign(t.y-n.y)};return{x:{[l.Backward]:e.x[l.Backward]||-1===a.x,[l.Forward]:e.x[l.Forward]||1===a.x},y:{[l.Backward]:e.y[l.Backward]||-1===a.y,[l.Forward]:e.y[l.Forward]||1===a.y}}},[r,t,n])}({delta:m,disabled:!i}),[v,x]=function(){let e=(0,p.useRef)(null);return[(0,p.useCallback)((t,r)=>{e.current=setInterval(t,r)},[]),(0,p.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),b=(0,p.useRef)({x:0,y:0}),w=(0,p.useRef)({x:0,y:0}),D=(0,p.useMemo)(()=>{switch(r){case d.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case d.DraggableRect:return a}},[r,a,u]),C=(0,p.useRef)(null),j=(0,p.useCallback)(()=>{let e=C.current;if(!e)return;let t=b.current.x*w.current.x,r=b.current.y*w.current.y;e.scrollBy(t,r)},[]),k=(0,p.useMemo)(()=>o===c.TreeOrder?[...f].reverse():f,[o,f]);(0,p.useEffect)(()=>{if(!i||!f.length||!D)return void x();for(let e of k){if((null==n?void 0:n(e))===!1)continue;let r=g[f.indexOf(e)];if(!r)continue;let{direction:a,speed:i}=function(e,t,r,n,a){let{top:i,left:s,right:o,bottom:d}=r;void 0===n&&(n=10),void 0===a&&(a=ep);let{isTop:c,isBottom:u,isLeft:f,isRight:g}=em(e),m={x:0,y:0},p={x:0,y:0},h={height:t.height*a.y,width:t.width*a.x};return!c&&i<=t.top+h.height?(m.y=l.Backward,p.y=n*Math.abs((t.top+h.height-i)/h.height)):!u&&d>=t.bottom-h.height&&(m.y=l.Forward,p.y=n*Math.abs((t.bottom-h.height-d)/h.height)),!g&&o>=t.right-h.width?(m.x=l.Forward,p.x=n*Math.abs((t.right-h.width-o)/h.width)):!f&&s<=t.left+h.width&&(m.x=l.Backward,p.x=n*Math.abs((t.left+h.width-s)/h.width)),{direction:m,speed:p}}(e,r,D,t,h);for(let e of["x","y"])y[e][a[e]]||(i[e]=0,a[e]=0);if(i.x>0||i.y>0){x(),C.current=e,v(j,s),b.current=i,w.current=a;return}}b.current={x:0,y:0},w.current={x:0,y:0},x()},[t,j,n,x,i,s,JSON.stringify(D),JSON.stringify(y),v,f,k,g,JSON.stringify(h)])}({...eS,delta:G,draggingRect:tr,pointerCoordinates:e8,scrollableAncestors:eJ,scrollableAncestorRects:eZ});let tf=(0,p.useMemo)(()=>({active:J,activeNode:ej,activeNodeRect:eE,activatorEvent:ei,collisions:tn,containerNodeRect:eN,dragOverlay:eM,draggableNodes:K,droppableContainers:H,droppableRects:ew,over:tl,measureDroppableContainers:eD,scrollableAncestors:eJ,scrollableAncestorRects:eZ,measuringConfiguration:eb,measuringScheduled:eC,windowRect:eX}),[J,ej,eE,ei,tn,eN,eM,K,H,ew,tl,eD,eJ,eZ,eb,eC,eX]),tg=(0,p.useMemo)(()=>({activatorEvent:ei,activators:tu,active:J,activeNodeRect:eE,ariaDescribedById:{draggable:ey},dispatch:P,draggableNodes:K,over:tl,measureDroppableContainers:eD}),[ei,tu,J,eE,P,ey,K,tl,eD]);return h().createElement(W.Provider,{value:z},h().createElement(eQ.Provider,{value:tg},h().createElement(e0.Provider,{value:tf},h().createElement(e4.Provider,{value:ts},x)),h().createElement(e5,{disabled:(null==m?void 0:m.restoreFocus)===!1})),h().createElement(V,{...m,hiddenTextDescribedById:ey}))}),e8=(0,p.createContext)(null),e6="button";function e7(){return(0,p.useContext)(e0)}let te={timeout:25};function tt(e){let{animation:t,children:r}=e,[n,a]=(0,p.useState)(null),[l,i]=(0,p.useState)(null),s=O(r);return r||n||!s||a(s),E(()=>{if(!l)return;let e=null==n?void 0:n.key,r=null==n?void 0:n.props.id;if(null==e||null==r)return void a(null);Promise.resolve(t(r,l)).then(()=>{a(null)})},[t,n,l]),h().createElement(h().Fragment,null,r,n?(0,p.cloneElement)(n,{ref:i}):null)}let tr={x:0,y:0,scaleX:1,scaleY:1};function tn(e){let{children:t}=e;return h().createElement(eQ.Provider,{value:eZ},h().createElement(e4.Provider,{value:tr},t))}let ta={position:"fixed",touchAction:"none"},tl=e=>$(e)?"transform 250ms ease":void 0,ti=(0,p.forwardRef)((e,t)=>{let{as:r,activatorEvent:n,adjustScale:a,children:l,className:i,rect:s,style:o,transform:d,transition:c=tl}=e;if(!s)return null;let u=a?d:{...d,scaleX:1,scaleY:1},f={...ta,width:s.width,height:s.height,top:s.top,left:s.left,transform:F.Transform.toString(u),transformOrigin:a&&n?function(e,t){let r=q(e);if(!r)return"0 0";let n={x:(r.x-t.left)/t.width*100,y:(r.y-t.top)/t.height*100};return n.x+"% "+n.y+"%"}(n,s):void 0,transition:"function"==typeof c?c(n):c,...o};return h().createElement(r,{className:i,style:f,ref:t},l)}),ts={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:r}}=e;return[{transform:F.Transform.toString(t)},{transform:F.Transform.toString(r)}]},sideEffects:(n={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:r}=e,a={},{styles:l,className:i}=n;if(null!=l&&l.active)for(let[e,r]of Object.entries(l.active))void 0!==r&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,r));if(null!=l&&l.dragOverlay)for(let[e,t]of Object.entries(l.dragOverlay))void 0!==t&&r.node.style.setProperty(e,t);return null!=i&&i.active&&t.node.classList.add(i.active),null!=i&&i.dragOverlay&&r.node.classList.add(i.dragOverlay),function(){for(let[e,r]of Object.entries(a))t.node.style.setProperty(e,r);null!=i&&i.active&&t.node.classList.remove(i.active)}})},to=0,td=h().memo(e=>{let{adjustScale:t=!1,children:r,dropAnimation:n,style:a,transition:l,modifiers:i,wrapperElement:s="div",className:o,zIndex:d=999}=e,{activatorEvent:c,active:u,activeNodeRect:f,containerNodeRect:g,draggableNodes:m,droppableContainers:y,dragOverlay:v,over:x,measuringConfiguration:b,scrollableAncestors:w,scrollableAncestorRects:D,windowRect:j}=e7(),k=(0,p.useContext)(e4),S=function(e){return(0,p.useMemo)(()=>{if(null!=e)return++to},[e])}(null==u?void 0:u.id),A=e3(i,{activatorEvent:c,active:u,activeNodeRect:f,containerNodeRect:g,draggingNodeRect:v.rect,over:x,overlayNodeRect:v.rect,scrollableAncestors:w,scrollableAncestorRects:D,transform:k,windowRect:j}),E=ez(f),T=function(e){let{config:t,draggableNodes:r,droppableContainers:n,measuringConfiguration:a}=e;return N((e,l)=>{if(null===t)return;let i=r.get(e);if(!i)return;let s=i.node.current;if(!s)return;let o=eW(l);if(!o)return;let{transform:d}=C(l).getComputedStyle(l),c=en(d);if(!c)return;let u="function"==typeof t?t:function(e){let{duration:t,easing:r,sideEffects:n,keyframes:a}={...ts,...e};return e=>{let{active:l,dragOverlay:i,transform:s,...o}=e;if(!t)return;let d={x:i.rect.left-l.rect.left,y:i.rect.top-l.rect.top},c={scaleX:1!==s.scaleX?l.rect.width*s.scaleX/i.rect.width:1,scaleY:1!==s.scaleY?l.rect.height*s.scaleY/i.rect.height:1},u={x:s.x-d.x,y:s.y-d.y,...c},f=a({...o,active:l,dragOverlay:i,transform:{initial:s,final:u}}),[g]=f,m=f[f.length-1];if(JSON.stringify(g)===JSON.stringify(m))return;let p=null==n?void 0:n({active:l,dragOverlay:i,...o}),h=i.node.animate(f,{duration:t,easing:r,fill:"forwards"});return new Promise(e=>{h.onfinish=()=>{null==p||p(),e()}})}}(t);return ey(s,a.draggable.measure),u({active:{id:e,data:i.data,node:s,rect:a.draggable.measure(s)},draggableNodes:r,dragOverlay:{node:l,rect:a.dragOverlay.measure(o)},droppableContainers:n,measuringConfiguration:a,transform:c})})}({config:n,draggableNodes:m,droppableContainers:y,measuringConfiguration:b}),R=E?v.setRef:void 0;return h().createElement(tn,null,h().createElement(tt,{animation:T},u&&S?h().createElement(ti,{key:S,id:u.id,ref:R,as:s,activatorEvent:c,adjustScale:t,className:o,transition:l,rect:E,style:{zIndex:d,...a},transform:A},r):null))});function tc(e,t,r){let n=e.slice();return n.splice(r<0?n.length+r:r,0,n.splice(t,1)[0]),n}function tu(e){return null!==e&&e>=0}let tf=e=>{let{rects:t,activeIndex:r,overIndex:n,index:a}=e,l=tc(t,n,r),i=t[a],s=l[a];return s&&i?{x:s.left-i.left,y:s.top-i.top,scaleX:s.width/i.width,scaleY:s.height/i.height}:null},tg="Sortable",tm=h().createContext({activeIndex:-1,containerId:tg,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:tf,disabled:{draggable:!1,droppable:!1}});function tp(e){let{children:t,id:r,items:n,strategy:a=tf,disabled:l=!1}=e,{active:i,dragOverlay:s,droppableRects:o,over:d,measureDroppableContainers:c}=e7(),u=I(tg,r),f=null!==s.rect,g=(0,p.useMemo)(()=>n.map(e=>"object"==typeof e&&"id"in e?e.id:e),[n]),m=null!=i,y=i?g.indexOf(i.id):-1,v=d?g.indexOf(d.id):-1,x=(0,p.useRef)(g),b=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(g,x.current),w=-1!==v&&-1===y||b,D="boolean"==typeof l?{draggable:l,droppable:l}:l;E(()=>{b&&m&&c(g)},[b,g,m,c]),(0,p.useEffect)(()=>{x.current=g},[g]);let C=(0,p.useMemo)(()=>({activeIndex:y,containerId:u,disabled:D,disableTransforms:w,items:g,overIndex:v,useDragOverlay:f,sortedRects:g.reduce((e,t,r)=>{let n=o.get(t);return n&&(e[r]=n),e},Array(g.length)),strategy:a}),[y,u,D.draggable,D.droppable,w,g,v,o,f,a]);return h().createElement(tm.Provider,{value:C},t)}let th=e=>{let{id:t,items:r,activeIndex:n,overIndex:a}=e;return tc(r,n,a).indexOf(t)},ty=e=>{let{containerId:t,isSorting:r,wasDragging:n,index:a,items:l,newIndex:i,previousItems:s,previousContainerId:o,transition:d}=e;return!!d&&!!n&&(s===l||a!==i)&&(!!r||i!==a&&t===o)},tv={duration:200,easing:"ease"},tx="transform",tb=F.Transition.toString({property:tx,duration:0,easing:"linear"}),tw={roleDescription:"sortable"};function tD(e){var t,r,n,l;let{animateLayoutChanges:i=ty,attributes:s,disabled:o,data:d,getNewIndex:c=th,id:u,strategy:f,resizeObserverConfig:g,transition:m=tv}=e,{items:h,containerId:y,activeIndex:v,disabled:x,disableTransforms:b,sortedRects:w,overIndex:D,useDragOverlay:C,strategy:j}=(0,p.useContext)(tm),k=(t=o,r=x,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(n=null==t?void 0:t.draggable)?n:r.draggable,droppable:null!=(l=null==t?void 0:t.droppable)?l:r.droppable}),S=h.indexOf(u),A=(0,p.useMemo)(()=>({sortable:{containerId:y,index:S,items:h},...d}),[y,d,S,h]),N=(0,p.useMemo)(()=>h.slice(h.indexOf(u)),[h,u]),{rect:R,node:O,isOver:P,setNodeRef:L}=function(e){let{data:t,disabled:r=!1,id:n,resizeObserverConfig:l}=e,i=I("Droppable"),{active:s,dispatch:o,over:d,measureDroppableContainers:c}=(0,p.useContext)(eQ),u=(0,p.useRef)({disabled:r}),f=(0,p.useRef)(!1),g=(0,p.useRef)(null),m=(0,p.useRef)(null),{disabled:h,updateMeasurementsFor:y,timeout:v}={...te,...l},x=T(null!=y?y:n),b=e$({callback:(0,p.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=m.current&&clearTimeout(m.current),m.current=setTimeout(()=>{c(Array.isArray(x.current)?x.current:[x.current]),m.current=null},v)},[v]),disabled:h||!s}),[w,D]=M((0,p.useCallback)((e,t)=>{b&&(t&&(b.unobserve(t),f.current=!1),e&&b.observe(e))},[b])),C=T(t);return(0,p.useEffect)(()=>{b&&w.current&&(b.disconnect(),f.current=!1,b.observe(w.current))},[w,b]),(0,p.useEffect)(()=>(o({type:a.RegisterDroppable,element:{id:n,key:i,disabled:r,node:w,rect:g,data:C}}),()=>o({type:a.UnregisterDroppable,key:i,id:n})),[n]),(0,p.useEffect)(()=>{r!==u.current.disabled&&(o({type:a.SetDroppableDisabled,id:n,key:i,disabled:r}),u.current.disabled=r)},[n,i,r,o]),{active:s,rect:g,isOver:(null==d?void 0:d.id)===n,node:w,over:d,setNodeRef:D}}({id:u,data:A,disabled:k.droppable,resizeObserverConfig:{updateMeasurementsFor:N,...g}}),{active:B,activatorEvent:z,activeNodeRect:q,attributes:U,setNodeRef:_,listeners:K,isDragging:G,over:W,setActivatorNodeRef:H,transform:Y}=function(e){let{id:t,data:r,disabled:n=!1,attributes:a}=e,l=I("Draggable"),{activators:i,activatorEvent:s,active:o,activeNodeRect:d,ariaDescribedById:c,draggableNodes:u,over:f}=(0,p.useContext)(eQ),{role:g=e6,roleDescription:m="draggable",tabIndex:h=0}=null!=a?a:{},y=(null==o?void 0:o.id)===t,v=(0,p.useContext)(y?e4:e8),[x,b]=M(),[w,D]=M(),C=(0,p.useMemo)(()=>i.reduce((e,r)=>{let{eventName:n,handler:a}=r;return e[n]=e=>{a(e,t)},e},{}),[i,t]),j=T(r);return E(()=>(u.set(t,{id:t,key:l,node:x,activatorNode:w,data:j}),()=>{let e=u.get(t);e&&e.key===l&&u.delete(t)}),[u,t]),{active:o,activatorEvent:s,activeNodeRect:d,attributes:(0,p.useMemo)(()=>({role:g,tabIndex:h,"aria-disabled":n,"aria-pressed":!!y&&g===e6||void 0,"aria-roledescription":m,"aria-describedby":c.draggable}),[n,g,h,y,m,c.draggable]),isDragging:y,listeners:n?void 0:C,node:x,over:f,setNodeRef:b,setActivatorNodeRef:D,transform:v}}({id:u,data:A,attributes:{...tw,...s},disabled:k.draggable}),V=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,p.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(L,_),X=!!B,J=X&&!b&&tu(v)&&tu(D),Z=!C&&G,Q=Z&&J?Y:null,ee=J?null!=Q?Q:(null!=f?f:j)({rects:w,activeNodeRect:q,activeIndex:v,overIndex:D,index:S}):null,et=tu(v)&&tu(D)?c({id:u,items:h,activeIndex:v,overIndex:D}):S,er=null==B?void 0:B.id,en=(0,p.useRef)({activeId:er,items:h,newIndex:et,containerId:y}),ea=h!==en.current.items,ei=i({active:B,containerId:y,isDragging:G,isSorting:X,id:u,index:S,items:h,newIndex:en.current.newIndex,previousItems:en.current.items,previousContainerId:en.current.containerId,transition:m,wasDragging:null!=en.current.activeId}),es=function(e){let{disabled:t,index:r,node:n,rect:a}=e,[l,i]=(0,p.useState)(null),s=(0,p.useRef)(r);return E(()=>{if(!t&&r!==s.current&&n.current){let e=a.current;if(e){let t=el(n.current,{ignoreTransform:!0}),r={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(r.x||r.y)&&i(r)}}r!==s.current&&(s.current=r)},[t,r,n,a]),(0,p.useEffect)(()=>{l&&i(null)},[l]),l}({disabled:!ei,index:S,node:O,rect:R});return(0,p.useEffect)(()=>{X&&en.current.newIndex!==et&&(en.current.newIndex=et),y!==en.current.containerId&&(en.current.containerId=y),h!==en.current.items&&(en.current.items=h)},[X,et,y,h]),(0,p.useEffect)(()=>{if(er===en.current.activeId)return;if(er&&!en.current.activeId){en.current.activeId=er;return}let e=setTimeout(()=>{en.current.activeId=er},50);return()=>clearTimeout(e)},[er]),{active:B,activeIndex:v,attributes:U,data:A,rect:R,index:S,newIndex:et,items:h,isOver:P,isSorting:X,isDragging:G,listeners:K,node:O,overIndex:D,over:W,setNodeRef:V,setActivatorNodeRef:H,setDroppableNodeRef:L,setDraggableNodeRef:_,transform:null!=es?es:ee,transition:es||ea&&en.current.newIndex===S?tb:(!Z||$(z))&&m&&(X||ei)?F.Transition.toString({...m,property:tx}):void 0}}s.Down,s.Right,s.Up,s.Left;var tC=r(25758),tj=r(57850),tk=r(56682),tS=r(9260),tA=r(78103),tE=r(91269),tN=r(92681),tT=r(59672),tR=r(81012);function tM({title:e,id:t}){let[r,n]=p.useState(e),a=(0,v.O)(e=>e.updateCol),l=(0,v.O)(e=>e.removeCol),[i,s]=p.useState(!0),[o,d]=p.useState(!1),c=p.useRef(null);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("form",{onSubmit:n=>{n.preventDefault(),s(!i),a(t,r),(0,tR.oR)(`${e} updated to ${r}`)},children:(0,m.jsx)(tT.p,{value:r,onChange:e=>n(e.target.value),className:"mt-0! mr-auto text-base disabled:cursor-pointer disabled:border-none disabled:opacity-100",disabled:i,ref:c,"data-sentry-element":"Input","data-sentry-source-file":"column-action.tsx"})}),(0,m.jsxs)(tN.rI,{modal:!1,"data-sentry-element":"DropdownMenu","data-sentry-source-file":"column-action.tsx",children:[(0,m.jsx)(tN.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"column-action.tsx",children:(0,m.jsxs)(tk.$,{variant:"secondary",className:"ml-1","data-sentry-element":"Button","data-sentry-source-file":"column-action.tsx",children:[(0,m.jsx)("span",{className:"sr-only",children:"Actions"}),(0,m.jsx)(tA.Oer,{className:"h-4 w-4","data-sentry-element":"DotsHorizontalIcon","data-sentry-source-file":"column-action.tsx"})]})}),(0,m.jsxs)(tN.SQ,{align:"end","data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"column-action.tsx",children:[(0,m.jsx)(tN._2,{onSelect:()=>{s(!i),setTimeout(()=>{c.current&&c.current?.focus()},500)},"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"column-action.tsx",children:"Rename"}),(0,m.jsx)(tN.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"column-action.tsx"}),(0,m.jsx)(tN._2,{onSelect:()=>d(!0),className:"text-red-600","data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"column-action.tsx",children:"Delete Section"})]})]}),(0,m.jsx)(tE.Lt,{open:o,onOpenChange:d,"data-sentry-element":"AlertDialog","data-sentry-source-file":"column-action.tsx",children:(0,m.jsxs)(tE.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"column-action.tsx",children:[(0,m.jsxs)(tE.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"column-action.tsx",children:[(0,m.jsx)(tE.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"column-action.tsx",children:"Are you sure want to delete column?"}),(0,m.jsx)(tE.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"column-action.tsx",children:"NOTE: All tasks related to this category will also be deleted."})]}),(0,m.jsxs)(tE.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"column-action.tsx",children:[(0,m.jsx)(tE.Zr,{"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"column-action.tsx",children:"Cancel"}),(0,m.jsx)(tk.$,{variant:"destructive",onClick:()=>{setTimeout(()=>document.body.style.pointerEvents="",100),d(!1),l(t),(0,tR.oR)("This column has been deleted.")},"data-sentry-element":"Button","data-sentry-source-file":"column-action.tsx",children:"Delete"})]})]})})]})}var tO=r(80601);function tP({task:e,isOverlay:t}){let{setNodeRef:r,attributes:n,listeners:a,transform:l,transition:i,isDragging:s}=tD({id:e.id,data:{type:"Task",task:e},attributes:{roleDescription:"Task"}}),o={transition:i,transform:F.Translate.toString(l)},d=(0,tC.F)("mb-2",{variants:{dragging:{over:"ring-2 opacity-30",overlay:"ring-2 ring-primary"}}});return(0,m.jsxs)(tS.Zp,{ref:r,style:o,className:d({dragging:t?"overlay":s?"over":void 0}),"data-sentry-element":"Card","data-sentry-component":"TaskCard","data-sentry-source-file":"task-card.tsx",children:[(0,m.jsxs)(tS.aR,{className:"space-between border-secondary relative flex flex-row border-b-2 px-3 py-3","data-sentry-element":"CardHeader","data-sentry-source-file":"task-card.tsx",children:[(0,m.jsxs)(tk.$,{variant:"ghost",...n,...a,className:"text-secondary-foreground/50 -ml-2 h-auto cursor-grab p-1","data-sentry-element":"Button","data-sentry-source-file":"task-card.tsx",children:[(0,m.jsx)("span",{className:"sr-only",children:"Move task"}),(0,m.jsx)(tj.A,{"data-sentry-element":"GripVertical","data-sentry-source-file":"task-card.tsx"})]}),(0,m.jsx)(tO.E,{variant:"outline",className:"ml-auto font-semibold","data-sentry-element":"Badge","data-sentry-source-file":"task-card.tsx",children:"Task"})]}),(0,m.jsx)(tS.Wu,{className:"px-3 pt-3 pb-6 text-left whitespace-pre-wrap","data-sentry-element":"CardContent","data-sentry-source-file":"task-card.tsx",children:e.title})]})}var tI=r(51643);function tL({column:e,tasks:t,isOverlay:r}){let n=(0,p.useMemo)(()=>t.map(e=>e.id),[t]),{setNodeRef:a,attributes:l,listeners:i,transform:s,transition:o,isDragging:d}=tD({id:e.id,data:{type:"Column",column:e},attributes:{roleDescription:`Column: ${e.title}`}}),c={transition:o,transform:F.Translate.toString(s)},u=(0,tC.F)("h-[75vh] max-h-[75vh] w-[350px] max-w-full bg-secondary flex flex-col shrink-0 snap-center",{variants:{dragging:{default:"border-2 border-transparent",over:"ring-2 opacity-30",overlay:"ring-2 ring-primary"}}});return(0,m.jsxs)(tS.Zp,{ref:a,style:c,className:u({dragging:r?"overlay":d?"over":void 0}),"data-sentry-element":"Card","data-sentry-component":"BoardColumn","data-sentry-source-file":"board-column.tsx",children:[(0,m.jsxs)(tS.aR,{className:"space-between flex flex-row items-center border-b-2 p-4 text-left font-semibold","data-sentry-element":"CardHeader","data-sentry-source-file":"board-column.tsx",children:[(0,m.jsxs)(tk.$,{variant:"ghost",...l,...i,className:"text-primary/50 relative -ml-2 h-auto cursor-grab p-1","data-sentry-element":"Button","data-sentry-source-file":"board-column.tsx",children:[(0,m.jsx)("span",{className:"sr-only",children:`Move column: ${e.title}`}),(0,m.jsx)(tj.A,{"data-sentry-element":"GripVertical","data-sentry-source-file":"board-column.tsx"})]}),(0,m.jsx)(tM,{id:e.id,title:e.title,"data-sentry-element":"ColumnActions","data-sentry-source-file":"board-column.tsx"})]}),(0,m.jsx)(tS.Wu,{className:"flex grow flex-col gap-4 overflow-x-hidden p-2","data-sentry-element":"CardContent","data-sentry-source-file":"board-column.tsx",children:(0,m.jsx)(tI.ScrollArea,{className:"h-full","data-sentry-element":"ScrollArea","data-sentry-source-file":"board-column.tsx",children:(0,m.jsx)(tp,{items:n,"data-sentry-element":"SortableContext","data-sentry-source-file":"board-column.tsx",children:t.map(e=>(0,m.jsx)(tP,{task:e},e.id))})})})]})}function tB({children:e}){let t=e7(),r=(0,tC.F)("px-2  pb-4 md:px-0 flex lg:justify-start",{variants:{dragging:{default:"",active:"snap-none"}}});return(0,m.jsxs)(tI.ScrollArea,{className:"w-full rounded-md whitespace-nowrap","data-sentry-element":"ScrollArea","data-sentry-component":"BoardContainer","data-sentry-source-file":"board-column.tsx",children:[(0,m.jsx)("div",{className:r({dragging:t.active?"active":"default"}),children:(0,m.jsx)("div",{className:"flex flex-row items-start justify-center gap-4",children:e})}),(0,m.jsx)(tI.$,{orientation:"horizontal","data-sentry-element":"ScrollBar","data-sentry-source-file":"board-column.tsx"})]})}var tz=r(93438);function t$(){let e=(0,v.O)(e=>e.addCol);return(0,m.jsxs)(tz.lG,{"data-sentry-element":"Dialog","data-sentry-component":"NewSectionDialog","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,m.jsx)(tz.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-section-dialog.tsx",children:(0,m.jsx)(tk.$,{variant:"secondary",size:"lg",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"new-section-dialog.tsx",children:"＋ Add New Section"})}),(0,m.jsxs)(tz.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,m.jsxs)(tz.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,m.jsx)(tz.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"new-section-dialog.tsx",children:"Add New Section"}),(0,m.jsx)(tz.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"new-section-dialog.tsx",children:"What section you want to add today?"})]}),(0,m.jsx)("form",{id:"todo-form",className:"grid gap-4 py-4",onSubmit:t=>{t.preventDefault();let{title:r}=Object.fromEntries(new FormData(t.currentTarget));"string"==typeof r&&e(r)},children:(0,m.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,m.jsx)(tT.p,{id:"title",name:"title",placeholder:"Section title...",className:"col-span-4","data-sentry-element":"Input","data-sentry-source-file":"new-section-dialog.tsx"})})}),(0,m.jsx)(tz.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"new-section-dialog.tsx",children:(0,m.jsx)(tz.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-section-dialog.tsx",children:(0,m.jsx)(tk.$,{type:"submit",size:"sm",form:"todo-form","data-sentry-element":"Button","data-sentry-source-file":"new-section-dialog.tsx",children:"Add Section"})})})]})]})}function tq(){let e=(0,v.O)(e=>e.columns),t=(0,v.O)(e=>e.setCols),r=(0,p.useRef)("TODO"),n=(0,p.useMemo)(()=>e.map(e=>e.id),[e]),a=(0,v.O)(e=>e.tasks),l=(0,v.O)(e=>e.setTasks),[i,s]=(0,p.useState)(null),[o,d]=(0,p.useState)(!1),[c,u]=(0,p.useState)(null),f=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,p.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(J(eO),J(eI));if(o)return(0,m.jsxs)(e9,{accessibility:{announcements:{onDragStart({active:t}){if(x(t)){if(t.data.current?.type==="Column"){let r=n.findIndex(e=>e===t.id),a=e[r];return`Picked up Column ${a?.title} at position: ${r+1} of ${n.length}`}else if(t.data.current?.type==="Task"){r.current=t.data.current.task.status;let{tasksInColumn:e,taskPosition:n,column:a}=g(t.id,r.current);return`Picked up Task ${t.data.current.task.title} at position: ${n+1} of ${e.length} in column ${a?.title}`}}},onDragOver({active:e,over:t}){if(x(e)&&x(t)){if(e.data.current?.type==="Column"&&t.data.current?.type==="Column"){let r=n.findIndex(e=>e===t.id);return`Column ${e.data.current.column.title} was moved over ${t.data.current.column.title} at position ${r+1} of ${n.length}`}else if(e.data.current?.type==="Task"&&t.data.current?.type==="Task"){let{tasksInColumn:n,taskPosition:a,column:l}=g(t.id,t.data.current.task.status);return t.data.current.task.status!==r.current?`Task ${e.data.current.task.title} was moved over column ${l?.title} in position ${a+1} of ${n.length}`:`Task was moved over position ${a+1} of ${n.length} in column ${l?.title}`}}},onDragEnd({active:e,over:t}){if(!x(e)||!x(t)){r.current="TODO";return}if(e.data.current?.type==="Column"&&t.data.current?.type==="Column"){let r=n.findIndex(e=>e===t.id);return`Column ${e.data.current.column.title} was dropped into position ${r+1} of ${n.length}`}if(e.data.current?.type==="Task"&&t.data.current?.type==="Task"){let{tasksInColumn:e,taskPosition:n,column:a}=g(t.id,t.data.current.task.status);return t.data.current.task.status!==r.current?`Task was dropped into column ${a?.title} in position ${n+1} of ${e.length}`:`Task was dropped into position ${n+1} of ${e.length} in column ${a?.title}`}r.current="TODO"},onDragCancel({active:e}){if(r.current="TODO",x(e))return`Dragging ${e.data.current?.type} cancelled.`}}},sensors:f,onDragStart:function(e){if(!x(e.active))return;let t=e.active.data.current;return t?.type==="Column"?void s(t.column):t?.type==="Task"?void u(t.task):void 0},onDragEnd:function(r){s(null),u(null);let{active:n,over:a}=r;if(!a)return;let l=n.id,i=a.id;if(!x(n))return;let o=n.data.current;if(l===i||o?.type!=="Column")return;let d=e.findIndex(e=>e.id===l),c=e.findIndex(e=>e.id===i);t(tc(e,d,c))},onDragOver:function(e){let{active:t,over:r}=e;if(!r)return;let n=t.id,i=r.id;if(n===i||!x(t)||!x(r))return;let s=t.data.current,o=r.data.current,d=s?.type==="Task",c=s?.type==="Task";if(!d)return;if(d&&c){let e=a.findIndex(e=>e.id===n),t=a.findIndex(e=>e.id===i),r=a[e],s=a[t];r&&s&&r.status!==s.status&&(r.status=s.status,l(tc(a,e,t-1))),l(tc(a,e,t))}let u=o?.type==="Column";if(d&&u){let e=a.findIndex(e=>e.id===n),t=a[e];t&&(t.status=i,l(tc(a,e,e)))}},"data-sentry-element":"DndContext","data-sentry-component":"KanbanBoard","data-sentry-source-file":"kanban-board.tsx",children:[(0,m.jsx)(tB,{"data-sentry-element":"BoardContainer","data-sentry-source-file":"kanban-board.tsx",children:(0,m.jsxs)(tp,{items:n,"data-sentry-element":"SortableContext","data-sentry-source-file":"kanban-board.tsx",children:[e?.map((t,r)=>(0,m.jsxs)(p.Fragment,{children:[(0,m.jsx)(tL,{column:t,tasks:a.filter(e=>e.status===t.id)}),r===e?.length-1&&(0,m.jsx)("div",{className:"w-[300px]",children:(0,m.jsx)(t$,{})})]},t.id)),!e.length&&(0,m.jsx)(t$,{})]})}),"document"in window&&(0,y.createPortal)((0,m.jsxs)(td,{children:[i&&(0,m.jsx)(tL,{isOverlay:!0,column:i,tasks:a.filter(e=>e.status===i.id)}),c&&(0,m.jsx)(tP,{task:c,isOverlay:!0})]}),document.body)]});function g(t,r){let n=a.filter(e=>e.status===r),l=n.findIndex(e=>e.id===t);return{tasksInColumn:n,taskPosition:l,column:e.find(e=>e.id===r)}}}},51643:(e,t,r)=>{"use strict";r.d(t,{$:()=>s,ScrollArea:()=>i});var n=r(91754);r(93491);var a=r(43168),l=r(82233);function i({className:e,children:t,...r}){return(0,n.jsxs)(a.bL,{"data-slot":"scroll-area",className:(0,l.cn)("relative",e),...r,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,n.jsx)(a.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,n.jsx)(s,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,n.jsx)(a.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function s({className:e,orientation:t="vertical",...r}){return(0,n.jsx)(a.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,l.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,n.jsx)(a.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53891:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(18188);r(95093);var a=r(99781);function l({children:e,scrollable:t=!0}){return(0,n.jsx)(n.Fragment,{children:t?(0,n.jsx)(a.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,n.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,n.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57714:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(91754),a=r(56682),l=r(93438),i=r(59672),s=r(58428),o=r(13358);function d(){let e=(0,o.O)(e=>e.addTask);return(0,n.jsxs)(l.lG,{"data-sentry-element":"Dialog","data-sentry-component":"NewTaskDialog","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsx)(l.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(a.$,{variant:"secondary",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"new-task-dialog.tsx",children:"＋ Add New Todo"})}),(0,n.jsxs)(l.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsxs)(l.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsx)(l.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"new-task-dialog.tsx",children:"Add New Todo"}),(0,n.jsx)(l.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"new-task-dialog.tsx",children:"What do you want to get done today?"})]}),(0,n.jsxs)("form",{id:"todo-form",className:"grid gap-4 py-4",onSubmit:t=>{t.preventDefault();let{title:r,description:n}=Object.fromEntries(new FormData(t.currentTarget));"string"==typeof r&&"string"==typeof n&&e(r,n)},children:[(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(i.p,{id:"title",name:"title",placeholder:"Todo title...",className:"col-span-4","data-sentry-element":"Input","data-sentry-source-file":"new-task-dialog.tsx"})}),(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(s.T,{id:"description",name:"description",placeholder:"Description...",className:"col-span-4","data-sentry-element":"Textarea","data-sentry-source-file":"new-task-dialog.tsx"})})]}),(0,n.jsx)(l.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(l.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(a.$,{type:"submit",size:"sm",form:"todo-form","data-sentry-element":"Button","data-sentry-source-file":"new-task-dialog.tsx",children:"Add Todo"})})})]})]})}},57850:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(55732).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},57975:e=>{"use strict";e.exports=require("node:util")},58428:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var n=r(91754);r(93491);var a=r(82233);function l({className:e,...t}){return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},60290:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>x,generateImageMetadata:()=>y,generateMetadata:()=>h,generateViewport:()=>v,metadata:()=>f});var a=r(63033),l=r(18188),i=r(5434),s=r(45188),o=r(67999),d=r(4590),c=r(23064),u=r(7688);let f={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function g({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,l.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,l.jsxs)(d.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,l.jsx)(s.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,l.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,l.jsx)(o.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,l.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let m={...a},p="workUnitAsyncStorage"in m?m.workUnitAsyncStorage:"requestAsyncStorage"in m?m.requestAsyncStorage:void 0;n=new Proxy(g,{apply:(e,t,r)=>{let n,a,l;try{let e=p?.getStore();n=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,l=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:n,baggageHeader:a,headers:l}).apply(t,r)}});let h=void 0,y=void 0,v=void 0,x=n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63780:(e,t,r)=>{Promise.resolve().then(r.bind(r,51643)),Promise.resolve().then(r.bind(r,48163)),Promise.resolve().then(r.bind(r,57714))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80601:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(91754);r(93491);var a=r(16435),l=r(25758),i=r(82233);let s=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:r=!1,...l}){let o=r?a.DX:"span";return(0,n.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(s({variant:t}),e),...l,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},82546:(e,t,r)=>{"use strict";r.d(t,{UC:()=>I,VY:()=>$,ZD:()=>B,ZL:()=>O,bL:()=>R,hE:()=>z,hJ:()=>P,l9:()=>M,rc:()=>L});var n=r(93491),a=r(10158),l=r(42014),i=r(18227),s=r(18682),o=r(16435),d=r(91754),c="AlertDialog",[u,f]=(0,a.A)(c,[i.Hs]),g=(0,i.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,n=g(t);return(0,d.jsx)(i.bL,{...n,...r,modal:!0})};m.displayName=c;var p=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=g(r);return(0,d.jsx)(i.l9,{...a,...n,ref:t})});p.displayName="AlertDialogTrigger";var h=e=>{let{__scopeAlertDialog:t,...r}=e,n=g(t);return(0,d.jsx)(i.ZL,{...n,...r})};h.displayName="AlertDialogPortal";var y=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=g(r);return(0,d.jsx)(i.hJ,{...a,...n,ref:t})});y.displayName="AlertDialogOverlay";var v="AlertDialogContent",[x,b]=u(v),w=(0,o.Dc)("AlertDialogContent"),D=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:a,...o}=e,c=g(r),u=n.useRef(null),f=(0,l.s)(t,u),m=n.useRef(null);return(0,d.jsx)(i.G$,{contentName:v,titleName:C,docsSlug:"alert-dialog",children:(0,d.jsx)(x,{scope:r,cancelRef:m,children:(0,d.jsxs)(i.UC,{role:"alertdialog",...c,...o,ref:f,onOpenAutoFocus:(0,s.m)(o.onOpenAutoFocus,e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(w,{children:a}),(0,d.jsx)(T,{contentRef:u})]})})})});D.displayName=v;var C="AlertDialogTitle",j=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=g(r);return(0,d.jsx)(i.hE,{...a,...n,ref:t})});j.displayName=C;var k="AlertDialogDescription",S=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=g(r);return(0,d.jsx)(i.VY,{...a,...n,ref:t})});S.displayName=k;var A=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=g(r);return(0,d.jsx)(i.bm,{...a,...n,ref:t})});A.displayName="AlertDialogAction";var E="AlertDialogCancel",N=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:a}=b(E,r),s=g(r),o=(0,l.s)(t,a);return(0,d.jsx)(i.bm,{...s,...n,ref:o})});N.displayName=E;var T=({contentRef:e})=>{let t=`\`${v}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${v}\` by passing a \`${k}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${v}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return n.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},R=m,M=p,O=h,P=y,I=D,L=A,B=N,z=j,$=S},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87933:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var n=r(95500),a=r(56947),l=r(26052),i=r(13636),s={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>i[e]);r.d(t,s);let o={children:["",{children:["dashboard",{children:["kanban",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35590)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\kanban\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\kanban\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/kanban/page",pathname:"/dashboard/kanban",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},91269:(e,t,r)=>{"use strict";r.d(t,{$v:()=>p,EO:()=>u,Lt:()=>s,Rx:()=>h,Zr:()=>y,ck:()=>g,r7:()=>m,tv:()=>o,wd:()=>f});var n=r(91754);r(93491);var a=r(82546),l=r(82233),i=r(56682);function s({...e}){return(0,n.jsx)(a.bL,{"data-slot":"alert-dialog",...e,"data-sentry-element":"AlertDialogPrimitive.Root","data-sentry-component":"AlertDialog","data-sentry-source-file":"alert-dialog.tsx"})}function o({...e}){return(0,n.jsx)(a.l9,{"data-slot":"alert-dialog-trigger",...e,"data-sentry-element":"AlertDialogPrimitive.Trigger","data-sentry-component":"AlertDialogTrigger","data-sentry-source-file":"alert-dialog.tsx"})}function d({...e}){return(0,n.jsx)(a.ZL,{"data-slot":"alert-dialog-portal",...e,"data-sentry-element":"AlertDialogPrimitive.Portal","data-sentry-component":"AlertDialogPortal","data-sentry-source-file":"alert-dialog.tsx"})}function c({className:e,...t}){return(0,n.jsx)(a.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"AlertDialogPrimitive.Overlay","data-sentry-component":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"})}function u({className:e,...t}){return(0,n.jsxs)(d,{"data-sentry-element":"AlertDialogPortal","data-sentry-component":"AlertDialogContent","data-sentry-source-file":"alert-dialog.tsx",children:[(0,n.jsx)(c,{"data-sentry-element":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"}),(0,n.jsx)(a.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t,"data-sentry-element":"AlertDialogPrimitive.Content","data-sentry-source-file":"alert-dialog.tsx"})]})}function f({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"AlertDialogHeader","data-sentry-source-file":"alert-dialog.tsx"})}function g({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"AlertDialogFooter","data-sentry-source-file":"alert-dialog.tsx"})}function m({className:e,...t}){return(0,n.jsx)(a.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",e),...t,"data-sentry-element":"AlertDialogPrimitive.Title","data-sentry-component":"AlertDialogTitle","data-sentry-source-file":"alert-dialog.tsx"})}function p({className:e,...t}){return(0,n.jsx)(a.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"AlertDialogPrimitive.Description","data-sentry-component":"AlertDialogDescription","data-sentry-source-file":"alert-dialog.tsx"})}function h({className:e,...t}){return(0,n.jsx)(a.rc,{className:(0,l.cn)((0,i.r)(),e),...t,"data-sentry-element":"AlertDialogPrimitive.Action","data-sentry-component":"AlertDialogAction","data-sentry-source-file":"alert-dialog.tsx"})}function y({className:e,...t}){return(0,n.jsx)(a.ZD,{className:(0,l.cn)((0,i.r)({variant:"outline"}),e),...t,"data-sentry-element":"AlertDialogPrimitive.Cancel","data-sentry-component":"AlertDialogCancel","data-sentry-source-file":"alert-dialog.tsx"})}},93438:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>g,L3:()=>m,c7:()=>f,lG:()=>s,rr:()=>p,zM:()=>o});var n=r(91754);r(93491);var a=r(18227),l=r(31619),i=r(82233);function s({...e}){return(0,n.jsx)(a.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function o({...e}){return(0,n.jsx)(a.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,n.jsx)(a.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,n.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u({className:e,children:t,...r}){return(0,n.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,n.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,n.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,n.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,n.jsx)(l.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function g({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function m({className:e,...t}){return(0,n.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,n.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},94735:e=>{"use strict";e.exports=require("events")},98732:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(1472).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\kanban\\\\components\\\\new-task-dialog.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\kanban\\components\\new-task-dialog.tsx","default")},99781:(e,t,r)=>{"use strict";r.d(t,{ScrollArea:()=>a});var n=r(1472);let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\scroll-area.tsx","ScrollArea");(0,n.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\scroll-area.tsx","ScrollBar")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,787,8103,8134,8634],()=>r(87933));module.exports=n})();
//# sourceMappingURL=page.js.map