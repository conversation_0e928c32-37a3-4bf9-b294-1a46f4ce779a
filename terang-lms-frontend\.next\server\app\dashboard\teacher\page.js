try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="67b930c1-017c-43af-9df9-c161b847b2eb",e._sentryDebugIdIdentifier="sentry-dbid-67b930c1-017c-43af-9df9-c161b847b2eb")}catch(e){}(()=>{var e={};e.id=7e3,e.ids=[7e3],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(91754);function a({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}s(93491),s(76328)},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46378:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(91754),a=s(9260),n=s(56682),o=s(80601),i=s(19698),d=s(41939),l=s(84795),c=s(41867),u=s(77406),p=s(16041),x=s.n(p),m=s(76328),f=s(7375),h=s(93491);function y(){let[e,t]=(0,h.useState)(m.qs.getUser()),[s,p]=(0,h.useState)(!0);if(s)return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading..."});if(!e?.institutionId)return(0,r.jsx)(f.A,{userRole:"teacher"});let y={totalClasses:5,totalCourses:12,totalStudents:150,completionRate:78};return(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"TeacherDashboard","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Teacher Dashboard"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your classes, courses, and track student progress"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(x(),{href:"/dashboard/teacher/courses/generate","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(n.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Bot","data-sentry-source-file":"page.tsx"}),"AI Generator"]})}),(0,r.jsx)(x(),{href:"/dashboard/teacher/courses/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(n.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Create Course"]})})]})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Classes"}),(0,r.jsx)(l.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Users","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.totalClasses}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Active classes"})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Courses"}),(0,r.jsx)(c.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.totalCourses}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Published courses"})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Students"}),(0,r.jsx)(l.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Users","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.totalStudents}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Enrolled students"})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Completion Rate"}),(0,r.jsx)(u.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[y.completionRate,"%"]}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Average completion"})]})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Recent Courses"}),(0,r.jsx)(a.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your latest course activities"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"space-y-4",children:[{id:1,name:"Introduction to Mathematics",type:"self_paced",students:45,completion:85,status:"active"},{id:2,name:"Basic Physics",type:"verified",students:32,completion:72,status:"active"},{id:3,name:"Chemistry Fundamentals",type:"self_paced",students:28,completion:90,status:"completed"}].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.E,{variant:"verified"===e.type?"default":"secondary",children:e.type}),(0,r.jsx)(o.E,{variant:"active"===e.status?"default":"outline",children:e.status}),(0,r.jsxs)("span",{className:"text-muted-foreground text-sm",children:[e.students," students"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-sm font-medium",children:[e.completion,"%"]}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"completion"})]}),(0,r.jsx)(x(),{href:`/dashboard/teacher/courses/${e.id}`,children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",children:"View"})})]})]},e.id))}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(x(),{href:"/dashboard/teacher/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(n.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"View All Courses"})})})]})]})]})}},47656:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),o=s(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof i?new Proxy(i,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):i;let c=void 0,u=void 0,p=void 0,x=r},48161:e=>{"use strict";e.exports=require("node:os")},52377:(e,t,s)=>{Promise.resolve().then(s.bind(s,14621))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64755:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),o=s(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof i?new Proxy(i,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher",componentType:"Layout",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):i;let c=void 0,u=void 0,p=void 0,x=r},66286:(e,t,s)=>{Promise.resolve().then(s.bind(s,46378))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79617:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var r=s(95500),a=s(56947),n=s(26052),o=s(13636),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let d={children:["",{children:["dashboard",{children:["teacher",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,47656)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,64755)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/teacher/page",pathname:"/dashboard/teacher",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81753:(e,t,s)=>{Promise.resolve().then(s.bind(s,64755))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96550:(e,t,s)=>{Promise.resolve().then(s.bind(s,47656))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,787,8134,8634,8243],()=>s(79617));module.exports=r})();
//# sourceMappingURL=page.js.map