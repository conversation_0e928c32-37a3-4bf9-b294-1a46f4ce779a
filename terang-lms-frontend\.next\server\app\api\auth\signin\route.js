try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="449f36f1-b20f-4516-951a-0a95f29cc2d5",e._sentryDebugIdIdentifier="sentry-dbid-449f36f1-b20f-4516-951a-0a95f29cc2d5")}catch(e){}(()=>{var e={};e.id=4957,e.ids=[4957],e.modules={1683:(e,r,t)=>{"use strict";t.d(r,{P:()=>o});var s=t(138);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let i=(0,s.lw)(process.env.DATABASE_URL);async function o(e,...r){return await i(e,...r)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=44725,e.exports=r},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78099:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>A,serverHooks:()=>S,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>T});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>h,HEAD:()=>w,OPTIONS:()=>E,PATCH:()=>y,POST:()=>g,PUT:()=>v});var i=t(3690),o=t(56947),n=t(75250),u=t(63033),a=t(62187),d=t(82446),p=t(1683),c=t(7688);async function l(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return a.NextResponse.json({success:!1,error:"Email and password are required"},{status:400});let s=(await (0,p.P)`
      SELECT u.*, i.name as institution_name
      FROM users u
      LEFT JOIN institutions i ON u.institution_id = i.id
      WHERE u.email = ${r.toLowerCase()}
    `)[0];if(!s||!await d.Ay.compare(t,s.password))return a.NextResponse.json({success:!1,error:"Invalid email or password"},{status:401});let i={id:s.id,name:s.name,email:s.email,role:s.role,institutionId:s.institution_id||void 0};return a.NextResponse.json({success:!0,data:{user:i,institution:s?{id:s.institution_id,name:s.institution_name}:void 0},message:"Sign in successful"})}catch(e){return console.error("Sign in error:",e),a.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let x={...u},q="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;function f(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let i;try{let e=q?.getStore();i=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/auth/signin",headers:i}).apply(t,s)}})}let h=f(void 0,"GET"),g=f(l,"POST"),v=f(void 0,"PUT"),y=f(void 0,"PATCH"),m=f(void 0,"DELETE"),w=f(void 0,"HEAD"),E=f(void 0,"OPTIONS"),A=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/signin/route",pathname:"/api/auth/signin",filename:"route",bundlePath:"app/api/auth/signin/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\signin\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:T,serverHooks:S}=A;function P(){return(0,n.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:T})}},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,2446],()=>t(78099));module.exports=s})();
//# sourceMappingURL=route.js.map