try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="854eae8e-2451-4b2c-9ac2-f729883aa42f",e._sentryDebugIdIdentifier="sentry-dbid-854eae8e-2451-4b2c-9ac2-f729883aa42f")}catch(e){}"use strict";(()=>{var e={};e.id=5369,e.ids=[5369],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},50912:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>S,routeModule:()=>j,serverHooks:()=>E,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>R});var s={};t.r(s),t.d(s,{DELETE:()=>N,GET:()=>I,HEAD:()=>A,OPTIONS:()=>b,PATCH:()=>v,POST:()=>g,PUT:()=>m});var o=t(3690),n=t(56947),i=t(75250),u=t(63033),a=t(62187),d=t(18621),p=t(32230),l=t(74683),q=t(7688);async function c(e,{params:r}){try{let{id:e}=await r,t=parseInt(e);if(isNaN(t))return a.NextResponse.json({error:"Invalid question ID"},{status:400});let s=await d.db.select().from(p.questions).where((0,l.eq)(p.questions.id,t)).limit(1);if(0===s.length)return a.NextResponse.json({error:"Question not found"},{status:404});let o=s[0],n={...o,question:o.question,options:o.options,essayAnswer:o.essayAnswer,explanation:o.explanation};return a.NextResponse.json({question:n})}catch(e){return console.error("Error fetching question:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let{id:t}=await r,s=parseInt(t);if(isNaN(s))return a.NextResponse.json({error:"Invalid question ID"},{status:400});let{type:o,question:n,options:i,essayAnswer:u,explanation:q,points:c,orderIndex:x,teacherId:h}=await e.json(),f=await d.db.select().from(p.questions).where((0,l.eq)(p.questions.id,s)).limit(1);if(0===f.length)return a.NextResponse.json({error:"Question not found"},{status:404});if(h){let e=await d.db.select({questionId:p.questions.id,quizId:p.questions.quizId,teacherId:p.courses.teacherId}).from(p.questions).leftJoin(p.quizzes,(0,l.eq)(p.questions.quizId,p.quizzes.id)).leftJoin(p.chapters,(0,l.eq)(p.quizzes.chapterId,p.chapters.id)).leftJoin(p.modules,(0,l.eq)(p.chapters.moduleId,p.modules.id)).leftJoin(p.courses,(0,l.eq)(p.modules.courseId,p.courses.id)).where((0,l.Uo)((0,l.eq)(p.questions.id,s),(0,l.eq)(p.courses.teacherId,h))).limit(1);if(0===e.length)return a.NextResponse.json({error:"Not authorized to update this question"},{status:403})}let y=await d.db.update(p.questions).set({...o&&{type:o},...n&&{question:JSON.stringify(n)},...i&&{options:JSON.stringify(i)},...void 0!==u&&{essayAnswer:""===u?null:u},...void 0!==q&&{explanation:""===q?null:q},...void 0!==c&&{points:c.toString()},...void 0!==x&&{orderIndex:x},updatedAt:new Date}).where((0,l.eq)(p.questions.id,s)).returning(),w={...y[0],question:y[0].question,options:y[0].options,essayAnswer:y[0].essayAnswer,explanation:y[0].explanation};return a.NextResponse.json({question:w,message:"Question updated successfully"})}catch(e){return console.error("Error updating question:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{id:t}=await r,s=parseInt(t);if(isNaN(s))return a.NextResponse.json({error:"Invalid question ID"},{status:400});let o=e.nextUrl.searchParams.get("teacherId"),n=await d.db.select().from(p.questions).where((0,l.eq)(p.questions.id,s)).limit(1);if(0===n.length)return a.NextResponse.json({error:"Question not found"},{status:404});if(o){let e=await d.db.select({questionId:p.questions.id,quizId:p.questions.quizId,teacherId:p.courses.teacherId}).from(p.questions).leftJoin(p.quizzes,(0,l.eq)(p.questions.quizId,p.quizzes.id)).leftJoin(p.chapters,(0,l.eq)(p.quizzes.chapterId,p.chapters.id)).leftJoin(p.modules,(0,l.eq)(p.chapters.moduleId,p.modules.id)).leftJoin(p.courses,(0,l.eq)(p.modules.courseId,p.courses.id)).where((0,l.Uo)((0,l.eq)(p.questions.id,s),(0,l.eq)(p.courses.teacherId,parseInt(o)))).limit(1);if(0===e.length)return a.NextResponse.json({error:"Not authorized to delete this question"},{status:403})}return await d.db.delete(p.questions).where((0,l.eq)(p.questions.id,s)),a.NextResponse.json({message:"Question deleted successfully"})}catch(e){return console.error("Error deleting question:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let f={...u},y="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function w(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=y?.getStore();o=e?.headers}catch{}return q.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/questions/[id]",headers:o}).apply(t,s)}})}let I=w(c,"GET"),g=w(void 0,"POST"),m=w(x,"PUT"),v=w(void 0,"PATCH"),N=w(h,"DELETE"),A=w(void 0,"HEAD"),b=w(void 0,"OPTIONS"),j=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/questions/[id]/route",pathname:"/api/questions/[id]",filename:"route",bundlePath:"app/api/questions/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\questions\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:z,workUnitAsyncStorage:R,serverHooks:E}=j;function S(){return(0,i.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:R})}},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(50912));module.exports=s})();
//# sourceMappingURL=route.js.map