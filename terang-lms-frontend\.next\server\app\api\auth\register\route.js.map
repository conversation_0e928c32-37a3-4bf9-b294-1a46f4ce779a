{"version": 3, "file": "../app/api/auth/register/route.js", "mappings": "ubAAA,gGCAA,sCCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,mXCIO,eAAeA,EAAKC,CAAoB,EAC7C,GAAI,CAEF,GAAM,WAAEC,CAAS,UAAEC,CAAQ,OAAEC,CAAK,OAAEC,CAAK,UAAEC,CAAQ,eAAEC,CAAa,cAAEC,CAAY,CAAE,CADrE,EACwEC,IADlER,EAAQS,IAAI,CAAZT,EAInB,GAAI,CAACC,GAAa,CAACC,GAAY,CAACC,CAA3BF,EAAoC,CAACI,CAAVF,CAC9B,MADwCE,CACjCK,CAD2C,CAC3CA,YAAAA,CAAaD,IAAI,CACtB,CAAEE,KAAAA,CAAO,0BAA0B,CACnC,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMC,EAAO,EAAPA,CAAUZ,EAAU,CAAC,EAAEC,EAAAA,CAAU,CAAvBD,GAOZa,CAPyBZ,MAGFa,CAAAA,EAAAA,EAAAA,EAAAA,CAAG,CAAC;yCACM,EAAEZ,EAAM,GAANA;KACtC,EAEgBa,MAAM,CAAG,EACxB,CAD2B,MACpBN,EAAAA,YAAAA,CAAaD,IAAI,CACtB,CAAEE,KAAAA,CAAO,sCAAsC,CAC/C,CAAEC,MAAAA,CAAQ,GAAI,GAMlB,IAAMK,EAAiB,MAAMC,IAAAA,EAAvBD,EAAkC,CAACZ,EADtB,IAUbc,EAAOC,CANE,CAMTD,KANeJ,CAAAA,EAAAA,EAAAA,CAH8BM,CAG9BN,CAAG,CAAC;;cAEf,EAAEF,EAAK,EAAE,EAAEV,EAAM,EAAE,CAARA,CAAUc,EAAe,YAAfA;;KAE/B,CAEmB,CAAC,EAAE,CAEtB,OAAOP,EAAAA,YAAAA,CAAaD,IAAI,CAAC,CACvBa,OAAAA,CAAS,+BACTH,IAAAA,CAAM,CACJI,EAAAA,CAAIJ,EAAKI,EAAE,CACXV,IAAAA,CAAMM,EAAKN,IAAI,CACfV,KAAAA,CAAOgB,EAAKhB,KAAK,CACjBqB,IAAAA,CAAML,EAAKK,IAAI,CACfC,UAAAA,CAAYN,EAAKM,UAAAA,EAErB,CAAG,CAAEb,MAAAA,CAAQ,GAAI,EAEnB,CAAE,MAAOD,EAAO,CAEd,EAFOA,KACPe,OAAAA,CAAQf,KAAK,CAAC,sBAAuBA,GAC9BD,EAD8BC,CAAAA,WAC9BD,CAAaD,IAAI,CACtB,CAAEE,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCtDA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,oBAAoB,SACxC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CAMxB,IAAC,EAAM,CAAH,CAAee,OAA4B,EAA7B,GAAkC,EAEnD,EAAO,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,gCACA,8BACA,iBACA,wCACA,CAAK,CACL,mJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs \"bcrypt\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/src/app/api/auth/register/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?b999", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"bcrypt\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport bcrypt from 'bcrypt';\r\nimport {sql} from '@/lib/db';\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const { firstName, lastName, email, phone, password, iaiMembership, organization } = body;\r\n\r\n    // Validate required fields\r\n    if (!firstName || !lastName || !email || !password) {\r\n      return NextResponse.json(\r\n        { error: 'Missing required fields' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Combine first and last name\r\n    const name = `${firstName} ${lastName}`;\r\n\r\n    // Check if user already exists\r\n    const existingUser = await sql`\r\n      SELECT id FROM users WHERE email = ${email}\r\n    `;\r\n\r\n    if (existingUser.length > 0) {\r\n      return NextResponse.json(\r\n        { error: 'User already exists with this email' },\r\n        { status: 409 }\r\n      );\r\n    }\r\n\r\n    // Hash the password\r\n    const saltRounds = 12;\r\n    const hashedPassword = await bcrypt.hash(password, saltRounds);\r\n\r\n    // Insert new user\r\n    const result = await sql`\r\n      INSERT INTO users (name, email, password, role)\r\n      VALUES (${name}, ${email}, ${hashedPassword}, 'student')\r\n      RETURNING id, name, email, role, created_at\r\n    `;\r\n\r\n    const user = result[0];\r\n\r\n    return NextResponse.json({\r\n      message: 'User registered successfully',\r\n      user: {\r\n        id: user.id,\r\n        name: user.name,\r\n        email: user.email,\r\n        role: user.role,\r\n        created_at: user.created_at\r\n      }\r\n    }, { status: 201 });\r\n\r\n  } catch (error) {\r\n    console.error('Registration error:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/auth/register',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\auth\\\\register\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/route\",\n        pathname: \"/api/auth/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\auth\\\\register\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["POST", "request", "firstName", "lastName", "email", "phone", "password", "iaiMembership", "organization", "body", "json", "NextResponse", "error", "status", "name", "existingUser", "sql", "length", "hashedPassword", "bcrypt", "user", "result", "saltRounds", "message", "id", "role", "created_at", "console", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}