try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b94a184f-dce7-41dc-b37f-e06cbf319c2f",e._sentryDebugIdIdentifier="sentry-dbid-b94a184f-dce7-41dc-b37f-e06cbf319c2f")}catch(e){}(()=>{var e={};e.id=7949,e.ids=[7949],e.modules={116:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(91754);function a({children:e}){return(0,r.jsx)(r.Frag<PERSON>,{children:e})}s(93491),s(76328)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5827:(e,t,s)=>{"use strict";s.d(t,{k:()=>i});var r=s(91754),a=s(93491),n=s(66536),o=s(82233);let i=a.forwardRef(({className:e,value:t,...s},a)=>(0,r.jsx)(n.bL,{ref:a,className:(0,o.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:(0,r.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));i.displayName=n.bL.displayName},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38006:(e,t,s)=>{Promise.resolve().then(s.bind(s,116))},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},51897:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(55732).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54717:(e,t,s)=>{Promise.resolve().then(s.bind(s,99641))},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66536:(e,t,s)=>{"use strict";s.d(t,{C1:()=>b,bL:()=>j});var r=s(93491),a=s(10158),n=s(90604),o=s(91754),i="Progress",[d,l]=(0,a.A)(i),[u,c]=d(i),p=r.forwardRef((e,t)=>{var s,r;let{__scopeProgress:a,value:i=null,max:d,getValueLabel:l=f,...c}=e;(d||0===d)&&!g(d)&&console.error((s=`${d}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=g(d)?d:100;null===i||v(i,p)||console.error((r=`${i}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=v(i,p)?i:null,m=y(x)?l(x,p):void 0;return(0,o.jsx)(u,{scope:a,value:x,max:p,children:(0,o.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":y(x)?x:void 0,"aria-valuetext":m,role:"progressbar","data-state":h(x,p),"data-value":x??void 0,"data-max":p,...c,ref:t})})});p.displayName=i;var x="ProgressIndicator",m=r.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,a=c(x,s);return(0,o.jsx)(n.sG.div,{"data-state":h(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...r,ref:t})});function f(e,t){return`${Math.round(e/t*100)}%`}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function g(e){return y(e)&&!isNaN(e)&&e>0}function v(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=x;var j=p,b=m},73024:e=>{"use strict";e.exports=require("node:fs")},73277:(e,t,s)=>{Promise.resolve().then(s.bind(s,97139))},73566:e=>{"use strict";e.exports=require("worker_threads")},74629:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>d});var r=s(95500),a=s(56947),n=s(26052),o=s(13636),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let d={children:["",{children:["dashboard",{children:["student",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,99641)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,97890)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},c=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/student/page",pathname:"/dashboard/student",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},97139:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(91754),a=s(9260),n=s(56682),o=s(80601),i=s(5827),d=s(41867),l=s(77406),u=s(69622),c=s(51897),p=s(16041),x=s.n(p),m=s(76328),f=s(7375),h=s(93491);function y(){let[e,t]=(0,h.useState)(m.qs.getUser()),[s,p]=(0,h.useState)(!0);if(s)return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:"Loading..."});if(!e?.institutionId)return(0,r.jsx)(f.A,{userRole:"student"});let y={enrolledCourses:3,completedCourses:1,certificates:1,totalHours:24};return(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentDashboard","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Student Dashboard"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Track your learning progress and access your courses"})]}),(0,r.jsx)(x(),{href:"/dashboard/student/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(n.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"}),"Browse Courses"]})})]}),(0,r.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Enrolled Courses"}),(0,r.jsx)(d.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.enrolledCourses}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Active enrollments"})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Completed"}),(0,r.jsx)(l.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.completedCourses}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Courses completed"})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Certificates"}),(0,r.jsx)(u.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Award","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.certificates}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Earned certificates"})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Study Hours"}),(0,r.jsx)(c.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Clock","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:y.totalHours}),(0,r.jsx)("p",{className:"text-muted-foreground text-xs",children:"Total hours"})]})]})]}),(0,r.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(a.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(a.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"My Courses"}),(0,r.jsx)(a.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your current course enrollments and progress"})]}),(0,r.jsxs)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"space-y-4",children:[{id:1,name:"Introduction to Mathematics",type:"self_paced",progress:85,status:"in_progress",dueDate:"2024-12-15"},{id:2,name:"Basic Physics",type:"verified",progress:45,status:"in_progress",dueDate:"2024-11-30"},{id:3,name:"Chemistry Fundamentals",type:"self_paced",progress:100,status:"completed",dueDate:"2024-10-20"}].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between rounded-lg border p-4",children:[(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(o.E,{variant:"verified"===e.type?"default":"secondary",children:e.type}),(0,r.jsx)(o.E,{variant:"completed"===e.status?"default":"outline",children:e.status})]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsxs)("span",{children:[e.progress,"%"]})]}),(0,r.jsx)(i.k,{value:e.progress,className:"h-2"})]}),(0,r.jsxs)("p",{className:"text-muted-foreground text-xs",children:["Due: ",new Date(e.dueDate).toLocaleDateString()]})]}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)(x(),{href:`/dashboard/student/courses/${e.id}`,children:(0,r.jsx)(n.$,{variant:"outline",size:"sm",children:"completed"===e.status?"Review":"Continue"})})})]},e.id))}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(x(),{href:"/dashboard/student/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(n.$,{variant:"outline",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"View All Courses"})})})]})]})]})}},97890:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>c,generateMetadata:()=>u,generateViewport:()=>p});var a=s(63033),n=s(1472),o=s(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof i?new Proxy(i,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student",componentType:"Layout",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):i;let u=void 0,c=void 0,p=void 0,x=r},98254:(e,t,s)=>{Promise.resolve().then(s.bind(s,97890))},99641:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>x,generateImageMetadata:()=>c,generateMetadata:()=>u,generateViewport:()=>p});var a=s(63033),n=s(1472),o=s(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof i?new Proxy(i,{apply:(e,t,s)=>{let r,a,n;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):i;let u=void 0,c=void 0,p=void 0,x=r}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,787,8134,8634,8243],()=>s(74629));module.exports=r})();
//# sourceMappingURL=page.js.map