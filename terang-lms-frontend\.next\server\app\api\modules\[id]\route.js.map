{"version": 3, "file": "../app/api/modules/[id]/route.js", "mappings": "ubAAA,4aCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAWC,EADIH,MACJG,CAASF,EAAAA,CAAAA,GAEtBG,MAAMF,GACR,KADQA,CAAAA,CACDG,CADY,CACZA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,IAAMC,EAAa,MAAMC,EAAAA,EAAAA,CACtBC,MAAM,GACNC,IAAI,CAACC,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,OAAAA,CAAQZ,EAAE,CAAEC,IACrBc,IADqBd,CAAAA,CAAAA,GAGxB,GAA0B,GAAG,CAAzBO,EAAWQ,MAAM,CACnB,CADER,MACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGxE,IAAMU,EAAeT,CAAU,CAAC,EAAE,CAG5BU,EAAiB,GAHjBD,GAGuBR,EAAAA,EAAAA,CAC1BC,CADGQ,KACG,GACNP,IAAI,CAACQ,EAAAA,QAAAA,CAAAA,CACLN,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGK,EAAAA,QAAAA,CAASlB,QAAQ,CAAEA,IAGzBmB,EAAsB,EAHGnB,CAAAA,CAAAA,EAGGoB,OAAAA,CAAQC,GAApCF,CACJF,EAAeK,GAAG,CAAC,MAAOC,EAA1BN,EACE,GADwBM,CAClBC,EADkBD,MACKf,EAAAA,EAAAA,CAC1BC,CADGe,KACG,GACNd,IAAI,CAACe,EAAAA,OAAAA,CAAAA,CACLb,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGY,EAAAA,OAAAA,CAAQC,SAAS,CAAEH,EAAQxB,EAAE,GAEzC,MAAO,CACL,GAAGwB,CAAO,CACVE,OAAAA,CAASD,CACX,CACF,IAGF,OAAOrB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBuB,MAAAA,CAAQ,CACN,GAAGX,CAAY,CACfE,QAAAA,CAAUC,CACZ,CACF,EACF,CAAE,MAAOd,EAAO,CAEd,EAFOA,KACPuB,OAAAA,CAAQvB,KAAK,CAAC,yBAA0BA,GACjCF,EADiCE,CAAAA,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeuB,EACpBhC,CAAoB,CACpB,CAFoBgC,QAEZ,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAE9B,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAWC,EADIH,MACJG,CAASF,EAAAA,CAAAA,GAEtBG,MAAMF,GACR,KADQA,CAAAA,CACDG,CADY,CACZA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,GAAM,MACJwB,CAAI,aACJC,CAAW,YACXC,CAAU,CACVC,WAAS,CACV,CANY,EAMTC,IANerC,EAAQO,IAAI,CAAZP,EASbsC,EAAiB,MAAM3B,EAAAA,EAAAA,CAC1BC,MAAM,GACNC,IAAI,CAACC,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,OAAAA,CAAQZ,EAAE,CAAEC,IACrBc,IADqBd,CAAAA,CACf,GAET,GAA8B,GAAG,CAA7BmC,EAAepB,MAAM,CACvB,KADEoB,EACKhC,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,GAAI2B,EAAW,CACb,IAAMG,EAAS,IAATA,EAAe5B,EAAAA,EAAAA,CAClBC,MAAM,GACNC,IAAI,CAAC2B,EAAAA,OAAAA,CAAAA,CACLzB,KAAK,CACJ0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEzB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGwB,EAAAA,OAAAA,CAAQtC,EAAE,CAAEoC,CAAc,CAAC,EAAE,CAACI,QAAQ,EACzC1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGwB,EAAAA,OAAAA,CAAQJ,SAAS,CAAEA,KAGzBnB,IAHyBmB,CAGpB,CAAC,CAHmBA,EAK5B,GAAIG,GAAqB,GAArBA,MAAa,CACf,OAAOjC,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,uCAAuC,CAChD,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,IAAMkC,EAAgB,MAAMhC,EAAAA,EAAAA,CACzBiC,MAAM,CAAC9B,EAAAA,OAAAA,CAAAA,CACP+B,GAAG,CAAC,CACH,GAAIZ,GAAQ,CAARA,KAAUA,EAAM,CACpB,GAAIC,GAAe,QAAfA,KAAiBA,EAAa,CAClC,GAAmBY,SAAfX,GAA4B,CAAEA,aAAY,CAC9CY,SAAAA,CAAW,IAAIC,IAAAA,GAEhBjC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,OAAOA,CAACZ,EAAE,CAAEC,IACrB8C,SAAS,GAEZ,OAAO3C,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBuB,MAAAA,CAAQa,CAAa,CAAC,EAAE,CACxBO,OAAAA,CAAS,6BACX,EACF,CAAE,MAAO1C,EAAO,CAEd,EAFOA,KACPuB,OAAAA,CAAQvB,KAAK,CAAC,yBAA0BA,GACjCF,EADiCE,CAAAA,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAe0C,EACpBnD,CAAoB,CACpB,CAAEC,GAFkBkD,KAEZ,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEjD,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAWC,EADIH,MACJG,CAASF,EAAAA,CAAAA,GAEtBG,MAAMF,GACR,KADQA,CAAAA,CACDG,CADY,CACZA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,IAAM2B,EADepC,EAAQoD,KACvBhB,EAD8B,CAACiB,EACnBA,UAD+B,CAClBC,GAAG,CAAC,aAG7BhB,EAAiB,MAAM3B,EAAAA,EAAAA,CAC1BC,MAAM,GACNC,IAAI,CAACC,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,OAAAA,CAAQZ,EAAE,CAAEC,IACrBc,IADqBd,CAAAA,CAAAA,GAGxB,GAA8B,GAAG,CAA7BmC,EAAepB,MAAM,CACvB,KADEoB,EACKhC,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,GAAI2B,EAAW,CACb,IAAMG,EADJH,IACIG,EAAe5B,EAAAA,EAAAA,CAClBC,MAAM,GACNC,IAAI,CAAC2B,EAAAA,OAAAA,CAAAA,CACLzB,KAAK,CACJ0B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEzB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGwB,EAAAA,OAAAA,CAAQtC,EAAE,CAAEoC,CAAc,CAAC,EAAE,CAACI,QAAQ,EACzC1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGwB,EAAAA,OAAAA,CAAQJ,SAAS,CAAEhC,QAAAA,CAASgC,MAGlCnB,GAHkCmB,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAIG,GAAqB,GAArBA,MAAa,CACf,OAAOjC,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,uCAAuC,CAChD,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CASA,IAAK,IAAMiB,KALY,MAKDN,EALOT,EAAAA,CAC1BC,MAAM,CAAC,CAAEV,CAIUkB,CAAgB,CAJtBC,EAAAA,QAAQA,CAACnB,EAAAA,CAAG,EACzBW,IAAI,CAACQ,EAAAA,QAAAA,CAAAA,CACLN,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGK,EAAAA,QAAAA,CAASlB,QAAQ,CAAEA,GAAAA,CAAAA,CAG7B,GAH6BA,GAGvBQ,EAAAA,EAAAA,CAAG4C,MAAM,CAAC3B,EAAAA,OAAAA,CAAAA,CAASb,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGY,EAAAA,OAAAA,CAAQC,SAAS,CAAEH,EAAQxB,EAAE,GAAVwB,OAIvD,MAAMf,EAAAA,EAAAA,CAAG4C,MAAM,CAAClC,EAAAA,QAAAA,CAAAA,CAAUN,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGK,EAAAA,QAAAA,CAASlB,QAAQ,CAAEA,IAGtD,IAHsDA,CAAAA,CAAAA,EAGhDQ,EAAAA,CAAG4C,MAAM,CAACzC,EAAAA,OAAAA,CAAAA,CAASC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,OAAAA,CAAQZ,EAAE,CAAEC,IAEvCG,EAAAA,EAFuCH,CAAAA,CAAAA,QAEvCG,CAAaC,IAAI,CAAC,CAAE2C,OAAAA,CAAS,6BAA8B,EACpE,CAAE,MAAO1C,EAAO,CAEd,EAFOA,KACPuB,OAAAA,CAAQvB,KAAK,CAAC,yBAA0BA,GACjCF,EADiCE,CAAAA,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCjNA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,mBAAmB,SACvC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAe+C,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,+BACA,6BACA,iBACA,uCACA,CAAK,CACL,kJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,YC5BA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/modules/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?5b2c", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { modules, courses, chapters, quizzes } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/modules/[id] - Get a specific module with chapters\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const moduleId = parseInt(id);\r\n    \r\n    if (isNaN(moduleId)) {\r\n      return NextResponse.json({ error: 'Invalid module ID' }, { status: 400 });\r\n    }\r\n\r\n    // Get module\r\n    const moduleData = await db\r\n      .select()\r\n      .from(modules)\r\n      .where(eq(modules.id, moduleId))\r\n      .limit(1);\r\n\r\n    if (moduleData.length === 0) {\r\n      return NextResponse.json({ error: 'Module not found' }, { status: 404 });\r\n    }\r\n\r\n    const moduleRecord = moduleData[0];\r\n\r\n    // Get chapters for this module\r\n    const moduleChapters = await db\r\n      .select()\r\n      .from(chapters)\r\n      .where(eq(chapters.moduleId, moduleId));\r\n\r\n    // Get quizzes for each chapter\r\n    const chaptersWithQuizzes = await Promise.all(\r\n      moduleChapters.map(async (chapter) => {\r\n        const chapterQuizzes = await db\r\n          .select()\r\n          .from(quizzes)\r\n          .where(eq(quizzes.chapterId, chapter.id));\r\n\r\n        return {\r\n          ...chapter,\r\n          quizzes: chapterQuizzes\r\n        };\r\n      })\r\n    );\r\n\r\n    return NextResponse.json({\r\n      module: {\r\n        ...moduleRecord,\r\n        chapters: chaptersWithQuizzes\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching module:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/modules/[id] - Update a module\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const moduleId = parseInt(id);\r\n    \r\n    if (isNaN(moduleId)) {\r\n      return NextResponse.json({ error: 'Invalid module ID' }, { status: 400 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      orderIndex,\r\n      teacherId\r\n    } = body;\r\n\r\n    // Check if module exists\r\n    const existingModule = await db\r\n      .select()\r\n      .from(modules)\r\n      .where(eq(modules.id, moduleId))\r\n      .limit(1);\r\n\r\n    if (existingModule.length === 0) {\r\n      return NextResponse.json({ error: 'Module not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to update this module\r\n    if (teacherId) {\r\n      const course = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.id, existingModule[0].courseId),\r\n            eq(courses.teacherId, teacherId)\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (course.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to update this module' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Update the module\r\n    const updatedModule = await db\r\n      .update(modules)\r\n      .set({\r\n        ...(name && { name }),\r\n        ...(description && { description }),\r\n        ...(orderIndex !== undefined && { orderIndex }),\r\n        updatedAt: new Date()\r\n      })\r\n      .where(eq(modules.id, moduleId))\r\n      .returning();\r\n\r\n    return NextResponse.json({\r\n      module: updatedModule[0],\r\n      message: 'Module updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating module:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/modules/[id] - Delete a module\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const moduleId = parseInt(id);\r\n    \r\n    if (isNaN(moduleId)) {\r\n      return NextResponse.json({ error: 'Invalid module ID' }, { status: 400 });\r\n    }\r\n\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n\r\n    // Check if module exists\r\n    const existingModule = await db\r\n      .select()\r\n      .from(modules)\r\n      .where(eq(modules.id, moduleId))\r\n      .limit(1);\r\n\r\n    if (existingModule.length === 0) {\r\n      return NextResponse.json({ error: 'Module not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to delete this module\r\n    if (teacherId) {\r\n      const course = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.id, existingModule[0].courseId),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (course.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to delete this module' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Delete related data in correct order\r\n    // 1. Delete quizzes first\r\n    const moduleChapters = await db\r\n      .select({ id: chapters.id })\r\n      .from(chapters)\r\n      .where(eq(chapters.moduleId, moduleId));\r\n\r\n    for (const chapter of moduleChapters) {\r\n      await db.delete(quizzes).where(eq(quizzes.chapterId, chapter.id));\r\n    }\r\n\r\n    // 2. Delete chapters\r\n    await db.delete(chapters).where(eq(chapters.moduleId, moduleId));\r\n\r\n    // 3. Finally delete the module\r\n    await db.delete(modules).where(eq(modules.id, moduleId));\r\n\r\n    return NextResponse.json({ message: 'Module deleted successfully' });\r\n  } catch (error) {\r\n    console.error('Error deleting module:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/modules/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\modules\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/modules/[id]/route\",\n        pathname: \"/api/modules/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/modules/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\modules\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "moduleId", "parseInt", "isNaN", "NextResponse", "json", "error", "status", "moduleData", "db", "select", "from", "modules", "where", "eq", "limit", "length", "moduleRecord", "moduleChapters", "chapters", "chaptersWithQuizzes", "Promise", "all", "map", "chapter", "chapterQuizzes", "quizzes", "chapterId", "module", "console", "PUT", "name", "description", "orderIndex", "teacherId", "body", "existingModule", "course", "courses", "and", "courseId", "updatedModule", "update", "set", "undefined", "updatedAt", "Date", "returning", "message", "DELETE", "nextUrl", "searchParams", "get", "delete", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}