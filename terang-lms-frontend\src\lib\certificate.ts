// Certificate generation utilities
// In a real implementation, you might use libraries like jsPDF, canvas, or server-side PDF generation

export interface CertificateData {
  studentName: string;
  courseName: string;
  courseCode: string;
  completionDate: string;
  finalScore: number;
  instructorName: string;
  institutionName: string;
  certificateId: string;
}

export const generateCertificateId = (): string => {
  const year = new Date().getFullYear();
  const randomNum = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, '0');
  return `CERT-${year}-${randomNum}`;
};

export const generateCertificateHTML = (data: CertificateData): string => {
  return `
  <!DOCTYPE html>
  <html lang="id">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Serti<PERSON>kat Kelulusan</title>
      <!-- Font -->
      <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
      <style>
          :root{
              --primary:#4a90e2;
              --accent:#a370e8;
              --text:#2c3e50;
              --muted:#7f8c8d;
              --light:#f0f4f8;
              --white:#ffffff;
          }
          *{box-sizing:border-box;margin:0;padding:0}
          body{
              background:linear-gradient(135deg,var(--light),#e2e8f0);
              font-family:'Montserrat',sans-serif;
              display:flex;
              align-items:center;
              justify-content:center;
              min-height:100vh;
              padding:20px;
          }
          .certificate{
              width:100%;
              max-width:900px;
              background:var(--white);
              border-radius:16px;
              box-shadow:0 20px 40px rgba(0,0,0,.08);
              position:relative;
              overflow:hidden;
              padding:80px 80px 110px;
          }
          .certificate::before,
          .certificate::after{
              content:'';
              position:absolute;
              width:300px;
              height:300px;
              border-radius:50%;
              opacity:.05;
              z-index:0;
          }
          .certificate::before{top:-80px;left:-80px;background:radial-gradient(var(--primary),transparent 70%)}
          .certificate::after{bottom:-80px;right:-80px;background:radial-gradient(var(--accent),transparent 70%)}

          .watermark{
              position:absolute;
              top:50%;left:50%;
              transform:translate(-50%,-50%) rotate(-45deg);
              font-family:'Playfair Display',serif;
              font-size:150px;
              color:rgba(0,0,0,.03);
              font-weight:700;
              pointer-events:none;
              z-index:0;
          }

          .header{text-align:center;margin-bottom:50px}
          .title{
              font-family:'Playfair Display',serif;
              font-size:44px;
              color:var(--text);
              margin:0;
          }
          .subtitle{
              font-size:16px;
              color:var(--muted);
              margin-top:8px;
          }

          .main-content{
              text-align:center;
              margin-bottom:60px;
          }
          .awarded-to{
              font-size:16px;
              color:var(--muted);
              margin-bottom:8px;
          }
          .student-name{
              font-family:'Playfair Display',serif;
              font-size:42px;
              color:var(--text);
              position:relative;
              display:inline-block;
              margin-bottom:20px;
          }
          .student-name::after{
              content:'';
              position:absolute;
              left:50%;
              bottom:-6px;
              transform:translateX(-50%);
              width:80%;
              height:3px;
              background:linear-gradient(90deg,var(--primary),var(--accent));
              border-radius:2px;
          }
          .completion-text{
              font-size:18px;
              color:#555;
              line-height:1.6;
              max-width:600px;
              margin:0 auto 25px;
          }
          .course-details{
              display:inline-block;
              background:var(--light);
              border-radius:12px;
              padding:20px 35px;
              box-shadow:0 4px 15px rgba(0,0,0,.05);
              margin-bottom:25px;
          }
          .course-name{
              font-size:24px;
              font-weight:600;
              color:var(--text);
              margin:0;
          }
          .course-code{
              font-size:15px;
              color:var(--muted);
              margin-top:4px;
          }
          .score{
              font-size:20px;
              font-weight:700;
              color:var(--primary);
          }

          .footer{
              display:flex;
              justify-content:space-around;
              align-items:flex-end;
              border-top:1px solid #ecf0f1;
              padding-top:30px;
          }
          .signature-section{
              text-align:center;
              flex:1;
          }
          .signature-line{
              width:180px;
              height:1px;
              background:var(--muted);
              margin:0 auto 8px;
          }
          .signature-label{
              font-size:14px;
              color:var(--muted);
              line-height:1.4;
          }

          .id-date-row{
              margin-top:30px;
              display:flex;
              justify-content:space-between;
              font-size:13px;
              color:#95a5a6;
          }
      </style>
  </head>
  <body>
      <div class="certificate">
          <div class="watermark">TERANG</div>

          <!-- Konten utama -->
          <div class="header">
              <h1 class="title">Sertifikat Kelulusan</h1>
              <p class="subtitle">${data.institutionName}</p>
          </div>

          <div class="main-content">
              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>
              <h2 class="student-name">${data.studentName}</h2>
              <p class="completion-text">
                  karena telah berhasil menyelesaikan dan lulus dari program
              </p>

              <div class="course-details">
                  <h3 class="course-name">${data.courseName}</h3>
                  <div class="course-code">Kode Kursus: ${data.courseCode}</div>
              </div>

              <p class="score">Nilai Akhir: ${data.finalScore}%</p>
          </div>

          <div class="footer">
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">${data.instructorName}<br>Instruktur Kursus</p>
              </div>
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">Tanggal Kelulusan<br>${data.completionDate}</p>
              </div>
          </div>

          <div class="id-date-row">
              <span>ID Sertifikat: ${data.certificateId}</span>
              <span>Diterbitkan pada: ${data.completionDate}</span>
          </div>
      </div>
  </body>
  </html>
  `;
};

// Modal-friendly version (just the content without html/body tags)
export const generateCertificateModalHTML = (data: CertificateData): string => {
  return `
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
  <style>
      .certificate-modal-container {
          --primary:#4a90e2;
          --accent:#a370e8;
          --text:#2c3e50;
          --muted:#7f8c8d;
          --light:#f0f4f8;
          --white:#ffffff;
          font-family:'Montserrat',sans-serif;
          width: 100%;
          min-height: 600px;
          background: var(--white);
          position: relative;
      }
      .certificate-modal{
          width:100%;
          background:var(--white);
          border-radius:16px;
          box-shadow:0 20px 40px rgba(0,0,0,.08);
          position:relative;
          overflow:hidden;
          padding:60px 60px 80px;
          margin: 0;
      }
      .certificate-modal *{box-sizing:border-box;}
      
      .certificate-modal::before,
      .certificate-modal::after{
          content:'';
          position:absolute;
          width:250px;
          height:250px;
          border-radius:50%;
          opacity:.05;
          z-index:0;
      }
      .certificate-modal::before{top:-60px;left:-60px;background:radial-gradient(var(--primary),transparent 70%)}
      .certificate-modal::after{bottom:-60px;right:-60px;background:radial-gradient(var(--accent),transparent 70%)}

      .certificate-modal .watermark{
          position:absolute;
          top:50%;left:50%;
          transform:translate(-50%,-50%) rotate(-45deg);
          font-family:'Playfair Display',serif;
          font-size:100px;
          color:rgba(0,0,0,.03);
          font-weight:700;
          pointer-events:none;
          z-index:0;
      }

      .certificate-modal .header{text-align:center;margin-bottom:40px;position:relative;z-index:1;}
      .certificate-modal .title{
          font-family:'Playfair Display',serif;
          font-size:32px;
          color:var(--text);
          margin:0;
      }
      .certificate-modal .subtitle{
          font-size:14px;
          color:var(--muted);
          margin-top:8px;
      }

      .certificate-modal .main-content{
          text-align:center;
          margin-bottom:50px;
          position:relative;
          z-index:1;
      }
      .certificate-modal .awarded-to{
          font-size:14px;
          color:var(--muted);
          margin-bottom:8px;
      }
      .certificate-modal .student-name{
          font-family:'Playfair Display',serif;
          font-size:28px;
          color:var(--text);
          position:relative;
          display:inline-block;
          margin-bottom:20px;
      }
      .certificate-modal .student-name::after{
          content:'';
          position:absolute;
          left:50%;
          bottom:-6px;
          transform:translateX(-50%);
          width:80%;
          height:3px;
          background:linear-gradient(90deg,var(--primary),var(--accent));
          border-radius:2px;
      }
      .certificate-modal .completion-text{
          font-size:15px;
          color:#555;
          line-height:1.6;
          max-width:500px;
          margin:0 auto 20px;
      }
      .certificate-modal .course-details{
          display:inline-block;
          background:var(--light);
          border-radius:12px;
          padding:16px 30px;
          box-shadow:0 4px 15px rgba(0,0,0,.05);
          margin-bottom:20px;
      }
      .certificate-modal .course-name{
          font-size:18px;
          font-weight:600;
          color:var(--text);
          margin:0;
      }
      .certificate-modal .course-code{
          font-size:13px;
          color:var(--muted);
          margin-top:4px;
      }
      .certificate-modal .score{
          font-size:16px;
          font-weight:700;
          color:var(--primary);
      }

      .certificate-modal .footer{
          display:flex;
          justify-content:space-around;
          align-items:flex-end;
          border-top:1px solid #ecf0f1;
          padding-top:25px;
          position:relative;
          z-index:1;
      }
      .certificate-modal .signature-section{
          text-align:center;
          flex:1;
      }
      .certificate-modal .signature-line{
          width:140px;
          height:1px;
          background:var(--muted);
          margin:0 auto 8px;
      }
      .certificate-modal .signature-label{
          font-size:12px;
          color:var(--muted);
          line-height:1.4;
      }

      .certificate-modal .id-date-row{
          margin-top:25px;
          display:flex;
          justify-content:space-between;
          font-size:11px;
          color:#95a5a6;
          position:relative;
          z-index:1;
      }
  </style>
  
  <div class="certificate-modal-container">
      <div class="certificate-modal">
          <div class="watermark">TERANG</div>

          <div class="header">
              <h1 class="title">Sertifikat Kelulusan</h1>
              <p class="subtitle">${data.institutionName}</p>
          </div>

          <div class="main-content">
              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>
              <h2 class="student-name">${data.studentName}</h2>
              <p class="completion-text">
                  karena telah berhasil menyelesaikan dan lulus dari program
              </p>

              <div class="course-details">
                  <h3 class="course-name">${data.courseName}</h3>
                  <div class="course-code">Kode Kursus: ${data.courseCode}</div>
              </div>

              <p class="score">Nilai Akhir: ${data.finalScore}%</p>
          </div>

          <div class="footer">
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">${data.instructorName}<br>Instruktur Kursus</p>
              </div>
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">Tanggal Kelulusan<br>${data.completionDate}</p>
              </div>
          </div>

          <div class="id-date-row">
              <span>ID Sertifikat: ${data.certificateId}</span>
              <span>Diterbitkan pada: ${data.completionDate}</span>
          </div>
      </div>
  </div>
  `;
};

export const downloadCertificateAsPDF = async (
  data: CertificateData
): Promise<void> => {
  try {
    const htmlContent = generateCertificateHTML(data);
    
    // Call our API endpoint to generate PDF using puppeteer-service
    const response = await fetch('/api/certificates', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ htmlContent }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate PDF');
    }

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `certificate-${data.certificateId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error generating PDF:', error);
    // Fallback to downloading as HTML if PDF generation fails
    const htmlContent = generateCertificateHTML(data);
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `certificate-${data.certificateId}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

export const previewCertificate = (data: CertificateData): void => {
  const htmlContent = generateCertificateHTML(data);
  const newWindow = window.open('', '_blank');
  if (newWindow) {
    newWindow.document.write(htmlContent);
    newWindow.document.close();
  }
};

export const shareCertificate = async (
  data: CertificateData
): Promise<void> => {
  if (navigator.share) {
    try {
      await navigator.share({
        title: `Certificate of Completion - ${data.courseName}`,
        text: `I've completed ${data.courseName} with a score of ${data.finalScore}%!`,
        url: window.location.href
      });
    } catch (error) {
      console.error('Error sharing certificate:', error);
      // Fallback to copying to clipboard
      copyToClipboard(
        `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`
      );
    }
  } else {
    // Fallback for browsers that don't support Web Share API
    copyToClipboard(
      `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`
    );
  }
};

const copyToClipboard = (text: string): void => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      // You could show a toast notification here
      console.log('Certificate details copied to clipboard');
    })
    .catch((error) => {
      console.error('Failed to copy to clipboard:', error);
    });
};
