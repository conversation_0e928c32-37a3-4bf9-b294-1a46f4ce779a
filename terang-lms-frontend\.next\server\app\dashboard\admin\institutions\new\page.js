try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="646ced04-b2af-48d4-af8c-cfb6013b7deb",e._sentryDebugIdIdentifier="sentry-dbid-646ced04-b2af-48d4-af8c-cfb6013b7deb")}catch(e){}(()=>{var e={};e.id=3148,e.ids=[3148],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21424:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(91754),a=s(93491),n=s(21372),i=s(9260),l=s(56682),o=s(59672),d=s(21626),c=s(69122),u=s(4978),p=s(73562),m=s(16041),x=s.n(m),h=s(15852),y=s(40254);function g(){let e=(0,n.useRouter)(),{toast:t}=(0,y.d)(),[s,m]=(0,a.useState)(!1),[g,f]=(0,a.useState)({name:"",type:"",subscriptionPlan:"basic",billingCycle:"monthly",studentCount:0,teacherCount:0,paymentStatus:"unpaid"}),b=async s=>{s.preventDefault(),m(!0);try{let s=await fetch("/api/institutions",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(g)}),r=await s.json();r.success?(t({title:"Success",description:"Institution created successfully"}),e.push("/dashboard/admin/institutions")):t({title:"Error",description:r.error||"Failed to create institution",variant:"destructive"})}catch(e){console.error("Error creating institution:",e),t({title:"Error",description:"Failed to create institution",variant:"destructive"})}finally{m(!1)}},v=(e,t)=>{f(s=>({...s,[e]:t}))};return(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewInstitutionPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(x(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Add New Institution"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Create a new educational institution on the platform"})]})]}),(0,r.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Institution Details"}),(0,r.jsx)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Enter the basic information for the new institution"})]}),(0,r.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Name"}),(0,r.jsx)(o.p,{id:"name",value:g.name,onChange:e=>v("name",e.target.value),placeholder:"Enter institution name",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"type","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Type"}),(0,r.jsxs)(c.l6,{value:g.type,onValueChange:e=>v("type",e),required:!0,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(c.yv,{placeholder:"Select institution type","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsx)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:h.g0.map(e=>(0,r.jsx)(c.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"subscriptionPlan","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Subscription Plan"}),(0,r.jsxs)(c.l6,{value:g.subscriptionPlan,onValueChange:e=>v("subscriptionPlan",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(c.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsx)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:Object.entries(h.Ap).map(([e,t])=>(0,r.jsxs)(c.eb,{value:e,children:[t.name," - Rp"," ",t.pricePerStudent.monthly.toLocaleString(),"/student/month"]},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"billingCycle","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Billing Cycle"}),(0,r.jsxs)(c.l6,{value:g.billingCycle,onValueChange:e=>v("billingCycle",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(c.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsxs)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.eb,{value:"monthly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Monthly"}),(0,r.jsx)(c.eb,{value:"yearly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Yearly (25% discount)"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"studentCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Students"}),(0,r.jsx)(o.p,{id:"studentCount",type:"number",value:g.studentCount,onChange:e=>v("studentCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"teacherCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Teachers"}),(0,r.jsx)(o.p,{id:"teacherCount",type:"number",value:g.teacherCount,onChange:e=>v("teacherCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"paymentStatus","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,r.jsxs)(c.l6,{value:g.paymentStatus,onValueChange:e=>v("paymentStatus",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(c.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsxs)(c.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(c.eb,{value:"paid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Paid"}),(0,r.jsx)(c.eb,{value:"unpaid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Unpaid"})]})]})]})]}),g.subscriptionPlan&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsxs)(i.ZB,{className:"text-lg",children:[h.Ap[g.subscriptionPlan].name," ","Plan Preview"]})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-semibold",children:"Features:"}),(0,r.jsx)("ul",{className:"space-y-1 text-sm",children:h.Ap[g.subscriptionPlan].features.slice(0,5).map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-2 h-2 w-2 rounded-full bg-green-500"}),e]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-semibold",children:"Pricing:"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsxs)("p",{children:["Monthly: Rp"," ",h.Ap[g.subscriptionPlan].pricePerStudent.monthly.toLocaleString(),"/student"]}),(0,r.jsxs)("p",{children:["Yearly: Rp"," ",h.Ap[g.subscriptionPlan].pricePerStudent.yearly.toLocaleString(),"/student"]}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Student Range:"," ",h.Ap[g.subscriptionPlan].minStudents," ","-"," ",h.Ap[g.subscriptionPlan].maxStudents]})]})]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(x(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(l.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,r.jsxs)(l.$,{type:"submit",disabled:s,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),s?"Creating...":"Create Institution"]})]})]})})]})]})}},21820:e=>{"use strict";e.exports=require("os")},23152:(e,t,s)=>{Promise.resolve().then(s.bind(s,21424))},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32781:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=s(95500),a=s(56947),n=s(26052),i=s(13636),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let o={children:["",{children:["dashboard",{children:["admin",{children:["institutions",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61378)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,28782)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\new\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/institutions/new/page",pathname:"/dashboard/admin/institutions/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},61378:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=s(63033),n=s(1472),i=s(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\new\\page.tsx","default");let o={...a},d="workUnitAsyncStorage"in o?o.workUnitAsyncStorage:"requestAsyncStorage"in o?o.requestAsyncStorage:void 0;r="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let r,a,n;try{let e=d?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin/institutions/new",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):l;let c=void 0,u=void 0,p=void 0,m=r},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81360:(e,t,s)=>{Promise.resolve().then(s.bind(s,61378))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8134,8634,99],()=>s(32781));module.exports=r})();
//# sourceMappingURL=page.js.map