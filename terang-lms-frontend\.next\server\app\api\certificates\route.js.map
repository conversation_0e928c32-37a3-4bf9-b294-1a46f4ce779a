{"version": 3, "file": "../app/api/certificates/route.js", "mappings": "6bA6BgB,kBAAoF,wTC1B7F,eAAeA,EAAKC,CAAgB,EACzC,GAAI,CAEF,GAAM,aAAEC,CAAW,CAAE,CADR,EACWC,IADLF,EAAIG,CAAJH,GAAQ,GAI3B,GAAI,CAACC,EACH,OAAO,EADJA,EAAa,QACLG,CACTC,IAAAA,CAAKC,SAAS,CAAC,CAAEC,KAAAA,CAAO,2BAA2B,EACnD,CAAEC,MAAAA,CAAQ,IAAKC,OAAAA,CAAS,CAAE,eAAgB,kBAAmB,CAAE,GAMnE,IAAMC,EAAwBC,OAAAA,CAAQC,GAAG,CAACF,OAApCA,cAAyD,EAAI,wBAE7DG,EAAW,MAAMC,KAAAA,CAAM,GAAGJ,EAAsB,IAAI,CAAC,CAAE,CAC3DK,MAAAA,CAAQ,KADsBL,EAE9BD,OAAAA,CAAS,CACP,eAAgB,kBAClB,EACAP,IAAAA,CAAMG,IAAAA,CAAKC,SAAS,CAAC,CACnBU,IAAAA,CAAMf,EACNgB,MAAAA,CAAQ,EADFhB,GAENiB,SAAAA,EAAW,CACb,EACF,GAEA,GAAI,CAACL,EAASM,EAAE,CAAE,CAChB,EADGN,EACGO,EAAY,MAAMP,CAAlBO,CAA2BjB,IAAI,EAAbU,CACxB,OAAO,IAAIT,QAAAA,CACTC,IAAAA,CAAKC,SAAS,CAAC,CAAEC,KAAAA,CAAO,yBAA0Bc,OAAAA,CAASD,EAAU,EACrE,CAAEZ,MAAAA,CAAQK,EAASL,MAAM,CAAEC,OAAAA,CAAS,CAAE,eAAgB,kBAAmB,CAAE,EAE/E,CAEA,IAAMa,EAAY,MAAMT,CAAlBS,CAA2BC,MAATV,KAAoB,GAE5C,OAAO,IAAIT,SAASkB,EAAW,CAC7Bd,MADkBc,CACV,IACRb,OAAAA,CAAS,CACP,eAAgB,kBAChB,sBAAuB,wCACzB,CACF,EACF,CAAE,MAAOF,EAAO,CAEd,EAFOA,KACPiB,OAAAA,CAAQjB,KAAK,CAAC,wBAAyBA,GAChC,EADgCA,CAAAA,CAC5BH,QAAAA,CACTC,IAAAA,CAAKC,SAAS,CAAC,CAAEC,KAAAA,CAAO,wBAAyBc,OAAAA,CAAUd,EAAgBkB,OAAAA,CAAQ,EACnF,CAAEjB,MAAAA,CAAQ,IAAKC,OAAAA,CAAS,CAAE,eAAgB,kBAAmB,CAAE,EAEnE,CACF,CC/CA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAC3B,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACpC,KAAM,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,mBAAmB,SACvC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,MAAeiB,EAA4B,EAA7B,GAAkC,EAEnD,EAAO,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAYC,OAA6B,EAAH,IAAS,EAEtD,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,+BACA,6BACA,iBACA,uCACA,CAAK,CACL,iJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,yBC5BA,mDCAA,sGCAA,mECAA,oDCAA,iDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,2CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,sDCAA,8GCAA,qDCAA,4DCAA,wDCAA,gECAA,wDCAA,kECAA,kDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js", "webpack://terang-lms-ui/src/app/api/certificates/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?2d25", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["\"use strict\";\nif (process.env.NEXT_RUNTIME === 'edge') {\n    module.exports = require('next/dist/server/route-modules/app-route/module.js');\n} else {\n    if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n        if (process.env.NODE_ENV === 'development') {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.dev.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.dev.js');\n            }\n        } else {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo-experimental.runtime.prod.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route-experimental.runtime.prod.js');\n            }\n        }\n    } else {\n        if (process.env.NODE_ENV === 'development') {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.dev.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route.runtime.dev.js');\n            }\n        } else {\n            if (process.env.TURBOPACK) {\n                module.exports = require('next/dist/compiled/next-server/app-route-turbo.runtime.prod.js');\n            } else {\n                module.exports = require('next/dist/compiled/next-server/app-route.runtime.prod.js');\n            }\n        }\n    }\n}\n\n//# sourceMappingURL=module.compiled.js.map", "import { NextRequest } from 'next/server';\r\n\r\n// PDF generation using puppeteer-service\r\nexport async function POST(req: NextRequest) {\r\n  try {\r\n    const body = await req.json();\r\n    const { htmlContent } = body;\r\n\r\n    // Validate input\r\n    if (!htmlContent) {\r\n      return new Response(\r\n        JSON.stringify({ error: 'HTML content is required' }),\r\n        { status: 400, headers: { 'Content-Type': 'application/json' } }\r\n      );\r\n    }\r\n\r\n    // Call puppeteer-service to generate PDF\r\n    // In production, this should be configured via environment variables\r\n    const PUPPETEER_SERVICE_URL = process.env.PUPPETEER_SERVICE_URL || 'http://localhost:3000';\r\n    \r\n    const response = await fetch(`${PUPPETEER_SERVICE_URL}/pdf`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        html: htmlContent,\r\n        format: 'A4',\r\n        landscape: true,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      return new Response(\r\n        JSON.stringify({ error: 'Failed to generate PDF', details: errorData }),\r\n        { status: response.status, headers: { 'Content-Type': 'application/json' } }\r\n      );\r\n    }\r\n\r\n    const pdfBuffer = await response.arrayBuffer();\r\n    \r\n    return new Response(pdfBuffer, {\r\n      status: 200,\r\n      headers: {\r\n        'Content-Type': 'application/pdf',\r\n        'Content-Disposition': 'attachment; filename=\"certificate.pdf\"',\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error('Error generating PDF:', error);\r\n    return new Response(\r\n      JSON.stringify({ error: 'Internal server error', details: (error as Error).message }),\r\n      { status: 500, headers: { 'Content-Type': 'application/json' } }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/certificates',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\certificates\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/certificates/route\",\n        pathname: \"/api/certificates\",\n        filename: \"route\",\n        bundlePath: \"app/api/certificates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\certificates\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["POST", "req", "htmlContent", "body", "json", "Response", "JSON", "stringify", "error", "status", "headers", "PUPPETEER_SERVICE_URL", "process", "env", "response", "fetch", "method", "html", "format", "landscape", "ok", "errorData", "details", "pdfBuffer", "arrayBuffer", "console", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}