{"version": 3, "file": "../app/api/questions/route.js", "mappings": "ubAAA,gGCAA,uCCAA,oaCMO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IAAMC,EAAeD,EAAQE,KAARF,EAAe,CAA9BC,YAA2C,CAC3CE,EAASF,EAAaG,EAAtBD,CAAyB,CAAC,MAAjBF,IACTI,EAAYJ,EAAaG,GAAG,CAAC,CAA7BC,KAAYJ,OAElB,GAAI,CAACE,EACH,IADGA,EAAQ,CACJG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,GAAIJ,EAAW,CACb,IAAMK,EAAiB,MAAMC,EAAAA,EAAAA,CAC1BC,CADGF,KACG,CAAC,CACNP,MAAAA,CAAQU,EAAAA,OAAOA,CAACC,EAAE,CAClBC,SAAAA,CAAWF,EAAAA,OAAOA,CAACE,SAAS,CAC5BV,SAAAA,CAAWW,EAAAA,OAAOA,CAACX,SAAAA,CACrB,EACCY,IAAI,CAACJ,EAAAA,OAAAA,CAAAA,CACLK,QAAQ,CAACC,EAAAA,QAAAA,CAAUC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGP,EAAAA,OAAAA,CAAQE,SAAS,CAAEI,EAAAA,QAAAA,CAASL,EAAE,GACpDI,QAAQ,CAACG,EAAAA,OAAAA,CAASD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGD,EAAAA,QAAQA,CAACG,QAAQ,CAAED,EAAAA,OAAAA,CAAQP,EAAE,GAClDI,QAAQ,CAACF,EAAAA,OAAAA,CAASI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,OAAOA,CAACE,QAAQ,CAAEP,EAAAA,OAAOA,CAACF,EAAE,GACjDU,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEL,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGP,EAAAA,OAAOA,CAACC,EAAE,CAAEY,QAAAA,CAASvB,IACxBiB,CAAAA,CADwBjB,CAAAA,CAAAA,CACxBiB,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQX,SAAS,CAAEqB,QAAAA,CAASrB,MAGlCsB,GAHkCtB,CAAAA,CAG7B,CAAC,CAH4BA,EAKrC,GAA8B,GAAG,CAA7BK,EAAekB,MAAM,CACvB,KADElB,EACKJ,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,kCAAkC,CAC3C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CASA,IAAMoB,EAA0BC,CANV,MAAMnB,EAAAA,EAAAA,CACzBC,CAK6BkB,KALvB,GACNb,IAAI,CAACc,EAAAA,SAAAA,CAAAA,CACLP,KAAK,CAACJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,SAAAA,CAAU5B,MAAM,CAAEuB,QAAAA,CAASvB,IAAAA,CAAAA,CAGO6B,CAHP7B,EAGU,CAAC8B,GAAa,EAC7D,GAAGA,CAAQ,CACXA,QAAAA,CAAUA,EAASA,QAAQ,CAC3BC,OAAAA,CAASD,EAASC,OAAO,CACzBC,WAAAA,CAAaF,EAASE,WAAW,CACjCC,WAAAA,CAAaH,EAASG,WAAAA,CACxB,GAEA,OAAO9B,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEwB,SAAAA,CAAWF,CAAwB,EAChE,CAAE,MAAOrB,EAAO,CAEd,EAFOA,KACP6B,OAAAA,CAAQ7B,KAAK,CAAC,4BAA6BA,GACpCF,EADoCE,CAAAA,WACpCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAe6B,EAAKtC,CAAoB,EAC7C,GAAI,CAEF,GAAM,QACJG,CAAM,MACNoC,EAAO,EAAPA,eAAwB,UACxBN,CAAQ,SACRC,CAAO,aACPC,CAAW,aACXC,CAAW,QACXI,EAAS,CAAC,GAAVA,SACAC,CAAU,WACVpC,CAAS,CACV,CAXY,EAWTqC,IAAAA,EAXuBnC,IAAI,CAAZP,EAcnB,GAAI,CAACG,GAAU,CAAC8B,EACd,MADcA,CACP3B,CADiB,CACjBA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMC,EAAiB,MAAMC,EAAAA,EAAAA,CAC1BC,CADGF,KACG,CAAC,CACNP,MAAAA,CAAQU,EAAAA,OAAOA,CAACC,EAAE,CAClBC,SAAAA,CAAWF,EAAAA,OAAOA,CAACE,SAAS,CAC5BV,SAAAA,CAAWW,EAAAA,OAAOA,CAACX,SAAAA,GAEpBY,IAAI,CAACJ,EAAAA,OAAAA,CAAAA,CACLK,QAAQ,CAACC,EAAAA,QAAAA,CAAUC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACP,EAAAA,OAAAA,CAAQE,SAAS,CAAEI,EAAAA,QAAAA,CAASL,EAAE,GACpDI,QAAQ,CAACG,EAAAA,OAAAA,CAASD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGD,EAAAA,QAAQA,CAACG,QAAQ,CAAED,EAAAA,OAAAA,CAAQP,EAAE,GAClDI,QAAQ,CAACF,EAAAA,OAAOA,CAAEI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,OAAAA,CAAQE,QAAQ,CAAEP,EAAAA,OAAAA,CAAQF,EAAE,GACjDU,KAAK,CAACJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGP,EAAAA,OAAAA,CAAQC,EAAE,CAAEX,IACrBwB,EADqBxB,CAAAA,CAAAA,CAChB,CAAC,GAET,GAA8B,GAAG,CAA7BO,EAAekB,MAAM,CACvB,KADElB,EACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,iBAAiB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGtE,GAAIJ,GAAaK,CAAc,CAAC,EAAE,CAACL,SAAS,GAAKA,EAC/C,OAD+CA,EAAW,YACnDC,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,+CAA+C,CACxD,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAIkC,EAAkBF,EACtB,QADsBA,GAAlBE,CACAA,EAA+B,CACjC,EADsBC,EAChBC,EAAoB,MAAMlC,EAAAA,EAAAA,CAC7BC,IADGiC,EACG,CAAC,CAAEJ,UAAAA,CAAYV,EAAAA,SAASA,CAACU,UAAAA,CAAW,EAC1CxB,IAAI,CAACc,EAAAA,SAAAA,CAAAA,CACLP,KAAK,CAACJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,SAAAA,CAAU5B,MAAM,CAAEA,IAE9BwC,EAAkBE,CAFY1C,CAAAA,MAEY,CAAG,EACzC2C,EADJH,EACIG,CAAKC,GAAG,IAAIF,EAAkBb,GAAG,CAACgB,CAAAA,EAAKA,CAAAA,CAAEP,MAA7BI,IAAuC,EAAI,IAAM,EAC7D,CACN,CAGA,IAAMI,EAAc,MAAMtC,EAAAA,EAAAA,CACvBuC,MAAM,CAACnB,EAAAA,SAAAA,CAAAA,CACPoB,MAAM,CAAC,QACNhD,MAAAA,CACAoC,EACAN,EADAM,MACAN,CAAUmB,IAAAA,CAAKC,SAAS,CAACpB,GACzBC,KADyBD,CAAAA,CACzBC,CAASA,EAAUkB,IAAAA,CAAVlB,SAAwB,CAACA,GAAW,IAAXA,CAClCC,WAAAA,CAAaA,OAAqB,IAArBA,CAA4BA,EACzCC,SADyCD,EACzCC,CAAaA,EAAcgB,IAAAA,CAAKC,IAAnBjB,KAA4B,CAACA,GAAe,KACzDI,GAD0CJ,CAAAA,EAC1CI,CAAQA,EAAOc,QAAQ,GACvBb,UAAAA,CAAYE,CACd,GACCY,SAAS,GAGNC,EAAyB,CAC7B,GAAGP,CAAW,CAAC,EAAE,CACjBhB,QAAAA,CAAUgB,CAAW,CAFjBO,EAEoB,CAACvB,QAAQ,CACjCC,OAAAA,CAASe,CAAW,CAAC,EAAE,CAACf,OAAO,CAC/BC,WAAAA,CAAac,CAAW,CAAC,EAAE,CAACd,WAAW,CACvCC,WAAAA,CAAaa,CAAW,CAAC,EAAE,CAACb,WAAAA,EAG9B,OAAO9B,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAE0B,QAAAA,CAAUuB,EAAwBC,OAAAA,CAAS,YAAjCD,oBAAiE,CAC7E,CAAE/C,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOD,EAAO,CAEd,EAFOA,KACP6B,OAAAA,CAAQ7B,KAAK,CAAC,2BAA4BA,GACnCF,EADmCE,CAAAA,WACnCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC7JA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAAI,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,gBAAgB,SACpC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CAMxB,IAAC,EAAM,CAAH,CAAeiD,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,EAAYC,CAAf,MAA6C,EAA/B,KAAsC,EAEzD,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,4BACA,0BACA,iBACA,oCACA,CAAK,CACL,8IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/src/app/api/questions/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?515f", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { questions, quizzes, chapters, modules, courses } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/questions - Get questions for a quiz\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const quizId = searchParams.get('quizId');\r\n    const teacherId = searchParams.get('teacherId');\r\n    \r\n    if (!quizId) {\r\n      return NextResponse.json({ error: 'Quiz ID required' }, { status: 400 });\r\n    }\r\n\r\n    // Verify quiz exists and teacher has access\r\n    if (teacherId) {\r\n      const quizWithCourse = await db\r\n        .select({\r\n          quizId: quizzes.id,\r\n          chapterId: quizzes.chapterId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(quizzes)\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(quizzes.id, parseInt(quizId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (quizWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Quiz not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Get questions for this quiz\r\n    const quizQuestions = await db\r\n      .select()\r\n      .from(questions)\r\n      .where(eq(questions.quizId, parseInt(quizId)));\r\n\r\n    // Parse options for each question\r\n    const questionsWithParsedData = quizQuestions.map(question => ({\r\n      ...question,\r\n      question: question.question,\r\n      options: question.options,\r\n      essayAnswer: question.essayAnswer,\r\n      explanation: question.explanation,\r\n    }));\r\n\r\n    return NextResponse.json({ questions: questionsWithParsedData });\r\n  } catch (error) {\r\n    console.error('Error fetching questions:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/questions - Create a new question\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      quizId,\r\n      type = 'multiple_choice',\r\n      question,\r\n      options,\r\n      essayAnswer, // Renamed from correctAnswer\r\n      explanation, // New field\r\n      points = 1,\r\n      orderIndex,\r\n      teacherId\r\n    } = body;\r\n\r\n    // Validate required fields\r\n    if (!quizId || !question) {\r\n      return NextResponse.json(\r\n        { error: 'Quiz ID and question are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify quiz exists and teacher has access\r\n    const quizWithCourse = await db\r\n      .select({\r\n        quizId: quizzes.id,\r\n        chapterId: quizzes.chapterId,\r\n        teacherId: courses.teacherId\r\n      })\r\n      .from(quizzes)\r\n      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n      .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n      .leftJoin(courses, eq(modules.courseId, courses.id))\r\n      .where(eq(quizzes.id, quizId))\r\n      .limit(1);\r\n\r\n    if (quizWithCourse.length === 0) {\r\n      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });\r\n    }\r\n\r\n    if (teacherId && quizWithCourse[0].teacherId !== teacherId) {\r\n      return NextResponse.json(\r\n        { error: 'Not authorized to add questions to this quiz' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // If no orderIndex provided, set it to the next available\r\n    let finalOrderIndex = orderIndex;\r\n    if (finalOrderIndex === undefined) {\r\n      const existingQuestions = await db\r\n        .select({ orderIndex: questions.orderIndex })\r\n        .from(questions)\r\n        .where(eq(questions.quizId, quizId));\r\n      \r\n      finalOrderIndex = existingQuestions.length > 0 \r\n        ? Math.max(...existingQuestions.map(q => q.orderIndex || 0)) + 1 \r\n        : 1;\r\n    }\r\n\r\n    // Create the question\r\n    const newQuestion = await db\r\n      .insert(questions)\r\n      .values({\r\n        quizId,\r\n        type,\r\n        question: JSON.stringify(question),\r\n        options: options ? JSON.stringify(options) : null,\r\n        essayAnswer: essayAnswer === '' ? null : essayAnswer,\r\n        explanation: explanation ? JSON.stringify(explanation) : null,\r\n        points: points.toString(),\r\n        orderIndex: finalOrderIndex\r\n      })\r\n      .returning();\r\n\r\n    // Parse options for response\r\n    const questionWithParsedData = {\r\n      ...newQuestion[0],\r\n      question: newQuestion[0].question,\r\n      options: newQuestion[0].options,\r\n      essayAnswer: newQuestion[0].essayAnswer,\r\n      explanation: newQuestion[0].explanation,\r\n    };\r\n\r\n    return NextResponse.json(\r\n      { question: questionWithParsedData, message: 'Question created successfully' },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating question:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/questions',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\questions\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/questions/route\",\n        pathname: \"/api/questions\",\n        filename: \"route\",\n        bundlePath: \"app/api/questions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\questions\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "searchParams", "nextUrl", "quizId", "get", "teacherId", "NextResponse", "json", "error", "status", "quizWithCourse", "db", "select", "quizzes", "id", "chapterId", "courses", "from", "leftJoin", "chapters", "eq", "modules", "moduleId", "courseId", "where", "and", "parseInt", "limit", "length", "questionsWithParsedData", "quizQuestions", "questions", "map", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "console", "POST", "type", "points", "orderIndex", "body", "finalOrderIndex", "undefined", "existingQuestions", "Math", "max", "q", "newQuestion", "insert", "values", "JSON", "stringify", "toString", "returning", "questionWithParsedData", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}