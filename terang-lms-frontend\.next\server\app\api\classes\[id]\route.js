try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="c5594ab0-cb67-455d-9fde-fa344597de77",e._sentryDebugIdIdentifier="sentry-dbid-c5594ab0-cb67-455d-9fde-fa344597de77")}catch(e){}"use strict";(()=>{var e={};e.id=2832,e.ids=[2832],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67339:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>T,routeModule:()=>A,serverHooks:()=>P,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>R});var t={};s.r(t),s.d(t,{DELETE:()=>E,GET:()=>y,HEAD:()=>b,OPTIONS:()=>N,PATCH:()=>v,POST:()=>g,PUT:()=>I});var o=s(3690),n=s(56947),a=s(75250),i=s(63033),u=s(62187),d=s(18621),c=s(32230),l=s(74683),p=s(7688);async function x(e,{params:r}){try{let{id:e}=await r,s=parseInt(e);if(isNaN(s))return u.NextResponse.json({error:"Invalid class ID"},{status:400});let t=await d.db.select({id:c.classes.id,name:c.classes.name,description:c.classes.description,institutionId:c.classes.institutionId,teacherId:c.classes.teacherId,coverPicture:c.classes.coverPicture,createdAt:c.classes.createdAt,updatedAt:c.classes.updatedAt,teacherName:c.users.name,teacherEmail:c.users.email}).from(c.classes).leftJoin(c.users,(0,l.eq)(c.classes.teacherId,c.users.id)).where((0,l.eq)(c.classes.id,s)).limit(1);if(0===t.length)return u.NextResponse.json({error:"Class not found"},{status:404});let o=await d.db.select({courseId:c.courses.id,courseName:c.courses.name,courseDescription:c.courses.description,courseCode:c.courses.courseCode,courseType:c.courses.type,enrolledAt:c.courseEnrollments.enrolledAt}).from(c.courseEnrollments).leftJoin(c.courses,(0,l.eq)(c.courseEnrollments.courseId,c.courses.id)).where((0,l.eq)(c.courseEnrollments.classId,s)),n={...t[0],enrolledCourses:o,studentCount:0,courseCount:o.length};return u.NextResponse.json({success:!0,class:n})}catch(e){return console.error("Error fetching class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{id:s}=await r,t=parseInt(s),{name:o,description:n,coverPicture:a,teacherId:i}=await e.json();if(isNaN(t))return u.NextResponse.json({error:"Invalid class ID"},{status:400});let p=await d.db.select().from(c.classes).where((0,l.eq)(c.classes.id,t)).limit(1);if(0===p.length)return u.NextResponse.json({error:"Class not found"},{status:404});if(i&&i!==p[0].teacherId){let e=await d.db.select().from(c.users).where((0,l.Uo)((0,l.eq)(c.users.id,i),(0,l.eq)(c.users.institutionId,p[0].institutionId),(0,l.eq)(c.users.role,"teacher"))).limit(1);if(0===e.length)return u.NextResponse.json({error:"Teacher not found or not authorized"},{status:403})}let x=await d.db.update(c.classes).set({name:o||p[0].name,description:n||p[0].description,coverPicture:a||p[0].coverPicture,teacherId:i||p[0].teacherId,updatedAt:new Date}).where((0,l.eq)(c.classes.id,t)).returning();return u.NextResponse.json({success:!0,class:x[0],message:"Class updated successfully"})}catch(e){return console.error("Error updating class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:r}){try{let{id:e}=await r,s=parseInt(e);if(isNaN(s))return u.NextResponse.json({error:"Invalid class ID"},{status:400});let t=await d.db.select().from(c.classes).where((0,l.eq)(c.classes.id,s)).limit(1);if(0===t.length)return u.NextResponse.json({error:"Class not found"},{status:404});return await d.db.delete(c.classEnrollments).where((0,l.eq)(c.classEnrollments.classId,s)),await d.db.delete(c.courseEnrollments).where((0,l.eq)(c.courseEnrollments.classId,s)),await d.db.delete(c.classes).where((0,l.eq)(c.classes.id,s)),u.NextResponse.json({success:!0,message:"Class deleted successfully"})}catch(e){return console.error("Error deleting class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...i},m="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function w(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let o;try{let e=m?.getStore();o=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/classes/[id]",headers:o}).apply(s,t)}})}let y=w(x,"GET"),g=w(void 0,"POST"),I=w(h,"PUT"),v=w(void 0,"PATCH"),E=w(f,"DELETE"),b=w(void 0,"HEAD"),N=w(void 0,"OPTIONS"),A=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/classes/[id]/route",pathname:"/api/classes/[id]",filename:"route",bundlePath:"app/api/classes/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\classes\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:j,workUnitAsyncStorage:R,serverHooks:P}=A;function T(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:R})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,1617,2957],()=>s(67339));module.exports=t})();
//# sourceMappingURL=route.js.map