{"version": 3, "file": "../app/api/chapters/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,qXCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,QAEZ,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEE,CAAE,CAAE,CAAG,MAAMC,EACfC,EAAYC,EADGF,MACHE,CAASH,EAAAA,CAAAA,GAEvBI,MAAMF,GACR,MADQA,CAAAA,EAAY,YACbG,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,IAAMC,EAAc,MAAMC,EAAAA,EAAAA,CACvBC,MAAM,GACNC,IAAI,CAACC,EAAAA,QAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,QAAAA,CAASb,EAAE,CAAEE,IACtBc,KAAK,CADiBd,CAAAA,EAGzB,GAA2B,GAAG,CAA1BO,EAAYQ,MAAM,CACpB,EADER,KACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGzE,IAAMU,EAAUT,CAAW,CAAC,EAAE,CAGxBU,EAAiB,MAAMT,EAAAA,EAAAA,CAC1BC,CADGQ,KACG,GACNP,IAAI,CAACQ,EAAAA,OAAAA,CAAAA,CACLN,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGK,EAAAA,OAAAA,CAAQlB,SAAS,CAAEA,IAE/B,KAF+BA,CAAAA,CAAAA,EAExBG,YAAAA,CAAaC,IAAI,CAAC,CACvBY,OAAAA,CAAS,CACP,GAAGA,CAAO,CACVG,OAAAA,CAASH,EAAQG,OAAO,CACxBD,OAAAA,CAASD,CACX,CACF,EACF,CAAE,MAAOZ,EAAO,CAEd,EAFOA,KACPe,OAAAA,CAAQf,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,CADkCE,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAee,EACpBxB,CAAoB,CACpB,CAFoBwB,OAElBtB,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAED,CAAE,CAAE,CAAG,MAAMC,EACfC,EAAYC,EADGF,MACHE,CAASH,EAAAA,CAAAA,GAEvBI,MAAMF,GACR,MADQA,CAAAA,EAAY,YACbG,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,GAAM,MACJgB,CAAI,SACJH,CAAO,YACPI,CAAU,WACVC,CAAS,CACV,CANY,EAMTC,IAAAA,EANuBrB,IAAI,CAAZP,EASb6B,EAAkB,MAAMlB,EAAAA,EAAAA,CAC3BC,MAAM,GACNC,IAAI,CAACC,EAAAA,QAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,QAAAA,CAASb,EAAE,CAAEE,IACtBc,KADsBd,CAAAA,CAAAA,EAGzB,GAA+B,GAAG,CAA9B0B,EAAgBX,MAAM,CACxB,MADEW,CACKvB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,GAAIkB,EAAW,CACb,IAAMG,EAAoB,MAAMnB,EAAAA,EAAAA,CAC7BC,IADGkB,EACG,CAAC,CACN3B,SAAAA,CAAWW,EAAAA,QAAQA,CAACb,EAAE,CACtB8B,QAAAA,CAAUjB,EAAAA,QAAQA,CAACiB,QAAQ,CAC3BC,QAAAA,CAAUC,EAAAA,OAAOA,CAACD,QAAQ,CAC1BL,SAAAA,CAAWO,EAAAA,OAAOA,CAACP,SACrB,GACCd,IAAI,CAACC,EAAAA,QAAAA,CAAAA,CACLqB,QAAQ,CAACF,EAAAA,OAAOA,CAAEjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,QAAAA,CAASiB,QAAQ,CAAEE,EAAAA,OAAOA,CAAChC,EAAE,GAClDkC,QAAQ,CAACD,EAAAA,OAAOA,CAAElB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGiB,EAAAA,OAAAA,CAAQD,QAAQ,CAAEE,EAAAA,OAAOA,CAACjC,EAAE,GACjDc,KAAK,CACJqB,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,QAAAA,CAASb,EAAE,CAAEE,GAChBa,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQP,SAAS,CAAEA,KAGzBV,IAHyBU,CAAAA,CAAAA,CAAAA,EAK5B,GAAiC,GAAG,CAAhCG,EAAkBZ,MAAM,CAC1B,OAAOZ,CADLwB,CACKxB,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wCAAwC,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,IAAM4B,EAAiB,MAAM1B,EAAAA,EAAAA,CAC1B2B,MAAM,CAACxB,EAAAA,QAAAA,CAAAA,CACPyB,GAAG,CAAC,CACH,GAAId,GAAQ,CAARA,KAAUA,EAAM,CACpB,GAAIH,GAAW,CAAEA,GAAbA,IAAaA,CAASkB,IAAAA,CAAKC,SAAS,CAACnB,GAAU,CACnD,GADyCA,KACtBoB,IAAfhB,GAA4B,EAAbgB,UAAehB,EAAY,CAC9CiB,SAAAA,CAAW,IAAIC,IAAAA,GAEhB7B,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,QAAQA,CAACb,EAAE,CAAEE,IACtB0C,SAAS,GAENC,EAA2B,CAC/B,GAAGT,CAAc,CAAC,EAAE,CACpBf,OAAAA,CAASe,CAAc,CAAC,EAAE,CAACf,OAAAA,EAG7B,OAAOhB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBY,OAAAA,CAAS2B,EACTC,OAAAA,CAAS,cADAD,gBAEX,EACF,CAAE,MAAOtC,EAAO,CAEd,EAFOA,KACPe,OAAAA,CAAQf,KAAK,CAAC,0BAA2BA,GAClCF,EADkCE,CAAAA,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeuC,EACpBhD,CAAoB,CACpB,IAFoBgD,IAElB9C,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAED,CAAE,CAAE,CAAG,MAAMC,EACfC,EAAYC,EADGF,MACHE,CAASH,EAAAA,CAAAA,GAEvBI,MAAMF,GACR,MADQA,CAAAA,EAAY,YACbG,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,IAAMkB,EAAYsB,EADWC,KAARlD,EAAe,CAACiD,EACnBA,UAD+B,CAClBE,GAAG,CAAC,aAG7BtB,EAAkB,MAAMlB,EAAAA,EAAAA,CAC3BC,MAAM,GACNC,IAAI,CAACC,EAAAA,QAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,QAAAA,CAASb,EAAE,CAAEE,IACtBc,KADsBd,CAAAA,CAAAA,EAGzB,GAAI0B,GAA8B,GAAdX,MAAM,CACxB,EADEW,KACKvB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,GAAIkB,EAAW,CACb,IAAMG,EAAoB,MAAMnB,EAAAA,EAAAA,CAC7BC,IADGkB,EACG,CAAC,CACN3B,SAAAA,CAAWW,EAAAA,QAAQA,CAACb,EAAE,CACtB8B,QAAAA,CAAUjB,EAAAA,QAAQA,CAACiB,QAAQ,CAC3BC,QAAAA,CAAUC,EAAAA,OAAOA,CAACD,QAAQ,CAC1BL,SAAAA,CAAWO,EAAAA,OAAOA,CAACP,SAAAA,GAEpBd,IAAI,CAACC,EAAAA,QAAAA,CAAAA,CACLqB,QAAQ,CAACF,EAAAA,OAAOA,CAAEjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,QAAAA,CAASiB,QAAQ,CAAEE,EAAAA,OAAOA,CAAChC,EAAE,GAClDkC,QAAQ,CAACD,EAAAA,OAAAA,CAASlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiB,EAAAA,OAAAA,CAAQD,QAAQ,CAAEE,EAAAA,OAAAA,CAAQjC,EAAE,GACjDc,KAAK,CACJqB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,QAAAA,CAASb,EAAE,CAAEE,GAChBa,CAAAA,EAAAA,EAAAA,CADgBb,CAAAA,CACda,CAACkB,EAAAA,OAAAA,CAAQP,SAAS,CAAEvB,QAAAA,CAASuB,MAGlCV,GAHkCU,CAAAA,CAG7B,CAAC,CAH4BA,EAKrC,GAAiC,GAAG,CAAhCG,EAAkBZ,MAAM,CAC1B,OAAOZ,CADLwB,CACKxB,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wCAAwC,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CASA,OALA,MAAME,EAAAA,EAAAA,CAAGyC,MAAM,CAAC/B,EAAAA,OAAAA,CAAAA,CAASN,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGK,EAAAA,OAAAA,CAAQlB,SAAS,CAAEA,IAGrD,KAHqDA,CAAAA,CAAAA,CAG/CQ,EAAAA,CAAGyC,MAAM,CAACtC,EAAAA,QAAAA,CAAAA,CAAUC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,QAAAA,CAASb,EAAE,CAAEE,IAEzCG,EAAAA,GAFyCH,CAAAA,CAAAA,OAEzCG,CAAaC,IAAI,CAAC,CAAEwC,OAAAA,CAAS,8BAA+B,EACrE,CAAE,MAAOvC,EAAO,CAEd,EAFOA,KACPe,OAAAA,CAAQf,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,CADkCE,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC5MA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,oBAAoB,SACxC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAe4C,EAA4B,GAAH,EAAQ,EAEnD,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAAf,MAA2C,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,EAAYC,GAAf,IAA+C,EAAjC,OAA0C,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,gCACA,8BACA,iBACA,wCACA,CAAK,CACL,mJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/src/app/api/chapters/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?324f", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { chapters, modules, courses, quizzes } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/chapters/[id] - Get a specific chapter with quizzes\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const chapterId = parseInt(id);\r\n    \r\n    if (isNaN(chapterId)) {\r\n      return NextResponse.json({ error: 'Invalid chapter ID' }, { status: 400 });\r\n    }\r\n\r\n    // Get chapter\r\n    const chapterData = await db\r\n      .select()\r\n      .from(chapters)\r\n      .where(eq(chapters.id, chapterId))\r\n      .limit(1);\r\n\r\n    if (chapterData.length === 0) {\r\n      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 });\r\n    }\r\n\r\n    const chapter = chapterData[0];\r\n\r\n    // Get quizzes for this chapter\r\n    const chapterQuizzes = await db\r\n      .select()\r\n      .from(quizzes)\r\n      .where(eq(quizzes.chapterId, chapterId));\r\n\r\n    return NextResponse.json({\r\n      chapter: {\r\n        ...chapter,\r\n        content: chapter.content,\r\n        quizzes: chapterQuizzes\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching chapter:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/chapters/[id] - Update a chapter\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const chapterId = parseInt(id);\r\n    \r\n    if (isNaN(chapterId)) {\r\n      return NextResponse.json({ error: 'Invalid chapter ID' }, { status: 400 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      content,\r\n      orderIndex,\r\n      teacherId\r\n    } = body;\r\n\r\n    // Check if chapter exists\r\n    const existingChapter = await db\r\n      .select()\r\n      .from(chapters)\r\n      .where(eq(chapters.id, chapterId))\r\n      .limit(1);\r\n\r\n    if (existingChapter.length === 0) {\r\n      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to update this chapter\r\n    if (teacherId) {\r\n      const chapterWithCourse = await db\r\n        .select({\r\n          chapterId: chapters.id,\r\n          moduleId: chapters.moduleId,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(chapters)\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(chapters.id, chapterId),\r\n            eq(courses.teacherId, teacherId)\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (chapterWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to update this chapter' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Update the chapter\r\n    const updatedChapter = await db\r\n      .update(chapters)\r\n      .set({\r\n        ...(name && { name }),\r\n        ...(content && { content: JSON.stringify(content) }),\r\n        ...(orderIndex !== undefined && { orderIndex }),\r\n        updatedAt: new Date()\r\n      })\r\n      .where(eq(chapters.id, chapterId))\r\n      .returning();\r\n\r\n    const chapterWithParsedContent = {\r\n      ...updatedChapter[0],\r\n      content: updatedChapter[0].content,\r\n    };\r\n\r\n    return NextResponse.json({\r\n      chapter: chapterWithParsedContent,\r\n      message: 'Chapter updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating chapter:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/chapters/[id] - Delete a chapter\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const chapterId = parseInt(id);\r\n    \r\n    if (isNaN(chapterId)) {\r\n      return NextResponse.json({ error: 'Invalid chapter ID' }, { status: 400 });\r\n    }\r\n\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n\r\n    // Check if chapter exists\r\n    const existingChapter = await db\r\n      .select()\r\n      .from(chapters)\r\n      .where(eq(chapters.id, chapterId))\r\n      .limit(1);\r\n\r\n    if (existingChapter.length === 0) {\r\n      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to delete this chapter\r\n    if (teacherId) {\r\n      const chapterWithCourse = await db\r\n        .select({\r\n          chapterId: chapters.id,\r\n          moduleId: chapters.moduleId,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(chapters)\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(chapters.id, chapterId),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (chapterWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to delete this chapter' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Delete related data in correct order\r\n    // 1. Delete quizzes first\r\n    await db.delete(quizzes).where(eq(quizzes.chapterId, chapterId));\r\n\r\n    // 2. Delete the chapter\r\n    await db.delete(chapters).where(eq(chapters.id, chapterId));\r\n\r\n    return NextResponse.json({ message: 'Chapter deleted successfully' });\r\n  } catch (error) {\r\n    console.error('Error deleting chapter:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/chapters/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\chapters\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/chapters/[id]/route\",\n        pathname: \"/api/chapters/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/chapters/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\chapters\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "id", "params", "chapterId", "parseInt", "isNaN", "NextResponse", "json", "error", "status", "chapterData", "db", "select", "from", "chapters", "where", "eq", "limit", "length", "chapter", "chapterQuizzes", "quizzes", "content", "console", "PUT", "name", "orderIndex", "teacherId", "body", "existingChapter", "chapterWithCourse", "moduleId", "courseId", "modules", "courses", "leftJoin", "and", "updatedChapter", "update", "set", "JSON", "stringify", "undefined", "updatedAt", "Date", "returning", "chapterWithParsedContent", "message", "DELETE", "searchParams", "nextUrl", "get", "delete", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}