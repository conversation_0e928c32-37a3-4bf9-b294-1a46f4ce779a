{"version": 3, "file": "../app/api/quizzes/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,+WCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAASC,EADMH,MACNG,CAASF,EAAAA,CAAAA,GAEpBG,MAAMF,GACR,GADQA,CAAAA,EAAS,CACVG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIvE,IAAMC,EAAW,MAAXA,EAAiBC,EAAAA,CACpBC,MAAM,CAAC,CACNV,EAAAA,CAAIW,EAAAA,OAAOA,CAACX,EAAE,CACdY,IAAAA,CAAMD,EAAAA,OAAOA,CAACC,IAAI,CAClBC,WAAAA,CAAaF,EAAAA,OAAOA,CAACE,WAAW,CAChCC,QAAAA,CAAUH,EAAAA,OAAOA,CAACG,QAAQ,CAC1BC,SAAAA,CAAWJ,EAAAA,OAAOA,CAACI,SAAS,CAC5BC,YAAAA,CAAcL,EAAAA,OAAOA,CAACK,YAAY,CAClCC,QAAAA,CAAUN,EAAAA,OAAOA,CAACM,QAAQ,CAC1BC,SAAAA,CAAWP,EAAAA,OAAOA,CAACO,SAAS,CAC5BC,QAAAA,CAAUR,EAAAA,OAAOA,CAACQ,QAAQ,CAC1BC,QAAAA,CAAUT,EAAAA,OAAOA,CAACS,QAAQ,CAC1BC,SAAAA,CAAWV,EAAAA,OAAOA,CAACU,SAAS,CAC5BC,SAAAA,CAAWX,EAAAA,OAAOA,CAACW,SAAS,CAC5BC,WAAAA,CAAaC,EAAAA,QAAQA,CAACZ,IAAI,CAC1Ba,UAAAA,CAAYC,EAAAA,OAAOA,CAACd,IAAI,CACxBe,UAAAA,CAAYC,EAAAA,OAAOA,CAAChB,IAAAA,CACtB,EACCiB,IAAI,CAAClB,EAAAA,OAAAA,CAAAA,CACLmB,QAAQ,CAACN,EAAAA,QAAAA,CAAUO,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQO,SAAS,CAAEM,EAAAA,QAAAA,CAASxB,EAAE,GACpD8B,QAAQ,CAACJ,EAAAA,OAAAA,CAASM,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACjBD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQQ,QAAQ,CAAEO,EAAAA,OAAAA,CAAQ1B,EAAE,EAC/B+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGP,EAAAA,QAAAA,CAASL,QAAQ,CAAEO,EAAAA,OAAOA,CAAC1B,EAAE,IAEjC8B,QAAQ,CAACF,EAAAA,OAAAA,CAASI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACjBD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQS,QAAQ,CAAEQ,EAAAA,OAAAA,CAAQ5B,EAAE,EAC/B+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,EAAAA,OAAAA,CAAQN,QAAQ,CAAEQ,EAAAA,OAAAA,CAAQ5B,EAAE,IAEhCiC,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrBiC,EADqBjC,CAAAA,CAAAA,CAChB,CAAC,GAET,GAAwB,GAAG,CAAvBO,EAAS2B,MAAM,CACjB,OAAO/B,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,iBAAiB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGtE,IAAM6B,EAAO5B,CAAQ,CAAf4B,EAAkB,CASlBC,EAA0BC,CANV,MAAM7B,EAAAA,EAAAA,CACzBC,CAK6B4B,KALvB,GACNT,IAAI,CAACU,EAAAA,SAAAA,CAAAA,CACLN,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGQ,EAAAA,SAAAA,CAAUtC,MAAM,CAAEA,GAAAA,CAAAA,CAGgBuC,CAHhBvC,EAGmB,CAACwC,IAAa,CAC7D,GAAGA,CAAQ,CACXA,QAAAA,CAAUA,EAASA,QAAQ,CAC3BC,OAAAA,CAASD,EAASC,OAAO,CACzBC,WAAAA,CAAaF,EAASE,WAAW,CACjCC,WAAAA,CAAaH,EAASG,WAAAA,EACxB,EAEA,OAAOxC,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvB+B,IAAAA,CAAM,CACJ,GAAGA,CAAI,CACPG,SAAAA,CAAWF,CACb,CACF,EACF,CAAE,MAAO/B,EAAO,CAEd,EAFOA,KACPuC,OAAAA,CAAQvC,KAAK,CAAC,uBAAwBA,GAC/BF,EAAAA,CAD+BE,WAC/BF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeuC,EACpBhD,CAAoB,CACpB,CAFoBgD,OAElB/C,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAASC,EADMH,MACNG,CAASF,EAAAA,CAAAA,GAEpBG,MAAMF,GACR,GADQA,CAAAA,EAAS,CACVG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIvE,GAAM,MACJK,CAAI,aACJC,CAAW,UACXC,CAAQ,WACRC,CAAS,cACTC,CAAY,UACZC,CAAQ,WACR8B,CAAS,UACT5B,CAAQ,UACRC,CAAQ,CACRmB,SAAAA,CAAWS,CAAgB,CAC5B,CAZY,EAYTC,IAAAA,EAZuB5C,IAAI,CAAZP,EAcnB+C,OAAAA,CAAQK,GAAG,CAAC,iBAAkB,CAC5BjD,MAAAA,GACAa,QAAAA,GACAK,QAAAA,YACAC,QAAAA,IACA2B,OACAnC,CACF,CAFEmC,EAKF,IAAMI,EAAoB1C,EAAAA,EAAAA,CACvBC,MAAM,CAAC,CACNV,EAAAA,CAAIW,EAAAA,OAAOA,CAACX,EAAE,CACdc,QAAAA,CAAUH,EAAAA,OAAOA,CAACG,QAAQ,CAC1BI,SAAAA,CAAWP,EAAAA,OAAOA,CAACO,SAAS,CAC5BC,QAAAA,CAAUR,EAAAA,OAAOA,CAACQ,QAAQ,CAC1BC,QAAAA,CAAUT,EAAAA,OAAOA,CAACS,QAAQ,CAC1B2B,SAAAA,CAAWnB,EAAAA,OAAOA,CAACmB,SAAS,CAC5BK,kBAAAA,CAAoB1B,EAAAA,OAAOA,CAACN,QAAQ,CACpCiC,mBAAAA,CAAqBzB,EAAAA,OAAOA,CAAC5B,EAAAA,CAC/B,EACC6B,IAAI,CAAClB,EAAAA,OAAAA,CAAAA,CACLmB,QAAQ,CAACN,EAAAA,QAAAA,CAAUO,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQO,SAAS,CAAEM,EAAAA,QAAAA,CAASxB,EAAE,GACpD8B,QAAQ,CAACJ,EAAAA,OAAAA,CAASM,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACjBD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQQ,QAAQ,CAAEO,EAAAA,OAAAA,CAAQ1B,EAAE,EAC/B+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGP,EAAAA,QAAAA,CAASL,QAAQ,CAAEO,EAAAA,OAAOA,CAAC1B,EAAE,IAEjC8B,QAAQ,CAACF,EAAAA,OAAAA,CAASI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACjBD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQS,QAAQ,CAAEQ,EAAAA,OAAAA,CAAQ5B,EAAE,EAC/B+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,EAAAA,OAAAA,CAAQN,QAAQ,CAAEQ,EAAAA,OAAAA,CAAQ5B,EAAE,IAEhCiC,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrBiC,EADqBjC,CAAAA,CAAAA,CAChB,CAAC,GAEHqD,EAAe,MAAMH,EAE3B,GAA4B,GAAG,CAA3BG,EAAanB,MAAM,CACrB,GADEmB,IACKlD,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,iBAAiB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGtE,IAAM6B,EAAOkB,CAAY,CAAnBlB,EAAsB,CAC5BS,OAAAA,CAAQK,GAAG,CAAC,uBAAwBd,GAGpC,CAHoCA,CAAAA,EAG9BmB,EAAgBnB,EAAKW,SAArBQ,CACN,GAAIR,GAAaQ,IAAkBR,EAA/BA,OAA+BA,EAAW,KAC5CF,CAAQK,GAAG,CAAC,qBAAsB,eAAEK,EAAeC,WAAfD,KAAeC,CAAkBT,CAAU,GACxE3C,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,qCAAqC,CAC9C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMkD,EAAkB,CACtB,GAAI7C,GAAQ,CAARA,KAAUA,EAAM,CACpB,GAAIC,GAAe,QAAfA,KAAiBA,EAAa,CAClC,GAAIC,GAAY,KAAZA,KAAcA,EAAU,CAC5B,QAAkB4C,IAAd3C,GAA2B,CAAEA,CAAf2C,WAA0B,CAC5C,GAAI1C,KAAiB0C,OAAa,CAAE1C,CAAf0C,WAAe1C,CAAcA,EAAa2C,QAAQ,GAAI,CAC3E,QAAiBD,IAAbzC,GAA0B,EAAbyC,QAAezC,EAAU,CAC1CK,SAAAA,CAAW,IAAIsC,IACjB,EAGiB,WAAb9C,GAAyBK,GAC3BsC,EADE3C,GAAyBK,EAAU,GACrCsC,CAAsBtC,EAEtBsC,EAAWvC,IAFWC,IAEtBsC,CAAoB,CAAG,KACvBA,EAAWrC,QAAXqC,CAAsB,MACb3C,QAAAA,KAAwBM,GACjCqC,EAAWrC,GADsBA,EAAU,GAC3CqC,CAAsBrC,EAEtBqC,EAAWvC,IAFWE,IAEtBqC,CAAoB,CAAG,KACvBA,EAAWtC,QAAXsC,CAAsB,MACA,WAAW,CAAxB3C,IAGT2C,EAAWtC,QAAXsC,CAAsB,KACtBA,EAAWrC,QAAQ,CAAG,MAGxByB,OAAAA,CAAQK,GAAG,CAAC,eAAgBO,GAG5B,IAAMI,EAAc,CAHQJ,CAAAA,IAGFhD,EAAAA,EAAAA,CACvBqD,MAAM,CAACnD,EAAAA,OAAOA,EACdoD,GAAG,CAACN,GACJxB,KAAK,CAACF,CADF0B,CAAAA,CACE1B,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrB+D,SAAS,GAKZ,GAHAnB,OAAAA,CAAQK,GAAG,CAAC,uBAAwBW,CAAW,CAAC,EAAE,EAG9Cb,GAAoBiB,KAAAA,CAAMC,OAA1BlB,CAAkCA,KACpCH,OAAAA,CAAQK,GAAG,CAAC,EAD2C,oBACpBF,EAAiBb,MAAM,EAG1D,MAHmCa,EAG7BvC,EAAAA,CAAG0D,MAAM,CAAC5B,EAAAA,SAAAA,CAAAA,CAAWN,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGQ,EAAAA,SAAAA,CAAUtC,MAAM,CAAEA,IAGlD+C,EAHkD/C,CAAAA,CAAAA,IAG3B,CAAG,GAAG,CAC/B,GADE+C,CACIoB,EAAoBpB,EAAiBR,GAAG,CAAC,CAACC,EAAe4B,KAAmB,CAAlC5B,CAAtBO,MACxB/C,EACAqE,IAAAA,CAAM7B,EAAS6B,IAAI,EAAI,kBACvB7B,QAAAA,CAAuC,UAA7B,OAAOA,EAASA,MAATA,EAAiB,CAC9BA,EAASA,MAATA,EAAiB,CACjB8B,IAAAA,CAAKC,SAAS,CAAC/B,EAASA,MAATA,EAAiB,EACpCC,OAAAA,CAASD,EAASC,MAATD,CAAgB,CAAG8B,IAAAA,CAAKC,SAAS,CAAC/B,EAASC,MAATD,CAAgB,EAAI,KAC/DE,WAAAA,CAAaF,EAASE,MAATF,KAAoB,EAAI,KACrCG,WAAAA,CAAaH,EAASG,MAATH,KAAoB,CAAG8B,IAAAA,CAAKC,SAAS,CAAC/B,EAASG,MAATH,KAAoB,EAAI,KAC3EgC,MAAAA,CAAQhC,EAASgC,MAAThC,EAAmB,EAC3BiC,UAAAA,MAAoChB,IAAxBjB,EAASiC,UAAU,CAAiBjC,EAASiC,MAATjC,IAAmB,CAAG4B,EAAQ,GAARA,EAGxExB,OAAAA,CAAQK,GAAG,CAAC,uBAAwBkB,EAAkBjC,MAAM,EAC5D,MAAM1B,CAD8B2D,CAC9B3D,EAAAA,CAAGkE,MAAM,CAACpC,EAAAA,SAAAA,CAAAA,CAAWqC,MAAM,CAACR,EACpC,CAGF,OAAOhE,EAAAA,KAJ+BgE,CAAAA,MAI/BhE,CAAaC,IAAI,CAAC,CACvBwE,OAAAA,EAAS,EACTzC,IAAAA,CAAMyB,CAAW,CAAC,EAAE,CACpBiB,OAAAA,CAAS,2BACX,EACF,CAAE,MAAOxE,EAAO,CAEd,EAFOA,KACPuC,OAAAA,CAAQvC,KAAK,CAAC,uBAAwBA,GAC/BF,EAD+BE,CAAAA,WAC/BF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAyByE,OAAAA,CAASzE,EAAM,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeyE,EACpBlF,CAAoB,CACpB,IAFoBkF,IAElBjF,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,IAAE,CAAE,CAAG,MAAMD,EACfE,EAASC,EADMH,MACNG,CAASF,EAAAA,CAAAA,GAEpBG,MAAMF,GACR,GADQA,CAAAA,EAAS,CACVG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIvE,IAAMwC,EADejD,EAAQmF,KAARnF,EAAe,CAACoF,EACnBA,UAD+B,CAClBC,GAAG,CAAC,aAG7BhC,EAAoB1C,EAAAA,EAAAA,CACvBC,MAAM,CAAC,CACNV,EAFEmD,CAEExC,EAAAA,OAAOA,CAACX,EAAE,CACdc,QAAAA,CAAUH,EAAAA,OAAOA,CAACG,QAAQ,CAC1BI,SAAAA,CAAWP,EAAAA,OAAOA,CAACO,SAAS,CAC5BC,QAAAA,CAAUR,EAAAA,OAAOA,CAACQ,QAAQ,CAC1BC,QAAAA,CAAUT,EAAAA,OAAOA,CAACS,QAAQ,CAC1B2B,SAAAA,CAAWnB,EAAAA,OAAOA,CAACmB,SAAAA,CACrB,EACClB,IAAI,CAAClB,EAAAA,OAAAA,CAAAA,CACLmB,QAAQ,CAACN,EAAAA,QAAAA,CAAUO,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQO,SAAS,CAAEM,EAAAA,QAAAA,CAASxB,EAAE,GACpD8B,QAAQ,CAACJ,EAAAA,OAAAA,CAASM,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACjBD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQQ,QAAQ,CAAEO,EAAAA,OAAAA,CAAQ1B,EAAE,EAC/B+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGP,EAAAA,QAAAA,CAASL,QAAQ,CAAEO,EAAAA,OAAOA,CAAC1B,EAAE,IAEjC8B,QAAQ,CAACF,EAAAA,OAAAA,CAASI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACjBD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQS,QAAQ,CAAEQ,EAAAA,OAAAA,CAAQ5B,EAAE,EAC/B+B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,EAAAA,OAAAA,CAAQN,QAAQ,CAAEQ,EAAAA,OAAAA,CAAQ5B,EAAE,IAEhCiC,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrBiC,EADqBjC,CAAAA,CAAAA,CAChB,CAAC,GAEHqD,EAAe,MAAMH,EAE3B,GAAIG,GAA2B,GAAdnB,MAAM,CACrB,OAAO/B,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,iBAAiB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAItE,GAAIwC,GAAaO,CAAY,CAAC,EAAE,CAACP,CAA7BA,QAAsC,GAAK7C,SAAS6C,GACtD,MADsDA,CAC/C3C,EAD2D,YAC3DA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,qCAAqC,CAC9C,CAAEC,MAAAA,CAAQ,GAAI,GAclB,OARA,MAAME,EAAAA,EAAAA,CAAG0D,MAAM,CAACiB,EAAAA,YAAAA,CAAAA,CAAcnD,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGqD,EAAAA,YAAAA,CAAanF,MAAM,CAAEA,IAG5D,EAH4DA,CAAAA,CAAAA,EAGtDQ,EAAAA,EAAAA,CAAG0D,MAAM,CAAC5B,EAAAA,SAAAA,CAAAA,CAAWN,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGQ,EAAAA,SAAAA,CAAUtC,MAAM,CAAEA,IAGtD,EAHsDA,CAAAA,CAAAA,EAGhDQ,EAAAA,EAAAA,CAAG0D,MAAM,CAACxD,EAAAA,OAAAA,CAAAA,CAASsB,KAAK,CAACF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGpB,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IAEvCG,EAFuCH,CAAAA,CAAAA,UAEvCG,CAAaC,IAAI,CAAC,CACvBwE,OAAAA,EAAS,EACTC,OAAAA,CAAS,2BACX,EACF,CAAE,MAAOxE,EAAO,CAEd,EAFOA,KACPuC,OAAAA,CAAQvC,KAAK,CAAC,uBAAwBA,GAC/BF,EAAAA,CAD+BE,WAC/BF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCxTA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGa,wBAAwB,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAItB,UAA6B,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAC3B,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,mBAAmB,SACvC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAIC,IAAC,EAAM,CAAH,CAAe8E,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAAf,MAA2C,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,+BACA,6BACA,iBACA,uCACA,CAAK,CACL,kJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/src/app/api/quizzes/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?ae63", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { quizzes, questions, chapters, modules, courses, quizAttempts } from '@/lib/db/schema';\r\nimport { eq, and, or } from 'drizzle-orm';\r\n\r\n// GET /api/quizzes/[id] - Get a specific quiz with questions\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const quizId = parseInt(id);\r\n    \r\n    if (isNaN(quizId)) {\r\n      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });\r\n    }\r\n\r\n    // Get quiz with chapter/module/course information based on quiz type\r\n    const quizData = await db\r\n      .select({\r\n        id: quizzes.id,\r\n        name: quizzes.name,\r\n        description: quizzes.description,\r\n        quizType: quizzes.quizType,\r\n        timeLimit: quizzes.timeLimit,\r\n        minimumScore: quizzes.minimumScore,\r\n        isActive: quizzes.isActive,\r\n        chapterId: quizzes.chapterId,\r\n        moduleId: quizzes.moduleId,\r\n        courseId: quizzes.courseId,\r\n        createdAt: quizzes.createdAt,\r\n        updatedAt: quizzes.updatedAt,\r\n        chapterName: chapters.name,\r\n        moduleName: modules.name,\r\n        courseName: courses.name,\r\n      })\r\n      .from(quizzes)\r\n      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n      .leftJoin(modules, or(\r\n        eq(quizzes.moduleId, modules.id),\r\n        eq(chapters.moduleId, modules.id)\r\n      ))\r\n      .leftJoin(courses, or(\r\n        eq(quizzes.courseId, courses.id),\r\n        eq(modules.courseId, courses.id)\r\n      ))\r\n      .where(eq(quizzes.id, quizId))\r\n      .limit(1);\r\n\r\n    if (quizData.length === 0) {\r\n      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });\r\n    }\r\n\r\n    const quiz = quizData[0];\r\n\r\n    // Get questions for this quiz\r\n    const quizQuestions = await db\r\n      .select()\r\n      .from(questions)\r\n      .where(eq(questions.quizId, quizId));\r\n\r\n    // Parse options for each question\r\n    const questionsWithParsedData = quizQuestions.map(question => ({\r\n      ...question,\r\n      question: question.question,\r\n      options: question.options,\r\n      essayAnswer: question.essayAnswer,\r\n      explanation: question.explanation,\r\n    }));\r\n\r\n    return NextResponse.json({\r\n      quiz: {\r\n        ...quiz,\r\n        questions: questionsWithParsedData\r\n      }\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching quiz:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/quizzes/[id] - Update a quiz\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const quizId = parseInt(id);\r\n    \r\n    if (isNaN(quizId)) {\r\n      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      quizType,\r\n      timeLimit,\r\n      minimumScore,\r\n      isActive,\r\n      teacherId,\r\n      moduleId, // Add moduleId for module quizzes\r\n      courseId, // Add courseId for final exams\r\n      questions: updatedQuestions\r\n    } = body;\r\n\r\n    console.log('Updating quiz:', {\r\n      quizId,\r\n      quizType,\r\n      moduleId,\r\n      courseId,\r\n      teacherId,\r\n      name\r\n    });\r\n\r\n    // FIXED: Get quiz with proper joins based on quiz type\r\n    const existingQuizQuery = db\r\n      .select({\r\n        id: quizzes.id,\r\n        quizType: quizzes.quizType,\r\n        chapterId: quizzes.chapterId,\r\n        moduleId: quizzes.moduleId,\r\n        courseId: quizzes.courseId,\r\n        teacherId: courses.teacherId,\r\n        courseIdFromModule: modules.courseId,\r\n        courseIdFromChapter: courses.id\r\n      })\r\n      .from(quizzes)\r\n      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n      .leftJoin(modules, or(\r\n        eq(quizzes.moduleId, modules.id),\r\n        eq(chapters.moduleId, modules.id)\r\n      ))\r\n      .leftJoin(courses, or(\r\n        eq(quizzes.courseId, courses.id),\r\n        eq(modules.courseId, courses.id)\r\n      ))\r\n      .where(eq(quizzes.id, quizId))\r\n      .limit(1);\r\n\r\n    const existingQuiz = await existingQuizQuery;\r\n\r\n    if (existingQuiz.length === 0) {\r\n      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });\r\n    }\r\n\r\n    const quiz = existingQuiz[0];\r\n    console.log('Found existing quiz:', quiz);\r\n\r\n    // FIXED: Verify teacher has permission based on quiz type\r\n    const quizTeacherId = quiz.teacherId;\r\n    if (teacherId && quizTeacherId !== teacherId) {\r\n      console.log('Permission denied:', { quizTeacherId, requestTeacherId: teacherId });\r\n      return NextResponse.json(\r\n        { error: 'Not authorized to update this quiz' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // FIXED: Prepare update data with proper handling of foreign keys\r\n    const updateData: any = {\r\n      ...(name && { name }),\r\n      ...(description && { description }),\r\n      ...(quizType && { quizType }),\r\n      ...(timeLimit !== undefined && { timeLimit }),\r\n      ...(minimumScore !== undefined && { minimumScore: minimumScore.toString() }),\r\n      ...(isActive !== undefined && { isActive }),\r\n      updatedAt: new Date()\r\n    };\r\n\r\n    // FIXED: Handle foreign key updates based on quiz type\r\n    if (quizType === 'module' && moduleId) {\r\n      updateData.moduleId = moduleId;\r\n      // Clear other foreign keys\r\n      updateData.chapterId = null;\r\n      updateData.courseId = null;\r\n    } else if (quizType === 'final' && courseId) {\r\n      updateData.courseId = courseId;\r\n      // Clear other foreign keys\r\n      updateData.chapterId = null;\r\n      updateData.moduleId = null;\r\n    } else if (quizType === 'chapter') {\r\n      // For chapter quizzes, keep existing chapterId\r\n      // Clear module and course IDs\r\n      updateData.moduleId = null;\r\n      updateData.courseId = null;\r\n    }\r\n\r\n    console.log('Update data:', updateData);\r\n\r\n    // Update the quiz\r\n    const updatedQuiz = await db\r\n      .update(quizzes)\r\n      .set(updateData)\r\n      .where(eq(quizzes.id, quizId))\r\n      .returning();\r\n\r\n    console.log('Updated quiz result:', updatedQuiz[0]);\r\n\r\n    // Update questions if provided\r\n    if (updatedQuestions && Array.isArray(updatedQuestions)) {\r\n      console.log('Updating questions:', updatedQuestions.length);\r\n      \r\n      // Delete existing questions\r\n      await db.delete(questions).where(eq(questions.quizId, quizId));\r\n\r\n      // Insert updated questions\r\n      if (updatedQuestions.length > 0) {\r\n        const questionsToInsert = updatedQuestions.map((question: any, index: number) => ({\r\n          quizId,\r\n          type: question.type || 'multiple_choice',\r\n          question: typeof question.question === 'string' \r\n            ? question.question \r\n            : JSON.stringify(question.question),\r\n          options: question.options ? JSON.stringify(question.options) : null,\r\n          essayAnswer: question.essayAnswer || null,\r\n          explanation: question.explanation ? JSON.stringify(question.explanation) : null,\r\n          points: question.points || 1,\r\n          orderIndex: question.orderIndex !== undefined ? question.orderIndex : index + 1\r\n        }));\r\n\r\n        console.log('Inserting questions:', questionsToInsert.length);\r\n        await db.insert(questions).values(questionsToInsert);\r\n      }\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      quiz: updatedQuiz[0],\r\n      message: 'Quiz updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating quiz:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error', details: error },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/quizzes/[id] - Delete a quiz\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const quizId = parseInt(id);\r\n    \r\n    if (isNaN(quizId)) {\r\n      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });\r\n    }\r\n\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n\r\n    // FIXED: Check quiz ownership with proper joins\r\n    const existingQuizQuery = db\r\n      .select({\r\n        id: quizzes.id,\r\n        quizType: quizzes.quizType,\r\n        chapterId: quizzes.chapterId,\r\n        moduleId: quizzes.moduleId,\r\n        courseId: quizzes.courseId,\r\n        teacherId: courses.teacherId\r\n      })\r\n      .from(quizzes)\r\n      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n      .leftJoin(modules, or(\r\n        eq(quizzes.moduleId, modules.id),\r\n        eq(chapters.moduleId, modules.id)\r\n      ))\r\n      .leftJoin(courses, or(\r\n        eq(quizzes.courseId, courses.id),\r\n        eq(modules.courseId, courses.id)\r\n      ))\r\n      .where(eq(quizzes.id, quizId))\r\n      .limit(1);\r\n\r\n    const existingQuiz = await existingQuizQuery;\r\n\r\n    if (existingQuiz.length === 0) {\r\n      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to delete this quiz\r\n    if (teacherId && existingQuiz[0].teacherId !== parseInt(teacherId)) {\r\n      return NextResponse.json(\r\n        { error: 'Not authorized to delete this quiz' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Delete related data in correct order\r\n    // 1. Delete quiz attempts first\r\n    await db.delete(quizAttempts).where(eq(quizAttempts.quizId, quizId));\r\n\r\n    // 2. Delete questions\r\n    await db.delete(questions).where(eq(questions.quizId, quizId));\r\n\r\n    // 3. Finally delete the quiz\r\n    await db.delete(quizzes).where(eq(quizzes.id, quizId));\r\n\r\n    return NextResponse.json({ \r\n      success: true,\r\n      message: 'Quiz deleted successfully' \r\n    });\r\n  } catch (error) {\r\n    console.error('Error deleting quiz:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/quizzes/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\quizzes\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/quizzes/[id]/route\",\n        pathname: \"/api/quizzes/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/quizzes/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\quizzes\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "quizId", "parseInt", "isNaN", "NextResponse", "json", "error", "status", "quizData", "db", "select", "quizzes", "name", "description", "quizType", "timeLimit", "minimumScore", "isActive", "chapterId", "moduleId", "courseId", "createdAt", "updatedAt", "chapterName", "chapters", "moduleName", "modules", "courseName", "courses", "from", "leftJoin", "eq", "or", "where", "limit", "length", "quiz", "questionsWithParsedData", "quizQuestions", "questions", "map", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "console", "PUT", "teacherId", "updatedQuestions", "body", "log", "existingQuizQuery", "courseIdFromModule", "courseIdFromChapter", "existingQuiz", "quizTeacherId", "requestTeacherId", "updateData", "undefined", "toString", "Date", "updatedQuiz", "update", "set", "returning", "Array", "isArray", "delete", "questionsToInsert", "index", "type", "JSON", "stringify", "points", "orderIndex", "insert", "values", "success", "message", "details", "DELETE", "nextUrl", "searchParams", "get", "quizAttempts", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}