try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4db31142-5b87-4e59-8d48-08735aa46f82",e._sentryDebugIdIdentifier="sentry-dbid-4db31142-5b87-4e59-8d48-08735aa46f82")}catch(e){}exports.id=7852,exports.ids=[7852],exports.modules={16451:(e,t,r)=>{"use strict";r.d(t,{EnrollmentProvider:()=>a});var n=r(1472);(0,n.registerClientReference)(function(){throw Error("Attempted to call useEnrollment() from the server but useEnrollment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\contexts\\enrollment-context.tsx","useEnrollment");let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call EnrollmentProvider() from the server but EnrollmentProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\contexts\\enrollment-context.tsx","EnrollmentProvider")},30047:(e,t,r)=>{"use strict";r.d(t,{B:()=>g});var n=r(91754),a=r(93491),o=r(16435),s=r(44331),l=r(82233);function i({...e}){return(0,n.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e,"data-sentry-component":"Breadcrumb","data-sentry-source-file":"breadcrumb.tsx"})}function d({className:e,...t}){return(0,n.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,l.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t,"data-sentry-component":"BreadcrumbList","data-sentry-source-file":"breadcrumb.tsx"})}function c({className:e,...t}){return(0,n.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,l.cn)("inline-flex items-center gap-1.5",e),...t,"data-sentry-component":"BreadcrumbItem","data-sentry-source-file":"breadcrumb.tsx"})}function u({asChild:e,className:t,...r}){let a=e?o.DX:"a";return(0,n.jsx)(a,{"data-slot":"breadcrumb-link",className:(0,l.cn)("hover:text-foreground transition-colors",t),...r,"data-sentry-element":"Comp","data-sentry-component":"BreadcrumbLink","data-sentry-source-file":"breadcrumb.tsx"})}function m({className:e,...t}){return(0,n.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("text-foreground font-normal",e),...t,"data-sentry-component":"BreadcrumbPage","data-sentry-source-file":"breadcrumb.tsx"})}function b({children:e,className:t,...r}){return(0,n.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",t),...r,"data-sentry-component":"BreadcrumbSeparator","data-sentry-source-file":"breadcrumb.tsx",children:e??(0,n.jsx)(s.A,{})})}var p=r(21372);let f={"/dashboard":[{title:"Dashboard",link:"/dashboard"}],"/dashboard/employee":[{title:"Dashboard",link:"/dashboard"},{title:"Employee",link:"/dashboard/employee"}],"/dashboard/product":[{title:"Dashboard",link:"/dashboard"},{title:"Product",link:"/dashboard/product"}],"/courses":[{title:"Home",link:"/"},{title:"Available Courses",link:"/courses"}],"/my-courses":[{title:"Home",link:"/"},{title:"My Courses",link:"/my-courses"}]},y=(0,r(55732).A)("Slash",[["path",{d:"M22 2 2 22",key:"y4kqgn"}]]);function g(){let e=function(){let e=(0,p.usePathname)();return(0,a.useMemo)(()=>{if(f[e])return f[e];let t=e.split("/").filter(Boolean);return t.map((e,r)=>{let n=`/${t.slice(0,r+1).join("/")}`;return{title:e.charAt(0).toUpperCase()+e.slice(1),link:n}})},[e])}();return 0===e.length?null:(0,n.jsx)(i,{"data-sentry-element":"Breadcrumb","data-sentry-component":"Breadcrumbs","data-sentry-source-file":"breadcrumbs.tsx",children:(0,n.jsx)(d,{"data-sentry-element":"BreadcrumbList","data-sentry-source-file":"breadcrumbs.tsx",children:e.map((t,r)=>(0,n.jsxs)(a.Fragment,{children:[r!==e.length-1&&(0,n.jsx)(c,{className:"hidden md:block",children:(0,n.jsx)(u,{href:t.link,children:t.title})}),r<e.length-1&&(0,n.jsx)(b,{className:"hidden md:block",children:(0,n.jsx)(y,{})}),r===e.length-1&&(0,n.jsx)(m,{children:t.title})]},t.title))})})}},47594:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(15349).A)("PlayIcon",[["path",{d:"M18.8906 12.846C18.5371 14.189 16.8667 15.138 13.5257 17.0361C10.296 18.8709 8.6812 19.7884 7.37983 19.4196C6.8418 19.2671 6.35159 18.9776 5.95624 18.5787C5 17.6139 5 15.7426 5 12C5 8.2574 5 6.3861 5.95624 5.42132C6.35159 5.02245 6.8418 4.73288 7.37983 4.58042C8.6812 4.21165 10.296 5.12907 13.5257 6.96393C16.8667 8.86197 18.5371 9.811 18.8906 11.154C19.0365 11.7084 19.0365 12.2916 18.8906 12.846Z",stroke:"currentColor",key:"k0"}]])},54093:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436)),Promise.resolve().then(r.bind(r,74829))},56301:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590)),Promise.resolve().then(r.bind(r,16451))},74829:(e,t,r)=>{"use strict";r.d(t,{EnrollmentProvider:()=>i,q:()=>l});var n=r(91754),a=r(93491),o=r(9695);let s=(0,a.createContext)(void 0),l=()=>{let e=(0,a.useContext)(s);if(!e)throw Error("useEnrollment must be used within an EnrollmentProvider");return e},i=({children:e})=>{let[t,r]=(0,a.useState)(!1),[l,i]=(0,a.useState)(o.n4),[d,c]=(0,a.useState)([]),u="lms-enrollment-data",m="lms-multiple-enrollment-data";(0,a.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(m);if(e){let t=JSON.parse(e);Date.now()<t.expirationTime?(c(t.enrolledCourses),r(t.enrolledCourses.length>0),t.enrolledCourses.length>0&&i(t.enrolledCourses[0])):localStorage.removeItem(m);return}let t=localStorage.getItem(u);if(t){let e=JSON.parse(t);if(Date.now()<e.expirationTime){r(e.isEnrolled),i(e.courseData),c([e.courseData]);let t={enrolledCourses:[e.courseData],enrollmentTimestamp:e.enrollmentTimestamp,expirationTime:e.expirationTime};localStorage.setItem(m,JSON.stringify(t)),localStorage.removeItem(u)}else localStorage.removeItem(u)}}catch(e){console.error("Failed to load enrollment data:",e),localStorage.removeItem(u),localStorage.removeItem(m)}})()},[]);let b=e=>{let t=Date.now();try{c(r=>{let n,a={enrolledCourses:n=r.some(t=>t.id===e.id)?r.map(t=>t.id===e.id?e:t):[...r,e],enrollmentTimestamp:t,expirationTime:t+6e5};return localStorage.setItem(m,JSON.stringify(a)),n}),setTimeout(()=>{localStorage.removeItem(m),r(!1),c([]),i(o.n4)},6e5)}catch(e){console.error("Failed to persist enrollment data:",e)}};return(0,n.jsx)(s.Provider,{value:{isEnrolled:t,courseData:l,enrollInCourse:()=>{r(!0);let e={...o.n4,status:"in-progress"};i(e),b(e)},enrollInCourseWithPurchase:e=>{r(!0);let t={...e,status:"in-progress",totalProgress:0};i(t),b(t)},updateCourseProgress:e=>{l.id===e.id&&i(e),c(t=>t.map(t=>t.id===e.id?e:t)),t&&b(e)},enrolledCourses:d,isEnrolledInCourse:e=>d.some(t=>t.id===e),getCourseById:e=>d.find(t=>t.id===e)},"data-sentry-element":"EnrollmentContext.Provider","data-sentry-component":"EnrollmentProvider","data-sentry-source-file":"enrollment-context.tsx",children:e})}},79313:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(15349).A)("UserIcon",[["path",{d:"M6.57757 15.4816C5.1628 16.324 1.45336 18.0441 3.71266 20.1966C4.81631 21.248 6.04549 22 7.59087 22H16.4091C17.9545 22 19.1837 21.248 20.2873 20.1966C22.5466 18.0441 18.8372 16.324 17.4224 15.4816C14.1048 13.5061 9.89519 13.5061 6.57757 15.4816Z",stroke:"currentColor",key:"k0"}],["path",{d:"M16.5 6.5C16.5 8.98528 14.4853 11 12 11C9.51472 11 7.5 8.98528 7.5 6.5C7.5 4.01472 9.51472 2 12 2C14.4853 2 16.5 4.01472 16.5 6.5Z",stroke:"currentColor",key:"k1"}]])},80601:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(91754);r(93491);var a=r(16435),o=r(25758),s=r(82233);let l=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,asChild:r=!1,...o}){let i=r?a.DX:"span";return(0,n.jsx)(i,{"data-slot":"badge",className:(0,s.cn)(l({variant:t}),e),...o,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},97893:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>x,generateImageMetadata:()=>v,generateMetadata:()=>g,generateViewport:()=>h,metadata:()=>b});var a=r(63033),o=r(18188),s=r(5434),l=r(45188),i=r(67999),d=r(4590),c=r(16451),u=r(23064),m=r(7688);let b={title:"Student Portal - LMS",description:"Learning Management System - Student Portal"};async function p({children:e}){let t=await (0,u.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,o.jsx)(c.EnrollmentProvider,{"data-sentry-element":"EnrollmentProvider","data-sentry-component":"StudentsLayout","data-sentry-source-file":"layout.tsx",children:(0,o.jsx)(s.default,{"data-sentry-element":"KBar","data-sentry-source-file":"layout.tsx",children:(0,o.jsxs)(d.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,o.jsx)(l.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,o.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,o.jsx)(i.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),e]})]})})})}let f={...a},y="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;n=new Proxy(p,{apply:(e,t,r)=>{let n,a,o;try{let e=y?.getStore();n=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,o=e?.headers}catch{}return m.wrapServerComponentWithSentry(e,{componentRoute:"/(students-page)",componentType:"Layout",sentryTraceHeader:n,baggageHeader:a,headers:o}).apply(t,r)}});let g=void 0,v=void 0,h=void 0,x=n}};
//# sourceMappingURL=7852.js.map