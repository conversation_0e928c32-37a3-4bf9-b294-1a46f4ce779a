try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="4163e5a3-5369-4334-99e6-1372af1d7653",e._sentryDebugIdIdentifier="sentry-dbid-4163e5a3-5369-4334-99e6-1372af1d7653")}catch(e){}"use strict";(()=>{var e={};e.id=6834,e.ids=[6834],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45370:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>P,routeModule:()=>E,serverHooks:()=>T,workAsyncStorage:()=>D,workUnitAsyncStorage:()=>N});var t={};s.r(t),s.d(t,{DELETE:()=>g,GET:()=>x,HEAD:()=>A,OPTIONS:()=>v,PATCH:()=>y,POST:()=>I,PUT:()=>b});var o=s(3690),i=s(56947),u=s(75250),n=s(63033),a=s(62187),d=s(18621),c=s(32230),l=s(74683),p=s(7688);async function m(e,{params:r}){try{let{id:e}=await r,s=parseInt(e);if(isNaN(s))return a.NextResponse.json({error:"Invalid course ID"},{status:400});let t=await d.db.select({id:c.courses.id,name:c.courses.name,description:c.courses.description,instructor:c.courses.instructor,type:c.courses.type,enrollmentType:c.courses.enrollmentType,startDate:c.courses.startDate,endDate:c.courses.endDate,teacherId:c.courses.teacherId,institutionId:c.courses.institutionId,courseCode:c.courses.courseCode,coverPicture:c.courses.coverPicture,isPurchasable:c.courses.isPurchasable,price:c.courses.price,currency:c.courses.currency,previewMode:c.courses.previewMode,createdAt:c.courses.createdAt,updatedAt:c.courses.updatedAt,teacherName:c.users.name,teacherEmail:c.users.email}).from(c.courses).leftJoin(c.users,(0,l.eq)(c.courses.teacherId,c.users.id)).where((0,l.eq)(c.courses.id,s)).limit(1);if(0===t.length)return a.NextResponse.json({error:"Course not found"},{status:404});let o=t[0],[i,u,n,p,m]=await Promise.all([d.db.select().from(c.courseAdmissions).where((0,l.eq)(c.courseAdmissions.courseId,s)).limit(1),d.db.select().from(c.courseAcademics).where((0,l.eq)(c.courseAcademics.courseId,s)).limit(1),d.db.select().from(c.courseTuitionAndFinancing).where((0,l.eq)(c.courseTuitionAndFinancing.courseId,s)).limit(1),d.db.select().from(c.courseCareers).where((0,l.eq)(c.courseCareers.courseId,s)).limit(1),d.db.select().from(c.courseStudentExperience).where((0,l.eq)(c.courseStudentExperience.courseId,s)).limit(1)]),q=await d.db.select().from(c.modules).where((0,l.eq)(c.modules.courseId,s)),h=await Promise.all(q.map(async e=>{let r=await d.db.select().from(c.chapters).where((0,l.eq)(c.chapters.moduleId,e.id)),s=await Promise.all(r.map(async e=>{let r=await d.db.select().from(c.quizzes).where((0,l.eq)(c.quizzes.chapterId,e.id)),s=await Promise.all(r.map(async e=>{let r=await d.db.select().from(c.questions).where((0,l.eq)(c.questions.quizId,e.id));return{...e,questions:r}}));return{...e,quizzes:s}}));return{...e,chapters:s}})),w=await d.db.select({id:c.quizzes.id,name:c.quizzes.name,description:c.quizzes.description,chapterId:c.quizzes.chapterId,moduleId:c.quizzes.moduleId,courseId:c.quizzes.courseId,quizType:c.quizzes.quizType,minimumScore:c.quizzes.minimumScore,timeLimit:c.quizzes.timeLimit,startDate:c.quizzes.startDate,endDate:c.quizzes.endDate,isActive:c.quizzes.isActive,createdAt:c.quizzes.createdAt,updatedAt:c.quizzes.updatedAt}).from(c.quizzes).leftJoin(c.modules,(0,l.eq)(c.quizzes.moduleId,c.modules.id)).where((0,l.or)((0,l.eq)(c.quizzes.courseId,s),(0,l.Uo)((0,l.eq)(c.modules.courseId,s),(0,l.eq)(c.quizzes.quizType,"module")))),f=await Promise.all(w.map(async e=>{let r=await d.db.select().from(c.questions).where((0,l.eq)(c.questions.quizId,e.id));return{...e,questions:r}})),z=await d.db.select({count:c.courseEnrollments.id}).from(c.courseEnrollments).where((0,l.eq)(c.courseEnrollments.courseId,s)),x=await d.db.select({count:c.studentEnrollments.id}).from(c.studentEnrollments).where((0,l.eq)(c.studentEnrollments.courseId,s));return a.NextResponse.json({success:!0,course:{...o,modules:h,moduleQuizzes:f,enrollmentCount:z.length,studentCount:x.length,admissions:i[0]||null,academics:u[0]||null,tuitionAndFinancing:n[0]||null,careers:p[0]||null,studentExperience:m[0]||null}})}catch(e){return console.error("Error fetching course:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function q(e,{params:r}){try{let{id:s}=await r,t=parseInt(s);if(isNaN(t))return a.NextResponse.json({error:"Invalid course ID"},{status:400});let{name:o,description:i,type:u,enrollmentType:n,price:p,currency:m,isPurchasable:q,startDate:h,endDate:w,teacherId:f,courseCode:z,coverPicture:x,admissions:I,academics:b,tuitionAndFinancing:y,careers:g,studentExperience:A}=await e.json(),v=await d.db.select().from(c.courses).where((0,l.eq)(c.courses.id,t)).limit(1);if(0===v.length)return a.NextResponse.json({error:"Course not found"},{status:404});if(f){let e=await d.db.select().from(c.users).where((0,l.Uo)((0,l.eq)(c.users.id,f),(0,l.eq)(c.users.role,"teacher"))).limit(1);if(0===e.length)return a.NextResponse.json({error:"Teacher not found or not authorized"},{status:403})}if(z&&z!==v[0].courseCode&&(await d.db.select().from(c.courses).where((0,l.Uo)((0,l.eq)(c.courses.courseCode,z),(0,l.eq)(c.courses.id,t))).limit(1)).length>0)return a.NextResponse.json({error:"Course code already exists"},{status:400});let E=await d.db.update(c.courses).set({...o&&{name:o},...i&&{description:i},...u&&{type:u},...n&&{enrollmentType:n},...void 0!==p&&{price:p.toString()},...m&&{currency:m},...void 0!==q&&{isPurchasable:q},...h&&{startDate:new Date(h)},...w&&{endDate:new Date(w)},...f&&{teacherId:f},...z&&{courseCode:z},...x&&{coverPicture:x},updatedAt:new Date}).where((0,l.eq)(c.courses.id,t)).returning();return I&&(await d.db.delete(c.courseAdmissions).where((0,l.eq)(c.courseAdmissions.courseId,t)),await d.db.insert(c.courseAdmissions).values({courseId:t,requirements:I.requirements,applicationDeadline:I.applicationDeadline,prerequisites:I.prerequisites})),b&&(await d.db.delete(c.courseAcademics).where((0,l.eq)(c.courseAcademics.courseId,t)),await d.db.insert(c.courseAcademics).values({courseId:t,credits:b.credits,workload:b.workload,assessment:b.assessment})),y&&(await d.db.delete(c.courseTuitionAndFinancing).where((0,l.eq)(c.courseTuitionAndFinancing.courseId,t)),await d.db.insert(c.courseTuitionAndFinancing).values({courseId:t,totalCost:y.totalCost,paymentOptions:y.paymentOptions,scholarships:y.scholarships})),g&&(await d.db.delete(c.courseCareers).where((0,l.eq)(c.courseCareers.courseId,t)),await d.db.insert(c.courseCareers).values({courseId:t,outcomes:g.outcomes,industries:g.industries,averageSalary:g.averageSalary})),A&&(await d.db.delete(c.courseStudentExperience).where((0,l.eq)(c.courseStudentExperience.courseId,t)),await d.db.insert(c.courseStudentExperience).values({courseId:t,testimonials:A.testimonials,facilities:A.facilities,support:A.support})),a.NextResponse.json({course:E[0],message:"Course updated successfully"})}catch(e){return console.error("Error updating course:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{id:s}=await r,t=parseInt(s);if(isNaN(t))return a.NextResponse.json({error:"Invalid course ID"},{status:400});let o=e.nextUrl.searchParams.get("teacherId"),i=await d.db.select().from(c.courses).where((0,l.eq)(c.courses.id,t)).limit(1);if(0===i.length)return a.NextResponse.json({error:"Course not found"},{status:404});if(o&&i[0].teacherId!==parseInt(o))return a.NextResponse.json({error:"Not authorized to delete this course"},{status:403});let u=await d.db.select({id:c.quizzes.id}).from(c.quizzes).where((0,l.Uo)((0,l.eq)(c.quizzes.courseId,t))),n=await d.db.select({id:c.modules.id}).from(c.modules).where((0,l.eq)(c.modules.courseId,t)),p=await d.db.select({id:c.quizzes.id}).from(c.quizzes).innerJoin(c.modules,(0,l.eq)(c.quizzes.moduleId,c.modules.id)).where((0,l.eq)(c.modules.courseId,t)),m=[];for(let e of n)for(let r of(await d.db.select({id:c.chapters.id}).from(c.chapters).where((0,l.eq)(c.chapters.moduleId,e.id)))){let e=await d.db.select({id:c.quizzes.id}).from(c.quizzes).where((0,l.eq)(c.quizzes.chapterId,r.id));m.push(...e)}let q=[...u.map(e=>e.id),...p.map(e=>e.id),...m.map(e=>e.id)];for(let e of q)await d.db.delete(c.questions).where((0,l.eq)(c.questions.quizId,e)),await d.db.delete(c.quizAttempts).where((0,l.eq)(c.quizAttempts.quizId,e));for(let e of q)await d.db.delete(c.quizzes).where((0,l.eq)(c.quizzes.id,e));for(let e of n)await d.db.delete(c.chapters).where((0,l.eq)(c.chapters.moduleId,e.id));return await d.db.delete(c.modules).where((0,l.eq)(c.modules.courseId,t)),await d.db.delete(c.courseEnrollments).where((0,l.eq)(c.courseEnrollments.courseId,t)),await d.db.delete(c.studentEnrollments).where((0,l.eq)(c.studentEnrollments.courseId,t)),await d.db.delete(c.courses).where((0,l.eq)(c.courses.id,t)),a.NextResponse.json({success:!0,message:"Course deleted successfully"})}catch(e){return console.error("Error deleting course:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let w={...n},f="workUnitAsyncStorage"in w?w.workUnitAsyncStorage:"requestAsyncStorage"in w?w.requestAsyncStorage:void 0;function z(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let o;try{let e=f?.getStore();o=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/courses/[id]",headers:o}).apply(s,t)}})}let x=z(m,"GET"),I=z(void 0,"POST"),b=z(q,"PUT"),y=z(void 0,"PATCH"),g=z(h,"DELETE"),A=z(void 0,"HEAD"),v=z(void 0,"OPTIONS"),E=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/courses/[id]/route",pathname:"/api/courses/[id]",filename:"route",bundlePath:"app/api/courses/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\courses\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:D,workUnitAsyncStorage:N,serverHooks:T}=E;function P(){return(0,u.patchFetch)({workAsyncStorage:D,workUnitAsyncStorage:N})}},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,1617,2957],()=>s(45370));module.exports=t})();
//# sourceMappingURL=route.js.map