try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="8d04480e-157b-4cf2-8e35-b1f1cfe4a047",e._sentryDebugIdIdentifier="sentry-dbid-8d04480e-157b-4cf2-8e35-b1f1cfe4a047")}catch(e){}"use strict";(()=>{var e={};e.id=2185,e.ids=[2185],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65192:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>T,routeModule:()=>v,serverHooks:()=>R,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>j});var t={};s.r(t),s.d(t,{DELETE:()=>w,GET:()=>f,HEAD:()=>N,OPTIONS:()=>A,PATCH:()=>g,POST:()=>x,PUT:()=>y});var o=s(3690),i=s(56947),u=s(75250),d=s(63033),n=s(62187),a=s(18621),c=s(32230),l=s(74683),p=s(7688);async function q(e){try{let r,s,t=e.nextUrl.searchParams,o=t.get("chapterId"),i=t.get("moduleId"),u=t.get("courseId"),d=t.get("quizType"),p=t.get("teacherId");if(!p)return n.NextResponse.json({error:"Teacher ID required"},{status:400});let q=[],m=!1;if(o){let e=await a.db.select({chapterId:c.chapters.id,moduleId:c.chapters.moduleId,courseId:c.modules.courseId,teacherId:c.courses.teacherId}).from(c.chapters).leftJoin(c.modules,(0,l.eq)(c.chapters.moduleId,c.modules.id)).leftJoin(c.courses,(0,l.eq)(c.modules.courseId,c.courses.id)).where((0,l.Uo)((0,l.eq)(c.chapters.id,parseInt(o)),(0,l.eq)(c.courses.teacherId,parseInt(p)))).limit(1);if(0===e.length)return n.NextResponse.json({error:"Chapter not found or access denied"},{status:403});q.push((0,l.eq)(c.quizzes.chapterId,parseInt(o))),m=!0}else if(i){let e=await a.db.select({moduleId:c.modules.id,courseId:c.modules.courseId,teacherId:c.courses.teacherId}).from(c.modules).leftJoin(c.courses,(0,l.eq)(c.modules.courseId,c.courses.id)).where((0,l.Uo)((0,l.eq)(c.modules.id,parseInt(i)),(0,l.eq)(c.courses.teacherId,parseInt(p)))).limit(1);if(0===e.length)return n.NextResponse.json({error:"Module not found or access denied"},{status:403});q.push((0,l.eq)(c.quizzes.moduleId,parseInt(i))),m=!0}else if(u){let e=await a.db.select().from(c.courses).where((0,l.Uo)((0,l.eq)(c.courses.id,parseInt(u)),(0,l.eq)(c.courses.teacherId,parseInt(p)))).limit(1);if(0===e.length)return n.NextResponse.json({error:"Course not found or access denied"},{status:403});d&&(q.push((0,l.eq)(c.quizzes.quizType,d)),"final"===d&&q.push((0,l.eq)(c.quizzes.courseId,parseInt(u)))),m=!0}else{let e=(await a.db.select({id:c.courses.id}).from(c.courses).where((0,l.eq)(c.courses.teacherId,parseInt(p)))).map(e=>e.id);if(0===e.length)return n.NextResponse.json({quizzes:[]});m=!0}if(!m)return n.NextResponse.json({error:"Access denied"},{status:403});r=1===q.length?q[0]:q.length>1?(0,l.Uo)(...q):(0,l.eq)(c.courses.teacherId,parseInt(p)),s=q.length>0?await a.db.select({id:c.quizzes.id,name:c.quizzes.name,description:c.quizzes.description,quizType:c.quizzes.quizType,timeLimit:c.quizzes.timeLimit,minimumScore:c.quizzes.minimumScore,isActive:c.quizzes.isActive,chapterId:c.quizzes.chapterId,moduleId:c.quizzes.moduleId,courseId:c.quizzes.courseId,createdAt:c.quizzes.createdAt,updatedAt:c.quizzes.updatedAt,chapterName:c.chapters.name,moduleName:c.modules.name,courseName:c.courses.name}).from(c.quizzes).leftJoin(c.chapters,(0,l.eq)(c.quizzes.chapterId,c.chapters.id)).leftJoin(c.modules,(0,l.or)((0,l.eq)(c.chapters.moduleId,c.modules.id),(0,l.eq)(c.quizzes.moduleId,c.modules.id))).leftJoin(c.courses,(0,l.or)((0,l.eq)(c.modules.courseId,c.courses.id),(0,l.eq)(c.quizzes.courseId,c.courses.id))).where(r):await a.db.select({id:c.quizzes.id,name:c.quizzes.name,description:c.quizzes.description,quizType:c.quizzes.quizType,timeLimit:c.quizzes.timeLimit,minimumScore:c.quizzes.minimumScore,isActive:c.quizzes.isActive,chapterId:c.quizzes.chapterId,moduleId:c.quizzes.moduleId,courseId:c.quizzes.courseId,createdAt:c.quizzes.createdAt,updatedAt:c.quizzes.updatedAt,chapterName:c.chapters.name,moduleName:c.modules.name,courseName:c.courses.name}).from(c.quizzes).leftJoin(c.chapters,(0,l.eq)(c.quizzes.chapterId,c.chapters.id)).leftJoin(c.modules,(0,l.or)((0,l.eq)(c.chapters.moduleId,c.modules.id),(0,l.eq)(c.quizzes.moduleId,c.modules.id))).leftJoin(c.courses,(0,l.or)((0,l.eq)(c.modules.courseId,c.courses.id),(0,l.eq)(c.quizzes.courseId,c.courses.id))).where((0,l.eq)(c.courses.teacherId,parseInt(p)));let h=await Promise.all(s.map(async e=>{let r=await a.db.select({count:c.questions.id}).from(c.questions).where((0,l.eq)(c.questions.quizId,e.id));return{...e,questionCount:r.length}}));return n.NextResponse.json({quizzes:h})}catch(e){return console.error("Error fetching quizzes:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e){try{let r,{name:s,description:t,quizType:o="chapter",timeLimit:i,minimumScore:u=70,isActive:d=!0,chapterId:p,moduleId:q,courseId:m,teacherId:h,questions:z=[]}=await e.json();if(!s||!h)return n.NextResponse.json({error:"Name and teacher ID are required"},{status:400});if(!["chapter","module","final"].includes(o))return n.NextResponse.json({error:"Invalid quiz type. Must be chapter, module, or final"},{status:400});if("chapter"===o&&(q||m))return n.NextResponse.json({error:"Chapter quiz should only have chapterId, not moduleId or courseId"},{status:400});if("module"===o&&(p||m))return n.NextResponse.json({error:"Module quiz should only have moduleId, not chapterId or courseId"},{status:400});if("final"===o&&(p||q))return n.NextResponse.json({error:"Final exam should only have courseId, not chapterId or moduleId"},{status:400});if("chapter"===o){if(!p)return n.NextResponse.json({error:"Chapter ID is required for chapter quiz"},{status:400});if(r=await a.db.select({chapterId:c.chapters.id,moduleId:c.chapters.moduleId,courseId:c.modules.courseId,teacherId:c.courses.teacherId}).from(c.chapters).leftJoin(c.modules,(0,l.eq)(c.chapters.moduleId,c.modules.id)).leftJoin(c.courses,(0,l.eq)(c.modules.courseId,c.courses.id)).where((0,l.Uo)((0,l.eq)(c.chapters.id,p),(0,l.eq)(c.courses.teacherId,h))).limit(1),0===r.length)return n.NextResponse.json({error:"Chapter not found or access denied"},{status:403})}else if("module"===o){if(!q)return n.NextResponse.json({error:"Module ID is required for module quiz"},{status:400});if(r=await a.db.select({moduleId:c.modules.id,courseId:c.modules.courseId,teacherId:c.courses.teacherId}).from(c.modules).leftJoin(c.courses,(0,l.eq)(c.modules.courseId,c.courses.id)).where((0,l.Uo)((0,l.eq)(c.modules.id,q),(0,l.eq)(c.courses.teacherId,h))).limit(1),0===r.length)return n.NextResponse.json({error:"Module not found or access denied"},{status:403})}else{if("final"!==o)return n.NextResponse.json({error:"Invalid quiz type. Must be chapter, module, or final"},{status:400});if(!m)return n.NextResponse.json({error:"Course ID is required for final exam"},{status:400});if(r=await a.db.select({courseId:c.courses.id,teacherId:c.courses.teacherId}).from(c.courses).where((0,l.Uo)((0,l.eq)(c.courses.id,m),(0,l.eq)(c.courses.teacherId,h))).limit(1),0===r.length)return n.NextResponse.json({error:"Course not found or access denied"},{status:403})}if("final"===o){if((await a.db.select({id:c.quizzes.id}).from(c.quizzes).where((0,l.Uo)((0,l.eq)(c.quizzes.courseId,m),(0,l.eq)(c.quizzes.quizType,"final"))).limit(1)).length>0)return n.NextResponse.json({error:"A final exam already exists for this course"},{status:409})}else if("module"===o&&(await a.db.select({id:c.quizzes.id}).from(c.quizzes).where((0,l.Uo)((0,l.eq)(c.quizzes.moduleId,q),(0,l.eq)(c.quizzes.quizType,"module"))).limit(1)).length>0)return n.NextResponse.json({error:"A module quiz already exists for this module"},{status:409});let I={name:s,description:t,quizType:o,timeLimit:i,minimumScore:u.toString(),isActive:d,chapterId:"chapter"===o?p:null,moduleId:"module"===o?q:null,courseId:"final"===o?m:null},f=await a.db.insert(c.quizzes).values(I).returning(),x=f[0].id;if(z.length>0){let e=z.map((e,r)=>({quizId:x,type:e.type||"multiple_choice",question:JSON.stringify(e.question),options:e.options?JSON.stringify(e.options):null,essayAnswer:""===e.essayAnswer?null:e.essayAnswer,explanation:""===e.explanation?null:e.explanation,points:e.points||1,orderIndex:e.orderIndex||r+1}));await a.db.insert(c.questions).values(e)}return n.NextResponse.json({quiz:f[0],message:"Quiz created successfully"},{status:201})}catch(e){return console.error("Error creating quiz:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let h={...d},z="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;function I(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let o;try{let e=z?.getStore();o=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/quizzes",headers:o}).apply(s,t)}})}let f=I(q,"GET"),x=I(m,"POST"),y=I(void 0,"PUT"),g=I(void 0,"PATCH"),w=I(void 0,"DELETE"),N=I(void 0,"HEAD"),A=I(void 0,"OPTIONS"),v=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/quizzes/route",pathname:"/api/quizzes",filename:"route",bundlePath:"app/api/quizzes/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\quizzes\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:b,workUnitAsyncStorage:j,serverHooks:R}=v;function T(){return(0,u.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:j})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,1617,2957],()=>s(65192));module.exports=t})();
//# sourceMappingURL=route.js.map