try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="5e04420e-bc8c-4775-ab37-55e3f6d734b5",e._sentryDebugIdIdentifier="sentry-dbid-5e04420e-bc8c-4775-ab37-55e3f6d734b5")}catch(e){}"use strict";(()=>{var e={};e.id=1612,e.ids=[1612],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{e.exports=require("bcrypt")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},40878:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>S,routeModule:()=>b,serverHooks:()=>R,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{DELETE:()=>w,GET:()=>y,HEAD:()=>E,OPTIONS:()=>T,PATCH:()=>v,POST:()=>f,PUT:()=>m});var o=t(3690),i=t(56947),n=t(75250),a=t(63033),u=t(62187),p=t(5486),d=t.n(p),l=t(18621),c=t(7688);async function x(e){try{let{firstName:r,lastName:t,email:s,phone:o,password:i,iaiMembership:n,organization:a}=await e.json();if(!r||!t||!s||!i)return u.NextResponse.json({error:"Missing required fields"},{status:400});let p=`${r} ${t}`;if((await (0,l.ll)`
      SELECT id FROM users WHERE email = ${s}
    `).length>0)return u.NextResponse.json({error:"User already exists with this email"},{status:409});let c=await d().hash(i,12),x=(await (0,l.ll)`
      INSERT INTO users (name, email, password, role)
      VALUES (${p}, ${s}, ${c}, 'student')
      RETURNING id, name, email, role, created_at
    `)[0];return u.NextResponse.json({message:"User registered successfully",user:{id:x.id,name:x.name,email:x.email,role:x.role,created_at:x.created_at}},{status:201})}catch(e){return console.error("Registration error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...a},h="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function g(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=h?.getStore();o=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/auth/register",headers:o}).apply(t,s)}})}let y=g(void 0,"GET"),f=g(x,"POST"),m=g(void 0,"PUT"),v=g(void 0,"PATCH"),w=g(void 0,"DELETE"),E=g(void 0,"HEAD"),T=g(void 0,"OPTIONS"),b=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:A,workUnitAsyncStorage:_,serverHooks:R}=b;function S(){return(0,n.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:_})}},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(40878));module.exports=s})();
//# sourceMappingURL=route.js.map