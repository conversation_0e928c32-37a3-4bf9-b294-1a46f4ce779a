'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Trash2, Image as ImageIcon, TextIcon, VideoIcon, FileTextIcon, MonitorPlayIcon, Link, Upload } from 'lucide-react';
import { FileUploader } from '@/components/file-uploader';
import Image from 'next/image';
import { toast } from 'sonner';
import { WysiwygEditor } from './wysiwyg-editor';

// Define the types for content blocks
export type ContentBlock = {
  id: string;
  type: 'text' | 'image' | 'video' | 'pdf' | 'zoom-recording';
  value: string; // For text content, image URL, video URL, PDF URL, Zoom recording URL
};

interface DynamicContentEditorProps {
  initialContent: ContentBlock[];
  onContentChange: (content: ContentBlock[]) => void;
  allowImages?: boolean;
  placeholder?: string;
  contentRefs?: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>;
}

export function DynamicContentEditor({
  initialContent,
  onContentChange,
  allowImages = true,
  placeholder,
  contentRefs,
}: DynamicContentEditorProps) {
  const [content, setContent] = useState<ContentBlock[]>(initialContent);
  const [showFileDialog, setShowFileDialog] = useState(false);
  const [selectedFileType, setSelectedFileType] = useState<'image' | 'video' | 'pdf' | 'zoom-recording'>('image');
  const [linkUrl, setLinkUrl] = useState('');

  React.useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const addBlock = (type: ContentBlock['type']) => {
    const newBlock: ContentBlock = {
      id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type,
      value: '',
    };
    const updatedContent = [...content, newBlock];
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const handleFileBlockAdd = (type: 'image' | 'video' | 'pdf' | 'zoom-recording') => {
    setSelectedFileType(type);
    setShowFileDialog(true);
    setLinkUrl('');
  };

  const handleAddFromLink = () => {
    if (linkUrl.trim()) {
      const newBlock: ContentBlock = {
        id: `block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        type: selectedFileType,
        value: linkUrl.trim(),
      };
      const updatedContent = [...content, newBlock];
      setContent(updatedContent);
      onContentChange(updatedContent);
      setShowFileDialog(false);
      setLinkUrl('');
      toast.success('Block berhasil ditambahkan dari link!');
    } else {
      toast.error('Silakan masukkan URL yang valid');
    }
  };

  const handleAddFromUpload = () => {
    addBlock(selectedFileType);
    setShowFileDialog(false);
  };

  const updateBlock = (id: string, newValue: string) => {
    const updatedContent = content.map((block) =>
      block.id === id ? { ...block, value: newValue } : block
    );
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const removeBlock = (id: string) => {
    const updatedContent = content.filter((block) => block.id !== id);
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const handleFileUpload = useCallback(async (files: File[], blockId: string, fileType: 'image' | 'video' | 'pdf' | 'zoom-recording') => {
    if (!files || files.length === 0) {
      toast.error('No file selected for upload.');
      return;
    }

    const file = files[0];
    toast.info(`Uploading ${file.name}...`);

    try {
      const response = await fetch(`/api/upload?filename=${file.name}`, {
        method: 'POST',
        body: file,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const newBlob = await response.json();
      updateBlock(blockId, newBlob.url);
      toast.success(`${fileType.charAt(0).toUpperCase() + fileType.slice(1)} uploaded successfully!`);
    } catch (error) {
      console.error(`Error uploading ${fileType}:`, error);
      toast.error(`Failed to upload ${fileType}: ${(error as Error).message}`);
    }
  }, [updateBlock]);

  return (
    <div className="space-y-4">
      {content.map((block, index) => (
        <Card
          key={block.id}
          className="relative p-4 mb-4 scroll-mt-4"
          ref={(el) => {
            if (contentRefs) {
              contentRefs.current[block.id || `block-${index}`] = el;
            }
          }}
          id={block.id || `block-${index}`}
        >
          {block.type === 'text' ? (
            <div className="wysiwyg-editor">
              <WysiwygEditor
                content={block.value}
                onChange={(content: string) => updateBlock(block.id, content)}
                placeholder={placeholder || "Start typing your content..."}
              />
            </div>
          ) : block.type === 'image' ? (
            <div className="space-y-2">
              {block.value ? (
                <div className="relative w-full h-48 border rounded-md overflow-hidden">
                  <Image
                    src={block.value}
                    alt="Uploaded content"
                    layout="fill"
                    objectFit="contain"
                    className="rounded-md"
                  />
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground mb-2">Pilih cara menambahkan gambar:</div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'image/*';
                        input.onchange = (e) => {
                          const files = (e.target as HTMLInputElement).files;
                          if (files) handleFileUpload(Array.from(files), block.id, 'image');
                        };
                        input.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload File
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = prompt('Masukkan URL gambar:');
                        if (url) updateBlock(block.id, url);
                      }}
                    >
                      <Link className="h-4 w-4 mr-2" />
                      Dari Link
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : block.type === 'video' ? (
            <div className="space-y-2">
              {block.value ? (
                <video controls src={block.value} className="w-full h-auto max-h-96 rounded-md" />
              ) : (
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground mb-2">Pilih cara menambahkan video:</div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'video/*';
                        input.onchange = (e) => {
                          const files = (e.target as HTMLInputElement).files;
                          if (files) handleFileUpload(Array.from(files), block.id, 'video');
                        };
                        input.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload File
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = prompt('Masukkan URL video:');
                        if (url) updateBlock(block.id, url);
                      }}
                    >
                      <Link className="h-4 w-4 mr-2" />
                      Dari Link
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : block.type === 'pdf' ? (
            <div className="space-y-2">
              {block.value ? (
                <iframe src={block.value} className="w-full h-96 rounded-md" />
              ) : (
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground mb-2">Pilih cara menambahkan PDF:</div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'application/pdf';
                        input.onchange = (e) => {
                          const files = (e.target as HTMLInputElement).files;
                          if (files) handleFileUpload(Array.from(files), block.id, 'pdf');
                        };
                        input.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload File
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = prompt('Masukkan URL PDF:');
                        if (url) updateBlock(block.id, url);
                      }}
                    >
                      <Link className="h-4 w-4 mr-2" />
                      Dari Link
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : block.type === 'zoom-recording' ? (
            <div className="space-y-2">
              {block.value ? (
                <iframe src={block.value} className="w-full h-96 rounded-md" />
              ) : (
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground mb-2">Pilih cara menambahkan Zoom Recording:</div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = 'video/*';
                        input.onchange = (e) => {
                          const files = (e.target as HTMLInputElement).files;
                          if (files) handleFileUpload(Array.from(files), block.id, 'zoom-recording');
                        };
                        input.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload File
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = prompt('Masukkan URL Zoom Recording:');
                        if (url) updateBlock(block.id, url);
                      }}
                    >
                      <Link className="h-4 w-4 mr-2" />
                      Dari Link
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <Textarea
              placeholder={`Enter ${block.type} URL`}
              value={block.value}
              onChange={(e) => updateBlock(block.id, e.target.value)}
              rows={3}
            />
          )}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 text-muted-foreground hover:text-destructive"
            onClick={() => removeBlock(block.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </Card>
      ))}
      <div className="flex flex-wrap gap-2 pt-2">
        <Button variant="outline" onClick={() => addBlock('text')} size="sm">
          <TextIcon className="h-4 w-4 mr-2" /> Add Text Block
        </Button>
        {allowImages && (
          <>
            <Button variant="outline" onClick={() => handleFileBlockAdd('image')} size="sm">
              <ImageIcon className="h-4 w-4 mr-2" /> Add Image Block
            </Button>
            <Button variant="outline" onClick={() => handleFileBlockAdd('video')} size="sm">
              <MonitorPlayIcon className="h-4 w-4 mr-2" /> Add Video Block
            </Button>
            <Button variant="outline" onClick={() => handleFileBlockAdd('pdf')} size="sm">
              <FileTextIcon className="h-4 w-4 mr-2" /> Add PDF Block
            </Button>
            <Button variant="outline" onClick={() => handleFileBlockAdd('zoom-recording')} size="sm">
              <VideoIcon className="h-4 w-4 mr-2" /> Add Zoom Recording Block
            </Button>
          </>
        )}
      </div>

      {/* File Addition Dialog */}
      <Dialog open={showFileDialog} onOpenChange={setShowFileDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tambah {selectedFileType.charAt(0).toUpperCase() + selectedFileType.slice(1)} Block</DialogTitle>
            <DialogDescription>
              Pilih cara menambahkan {selectedFileType}: upload file atau masukkan link.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="link-url">URL {selectedFileType.charAt(0).toUpperCase() + selectedFileType.slice(1)}</Label>
              <Input
                id="link-url"
                placeholder={`Masukkan URL ${selectedFileType}...`}
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={handleAddFromUpload}>
              <Upload className="h-4 w-4 mr-2" />
              Upload File
            </Button>
            <Button onClick={handleAddFromLink} disabled={!linkUrl.trim()}>
              <Link className="h-4 w-4 mr-2" />
              Gunakan Link
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}