try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="183fd952-8a2e-4042-b315-2d7018c6a6b2",e._sentryDebugIdIdentifier="sentry-dbid-183fd952-8a2e-4042-b315-2d7018c6a6b2")}catch(e){}(()=>{var e={};e.id=1674,e.ids=[1674],e.modules={1968:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});let i=(0,a(1472).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\products\\\\components\\\\product-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\products\\components\\product-form.tsx","default")},2211:(e,t,a)=>{"use strict";function i(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return i}}),a(28171).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3621:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var a=Array.isArray(t)?t:t.split(",");if(0===a.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),r=n.replace(/\/.*$/,"");return a.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?r===t.replace(/\/.*$/,""):n===t})}return!0}},5827:(e,t,a)=>{"use strict";a.d(t,{k:()=>l});var i=a(91754),n=a(93491),r=a(66536),o=a(82233);let l=n.forwardRef(({className:e,value:t,...a},n)=>(0,i.jsx)(r.bL,{ref:n,className:(0,o.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...a,children:(0,i.jsx)(r.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));l.displayName=r.bL.displayName},8086:e=>{"use strict";e.exports=require("module")},8238:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(55732).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},9260:(e,t,a)=>{"use strict";a.d(t,{BT:()=>s,Wu:()=>c,ZB:()=>l,Zp:()=>r,aR:()=>o,wL:()=>p});var i=a(91754);a(93491);var n=a(82233);function r({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function s({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function p({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,a)=>{Promise.resolve().then(a.bind(a,7346)),Promise.resolve().then(a.bind(a,21444)),Promise.resolve().then(a.bind(a,3033)),Promise.resolve().then(a.bind(a,84436))},16069:(e,t,a)=>{"use strict";a.d(t,{E:()=>r});var i=a(18188),n=a(60340);function r({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"skeleton",className:(0,n.cn)("bg-accent animate-pulse rounded-md",e),...t,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20610:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return n}});let i=""+a(28171).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function n(){let e=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=i,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21626:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var i=a(91754);a(93491);var n=a(66207),r=a(82233);function o({className:e,...t}){return(0,i.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29392:(e,t,a)=>{Promise.resolve().then(a.bind(a,99781)),Promise.resolve().then(a.bind(a,1968))},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},39120:(e,t,a)=>{Promise.resolve().then(a.bind(a,51643)),Promise.resolve().then(a.bind(a,56184))},40582:(e,t,a)=>{"use strict";a.d(t,{BT:()=>s,Wu:()=>p,X9:()=>c,ZB:()=>l,Zp:()=>r,aR:()=>o,wL:()=>d});var i=a(18188);a(95093);var n=a(60340);function r({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function s({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function p({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46514:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return i}});let i=a(67057).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48161:e=>{"use strict";e.exports=require("node:os")},51643:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,ScrollArea:()=>o});var i=a(91754);a(93491);var n=a(43168),r=a(82233);function o({className:e,children:t,...a}){return(0,i.jsxs)(n.bL,{"data-slot":"scroll-area",className:(0,r.cn)("relative",e),...a,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,i.jsx)(n.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,i.jsx)(l,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,i.jsx)(n.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function l({className:e,orientation:t="vertical",...a}){return(0,i.jsx)(n.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,r.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...a,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,i.jsx)(n.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53891:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var i=a(18188);a(95093);var n=a(99781);function r({children:e,scrollable:t=!0}){return(0,i.jsx)(i.Fragment,{children:t?(0,i.jsx)(n.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,i.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,i.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},55511:e=>{"use strict";e.exports=require("crypto")},56184:(e,t,a)=>{"use strict";a.d(t,{default:()=>tH});var i=a(91754),n=a(8238),r=a(31619),o=a(15854),l=a(93491),s=a(16514),c=a(30670);let p=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function d(e,t,a){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let a=t.split(".").pop().toLowerCase(),i=p.get(a);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,r="string"==typeof t?t:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&u(i,"path",r),void 0!==a&&Object.defineProperty(i,"handle",{value:a,writable:!1,configurable:!1,enumerable:!0}),u(i,"relativePath",r),i}function u(e,t,a){Object.defineProperty(e,t,{value:a,writable:!1,configurable:!1,enumerable:!0})}let m=[".DS_Store","Thumbs.db"];function f(e){return"object"==typeof e&&null!==e}function v(e){return e.filter(e=>-1===m.indexOf(e.name))}function x(e){if(null===e)return[];let t=[];for(let a=0;a<e.length;a++){let i=e[a];t.push(i)}return t}function g(e){if("function"!=typeof e.webkitGetAsEntry)return y(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?b(t):y(e,t)}function y(e,t){return(0,c.sH)(this,void 0,void 0,function*(){var a;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,d(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return d(i,null!=(a=null==t?void 0:t.fullPath)?a:void 0)})}function h(e){return(0,c.sH)(this,void 0,void 0,function*(){return e.isDirectory?b(e):function(e){return(0,c.sH)(this,void 0,void 0,function*(){return new Promise((t,a)=>{e.file(a=>{t(d(a,e.fullPath))},e=>{a(e)})})})}(e)})}function b(e){let t=e.createReader();return new Promise((e,a)=>{let i=[];!function n(){t.readEntries(t=>(0,c.sH)(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(h));i.push(e),n()}else try{let t=yield Promise.all(i);e(t)}catch(e){a(e)}}),e=>{a(e)})}()})}var w=a(3621);function j(e){return function(e){if(Array.isArray(e))return D(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||E(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function _(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?k(Object(a),!0).forEach(function(t){A(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):k(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function A(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(a=n.next()).done)&&(r.push(a.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return r}}(e,t)||E(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){if(e){if("string"==typeof e)return D(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return D(e,t)}}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}var F="function"==typeof w?w:w.default,O=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),a=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(a)}},C=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},P=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},z={code:"too-many-files",message:"Too many files"};function R(e,t){var a="application/x-moz-file"===e.type||F(e,t);return[a,a?null:O(t)]}function N(e,t,a){if(V(e.size)){if(V(t)&&V(a)){if(e.size>a)return[!1,C(a)];if(e.size<t)return[!1,P(t)]}else if(V(t)&&e.size<t)return[!1,P(t)];else if(V(a)&&e.size>a)return[!1,C(a)]}return[!0,null]}function V(e){return null!=e}function q(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function T(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function M(e){e.preventDefault()}function I(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return function(e){for(var a=arguments.length,i=Array(a>1?a-1:0),n=1;n<a;n++)i[n-1]=arguments[n];return t.some(function(t){return!q(e)&&t&&t.apply(void 0,[e].concat(i)),q(e)})}}function L(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function U(e){return/^.*\.[\w]+$/.test(e)}var B=["children"],$=["open"],G=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],H=["refKey","onChange","onClick"];function W(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],o=!0,l=!1;try{for(n=n.call(e);!(o=(a=n.next()).done)&&(r.push(a.value),!t||r.length!==t);o=!0);}catch(e){l=!0,i=e}finally{try{o||null==n.return||n.return()}finally{if(l)throw i}}return r}}(e,t)||K(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function K(e,t){if(e){if("string"==typeof e)return X(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return X(e,t)}}function X(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function Z(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function Y(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(a),!0).forEach(function(t){J(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):Z(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function J(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function Q(e,t){if(null==e)return{};var a,i,n=function(e,t){if(null==e)return{};var a,i,n={},r=Object.keys(e);for(i=0;i<r.length;i++)a=r[i],t.indexOf(a)>=0||(n[a]=e[a]);return n}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(i=0;i<r.length;i++)a=r[i],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}var ee=(0,l.forwardRef)(function(e,t){var a=e.children,i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Y(Y({},et),e),a=t.accept,i=t.disabled,n=t.getFilesFromEvent,r=t.maxSize,o=t.minSize,s=t.multiple,c=t.maxFiles,p=t.onDragEnter,d=t.onDragLeave,u=t.onDragOver,m=t.onDrop,f=t.onDropAccepted,v=t.onDropRejected,x=t.onFileDialogCancel,g=t.onFileDialogOpen,y=t.useFsAccessApi,h=t.autoFocus,b=t.preventDropOnDocument,w=t.noClick,k=t.noKeyboard,E=t.noDrag,D=t.noDragEventsBubbling,F=t.onError,O=t.validator,C=(0,l.useMemo)(function(){return V(a)?Object.entries(a).reduce(function(e,t){var a=S(t,2),i=a[0],n=a[1];return[].concat(j(e),[i],j(n))},[]).filter(function(e){return L(e)||U(e)}).join(","):void 0},[a]),P=(0,l.useMemo)(function(){return V(a)?[{description:"Files",accept:Object.entries(a).filter(function(e){var t=S(e,2),a=t[0],i=t[1],n=!0;return L(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(U)||(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,t){var a=S(t,2),i=a[0],n=a[1];return _(_({},e),{},A({},i,n))},{})}]:a},[a]),B=(0,l.useMemo)(function(){return"function"==typeof g?g:en},[g]),$=(0,l.useMemo)(function(){return"function"==typeof x?x:en},[x]),Z=(0,l.useRef)(null),ee=(0,l.useRef)(null),er=W((0,l.useReducer)(ei,ea),2),eo=er[0],el=er[1],es=eo.isFocused,ec=eo.isFileDialogActive,ep=(0,l.useRef)("undefined"!=typeof window&&window.isSecureContext&&y&&"showOpenFilePicker"in window),ed=function(){!ep.current&&ec&&setTimeout(function(){ee.current&&(ee.current.files.length||(el({type:"closeDialog"}),$()))},300)};(0,l.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[ee,ec,$,ep]);var eu=(0,l.useRef)([]),em=function(e){Z.current&&Z.current.contains(e.target)||(e.preventDefault(),eu.current=[])};(0,l.useEffect)(function(){return b&&(document.addEventListener("dragover",M,!1),document.addEventListener("drop",em,!1)),function(){b&&(document.removeEventListener("dragover",M),document.removeEventListener("drop",em))}},[Z,b]),(0,l.useEffect)(function(){return!i&&h&&Z.current&&Z.current.focus(),function(){}},[Z,h,i]);var ef=(0,l.useCallback)(function(e){F?F(e):console.error(e)},[F]),ev=(0,l.useCallback)(function(e){var t;e.preventDefault(),e.persist(),eD(e),eu.current=[].concat(function(e){if(Array.isArray(e))return X(e)}(t=eu.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||K(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),T(e)&&Promise.resolve(n(e)).then(function(t){if(!q(e)||D){var a,i,n,l,d,u,m,f,v=t.length,x=v>0&&(i=(a={files:t,accept:C,minSize:o,maxSize:r,multiple:s,maxFiles:c,validator:O}).files,n=a.accept,l=a.minSize,d=a.maxSize,u=a.multiple,m=a.maxFiles,f=a.validator,(!!u||!(i.length>1))&&(!u||!(m>=1)||!(i.length>m))&&i.every(function(e){var t=S(R(e,n),1)[0],a=S(N(e,l,d),1)[0],i=f?f(e):null;return t&&a&&!i}));el({isDragAccept:x,isDragReject:v>0&&!x,isDragActive:!0,type:"setDraggedFiles"}),p&&p(e)}}).catch(function(e){return ef(e)})},[n,p,ef,D,C,o,r,s,c,O]),ex=(0,l.useCallback)(function(e){e.preventDefault(),e.persist(),eD(e);var t=T(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&u&&u(e),!1},[u,D]),eg=(0,l.useCallback)(function(e){e.preventDefault(),e.persist(),eD(e);var t=eu.current.filter(function(e){return Z.current&&Z.current.contains(e)}),a=t.indexOf(e.target);-1!==a&&t.splice(a,1),eu.current=t,!(t.length>0)&&(el({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),T(e)&&d&&d(e))},[Z,d,D]),ey=(0,l.useCallback)(function(e,t){var a=[],i=[];e.forEach(function(e){var t=W(R(e,C),2),n=t[0],l=t[1],s=W(N(e,o,r),2),c=s[0],p=s[1],d=O?O(e):null;if(n&&c&&!d)a.push(e);else{var u=[l,p];d&&(u=u.concat(d)),i.push({file:e,errors:u.filter(function(e){return e})})}}),(!s&&a.length>1||s&&c>=1&&a.length>c)&&(a.forEach(function(e){i.push({file:e,errors:[z]})}),a.splice(0)),el({acceptedFiles:a,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),m&&m(a,i,t),i.length>0&&v&&v(i,t),a.length>0&&f&&f(a,t)},[el,s,C,o,r,c,m,f,v,O]),eh=(0,l.useCallback)(function(e){e.preventDefault(),e.persist(),eD(e),eu.current=[],T(e)&&Promise.resolve(n(e)).then(function(t){(!q(e)||D)&&ey(t,e)}).catch(function(e){return ef(e)}),el({type:"reset"})},[n,ey,ef,D]),eb=(0,l.useCallback)(function(){if(ep.current){el({type:"openDialog"}),B(),window.showOpenFilePicker({multiple:s,types:P}).then(function(e){return n(e)}).then(function(e){ey(e,null),el({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?($(e),el({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(ep.current=!1,ee.current?(ee.current.value=null,ee.current.click()):ef(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ef(e)});return}ee.current&&(el({type:"openDialog"}),B(),ee.current.value=null,ee.current.click())},[el,B,$,y,ey,ef,P,s]),ew=(0,l.useCallback)(function(e){Z.current&&Z.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),eb())},[Z,eb]),ej=(0,l.useCallback)(function(){el({type:"focus"})},[]),ek=(0,l.useCallback)(function(){el({type:"blur"})},[]),e_=(0,l.useCallback)(function(){w||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(eb,0):eb())},[w,eb]),eA=function(e){return i?null:e},eS=function(e){return k?null:eA(e)},eE=function(e){return E?null:eA(e)},eD=function(e){D&&e.stopPropagation()},eF=(0,l.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.role,n=e.onKeyDown,r=e.onFocus,o=e.onBlur,l=e.onClick,s=e.onDragEnter,c=e.onDragOver,p=e.onDragLeave,d=e.onDrop,u=Q(e,G);return Y(Y(J({onKeyDown:eS(I(n,ew)),onFocus:eS(I(r,ej)),onBlur:eS(I(o,ek)),onClick:eA(I(l,e_)),onDragEnter:eE(I(s,ev)),onDragOver:eE(I(c,ex)),onDragLeave:eE(I(p,eg)),onDrop:eE(I(d,eh)),role:"string"==typeof a&&""!==a?a:"presentation"},void 0===t?"ref":t,Z),i||k?{}:{tabIndex:0}),u)}},[Z,ew,ej,ek,e_,ev,ex,eg,eh,k,E,i]),eO=(0,l.useCallback)(function(e){e.stopPropagation()},[]),eC=(0,l.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.onChange,i=e.onClick,n=Q(e,H);return Y(Y({},J({accept:C,multiple:s,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eA(I(a,eh)),onClick:eA(I(i,eO)),tabIndex:-1},void 0===t?"ref":t,ee)),n)}},[ee,a,s,eh,i]);return Y(Y({},eo),{},{isFocused:es&&!i,getRootProps:eF,getInputProps:eC,rootRef:Z,inputRef:ee,open:eA(eb)})}(Q(e,B)),n=i.open,r=Q(i,$);return(0,l.useImperativeHandle)(t,function(){return{open:n}},[n]),l.createElement(l.Fragment,null,a(Y(Y({},r),{},{open:n})))});ee.displayName="Dropzone";var et={disabled:!1,getFilesFromEvent:function(e){return(0,c.sH)(this,void 0,void 0,function*(){var t;if(f(e)&&f(e.dataTransfer))return function(e,t){return(0,c.sH)(this,void 0,void 0,function*(){if(e.items){let a=x(e.items).filter(e=>"file"===e.kind);return"drop"!==t?a:v(function e(t){return t.reduce((t,a)=>[...t,...Array.isArray(a)?e(a):[a]],[])}((yield Promise.all(a.map(g)))))}return v(x(e.files).map(e=>d(e)))})}(e.dataTransfer,e.type);if(f(t=e)&&f(t.target))return x(e.target.files).map(e=>d(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return(0,c.sH)(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>d(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};ee.defaultProps=et,ee.propTypes={children:s.func,accept:s.objectOf(s.arrayOf(s.string)),multiple:s.bool,preventDropOnDocument:s.bool,noClick:s.bool,noKeyboard:s.bool,noDrag:s.bool,noDragEventsBubbling:s.bool,minSize:s.number,maxSize:s.number,maxFiles:s.number,disabled:s.bool,getFilesFromEvent:s.func,onFileDialogCancel:s.func,onFileDialogOpen:s.func,useFsAccessApi:s.bool,autoFocus:s.bool,onDragEnter:s.func,onDragLeave:s.func,onDragOver:s.func,onDrop:s.func,onDropAccepted:s.func,onDropRejected:s.func,onError:s.func,validator:s.func};var ea={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ei(e,t){switch(t.type){case"focus":return Y(Y({},e),{},{isFocused:!0});case"blur":return Y(Y({},e),{},{isFocused:!1});case"openDialog":return Y(Y({},ea),{},{isFileDialogActive:!0});case"closeDialog":return Y(Y({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return Y(Y({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return Y(Y({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return Y({},ea);default:return e}}function en(){}var er=a(81012),eo=a(56682),el=a(5827),es=a(51643),ec=a(88914),ep=a(82233);function ed(e){let{value:t,onValueChange:a,onUpload:r,progresses:o,accept:s={"image/*":[]},maxSize:c=2097152,maxFiles:p=1,multiple:d=!1,disabled:u=!1,className:m,...f}=e,[v,x]=function({prop:e,defaultProp:t,onChange:a=()=>{}}){let[i,n]=function({defaultProp:e,onChange:t}){let a=l.useState(e),[i]=a,n=l.useRef(i),r=(0,ec.c)(t);return l.useEffect(()=>{n.current!==i&&(r(i),n.current=i)},[i,n,r]),a}({defaultProp:t,onChange:a}),r=void 0!==e,o=r?e:i,s=(0,ec.c)(a);return[o,l.useCallback(t=>{if(r){let a="function"==typeof t?t(e):t;a!==e&&s(a)}else n(t)},[r,e,n,s])]}({prop:t,onChange:a}),g=l.useCallback((e,t)=>{if(!d&&1===p&&e.length>1)return void er.oR.error("Cannot upload more than 1 file at a time");if((v?.length??0)+e.length>p)return void er.oR.error(`Cannot upload more than ${p} files`);let a=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)})),i=v?[...v,...a]:a;if(x(i),t.length>0&&t.forEach(({file:e})=>{er.oR.error(`File ${e.name} was rejected`)}),r&&i.length>0&&i.length<=p){let e=i.length>0?`${i.length} files`:"file";er.oR.promise(r(i),{loading:`Uploading ${e}...`,success:()=>(x([]),`${e} uploaded`),error:`Failed to upload ${e}`})}},[v,p,d,r,x]);l.useEffect(()=>()=>{v&&v.forEach(e=>{em(e)&&URL.revokeObjectURL(e.preview)})},[]);let y=u||(v?.length??0)>=p;return(0,i.jsxs)("div",{className:"relative flex flex-col gap-6 overflow-hidden","data-sentry-component":"FileUploader","data-sentry-source-file":"file-uploader.tsx",children:[(0,i.jsx)(ee,{onDrop:g,accept:s,maxSize:c,maxFiles:p,multiple:p>1||d,disabled:y,"data-sentry-element":"Dropzone","data-sentry-source-file":"file-uploader.tsx",children:({getRootProps:e,getInputProps:t,isDragActive:a})=>(0,i.jsxs)("div",{...e(),className:(0,ep.cn)("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden",a&&"border-muted-foreground/50",y&&"pointer-events-none opacity-60",m),...f,children:[(0,i.jsx)("input",{...t()}),a?(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[(0,i.jsx)("div",{className:"rounded-full border border-dashed p-3",children:(0,i.jsx)(n.A,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),(0,i.jsx)("p",{className:"text-muted-foreground font-medium",children:"Drop the files here"})]}):(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[(0,i.jsx)("div",{className:"rounded-full border border-dashed p-3",children:(0,i.jsx)(n.A,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),(0,i.jsxs)("div",{className:"space-y-px",children:[(0,i.jsxs)("p",{className:"text-muted-foreground font-medium",children:["Drag ","'n'"," drop files here, or click to select files"]}),(0,i.jsxs)("p",{className:"text-muted-foreground/70 text-sm",children:["You can upload",p>1?` ${p===1/0?"multiple":p}
                      files (up to ${(0,ep.z)(c)} each)`:` a file with ${(0,ep.z)(c)}`]})]})]})]})}),v?.length?(0,i.jsx)(es.ScrollArea,{className:"h-fit w-full px-3",children:(0,i.jsx)("div",{className:"max-h-48 space-y-4",children:v?.map((e,t)=>(0,i.jsx)(eu,{file:e,onRemove:()=>(function(e){if(!v)return;let t=v.filter((t,a)=>a!==e);x(t),a?.(t)})(t),progress:o?.[e.name]},t))})}):null]})}function eu({file:e,progress:t,onRemove:a}){return(0,i.jsxs)("div",{className:"relative flex items-center space-x-4","data-sentry-component":"FileCard","data-sentry-source-file":"file-uploader.tsx",children:[(0,i.jsxs)("div",{className:"flex flex-1 space-x-4",children:[em(e)?(0,i.jsx)(o.default,{src:e.preview,alt:e.name,width:48,height:48,loading:"lazy",className:"aspect-square shrink-0 rounded-md object-cover"}):null,(0,i.jsxs)("div",{className:"flex w-full flex-col gap-2",children:[(0,i.jsxs)("div",{className:"space-y-px",children:[(0,i.jsx)("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),(0,i.jsx)("p",{className:"text-muted-foreground text-xs",children:(0,ep.z)(e.size)})]}),t?(0,i.jsx)(el.k,{value:t}):null]})]}),(0,i.jsx)("div",{className:"flex items-center gap-2",children:(0,i.jsxs)(eo.$,{type:"button",variant:"ghost",size:"icon",onClick:a,disabled:void 0!==t&&t<100,className:"size-8 rounded-full","data-sentry-element":"Button","data-sentry-source-file":"file-uploader.tsx",children:[(0,i.jsx)(r.A,{className:"text-muted-foreground","data-sentry-element":"X","data-sentry-source-file":"file-uploader.tsx"}),(0,i.jsx)("span",{className:"sr-only",children:"Remove file"})]})})]})}function em(e){return"preview"in e&&"string"==typeof e.preview}var ef=a(9260),ev=a(16435),ex=e=>"checkbox"===e.type,eg=e=>e instanceof Date,ey=e=>null==e;let eh=e=>"object"==typeof e;var eb=e=>!ey(e)&&!Array.isArray(e)&&eh(e)&&!eg(e),ew=e=>eb(e)&&e.target?ex(e.target)?e.target.checked:e.target.value:e,ej=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,ek=(e,t)=>e.has(ej(t)),e_=e=>{let t=e.constructor&&e.constructor.prototype;return eb(t)&&t.hasOwnProperty("isPrototypeOf")},eA="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function eS(e){let t,a=Array.isArray(e),i="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(!(!(eA&&(e instanceof Blob||i))&&(a||eb(e))))return e;else if(t=a?[]:Object.create(Object.getPrototypeOf(e)),a||e_(e))for(let a in e)e.hasOwnProperty(a)&&(t[a]=eS(e[a]));else t=e;return t}var eE=e=>/^\w*$/.test(e),eD=e=>void 0===e,eF=e=>Array.isArray(e)?e.filter(Boolean):[],eO=e=>eF(e.replace(/["|']|\]/g,"").split(/\.|\[/)),eC=(e,t,a)=>{if(!t||!eb(e))return a;let i=(eE(t)?[t]:eO(t)).reduce((e,t)=>ey(e)?e:e[t],e);return eD(i)||i===e?eD(e[t])?a:e[t]:i},eP=e=>"boolean"==typeof e,ez=(e,t,a)=>{let i=-1,n=eE(t)?[t]:eO(t),r=n.length,o=r-1;for(;++i<r;){let t=n[i],r=a;if(i!==o){let a=e[t];r=eb(a)||Array.isArray(a)?a:isNaN(+n[i+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=r,e=e[t]}};let eR={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},eN={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},eV={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},eq=l.createContext(null);eq.displayName="HookFormContext";let eT=()=>l.useContext(eq);var eM=(e,t,a,i=!0)=>{let n={defaultValues:t._defaultValues};for(let r in e)Object.defineProperty(n,r,{get:()=>(t._proxyFormState[r]!==eN.all&&(t._proxyFormState[r]=!i||eN.all),a&&(a[r]=!0),e[r])});return n};let eI="undefined"!=typeof window?l.useLayoutEffect:l.useEffect;function eL(e){let t=eT(),{control:a=t.control,disabled:i,name:n,exact:r}=e||{},[o,s]=l.useState(a._formState),c=l.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return eI(()=>a._subscribe({name:n,formState:c.current,exact:r,callback:e=>{i||s({...a._formState,...e})}}),[n,i,r]),l.useEffect(()=>{c.current.isValid&&a._setValid(!0)},[a]),l.useMemo(()=>eM(o,a,c.current,!1),[o,a])}var eU=e=>"string"==typeof e,eB=(e,t,a,i,n)=>eU(e)?(i&&t.watch.add(e),eC(a,e,n)):Array.isArray(e)?e.map(e=>(i&&t.watch.add(e),eC(a,e))):(i&&(t.watchAll=!0),a),e$=e=>ey(e)||!eh(e);function eG(e,t,a=new WeakSet){if(e$(e)||e$(t))return e===t;if(eg(e)&&eg(t))return e.getTime()===t.getTime();let i=Object.keys(e),n=Object.keys(t);if(i.length!==n.length)return!1;if(a.has(e)||a.has(t))return!0;for(let r of(a.add(e),a.add(t),i)){let i=e[r];if(!n.includes(r))return!1;if("ref"!==r){let e=t[r];if(eg(i)&&eg(e)||eb(i)&&eb(e)||Array.isArray(i)&&Array.isArray(e)?!eG(i,e,a):i!==e)return!1}}return!0}let eH=e=>e.render(function(e){let t=eT(),{name:a,disabled:i,control:n=t.control,shouldUnregister:r,defaultValue:o}=e,s=ek(n._names.array,a),c=l.useMemo(()=>eC(n._formValues,a,eC(n._defaultValues,a,o)),[n,a,o]),p=function(e){let t=eT(),{control:a=t.control,name:i,defaultValue:n,disabled:r,exact:o,compute:s}=e||{},c=l.useRef(n),p=l.useRef(s),d=l.useRef(void 0);p.current=s;let u=l.useMemo(()=>a._getWatch(i,c.current),[a,i]),[m,f]=l.useState(p.current?p.current(u):u);return eI(()=>a._subscribe({name:i,formState:{values:!0},exact:o,callback:e=>{if(!r){let t=eB(i,a._names,e.values||a._formValues,!1,c.current);if(p.current){let e=p.current(t);eG(e,d.current)||(f(e),d.current=e)}else f(t)}}}),[a,r,i,o]),l.useEffect(()=>a._removeUnmounted()),m}({control:n,name:a,defaultValue:c,exact:!0}),d=eL({control:n,name:a,exact:!0}),u=l.useRef(e),m=l.useRef(n.register(a,{...e.rules,value:p,...eP(e.disabled)?{disabled:e.disabled}:{}}));u.current=e;let f=l.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!eC(d.errors,a)},isDirty:{enumerable:!0,get:()=>!!eC(d.dirtyFields,a)},isTouched:{enumerable:!0,get:()=>!!eC(d.touchedFields,a)},isValidating:{enumerable:!0,get:()=>!!eC(d.validatingFields,a)},error:{enumerable:!0,get:()=>eC(d.errors,a)}}),[d,a]),v=l.useCallback(e=>m.current.onChange({target:{value:ew(e),name:a},type:eR.CHANGE}),[a]),x=l.useCallback(()=>m.current.onBlur({target:{value:eC(n._formValues,a),name:a},type:eR.BLUR}),[a,n._formValues]),g=l.useCallback(e=>{let t=eC(n._fields,a);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[n._fields,a]),y=l.useMemo(()=>({name:a,value:p,...eP(i)||d.disabled?{disabled:d.disabled||i}:{},onChange:v,onBlur:x,ref:g}),[a,i,d.disabled,v,x,g,p]);return l.useEffect(()=>{let e=n._options.shouldUnregister||r;n.register(a,{...u.current.rules,...eP(u.current.disabled)?{disabled:u.current.disabled}:{}});let t=(e,t)=>{let a=eC(n._fields,e);a&&a._f&&(a._f.mount=t)};if(t(a,!0),e){let e=eS(eC(n._options.defaultValues,a));ez(n._defaultValues,a,e),eD(eC(n._formValues,a))&&ez(n._formValues,a,e)}return s||n.register(a),()=>{(s?e&&!n._state.action:e)?n.unregister(a):t(a,!1)}},[a,n,s,r]),l.useEffect(()=>{n._setDisabledField({disabled:i,name:a})},[i,a,n]),l.useMemo(()=>({field:y,formState:d,fieldState:f}),[y,d,f])}(e));var eW=(e,t,a,i,n)=>t?{...a[e],types:{...a[e]&&a[e].types?a[e].types:{},[i]:n||!0}}:{},eK=e=>Array.isArray(e)?e:[e],eX=()=>{let e=[];return{get observers(){return e},next:t=>{for(let a of e)a.next&&a.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},eZ=e=>eb(e)&&!Object.keys(e).length,eY=e=>"file"===e.type,eJ=e=>"function"==typeof e,eQ=e=>{if(!eA)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},e0=e=>"select-multiple"===e.type,e1=e=>"radio"===e.type,e2=e=>e1(e)||ex(e),e3=e=>eQ(e)&&e.isConnected;function e4(e,t){let a=Array.isArray(t)?t:eE(t)?[t]:eO(t),i=1===a.length?e:function(e,t){let a=t.slice(0,-1).length,i=0;for(;i<a;)e=eD(e)?i++:e[t[i++]];return e}(e,a),n=a.length-1,r=a[n];return i&&delete i[r],0!==n&&(eb(i)&&eZ(i)||Array.isArray(i)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!eD(e[t]))return!1;return!0}(i))&&e4(e,a.slice(0,-1)),e}var e6=e=>{for(let t in e)if(eJ(e[t]))return!0;return!1};function e8(e,t={}){let a=Array.isArray(e);if(eb(e)||a)for(let a in e)Array.isArray(e[a])||eb(e[a])&&!e6(e[a])?(t[a]=Array.isArray(e[a])?[]:{},e8(e[a],t[a])):ey(e[a])||(t[a]=!0);return t}var e7=(e,t)=>(function e(t,a,i){let n=Array.isArray(t);if(eb(t)||n)for(let n in t)Array.isArray(t[n])||eb(t[n])&&!e6(t[n])?eD(a)||e$(i[n])?i[n]=Array.isArray(t[n])?e8(t[n],[]):{...e8(t[n])}:e(t[n],ey(a)?{}:a[n],i[n]):i[n]=!eG(t[n],a[n]);return i})(e,t,e8(t));let e5={value:!1,isValid:!1},e9={value:!0,isValid:!0};var te=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!eD(e[0].attributes.value)?eD(e[0].value)||""===e[0].value?e9:{value:e[0].value,isValid:!0}:e9:e5}return e5},tt=(e,{valueAsNumber:t,valueAsDate:a,setValueAs:i})=>eD(e)?e:t?""===e?NaN:e?+e:e:a&&eU(e)?new Date(e):i?i(e):e;let ta={isValid:!1,value:null};var ti=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ta):ta;function tn(e){let t=e.ref;return eY(t)?t.files:e1(t)?ti(e.refs).value:e0(t)?[...t.selectedOptions].map(({value:e})=>e):ex(t)?te(e.refs).value:tt(eD(t.value)?e.ref.value:t.value,e)}var tr=(e,t,a,i)=>{let n={};for(let a of e){let e=eC(t,a);e&&ez(n,a,e._f)}return{criteriaMode:a,names:[...e],fields:n,shouldUseNativeValidation:i}},to=e=>e instanceof RegExp,tl=e=>eD(e)?e:to(e)?e.source:eb(e)?to(e.value)?e.value.source:e.value:e,ts=e=>({isOnSubmit:!e||e===eN.onSubmit,isOnBlur:e===eN.onBlur,isOnChange:e===eN.onChange,isOnAll:e===eN.all,isOnTouch:e===eN.onTouched});let tc="AsyncFunction";var tp=e=>!!e&&!!e.validate&&!!(eJ(e.validate)&&e.validate.constructor.name===tc||eb(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===tc)),td=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),tu=(e,t,a)=>!a&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let tm=(e,t,a,i)=>{for(let n of a||Object.keys(e)){let a=eC(e,n);if(a){let{_f:e,...r}=a;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],n)&&!i)return!0;else if(e.ref&&t(e.ref,e.name)&&!i)return!0;else if(tm(r,t))break}else if(eb(r)&&tm(r,t))break}}};function tf(e,t,a){let i=eC(e,a);if(i||eE(a))return{error:i,name:a};let n=a.split(".");for(;n.length;){let i=n.join("."),r=eC(t,i),o=eC(e,i);if(r&&!Array.isArray(r)&&a!==i)break;if(o&&o.type)return{name:i,error:o};if(o&&o.root&&o.root.type)return{name:`${i}.root`,error:o.root};n.pop()}return{name:a}}var tv=(e,t,a,i)=>{a(e);let{name:n,...r}=e;return eZ(r)||Object.keys(r).length>=Object.keys(t).length||Object.keys(r).find(e=>t[e]===(!i||eN.all))},tx=(e,t,a)=>!e||!t||e===t||eK(e).some(e=>e&&(a?e===t:e.startsWith(t)||t.startsWith(e))),tg=(e,t,a,i,n)=>!n.isOnAll&&(!a&&n.isOnTouch?!(t||e):(a?i.isOnBlur:n.isOnBlur)?!e:(a?!i.isOnChange:!n.isOnChange)||e),ty=(e,t)=>!eF(eC(e,t)).length&&e4(e,t),th=(e,t,a)=>{let i=eK(eC(e,a));return ez(i,"root",t[a]),ez(e,a,i),e},tb=e=>eU(e);function tw(e,t,a="validate"){if(tb(e)||Array.isArray(e)&&e.every(tb)||eP(e)&&!e)return{type:a,message:tb(e)?e:"",ref:t}}var tj=e=>eb(e)&&!to(e)?e:{value:e,message:""},tk=async(e,t,a,i,n,r)=>{let{ref:o,refs:l,required:s,maxLength:c,minLength:p,min:d,max:u,pattern:m,validate:f,name:v,valueAsNumber:x,mount:g}=e._f,y=eC(a,v);if(!g||t.has(v))return{};let h=l?l[0]:o,b=e=>{n&&h.reportValidity&&(h.setCustomValidity(eP(e)?"":e||""),h.reportValidity())},w={},j=e1(o),k=ex(o),_=(x||eY(o))&&eD(o.value)&&eD(y)||eQ(o)&&""===o.value||""===y||Array.isArray(y)&&!y.length,A=eW.bind(null,v,i,w),S=(e,t,a,i=eV.maxLength,n=eV.minLength)=>{let r=e?t:a;w[v]={type:e?i:n,message:r,ref:o,...A(e?i:n,r)}};if(r?!Array.isArray(y)||!y.length:s&&(!(j||k)&&(_||ey(y))||eP(y)&&!y||k&&!te(l).isValid||j&&!ti(l).isValid)){let{value:e,message:t}=tb(s)?{value:!!s,message:s}:tj(s);if(e&&(w[v]={type:eV.required,message:t,ref:h,...A(eV.required,t)},!i))return b(t),w}if(!_&&(!ey(d)||!ey(u))){let e,t,a=tj(u),n=tj(d);if(ey(y)||isNaN(y)){let i=o.valueAsDate||new Date(y),r=e=>new Date(new Date().toDateString()+" "+e),l="time"==o.type,s="week"==o.type;eU(a.value)&&y&&(e=l?r(y)>r(a.value):s?y>a.value:i>new Date(a.value)),eU(n.value)&&y&&(t=l?r(y)<r(n.value):s?y<n.value:i<new Date(n.value))}else{let i=o.valueAsNumber||(y?+y:y);ey(a.value)||(e=i>a.value),ey(n.value)||(t=i<n.value)}if((e||t)&&(S(!!e,a.message,n.message,eV.max,eV.min),!i))return b(w[v].message),w}if((c||p)&&!_&&(eU(y)||r&&Array.isArray(y))){let e=tj(c),t=tj(p),a=!ey(e.value)&&y.length>+e.value,n=!ey(t.value)&&y.length<+t.value;if((a||n)&&(S(a,e.message,t.message),!i))return b(w[v].message),w}if(m&&!_&&eU(y)){let{value:e,message:t}=tj(m);if(to(e)&&!y.match(e)&&(w[v]={type:eV.pattern,message:t,ref:o,...A(eV.pattern,t)},!i))return b(t),w}if(f){if(eJ(f)){let e=tw(await f(y,a),h);if(e&&(w[v]={...e,...A(eV.validate,e.message)},!i))return b(e.message),w}else if(eb(f)){let e={};for(let t in f){if(!eZ(e)&&!i)break;let n=tw(await f[t](y,a),h,t);n&&(e={...n,...A(t,n.message)},b(n.message),i&&(w[v]=e))}if(!eZ(e)&&(w[v]={ref:h,...e},!i))return w}}return b(!0),w};let t_={mode:eN.onSubmit,reValidateMode:eN.onChange,shouldFocusError:!0};var tA=a(21626);let tS=e=>{let{children:t,...a}=e;return l.createElement(eq.Provider,{value:a},t)},tE=l.createContext({}),tD=({...e})=>(0,i.jsx)(tE.Provider,{value:{name:e.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,i.jsx)(eH,{...e,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})}),tF=()=>{let e=l.useContext(tE),t=l.useContext(tO),{getFieldState:a}=eT(),i=eL({name:e.name}),n=a(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:r}=t;return{id:r,name:e.name,formItemId:`${r}-form-item`,formDescriptionId:`${r}-form-item-description`,formMessageId:`${r}-form-item-message`,...n}},tO=l.createContext({});function tC({className:e,...t}){let a=l.useId();return(0,i.jsx)(tO.Provider,{value:{id:a},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,i.jsx)("div",{"data-slot":"form-item",className:(0,ep.cn)("grid gap-2",e),...t})})}function tP({className:e,...t}){let{error:a,formItemId:n}=tF();return(0,i.jsx)(tA.J,{"data-slot":"form-label","data-error":!!a,className:(0,ep.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...t,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function tz({...e}){let{error:t,formItemId:a,formDescriptionId:n,formMessageId:r}=tF();return(0,i.jsx)(ev.DX,{"data-slot":"form-control",id:a,"aria-describedby":t?`${n} ${r}`:`${n}`,"aria-invalid":!!t,...e,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function tR({className:e,...t}){let{error:a,formMessageId:n}=tF(),r=a?String(a?.message??""):t.children;return r?(0,i.jsx)("p",{"data-slot":"form-message",id:n,className:(0,ep.cn)("text-destructive text-sm",e),...t,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:r}):null}var tN=a(59672),tV=a(69122),tq=a(58428);let tT=(e,t,a)=>{if(e&&"reportValidity"in e){let i=eC(a,t);e.setCustomValidity(i&&i.message||""),e.reportValidity()}},tM=(e,t)=>{for(let a in t.fields){let i=t.fields[a];i&&i.ref&&"reportValidity"in i.ref?tT(i.ref,a,e):i.refs&&i.refs.forEach(t=>tT(t,a,e))}},tI=(e,t)=>{t.shouldUseNativeValidation&&tM(e,t);let a={};for(let i in e){let n=eC(t.fields,i),r=Object.assign(e[i]||{},{ref:n&&n.ref});if(tL(t.names||Object.keys(e),i)){let e=Object.assign({},eC(a,i));ez(e,"root",r),ez(a,i,e)}else ez(a,i,r)}return a},tL=(e,t)=>e.some(e=>e.startsWith(t+"."));var tU=function(e,t){for(var a={};e.length;){var i=e[0],n=i.code,r=i.message,o=i.path.join(".");if(!a[o])if("unionErrors"in i){var l=i.unionErrors[0].errors[0];a[o]={message:l.message,type:l.code}}else a[o]={message:r,type:n};if("unionErrors"in i&&i.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var s=a[o].types,c=s&&s[i.code];a[o]=eW(o,t,a,n,c?[].concat(c,i.message):i.message)}e.shift()}return a},tB=a(75121);let t$=["image/jpeg","image/jpg","image/png","image/webp"],tG=tB.Ik({image:tB.bz().refine(e=>e?.length==1,"Image is required.").refine(e=>e?.[0]?.size<=5e6,"Max file size is 5MB.").refine(e=>t$.includes(e?.[0]?.type),".jpg, .jpeg, .png and .webp files are accepted."),name:tB.Yj().min(2,{message:"Product name must be at least 2 characters."}),category:tB.Yj(),price:tB.ai(),description:tB.Yj().min(10,{message:"Description must be at least 10 characters."})});function tH({initialData:e,pageTitle:t}){var a;let n={name:e?.name||"",category:e?.category||"",price:e?.price||0,description:e?.description||""},r=function(e={}){let t=l.useRef(void 0),a=l.useRef(void 0),[i,n]=l.useState({isDirty:!1,isValidating:!1,isLoading:eJ(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:eJ(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:i},e.defaultValues&&!eJ(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:a,...n}=function(e={}){let t,a={...t_,...e},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:eJ(a.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1},n={},r=(eb(a.defaultValues)||eb(a.values))&&eS(a.defaultValues||a.values)||{},o=a.shouldUnregister?{}:eS(r),l={action:!1,mount:!1,watch:!1},s={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c=0,p={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},d={...p},u={array:eX(),state:eX()},m=a.criteriaMode===eN.all,f=e=>t=>{clearTimeout(c),c=setTimeout(e,t)},v=async e=>{if(!a.disabled&&(p.isValid||d.isValid||e)){let e=a.resolver?eZ((await w()).errors):await k(n,!0);e!==i.isValid&&u.state.next({isValid:e})}},x=(e,t)=>{!a.disabled&&(p.isValidating||p.validatingFields||d.isValidating||d.validatingFields)&&((e||Array.from(s.mount)).forEach(e=>{e&&(t?ez(i.validatingFields,e,t):e4(i.validatingFields,e))}),u.state.next({validatingFields:i.validatingFields,isValidating:!eZ(i.validatingFields)}))},g=(e,t)=>{ez(i.errors,e,t),u.state.next({errors:i.errors})},y=(e,t,a,i)=>{let s=eC(n,e);if(s){let n=eC(o,e,eD(a)?eC(r,e):a);eD(n)||i&&i.defaultChecked||t?ez(o,e,t?n:tn(s._f)):S(e,n),l.mount&&v()}},h=(e,t,n,o,l)=>{let s=!1,c=!1,m={name:e};if(!a.disabled){if(!n||o){(p.isDirty||d.isDirty)&&(c=i.isDirty,i.isDirty=m.isDirty=_(),s=c!==m.isDirty);let a=eG(eC(r,e),t);c=!!eC(i.dirtyFields,e),a?e4(i.dirtyFields,e):ez(i.dirtyFields,e,!0),m.dirtyFields=i.dirtyFields,s=s||(p.dirtyFields||d.dirtyFields)&&!a!==c}if(n){let t=eC(i.touchedFields,e);t||(ez(i.touchedFields,e,n),m.touchedFields=i.touchedFields,s=s||(p.touchedFields||d.touchedFields)&&t!==n)}s&&l&&u.state.next(m)}return s?m:{}},b=(e,n,r,o)=>{let l=eC(i.errors,e),s=(p.isValid||d.isValid)&&eP(n)&&i.isValid!==n;if(a.delayError&&r?(t=f(()=>g(e,r)))(a.delayError):(clearTimeout(c),t=null,r?ez(i.errors,e,r):e4(i.errors,e)),(r?!eG(l,r):l)||!eZ(o)||s){let t={...o,...s&&eP(n)?{isValid:n}:{},errors:i.errors,name:e};i={...i,...t},u.state.next(t)}},w=async e=>{x(e,!0);let t=await a.resolver(o,a.context,tr(e||s.mount,n,a.criteriaMode,a.shouldUseNativeValidation));return x(e),t},j=async e=>{let{errors:t}=await w(e);if(e)for(let a of e){let e=eC(t,a);e?ez(i.errors,a,e):e4(i.errors,a)}else i.errors=t;return t},k=async(e,t,n={valid:!0})=>{for(let r in e){let l=e[r];if(l){let{_f:e,...c}=l;if(e){let c=s.array.has(e.name),d=l._f&&tp(l._f);d&&p.validatingFields&&x([r],!0);let u=await tk(l,s.disabled,o,m,a.shouldUseNativeValidation&&!t,c);if(d&&p.validatingFields&&x([r]),u[e.name]&&(n.valid=!1,t))break;t||(eC(u,e.name)?c?th(i.errors,u,e.name):ez(i.errors,e.name,u[e.name]):e4(i.errors,e.name))}eZ(c)||await k(c,t,n)}}return n.valid},_=(e,t)=>!a.disabled&&(e&&t&&ez(o,e,t),!eG(P(),r)),A=(e,t,a)=>eB(e,s,{...l.mount?o:eD(t)?r:eU(e)?{[e]:t}:t},a,t),S=(e,t,a={})=>{let i=eC(n,e),r=t;if(i){let a=i._f;a&&(a.disabled||ez(o,e,tt(t,a)),r=eQ(a.ref)&&ey(t)?"":t,e0(a.ref)?[...a.ref.options].forEach(e=>e.selected=r.includes(e.value)):a.refs?ex(a.ref)?a.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(r)?e.checked=!!r.find(t=>t===e.value):e.checked=r===e.value||!!r)}):a.refs.forEach(e=>e.checked=e.value===r):eY(a.ref)?a.ref.value="":(a.ref.value=r,a.ref.type||u.state.next({name:e,values:eS(o)})))}(a.shouldDirty||a.shouldTouch)&&h(e,r,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&C(e)},E=(e,t,a)=>{for(let i in t){if(!t.hasOwnProperty(i))return;let r=t[i],o=e+"."+i,l=eC(n,o);(s.array.has(e)||eb(r)||l&&!l._f)&&!eg(r)?E(o,r,a):S(o,r,a)}},D=(e,t,a={})=>{let c=eC(n,e),m=s.array.has(e),f=eS(t);ez(o,e,f),m?(u.array.next({name:e,values:eS(o)}),(p.isDirty||p.dirtyFields||d.isDirty||d.dirtyFields)&&a.shouldDirty&&u.state.next({name:e,dirtyFields:e7(r,o),isDirty:_(e,f)})):!c||c._f||ey(f)?S(e,f,a):E(e,f,a),tu(e,s)&&u.state.next({...i,name:e}),u.state.next({name:l.mount?e:void 0,values:eS(o)})},F=async e=>{l.mount=!0;let r=e.target,c=r.name,f=!0,g=eC(n,c),y=e=>{f=Number.isNaN(e)||eg(e)&&isNaN(e.getTime())||eG(e,eC(o,c,e))},j=ts(a.mode),_=ts(a.reValidateMode);if(g){let l,A,S=r.type?tn(g._f):ew(e),E=e.type===eR.BLUR||e.type===eR.FOCUS_OUT,D=!td(g._f)&&!a.resolver&&!eC(i.errors,c)&&!g._f.deps||tg(E,eC(i.touchedFields,c),i.isSubmitted,_,j),F=tu(c,s,E);ez(o,c,S),E?r&&r.readOnly||(g._f.onBlur&&g._f.onBlur(e),t&&t(0)):g._f.onChange&&g._f.onChange(e);let O=h(c,S,E),P=!eZ(O)||F;if(E||u.state.next({name:c,type:e.type,values:eS(o)}),D)return(p.isValid||d.isValid)&&("onBlur"===a.mode?E&&v():E||v()),P&&u.state.next({name:c,...F?{}:O});if(!E&&F&&u.state.next({...i}),a.resolver){let{errors:e}=await w([c]);if(y(S),f){let t=tf(i.errors,n,c),a=tf(e,n,t.name||c);l=a.error,c=a.name,A=eZ(e)}}else x([c],!0),l=(await tk(g,s.disabled,o,m,a.shouldUseNativeValidation))[c],x([c]),y(S),f&&(l?A=!1:(p.isValid||d.isValid)&&(A=await k(n,!0)));f&&(g._f.deps&&C(g._f.deps),b(c,A,l,O))}},O=(e,t)=>{if(eC(i.errors,t)&&e.focus)return e.focus(),1},C=async(e,t={})=>{let r,o,l=eK(e);if(a.resolver){let t=await j(eD(e)?e:l);r=eZ(t),o=e?!l.some(e=>eC(t,e)):r}else e?((o=(await Promise.all(l.map(async e=>{let t=eC(n,e);return await k(t&&t._f?{[e]:t}:t)}))).every(Boolean))||i.isValid)&&v():o=r=await k(n);return u.state.next({...!eU(e)||(p.isValid||d.isValid)&&r!==i.isValid?{}:{name:e},...a.resolver||!e?{isValid:r}:{},errors:i.errors}),t.shouldFocus&&!o&&tm(n,O,e?l:s.mount),o},P=e=>{let t={...l.mount?o:r};return eD(e)?t:eU(e)?eC(t,e):e.map(e=>eC(t,e))},z=(e,t)=>({invalid:!!eC((t||i).errors,e),isDirty:!!eC((t||i).dirtyFields,e),error:eC((t||i).errors,e),isValidating:!!eC(i.validatingFields,e),isTouched:!!eC((t||i).touchedFields,e)}),R=(e,t,a)=>{let r=(eC(n,e,{_f:{}})._f||{}).ref,{ref:o,message:l,type:s,...c}=eC(i.errors,e)||{};ez(i.errors,e,{...c,...t,ref:r}),u.state.next({name:e,errors:i.errors,isValid:!1}),a&&a.shouldFocus&&r&&r.focus&&r.focus()},N=e=>u.state.subscribe({next:t=>{tx(e.name,t.name,e.exact)&&tv(t,e.formState||p,B,e.reRenderRoot)&&e.callback({values:{...o},...i,...t,defaultValues:r})}}).unsubscribe,V=(e,t={})=>{for(let l of e?eK(e):s.mount)s.mount.delete(l),s.array.delete(l),t.keepValue||(e4(n,l),e4(o,l)),t.keepError||e4(i.errors,l),t.keepDirty||e4(i.dirtyFields,l),t.keepTouched||e4(i.touchedFields,l),t.keepIsValidating||e4(i.validatingFields,l),a.shouldUnregister||t.keepDefaultValue||e4(r,l);u.state.next({values:eS(o)}),u.state.next({...i,...!t.keepDirty?{}:{isDirty:_()}}),t.keepIsValid||v()},q=({disabled:e,name:t})=>{(eP(e)&&l.mount||e||s.disabled.has(t))&&(e?s.disabled.add(t):s.disabled.delete(t))},T=(e,t={})=>{let i=eC(n,e),o=eP(t.disabled)||eP(a.disabled);return ez(n,e,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:e}},name:e,mount:!0,...t}}),s.mount.add(e),i?q({disabled:eP(t.disabled)?t.disabled:a.disabled,name:e}):y(e,!0,t.value),{...o?{disabled:t.disabled||a.disabled}:{},...a.progressive?{required:!!t.required,min:tl(t.min),max:tl(t.max),minLength:tl(t.minLength),maxLength:tl(t.maxLength),pattern:tl(t.pattern)}:{},name:e,onChange:F,onBlur:F,ref:o=>{if(o){T(e,t),i=eC(n,e);let a=eD(o.value)&&o.querySelectorAll&&o.querySelectorAll("input,select,textarea")[0]||o,l=e2(a),s=i._f.refs||[];(l?s.find(e=>e===a):a===i._f.ref)||(ez(n,e,{_f:{...i._f,...l?{refs:[...s.filter(e3),a,...Array.isArray(eC(r,e))?[{}]:[]],ref:{type:a.type,name:e}}:{ref:a}}}),y(e,!1,void 0,a))}else(i=eC(n,e,{}))._f&&(i._f.mount=!1),(a.shouldUnregister||t.shouldUnregister)&&!(ek(s.array,e)&&l.action)&&s.unMount.add(e)}}},M=()=>a.shouldFocusError&&tm(n,O,s.mount),I=(e,t)=>async r=>{let l;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let c=eS(o);if(u.state.next({isSubmitting:!0}),a.resolver){let{errors:e,values:t}=await w();i.errors=e,c=eS(t)}else await k(n);if(s.disabled.size)for(let e of s.disabled)e4(c,e);if(e4(i.errors,"root"),eZ(i.errors)){u.state.next({errors:{}});try{await e(c,r)}catch(e){l=e}}else t&&await t({...i.errors},r),M(),setTimeout(M);if(u.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:eZ(i.errors)&&!l,submitCount:i.submitCount+1,errors:i.errors}),l)throw l},L=(e,t={})=>{let c=e?eS(e):r,d=eS(c),m=eZ(e),f=m?r:d;if(t.keepDefaultValues||(r=c),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...s.mount,...Object.keys(e7(r,o))])))eC(i.dirtyFields,e)?ez(f,e,eC(o,e)):D(e,eC(f,e));else{if(eA&&eD(e))for(let e of s.mount){let t=eC(n,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(eQ(e)){let t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(let e of s.mount)D(e,eC(f,e));else n={}}o=a.shouldUnregister?t.keepDefaultValues?eS(r):{}:eS(f),u.array.next({values:{...f}}),u.state.next({values:{...f}})}s={mount:t.keepDirtyValues?s.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},l.mount=!p.isValid||!!t.keepIsValid||!!t.keepDirtyValues,l.watch=!!a.shouldUnregister,u.state.next({submitCount:t.keepSubmitCount?i.submitCount:0,isDirty:!m&&(t.keepDirty?i.isDirty:!!(t.keepDefaultValues&&!eG(e,r))),isSubmitted:!!t.keepIsSubmitted&&i.isSubmitted,dirtyFields:m?{}:t.keepDirtyValues?t.keepDefaultValues&&o?e7(r,o):i.dirtyFields:t.keepDefaultValues&&e?e7(r,e):t.keepDirty?i.dirtyFields:{},touchedFields:t.keepTouched?i.touchedFields:{},errors:t.keepErrors?i.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&i.isSubmitSuccessful,isSubmitting:!1,defaultValues:r})},U=(e,t)=>L(eJ(e)?e(o):e,t),B=e=>{i={...i,...e}},$={control:{register:T,unregister:V,getFieldState:z,handleSubmit:I,setError:R,_subscribe:N,_runSchema:w,_focusError:M,_getWatch:A,_getDirty:_,_setValid:v,_setFieldArray:(e,t=[],s,c,m=!0,f=!0)=>{if(c&&s&&!a.disabled){if(l.action=!0,f&&Array.isArray(eC(n,e))){let t=s(eC(n,e),c.argA,c.argB);m&&ez(n,e,t)}if(f&&Array.isArray(eC(i.errors,e))){let t=s(eC(i.errors,e),c.argA,c.argB);m&&ez(i.errors,e,t),ty(i.errors,e)}if((p.touchedFields||d.touchedFields)&&f&&Array.isArray(eC(i.touchedFields,e))){let t=s(eC(i.touchedFields,e),c.argA,c.argB);m&&ez(i.touchedFields,e,t)}(p.dirtyFields||d.dirtyFields)&&(i.dirtyFields=e7(r,o)),u.state.next({name:e,isDirty:_(e,t),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else ez(o,e,t)},_setDisabledField:q,_setErrors:e=>{i.errors=e,u.state.next({errors:i.errors,isValid:!1})},_getFieldArray:e=>eF(eC(l.mount?o:r,e,a.shouldUnregister?eC(r,e,[]):[])),_reset:L,_resetDefaultValues:()=>eJ(a.defaultValues)&&a.defaultValues().then(e=>{U(e,a.resetOptions),u.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of s.unMount){let t=eC(n,e);t&&(t._f.refs?t._f.refs.every(e=>!e3(e)):!e3(t._f.ref))&&V(e)}s.unMount=new Set},_disableForm:e=>{eP(e)&&(u.state.next({disabled:e}),tm(n,(t,a)=>{let i=eC(n,a);i&&(t.disabled=i._f.disabled||e,Array.isArray(i._f.refs)&&i._f.refs.forEach(t=>{t.disabled=i._f.disabled||e}))},0,!1))},_subjects:u,_proxyFormState:p,get _fields(){return n},get _formValues(){return o},get _state(){return l},set _state(value){l=value},get _defaultValues(){return r},get _names(){return s},set _names(value){s=value},get _formState(){return i},get _options(){return a},set _options(value){a={...a,...value}}},subscribe:e=>(l.mount=!0,d={...d,...e.formState},N({...e,formState:d})),trigger:C,register:T,handleSubmit:I,watch:(e,t)=>eJ(e)?u.state.subscribe({next:a=>"values"in a&&e(A(void 0,t),a)}):A(e,t,!0),setValue:D,getValues:P,reset:U,resetField:(e,t={})=>{eC(n,e)&&(eD(t.defaultValue)?D(e,eS(eC(r,e))):(D(e,t.defaultValue),ez(r,e,eS(t.defaultValue))),t.keepTouched||e4(i.touchedFields,e),t.keepDirty||(e4(i.dirtyFields,e),i.isDirty=t.defaultValue?_(e,eS(eC(r,e))):_()),!t.keepError&&(e4(i.errors,e),p.isValid&&v()),u.state.next({...i}))},clearErrors:e=>{e&&eK(e).forEach(e=>e4(i.errors,e)),u.state.next({errors:e?i.errors:{}})},unregister:V,setError:R,setFocus:(e,t={})=>{let a=eC(n,e),i=a&&a._f;if(i){let e=i.refs?i.refs[0]:i.ref;e.focus&&(e.focus(),t.shouldSelect&&eJ(e.select)&&e.select())}},getFieldState:z};return{...$,formControl:$}}(e);t.current={...n,formState:i}}let r=t.current.control;return r._options=e,eI(()=>{let e=r._subscribe({formState:r._proxyFormState,callback:()=>n({...r._formState}),reRenderRoot:!0});return n(e=>({...e,isReady:!0})),r._formState.isReady=!0,e},[r]),l.useEffect(()=>r._disableForm(e.disabled),[r,e.disabled]),l.useEffect(()=>{e.mode&&(r._options.mode=e.mode),e.reValidateMode&&(r._options.reValidateMode=e.reValidateMode)},[r,e.mode,e.reValidateMode]),l.useEffect(()=>{e.errors&&(r._setErrors(e.errors),r._focusError())},[r,e.errors]),l.useEffect(()=>{e.shouldUnregister&&r._subjects.state.next({values:r._getWatch()})},[r,e.shouldUnregister]),l.useEffect(()=>{if(r._proxyFormState.isDirty){let e=r._getDirty();e!==i.isDirty&&r._subjects.state.next({isDirty:e})}},[r,i.isDirty]),l.useEffect(()=>{e.values&&!eG(e.values,a.current)?(r._reset(e.values,{keepFieldsRef:!0,...r._options.resetOptions}),a.current=e.values,n(e=>({...e}))):r._resetDefaultValues()},[r,e.values]),l.useEffect(()=>{r._state.mount||(r._setValid(),r._state.mount=!0),r._state.watch&&(r._state.watch=!1,r._subjects.state.next({...r._formState})),r._removeUnmounted()}),t.current.formState=eM(i,r),t.current}({resolver:(void 0===a&&(a={}),function(e,t,i){try{return Promise.resolve(function(t,n){try{var r=Promise.resolve(tG["sync"===a.mode?"parse":"parseAsync"](e,void 0)).then(function(t){return i.shouldUseNativeValidation&&tM({},i),{errors:{},values:a.raw?e:t}})}catch(e){return n(e)}return r&&r.then?r.then(void 0,n):r}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:tI(tU(e.errors,!i.shouldUseNativeValidation&&"all"===i.criteriaMode),i)};throw e}))}catch(e){return Promise.reject(e)}}),values:n});return(0,i.jsxs)(ef.Zp,{className:"mx-auto w-full","data-sentry-element":"Card","data-sentry-component":"ProductForm","data-sentry-source-file":"product-form.tsx",children:[(0,i.jsx)(ef.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"product-form.tsx",children:(0,i.jsx)(ef.ZB,{className:"text-left text-2xl font-bold","data-sentry-element":"CardTitle","data-sentry-source-file":"product-form.tsx",children:t})}),(0,i.jsx)(ef.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"product-form.tsx",children:(0,i.jsx)(tS,{...r,"data-sentry-element":"Form","data-sentry-source-file":"product-form.tsx",children:(0,i.jsxs)("form",{onSubmit:r.handleSubmit(function(e){}),className:"space-y-8",children:[(0,i.jsx)(tD,{control:r.control,name:"image",render:({field:e})=>(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)(tC,{className:"w-full",children:[(0,i.jsx)(tP,{children:"Images"}),(0,i.jsx)(tz,{children:(0,i.jsx)(ed,{value:e.value,onValueChange:e.onChange,maxFiles:4,maxSize:4194304})}),(0,i.jsx)(tR,{})]})}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,i.jsx)(tD,{control:r.control,name:"name",render:({field:e})=>(0,i.jsxs)(tC,{children:[(0,i.jsx)(tP,{children:"Product Name"}),(0,i.jsx)(tz,{children:(0,i.jsx)(tN.p,{placeholder:"Enter product name",...e})}),(0,i.jsx)(tR,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsx)(tD,{control:r.control,name:"category",render:({field:e})=>(0,i.jsxs)(tC,{children:[(0,i.jsx)(tP,{children:"Category"}),(0,i.jsxs)(tV.l6,{onValueChange:t=>e.onChange(t),value:e.value[e.value.length-1],children:[(0,i.jsx)(tz,{children:(0,i.jsx)(tV.bq,{children:(0,i.jsx)(tV.yv,{placeholder:"Select categories"})})}),(0,i.jsxs)(tV.gC,{children:[(0,i.jsx)(tV.eb,{value:"beauty",children:"Beauty Products"}),(0,i.jsx)(tV.eb,{value:"electronics",children:"Electronics"}),(0,i.jsx)(tV.eb,{value:"clothing",children:"Clothing"}),(0,i.jsx)(tV.eb,{value:"home",children:"Home & Garden"}),(0,i.jsx)(tV.eb,{value:"sports",children:"Sports & Outdoors"})]})]}),(0,i.jsx)(tR,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsx)(tD,{control:r.control,name:"price",render:({field:e})=>(0,i.jsxs)(tC,{children:[(0,i.jsx)(tP,{children:"Price"}),(0,i.jsx)(tz,{children:(0,i.jsx)(tN.p,{type:"number",step:"0.01",placeholder:"Enter price",...e})}),(0,i.jsx)(tR,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"})]}),(0,i.jsx)(tD,{control:r.control,name:"description",render:({field:e})=>(0,i.jsxs)(tC,{children:[(0,i.jsx)(tP,{children:"Description"}),(0,i.jsx)(tz,{children:(0,i.jsx)(tq.T,{placeholder:"Enter product description",className:"resize-none",...e})}),(0,i.jsx)(tR,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsx)(eo.$,{type:"submit","data-sentry-element":"Button","data-sentry-source-file":"product-form.tsx",children:"Add Product"})]})})})]})}},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},58428:(e,t,a)=>{"use strict";a.d(t,{T:()=>r});var i=a(91754);a(93491);var n=a(82233);function r({className:e,...t}){return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},60290:(e,t,a)=>{"use strict";let i;a.r(t),a.d(t,{default:()=>h,generateImageMetadata:()=>g,generateMetadata:()=>x,generateViewport:()=>y,metadata:()=>u});var n=a(63033),r=a(18188),o=a(5434),l=a(45188),s=a(67999),c=a(4590),p=a(23064),d=a(7688);let u={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,p.UL)(),a=t.get("sidebar_state")?.value==="true";return(0,r.jsx)(o.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,r.jsxs)(c.SidebarProvider,{defaultOpen:a,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,r.jsx)(l.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,r.jsxs)(c.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,r.jsx)(s.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,r.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let f={...n},v="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;i=new Proxy(m,{apply:(e,t,a)=>{let i,n,r;try{let e=v?.getStore();i=e?.headers.get("sentry-trace")??void 0,n=e?.headers.get("baggage")??void 0,r=e?.headers}catch{}return d.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:i,baggageHeader:n,headers:r}).apply(t,a)}});let x=void 0,g=void 0,y=void 0,h=i},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66536:(e,t,a)=>{"use strict";a.d(t,{C1:()=>w,bL:()=>b});var i=a(93491),n=a(10158),r=a(90604),o=a(91754),l="Progress",[s,c]=(0,n.A)(l),[p,d]=s(l),u=i.forwardRef((e,t)=>{var a,i;let{__scopeProgress:n,value:l=null,max:s,getValueLabel:c=v,...d}=e;(s||0===s)&&!y(s)&&console.error((a=`${s}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=y(s)?s:100;null===l||h(l,u)||console.error((i=`${l}`,`Invalid prop \`value\` of value \`${i}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=h(l,u)?l:null,f=g(m)?c(m,u):void 0;return(0,o.jsx)(p,{scope:n,value:m,max:u,children:(0,o.jsx)(r.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":f,role:"progressbar","data-state":x(m,u),"data-value":m??void 0,"data-max":u,...d,ref:t})})});u.displayName=l;var m="ProgressIndicator",f=i.forwardRef((e,t)=>{let{__scopeProgress:a,...i}=e,n=d(m,a);return(0,o.jsx)(r.sG.div,{"data-state":x(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...i,ref:t})});function v(e,t){return`${Math.round(e/t*100)}%`}function x(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function y(e){return g(e)&&!isNaN(e)&&e>0}function h(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=m;var b=u,w=f},67014:(e,t,a)=>{"use strict";function i(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return i}}),a(28171).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67057:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,r.isBailoutToCSRError)(t)||(0,s.isDynamicServerError)(t)||(0,l.isDynamicPostpone)(t)||(0,n.isPostpone)(t)||(0,i.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let i=a(3777),n=a(31266),r=a(85387),o=a(30847),l=a(7596),s=a(82594);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69122:(e,t,a)=>{"use strict";a.d(t,{bq:()=>d,eb:()=>m,gC:()=>u,l6:()=>c,yv:()=>p});var i=a(91754);a(93491);var n=a(97543),r=a(33093),o=a(87435),l=a(20388),s=a(82233);function c({...e}){return(0,i.jsx)(n.bL,{"data-slot":"select",...e,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function p({...e}){return(0,i.jsx)(n.WT,{"data-slot":"select-value",...e,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function d({className:e,size:t="default",children:a,...o}){return(0,i.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,s.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[a,(0,i.jsx)(n.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,i.jsx)(r.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function u({className:e,children:t,position:a="popper",...r}){return(0,i.jsx)(n.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,i.jsxs)(n.UC,{"data-slot":"select-content",className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,i.jsx)(f,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,i.jsx)(n.LM,{className:(0,s.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:t}),(0,i.jsx)(v,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function m({className:e,children:t,...a}){return(0,i.jsxs)(n.q7,{"data-slot":"select-item",className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,i.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,i.jsx)(n.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,i.jsx)(o.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,i.jsx)(n.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:t})]})}function f({className:e,...t}){return(0,i.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,s.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,i.jsx)(l.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function v({className:e,...t}){return(0,i.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,s.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,i.jsx)(r.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},71437:(e,t,a)=>{"use strict";a.d(t,{c:()=>r,g:()=>o});var i=a(75387),n=a(16705);let r=e=>new Promise(t=>setTimeout(t,e)),o={records:[],initialize(){let e=[];for(let a=1;a<=20;a++){var t;e.push({id:t=a,name:i.a.commerce.productName(),description:i.a.commerce.productDescription(),created_at:i.a.date.between({from:"2022-01-01",to:"2023-12-31"}).toISOString(),price:parseFloat(i.a.commerce.price({min:5,max:500,dec:2})),photo_url:`https://api.slingacademy.com/public/sample-products/${t}.png`,category:i.a.helpers.arrayElement(["Electronics","Furniture","Clothing","Toys","Groceries","Books","Jewelry","Beauty Products"]),updated_at:i.a.date.recent().toISOString()})}this.records=e},async getAll({categories:e=[],search:t}){let a=[...this.records];return e.length>0&&(a=a.filter(t=>e.includes(t.category))),t&&(a=(0,n.Ht)(a,t,{keys:["name","description","category"]})),a},async getProducts({page:e=1,limit:t=10,categories:a,search:i}){await r(1e3);let n=a?a.split("."):[],o=await this.getAll({categories:n,search:i}),l=o.length,s=(e-1)*t,c=o.slice(s,s+t);return{success:!0,time:new Date().toISOString(),message:"Sample data for testing and learning purposes",total_products:l,offset:s,limit:t,products:c}},async getProductById(e){await r(1e3);let t=this.records.find(t=>t.id===e);return t?{success:!0,time:new Date().toISOString(),message:`Product with ID ${e} found`,product:t}:{success:!1,message:`Product with ID ${e} not found`}}};o.initialize()},72567:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{ReadonlyURLSearchParams:function(){return p},RedirectType:function(){return n.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return r.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return s.unstable_rethrow}});let i=a(81696),n=a(6015),r=a(20610),o=a(2211),l=a(67014),s=a(46514);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class p extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72599:(e,t,a)=>{"use strict";let i;a.r(t),a.d(t,{default:()=>k,generateImageMetadata:()=>w,generateMetadata:()=>b,generateViewport:()=>j,metadata:()=>x});var n=a(63033),r=a(18188),o=a(95093),l=a(40582),s=a(16069);function c(){return(0,r.jsxs)(l.Zp,{className:"mx-auto w-full","data-sentry-element":"Card","data-sentry-component":"FormCardSkeleton","data-sentry-source-file":"form-card-skeleton.tsx",children:[(0,r.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"form-card-skeleton.tsx",children:[(0,r.jsx)(s.E,{className:"h-8 w-48","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," "]}),(0,r.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"form-card-skeleton.tsx",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(s.E,{className:"h-4 w-16","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," ",(0,r.jsx)(s.E,{className:"h-32 w-full rounded-lg","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," "]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(s.E,{className:"h-4 w-24","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," ",(0,r.jsx)(s.E,{className:"h-10 w-full","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," "]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(s.E,{className:"h-4 w-20","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," ",(0,r.jsx)(s.E,{className:"h-10 w-full","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," "]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(s.E,{className:"h-4 w-16","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," ",(0,r.jsx)(s.E,{className:"h-10 w-full","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," "]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(s.E,{className:"h-4 w-24","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," ",(0,r.jsx)(s.E,{className:"h-32 w-full","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})," "]}),(0,r.jsx)(s.E,{className:"h-10 w-28","data-sentry-element":"Skeleton","data-sentry-source-file":"form-card-skeleton.tsx"})]})})]})}var p=a(53891),d=a(71437),u=a(72567),m=a(1968);async function f({productId:e}){let t=null,a="Create New Product";return"new"!==e&&((t=(await d.g.getProductById(Number(e))).product)||(0,u.notFound)(),a="Edit Product"),(0,r.jsx)(m.default,{initialData:t,pageTitle:a,"data-sentry-element":"ProductForm","data-sentry-component":"ProductViewPage","data-sentry-source-file":"product-view-page.tsx"})}var v=a(7688);let x={title:"Dashboard : Product View"};async function g(e){let t=await e.params;return(0,r.jsx)(p.A,{scrollable:!0,"data-sentry-element":"PageContainer","data-sentry-component":"Page","data-sentry-source-file":"page.tsx",children:(0,r.jsx)("div",{className:"flex-1 space-y-4",children:(0,r.jsx)(o.Suspense,{fallback:(0,r.jsx)(c,{}),"data-sentry-element":"Suspense","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(f,{productId:t.productId,"data-sentry-element":"ProductViewPage","data-sentry-source-file":"page.tsx"})})})})}let y={...n},h="workUnitAsyncStorage"in y?y.workUnitAsyncStorage:"requestAsyncStorage"in y?y.requestAsyncStorage:void 0;i=new Proxy(g,{apply:(e,t,a)=>{let i,n,r;try{let e=h?.getStore();i=e?.headers.get("sentry-trace")??void 0,n=e?.headers.get("baggage")??void 0,r=e?.headers}catch{}return v.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/product/[productId]",componentType:"Page",sentryTraceHeader:i,baggageHeader:n,headers:r}).apply(t,a)}});let b=void 0,w=void 0,j=void 0,k=i},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,a)=>{Promise.resolve().then(a.bind(a,5434)),Promise.resolve().then(a.bind(a,45188)),Promise.resolve().then(a.bind(a,67999)),Promise.resolve().then(a.bind(a,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77093:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.default,__next_app__:()=>p,pages:()=>c,routeModule:()=>d,tree:()=>s});var i=a(95500),n=a(56947),r=a(26052),o=a(13636),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let s={children:["",{children:["dashboard",{children:["product",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,72599)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\product\\[productId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e),async e=>(await Promise.resolve().then(a.bind(a,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e),async e=>(await Promise.resolve().then(a.bind(a,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\product\\[productId]\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},d=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/product/[productId]/page",pathname:"/dashboard/product/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:s}})},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81696:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return d},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return s},redirect:function(){return l}});let i=a(21549),n=a(6015),r=a(19121).actionAsyncStorage;function o(e,t,a){void 0===a&&(a=i.RedirectStatusCode.TemporaryRedirect);let r=Object.defineProperty(Error(n.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r.digest=n.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+a+";",r}function l(e,t){var a;throw null!=t||(t=(null==r||null==(a=r.getStore())?void 0:a.isAction)?n.RedirectType.push:n.RedirectType.replace),o(e,t,i.RedirectStatusCode.TemporaryRedirect)}function s(e,t){throw void 0===t&&(t=n.RedirectType.replace),o(e,t,i.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,n.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function p(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function d(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},88914:(e,t,a)=>{"use strict";a.d(t,{c:()=>n});var i=a(93491);function n(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}},94735:e=>{"use strict";e.exports=require("events")},99781:(e,t,a)=>{"use strict";a.d(t,{ScrollArea:()=>n});var i=a(1472);let n=(0,i.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\scroll-area.tsx","ScrollArea");(0,i.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\ui\\scroll-area.tsx","ScrollBar")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,787,2267,543,8134,8634],()=>a(77093));module.exports=i})();
//# sourceMappingURL=page.js.map