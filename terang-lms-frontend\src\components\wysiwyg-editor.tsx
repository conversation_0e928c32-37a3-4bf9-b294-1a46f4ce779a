'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  Quote, 
  Code, 
  Link, 
  Heading1, 
  Heading2, 
  Heading3,
  Type,
  Eye,
  Edit3
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface WysiwygEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
}

export function WysiwygEditor({ content, onChange, placeholder }: WysiwygEditorProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(true);
  const [markdownContent, setMarkdownContent] = useState(content);
  const editorRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setMarkdownContent(content);
  }, [content]);

  const handleContentChange = useCallback((newContent: string) => {
    setMarkdownContent(newContent);
    onChange(newContent);
  }, [onChange]);

  const toggleMode = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  const insertMarkdown = (before: string, after: string = '', placeholder: string = '') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = markdownContent.substring(start, end);
    const textToInsert = selectedText || placeholder;
    
    const newContent = 
      markdownContent.substring(0, start) + 
      before + textToInsert + after + 
      markdownContent.substring(end);
    
    handleContentChange(newContent);
    
    // Set cursor position after insertion
    setTimeout(() => {
      if (textareaRef.current) {
        const newCursorPos = start + before.length + textToInsert.length;
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
      }
    }, 0);
  };

  const insertHeading = (level: number) => {
    const prefix = '#'.repeat(level) + ' ';
    insertMarkdown(prefix, '', 'Heading text');
  };

  const insertList = (ordered: boolean = false) => {
    const prefix = ordered ? '1. ' : '- ';
    insertMarkdown(prefix, '', 'List item');
  };

  const insertLink = () => {
    insertMarkdown('[', '](url)', 'link text');
  };

  const insertCode = () => {
    insertMarkdown('`', '`', 'code');
  };

  const insertQuote = () => {
    insertMarkdown('> ', '', 'Quote text');
  };

  const formatText = (format: 'bold' | 'italic' | 'underline') => {
    const formats = {
      bold: ['**', '**'],
      italic: ['*', '*'],
      underline: ['<u>', '</u>']
    };
    const [before, after] = formats[format];
    insertMarkdown(before, after, 'text');
  };

  return (
    <div className="border rounded-lg overflow-hidden">
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b bg-gray-50">
        <div className="flex items-center gap-1">
          <Button
            variant={isPreviewMode ? "default" : "outline"}
            size="sm"
            onClick={toggleMode}
            className="h-8"
          >
            {isPreviewMode ? <Eye className="h-4 w-4" /> : <Edit3 className="h-4 w-4" />}
            {isPreviewMode ? 'Preview' : 'Edit'}
          </Button>
        </div>
        
        <Separator orientation="vertical" className="h-6" />
        
        {!isPreviewMode && (
          <>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertHeading(1)}
                className="h-8"
                title="Heading 1"
              >
                <Heading1 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertHeading(2)}
                className="h-8"
                title="Heading 2"
              >
                <Heading2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertHeading(3)}
                className="h-8"
                title="Heading 3"
              >
                <Heading3 className="h-4 w-4" />
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText('bold')}
                className="h-8"
                title="Bold"
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText('italic')}
                className="h-8"
                title="Italic"
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => formatText('underline')}
                className="h-8"
                title="Underline"
              >
                <Underline className="h-4 w-4" />
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertList(false)}
                className="h-8"
                title="Bullet List"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => insertList(true)}
                className="h-8"
                title="Numbered List"
              >
                <ListOrdered className="h-4 w-4" />
              </Button>
            </div>

            <Separator orientation="vertical" className="h-6" />

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={insertLink}
                className="h-8"
                title="Link"
              >
                <Link className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={insertCode}
                className="h-8"
                title="Code"
              >
                <Code className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={insertQuote}
                className="h-8"
                title="Quote"
              >
                <Quote className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}
      </div>

      {/* Content Area */}
      <div className="min-h-[200px]">
        {isPreviewMode ? (
          <div 
            className="p-4 prose max-w-none cursor-text"
            onClick={() => setIsPreviewMode(false)}
          >
            {markdownContent ? (
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({ node, ...props }) => (
                    <h1 className="mb-4 text-2xl font-bold text-gray-900" {...props} />
                  ),
                  h2: ({ node, ...props }) => (
                    <h2 className="mb-3 text-xl font-semibold text-gray-800" {...props} />
                  ),
                  h3: ({ node, ...props }) => (
                    <h3 className="mb-2 text-lg font-semibold text-gray-800" {...props} />
                  ),
                  h4: ({ node, ...props }) => (
                    <h4 className="mb-2 text-base font-semibold text-gray-700" {...props} />
                  ),
                  p: ({ node, ...props }) => (
                    <p className="mb-3 leading-relaxed" {...props} />
                  ),
                  ul: ({ node, ...props }) => (
                    <ul className="mb-3 ml-4 list-disc" {...props} />
                  ),
                  ol: ({ node, ...props }) => (
                    <ol className="mb-3 ml-4 list-decimal" {...props} />
                  ),
                  li: ({ node, ...props }) => (
                    <li className="mb-1" {...props} />
                  ),
                  blockquote: ({ node, ...props }) => (
                    <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3" {...props} />
                  ),
                  code: ({ node, inline, ...props }) => (
                    inline ? (
                      <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" {...props} />
                    ) : (
                      <code className="block bg-gray-100 p-3 rounded text-sm font-mono overflow-x-auto" {...props} />
                    )
                  ),
                  a: ({ node, ...props }) => (
                    <a className="text-blue-600 hover:text-blue-800 underline" {...props} />
                  ),
                }}
              >
                {markdownContent}
              </ReactMarkdown>
            ) : (
              <p className="text-gray-400 italic">
                {placeholder || 'Click here to start writing...'}
              </p>
            )}
          </div>
        ) : (
          <textarea
            ref={textareaRef}
            value={markdownContent}
            onChange={(e) => handleContentChange(e.target.value)}
            placeholder={placeholder || 'Start typing your content...'}
            className="w-full h-full min-h-[200px] p-4 border-0 resize-none focus:outline-none font-mono text-sm"
            autoFocus
          />
        )}
      </div>
    </div>
  );
}
