try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="73c5c614-db89-49a2-be39-46603392671b",e._sentryDebugIdIdentifier="sentry-dbid-73c5c614-db89-49a2-be39-46603392671b")}catch(e){}(()=>{var e={};e.id=4771,e.ids=[4771],e.modules={1683:(e,t,s)=>{"use strict";s.d(t,{P:()=>n});var r=s(138);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let i=(0,r.lw)(process.env.DATABASE_URL);async function n(e,...t){return await i(e,...t)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},43078:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>S,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>C});var r={};s.r(r),s.d(r,{DELETE:()=>g,GET:()=>f,HEAD:()=>A,OPTIONS:()=>T,PATCH:()=>R,POST:()=>q,PUT:()=>h});var i=s(3690),n=s(56947),o=s(75250),u=s(63033),a=s(62187),c=s(1683),d=s(7688);async function p(e,{params:t}){try{let{id:e}=await t,s=parseInt(e);if(isNaN(s))return a.NextResponse.json({success:!1,error:"Invalid institution ID"},{status:400});let r=await (0,c.P)`
      SELECT 
        id,
        name,
        type,
        subscription_plan,
        billing_cycle,
        payment_status,
        payment_due_date,
        student_count,
        teacher_count,
        created_at,
        updated_at
      FROM institutions
      WHERE id = ${s}
    `;if(0===r.length)return a.NextResponse.json({success:!1,error:"Institution not found"},{status:404});return a.NextResponse.json({success:!0,data:r[0],message:"Institution retrieved successfully"})}catch(e){return console.error("Get institution error:",e),a.NextResponse.json({success:!1,error:"Failed to retrieve institution"},{status:500})}}async function l(e,{params:t}){try{let{id:s}=await t,r=parseInt(s),i=await e.json();if(isNaN(r))return a.NextResponse.json({success:!1,error:"Invalid institution ID"},{status:400});let{name:n,type:o,subscriptionPlan:u,billingCycle:d,studentCount:p,teacherCount:l,paymentStatus:E,paymentDueDate:x}=i,_=await (0,c.P)`
      SELECT id FROM institutions WHERE id = ${r}
    `;if(0===_.length)return a.NextResponse.json({success:!1,error:"Institution not found"},{status:404});let y=await (0,c.P)`
      UPDATE institutions
      SET
        name = COALESCE(${n}, name),
        type = COALESCE(${o}, type),
        subscription_plan = COALESCE(${u}, subscription_plan),
        billing_cycle = COALESCE(${d}, billing_cycle),
        student_count = COALESCE(${p}, student_count),
        teacher_count = COALESCE(${l}, teacher_count),
        payment_status = COALESCE(${E}, payment_status),
        payment_due_date = COALESCE(${x}, payment_due_date),
        updated_at = NOW()
      WHERE id = ${r}
      RETURNING *
    `;return a.NextResponse.json({success:!0,data:y[0],message:"Institution updated successfully"})}catch(e){return console.error("Update institution error:",e),a.NextResponse.json({success:!1,error:"Failed to update institution"},{status:500})}}async function E(e,{params:t}){try{let{id:e}=await t,s=parseInt(e);if(isNaN(s))return a.NextResponse.json({success:!1,error:"Invalid institution ID"},{status:400});let r=await (0,c.P)`
      SELECT id FROM institutions WHERE id = ${s}
    `;if(0===r.length)return a.NextResponse.json({success:!1,error:"Institution not found"},{status:404});let i=(await (0,c.P)`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE institution_id = ${s}) as user_count,
        (SELECT COUNT(*) FROM classes WHERE institution_id = ${s}) as class_count,
        (SELECT COUNT(*) FROM courses WHERE institution_id = ${s}) as course_count
    `)[0];if(i.user_count>0||i.class_count>0||i.course_count>0)return a.NextResponse.json({success:!1,error:"Cannot delete institution with existing users, classes, or courses. Please remove them first."},{status:400});return await (0,c.P)`
      DELETE FROM institutions WHERE id = ${s}
    `,a.NextResponse.json({success:!0,message:"Institution deleted successfully"})}catch(e){return console.error("Delete institution error:",e),a.NextResponse.json({success:!1,error:"Failed to delete institution"},{status:500})}}let x={...u},_="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;function y(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,r)=>{let i;try{let e=_?.getStore();i=e?.headers}catch{}return d.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/institutions/[id]",headers:i}).apply(s,r)}})}let f=y(p,"GET"),q=y(void 0,"POST"),h=y(l,"PUT"),R=y(void 0,"PATCH"),g=y(E,"DELETE"),A=y(void 0,"HEAD"),T=y(void 0,"OPTIONS"),m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/institutions/[id]/route",pathname:"/api/institutions/[id]",filename:"route",bundlePath:"app/api/institutions/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\institutions\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:C,serverHooks:S}=m;function w(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:C})}},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=44725,e.exports=t},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,8036,138],()=>s(43078));module.exports=r})();
//# sourceMappingURL=route.js.map