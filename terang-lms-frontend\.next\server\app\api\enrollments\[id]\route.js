try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="a79398dd-e60f-4adc-a56f-245bbabb6bfd",e._sentryDebugIdIdentifier="sentry-dbid-a79398dd-e60f-4adc-a56f-245bbabb6bfd")}catch(e){}"use strict";(()=>{var e={};e.id=4325,e.ids=[4325],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},50246:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>T,routeModule:()=>b,serverHooks:()=>A,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>R});var s={};t.r(s),t.d(s,{DELETE:()=>w,GET:()=>E,HEAD:()=>N,OPTIONS:()=>v,PATCH:()=>g,POST:()=>I,PUT:()=>y});var n=t(3690),o=t(56947),u=t(75250),l=t(63033),d=t(62187),i=t(18621),a=t(32230),c=t(74683),p=t(7688);async function m(e,{params:r}){try{let{id:t}=await r,s=parseInt(t),n=e.nextUrl.searchParams.get("type");if(isNaN(s))return d.NextResponse.json({error:"Invalid enrollment ID"},{status:400});if("course"===n){let e=await i.db.select({id:a.courseEnrollments.id,courseId:a.courseEnrollments.courseId,classId:a.courseEnrollments.classId,enrolledAt:a.courseEnrollments.enrolledAt,courseName:a.courses.name,courseCode:a.courses.courseCode}).from(a.courseEnrollments).leftJoin(a.courses,(0,c.eq)(a.courseEnrollments.courseId,a.courses.id)).where((0,c.eq)(a.courseEnrollments.id,s)).limit(1);if(0===e.length)return d.NextResponse.json({error:"Course enrollment not found"},{status:404});return d.NextResponse.json({enrollment:e[0]})}if("student"!==n)return d.NextResponse.json({error:"Type parameter required (course or student)"},{status:400});{let e=await i.db.select({id:a.studentEnrollments.id,studentId:a.studentEnrollments.studentId,courseId:a.studentEnrollments.courseId,enrolledAt:a.studentEnrollments.enrolledAt,completedAt:a.studentEnrollments.completedAt,finalScore:a.studentEnrollments.finalScore,certificateGenerated:a.studentEnrollments.certificateGenerated,studentName:a.users.name,studentEmail:a.users.email,courseName:a.courses.name,courseCode:a.courses.courseCode}).from(a.studentEnrollments).leftJoin(a.users,(0,c.eq)(a.studentEnrollments.studentId,a.users.id)).leftJoin(a.courses,(0,c.eq)(a.studentEnrollments.courseId,a.courses.id)).where((0,c.eq)(a.studentEnrollments.id,s)).limit(1);if(0===e.length)return d.NextResponse.json({error:"Student enrollment not found"},{status:404});return d.NextResponse.json({success:!0,enrollment:e[0]})}}catch(e){return console.error("Error fetching enrollment:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let{id:t}=await r,s=parseInt(t),n=e.nextUrl.searchParams,o=n.get("type"),u=n.get("teacherId");if(isNaN(s))return d.NextResponse.json({error:"Invalid enrollment ID"},{status:400});if(!u)return d.NextResponse.json({error:"Teacher ID required"},{status:400});if("course"===o){let e=await i.db.select({id:a.courseEnrollments.id,courseId:a.courseEnrollments.courseId,classId:a.courseEnrollments.classId,teacherId:a.courses.teacherId}).from(a.courseEnrollments).leftJoin(a.courses,(0,c.eq)(a.courseEnrollments.courseId,a.courses.id)).where((0,c.eq)(a.courseEnrollments.id,s)).limit(1);if(0===e.length)return d.NextResponse.json({error:"Course enrollment not found"},{status:404});if(e[0].teacherId!==parseInt(u))return d.NextResponse.json({error:"Not authorized to remove this enrollment"},{status:403});return await i.db.delete(a.courseEnrollments).where((0,c.eq)(a.courseEnrollments.id,s)),d.NextResponse.json({success:!0,message:"Course enrollment removed successfully"})}if("student"!==o)return d.NextResponse.json({error:"Type parameter required (course or student)"},{status:400});{let e=await i.db.select({id:a.studentEnrollments.id,studentId:a.studentEnrollments.studentId,courseId:a.studentEnrollments.courseId,teacherId:a.courses.teacherId}).from(a.studentEnrollments).leftJoin(a.courses,(0,c.eq)(a.studentEnrollments.courseId,a.courses.id)).where((0,c.eq)(a.studentEnrollments.id,s)).limit(1);if(0===e.length)return d.NextResponse.json({error:"Student enrollment not found"},{status:404});if(e[0].teacherId!==parseInt(u))return d.NextResponse.json({error:"Not authorized to remove this enrollment"},{status:403});return await i.db.delete(a.studentEnrollments).where((0,c.eq)(a.studentEnrollments.id,s)),d.NextResponse.json({success:!0,message:"Student enrollment removed successfully"})}}catch(e){return console.error("Error removing enrollment:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let f={...l},h="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let n;try{let e=h?.getStore();n=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/enrollments/[id]",headers:n}).apply(t,s)}})}let E=q(m,"GET"),I=q(void 0,"POST"),y=q(void 0,"PUT"),g=q(void 0,"PATCH"),w=q(x,"DELETE"),N=q(void 0,"HEAD"),v=q(void 0,"OPTIONS"),b=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/enrollments/[id]/route",pathname:"/api/enrollments/[id]",filename:"route",bundlePath:"app/api/enrollments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\enrollments\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:R,serverHooks:A}=b;function T(){return(0,u.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:R})}},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(50246));module.exports=s})();
//# sourceMappingURL=route.js.map