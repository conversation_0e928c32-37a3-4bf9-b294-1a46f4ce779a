try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f85f75a1-e4b8-4599-8f36-fff2e21378fb",e._sentryDebugIdIdentifier="sentry-dbid-f85f75a1-e4b8-4599-8f36-fff2e21378fb")}catch(e){}"use strict";exports.id=138,exports.ids=[138],exports.modules={138:(e,t,r)=>{r.d(t,{gK:()=>tM,lw:()=>tS});var n,s,i,o,a,u,c=Object.create,h=Object.defineProperty,l=Object.getOwnPropertyDescriptor,f=Object.getOwnPropertyNames,p=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,y=(e,t,r)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,g=(e,t)=>h(e,"name",{value:t,configurable:!0}),m=(e,t)=>()=>(e&&(t=e(e=0)),t),b=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),w=(e,t)=>{for(var r in t)h(e,r,{get:t[r],enumerable:!0})},v=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of f(t))d.call(e,s)||s===r||h(e,s,{get:()=>t[s],enumerable:!(n=l(t,s))||n.enumerable});return e},S=(e,t,r)=>(r=null!=e?c(p(e)):{},v(!t&&e&&e.__esModule?r:h(r,"default",{value:e,enumerable:!0}),e)),E=e=>v(h({},"__esModule",{value:!0}),e),_=(e,t,r)=>y(e,"symbol"!=typeof t?t+"":t,r),x=b(e=>{"use strict";I(),e.byteLength=u,e.toByteArray=h,e.fromByteArray=p;var t,r,n=[],s=[],i="u">typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(t=0,r=o.length;t<r;++t)n[t]=o[t],s[o.charCodeAt(t)]=t;function a(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}function u(e){var t=a(e),r=t[0],n=t[1];return(r+n)*3/4-n}function c(e,t,r){return(t+r)*3/4-r}function h(e){var t,r,n=a(e),o=n[0],u=n[1],h=new i(c(e,o,u)),l=0,f=u>0?o-4:o;for(r=0;r<f;r+=4)t=s[e.charCodeAt(r)]<<18|s[e.charCodeAt(r+1)]<<12|s[e.charCodeAt(r+2)]<<6|s[e.charCodeAt(r+3)],h[l++]=t>>16&255,h[l++]=t>>8&255,h[l++]=255&t;return 2===u&&(t=s[e.charCodeAt(r)]<<2|s[e.charCodeAt(r+1)]>>4,h[l++]=255&t),1===u&&(t=s[e.charCodeAt(r)]<<10|s[e.charCodeAt(r+1)]<<4|s[e.charCodeAt(r+2)]>>2,h[l++]=t>>8&255,h[l++]=255&t),h}function l(e){return n[e>>18&63]+n[e>>12&63]+n[e>>6&63]+n[63&e]}function f(e,t,r){for(var n=[],s=t;s<r;s+=3)n.push(l((e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2])));return n.join("")}function p(e){for(var t,r=e.length,s=r%3,i=[],o=0,a=r-s;o<a;o+=16383)i.push(f(e,o,o+16383>a?a:o+16383));return 1===s?i.push(n[(t=e[r-1])>>2]+n[t<<4&63]+"=="):2===s&&i.push(n[(t=(e[r-2]<<8)+e[r-1])>>10]+n[t>>4&63]+n[t<<2&63]+"="),i.join("")}s[45]=62,s[95]=63,g(a,"getLens"),g(u,"byteLength"),g(c,"_byteLength"),g(h,"toByteArray"),g(l,"tripletToBase64"),g(f,"encodeChunk"),g(p,"fromByteArray")}),A=b(e=>{I(),e.read=function(e,t,r,n,s){var i,o,a=8*s-n-1,u=(1<<a)-1,c=u>>1,h=-7,l=r?s-1:0,f=r?-1:1,p=e[t+l];for(l+=f,i=p&(1<<-h)-1,p>>=-h,h+=a;h>0;i=256*i+e[t+l],l+=f,h-=8);for(o=i&(1<<-h)-1,i>>=-h,h+=n;h>0;o=256*o+e[t+l],l+=f,h-=8);if(0===i)i=1-c;else{if(i===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),i-=c}return(p?-1:1)*o*Math.pow(2,i-n)},e.write=function(e,t,r,n,s,i){var o,a,u,c=8*i-s-1,h=(1<<c)-1,l=h>>1,f=5960464477539062e-23*(23===s),p=n?0:i-1,d=n?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(a=+!!isNaN(t),o=h):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+l>=1?t+=f/u:t+=f*Math.pow(2,1-l),t*u>=2&&(o++,u/=2),o+l>=h?(a=0,o=h):o+l>=1?(a=(t*u-1)*Math.pow(2,s),o+=l):(a=t*Math.pow(2,l-1)*Math.pow(2,s),o=0));s>=8;e[r+p]=255&a,p+=d,a/=256,s-=8);for(o=o<<s|a,c+=s;c>0;e[r+p]=255&o,p+=d,o/=256,c-=8);e[r+p-d]|=128*y}}),C=b(e=>{"use strict";I();var t=x(),r=A(),n="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(){try{let e=new Uint8Array(1),t={foo:g(function(){return 42},"foo")};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch{return!1}}function i(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');let t=new Uint8Array(e);return Object.setPrototypeOf(t,o.prototype),t}function o(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return h(e)}return a(e,t,r)}function a(e,t,r){if("string"==typeof e)return l(e,t);if(ArrayBuffer.isView(e))return p(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(eo(e,ArrayBuffer)||e&&eo(e.buffer,ArrayBuffer)||"u">typeof SharedArrayBuffer&&(eo(e,SharedArrayBuffer)||e&&eo(e.buffer,SharedArrayBuffer)))return d(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');let n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return o.from(n,t,r);let s=y(e);if(s)return s;if("u">typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return o.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e,t,r){return u(e),e<=0?i(e):void 0!==t?"string"==typeof r?i(e).fill(t,r):i(e).fill(t):i(e)}function h(e){return u(e),i(e<0?0:0|m(e))}function l(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!o.isEncoding(t))throw TypeError("Unknown encoding: "+t);let r=0|w(e,t),n=i(r),s=n.write(e,t);return s!==r&&(n=n.slice(0,s)),n}function f(e){let t=e.length<0?0:0|m(e.length),r=i(t);for(let n=0;n<t;n+=1)r[n]=255&e[n];return r}function p(e){if(eo(e,Uint8Array)){let t=new Uint8Array(e);return d(t.buffer,t.byteOffset,t.byteLength)}return f(e)}function d(e,t,r){let n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),o.prototype),n}function y(e){if(o.isBuffer(e)){let t=0|m(e.length),r=i(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||ea(e.length)?i(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}function m(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function b(e){return+e!=e&&(e=0),o.alloc(+e)}function w(e,t){if(o.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||eo(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);let r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;let s=!1;for(;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return et(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return es(e).length;default:if(s)return n?-1:et(e).length;t=(""+t).toLowerCase(),s=!0}}function v(e,t,r){let n=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0)||(r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return U(this,t,r);case"utf8":case"utf-8":return B(this,t,r);case"ascii":return O(this,t,r);case"latin1":case"binary":return D(this,t,r);case"base64":return M(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,t,r);default:if(n)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function S(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}function E(e,t,r,n,s){if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),ea(r*=1)&&(r=s?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(s)return -1;r=e.length-1}else if(r<0)if(!s)return -1;else r=0;if("string"==typeof t&&(t=o.from(t,n)),o.isBuffer(t))return 0===t.length?-1:_(e,t,r,n,s);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?s?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):_(e,[t],r,n,s);throw TypeError("val must be string, number or Buffer")}function _(e,t,r,n,s){let i,o=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,a/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(g(c,"read"),s){let n=-1;for(i=r;i<a;i++)if(c(e,i)===c(t,-1===n?0:i-n)){if(-1===n&&(n=i),i-n+1===u)return n*o}else -1!==n&&(i-=i-n),n=-1}else for(r+u>a&&(r=a-u),i=r;i>=0;i--){let r=!0;for(let n=0;n<u;n++)if(c(e,i+n)!==c(t,n)){r=!1;break}if(r)return i}return -1}function C(e,t,r,n){let s;r=Number(r)||0;let i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;let o=t.length;for(n>o/2&&(n=o/2),s=0;s<n;++s){let n=parseInt(t.substr(2*s,2),16);if(ea(n))break;e[r+s]=n}return s}function L(e,t,r,n){return ei(et(t,e.length-r),e,r,n)}function P(e,t,r,n){return ei(er(t),e,r,n)}function T(e,t,r,n){return ei(es(t),e,r,n)}function R(e,t,r,n){return ei(en(t,e.length-r),e,r,n)}function M(e,r,n){return 0===r&&n===e.length?t.fromByteArray(e):t.fromByteArray(e.slice(r,n))}function B(e,t,r){r=Math.min(e.length,r);let n=[],s=t;for(;s<r;){let t=e[s],i=null,o=t>239?4:t>223?3:t>191?2:1;if(s+o<=r){let r,n,a,u;switch(o){case 1:t<128&&(i=t);break;case 2:(192&(r=e[s+1]))==128&&(u=(31&t)<<6|63&r)>127&&(i=u);break;case 3:r=e[s+1],n=e[s+2],(192&r)==128&&(192&n)==128&&(u=(15&t)<<12|(63&r)<<6|63&n)>2047&&(u<55296||u>57343)&&(i=u);break;case 4:r=e[s+1],n=e[s+2],a=e[s+3],(192&r)==128&&(192&n)==128&&(192&a)==128&&(u=(15&t)<<18|(63&r)<<12|(63&n)<<6|63&a)>65535&&u<1114112&&(i=u)}}null===i?(i=65533,o=1):i>65535&&(i-=65536,n.push(i>>>10&1023|55296),i=56320|1023&i),n.push(i),s+=o}return k(n)}function k(e){let t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);let r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}function O(e,t,r){let n="";r=Math.min(e.length,r);for(let s=t;s<r;++s)n+=String.fromCharCode(127&e[s]);return n}function D(e,t,r){let n="";r=Math.min(e.length,r);for(let s=t;s<r;++s)n+=String.fromCharCode(e[s]);return n}function U(e,t,r){let n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);let s="";for(let n=t;n<r;++n)s+=eu[e[n]];return s}function N(e,t,r){let n=e.slice(t,r),s="";for(let e=0;e<n.length-1;e+=2)s+=String.fromCharCode(n[e]+256*n[e+1]);return s}function F(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function Q(e,t,r,n,s,i){if(!o.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>s||t<i)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function q(e,t,r,n,s){Y(t,n,s,e,r,7);let i=Number(t&BigInt(0xffffffff));e[r++]=i,i>>=8,e[r++]=i,i>>=8,e[r++]=i,i>>=8,e[r++]=i;let o=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,o>>=8,e[r++]=o,r}function j(e,t,r,n,s){Y(t,n,s,e,r,7);let i=Number(t&BigInt(0xffffffff));e[r+7]=i,i>>=8,e[r+6]=i,i>>=8,e[r+5]=i,i>>=8,e[r+4]=i;let o=Number(t>>BigInt(32)&BigInt(0xffffffff));return e[r+3]=o,o>>=8,e[r+2]=o,o>>=8,e[r+1]=o,o>>=8,e[r]=o,r+8}function W(e,t,r,n,s,i){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function $(e,t,n,s,i){return t*=1,n>>>=0,i||W(e,t,n,4,34028234663852886e22,-34028234663852886e22),r.write(e,t,n,s,23,4),n+4}function G(e,t,n,s,i){return t*=1,n>>>=0,i||W(e,t,n,8,17976931348623157e292,-17976931348623157e292),r.write(e,t,n,s,52,8),n+8}e.Buffer=o,e.SlowBuffer=b,e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,o.TYPED_ARRAY_SUPPORT=s(),!o.TYPED_ARRAY_SUPPORT&&"u">typeof console&&"function"==typeof console.error&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),g(s,"typedArraySupport"),Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:g(function(){if(o.isBuffer(this))return this.buffer},"get")}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:g(function(){if(o.isBuffer(this))return this.byteOffset},"get")}),g(i,"createBuffer"),g(o,"Buffer"),o.poolSize=8192,g(a,"from"),o.from=function(e,t,r){return a(e,t,r)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array),g(u,"assertSize"),g(c,"alloc"),o.alloc=function(e,t,r){return c(e,t,r)},g(h,"allocUnsafe"),o.allocUnsafe=function(e){return h(e)},o.allocUnsafeSlow=function(e){return h(e)},g(l,"fromString"),g(f,"fromArrayLike"),g(p,"fromArrayView"),g(d,"fromArrayBuffer"),g(y,"fromObject"),g(m,"checked"),g(b,"SlowBuffer"),o.isBuffer=g(function(e){return null!=e&&!0===e._isBuffer&&e!==o.prototype},"isBuffer"),o.compare=g(function(e,t){if(eo(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),eo(t,Uint8Array)&&(t=o.from(t,t.offset,t.byteLength)),!o.isBuffer(e)||!o.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let r=e.length,n=t.length;for(let s=0,i=Math.min(r,n);s<i;++s)if(e[s]!==t[s]){r=e[s],n=t[s];break}return r<n?-1:+(n<r)},"compare"),o.isEncoding=g(function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},"isEncoding"),o.concat=g(function(e,t){let r;if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;let n=o.allocUnsafe(t),s=0;for(r=0;r<e.length;++r){let t=e[r];if(eo(t,Uint8Array))s+t.length>n.length?(o.isBuffer(t)||(t=o.from(t)),t.copy(n,s)):Uint8Array.prototype.set.call(n,t,s);else if(o.isBuffer(t))t.copy(n,s);else throw TypeError('"list" argument must be an Array of Buffers');s+=t.length}return n},"concat"),g(w,"byteLength"),o.byteLength=w,g(v,"slowToString"),o.prototype._isBuffer=!0,g(S,"swap"),o.prototype.swap16=g(function(){let e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)S(this,t,t+1);return this},"swap16"),o.prototype.swap32=g(function(){let e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)S(this,t,t+3),S(this,t+1,t+2);return this},"swap32"),o.prototype.swap64=g(function(){let e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)S(this,t,t+7),S(this,t+1,t+6),S(this,t+2,t+5),S(this,t+3,t+4);return this},"swap64"),o.prototype.toString=g(function(){let e=this.length;return 0===e?"":0==arguments.length?B(this,0,e):v.apply(this,arguments)},"toString"),o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=g(function(e){if(!o.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},"equals"),o.prototype.inspect=g(function(){let t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},"inspect"),n&&(o.prototype[n]=o.prototype.inspect),o.prototype.compare=g(function(e,t,r,n,s){if(eo(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),!o.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===s&&(s=this.length),t<0||r>e.length||n<0||s>this.length)throw RangeError("out of range index");if(n>=s&&t>=r)return 0;if(n>=s)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,s>>>=0,this===e)return 0;let i=s-n,a=r-t,u=Math.min(i,a),c=this.slice(n,s),h=e.slice(t,r);for(let e=0;e<u;++e)if(c[e]!==h[e]){i=c[e],a=h[e];break}return i<a?-1:+(a<i)},"compare"),g(E,"bidirectionalIndexOf"),g(_,"arrayIndexOf"),o.prototype.includes=g(function(e,t,r){return -1!==this.indexOf(e,t,r)},"includes"),o.prototype.indexOf=g(function(e,t,r){return E(this,e,t,r,!0)},"indexOf"),o.prototype.lastIndexOf=g(function(e,t,r){return E(this,e,t,r,!1)},"lastIndexOf"),g(C,"hexWrite"),g(L,"utf8Write"),g(P,"asciiWrite"),g(T,"base64Write"),g(R,"ucs2Write"),o.prototype.write=g(function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let s=this.length-t;if((void 0===r||r>s)&&(r=s),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");let i=!1;for(;;)switch(n){case"hex":return C(this,e,t,r);case"utf8":case"utf-8":return L(this,e,t,r);case"ascii":case"latin1":case"binary":return P(this,e,t,r);case"base64":return T(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,e,t,r);default:if(i)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),i=!0}},"write"),o.prototype.toJSON=g(function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},"toJSON"),g(M,"base64Slice"),g(B,"utf8Slice"),g(k,"decodeCodePointsArray"),g(O,"asciiSlice"),g(D,"latin1Slice"),g(U,"hexSlice"),g(N,"utf16leSlice"),o.prototype.slice=g(function(e,t){let r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);let n=this.subarray(e,t);return Object.setPrototypeOf(n,o.prototype),n},"slice"),g(F,"checkOffset"),o.prototype.readUintLE=o.prototype.readUIntLE=g(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let n=this[e],s=1,i=0;for(;++i<t&&(s*=256);)n+=this[e+i]*s;return n},"readUIntLE"),o.prototype.readUintBE=o.prototype.readUIntBE=g(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let n=this[e+--t],s=1;for(;t>0&&(s*=256);)n+=this[e+--t]*s;return n},"readUIntBE"),o.prototype.readUint8=o.prototype.readUInt8=g(function(e,t){return e>>>=0,t||F(e,1,this.length),this[e]},"readUInt8"),o.prototype.readUint16LE=o.prototype.readUInt16LE=g(function(e,t){return e>>>=0,t||F(e,2,this.length),this[e]|this[e+1]<<8},"readUInt16LE"),o.prototype.readUint16BE=o.prototype.readUInt16BE=g(function(e,t){return e>>>=0,t||F(e,2,this.length),this[e]<<8|this[e+1]},"readUInt16BE"),o.prototype.readUint32LE=o.prototype.readUInt32LE=g(function(e,t){return e>>>=0,t||F(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},"readUInt32LE"),o.prototype.readUint32BE=o.prototype.readUInt32BE=g(function(e,t){return e>>>=0,t||F(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},"readUInt32BE"),o.prototype.readBigUInt64LE=ec(g(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&J(e,this.length-8);let n=t+256*this[++e]+65536*this[++e]+0x1000000*this[++e],s=this[++e]+256*this[++e]+65536*this[++e]+0x1000000*r;return BigInt(n)+(BigInt(s)<<BigInt(32))},"readBigUInt64LE")),o.prototype.readBigUInt64BE=ec(g(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];(void 0===t||void 0===r)&&J(e,this.length-8);let n=0x1000000*t+65536*this[++e]+256*this[++e]+this[++e],s=0x1000000*this[++e]+65536*this[++e]+256*this[++e]+r;return(BigInt(n)<<BigInt(32))+BigInt(s)},"readBigUInt64BE")),o.prototype.readIntLE=g(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let n=this[e],s=1,i=0;for(;++i<t&&(s*=256);)n+=this[e+i]*s;return n>=(s*=128)&&(n-=Math.pow(2,8*t)),n},"readIntLE"),o.prototype.readIntBE=g(function(e,t,r){e>>>=0,t>>>=0,r||F(e,t,this.length);let n=t,s=1,i=this[e+--n];for(;n>0&&(s*=256);)i+=this[e+--n]*s;return i>=(s*=128)&&(i-=Math.pow(2,8*t)),i},"readIntBE"),o.prototype.readInt8=g(function(e,t){return e>>>=0,t||F(e,1,this.length),128&this[e]?-((255-this[e]+1)*1):this[e]},"readInt8"),o.prototype.readInt16LE=g(function(e,t){e>>>=0,t||F(e,2,this.length);let r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},"readInt16LE"),o.prototype.readInt16BE=g(function(e,t){e>>>=0,t||F(e,2,this.length);let r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},"readInt16BE"),o.prototype.readInt32LE=g(function(e,t){return e>>>=0,t||F(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},"readInt32LE"),o.prototype.readInt32BE=g(function(e,t){return e>>>=0,t||F(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},"readInt32BE"),o.prototype.readBigInt64LE=ec(g(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];return(void 0===t||void 0===r)&&J(e,this.length-8),(BigInt(this[e+4]+256*this[e+5]+65536*this[e+6]+(r<<24))<<BigInt(32))+BigInt(t+256*this[++e]+65536*this[++e]+0x1000000*this[++e])},"readBigInt64LE")),o.prototype.readBigInt64BE=ec(g(function(e){Z(e>>>=0,"offset");let t=this[e],r=this[e+7];return(void 0===t||void 0===r)&&J(e,this.length-8),(BigInt((t<<24)+65536*this[++e]+256*this[++e]+this[++e])<<BigInt(32))+BigInt(0x1000000*this[++e]+65536*this[++e]+256*this[++e]+r)},"readBigInt64BE")),o.prototype.readFloatLE=g(function(e,t){return e>>>=0,t||F(e,4,this.length),r.read(this,e,!0,23,4)},"readFloatLE"),o.prototype.readFloatBE=g(function(e,t){return e>>>=0,t||F(e,4,this.length),r.read(this,e,!1,23,4)},"readFloatBE"),o.prototype.readDoubleLE=g(function(e,t){return e>>>=0,t||F(e,8,this.length),r.read(this,e,!0,52,8)},"readDoubleLE"),o.prototype.readDoubleBE=g(function(e,t){return e>>>=0,t||F(e,8,this.length),r.read(this,e,!1,52,8)},"readDoubleBE"),g(Q,"checkInt"),o.prototype.writeUintLE=o.prototype.writeUIntLE=g(function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){let n=Math.pow(2,8*r)-1;Q(this,e,t,r,n,0)}let s=1,i=0;for(this[t]=255&e;++i<r&&(s*=256);)this[t+i]=e/s&255;return t+r},"writeUIntLE"),o.prototype.writeUintBE=o.prototype.writeUIntBE=g(function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){let n=Math.pow(2,8*r)-1;Q(this,e,t,r,n,0)}let s=r-1,i=1;for(this[t+s]=255&e;--s>=0&&(i*=256);)this[t+s]=e/i&255;return t+r},"writeUIntBE"),o.prototype.writeUint8=o.prototype.writeUInt8=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,1,255,0),this[t]=255&e,t+1},"writeUInt8"),o.prototype.writeUint16LE=o.prototype.writeUInt16LE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},"writeUInt16LE"),o.prototype.writeUint16BE=o.prototype.writeUInt16BE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},"writeUInt16BE"),o.prototype.writeUint32LE=o.prototype.writeUInt32LE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},"writeUInt32LE"),o.prototype.writeUint32BE=o.prototype.writeUInt32BE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},"writeUInt32BE"),g(q,"wrtBigUInt64LE"),g(j,"wrtBigUInt64BE"),o.prototype.writeBigUInt64LE=ec(g(function(e,t=0){return q(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},"writeBigUInt64LE")),o.prototype.writeBigUInt64BE=ec(g(function(e,t=0){return j(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))},"writeBigUInt64BE")),o.prototype.writeIntLE=g(function(e,t,r,n){if(e*=1,t>>>=0,!n){let n=Math.pow(2,8*r-1);Q(this,e,t,r,n-1,-n)}let s=0,i=1,o=0;for(this[t]=255&e;++s<r&&(i*=256);)e<0&&0===o&&0!==this[t+s-1]&&(o=1),this[t+s]=(e/i|0)-o&255;return t+r},"writeIntLE"),o.prototype.writeIntBE=g(function(e,t,r,n){if(e*=1,t>>>=0,!n){let n=Math.pow(2,8*r-1);Q(this,e,t,r,n-1,-n)}let s=r-1,i=1,o=0;for(this[t+s]=255&e;--s>=0&&(i*=256);)e<0&&0===o&&0!==this[t+s+1]&&(o=1),this[t+s]=(e/i|0)-o&255;return t+r},"writeIntBE"),o.prototype.writeInt8=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},"writeInt8"),o.prototype.writeInt16LE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},"writeInt16LE"),o.prototype.writeInt16BE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},"writeInt16BE"),o.prototype.writeInt32LE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},"writeInt32LE"),o.prototype.writeInt32BE=g(function(e,t,r){return e*=1,t>>>=0,r||Q(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},"writeInt32BE"),o.prototype.writeBigInt64LE=ec(g(function(e,t=0){return q(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},"writeBigInt64LE")),o.prototype.writeBigInt64BE=ec(g(function(e,t=0){return j(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))},"writeBigInt64BE")),g(W,"checkIEEE754"),g($,"writeFloat"),o.prototype.writeFloatLE=g(function(e,t,r){return $(this,e,t,!0,r)},"writeFloatLE"),o.prototype.writeFloatBE=g(function(e,t,r){return $(this,e,t,!1,r)},"writeFloatBE"),g(G,"writeDouble"),o.prototype.writeDoubleLE=g(function(e,t,r){return G(this,e,t,!0,r)},"writeDoubleLE"),o.prototype.writeDoubleBE=g(function(e,t,r){return G(this,e,t,!1,r)},"writeDoubleBE"),o.prototype.copy=g(function(e,t,r,n){if(!o.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);let s=n-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,n):Uint8Array.prototype.set.call(e,this.subarray(r,n),t),s},"copy"),o.prototype.fill=g(function(e,t,r,n){let s;if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!o.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){let t=e.charCodeAt(0);("utf8"===n&&t<128||"latin1"===n)&&(e=t)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{let i=o.isBuffer(e)?e:o.from(e,n),a=i.length;if(0===a)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(s=0;s<r-t;++s)this[s+t]=i[s%a]}return this},"fill");var H={};function V(e,t,r){var n;H[e]=(g(n=class extends r{constructor(){super(),Object.defineProperty(this,"message",{value:t.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${e}]`,this.stack,delete this.name}get code(){return e}set code(e){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:e,writable:!0})}toString(){return`${this.name} [${e}\
]: ${this.message}`}},"NodeError"),n)}function z(e){let t="",r=e.length,n=+("-"===e[0]);for(;r>=n+4;r-=3)t=`\
_${e.slice(r-3,r)}${t}`;return`${e.slice(0,r)}${t}`}function K(e,t,r){Z(t,"offset"),(void 0===e[t]||void 0===e[t+r])&&J(t,e.length-(r+1))}function Y(e,t,r,n,s,i){if(e>r||e<t){let n="bigint"==typeof t?"n":"",s;throw s=i>3?0===t||t===BigInt(0)?`>= 0${n} and < 2${n}\
 ** ${(i+1)*8}${n}`:`>= -(2${n} ** ${(i+1)*8-1}${n}) and < 2 ** ${(i+1)*8-1}${n}`:`>= ${t}${n} a\
nd <= ${r}${n}`,new H.ERR_OUT_OF_RANGE("value",s,e)}K(n,s,i)}function Z(e,t){if("number"!=typeof e)throw new H.ERR_INVALID_ARG_TYPE(t,"number",e)}function J(e,t,r){throw Math.floor(e)!==e?(Z(e,r),new H.ERR_OUT_OF_RANGE(r||"offset","an integer",e)):t<0?new H.ERR_BUFFER_OUT_OF_BOUNDS:new H.ERR_OUT_OF_RANGE(r||"offset",`>= ${+!!r} and <= ${t}`,e)}g(V,"E"),V("ERR_BUFFER_OUT_OF_BOUNDS",function(e){return e?`${e} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),V("ERR_INVALID_ARG_TYPE",function(e,t){return`The "${e}" argument must be of type number. Received typ\
e ${typeof t}`},TypeError),V("ERR_OUT_OF_RANGE",function(e,t,r){let n=`The value of "${e}" is out o\
f range.`,s=r;return Number.isInteger(r)&&Math.abs(r)>0x100000000?s=z(String(r)):"bigint"==typeof r&&(s=String(r),(r>BigInt(2)**BigInt(32)||r<-(BigInt(2)**BigInt(32)))&&(s=z(s)),s+="n"),n+=` It must be ${t}. Re\
ceived ${s}`},RangeError),g(z,"addNumericalSeparator"),g(K,"checkBounds"),g(Y,"checkIntBI"),g(Z,"validateNumber"),g(J,"boundsError");var X=/[^+/0-9A-Za-z-_]/g;function ee(e){if((e=(e=e.split("=")[0]).trim().replace(X,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}function et(e,t){t=t||1/0;let r,n=e.length,s=null,i=[];for(let o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!s){if(r>56319||o+1===n){(t-=3)>-1&&i.push(239,191,189);continue}s=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),s=r;continue}r=(s-55296<<10|r-56320)+65536}else s&&(t-=3)>-1&&i.push(239,191,189);if(s=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function er(e){let t=[];for(let r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function en(e,t){let r,n,s=[];for(let i=0;i<e.length&&!((t-=2)<0);++i)n=(r=e.charCodeAt(i))>>8,s.push(r%256),s.push(n);return s}function es(e){return t.toByteArray(ee(e))}function ei(e,t,r,n){let s;for(s=0;s<n&&!(s+r>=t.length||s>=e.length);++s)t[s+r]=e[s];return s}function eo(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function ea(e){return e!=e}g(ee,"base64clean"),g(et,"utf8ToBytes"),g(er,"asciiToBytes"),g(en,"utf16leToBytes"),g(es,"base64ToBytes"),g(ei,"blitBuffer"),g(eo,"isInstance"),g(ea,"numberIsNaN");var eu=function(){let e="0123456789abcdef",t=Array(256);for(let r=0;r<16;++r){let n=16*r;for(let s=0;s<16;++s)t[n+s]=e[r]+e[s]}return t}();function ec(e){return typeof BigInt>"u"?eh:e}function eh(){throw Error("BigInt not supported")}g(ec,"defineBigIntMethod"),g(eh,"BufferBigIntNotDefined")}),I=m(()=>{"use strict";i=globalThis,o=globalThis.setImmediate??(e=>setTimeout(e,0)),globalThis.clearImmediate??(e=>clearTimeout(e)),a="function"==typeof globalThis.Buffer&&"function"==typeof globalThis.Buffer.allocUnsafe?globalThis.Buffer:C().Buffer,(u=globalThis.process??{}).env??(u.env={});try{u.nextTick(()=>{})}catch{let e=Promise.resolve();u.nextTick=e.then.bind(e)}}),L=b((e,t)=>{"use strict";I();var r,n="object"==typeof Reflect?Reflect:null,s=n&&"function"==typeof n.apply?n.apply:g(function(e,t,r){return Function.prototype.apply.call(e,t,r)},"ReflectApply");function i(e){console&&console.warn&&console.warn(e)}r=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?g(function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))},"ReflectOwnKeys"):g(function(e){return Object.getOwnPropertyNames(e)},"ReflectOwnKeys"),g(i,"ProcessEmitWarning");var o=Number.isNaN||g(function(e){return e!=e},"NumberIsNaN");function a(){a.init.call(this)}g(a,"EventEmitter"),t.exports=a,t.exports.once=v,a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var u=10;function c(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function h(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function l(e,t,r,n){var s,o,a;if(c(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),a=o[t]),void 0===a)a=o[t]=r,++e._eventsCount;else if("function"==typeof a?a=o[t]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),(s=h(e))>0&&a.length>s&&!a.warned){a.warned=!0;var u=Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=a.length,i(u)}return e}function f(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function p(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},s=f.bind(n);return s.listener=r,n.wrapFn=s,s}function d(e,t,r){var n=e._events;if(void 0===n)return[];var s=n[t];return void 0===s?[]:"function"==typeof s?r?[s.listener||s]:[s]:r?w(s):m(s,s.length)}function y(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function m(e,t){for(var r=Array(t),n=0;n<t;++n)r[n]=e[n];return r}function b(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function w(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}function v(e,t){return new Promise(function(r,n){function s(r){e.removeListener(t,i),n(r)}function i(){"function"==typeof e.removeListener&&e.removeListener("error",s),r([].slice.call(arguments))}g(s,"errorListener"),g(i,"resolver"),E(e,t,i,{once:!0}),"error"!==t&&S(e,s,{once:!0})})}function S(e,t,r){"function"==typeof e.on&&E(e,"error",t,r)}function E(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else if("function"==typeof e.addEventListener)e.addEventListener(t,g(function s(i){n.once&&e.removeEventListener(t,s),r(i)},"wrapListener"));else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}g(c,"checkListener"),Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:g(function(){return u},"get"),set:g(function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");u=e},"set")}),a.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=g(function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},"setMaxListeners"),g(h,"_getMaxListeners"),a.prototype.getMaxListeners=g(function(){return h(this)},"getMaxListeners"),a.prototype.emit=g(function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var o,a=Error("Unhandled error."+(o?" ("+o.message+")":""));throw a.context=o,a}var u=i[e];if(void 0===u)return!1;if("function"==typeof u)s(u,this,t);else for(var c=u.length,h=m(u,c),r=0;r<c;++r)s(h[r],this,t);return!0},"emit"),g(l,"_addListener"),a.prototype.addListener=g(function(e,t){return l(this,e,t,!1)},"addListener"),a.prototype.on=a.prototype.addListener,a.prototype.prependListener=g(function(e,t){return l(this,e,t,!0)},"prependListener"),g(f,"onceWrapper"),g(p,"_onceWrap"),a.prototype.once=g(function(e,t){return c(t),this.on(e,p(this,e,t)),this},"once"),a.prototype.prependOnceListener=g(function(e,t){return c(t),this.prependListener(e,p(this,e,t)),this},"prependOnceListener"),a.prototype.removeListener=g(function(e,t){var r,n,s,i,o;if(c(t),void 0===(n=this._events)||void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(s=-1,i=r.length-1;i>=0;i--)if(r[i]===t||r[i].listener===t){o=r[i].listener,s=i;break}if(s<0)return this;0===s?r.shift():b(r,s),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},"removeListener"),a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=g(function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var s,i=Object.keys(r);for(n=0;n<i.length;++n)"removeListener"!==(s=i[n])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},"removeAllListeners"),g(d,"_listeners"),a.prototype.listeners=g(function(e){return d(this,e,!0)},"listeners"),a.prototype.rawListeners=g(function(e){return d(this,e,!1)},"rawListeners"),a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):y.call(e,t)},a.prototype.listenerCount=y,g(y,"listenerCount"),a.prototype.eventNames=g(function(){return this._eventsCount>0?r(this._events):[]},"eventNames"),g(m,"arrayClone"),g(b,"spliceOne"),g(w,"unwrapListeners"),g(v,"once"),g(S,"addErrorHandlerIfEventEmitter"),g(E,"eventTargetAgnosticAddListener")}),P={};function T(e){return 0}w(P,{Socket:()=>k,isIP:()=>T});var R,M,B,k,O=m(()=>{"use s\
trict";I(),R=S(L(),1),g(T,"isIP"),M=/^[^.]+\./,B=class e extends R.EventEmitter{constructor(){super(...arguments),_(this,"opts",{}),_(this,"connecting",!1),_(this,"pending",!0),_(this,"writable",!0),_(this,"encrypted",!1),_(this,"authorized",!1),_(this,"destroyed",!1),_(this,"ws",null),_(this,"writeBuffer"),_(this,"tlsState",0),_(this,"tlsRead"),_(this,"tlsWrite")}static get poolQueryViaFetch(){return e.opts.poolQueryViaFetch??e.defaults.poolQueryViaFetch}static set poolQueryViaFetch(t){e.opts.poolQueryViaFetch=t}static get fetchEndpoint(){return e.opts.fetchEndpoint??e.defaults.fetchEndpoint}static set fetchEndpoint(t){e.opts.fetchEndpoint=t}static get fetchConnectionCache(){return!0}static set fetchConnectionCache(e){console.warn("The `fetchConnectionCache` option is deprecated (now always `true`)")}static get fetchFunction(){return e.opts.fetchFunction??e.defaults.fetchFunction}static set fetchFunction(t){e.opts.fetchFunction=t}static get webSocketConstructor(){return e.opts.webSocketConstructor??e.defaults.webSocketConstructor}static set webSocketConstructor(t){e.opts.webSocketConstructor=t}get webSocketConstructor(){return this.opts.webSocketConstructor??e.webSocketConstructor}set webSocketConstructor(e){this.opts.webSocketConstructor=e}static get wsProxy(){return e.opts.wsProxy??e.defaults.wsProxy}static set wsProxy(t){e.opts.wsProxy=t}get wsProxy(){return this.opts.wsProxy??e.wsProxy}set wsProxy(e){this.opts.wsProxy=e}static get coalesceWrites(){return e.opts.coalesceWrites??e.defaults.coalesceWrites}static set coalesceWrites(t){e.opts.coalesceWrites=t}get coalesceWrites(){return this.opts.coalesceWrites??e.coalesceWrites}set coalesceWrites(e){this.opts.coalesceWrites=e}static get useSecureWebSocket(){return e.opts.useSecureWebSocket??e.defaults.useSecureWebSocket}static set useSecureWebSocket(t){e.opts.useSecureWebSocket=t}get useSecureWebSocket(){return this.opts.useSecureWebSocket??e.useSecureWebSocket}set useSecureWebSocket(e){this.opts.useSecureWebSocket=e}static get forceDisablePgSSL(){return e.opts.forceDisablePgSSL??e.defaults.forceDisablePgSSL}static set forceDisablePgSSL(t){e.opts.forceDisablePgSSL=t}get forceDisablePgSSL(){return this.opts.forceDisablePgSSL??e.forceDisablePgSSL}set forceDisablePgSSL(e){this.opts.forceDisablePgSSL=e}static get disableSNI(){return e.opts.disableSNI??e.defaults.disableSNI}static set disableSNI(t){e.opts.disableSNI=t}get disableSNI(){return this.opts.disableSNI??e.disableSNI}set disableSNI(e){this.opts.disableSNI=e}static get disableWarningInBrowsers(){return e.opts.disableWarningInBrowsers??e.defaults.disableWarningInBrowsers}static set disableWarningInBrowsers(t){e.opts.disableWarningInBrowsers=t}get disableWarningInBrowsers(){return this.opts.disableWarningInBrowsers??e.disableWarningInBrowsers}set disableWarningInBrowsers(e){this.opts.disableWarningInBrowsers=e}static get pipelineConnect(){return e.opts.pipelineConnect??e.defaults.pipelineConnect}static set pipelineConnect(t){e.opts.pipelineConnect=t}get pipelineConnect(){return this.opts.pipelineConnect??e.pipelineConnect}set pipelineConnect(e){this.opts.pipelineConnect=e}static get subtls(){return e.opts.subtls??e.defaults.subtls}static set subtls(t){e.opts.subtls=t}get subtls(){return this.opts.subtls??e.subtls}set subtls(e){this.opts.subtls=e}static get pipelineTLS(){return e.opts.pipelineTLS??e.defaults.pipelineTLS}static set pipelineTLS(t){e.opts.pipelineTLS=t}get pipelineTLS(){return this.opts.pipelineTLS??e.pipelineTLS}set pipelineTLS(e){this.opts.pipelineTLS=e}static get rootCerts(){return e.opts.rootCerts??e.defaults.rootCerts}static set rootCerts(t){e.opts.rootCerts=t}get rootCerts(){return this.opts.rootCerts??e.rootCerts}set rootCerts(e){this.opts.rootCerts=e}wsProxyAddrForHost(e,t){let r=this.wsProxy;if(void 0===r)throw Error("No WebSocket proxy is configured. Please see https://github.com/neondatabase/serverless/blob/main/CONFIG.md#wsproxy-string--host-string-port-number--string--string");return"function"==typeof r?r(e,t):`${r}?address=${e}:${t}`}setNoDelay(){return this}setKeepAlive(){return this}ref(){return this}unref(){return this}connect(e,t,r){this.connecting=!0,r&&this.once("connect",r);let n=g(()=>{this.connecting=!1,this.pending=!1,this.emit("connect"),this.emit("ready")},"handleWebSocketOpen"),s=g((e,t=!1)=>{e.binaryType="arraybuffer",e.addEventListener("error",e=>{this.emit("error",e),this.emit("close")}),e.addEventListener("message",e=>{if(0===this.tlsState){let t=a.from(e.data);this.emit("data",t)}}),e.addEventListener("close",()=>{this.emit("close")}),t?n():e.addEventListener("open",n)},"configureWebSocket"),i;try{i=this.wsProxyAddrForHost(t,"string"==typeof e?parseInt(e,10):e)}catch(e){this.emit("error",e),this.emit("close");return}try{let e=(this.useSecureWebSocket?"wss:":"ws:")+"//"+i;if(void 0!==this.webSocketConstructor)this.ws=new this.webSocketConstructor(e),s(this.ws);else try{this.ws=new WebSocket(e),s(this.ws)}catch{this.ws=new __unstable_WebSocket(e),s(this.ws)}}catch(e){fetch((this.useSecureWebSocket?"https:":"http:")+"//"+i,{headers:{Upgrade:"websocket"}}).then(t=>{if(this.ws=t.webSocket,null==this.ws)throw e;this.ws.accept(),s(this.ws,!0)}).catch(e=>{this.emit("error",Error(`All attempts to open a WebSocket to connect to the database failed. Please refer \
to https://github.com/neondatabase/serverless/blob/main/CONFIG.md#websocketconstructor-typeof-websoc\
ket--undefined. Details: ${e}`)),this.emit("close")})}}async startTls(e){if(void 0===this.subtls)throw Error("For Postgres SSL connections, you must set `neonConfig.subtls` to the subtls library. See https://github.com/neondatabase/serverless/blob/main/CONFIG.md for more information.");this.tlsState=1;let t=await this.subtls.TrustedCert.databaseFromPEM(this.rootCerts),r=new this.subtls.WebSocketReadQueue(this.ws),n=r.read.bind(r),s=this.rawWrite.bind(this),{read:i,write:o}=await this.subtls.startTls(e,t,n,s,{useSNI:!this.disableSNI,expectPreData:this.pipelineTLS?new Uint8Array([83]):void 0});this.tlsRead=i,this.tlsWrite=o,this.tlsState=2,this.encrypted=!0,this.authorized=!0,this.emit("secureConnection",this),this.tlsReadLoop()}async tlsReadLoop(){for(;;){let e=await this.tlsRead();if(void 0===e)break;{let t=a.from(e);this.emit("data",t)}}}rawWrite(e){if(!this.coalesceWrites){this.ws&&this.ws.send(e);return}if(void 0===this.writeBuffer)this.writeBuffer=e,setTimeout(()=>{this.ws&&this.ws.send(this.writeBuffer),this.writeBuffer=void 0},0);else{let t=new Uint8Array(this.writeBuffer.length+e.length);t.set(this.writeBuffer),t.set(e,this.writeBuffer.length),this.writeBuffer=t}}write(e,t="utf8",r=e=>{}){return 0===e.length?r():("string"==typeof e&&(e=a.from(e,t)),0===this.tlsState?(this.rawWrite(e),r()):1===this.tlsState?this.once("secureConnection",()=>{this.write(e,t,r)}):(this.tlsWrite(e),r())),!0}end(e=a.alloc(0),t="utf8",r=()=>{}){return this.write(e,t,()=>{this.ws.close(),r()}),this}destroy(){return this.destroyed=!0,this.end()}},g(B,"Socket"),_(B,"defaults",{poolQueryViaFetch:!1,fetchEndpoint:g((e,t,r)=>{let n;return"https://"+(r?.jwtAuth?e.replace(M,"apiauth."):e.replace(M,"api."))+"/sql"},"fetchEndpoint"),fetchConnectionCache:!0,fetchFunction:void 0,webSocketConstructor:void 0,wsProxy:g(e=>e+"/v2","wsProxy"),useSecureWebSocket:!0,forceDisablePgSSL:!0,coalesceWrites:!0,pipelineConnect:"password",subtls:void 0,rootCerts:"",pipelineTLS:!1,disableSNI:!1,disableWarningInBrowsers:!1}),_(B,"opts",{}),k=B}),D={};function U(e,t=!1){let{protocol:r}=new URL(e),{username:n,password:s,host:i,hostname:o,port:a,pathname:u,search:c,searchParams:h,hash:l}=new URL("http:"+e.substring(r.length));s=decodeURIComponent(s),n=decodeURIComponent(n),u=decodeURIComponent(u);let f=n+":"+s,p=t?Object.fromEntries(h.entries()):c;return{href:e,protocol:r,auth:f,username:n,password:s,host:i,hostname:o,port:a,pathname:u,search:c,query:p,hash:l}}w(D,{parse:()=>U});var N=m(()=>{"use strict";I(),g(U,"parse")}),F=b(e=>{"use strict";I(),e.parse=function(e,t){return new r(e,t).parse()};var t=class e{constructor(e,t){this.source=e,this.transform=t||n,this.position=0,this.entries=[],this.recorded=[],this.dimension=0}isEof(){return this.position>=this.source.length}nextCharacter(){var e=this.source[this.position++];return"\\"===e?{value:this.source[this.position++],escaped:!0}:{value:e,escaped:!1}}record(e){this.recorded.push(e)}newEntry(e){var t;(this.recorded.length>0||e)&&("NULL"!==(t=this.recorded.join(""))||e||(t=null),null!==t&&(t=this.transform(t)),this.entries.push(t),this.recorded=[])}consumeDimensions(){if("["===this.source[0])for(;!this.isEof()&&"="!==this.nextCharacter().value;);}parse(t){var r,n,s;for(this.consumeDimensions();!this.isEof();)if("{"!==(r=this.nextCharacter()).value||s){if("}"!==r.value||s)'"'!==r.value||r.escaped?","!==r.value||s?this.record(r.value):this.newEntry():(s&&this.newEntry(!0),s=!s);else if(this.dimension--,!this.dimension&&(this.newEntry(),t))return this.entries}else this.dimension++,this.dimension>1&&(n=new e(this.source.substr(this.position-1),this.transform),this.entries.push(n.parse(!0)),this.position+=n.position-2);if(0!==this.dimension)throw Error("array dimension not balanced");return this.entries}};g(t,"ArrayParser");var r=t;function n(e){return e}g(n,"identity")}),Q=b((e,t)=>{I();var r=F();t.exports={create:g(function(e,t){return{parse:g(function(){return r.parse(e,t)},"parse")}},"create")}}),q=b((e,t)=>{"use strict";I();var r=/(\d{1,})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})(\.\d{1,})?.*?( BC)?$/,n=/^(\d{1,})-(\d{2})-(\d{2})( BC)?$/,s=/([Z+-])(\d{2})?:?(\d{2})?:?(\d{2})?/,i=/^-?infinity$/;function o(e){var t=n.exec(e);if(t){var r=parseInt(t[1],10);t[4]&&(r=u(r));var s=new Date(r,parseInt(t[2],10)-1,t[3]);return c(r)&&s.setFullYear(r),s}}function a(e){if(e.endsWith("+00"))return 0;var t=s.exec(e.split(" ")[1]);if(t){var r=t[1];return"Z"===r?0:(3600*parseInt(t[2],10)+60*parseInt(t[3]||0,10)+parseInt(t[4]||0,10))*("-"===r?-1:1)*1e3}}function u(e){return-(e-1)}function c(e){return e>=0&&e<100}t.exports=g(function(e){if(i.test(e))return Number(e.replace("i","I"));var t=r.exec(e);if(!t)return o(e)||null;var n=!!t[8],s=parseInt(t[1],10);n&&(s=u(s));var h=parseInt(t[2],10)-1,l=t[3],f=parseInt(t[4],10),p=parseInt(t[5],10),d=parseInt(t[6],10),y=t[7];y=y?1e3*parseFloat(y):0;var g,m=a(e);return null!=m?(g=new Date(Date.UTC(s,h,l,f,p,d,y)),c(s)&&g.setUTCFullYear(s),0!==m&&g.setTime(g.getTime()-m)):(g=new Date(s,h,l,f,p,d,y),c(s)&&g.setFullYear(s)),g},"parseDate"),g(o,"getDate"),g(a,"timeZoneOffset"),g(u,"bcYearToNegativeYear"),g(c,"is0To99")}),j=b((e,t)=>{I(),t.exports=n;var r=Object.prototype.hasOwnProperty;function n(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var s in n)r.call(n,s)&&(e[s]=n[s])}return e}g(n,"extend")}),W=b((e,t)=>{"use strict";I();var r=j();function n(e){if(!(this instanceof n))return new n(e);r(this,p(e))}t.exports=n,g(n,"PostgresInterval");var s=["seconds","minutes","hours","days","months","years"];n.prototype.toPostgres=function(){var e=s.filter(this.hasOwnProperty,this);return this.milliseconds&&0>e.indexOf("seconds")&&e.push("seconds"),0===e.length?"0":e.map(function(e){var t=this[e]||0;return"seconds"===e&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/\.?0+$/,"")),t+" "+e},this).join(" ")};var i={years:"Y",months:"M",days:"D",hours:"H",minutes:"M",seconds:"S"},o=["years","months","days"],a=["hours","minutes","seconds"];n.prototype.toISOString=n.prototype.toISO=function(){return"P"+o.map(e,this).join("")+"T"+a.map(e,this).join("");function e(e){var t=this[e]||0;return"seconds"===e&&this.milliseconds&&(t=(t+this.milliseconds/1e3).toFixed(6).replace(/0+$/,"")),t+i[e]}};var u="([+-]?\\d+)",c=new RegExp([u+"\\s+years?",u+"\\s+mons?",u+"\\s+days?","([+-])?([\\d]*):(\\d\\d):(\\d\\d)\\.?(\\d{1,6})?"].map(function(e){return"("+e+")?"}).join("\\s*")),h={years:2,months:4,days:6,hours:9,minutes:10,seconds:11,milliseconds:12},l=["hours","minutes","seconds","milliseconds"];function f(e){return parseInt(e+"000000".slice(e.length),10)/1e3}function p(e){if(!e)return{};var t=c.exec(e),r="-"===t[8];return Object.keys(h).reduce(function(e,n){var s=t[h[n]];return s&&(s="milliseconds"===n?f(s):parseInt(s,10))&&(r&&~l.indexOf(n)&&(s*=-1),e[n]=s),e},{})}g(f,"parseMilliseconds"),g(p,"parse")}),$=b((e,t)=>{"use strict";I(),t.exports=g(function(e){if(/^\\x/.test(e))return new a(e.substr(2),"hex");for(var t="",r=0;r<e.length;)if("\\"!==e[r])t+=e[r],++r;else if(/[0-7]{3}/.test(e.substr(r+1,3)))t+=String.fromCharCode(parseInt(e.substr(r+1,3),8)),r+=4;else{for(var n=1;r+n<e.length&&"\\"===e[r+n];)n++;for(var s=0;s<Math.floor(n/2);++s)t+="\\";r+=2*Math.floor(n/2)}return new a(t,"binary")},"parseBytea")}),G=b((e,t)=>{I();var r=F(),n=Q(),s=q(),i=W(),o=$();function a(e){return g(function(t){return null===t?t:e(t)},"nullAllowed")}function u(e){return null===e?e:"TRUE"===e||"t"===e||"true"===e||"y"===e||"yes"===e||"on"===e||"1"===e}function c(e){return e?r.parse(e,u):null}function h(e){return parseInt(e,10)}function l(e){return e?r.parse(e,a(h)):null}function f(e){return e?r.parse(e,a(function(e){return S(e).trim()})):null}g(a,"allowNull"),g(u,"parseBool"),g(c,"parseBoolArray"),g(h,"parseBaseTenInt"),g(l,"parseIntegerArray"),g(f,"parseBigIntegerArray");var p=g(function(e){return e?n.create(e,function(e){return null!==e&&(e=_(e)),e}).parse():null},"parsePointArray"),d=g(function(e){return e?n.create(e,function(e){return null!==e&&(e=parseFloat(e)),e}).parse():null},"parseFloatArray"),y=g(function(e){return e?n.create(e).parse():null},"parseStringArray"),m=g(function(e){return e?n.create(e,function(e){return null!==e&&(e=s(e)),e}).parse():null},"parseDateArray"),b=g(function(e){return e?n.create(e,function(e){return null!==e&&(e=i(e)),e}).parse():null},"parseIntervalArray"),w=g(function(e){return e?r.parse(e,a(o)):null},"parseByteAArray"),v=g(function(e){return parseInt(e,10)},"parseInteger"),S=g(function(e){var t=String(e);return/^\d+$/.test(t)?t:e},"parseBigInteger"),E=g(function(e){return e?r.parse(e,a(JSON.parse)):null},"parseJsonArray"),_=g(function(e){return"("!==e[0]?null:{x:parseFloat((e=e.substring(1,e.length-1).split(","))[0]),y:parseFloat(e[1])}},"parsePoint"),x=g(function(e){if("<"!==e[0]&&"("!==e[1])return null;for(var t="(",r="",n=!1,s=2;s<e.length-1;s++){if(n||(t+=e[s]),")"===e[s]){n=!0;continue}n&&","!==e[s]&&(r+=e[s])}var i=_(t);return i.radius=parseFloat(r),i},"parseCircle");t.exports={init:g(function(e){e(20,S),e(21,v),e(23,v),e(26,v),e(700,parseFloat),e(701,parseFloat),e(16,u),e(1082,s),e(1114,s),e(1184,s),e(600,_),e(651,y),e(718,x),e(1e3,c),e(1001,w),e(1005,l),e(1007,l),e(1028,l),e(1016,f),e(1017,p),e(1021,d),e(1022,d),e(1231,d),e(1014,y),e(1015,y),e(1008,y),e(1009,y),e(1040,y),e(1041,y),e(1115,m),e(1182,m),e(1185,m),e(1186,i),e(1187,b),e(17,o),e(114,JSON.parse.bind(JSON)),e(3802,JSON.parse.bind(JSON)),e(199,E),e(3807,E),e(3907,y),e(2951,y),e(791,y),e(1183,y),e(1270,y)},"init")}}),H=b((e,t)=>{"use strict";function r(e){var t=e.readInt32BE(0),r=e.readUInt32BE(4),n="";t<0&&(t=~t+(0===r),r=~r+1>>>0,n="-");var s,i,o,a,u,c,h="";if(s=t%1e6,t=t/1e6>>>0,r=(i=0x100000000*s+r)/1e6>>>0,o=""+(i-1e6*r),0===r&&0===t)return n+o+h;for(a="",u=6-o.length,c=0;c<u;c++)a+="0";if(h=a+o+h,s=t%1e6,t=t/1e6>>>0,r=(i=0x100000000*s+r)/1e6>>>0,o=""+(i-1e6*r),0===r&&0===t)return n+o+h;for(a="",u=6-o.length,c=0;c<u;c++)a+="0";if(h=a+o+h,s=t%1e6,t=t/1e6>>>0,r=(i=0x100000000*s+r)/1e6>>>0,o=""+(i-1e6*r),0===r&&0===t)return n+o+h;for(a="",u=6-o.length,c=0;c<u;c++)a+="0";return h=a+o+h,n+(o=""+(i=0x100000000*(s=t%1e6)+r)%1e6)+h}I(),g(r,"readInt8"),t.exports=r}),V=b((e,t)=>{I();var r=H(),n=g(function(e,t,r,n,s){r=r||0,n=n||!1,s=s||function(e,t,r){return e*Math.pow(2,r)+t};var i=r>>3,o=g(function(e){return n?255&~e:e},"inv"),a=255,u=8-r%8;t<u&&(a=255<<8-t&255,u=t),r&&(a>>=r%8);var c=0;r%8+t>=8&&(c=s(0,o(e[i])&a,u));for(var h=t+r>>3,l=i+1;l<h;l++)c=s(c,o(e[l]),8);var f=(t+r)%8;return f>0&&(c=s(c,o(e[h])>>8-f,f)),c},"parseBits"),s=g(function(e,t,r){var s=Math.pow(2,r-1)-1,i=n(e,1),o=n(e,r,1);if(0===o)return 0;var a=1,u=n(e,t,r+1,!1,g(function(e,t,r){0===e&&(e=1);for(var n=1;n<=r;n++)a/=2,(t&1<<r-n)>0&&(e+=a);return e},"parsePrecisionBits"));return o==Math.pow(2,r+1)-1?0===u?0===i?1/0:-1/0:NaN:(0===i?1:-1)*Math.pow(2,o-s)*u},"parseFloatFromBits"),i=g(function(e){return 1==n(e,1)?-1*(n(e,15,1,!0)+1):n(e,15,1)},"parseInt16"),o=g(function(e){return 1==n(e,1)?-1*(n(e,31,1,!0)+1):n(e,31,1)},"parseInt32"),a=g(function(e){return s(e,23,8)},"parseFloat32"),u=g(function(e){return s(e,52,11)},"parseFloat64"),c=g(function(e){var t=n(e,16,32);if(49152==t)return NaN;for(var r=Math.pow(1e4,n(e,16,16)),s=0,i=n(e,16),o=0;o<i;o++)s+=n(e,16,64+16*o)*r,r/=1e4;var a=Math.pow(10,n(e,16,48));return(0===t?1:-1)*Math.round(s*a)/a},"parseNumeric"),h=g(function(e,t){var r=n(t,1),s=n(t,63,1),i=new Date((0===r?1:-1)*s/1e3+9466848e5);return e||i.setTime(i.getTime()+6e4*i.getTimezoneOffset()),i.usec=s%1e3,i.getMicroSeconds=function(){return this.usec},i.setMicroSeconds=function(e){this.usec=e},i.getUTCMicroSeconds=function(){return this.usec},i},"parseDate"),l=g(function(e){for(var t=n(e,32),r=(n(e,32,32),n(e,32,64)),s=96,i=[],o=0;o<t;o++)i[o]=n(e,32,s),s+=64;var a=g(function(t){var r,i=n(e,32,s);return(s+=32,0xffffffff==i)?null:23==t||20==t?(r=n(e,8*i,s),s+=8*i,r):25==t?e.toString(this.encoding,s>>3,(s+=i<<3)>>3):void console.log("ERROR: ElementType not implemented: "+t)},"parseElement"),u=g(function(e,t){var r,n=[];if(e.length>1){var s=e.shift();for(r=0;r<s;r++)n[r]=u(e,t);e.unshift(s)}else for(r=0;r<e[0];r++)n[r]=a(t);return n},"parse");return u(i,r)},"parseArray"),f=g(function(e){return e.toString("utf8")},"parseText"),p=g(function(e){return null===e?null:n(e,8)>0},"parseBool");t.exports={init:g(function(e){e(20,r),e(21,i),e(23,o),e(26,o),e(1700,c),e(700,a),e(701,u),e(16,p),e(1114,h.bind(null,!1)),e(1184,h.bind(null,!0)),e(1e3,l),e(1007,l),e(1016,l),e(1008,l),e(1009,l),e(25,f)},"init")}}),z=b((e,t)=>{I(),t.exports={BOOL:16,BYTEA:17,CHAR:18,INT8:20,INT2:21,INT4:23,REGPROC:24,TEXT:25,OID:26,TID:27,XID:28,CID:29,JSON:114,XML:142,PG_NODE_TREE:194,SMGR:210,PATH:602,POLYGON:604,CIDR:650,FLOAT4:700,FLOAT8:701,ABSTIME:702,RELTIME:703,TINTERVAL:704,CIRCLE:718,MACADDR8:774,MONEY:790,MACADDR:829,INET:869,ACLITEM:1033,BPCHAR:1042,VARCHAR:1043,DATE:1082,TIME:1083,TIMESTAMP:1114,TIMESTAMPTZ:1184,INTERVAL:1186,TIMETZ:1266,BIT:1560,VARBIT:1562,NUMERIC:1700,REFCURSOR:1790,REGPROCEDURE:2202,REGOPER:2203,REGOPERATOR:2204,REGCLASS:2205,REGTYPE:2206,UUID:2950,TXID_SNAPSHOT:2970,PG_LSN:3220,PG_NDISTINCT:3361,PG_DEPENDENCIES:3402,TSVECTOR:3614,TSQUERY:3615,GTSVECTOR:3642,REGCONFIG:3734,REGDICTIONARY:3769,JSONB:3802,REGNAMESPACE:4089,REGROLE:4096}}),K=b(e=>{I();var t=G(),r=V(),n=Q(),s=z();e.getTypeParser=a,e.setTypeParser=u,e.arrayParser=n,e.builtins=s;var i={text:{},binary:{}};function o(e){return String(e)}function a(e,t){return i[t=t||"text"]&&i[t][e]||o}function u(e,t,r){"function"==typeof t&&(r=t,t="text"),i[t][e]=r}g(o,"noParse"),g(a,"getTypeParser"),g(u,"setTypeParser"),t.init(function(e,t){i.text[e]=t}),r.init(function(e,t){i.binary[e]=t})}),Y=b((e,t)=>{"use strict";I();var r=K();function n(e){this._types=e||r,this.text={},this.binary={}}g(n,"TypeOverrides"),n.prototype.getOverrides=function(e){switch(e){case"text":return this.text;case"binary":return this.binary;default:return{}}},n.prototype.setTypeParser=function(e,t,r){"function"==typeof t&&(r=t,t="text"),this.getOverrides(t)[e]=r},n.prototype.getTypeParser=function(e,t){return t=t||"text",this.getOverrides(t)[e]||this._types.getTypeParser(e,t)},t.exports=n});function Z(e){let t=0x6a09e667,r=0xbb67ae85,n=0x3c6ef372,s=0xa54ff53a,i=0x510e527f,o=0x9b05688c,a=0x1f83d9ab,u=0x5be0cd19,c=0,h=0,l=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],f=g((e,t)=>e>>>t|e<<32-t,"rrot"),p=new Uint32Array(64),d=new Uint8Array(64),y=g(()=>{for(let e=0,t=0;e<16;e++,t+=4)p[e]=d[t]<<24|d[t+1]<<16|d[t+2]<<8|d[t+3];for(let e=16;e<64;e++){let t=f(p[e-15],7)^f(p[e-15],18)^p[e-15]>>>3,r=f(p[e-2],17)^f(p[e-2],19)^p[e-2]>>>10;p[e]=p[e-16]+t+p[e-7]+r|0}let e=t,c=r,y=n,g=s,m=i,b=o,w=a,v=u;for(let t=0;t<64;t++){let r=v+(f(m,6)^f(m,11)^f(m,25))+(m&b^~m&w)+l[t]+p[t]|0,n=(f(e,2)^f(e,13)^f(e,22))+(e&c^e&y^c&y)|0;v=w,w=b,b=m,m=g+r|0,g=y,y=c,c=e,e=r+n|0}t=t+e|0,r=r+c|0,n=n+y|0,s=s+g|0,i=i+m|0,o=o+b|0,a=a+w|0,u=u+v|0,h=0},"process"),m=g(e=>{"string"==typeof e&&(e=new TextEncoder().encode(e));for(let t=0;t<e.length;t++)d[h++]=e[t],64===h&&y();c+=e.length},"add"),b=g(()=>{if(d[h++]=128,64==h&&y(),h+8>64){for(;h<64;)d[h++]=0;y()}for(;h<58;)d[h++]=0;let e=8*c;d[h++]=e/0x10000000000&255,d[h++]=e/0x100000000&255,d[h++]=e>>>24,d[h++]=e>>>16&255,d[h++]=e>>>8&255,d[h++]=255&e,y();let l=new Uint8Array(32);return l[0]=t>>>24,l[1]=t>>>16&255,l[2]=t>>>8&255,l[3]=255&t,l[4]=r>>>24,l[5]=r>>>16&255,l[6]=r>>>8&255,l[7]=255&r,l[8]=n>>>24,l[9]=n>>>16&255,l[10]=n>>>8&255,l[11]=255&n,l[12]=s>>>24,l[13]=s>>>16&255,l[14]=s>>>8&255,l[15]=255&s,l[16]=i>>>24,l[17]=i>>>16&255,l[18]=i>>>8&255,l[19]=255&i,l[20]=o>>>24,l[21]=o>>>16&255,l[22]=o>>>8&255,l[23]=255&o,l[24]=a>>>24,l[25]=a>>>16&255,l[26]=a>>>8&255,l[27]=255&a,l[28]=u>>>24,l[29]=u>>>16&255,l[30]=u>>>8&255,l[31]=255&u,l},"digest");return void 0===e?{add:m,digest:b}:(m(e),b())}var J,X,ee=m(()=>{"use strict";I(),g(Z,"sha256")}),et=m(()=>{"use strict";I(),g(J=class e{constructor(){_(this,"_dataLength",0),_(this,"_bufferLength",0),_(this,"_state",new Int32Array(4)),_(this,"_buffer",new ArrayBuffer(68)),_(this,"_buffer8"),_(this,"_buffer32"),this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashByteArray(e,t=!1){return this.onePassHasher.start().appendByteArray(e).end(t)}static hashStr(e,t=!1){return this.onePassHasher.start().appendStr(e).end(t)}static hashAsciiStr(e,t=!1){return this.onePassHasher.start().appendAsciiStr(e).end(t)}static _hex(t){let r=e.hexChars,n=e.hexOut,s,i,o,a;for(a=0;a<4;a+=1)for(i=8*a,s=t[a],o=0;o<8;o+=2)n[i+1+o]=r.charAt(15&s),s>>>=4,n[i+0+o]=r.charAt(15&s),s>>>=4;return n.join("")}static _md5cycle(e,t){let r=e[0],n=e[1],s=e[2],i=e[3];r+=(n&s|~n&i)+t[0]-0x28955b88|0,i+=((r=(r<<7|r>>>25)+n|0)&n|~r&s)+t[1]-0x173848aa|0,s+=((i=(i<<12|i>>>20)+r|0)&r|~i&n)+t[2]+0x242070db|0,n+=((s=(s<<17|s>>>15)+i|0)&i|~s&r)+t[3]-0x3e423112|0,r+=((n=(n<<22|n>>>10)+s|0)&s|~n&i)+t[4]-0xa83f051|0,i+=((r=(r<<7|r>>>25)+n|0)&n|~r&s)+t[5]+0x4787c62a|0,s+=((i=(i<<12|i>>>20)+r|0)&r|~i&n)+t[6]-0x57cfb9ed|0,n+=((s=(s<<17|s>>>15)+i|0)&i|~s&r)+t[7]-0x2b96aff|0,r+=((n=(n<<22|n>>>10)+s|0)&s|~n&i)+t[8]+0x698098d8|0,i+=((r=(r<<7|r>>>25)+n|0)&n|~r&s)+t[9]-0x74bb0851|0,s+=((i=(i<<12|i>>>20)+r|0)&r|~i&n)+t[10]-42063|0,n+=((s=(s<<17|s>>>15)+i|0)&i|~s&r)+t[11]-0x76a32842|0,r+=((n=(n<<22|n>>>10)+s|0)&s|~n&i)+t[12]+0x6b901122|0,i+=((r=(r<<7|r>>>25)+n|0)&n|~r&s)+t[13]-0x2678e6d|0,s+=((i=(i<<12|i>>>20)+r|0)&r|~i&n)+t[14]-0x5986bc72|0,n+=((s=(s<<17|s>>>15)+i|0)&i|~s&r)+t[15]+0x49b40821|0,r+=((n=(n<<22|n>>>10)+s|0)&i|s&~i)+t[1]-0x9e1da9e|0,i+=((r=(r<<5|r>>>27)+n|0)&s|n&~s)+t[6]-0x3fbf4cc0|0,s+=((i=(i<<9|i>>>23)+r|0)&n|r&~n)+t[11]+0x265e5a51|0,n+=((s=(s<<14|s>>>18)+i|0)&r|i&~r)+t[0]-0x16493856|0,r+=((n=(n<<20|n>>>12)+s|0)&i|s&~i)+t[5]-0x29d0efa3|0,i+=((r=(r<<5|r>>>27)+n|0)&s|n&~s)+t[10]+0x2441453|0,s+=((i=(i<<9|i>>>23)+r|0)&n|r&~n)+t[15]-0x275e197f|0,n+=((s=(s<<14|s>>>18)+i|0)&r|i&~r)+t[4]-0x182c0438|0,r+=((n=(n<<20|n>>>12)+s|0)&i|s&~i)+t[9]+0x21e1cde6|0,i+=((r=(r<<5|r>>>27)+n|0)&s|n&~s)+t[14]-0x3cc8f82a|0,s+=((i=(i<<9|i>>>23)+r|0)&n|r&~n)+t[3]-0xb2af279|0,n+=((s=(s<<14|s>>>18)+i|0)&r|i&~r)+t[8]+0x455a14ed|0,r+=((n=(n<<20|n>>>12)+s|0)&i|s&~i)+t[13]-0x561c16fb|0,i+=((r=(r<<5|r>>>27)+n|0)&s|n&~s)+t[2]-0x3105c08|0,s+=((i=(i<<9|i>>>23)+r|0)&n|r&~n)+t[7]+0x676f02d9|0,n+=((s=(s<<14|s>>>18)+i|0)&r|i&~r)+t[12]-0x72d5b376|0,r+=((n=(n<<20|n>>>12)+s|0)^s^i)+t[5]-378558|0,i+=((r=(r<<4|r>>>28)+n|0)^n^s)+t[8]-0x788e097f|0,s+=((i=(i<<11|i>>>21)+r|0)^r^n)+t[11]+0x6d9d6122|0,n+=((s=(s<<16|s>>>16)+i|0)^i^r)+t[14]-0x21ac7f4|0,r+=((n=(n<<23|n>>>9)+s|0)^s^i)+t[1]-0x5b4115bc|0,i+=((r=(r<<4|r>>>28)+n|0)^n^s)+t[4]+0x4bdecfa9|0,s+=((i=(i<<11|i>>>21)+r|0)^r^n)+t[7]-0x944b4a0|0,n+=((s=(s<<16|s>>>16)+i|0)^i^r)+t[10]-0x41404390|0,r+=((n=(n<<23|n>>>9)+s|0)^s^i)+t[13]+0x289b7ec6|0,i+=((r=(r<<4|r>>>28)+n|0)^n^s)+t[0]-0x155ed806|0,s+=((i=(i<<11|i>>>21)+r|0)^r^n)+t[3]-0x2b10cf7b|0,n+=((s=(s<<16|s>>>16)+i|0)^i^r)+t[6]+0x4881d05|0,r+=((n=(n<<23|n>>>9)+s|0)^s^i)+t[9]-0x262b2fc7|0,i+=((r=(r<<4|r>>>28)+n|0)^n^s)+t[12]-0x1924661b|0,s+=((i=(i<<11|i>>>21)+r|0)^r^n)+t[15]+0x1fa27cf8|0,n+=((s=(s<<16|s>>>16)+i|0)^i^r)+t[2]-0x3b53a99b|0,n=(n<<23|n>>>9)+s|0,r+=(s^(n|~i))+t[0]-0xbd6ddbc|0,r=(r<<6|r>>>26)+n|0,i+=(n^(r|~s))+t[7]+0x432aff97|0,i=(i<<10|i>>>22)+r|0,s+=(r^(i|~n))+t[14]-0x546bdc59|0,s=(s<<15|s>>>17)+i|0,n+=(i^(s|~r))+t[5]-0x36c5fc7|0,n=(n<<21|n>>>11)+s|0,r+=(s^(n|~i))+t[12]+0x655b59c3|0,r=(r<<6|r>>>26)+n|0,i+=(n^(r|~s))+t[3]-0x70f3336e|0,i=(i<<10|i>>>22)+r|0,s+=(r^(i|~n))+t[10]-1051523|0,s=(s<<15|s>>>17)+i|0,n+=(i^(s|~r))+t[1]-0x7a7ba22f|0,n=(n<<21|n>>>11)+s|0,r+=(s^(n|~i))+t[8]+0x6fa87e4f|0,r=(r<<6|r>>>26)+n|0,i+=(n^(r|~s))+t[15]-0x1d31920|0,i=(i<<10|i>>>22)+r|0,s+=(r^(i|~n))+t[6]-0x5cfebcec|0,s=(s<<15|s>>>17)+i|0,n+=(i^(s|~r))+t[13]+0x4e0811a1|0,n=(n<<21|n>>>11)+s|0,r+=(s^(n|~i))+t[4]-0x8ac817e|0,r=(r<<6|r>>>26)+n|0,i+=(n^(r|~s))+t[11]-0x42c50dcb|0,i=(i<<10|i>>>22)+r|0,s+=(r^(i|~n))+t[2]+0x2ad7d2bb|0,s=(s<<15|s>>>17)+i|0,n+=(i^(s|~r))+t[9]-0x14792c6f|0,n=(n<<21|n>>>11)+s|0,e[0]=r+e[0]|0,e[1]=n+e[1]|0,e[2]=s+e[2]|0,e[3]=i+e[3]|0}start(){return this._dataLength=0,this._bufferLength=0,this._state.set(e.stateIdentity),this}appendStr(t){let r=this._buffer8,n=this._buffer32,s=this._bufferLength,i,o;for(o=0;o<t.length;o+=1){if((i=t.charCodeAt(o))<128)r[s++]=i;else if(i<2048)r[s++]=(i>>>6)+192,r[s++]=63&i|128;else if(i<55296||i>56319)r[s++]=(i>>>12)+224,r[s++]=i>>>6&63|128,r[s++]=63&i|128;else{if((i=(i-55296)*1024+(t.charCodeAt(++o)-56320)+65536)>1114111)throw Error("Unicode standard supports code points up to U+10FFFF");r[s++]=(i>>>18)+240,r[s++]=i>>>12&63|128,r[s++]=i>>>6&63|128,r[s++]=63&i|128}s>=64&&(this._dataLength+=64,e._md5cycle(this._state,n),s-=64,n[0]=n[16])}return this._bufferLength=s,this}appendAsciiStr(t){let r=this._buffer8,n=this._buffer32,s=this._bufferLength,i,o=0;for(;;){for(i=Math.min(t.length-o,64-s);i--;)r[s++]=t.charCodeAt(o++);if(s<64)break;this._dataLength+=64,e._md5cycle(this._state,n),s=0}return this._bufferLength=s,this}appendByteArray(t){let r=this._buffer8,n=this._buffer32,s=this._bufferLength,i,o=0;for(;;){for(i=Math.min(t.length-o,64-s);i--;)r[s++]=t[o++];if(s<64)break;this._dataLength+=64,e._md5cycle(this._state,n),s=0}return this._bufferLength=s,this}getState(){let e=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this._dataLength,state:[e[0],e[1],e[2],e[3]]}}setState(e){let t=e.buffer,r=e.state,n=this._state,s;for(this._dataLength=e.length,this._bufferLength=e.buflen,n[0]=r[0],n[1]=r[1],n[2]=r[2],n[3]=r[3],s=0;s<t.length;s+=1)this._buffer8[s]=t.charCodeAt(s)}end(t=!1){let r=this._bufferLength,n=this._buffer8,s=this._buffer32,i=(r>>2)+1;this._dataLength+=r;let o=8*this._dataLength;if(n[r]=128,n[r+1]=n[r+2]=n[r+3]=0,s.set(e.buffer32Identity.subarray(i),i),r>55&&(e._md5cycle(this._state,s),s.set(e.buffer32Identity)),o<=0xffffffff)s[14]=o;else{let e=o.toString(16).match(/(.*?)(.{0,8})$/);if(null===e)return;let t=parseInt(e[2],16),r=parseInt(e[1],16)||0;s[14]=t,s[15]=r}return e._md5cycle(this._state,s),t?this._state:e._hex(this._state)}},"Md5"),_(J,"stateIdentity",new Int32Array([0x67452301,-0x10325477,-0x67452302,0x10325476])),_(J,"buffer32Identity",new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),_(J,"hexChars","0123456789abcdef"),_(J,"hexOut",[]),_(J,"onePassHasher",new J),X=J}),er={};function en(e){return crypto.getRandomValues(a.alloc(e))}function es(e){if("sha256"===e)return{update:g(function(e){return{digest:g(function(){return a.from(Z(e))},"digest")}},"update")};if("md5"===e)return{update:g(function(e){return{digest:g(function(){return"string"==typeof e?X.hashStr(e):X.hashByteArray(e)},"digest")}},"update")};throw Error(`Hash type '${e}' not supported`)}function ei(e,t){if("sha256"!==e)throw Error(`\
Only sha256 is supported (requested: '${e}')`);return{update:g(function(e){return{digest:g(function(){"string"==typeof t&&(t=new TextEncoder().encode(t)),"string"==typeof e&&(e=new TextEncoder().encode(e));let r=t.length;if(r>64)t=Z(t);else if(r<64){let e=new Uint8Array(64);e.set(t),t=e}let n=new Uint8Array(64),s=new Uint8Array(64);for(let e=0;e<64;e++)n[e]=54^t[e],s[e]=92^t[e];let i=new Uint8Array(e.length+64);i.set(n,0),i.set(e,64);let o=new Uint8Array(96);return o.set(s,0),o.set(Z(i),64),a.from(Z(o))},"digest")}},"update")}}w(er,{createHash:()=>es,createHmac:()=>ei,randomBytes:()=>en});var eo=m(()=>{"use strict";I(),ee(),et(),g(en,"randomBytes"),g(es,"createHash"),g(ei,"createHmac")}),ea=b((e,t)=>{"use strict";I(),t.exports={host:"localhost",user:"win32"===u.platform?u.env.USERNAME:u.env.USER,database:void 0,password:null,connectionString:void 0,port:5432,rows:0,binary:!1,max:10,idleTimeoutMillis:3e4,client_encoding:"",ssl:!1,application_name:void 0,fallback_application_name:void 0,options:void 0,parseInputDatesAsUTC:!1,statement_timeout:!1,lock_timeout:!1,idle_in_transaction_session_timeout:!1,query_timeout:!1,connect_timeout:0,keepalives:1,keepalives_idle:0};var r=K(),n=r.getTypeParser(20,"text"),s=r.getTypeParser(1016,"text");t.exports.__defineSetter__("parseInt8",function(e){r.setTypeParser(20,"text",e?r.getTypeParser(23,"text"):n),r.setTypeParser(1016,"text",e?r.getTypeParser(1007,"text"):s)})}),eu=b((e,t)=>{"use strict";I();var r=(eo(),E(er)),n=ea();function s(e){return'"'+e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"'}function i(e){for(var t="{",r=0;r<e.length;r++)r>0&&(t+=","),null===e[r]||typeof e[r]>"u"?t+="NULL":Array.isArray(e[r])?t+=i(e[r]):e[r]instanceof a?t+="\\\\x"+e[r].toString("hex"):t+=s(o(e[r]));return t+"}"}g(s,"escapeElement"),g(i,"arrayString");var o=g(function(e,t){if(null==e)return null;if(e instanceof a)return e;if(ArrayBuffer.isView(e)){var r=a.from(e.buffer,e.byteOffset,e.byteLength);return r.length===e.byteLength?r:r.slice(e.byteOffset,e.byteOffset+e.byteLength)}return e instanceof Date?n.parseInputDatesAsUTC?l(e):h(e):Array.isArray(e)?i(e):"object"==typeof e?u(e,t):e.toString()},"prepareValue");function u(e,t){if(e&&"function"==typeof e.toPostgres){if(-1!==(t=t||[]).indexOf(e))throw Error('circular reference detected while preparing "'+e+'" for query');return t.push(e),o(e.toPostgres(o),t)}return JSON.stringify(e)}function c(e,t){for(e=""+e;e.length<t;)e="0"+e;return e}function h(e){var t=-e.getTimezoneOffset(),r=e.getFullYear(),n=r<1;n&&(r=Math.abs(r)+1);var s=c(r,4)+"-"+c(e.getMonth()+1,2)+"-"+c(e.getDate(),2)+"T"+c(e.getHours(),2)+":"+c(e.getMinutes(),2)+":"+c(e.getSeconds(),2)+"."+c(e.getMilliseconds(),3);return t<0?(s+="-",t*=-1):s+="+",s+=c(Math.floor(t/60),2)+":"+c(t%60,2),n&&(s+=" BC"),s}function l(e){var t=e.getUTCFullYear(),r=t<1;r&&(t=Math.abs(t)+1);var n=c(t,4)+"-"+c(e.getUTCMonth()+1,2)+"-"+c(e.getUTCDate(),2)+"T"+c(e.getUTCHours(),2)+":"+c(e.getUTCMinutes(),2)+":"+c(e.getUTCSeconds(),2)+"."+c(e.getUTCMilliseconds(),3);return n+="+00:00",r&&(n+=" BC"),n}function f(e,t,r){return e="string"==typeof e?{text:e}:e,t&&("function"==typeof t?e.callback=t:e.values=t),r&&(e.callback=r),e}g(u,"prepareObject"),g(c,"pad"),g(h,"dateToString"),g(l,"dateToStringUTC"),g(f,"normalizeQueryConfig");var p=g(function(e){return r.createHash("md5").update(e,"utf-8").digest("hex")},"md5"),d=g(function(e,t,r){var n=p(t+e);return"md5"+p(a.concat([a.from(n),r]))},"postgresMd5PasswordHash");t.exports={prepareValue:g(function(e){return o(e)},"prepareValueWrapper"),normalizeQueryConfig:f,postgresMd5PasswordHash:d,md5:p}}),ec={};w(ec,{default:()=>eh});var eh,el=m(()=>{"use strict";I(),eh={}}),ef=b((e,t)=>{"use strict";I();var r=(eo(),E(er));function n(e){if(-1===e.indexOf("SCRAM-SHA-256"))throw Error("SASL: Only mechanism SCRAM-SHA-256 is currently supported");let t=r.randomBytes(18).toString("base64");return{mechanism:"SCRAM-SHA-256",clientNonce:t,response:"n,,n=*,r="+t,message:"SASLInitialResponse"}}function s(e,t,r){if("SASLInitialResponse"!==e.message)throw Error("SASL: Last message was not SASLInitialResponse");if("string"!=typeof t)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string");if("string"!=typeof r)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: serverData must be a string");let n=h(r);if(n.nonce.startsWith(e.clientNonce)){if(n.nonce.length===e.clientNonce.length)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce");var s=y(t,a.from(n.salt,"base64"),n.iteration),i=d(s,"Client Key"),o=p(i),u="n=*,r="+e.clientNonce,c="r="+n.nonce+",s="+n.salt+",i="+n.iteration,l="c=biws,r="+n.nonce,g=u+","+c+","+l,m=f(i,d(o,g)).toString("base64"),b=d(s,"Server Key"),w=d(b,g);e.message="SASLResponse",e.serverSignature=w.toString("base64"),e.response=l+",p="+m}function i(e,t){if("SASLResponse"!==e.message)throw Error("SASL: Last message was not SASLResponse");if("string"!=typeof t)throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: serverData must be a string");let{serverSignature:r}=l(t);if(r!==e.serverSignature)throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature does not match")}function o(e){if("string"!=typeof e)throw TypeError("SASL: text must be a string");return e.split("").map((t,r)=>e.charCodeAt(r)).every(e=>e>=33&&e<=43||e>=45&&e<=126)}function u(e){return/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(e)}function c(e){if("string"!=typeof e)throw TypeError("SASL: attribute pairs text must be a string");return new Map(e.split(",").map(e=>{if(!/^.=/.test(e))throw Error("SASL: Invalid attribute pair entry");return[e[0],e.substring(2)]}))}function h(e){let t=c(e),r=t.get("r");if(r){if(!o(r))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce must only contain printable characters")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing");let n=t.get("s");if(n){if(!u(n))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt must be base64")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing");let s=t.get("i");if(s){if(!/^[1-9][0-9]*$/.test(s))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: invalid iteration count")}else throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: iteration missing");return{nonce:r,salt:n,iteration:parseInt(s,10)}}function l(e){let t=c(e).get("v");if(t){if(!u(t))throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature must be base64")}else throw Error("SASL: SCRAM-SERVER-FINAL-MESSAGE: server signature is missing");return{serverSignature:t}}function f(e,t){if(!a.isBuffer(e))throw TypeError("first argument must be a Buffer");if(!a.isBuffer(t))throw TypeError("second argument must be a Buffer");if(e.length!==t.length)throw Error("Buffer lengths must match");if(0===e.length)throw Error("Buffers cannot be empty");return a.from(e.map((r,n)=>e[n]^t[n]))}function p(e){return r.createHash("sha256").update(e).digest()}function d(e,t){return r.createHmac("sha256",e).update(t).digest()}function y(e,t,r){for(var n=d(e,a.concat([t,a.from([0,0,0,1])])),s=n,i=0;i<r-1;i++)s=f(s,n=d(e,n));return s}g(n,"startSession"),g(s,"continueSession"),g(i,"finalizeSession"),g(o,"isPrintableChars"),g(u,"isBase64"),g(c,"parseAttributePairs"),g(h,"parseServerFirstMessage"),g(l,"parseServerFinalMessage"),g(f,"xorBuffers"),g(p,"sha256"),g(d,"hmacSha256"),g(y,"Hi"),t.exports={startSession:n,continueSession:s,finalizeSession:i}}),ep={};function ed(...e){return e.join("/")}w(ep,{join:()=>ed});var ey=m(()=>{"use strict";I(),g(ed,"join")}),eg={};function em(e,t){t(Error("No filesystem"))}w(eg,{stat:()=>em});var eb=m(()=>{"use str\
ict";I(),g(em,"stat")}),ew={};w(ew,{default:()=>ev});var ev,eS=m(()=>{"use strict";I(),ev={}}),eE={};w(eE,{StringDecoder:()=>ex});var e_,ex,eA=m(()=>{"use strict";I(),g(e_=class{constructor(e){_(this,"td"),this.td=new TextDecoder(e)}write(e){return this.td.decode(e,{stream:!0})}end(e){return this.td.decode(e)}},"StringDecoder"),ex=e_}),eC=b((e,t)=>{"use strict";I();var{Transform:r}=(eS(),E(ew)),{StringDecoder:n}=(eA(),E(eE)),s=Symbol("last"),i=Symbol("decoder");function o(e,t,r){let n;if(this.overflow){if(1===(n=this[i].write(e).split(this.matcher)).length)return r();n.shift(),this.overflow=!1}else this[s]+=this[i].write(e),n=this[s].split(this.matcher);this[s]=n.pop();for(let e=0;e<n.length;e++)try{u(this,this.mapper(n[e]))}catch(e){return r(e)}if(this.overflow=this[s].length>this.maxLength,this.overflow&&!this.skipOverflow)return void r(Error("maximum buffer reached"));r()}function a(e){if(this[s]+=this[i].end(),this[s])try{u(this,this.mapper(this[s]))}catch(t){return e(t)}e()}function u(e,t){void 0!==t&&e.push(t)}function c(e){return e}function h(e,t,u){switch(e=e||/\r?\n/,t=t||c,u=u||{},arguments.length){case 1:"function"==typeof e?(t=e,e=/\r?\n/):"object"!=typeof e||e instanceof RegExp||e[Symbol.split]||(u=e,e=/\r?\n/);break;case 2:"function"==typeof e?(u=t,t=e,e=/\r?\n/):"object"==typeof t&&(u=t,t=c)}(u=Object.assign({},u)).autoDestroy=!0,u.transform=o,u.flush=a,u.readableObjectMode=!0;let h=new r(u);return h[s]="",h[i]=new n("utf8"),h.matcher=e,h.mapper=t,h.maxLength=u.maxLength,h.skipOverflow=u.skipOverflow||!1,h.overflow=!1,h._destroy=function(e,t){this._writableState.errorEmitted=!1,t(e)},h}g(o,"transform"),g(a,"flush"),g(u,"push"),g(c,"noop"),g(h,"split"),t.exports=h}),eI=b((e,t)=>{"use strict";I();var r=(ey(),E(ep)),n=(eS(),E(ew)).Stream,s=eC(),i=(el(),E(ec)),o="win32"===u.platform,a=u.stderr;function c(e){return(61440&e)==32768}g(c,"isRegFile");var h=["host","port","database","user","password"],l=h.length,f=h[l-1];function p(){if(a instanceof n&&!0===a.writable){var e=Array.prototype.slice.call(arguments).concat(`
`);a.write(i.format.apply(i,e))}}g(p,"warn"),Object.defineProperty(t.exports,"isWin",{get:g(function(){return o},"get"),set:g(function(e){o=e},"set")}),t.exports.warnTo=function(e){var t=a;return a=e,t},t.exports.getFileName=function(e){var t=e||u.env;return t.PGPASSFILE||(o?r.join(t.APPDATA||"./","postgresql","pgpass.conf"):r.join(t.HOME||"./",".pgpass"))},t.exports.usePgPass=function(e,t){return!Object.prototype.hasOwnProperty.call(u.env,"PGPASSWORD")&&(!!o||(t=t||"<unkn>",c(e.mode)?!(63&e.mode)||(p('WARNING: password file "%s" has group or world access; permissions should be u=rw (0600) or less',t),!1):(p('WARNING: password file "%s" is not a plain file',t),!1)))};var d=t.exports.match=function(e,t){return h.slice(0,-1).reduce(function(r,n,s){return 1==s&&Number(e[n]||5432)===Number(t[n])?r&&!0:r&&("*"===t[n]||t[n]===e[n])},!0)};t.exports.getPassword=function(e,t,r){var n,i=t.pipe(s());function o(t){var r=y(t);r&&m(r)&&d(e,r)&&(n=r[f],i.end())}g(o,"onLine");var a=g(function(){t.destroy(),r(n)},"onEnd"),u=g(function(e){t.destroy(),p("WARNING: error on reading file: %s",e),r(void 0)},"onErr");t.on("error",u),i.on("data",o).on("end",a).on("error",u)};var y=t.exports.parseLine=function(e){if(e.length<11||e.match(/^\s+#/))return null;for(var t="",r="",n=0,s=0,i={},o=g(function(t,r,n){var s=e.substring(r,n);Object.hasOwnProperty.call(u.env,"PGPASS_NO_DEESCAPE")||(s=s.replace(/\\([:\\])/g,"$1")),i[h[t]]=s},"addToObj"),a=0;a<e.length-1;a+=1){if(t=e.charAt(a+1),r=e.charAt(a),n==l-1){o(n,s);break}a>=0&&":"==t&&"\\"!==r&&(o(n,s,a+1),s=a+2,n+=1)}return i=Object.keys(i).length===l?i:null},m=t.exports.isValidEntry=function(e){for(var t={0:function(e){return e.length>0},1:function(e){return"*"===e||isFinite(e=Number(e))&&e>0&&e<0x20000000000000&&Math.floor(e)===e},2:function(e){return e.length>0},3:function(e){return e.length>0},4:function(e){return e.length>0}},r=0;r<h.length;r+=1)if(!(0,t[r])(e[h[r]]||""))return!1;return!0}}),eL=b((e,t)=>{"use strict";I(),ey(),E(ep);var r=(eb(),E(eg)),n=eI();t.exports=function(e,t){var s=n.getFileName();r.stat(s,function(i,o){if(i||!n.usePgPass(o,s))return t(void 0);var a=r.createReadStream(s);n.getPassword(e,a,t)})},t.exports.warnTo=n.warnTo}),eP={};w(eP,{default:()=>eT});var eT,eR=m(()=>{"use strict";I(),eT={}}),eM=b((e,t)=>{"use strict";I();var r=(N(),E(D)),n=(eb(),E(eg));function s(e){if("/"===e.charAt(0)){var t=e.split(" ");return{host:t[0],database:t[1]}}var s=r.parse(/ |%[^a-f0-9]|%[a-f0-9][^a-f0-9]/i.test(e)?encodeURI(e).replace(/\%25(\d\d)/g,"%$1"):e,!0),t=s.query;for(var i in t)Array.isArray(t[i])&&(t[i]=t[i][t[i].length-1]);var o=(s.auth||":").split(":");if(t.user=o[0],t.password=o.splice(1).join(":"),t.port=s.port,"socket:"==s.protocol)return t.host=decodeURI(s.pathname),t.database=s.query.db,t.client_encoding=s.query.encoding,t;t.host||(t.host=s.hostname);var a=s.pathname;if(!t.host&&a&&/^%2f/i.test(a)){var u=a.split("/");t.host=decodeURIComponent(u[0]),a=u.splice(1).join("/")}switch(a&&"/"===a.charAt(0)&&(a=a.slice(1)||null),t.database=a&&decodeURI(a),("true"===t.ssl||"1"===t.ssl)&&(t.ssl=!0),"0"===t.ssl&&(t.ssl=!1),(t.sslcert||t.sslkey||t.sslrootcert||t.sslmode)&&(t.ssl={}),t.sslcert&&(t.ssl.cert=n.readFileSync(t.sslcert).toString()),t.sslkey&&(t.ssl.key=n.readFileSync(t.sslkey).toString()),t.sslrootcert&&(t.ssl.ca=n.readFileSync(t.sslrootcert).toString()),t.sslmode){case"disable":t.ssl=!1;break;case"prefer":case"require":case"verify-ca":case"verify-full":break;case"no-verify":t.ssl.rejectUnauthorized=!1}return t}g(s,"parse"),t.exports=s,s.parse=s}),eB=b((e,t)=>{"use strict";I();var r=(eR(),E(eP)),n=ea(),s=eM().parse,i=g(function(e,t,r){return void 0===r?r=u.env["PG"+e.toUpperCase()]:!1===r||(r=u.env[r]),t[e]||r||n[e]},"val"),o=g(function(){switch(u.env.PGSSLMODE){case"disable":return!1;case"prefer":case"require":case"verify-ca":case"verify-full":return!0;case"no-verify":return{rejectUnauthorized:!1}}return n.ssl},"readSSLConfigFromEnvironment"),a=g(function(e){return"'"+(""+e).replace(/\\/g,"\\\\").replace(/'/g,"\\'")+"'"},"quoteParamValue"),c=g(function(e,t,r){var n=t[r];null!=n&&e.push(r+"="+a(n))},"add"),h=class{constructor(e){(e="string"==typeof e?s(e):e||{}).connectionString&&(e=Object.assign({},e,s(e.connectionString))),this.user=i("user",e),this.database=i("database",e),void 0===this.database&&(this.database=this.user),this.port=parseInt(i("port",e),10),this.host=i("host",e),Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:i("password",e)}),this.binary=i("binary",e),this.options=i("options",e),this.ssl=typeof e.ssl>"u"?o():e.ssl,"string"==typeof this.ssl&&"true"===this.ssl&&(this.ssl=!0),"no-verify"===this.ssl&&(this.ssl={rejectUnauthorized:!1}),this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,"key",{enumerable:!1}),this.client_encoding=i("client_encoding",e),this.replication=i("replication",e),this.isDomainSocket=!(this.host||"").indexOf("/"),this.application_name=i("application_name",e,"PGAPPNAME"),this.fallback_application_name=i("fallback_application_name",e,!1),this.statement_timeout=i("statement_timeout",e,!1),this.lock_timeout=i("lock_timeout",e,!1),this.idle_in_transaction_session_timeout=i("idle_in_transaction_session_timeout",e,!1),this.query_timeout=i("query_timeout",e,!1),void 0===e.connectionTimeoutMillis?this.connect_timeout=u.env.PGCONNECT_TIMEOUT||0:this.connect_timeout=Math.floor(e.connectionTimeoutMillis/1e3),!1===e.keepAlive?this.keepalives=0:!0===e.keepAlive&&(this.keepalives=1),"number"==typeof e.keepAliveInitialDelayMillis&&(this.keepalives_idle=Math.floor(e.keepAliveInitialDelayMillis/1e3))}getLibpqConnectionString(e){var t=[];c(t,this,"user"),c(t,this,"password"),c(t,this,"port"),c(t,this,"application_name"),c(t,this,"fallback_application_name"),c(t,this,"connect_timeout"),c(t,this,"options");var n="object"==typeof this.ssl?this.ssl:this.ssl?{sslmode:this.ssl}:{};if(c(t,n,"sslmode"),c(t,n,"sslca"),c(t,n,"sslkey"),c(t,n,"sslcert"),c(t,n,"sslrootcert"),this.database&&t.push("dbname="+a(this.database)),this.replication&&t.push("replication="+a(this.replication)),this.host&&t.push("host="+a(this.host)),this.isDomainSocket)return e(null,t.join(" "));this.client_encoding&&t.push("client_encoding="+a(this.client_encoding)),r.lookup(this.host,function(r,n){return r?e(r,null):(t.push("hostaddr="+a(n)),e(null,t.join(" ")))})}};g(h,"ConnectionParameters"),t.exports=h}),ek=b((e,t)=>{"use strict";I();var r=K(),n=/^([A-Za-z]+)(?: (\d+))?(?: (\d+))?/,s=class{constructor(e,t){this.command=null,this.rowCount=null,this.oid=null,this.rows=[],this.fields=[],this._parsers=void 0,this._types=t,this.RowCtor=null,this.rowAsArray="array"===e,this.rowAsArray&&(this.parseRow=this._parseRowAsArray)}addCommandComplete(e){var t;(t=e.text?n.exec(e.text):n.exec(e.command))&&(this.command=t[1],t[3]?(this.oid=parseInt(t[2],10),this.rowCount=parseInt(t[3],10)):t[2]&&(this.rowCount=parseInt(t[2],10)))}_parseRowAsArray(e){for(var t=Array(e.length),r=0,n=e.length;r<n;r++){var s=e[r];null!==s?t[r]=this._parsers[r](s):t[r]=null}return t}parseRow(e){for(var t={},r=0,n=e.length;r<n;r++){var s=e[r],i=this.fields[r].name;null!==s?t[i]=this._parsers[r](s):t[i]=null}return t}addRow(e){this.rows.push(e)}addFields(e){this.fields=e,this.fields.length&&(this._parsers=Array(e.length));for(var t=0;t<e.length;t++){var n=e[t];this._types?this._parsers[t]=this._types.getTypeParser(n.dataTypeID,n.format||"text"):this._parsers[t]=r.getTypeParser(n.dataTypeID,n.format||"text")}}};g(s,"Result"),t.exports=s}),eO=b((e,t)=>{"use strict";I();var{EventEmitter:r}=L(),n=ek(),s=eu(),i=class extends r{constructor(e,t,r){super(),e=s.normalizeQueryConfig(e,t,r),this.text=e.text,this.values=e.values,this.rows=e.rows,this.types=e.types,this.name=e.name,this.binary=e.binary,this.portal=e.portal||"",this.callback=e.callback,this._rowMode=e.rowMode,u.domain&&e.callback&&(this.callback=u.domain.bind(e.callback)),this._result=new n(this._rowMode,this.types),this._results=this._result,this.isPreparedStatement=!1,this._canceledDueToError=!1,this._promise=null}requiresPreparation(){return!!this.name||!!this.rows||!!this.text&&!!this.values&&this.values.length>0}_checkForMultirow(){this._result.command&&(Array.isArray(this._results)||(this._results=[this._result]),this._result=new n(this._rowMode,this.types),this._results.push(this._result))}handleRowDescription(e){this._checkForMultirow(),this._result.addFields(e.fields),this._accumulateRows=this.callback||!this.listeners("row").length}handleDataRow(e){let t;if(!this._canceledDueToError){try{t=this._result.parseRow(e.fields)}catch(e){this._canceledDueToError=e;return}this.emit("row",t,this._result),this._accumulateRows&&this._result.addRow(t)}}handleCommandComplete(e,t){this._checkForMultirow(),this._result.addCommandComplete(e),this.rows&&t.sync()}handleEmptyQuery(e){this.rows&&e.sync()}handleError(e,t){if(this._canceledDueToError&&(e=this._canceledDueToError,this._canceledDueToError=!1),this.callback)return this.callback(e);this.emit("error",e)}handleReadyForQuery(e){if(this._canceledDueToError)return this.handleError(this._canceledDueToError,e);if(this.callback)try{this.callback(null,this._results)}catch(e){u.nextTick(()=>{throw e})}this.emit("end",this._results)}submit(e){if("string"!=typeof this.text&&"string"!=typeof this.name)return Error("A query must have either text or a name. Supplying neither is unsupported.");let t=e.parsedStatements[this.name];return this.text&&t&&this.text!==t?Error(`Prepared statements must be unique - '${this.name}\
' was used for a different statement`):this.values&&!Array.isArray(this.values)?Error("Query values must be an array"):(this.requiresPreparation()?this.prepare(e):e.query(this.text),null)}hasBeenParsed(e){return this.name&&e.parsedStatements[this.name]}handlePortalSuspended(e){this._getRows(e,this.rows)}_getRows(e,t){e.execute({portal:this.portal,rows:t}),t?e.flush():e.sync()}prepare(e){this.isPreparedStatement=!0,this.hasBeenParsed(e)||e.parse({text:this.text,name:this.name,types:this.types});try{e.bind({portal:this.portal,statement:this.name,values:this.values,binary:this.binary,valueMapper:s.prepareValue})}catch(t){this.handleError(t,e);return}e.describe({type:"P",name:this.portal||""}),this._getRows(e,this.rows)}handleCopyInResponse(e){e.sendCopyFail("No source stream defined")}handleCopyData(e,t){}};g(i,"Query"),t.exports=i}),eD=b(e=>{"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.NoticeMessage=e.DataRowMessage=e.CommandCompleteMessage=e.ReadyForQueryMessage=e.NotificationResponseMessage=e.BackendKeyDataMessage=e.AuthenticationMD5Password=e.ParameterStatusMessage=e.ParameterDescriptionMessage=e.RowDescriptionMessage=e.Field=e.CopyResponse=e.CopyDataMessage=e.DatabaseError=e.copyDone=e.emptyQuery=e.replicationStart=e.portalSuspended=e.noData=e.closeComplete=e.bindComplete=e.parseComplete=void 0,e.parseComplete={name:"parseComplete",length:5},e.bindComplete={name:"bindComplete",length:5},e.closeComplete={name:"closeComplete",length:5},e.noData={name:"noData",length:5},e.portalSuspended={name:"portalSuspended",length:5},e.replicationStart={name:"replicationStart",length:4},e.emptyQuery={name:"emptyQuery",length:4},e.copyDone={name:"copyDone",length:4};var t=class extends Error{constructor(e,t,r){super(e),this.length=t,this.name=r}};g(t,"DatabaseError"),e.DatabaseError=t;var r=class{constructor(e,t){this.length=e,this.chunk=t,this.name="copyData"}};g(r,"CopyDataMessage"),e.CopyDataMessage=r;var n=class{constructor(e,t,r,n){this.length=e,this.name=t,this.binary=r,this.columnTypes=Array(n)}};g(n,"CopyResponse"),e.CopyResponse=n;var s=class{constructor(e,t,r,n,s,i,o){this.name=e,this.tableID=t,this.columnID=r,this.dataTypeID=n,this.dataTypeSize=s,this.dataTypeModifier=i,this.format=o}};g(s,"Field"),e.Field=s;var i=class{constructor(e,t){this.length=e,this.fieldCount=t,this.name="rowDescription",this.fields=Array(this.fieldCount)}};g(i,"RowDescriptionMessage"),e.RowDescriptionMessage=i;var o=class{constructor(e,t){this.length=e,this.parameterCount=t,this.name="parameterDescription",this.dataTypeIDs=Array(this.parameterCount)}};g(o,"ParameterDescriptionMessage"),e.ParameterDescriptionMessage=o;var a=class{constructor(e,t,r){this.length=e,this.parameterName=t,this.parameterValue=r,this.name="parameterStatus"}};g(a,"ParameterStatusMessage"),e.ParameterStatusMessage=a;var u=class{constructor(e,t){this.length=e,this.salt=t,this.name="authenticationMD5Password"}};g(u,"AuthenticationMD5Password"),e.AuthenticationMD5Password=u;var c=class{constructor(e,t,r){this.length=e,this.processID=t,this.secretKey=r,this.name="backendKeyData"}};g(c,"BackendKeyDataMessage"),e.BackendKeyDataMessage=c;var h=class{constructor(e,t,r,n){this.length=e,this.processId=t,this.channel=r,this.payload=n,this.name="notification"}};g(h,"NotificationResponseMessage"),e.NotificationResponseMessage=h;var l=class{constructor(e,t){this.length=e,this.status=t,this.name="readyForQuery"}};g(l,"ReadyForQueryMessage"),e.ReadyForQueryMessage=l;var f=class{constructor(e,t){this.length=e,this.text=t,this.name="commandComplete"}};g(f,"CommandCompleteMessage"),e.CommandCompleteMessage=f;var p=class{constructor(e,t){this.length=e,this.fields=t,this.name="dataRow",this.fieldCount=t.length}};g(p,"DataRowMessage"),e.DataRowMessage=p;var d=class{constructor(e,t){this.length=e,this.message=t,this.name="notice"}};g(d,"NoticeMessage"),e.NoticeMessage=d}),eU=b(e=>{"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.Writer=void 0;var t=class{constructor(e=256){this.size=e,this.offset=5,this.headerPosition=0,this.buffer=a.allocUnsafe(e)}ensure(e){if(this.buffer.length-this.offset<e){let t=this.buffer,r=t.length+(t.length>>1)+e;this.buffer=a.allocUnsafe(r),t.copy(this.buffer)}}addInt32(e){return this.ensure(4),this.buffer[this.offset++]=e>>>24&255,this.buffer[this.offset++]=e>>>16&255,this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addInt16(e){return this.ensure(2),this.buffer[this.offset++]=e>>>8&255,this.buffer[this.offset++]=e>>>0&255,this}addCString(e){if(e){let t=a.byteLength(e);this.ensure(t+1),this.buffer.write(e,this.offset,"utf-8"),this.offset+=t}else this.ensure(1);return this.buffer[this.offset++]=0,this}addString(e=""){let t=a.byteLength(e);return this.ensure(t),this.buffer.write(e,this.offset),this.offset+=t,this}add(e){return this.ensure(e.length),e.copy(this.buffer,this.offset),this.offset+=e.length,this}join(e){if(e){this.buffer[this.headerPosition]=e;let t=this.offset-(this.headerPosition+1);this.buffer.writeInt32BE(t,this.headerPosition+1)}return this.buffer.slice(5*!e,this.offset)}flush(e){let t=this.join(e);return this.offset=5,this.headerPosition=0,this.buffer=a.allocUnsafe(this.size),t}};g(t,"Writer"),e.Writer=t}),eN=b(e=>{"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.serialize=void 0;var t=eU(),r=new t.Writer,n=g(e=>{for(let t of(r.addInt16(3).addInt16(0),Object.keys(e)))r.addCString(t).addCString(e[t]);r.addCString("client_encoding").addCString("UTF8");let n=r.addCString("").flush(),s=n.length+4;return new t.Writer().addInt32(s).add(n).flush()},"startup"),s=g(()=>{let e=a.allocUnsafe(8);return e.writeInt32BE(8,0),e.writeInt32BE(0x4d2162f,4),e},"requestSsl"),i=g(e=>r.addCString(e).flush(112),"password"),o=g(function(e,t){return r.addCString(e).addInt32(a.byteLength(t)).addString(t),r.flush(112)},"sendSASLInitialResponseMessage"),u=g(function(e){return r.addString(e).flush(112)},"sendSCRAMClientFinalMessage"),c=g(e=>r.addCString(e).flush(81),"query"),h=[],l=g(e=>{let t=e.name||"";t.length>63&&(console.error("Warning! Postgres only supports 63 characters for query names."),console.error("You supplied %s (%s)",t,t.length),console.error("This can cause conflicts and silent errors executing queries"));let n=e.types||h,s=n.length,i=r.addCString(t).addCString(e.text).addInt16(s);for(let e=0;e<s;e++)i.addInt32(n[e]);return r.flush(80)},"parse"),f=new t.Writer,p=g(function(e,t){for(let n=0;n<e.length;n++){let s=t?t(e[n],n):e[n];null==s?(r.addInt16(0),f.addInt32(-1)):s instanceof a?(r.addInt16(1),f.addInt32(s.length),f.add(s)):(r.addInt16(0),f.addInt32(a.byteLength(s)),f.addString(s))}},"writeValues"),d=g((e={})=>{let t=e.portal||"",n=e.statement||"",s=e.binary||!1,i=e.values||h,o=i.length;return r.addCString(t).addCString(n),r.addInt16(o),p(i,e.valueMapper),r.addInt16(o),r.add(f.flush()),r.addInt16(+!!s),r.flush(66)},"bind"),y=a.from([69,0,0,0,9,0,0,0,0,0]),m=g(e=>{if(!e||!e.portal&&!e.rows)return y;let t=e.portal||"",r=e.rows||0,n=a.byteLength(t),s=4+n+1+4,i=a.allocUnsafe(1+s);return i[0]=69,i.writeInt32BE(s,1),i.write(t,5,"utf-8"),i[n+5]=0,i.writeUInt32BE(r,i.length-4),i},"execute"),b=g((e,t)=>{let r=a.allocUnsafe(16);return r.writeInt32BE(16,0),r.writeInt16BE(1234,4),r.writeInt16BE(5678,6),r.writeInt32BE(e,8),r.writeInt32BE(t,12),r},"cancel"),w=g((e,t)=>{let r=4+a.byteLength(t)+1,n=a.allocUnsafe(1+r);return n[0]=e,n.writeInt32BE(r,1),n.write(t,5,"utf-8"),n[r]=0,n},"cstringMessage"),v=r.addCString("P").flush(68),S=r.addCString("S").flush(68),E=g(e=>e.name?w(68,`${e.type}${e.name||""}`):"P"===e.type?v:S,"describe"),_=g(e=>w(67,`${e.type}${e.name||""}`),"close"),x=g(e=>r.add(e).flush(100),"copyData"),A=g(e=>w(102,e),"copyFail"),C=g(e=>a.from([e,0,0,0,4]),"codeOnlyBuffer"),L=C(72),P=C(83),T=C(88),R=C(99);e.serialize={startup:n,password:i,requestSsl:s,sendSASLInitialResponseMessage:o,sendSCRAMClientFinalMessage:u,query:c,parse:l,bind:d,execute:m,describe:E,close:_,flush:g(()=>L,"flush"),sync:g(()=>P,"sync"),end:g(()=>T,"end"),copyData:x,copyDone:g(()=>R,"copyDone"),copyFail:A,cancel:b}}),eF=b(e=>{"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.BufferReader=void 0;var t=a.allocUnsafe(0),r=class{constructor(e=0){this.offset=e,this.buffer=t,this.encoding="utf-8"}setBuffer(e,t){this.offset=e,this.buffer=t}int16(){let e=this.buffer.readInt16BE(this.offset);return this.offset+=2,e}byte(){let e=this.buffer[this.offset];return this.offset++,e}int32(){let e=this.buffer.readInt32BE(this.offset);return this.offset+=4,e}uint32(){let e=this.buffer.readUInt32BE(this.offset);return this.offset+=4,e}string(e){let t=this.buffer.toString(this.encoding,this.offset,this.offset+e);return this.offset+=e,t}cstring(){let e=this.offset,t=e;for(;0!==this.buffer[t++];);return this.offset=t,this.buffer.toString(this.encoding,e,t-1)}bytes(e){let t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}};g(r,"BufferReader"),e.BufferReader=r}),eQ=b(e=>{"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.Parser=void 0;var t=eD(),r=eF(),n=a.allocUnsafe(0),s=class{constructor(e){if(this.buffer=n,this.bufferLength=0,this.bufferOffset=0,this.reader=new r.BufferReader,e?.mode==="binary")throw Error("Binary mode not supported yet");this.mode=e?.mode||"text"}parse(e,t){this.mergeBuffer(e);let r=this.bufferOffset+this.bufferLength,s=this.bufferOffset;for(;s+5<=r;){let e=this.buffer[s],n=this.buffer.readUInt32BE(s+1),i=1+n;if(i+s<=r)t(this.handlePacket(s+5,e,n,this.buffer)),s+=i;else break}s===r?(this.buffer=n,this.bufferLength=0,this.bufferOffset=0):(this.bufferLength=r-s,this.bufferOffset=s)}mergeBuffer(e){if(this.bufferLength>0){let t=this.bufferLength+e.byteLength;if(t+this.bufferOffset>this.buffer.byteLength){let e;if(t<=this.buffer.byteLength&&this.bufferOffset>=this.bufferLength)e=this.buffer;else{let r=2*this.buffer.byteLength;for(;t>=r;)r*=2;e=a.allocUnsafe(r)}this.buffer.copy(e,0,this.bufferOffset,this.bufferOffset+this.bufferLength),this.buffer=e,this.bufferOffset=0}e.copy(this.buffer,this.bufferOffset+this.bufferLength),this.bufferLength=t}else this.buffer=e,this.bufferOffset=0,this.bufferLength=e.byteLength}handlePacket(e,r,n,s){switch(r){case 50:return t.bindComplete;case 49:return t.parseComplete;case 51:return t.closeComplete;case 110:return t.noData;case 115:return t.portalSuspended;case 99:return t.copyDone;case 87:return t.replicationStart;case 73:return t.emptyQuery;case 68:return this.parseDataRowMessage(e,n,s);case 67:return this.parseCommandCompleteMessage(e,n,s);case 90:return this.parseReadyForQueryMessage(e,n,s);case 65:return this.parseNotificationMessage(e,n,s);case 82:return this.parseAuthenticationResponse(e,n,s);case 83:return this.parseParameterStatusMessage(e,n,s);case 75:return this.parseBackendKeyData(e,n,s);case 69:return this.parseErrorMessage(e,n,s,"error");case 78:return this.parseErrorMessage(e,n,s,"notice");case 84:return this.parseRowDescriptionMessage(e,n,s);case 116:return this.parseParameterDescriptionMessage(e,n,s);case 71:return this.parseCopyInMessage(e,n,s);case 72:return this.parseCopyOutMessage(e,n,s);case 100:return this.parseCopyData(e,n,s);default:return new t.DatabaseError("received invalid response: "+r.toString(16),n,"error")}}parseReadyForQueryMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.string(1);return new t.ReadyForQueryMessage(r,s)}parseCommandCompleteMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.cstring();return new t.CommandCompleteMessage(r,s)}parseCopyData(e,r,n){let s=n.slice(e,e+(r-4));return new t.CopyDataMessage(r,s)}parseCopyInMessage(e,t,r){return this.parseCopyMessage(e,t,r,"copyInResponse")}parseCopyOutMessage(e,t,r){return this.parseCopyMessage(e,t,r,"copyOutResponse")}parseCopyMessage(e,r,n,s){this.reader.setBuffer(e,n);let i=0!==this.reader.byte(),o=this.reader.int16(),a=new t.CopyResponse(r,s,i,o);for(let e=0;e<o;e++)a.columnTypes[e]=this.reader.int16();return a}parseNotificationMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.int32(),i=this.reader.cstring(),o=this.reader.cstring();return new t.NotificationResponseMessage(r,s,i,o)}parseRowDescriptionMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.int16(),i=new t.RowDescriptionMessage(r,s);for(let e=0;e<s;e++)i.fields[e]=this.parseField();return i}parseField(){let e=this.reader.cstring(),r=this.reader.uint32(),n=this.reader.int16(),s=this.reader.uint32(),i=this.reader.int16(),o=this.reader.int32(),a=0===this.reader.int16()?"text":"binary";return new t.Field(e,r,n,s,i,o,a)}parseParameterDescriptionMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.int16(),i=new t.ParameterDescriptionMessage(r,s);for(let e=0;e<s;e++)i.dataTypeIDs[e]=this.reader.int32();return i}parseDataRowMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.int16(),i=Array(s);for(let e=0;e<s;e++){let t=this.reader.int32();i[e]=-1===t?null:this.reader.string(t)}return new t.DataRowMessage(r,i)}parseParameterStatusMessage(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.cstring(),i=this.reader.cstring();return new t.ParameterStatusMessage(r,s,i)}parseBackendKeyData(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.int32(),i=this.reader.int32();return new t.BackendKeyDataMessage(r,s,i)}parseAuthenticationResponse(e,r,n){this.reader.setBuffer(e,n);let s=this.reader.int32(),i={name:"authenticationOk",length:r};switch(s){case 0:break;case 3:8===i.length&&(i.name="authenticationCleartextPassword");break;case 5:if(12===i.length){i.name="authenticationMD5Password";let e=this.reader.bytes(4);return new t.AuthenticationMD5Password(r,e)}break;case 10:{let e;i.name="authenticationSASL",i.mechanisms=[];do(e=this.reader.cstring())&&i.mechanisms.push(e);while(e)}break;case 11:i.name="authenticationSASLContinue",i.data=this.reader.string(r-8);break;case 12:i.name="authenticationSASLFinal",i.data=this.reader.string(r-8);break;default:throw Error("Unknown authenticationOk message type "+s)}return i}parseErrorMessage(e,r,n,s){this.reader.setBuffer(e,n);let i={},o=this.reader.string(1);for(;"\0"!==o;)i[o]=this.reader.cstring(),o=this.reader.string(1);let a=i.M,u="notice"===s?new t.NoticeMessage(r,a):new t.DatabaseError(a,r,s);return u.severity=i.S,u.code=i.C,u.detail=i.D,u.hint=i.H,u.position=i.P,u.internalPosition=i.p,u.internalQuery=i.q,u.where=i.W,u.schema=i.s,u.table=i.t,u.column=i.c,u.dataType=i.d,u.constraint=i.n,u.file=i.F,u.line=i.L,u.routine=i.R,u}};g(s,"Parser"),e.Parser=s}),eq=b(e=>{"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.DatabaseError=e.serialize=e.parse=void 0;var t=eD();Object.defineProperty(e,"DatabaseError",{enumerable:!0,get:g(function(){return t.DatabaseError},"get")});var r=eN();Object.defineProperty(e,"serialize",{enumerable:!0,get:g(function(){return r.serialize},"get")});var n=eQ();function s(e,t){let r=new n.Parser;return e.on("data",e=>r.parse(e,t)),new Promise(t=>e.on("end",()=>t()))}g(s,"parse"),e.parse=s}),ej={};function eW({socket:e,servername:t}){return e.startTls(t),e}w(ej,{connect:()=>eW});var e$=m(()=>{"use strict";I(),g(eW,"connect")}),eG=b((e,t)=>{"use strict";I();var r=(O(),E(P)),n=L().EventEmitter,{parse:s,serialize:i}=eq(),o=i.flush(),a=i.sync(),u=i.end(),c=class extends n{constructor(e){super(),e=e||{},this.stream=e.stream||new r.Socket,this._keepAlive=e.keepAlive,this._keepAliveInitialDelayMillis=e.keepAliveInitialDelayMillis,this.lastBuffer=!1,this.parsedStatements={},this.ssl=e.ssl||!1,this._ending=!1,this._emitMessage=!1;var t=this;this.on("newListener",function(e){"message"===e&&(t._emitMessage=!0)})}connect(e,t){var n=this;this._connecting=!0,this.stream.setNoDelay(!0),this.stream.connect(e,t),this.stream.once("connect",function(){n._keepAlive&&n.stream.setKeepAlive(!0,n._keepAliveInitialDelayMillis),n.emit("connect")});let s=g(function(e){n._ending&&("ECONNRESET"===e.code||"EPIPE"===e.code)||n.emit("error",e)},"reportStreamError");if(this.stream.on("error",s),this.stream.on("close",function(){n.emit("end")}),!this.ssl)return this.attachListeners(this.stream);this.stream.once("data",function(e){switch(e.toString("utf8")){case"S":break;case"N":return n.stream.end(),n.emit("error",Error("The server does not support SSL connections"));default:return n.stream.end(),n.emit("error",Error("There was an error establishing an SSL connection"))}var i=(e$(),E(ej));let o={socket:n.stream};!0!==n.ssl&&(Object.assign(o,n.ssl),"key"in n.ssl&&(o.key=n.ssl.key)),0===r.isIP(t)&&(o.servername=t);try{n.stream=i.connect(o)}catch(e){return n.emit("error",e)}n.attachListeners(n.stream),n.stream.on("error",s),n.emit("sslconnect")})}attachListeners(e){e.on("end",()=>{this.emit("end")}),s(e,e=>{var t="error"===e.name?"errorMessage":e.name;this._emitMessage&&this.emit("message",e),this.emit(t,e)})}requestSsl(){this.stream.write(i.requestSsl())}startup(e){this.stream.write(i.startup(e))}cancel(e,t){this._send(i.cancel(e,t))}password(e){this._send(i.password(e))}sendSASLInitialResponseMessage(e,t){this._send(i.sendSASLInitialResponseMessage(e,t))}sendSCRAMClientFinalMessage(e){this._send(i.sendSCRAMClientFinalMessage(e))}_send(e){return!!this.stream.writable&&this.stream.write(e)}query(e){this._send(i.query(e))}parse(e){this._send(i.parse(e))}bind(e){this._send(i.bind(e))}execute(e){this._send(i.execute(e))}flush(){this.stream.writable&&this.stream.write(o)}sync(){this._ending=!0,this._send(o),this._send(a)}ref(){this.stream.ref()}unref(){this.stream.unref()}end(){return(this._ending=!0,this._connecting&&this.stream.writable)?this.stream.write(u,()=>{this.stream.end()}):void this.stream.end()}close(e){this._send(i.close(e))}describe(e){this._send(i.describe(e))}sendCopyFromChunk(e){this._send(i.copyData(e))}endCopyFrom(){this._send(i.copyDone())}sendCopyFail(e){this._send(i.copyFail(e))}};g(c,"Connection"),t.exports=c}),eH=b((e,t)=>{"use strict";I();var r=L().EventEmitter,n=(el(),E(ec),eu()),s=ef(),o=eL(),a=Y(),c=eB(),h=eO(),l=ea(),f=eG(),p=class extends r{constructor(e){super(),this.connectionParameters=new c(e),this.user=this.connectionParameters.user,this.database=this.connectionParameters.database,this.port=this.connectionParameters.port,this.host=this.connectionParameters.host,Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:this.connectionParameters.password}),this.replication=this.connectionParameters.replication;var t=e||{};this._Promise=t.Promise||i.Promise,this._types=new a(t.types),this._ending=!1,this._connecting=!1,this._connected=!1,this._connectionError=!1,this._queryable=!0,this.connection=t.connection||new f({stream:t.stream,ssl:this.connectionParameters.ssl,keepAlive:t.keepAlive||!1,keepAliveInitialDelayMillis:t.keepAliveInitialDelayMillis||0,encoding:this.connectionParameters.client_encoding||"utf8"}),this.queryQueue=[],this.binary=t.binary||l.binary,this.processID=null,this.secretKey=null,this.ssl=this.connectionParameters.ssl||!1,this.ssl&&this.ssl.key&&Object.defineProperty(this.ssl,"key",{enumerable:!1}),this._connectionTimeoutMillis=t.connectionTimeoutMillis||0}_errorAllQueries(e){let t=g(t=>{u.nextTick(()=>{t.handleError(e,this.connection)})},"enqueueError");this.activeQuery&&(t(this.activeQuery),this.activeQuery=null),this.queryQueue.forEach(t),this.queryQueue.length=0}_connect(e){var t=this,r=this.connection;if(this._connectionCallback=e,this._connecting||this._connected){let t=Error("Client has already been connected. You cannot reuse a client.");u.nextTick(()=>{e(t)});return}this._connecting=!0,this.connectionTimeoutHandle,this._connectionTimeoutMillis>0&&(this.connectionTimeoutHandle=setTimeout(()=>{r._ending=!0,r.stream.destroy(Error("timeout expired"))},this._connectionTimeoutMillis)),this.host&&0===this.host.indexOf("/")?r.connect(this.host+"/.s.PGSQL."+this.port):r.connect(this.port,this.host),r.on("connect",function(){t.ssl?r.requestSsl():r.startup(t.getStartupConf())}),r.on("sslconnect",function(){r.startup(t.getStartupConf())}),this._attachListeners(r),r.once("end",()=>{let e=this._ending?Error("Connection terminated"):Error("Connection terminated unexpectedly");clearTimeout(this.connectionTimeoutHandle),this._errorAllQueries(e),this._ending||(this._connecting&&!this._connectionError?this._connectionCallback?this._connectionCallback(e):this._handleErrorEvent(e):this._connectionError||this._handleErrorEvent(e)),u.nextTick(()=>{this.emit("end")})})}connect(e){return e?void this._connect(e):new this._Promise((e,t)=>{this._connect(r=>{r?t(r):e()})})}_attachListeners(e){e.on("authenticationCleartextPassword",this._handleAuthCleartextPassword.bind(this)),e.on("authenticationMD5Password",this._handleAuthMD5Password.bind(this)),e.on("authenticationSASL",this._handleAuthSASL.bind(this)),e.on("authenticationSASLContinue",this._handleAuthSASLContinue.bind(this)),e.on("authenticationSASLFinal",this._handleAuthSASLFinal.bind(this)),e.on("backendKeyData",this._handleBackendKeyData.bind(this)),e.on("error",this._handleErrorEvent.bind(this)),e.on("errorMessage",this._handleErrorMessage.bind(this)),e.on("readyForQuery",this._handleReadyForQuery.bind(this)),e.on("notice",this._handleNotice.bind(this)),e.on("rowDescription",this._handleRowDescription.bind(this)),e.on("dataRow",this._handleDataRow.bind(this)),e.on("portalSuspended",this._handlePortalSuspended.bind(this)),e.on("emptyQuery",this._handleEmptyQuery.bind(this)),e.on("commandComplete",this._handleCommandComplete.bind(this)),e.on("parseComplete",this._handleParseComplete.bind(this)),e.on("copyInResponse",this._handleCopyInResponse.bind(this)),e.on("copyData",this._handleCopyData.bind(this)),e.on("notification",this._handleNotification.bind(this))}_checkPgPass(e){let t=this.connection;"function"==typeof this.password?this._Promise.resolve().then(()=>this.password()).then(r=>{if(void 0!==r){if("string"!=typeof r)return void t.emit("error",TypeError("Password must be a string"));this.connectionParameters.password=this.password=r}else this.connectionParameters.password=this.password=null;e()}).catch(e=>{t.emit("error",e)}):null!==this.password?e():o(this.connectionParameters,t=>{void 0!==t&&(this.connectionParameters.password=this.password=t),e()})}_handleAuthCleartextPassword(e){this._checkPgPass(()=>{this.connection.password(this.password)})}_handleAuthMD5Password(e){this._checkPgPass(()=>{let t=n.postgresMd5PasswordHash(this.user,this.password,e.salt);this.connection.password(t)})}_handleAuthSASL(e){this._checkPgPass(()=>{this.saslSession=s.startSession(e.mechanisms),this.connection.sendSASLInitialResponseMessage(this.saslSession.mechanism,this.saslSession.response)})}_handleAuthSASLContinue(e){s.continueSession(this.saslSession,this.password,e.data),this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}_handleAuthSASLFinal(e){s.finalizeSession(this.saslSession,e.data),this.saslSession=null}_handleBackendKeyData(e){this.processID=e.processID,this.secretKey=e.secretKey}_handleReadyForQuery(e){this._connecting&&(this._connecting=!1,this._connected=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback&&(this._connectionCallback(null,this),this._connectionCallback=null),this.emit("connect"));let{activeQuery:t}=this;this.activeQuery=null,this.readyForQuery=!0,t&&t.handleReadyForQuery(this.connection),this._pulseQueryQueue()}_handleErrorWhileConnecting(e){if(!this._connectionError){if(this._connectionError=!0,clearTimeout(this.connectionTimeoutHandle),this._connectionCallback)return this._connectionCallback(e);this.emit("error",e)}}_handleErrorEvent(e){if(this._connecting)return this._handleErrorWhileConnecting(e);this._queryable=!1,this._errorAllQueries(e),this.emit("error",e)}_handleErrorMessage(e){if(this._connecting)return this._handleErrorWhileConnecting(e);let t=this.activeQuery;if(!t)return void this._handleErrorEvent(e);this.activeQuery=null,t.handleError(e,this.connection)}_handleRowDescription(e){this.activeQuery.handleRowDescription(e)}_handleDataRow(e){this.activeQuery.handleDataRow(e)}_handlePortalSuspended(e){this.activeQuery.handlePortalSuspended(this.connection)}_handleEmptyQuery(e){this.activeQuery.handleEmptyQuery(this.connection)}_handleCommandComplete(e){this.activeQuery.handleCommandComplete(e,this.connection)}_handleParseComplete(e){this.activeQuery.name&&(this.connection.parsedStatements[this.activeQuery.name]=this.activeQuery.text)}_handleCopyInResponse(e){this.activeQuery.handleCopyInResponse(this.connection)}_handleCopyData(e){this.activeQuery.handleCopyData(e,this.connection)}_handleNotification(e){this.emit("notification",e)}_handleNotice(e){this.emit("notice",e)}getStartupConf(){var e=this.connectionParameters,t={user:e.user,database:e.database},r=e.application_name||e.fallback_application_name;return r&&(t.application_name=r),e.replication&&(t.replication=""+e.replication),e.statement_timeout&&(t.statement_timeout=String(parseInt(e.statement_timeout,10))),e.lock_timeout&&(t.lock_timeout=String(parseInt(e.lock_timeout,10))),e.idle_in_transaction_session_timeout&&(t.idle_in_transaction_session_timeout=String(parseInt(e.idle_in_transaction_session_timeout,10))),e.options&&(t.options=e.options),t}cancel(e,t){if(e.activeQuery===t){var r=this.connection;this.host&&0===this.host.indexOf("/")?r.connect(this.host+"/.s.PGSQL."+this.port):r.connect(this.port,this.host),r.on("connect",function(){r.cancel(e.processID,e.secretKey)})}else -1!==e.queryQueue.indexOf(t)&&e.queryQueue.splice(e.queryQueue.indexOf(t),1)}setTypeParser(e,t,r){return this._types.setTypeParser(e,t,r)}getTypeParser(e,t){return this._types.getTypeParser(e,t)}escapeIdentifier(e){return'"'+e.replace(/"/g,'""')+'"'}escapeLiteral(e){for(var t=!1,r="'",n=0;n<e.length;n++){var s=e[n];"'"===s?r+=s+s:"\\"===s?(r+=s+s,t=!0):r+=s}return r+="'",!0===t&&(r=" E"+r),r}_pulseQueryQueue(){if(!0===this.readyForQuery)if(this.activeQuery=this.queryQueue.shift(),this.activeQuery){this.readyForQuery=!1,this.hasExecuted=!0;let e=this.activeQuery.submit(this.connection);e&&u.nextTick(()=>{this.activeQuery.handleError(e,this.connection),this.readyForQuery=!0,this._pulseQueryQueue()})}else this.hasExecuted&&(this.activeQuery=null,this.emit("drain"))}query(e,t,r){var n,s,i,o,a;if(null==e)throw TypeError("Client was passed a null or undefined query");return"function"==typeof e.submit?(i=e.query_timeout||this.connectionParameters.query_timeout,s=n=e,"function"==typeof t&&(n.callback=n.callback||t)):(i=this.connectionParameters.query_timeout,(n=new h(e,t,r)).callback||(s=new this._Promise((e,t)=>{n.callback=(r,n)=>r?t(r):e(n)}))),i&&(a=n.callback,o=setTimeout(()=>{var e=Error("Query read timeout");u.nextTick(()=>{n.handleError(e,this.connection)}),a(e),n.callback=()=>{};var t=this.queryQueue.indexOf(n);t>-1&&this.queryQueue.splice(t,1),this._pulseQueryQueue()},i),n.callback=(e,t)=>{clearTimeout(o),a(e,t)}),this.binary&&!n.binary&&(n.binary=!0),n._result&&!n._result._types&&(n._result._types=this._types),this._queryable?this._ending?u.nextTick(()=>{n.handleError(Error("Client was closed and is not queryable"),this.connection)}):(this.queryQueue.push(n),this._pulseQueryQueue()):u.nextTick(()=>{n.handleError(Error("Client has encountered a connection error and is not queryable"),this.connection)}),s}ref(){this.connection.ref()}unref(){this.connection.unref()}end(e){if(this._ending=!0,!this.connection._connecting)if(!e)return this._Promise.resolve();else e();if(this.activeQuery||!this._queryable?this.connection.stream.destroy():this.connection.end(),!e)return new this._Promise(e=>{this.connection.once("end",e)});this.connection.once("end",e)}};g(p,"Client"),p.Query=h,t.exports=p}),eV=b((e,t)=>{"use strict";I();var r=L().EventEmitter,n=g(function(){},"NOOP"),s=g((e,t)=>{let r=e.findIndex(t);return -1===r?void 0:e.splice(r,1)[0]},"removeWhere"),a=class{constructor(e,t,r){this.client=e,this.idleListener=t,this.timeoutId=r}};g(a,"IdleItem");var c=class{constructor(e){this.callback=e}};function h(){throw Error("Release called on client which has already been released to the pool.")}function l(e,t){let r,n;return t?{callback:t,result:void 0}:{callback:g(function(e,t){e?r(e):n(t)},"cb"),result:new e(function(e,t){n=e,r=t}).catch(e=>{throw Error.captureStackTrace(e),e})}}function f(e,t){return g(function r(n){n.client=t,t.removeListener("error",r),t.on("error",()=>{e.log("additional client error after disconnection due to error",n)}),e._remove(t),e.emit("error",n,t)},"idleListener")}g(c,"PendingItem"),g(h,"throwOnDoubleRelease"),g(l,"promisify"),g(f,"makeIdleListener");var p=class extends r{constructor(e,t){super(),this.options=Object.assign({},e),null!=e&&"password"in e&&Object.defineProperty(this.options,"password",{configurable:!0,enumerable:!1,writable:!0,value:e.password}),null!=e&&e.ssl&&e.ssl.key&&Object.defineProperty(this.options.ssl,"key",{enumerable:!1}),this.options.max=this.options.max||this.options.poolSize||10,this.options.min=this.options.min||0,this.options.maxUses=this.options.maxUses||1/0,this.options.allowExitOnIdle=this.options.allowExitOnIdle||!1,this.options.maxLifetimeSeconds=this.options.maxLifetimeSeconds||0,this.log=this.options.log||function(){},this.Client=this.options.Client||t||e1().Client,this.Promise=this.options.Promise||i.Promise,typeof this.options.idleTimeoutMillis>"u"&&(this.options.idleTimeoutMillis=1e4),this._clients=[],this._idle=[],this._expired=new WeakSet,this._pendingQueue=[],this._endCallback=void 0,this.ending=!1,this.ended=!1}_isFull(){return this._clients.length>=this.options.max}_isAboveMin(){return this._clients.length>this.options.min}_pulseQueue(){if(this.log("pulse queue"),this.ended)return void this.log("pulse queue ended");if(this.ending){this.log("pulse queue on ending"),this._idle.length&&this._idle.slice().map(e=>{this._remove(e.client)}),this._clients.length||(this.ended=!0,this._endCallback());return}if(!this._pendingQueue.length)return void this.log("no queued requests");if(!this._idle.length&&this._isFull())return;let e=this._pendingQueue.shift();if(this._idle.length){let t=this._idle.pop();clearTimeout(t.timeoutId);let r=t.client;r.ref&&r.ref();let n=t.idleListener;return this._acquireClient(r,e,n,!1)}if(!this._isFull())return this.newClient(e);throw Error("unexpected condition")}_remove(e){let t=s(this._idle,t=>t.client===e);void 0!==t&&clearTimeout(t.timeoutId),this._clients=this._clients.filter(t=>t!==e),e.end(),this.emit("remove",e)}connect(e){if(this.ending){let t=Error("Cannot use a pool after calling end on the pool");return e?e(t):this.Promise.reject(t)}let t=l(this.Promise,e),r=t.result;if(this._isFull()||this._idle.length){if(this._idle.length&&u.nextTick(()=>this._pulseQueue()),!this.options.connectionTimeoutMillis)return this._pendingQueue.push(new c(t.callback)),r;let e=g((e,r,n)=>{clearTimeout(i),t.callback(e,r,n)},"queueCallback"),n=new c(e),i=setTimeout(()=>{s(this._pendingQueue,t=>t.callback===e),n.timedOut=!0,t.callback(Error("timeout exceeded when trying to connect"))},this.options.connectionTimeoutMillis);return i.unref&&i.unref(),this._pendingQueue.push(n),r}return this.newClient(new c(t.callback)),r}newClient(e){let t=new this.Client(this.options);this._clients.push(t);let r=f(this,t);this.log("checking client timeout");let s,i=!1;this.options.connectionTimeoutMillis&&(s=setTimeout(()=>{this.log("ending client due to timeout"),i=!0,t.connection?t.connection.stream.destroy():t.end()},this.options.connectionTimeoutMillis)),this.log("connecting new client"),t.connect(o=>{if(s&&clearTimeout(s),t.on("error",r),o)this.log("client failed to connect",o),this._clients=this._clients.filter(e=>e!==t),i&&(o=Error("Connection terminated due to connection timeout",{cause:o})),this._pulseQueue(),e.timedOut||e.callback(o,void 0,n);else{if(this.log("new client connected"),0!==this.options.maxLifetimeSeconds){let e=setTimeout(()=>{this.log("ending client due to expired lifetime"),this._expired.add(t),-1!==this._idle.findIndex(e=>e.client===t)&&this._acquireClient(t,new c((e,t,r)=>r()),r,!1)},1e3*this.options.maxLifetimeSeconds);e.unref(),t.once("end",()=>clearTimeout(e))}return this._acquireClient(t,e,r,!0)}})}_acquireClient(e,t,r,s){s&&this.emit("connect",e),this.emit("acquire",e),e.release=this._releaseOnce(e,r),e.removeListener("error",r),t.timedOut?s&&this.options.verify?this.options.verify(e,e.release):e.release():s&&this.options.verify?this.options.verify(e,r=>{if(r)return e.release(r),t.callback(r,void 0,n);t.callback(void 0,e,e.release)}):t.callback(void 0,e,e.release)}_releaseOnce(e,t){let r=!1;return n=>{r&&h(),r=!0,this._release(e,t,n)}}_release(e,t,r){let n;if(e.on("error",t),e._poolUseCount=(e._poolUseCount||0)+1,this.emit("release",r,e),r||this.ending||!e._queryable||e._ending||e._poolUseCount>=this.options.maxUses){e._poolUseCount>=this.options.maxUses&&this.log("remove expended client"),this._remove(e),this._pulseQueue();return}if(this._expired.has(e)){this.log("remove expired client"),this._expired.delete(e),this._remove(e),this._pulseQueue();return}this.options.idleTimeoutMillis&&this._isAboveMin()&&(n=setTimeout(()=>{this.log("remove idle client"),this._remove(e)},this.options.idleTimeoutMillis),this.options.allowExitOnIdle&&n.unref()),this.options.allowExitOnIdle&&e.unref(),this._idle.push(new a(e,t,n)),this._pulseQueue()}query(e,t,r){if("function"==typeof e){let t=l(this.Promise,e);return o(function(){return t.callback(Error("Passing a function as the first parameter to pool.query is not supported"))}),t.result}"function"==typeof t&&(r=t,t=void 0);let n=l(this.Promise,r);return r=n.callback,this.connect((n,s)=>{if(n)return r(n);let i=!1,o=g(e=>{i||(i=!0,s.release(e),r(e))},"onError");s.once("error",o),this.log("dispatching query");try{s.query(e,t,(e,t)=>{if(this.log("query dispatched"),s.removeListener("error",o),!i)return i=!0,s.release(e),e?r(e):r(void 0,t)})}catch(e){return s.release(e),r(e)}}),n.result}end(e){if(this.log("ending"),this.ending){let t=Error("Called end on pool more than once");return e?e(t):this.Promise.reject(t)}this.ending=!0;let t=l(this.Promise,e);return this._endCallback=t.callback,this._pulseQueue(),t.result}get waitingCount(){return this._pendingQueue.length}get idleCount(){return this._idle.length}get expiredCount(){return this._clients.reduce((e,t)=>e+ +!!this._expired.has(t),0)}get totalCount(){return this._clients.length}};g(p,"Pool"),t.exports=p}),ez={};w(ez,{default:()=>eK});var eK,eY=m(()=>{"use strict";I(),eK={}}),eZ=b((e,t)=>{t.exports={name:"pg",version:"8.8.0",description:"PostgreSQL client - pure javascript & libpq with the same API",keywords:["database","libpq","pg","postgre","postgres","postgresql","rdbms"],homepage:"https://github.com/brianc/node-postgres",repository:{type:"git",url:"git://github.com/brianc/node-postgres.git",directory:"packages/pg"},author:"Brian Carlson <<EMAIL>>",main:"./lib",dependencies:{"buffer-writer":"2.0.0","packet-reader":"1.0.0","pg-connection-string":"^2.5.0","pg-pool":"^3.5.2","pg-protocol":"^1.5.0","pg-types":"^2.1.0",pgpass:"1.x"},devDependencies:{async:"2.6.4",bluebird:"3.5.2",co:"4.6.0","pg-copy-streams":"0.3.0"},peerDependencies:{"pg-native":">=3.0.1"},peerDependenciesMeta:{"pg-native":{optional:!0}},scripts:{test:"make test-all"},files:["lib","SPONSORS.md"],license:"MIT",engines:{node:">= 8.0.0"},gitHead:"c99fb2c127ddf8d712500db2c7b9a5491a178655"}}),eJ=b((e,t)=>{"use strict";I();var r=L().EventEmitter,n=(el(),E(ec)),s=eu(),i=t.exports=function(e,t,n){r.call(this),e=s.normalizeQueryConfig(e,t,n),this.text=e.text,this.values=e.values,this.name=e.name,this.callback=e.callback,this.state="new",this._arrayMode="array"===e.rowMode,this._emitRowEvents=!1,this.on("newListener",(function(e){"row"===e&&(this._emitRowEvents=!0)}).bind(this))};n.inherits(i,r);var a={sqlState:"code",statementPosition:"position",messagePrimary:"message",context:"where",schemaName:"schema",tableName:"table",columnName:"column",dataTypeName:"dataType",constraintName:"constraint",sourceFile:"file",sourceLine:"line",sourceFunction:"routine"};i.prototype.handleError=function(e){var t=this.native.pq.resultErrorFields();if(t)for(var r in t)e[a[r]||r]=t[r];this.callback?this.callback(e):this.emit("error",e),this.state="error"},i.prototype.then=function(e,t){return this._getPromise().then(e,t)},i.prototype.catch=function(e){return this._getPromise().catch(e)},i.prototype._getPromise=function(){return this._promise||(this._promise=new Promise((function(e,t){this._once("end",e),this._once("error",t)}).bind(this))),this._promise},i.prototype.submit=function(e){this.state="running";var t=this;this.native=e.native,e.native.arrayMode=this._arrayMode;var r=g(function(r,n,s){if(e.native.arrayMode=!1,o(function(){t.emit("_done")}),r)return t.handleError(r);t._emitRowEvents&&(s.length>1?n.forEach((e,r)=>{e.forEach(e=>{t.emit("row",e,s[r])})}):n.forEach(function(e){t.emit("row",e,s)})),t.state="end",t.emit("end",s),t.callback&&t.callback(null,s)},"after");if(u.domain&&(r=u.domain.bind(r)),this.name){this.name.length>63&&(console.error("Warning! Postgres only supports 63 characters for query names."),console.error("You supplied %s (%s)",this.name,this.name.length),console.error("This can cause conflicts and silent errors executing queries"));var n=(this.values||[]).map(s.prepareValue);if(e.namedQueries[this.name]){if(this.text&&e.namedQueries[this.name]!==this.text){let e=Error(`Prepa\
red statements must be unique - '${this.name}' was used for a different statement`);return r(e)}return e.native.execute(this.name,n,r)}return e.native.prepare(this.name,this.text,n.length,function(s){return s?r(s):(e.namedQueries[t.name]=t.text,t.native.execute(t.name,n,r))})}if(this.values){if(!Array.isArray(this.values)){let e=Error("Query values must be an array");return r(e)}var i=this.values.map(s.prepareValue);e.native.query(this.text,i,r)}else e.native.query(this.text,r)}}),eX=b((e,t)=>{"use strict";I();var r=(eY(),E(ez)),n=Y(),s=(eZ(),L().EventEmitter),o=(el(),E(ec)),a=eB(),c=eJ(),h=t.exports=function(e){s.call(this),e=e||{},this._Promise=e.Promise||i.Promise,this._types=new n(e.types),this.native=new r({types:this._types}),this._queryQueue=[],this._ending=!1,this._connecting=!1,this._connected=!1,this._queryable=!0;var t=this.connectionParameters=new a(e);this.user=t.user,Object.defineProperty(this,"password",{configurable:!0,enumerable:!1,writable:!0,value:t.password}),this.database=t.database,this.host=t.host,this.port=t.port,this.namedQueries={}};h.Query=c,o.inherits(h,s),h.prototype._errorAllQueries=function(e){let t=g(t=>{u.nextTick(()=>{t.native=this.native,t.handleError(e)})},"enqueueError");this._hasActiveQuery()&&(t(this._activeQuery),this._activeQuery=null),this._queryQueue.forEach(t),this._queryQueue.length=0},h.prototype._connect=function(e){var t=this;if(this._connecting)return void u.nextTick(()=>e(Error("Client has already been connected. You cannot reuse a client.")));this._connecting=!0,this.connectionParameters.getLibpqConnectionString(function(r,n){if(r)return e(r);t.native.connect(n,function(r){if(r)return t.native.end(),e(r);t._connected=!0,t.native.on("error",function(e){t._queryable=!1,t._errorAllQueries(e),t.emit("error",e)}),t.native.on("notification",function(e){t.emit("notification",{channel:e.relname,payload:e.extra})}),t.emit("connect"),t._pulseQueryQueue(!0),e()})})},h.prototype.connect=function(e){return e?void this._connect(e):new this._Promise((e,t)=>{this._connect(r=>{r?t(r):e()})})},h.prototype.query=function(e,t,r){var n,s,i,o,a;if(null==e)throw TypeError("Client was passed a null or undefined query");if("function"==typeof e.submit)i=e.query_timeout||this.connectionParameters.query_timeout,s=n=e,"function"==typeof t&&(e.callback=t);else if(i=this.connectionParameters.query_timeout,!(n=new c(e,t,r)).callback){let e,t;s=new this._Promise((r,n)=>{e=r,t=n}),n.callback=(r,n)=>r?t(r):e(n)}return i&&(a=n.callback,o=setTimeout(()=>{var e=Error("Query read timeout");u.nextTick(()=>{n.handleError(e,this.connection)}),a(e),n.callback=()=>{};var t=this._queryQueue.indexOf(n);t>-1&&this._queryQueue.splice(t,1),this._pulseQueryQueue()},i),n.callback=(e,t)=>{clearTimeout(o),a(e,t)}),this._queryable?this._ending?(n.native=this.native,u.nextTick(()=>{n.handleError(Error("Client was closed and is not queryable"))})):(this._queryQueue.push(n),this._pulseQueryQueue()):(n.native=this.native,u.nextTick(()=>{n.handleError(Error("Client has encountered a connection error and is not queryable"))})),s},h.prototype.end=function(e){var t,r=this;return this._ending=!0,this._connected||this.once("connect",this.end.bind(this,e)),e||(t=new this._Promise(function(t,r){e=g(e=>e?r(e):t(),"cb")})),this.native.end(function(){r._errorAllQueries(Error("Connection terminated")),u.nextTick(()=>{r.emit("end"),e&&e()})}),t},h.prototype._hasActiveQuery=function(){return this._activeQuery&&"error"!==this._activeQuery.state&&"end"!==this._activeQuery.state},h.prototype._pulseQueryQueue=function(e){if(this._connected&&!this._hasActiveQuery()){var t=this._queryQueue.shift();if(!t){e||this.emit("drain");return}this._activeQuery=t,t.submit(this);var r=this;t.once("_done",function(){r._pulseQueryQueue()})}},h.prototype.cancel=function(e){this._activeQuery===e?this.native.cancel(function(){}):-1!==this._queryQueue.indexOf(e)&&this._queryQueue.splice(this._queryQueue.indexOf(e),1)},h.prototype.ref=function(){},h.prototype.unref=function(){},h.prototype.setTypeParser=function(e,t,r){return this._types.setTypeParser(e,t,r)},h.prototype.getTypeParser=function(e,t){return this._types.getTypeParser(e,t)}}),e0=b((e,t)=>{"use strict";I(),t.exports=eX()}),e1=b((e,t)=>{"use strict";I();var r=eH(),n=ea(),s=eG(),i=eV(),{DatabaseError:o}=eq(),a=g(e=>{var t;return g(t=class extends i{constructor(t){super(t,e)}},"BoundPool"),t},"poolFactory"),c=g(function(e){this.defaults=n,this.Client=e,this.Query=this.Client.Query,this.Pool=a(this.Client),this._pools=[],this.Connection=s,this.types=K(),this.DatabaseError=o},"PG");"u">typeof u.env.NODE_PG_FORCE_NATIVE?t.exports=new c(e0()):(t.exports=new c(r),Object.defineProperty(t.exports,"native",{configurable:!0,enumerable:!1,get(){var e=null;try{e=new c(e0())}catch(e){if("MODULE_NOT_FOUND"!==e.code)throw e}return Object.defineProperty(t.exports,"native",{value:e}),e}}))});I(),I(),O(),N(),I();var e2=Object.defineProperty,e6=Object.defineProperties,e5=Object.getOwnPropertyDescriptors,e8=Object.getOwnPropertySymbols,e3=Object.prototype.hasOwnProperty,e4=Object.prototype.propertyIsEnumerable,e7=g((e,t,r)=>t in e?e2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,"__defNormalProp"),e9=g((e,t)=>{for(var r in t||(t={}))e3.call(t,r)&&e7(e,r,t[r]);if(e8)for(var r of e8(t))e4.call(t,r)&&e7(e,r,t[r]);return e},"__spreadValues"),te=g((e,t)=>e6(e,e5(t)),"__spreadProps"),tt=2===new Uint8Array(new Uint16Array([258]).buffer)[0],tr=new TextDecoder,tn=new TextEncoder,ts=tn.encode("0123456789abcdef"),ti=tn.encode("0123456789ABCDEF"),to=tn.encode("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/").slice();function ta(e,{alphabet:t,scratchArr:r}={}){if(!n)if(n=new Uint16Array(256),s=new Uint16Array(256),tt)for(let e=0;e<256;e++)n[e]=ts[15&e]<<8|ts[e>>>4],s[e]=ti[15&e]<<8|ti[e>>>4];else for(let e=0;e<256;e++)n[e]=ts[15&e]|ts[e>>>4]<<8,s[e]=ti[15&e]|ti[e>>>4]<<8;e.byteOffset%4!=0&&(e=new Uint8Array(e));let i=e.length,o=i>>>2,a=r||new Uint16Array(i),u=new Uint32Array(e.buffer,e.byteOffset,o),c=new Uint32Array(a.buffer,a.byteOffset,i>>>1),h="upper"===t?s:n,l=0,f=0,p;if(tt)for(;l<o;)p=u[l++],c[f++]=h[p>>>8&255]<<16|h[255&p],c[f++]=h[p>>>24]<<16|h[p>>>16&255];else for(;l<o;)p=u[l++],c[f++]=h[p>>>24]<<16|h[p>>>16&255],c[f++]=h[p>>>8&255]<<16|h[255&p];for(l<<=2;l<i;)a[l]=h[e[l++]];return tr.decode(a.subarray(0,i))}function tu(e,t={}){let r="",n=e.length,s=Math.ceil(n/504e3),i=new Uint16Array(s>1?504e3:n);for(let n=0;n<s;n++){let s=504e3*n,o=s+504e3;r+=ta(e.subarray(s,o),te(e9({},t),{scratchArr:i}))}return r}function tc(e,t={}){return"upper"!==t.alphabet&&"function"==typeof e.toHex?e.toHex():tu(e,t)}to[62]=45,to[63]=95,g(ta,"_toHex"),g(tu,"_toHexChunked"),g(tc,"toHex"),I();var th=class e{constructor(e,t){this.strings=e,this.values=t}toParameterizedQuery(t={query:"",params:[]}){let{strings:r,values:n}=this;for(let s=0,i=r.length;s<i;s++)if(t.query+=r[s],s<n.length){let r=n[s];if(r instanceof tf)t.query+=r.sql;else if(r instanceof t_)if(r.queryData instanceof e)r.queryData.toParameterizedQuery(t);else{if(r.queryData.params?.length)throw Error("This query is not composable");t.query+=r.queryData.query}else{let{params:e}=t;e.push(r),t.query+="$"+e.length,(r instanceof a||ArrayBuffer.isView(r))&&(t.query+="::bytea")}}return t}};g(th,"SqlTemplate");var tl=class{constructor(e){this.sql=e}};g(tl,"UnsafeRawSql");var tf=tl;function tp(){"u">typeof window&&"u">typeof document&&"u">typeof console&&"function"==typeof console.warn&&console.warn(`          
        ************************************************************
        *                                                          *
        *  WARNING: Running SQL directly from the browser can have *
        *  security implications. Even if your database is         *
        *  protected by Row-Level Security (RLS), use it at your   *
        *  own risk. This approach is great for fast prototyping,  *
        *  but ensure proper safeguards are in place to prevent    *
        *  misuse or execution of expensive SQL queries by your    *
        *  end users.                                              *
        *                                                          *
        *  If you've assessed the risks, suppress this message     *
        *  using the disableWarningInBrowsers configuration        *
        *  parameter.                                              *
        *                                                          *
        ************************************************************`)}I(),g(tp,"warnIfBrowser"),O();var td=S(Y()),ty=S(eu()),tg=class e extends Error{constructor(t){super(t),_(this,"name","NeonDbError"),_(this,"severity"),_(this,"code"),_(this,"detail"),_(this,"hint"),_(this,"position"),_(this,"internalPosition"),_(this,"internalQuery"),_(this,"where"),_(this,"schema"),_(this,"table"),_(this,"column"),_(this,"dataType"),_(this,"constraint"),_(this,"file"),_(this,"line"),_(this,"routine"),_(this,"sourceError"),"captureStackTrace"in Error&&"function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,e)}};g(tg,"NeonDbError");var tm="transaction() expects an array of queries, or a function returning an array of queries",tb=["severity","code","detail","hint","position","internalPosition","internalQuery","where","schema","table","column","dataType","constraint","file","line","routine"];function tw(e){return e instanceof a?"\\x"+tc(e):e}function tv(e){let{query:t,params:r}=e instanceof th?e.toParameterizedQuery():e;return{query:t,params:r.map(e=>tw((0,ty.prepareValue)(e)))}}function tS(e,{arrayMode:t,fullResults:r,fetchOptions:n,isolationLevel:s,readOnly:i,deferrable:o,authToken:a,disableWarningInBrowsers:u}={}){let c;if(!e)throw Error("No database connection string was provided to `neon()`. Perhaps an environment variable has not been set?");try{c=U(e)}catch{throw Error("Database connection string provided to `neon()` is not a valid URL. Connection string: "+String(e))}let{protocol:h,username:l,hostname:f,port:p,pathname:d}=c;if("postgres:"!==h&&"postgresql:"!==h||!l||!f||!d)throw Error("Database connection string format for `neon()` should be: postgresql://user:<EMAIL>/dbname?option=value");function y(e,...t){if(!(Array.isArray(e)&&Array.isArray(e.raw)&&Array.isArray(t)))throw Error('This function can now be called only as a tagged-template function: sql`SELECT ${value}`, not sql("SELECT $1", [value], options). For a conventional function call with value placeholders ($1, $2, etc.), use sql.query("SELECT $1", [value], options).');return new t_(m,new th(e,t))}async function m(c,h,l){let d,{fetchEndpoint:y,fetchFunction:g}=k,m=Array.isArray(c)?{queries:c.map(e=>tv(e))}:tv(c),b=n??{},w=t??!1,v=r??!1,S=s,E=i,_=o;void 0!==l&&(void 0!==l.fetchOptions&&(b={...b,...l.fetchOptions}),void 0!==l.arrayMode&&(w=l.arrayMode),void 0!==l.fullResults&&(v=l.fullResults),void 0!==l.isolationLevel&&(S=l.isolationLevel),void 0!==l.readOnly&&(E=l.readOnly),void 0!==l.deferrable&&(_=l.deferrable)),void 0===h||Array.isArray(h)||void 0===h.fetchOptions||(b={...b,...h.fetchOptions});let x=a;Array.isArray(h)||h?.authToken===void 0||(x=h.authToken);let A="function"==typeof y?y(f,p,{jwtAuth:void 0!==x}):y,C={"Neon-Connection-String":e,"Neon-Raw-Text-Output":"true","Neon-Array-Mode":"true"},I=await tA(x);I&&(C.Authorization=`Bearer ${I}`),Array.isArray(c)&&(void 0!==S&&(C["Neon-Batch-Isolation-Level"]=S),void 0!==E&&(C["Neon-Batch-Read-Only"]=String(E)),void 0!==_&&(C["Neon-Batch-Deferrable"]=String(_))),u||k.disableWarningInBrowsers||tp();try{d=await (g??fetch)(A,{method:"POST",body:JSON.stringify(m),headers:C,...b})}catch(t){let e=new tg(`Error connecting to database: ${t}`);throw e.sourceError=t,e}if(d.ok){let e=await d.json();if(Array.isArray(c)){let t=e.results;if(!Array.isArray(t))throw new tg("Neon internal error: unexpected result format");return t.map((e,t)=>{let r=h[t]??{};return tx(e,{arrayMode:r.arrayMode??w,fullResults:r.fullResults??v,types:r.types})})}{let t=h??{};return tx(e,{arrayMode:t.arrayMode??w,fullResults:t.fullResults??v,types:t.types})}}{let{status:e}=d;if(400===e){let e=await d.json(),t=new tg(e.message);for(let r of tb)t[r]=e[r]??void 0;throw t}{let t=await d.text();throw new tg(`Server error (HTTP status ${e}): ${t}`)}}}return g(y,"templateFn"),y.query=(e,t,r)=>new t_(m,{query:e,params:t??[]},r),y.unsafe=e=>new tf(e),y.transaction=async(e,t)=>{if("function"==typeof e&&(e=e(y)),!Array.isArray(e))throw Error(tm);return e.forEach(e=>{if(!(e instanceof t_))throw Error(tm)}),m(e.map(e=>e.queryData),e.map(e=>e.opts??{}),t)},g(m,"execute"),y}g(tw,"encodeBuffersAsBytea"),g(tv,"prepareQuery"),g(tS,"neon");var tE=class{constructor(e,t,r){this.execute=e,this.queryData=t,this.opts=r}then(e,t){return this.execute(this.queryData,this.opts).then(e,t)}catch(e){return this.execute(this.queryData,this.opts).catch(e)}finally(e){return this.execute(this.queryData,this.opts).finally(e)}};g(tE,"NeonQueryPromise");var t_=tE;function tx(e,{arrayMode:t,fullResults:r,types:n}){let s=new td.default(n),i=e.fields.map(e=>e.name),o=e.fields.map(e=>s.getTypeParser(e.dataTypeID)),a=!0===t?e.rows.map(e=>e.map((e,t)=>null===e?null:o[t](e))):e.rows.map(e=>Object.fromEntries(e.map((e,t)=>[i[t],null===e?null:o[t](e)])));return r?(e.viaNeonFetch=!0,e.rowAsArray=t,e.rows=a,e._parsers=o,e._types=s,e):a}async function tA(e){if("string"==typeof e)return e;if("function"==typeof e)try{return await Promise.resolve(e())}catch(t){let e=new tg("Error getting auth token.");throw t instanceof Error&&(e=new tg(`Error getting auth token: ${t.message}`)),e}}g(tx,"processQueryResult"),g(tA,"getAuthToken"),I();var tC=S(e1());I();var tI=S(e1()),tL=class extends tI.Client{constructor(e){super(e),this.config=e}get neonConfig(){return this.connection.stream}connect(e){let{neonConfig:t}=this;t.forceDisablePgSSL&&(this.ssl=this.connection.ssl=!1),this.ssl&&t.useSecureWebSocket&&console.warn("SSL is enabled for both Postgres (e.g. ?sslmode=require in the connection string + forceDisablePgSSL = false) and the WebSocket tunnel (useSecureWebSocket = true). Double encryption will increase latency and CPU usage. It may be appropriate to disable SSL in the Postgres connection parameters or set forceDisablePgSSL = true.");let r="string"!=typeof this.config&&this.config?.host!==void 0||"string"!=typeof this.config&&this.config?.connectionString!==void 0||void 0!==u.env.PGHOST,n=u.env.USER??u.env.USERNAME;if(!r&&"localhost"===this.host&&this.user===n&&this.database===n&&null===this.password)throw Error(`No database host or connection string wa\
s set, and key parameters have default values (host: localhost, user: ${n}, db: ${n}, password: null\
). Is an environment variable missing? Alternatively, if you intended to connect with these paramete\
rs, please set the host to 'localhost' explicitly.`);let s=super.connect(e),i=t.pipelineTLS&&this.ssl,o="password"===t.pipelineConnect;if(!i&&!t.pipelineConnect)return s;let a=this.connection;if(i&&a.on("connect",()=>a.stream.emit("data","S")),o){a.removeAllListeners("authenticationCleartextPassword"),a.removeAllListeners("readyForQuery"),a.once("readyForQuery",()=>a.on("readyForQuery",this._handleReadyForQuery.bind(this)));let e=this.ssl?"sslconnect":"connect";a.on(e,()=>{this.neonConfig.disableWarningInBrowsers||tp(),this._handleAuthCleartextPassword(),this._handleReadyForQuery()})}return s}async _handleAuthSASLContinue(e){if(typeof crypto>"u"||void 0===crypto.subtle||void 0===crypto.subtle.importKey)throw Error("Cannot use SASL auth when `crypto.subtle` is not defined");let t=crypto.subtle,r=this.saslSession,n=this.password,s=e.data;if("SASLInitialResponse"!==r.message||"string"!=typeof n||"string"!=typeof s)throw Error("SASL: protocol error");let i=Object.fromEntries(s.split(",").map(e=>{if(!/^.=/.test(e))throw Error("SASL: Invalid attribute pair entry");return[e[0],e.substring(2)]})),o=i.r,u=i.s,c=i.i;if(!o||!/^[!-+--~]+$/.test(o))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: nonce missing/unprintable");if(!u||!/^(?:[a-zA-Z0-9+/]{4})*(?:[a-zA-Z0-9+/]{2}==|[a-zA-Z0-9+/]{3}=)?$/.test(u))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: salt missing/not base64");if(!c||!/^[1-9][0-9]*$/.test(c))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: missing/invalid iteration count");if(!o.startsWith(r.clientNonce))throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce does not start with client nonce");if(o.length===r.clientNonce.length)throw Error("SASL: SCRAM-SERVER-FIRST-MESSAGE: server nonce is too short");let h=parseInt(c,10),l=a.from(u,"base64"),f=new TextEncoder,p=f.encode(n),d=await t.importKey("raw",p,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),y=new Uint8Array(await t.sign("HMAC",d,a.concat([l,a.from([0,0,0,1])]))),g=y;for(var m=0;m<h-1;m++)y=new Uint8Array(await t.sign("HMAC",d,y)),g=a.from(g.map((e,t)=>g[t]^y[t]));let b=g,w=await t.importKey("raw",b,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),v=new Uint8Array(await t.sign("HMAC",w,f.encode("Client Key"))),S=await t.digest("SHA-256",v),E="n=*,r="+r.clientNonce,_="r="+o+",s="+u+",i="+h,x="c=biws,r="+o,A=E+","+_+","+x,C=await t.importKey("raw",S,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);var I=new Uint8Array(await t.sign("HMAC",C,f.encode(A))),L=a.from(v.map((e,t)=>v[t]^I[t])).toString("base64");let P=await t.importKey("raw",b,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),T=await t.sign("HMAC",P,f.encode("Server Key")),R=await t.importKey("raw",T,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]);var M=a.from(await t.sign("HMAC",R,f.encode(A)));r.message="SASLResponse",r.serverSignature=M.toString("base64"),r.response=x+",p="+L,this.connection.sendSCRAMClientFinalMessage(this.saslSession.response)}};g(tL,"NeonClient"),O();var tP=S(eB());function tT(e,t){let r,n;return t?{callback:t,result:void 0}:{callback:g(function(e,t){e?r(e):n(t)},"cb"),result:new e(function(e,t){n=e,r=t})}}g(tT,"promisify"),g(class extends tC.Pool{constructor(){super(...arguments),_(this,"Client",tL),_(this,"hasFetchUnsupportedListeners",!1),_(this,"addListener",this.on)}on(e,t){return"error"!==e&&(this.hasFetchUnsupportedListeners=!0),super.on(e,t)}query(e,t,r){if(!k.poolQueryViaFetch||this.hasFetchUnsupportedListeners||"function"==typeof e)return super.query(e,t,r);"function"==typeof t&&(r=t,t=void 0);let n=tT(this.Promise,r);r=n.callback;try{let n=new tP.default(this.options),s=encodeURIComponent,i=encodeURI,o=`postgresql://${s(n.user)}:${s(n.password)}@${s(n.host)}\
/${i(n.database)}`,a="string"==typeof e?e:e.text,u=t??e.values??[];tS(o,{fullResults:!0,arrayMode:"array"===e.rowMode}).query(a,u,{types:e.types??this.options?.types}).then(e=>r(void 0,e)).catch(e=>r(e))}catch(e){r(e)}return n.result}},"NeonPool"),O();var tR=S(e1());tR.DatabaseError,tR.defaults,tR.escapeIdentifier,tR.escapeLiteral;var tM=tR.types}};
//# sourceMappingURL=138.js.map