try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="4fb47c25-7339-4334-ace3-5d40a444eb52",e._sentryDebugIdIdentifier="sentry-dbid-4fb47c25-7339-4334-ace3-5d40a444eb52")}catch(e){}"use strict";(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5486:e=>{e.exports=require("bcrypt")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},19984:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>b,serverHooks:()=>k,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>A});var o={};t.r(o),t.d(o,{DELETE:()=>w,GET:()=>f,HEAD:()=>_,OPTIONS:()=>E,PATCH:()=>v,POST:()=>y,PUT:()=>m});var s=t(3690),i=t(56947),n=t(75250),a=t(63033),u=t(62187),p=t(5486),d=t.n(p),l=t(18621),c=t(7688);async function x(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return u.NextResponse.json({error:"Email and password are required"},{status:400});let o=await (0,l.ll)`
      SELECT id, name, email, password, role, institution_id, created_at, updated_at
      FROM users
      WHERE email = ${r}
    `;if(0===o.length)return u.NextResponse.json({error:"Invalid email or password"},{status:401});let s=o[0];if(!await d().compare(t,s.password))return u.NextResponse.json({error:"Invalid email or password"},{status:401});return u.NextResponse.json({message:"Login successful",user:{id:s.id,name:s.name,email:s.email,role:s.role,institutionId:s.institution_id,created_at:s.created_at,updated_at:s.updated_at}},{status:200})}catch(e){return console.error("Login error:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...a},h="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function g(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,o)=>{let s;try{let e=h?.getStore();s=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/auth/login",headers:s}).apply(t,o)}})}let f=g(void 0,"GET"),y=g(x,"POST"),m=g(void 0,"PUT"),v=g(void 0,"PATCH"),w=g(void 0,"DELETE"),_=g(void 0,"HEAD"),E=g(void 0,"OPTIONS"),b=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:T,workUnitAsyncStorage:A,serverHooks:k}=b;function P(){return(0,n.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:A})}},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(19984));module.exports=o})();
//# sourceMappingURL=route.js.map