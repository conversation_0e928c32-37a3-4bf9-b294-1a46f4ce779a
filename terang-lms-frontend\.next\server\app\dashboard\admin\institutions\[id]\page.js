try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8a1132b9-f646-44e7-8be1-13bbf5d3f1dd",e._sentryDebugIdIdentifier="sentry-dbid-8a1132b9-f646-44e7-8be1-13bbf5d3f1dd")}catch(e){}(()=>{var e={};e.id=409,e.ids=[409],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12503:(e,t,s)=>{Promise.resolve().then(s.bind(s,42073))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},35449:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>u,pages:()=>o,routeModule:()=>c,tree:()=>d});var r=s(95500),a=s(56947),n=s(26052),i=s(13636),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["dashboard",{children:["admin",{children:["institutions",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,42073)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,28782)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e),async e=>(await Promise.resolve().then(s.bind(s,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},c=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/institutions/[id]/page",pathname:"/dashboard/admin/institutions/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41399:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(91754),a=s(93491),n=s(21372),i=s(9260),l=s(56682),d=s(59672),o=s(21626),u=s(69122),c=s(88373),p=s(4978),m=s(73562),y=s(16041),x=s.n(y),h=s(15852),f=s(40254);function g(){let e=(0,n.useRouter)(),t=(0,n.useParams)().id,{toast:s}=(0,f.d)(),[y,g]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!0),[j,C]=(0,a.useState)(null),[S,A]=(0,a.useState)(!1),[q,D]=(0,a.useState)({name:"",type:"",subscriptionPlan:"basic",billingCycle:"monthly",studentCount:0,teacherCount:0,paymentStatus:"unpaid",paymentDueDate:""}),N=async r=>{r.preventDefault(),g(!0);try{let r=await fetch(`/api/institutions/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:q.name,type:q.type,subscriptionPlan:q.subscriptionPlan,billingCycle:q.billingCycle,studentCount:q.studentCount,teacherCount:q.teacherCount,paymentStatus:q.paymentStatus,paymentDueDate:q.paymentDueDate?new Date(q.paymentDueDate).toISOString():null})}),a=await r.json();a.success?(s({title:"Success",description:"Institution updated successfully"}),e.push("/dashboard/admin/institutions")):s({title:"Error",description:a.error||"Failed to update institution",variant:"destructive"})}catch(e){console.error("Error updating institution:",e),s({title:"Error",description:"Failed to update institution",variant:"destructive"})}finally{g(!1)}},P=(e,t)=>{D(s=>({...s,[e]:t}))};return b?(0,r.jsxs)("div",{className:"flex min-h-[400px] items-center justify-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("p",{className:"ml-2",children:"Loading institution..."})]}):j?(0,r.jsxs)("div",{className:"space-y-6","data-sentry-component":"EditInstitutionPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(x(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(l.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Edit Institution"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update institution information"})]})]}),(0,r.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Institution Details"}),(0,r.jsxs)(i.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:["Update the information for ",j.name]})]}),(0,r.jsx)(i.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"name","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Name"}),(0,r.jsx)(d.p,{id:"name",value:q.name,onChange:e=>P("name",e.target.value),placeholder:"Enter institution name",required:!0,"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"type","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Institution Type"}),(0,r.jsxs)(u.l6,{value:q.type,onValueChange:e=>P("type",e),required:!0,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(u.yv,{placeholder:"Select institution type","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsx)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:h.g0.map(e=>(0,r.jsx)(u.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"subscriptionPlan","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Subscription Plan"}),(0,r.jsxs)(u.l6,{value:q.subscriptionPlan,onValueChange:e=>P("subscriptionPlan",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(u.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsx)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:Object.entries(h.Ap).map(([e,t])=>(0,r.jsxs)(u.eb,{value:e,children:[t.name," - Rp"," ",t.pricePerStudent.monthly.toLocaleString(),"/student/month"]},e))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"billingCycle","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Billing Cycle"}),(0,r.jsxs)(u.l6,{value:q.billingCycle,onValueChange:e=>P("billingCycle",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(u.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.eb,{value:"monthly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Monthly"}),(0,r.jsx)(u.eb,{value:"yearly","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Yearly (25% discount)"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"studentCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Students"}),(0,r.jsx)(d.p,{id:"studentCount",type:"number",value:q.studentCount,onChange:e=>P("studentCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"teacherCount","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Number of Teachers"}),(0,r.jsx)(d.p,{id:"teacherCount",type:"number",value:q.teacherCount,onChange:e=>P("teacherCount",parseInt(e.target.value)||0),placeholder:"0",min:"0","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"paymentStatus","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,r.jsxs)(u.l6,{value:q.paymentStatus,onValueChange:e=>P("paymentStatus",e),"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(u.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,r.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.eb,{value:"paid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Paid"}),(0,r.jsx)(u.eb,{value:"unpaid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Unpaid"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"paymentDueDate","data-sentry-element":"Label","data-sentry-source-file":"page.tsx",children:"Payment Due Date"}),(0,r.jsx)(d.p,{id:"paymentDueDate",type:"date",value:q.paymentDueDate,onChange:e=>P("paymentDueDate",e.target.value),"data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(x(),{href:"/dashboard/admin/institutions","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,r.jsx)(l.$,{variant:"outline",type:"button","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Cancel"})}),(0,r.jsxs)(l.$,{type:"submit",disabled:y,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Save","data-sentry-source-file":"page.tsx"}),y?"Updating...":"Update Institution"]})]})]})})]})]}):(0,r.jsxs)("div",{className:"py-8 text-center",children:[(0,r.jsx)("p",{children:"Institution not found"}),(0,r.jsx)(x(),{href:"/dashboard/admin/institutions",children:(0,r.jsx)(l.$,{className:"mt-4",children:"Back to Institutions"})})]})}},41692:e=>{"use strict";e.exports=require("node:tls")},42073:(e,t,s)=>{"use strict";let r;s.r(t),s.d(t,{default:()=>m,generateImageMetadata:()=>c,generateMetadata:()=>u,generateViewport:()=>p});var a=s(63033),n=s(1472),i=s(7688),l=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\[id]\\page.tsx","default");let d={...a},o="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;r="function"==typeof l?new Proxy(l,{apply:(e,t,s)=>{let r,a,n;try{let e=o?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin/institutions/[id]",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(t,s)}}):l;let u=void 0,c=void 0,p=void 0,m=r},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65647:(e,t,s)=>{Promise.resolve().then(s.bind(s,41399))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8134,8634,99],()=>s(35449));module.exports=r})();
//# sourceMappingURL=page.js.map