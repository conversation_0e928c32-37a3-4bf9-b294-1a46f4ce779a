{"version": 3, "file": "../app/api/class-enrollments/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,+WCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,IAAE,CAAE,CAAG,MAAMD,EACfE,EAAeC,EADAH,MACAG,CAASF,EAAAA,CAAAA,GAE1BG,MAAMF,GACR,OAAOG,EADCH,CAAAA,EAAe,SAChBG,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,wBACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,IAAMC,EAAa,MAAMC,EAAnBD,EAAmBC,CACtBC,MAAM,CAAC,CACNX,EAAAA,CAAIY,EAAAA,gBAAgBA,CAACZ,EAAE,CACvBa,SAAAA,CAAWD,EAAAA,gBAAgBA,CAACC,SAAS,CACrCC,OAAAA,CAASF,EAAAA,gBAAgBA,CAACE,OAAO,CACjCC,UAAAA,CAAYH,EAAAA,gBAAgBA,CAACG,UAAU,CACvCP,MAAAA,CAAQI,EAAAA,gBAAgBA,CAACJ,MAAM,CAC/BQ,SAAAA,CAAWJ,EAAAA,gBAAgBA,CAACI,SAAS,CACrCC,SAAAA,CAAWL,EAAAA,gBAAgBA,CAACK,SAAS,CACrCC,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBC,YAAAA,CAAcF,EAAAA,KAAKA,CAACG,KAAK,CACzBC,SAAAA,CAAWC,EAAAA,OAAOA,CAACJ,IAAAA,GAEpBK,IAAI,CAACb,EAAAA,gBAAAA,CAAAA,CACLc,QAAQ,CAACP,EAAAA,KAAAA,CAAOQ,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGf,EAAAA,gBAAAA,CAAiBC,SAAS,CAAEM,EAAAA,KAAAA,CAAMnB,EAAE,GACvD0B,QAAQ,CAACF,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGf,EAAAA,gBAAAA,CAAiBE,OAAO,CAAEU,EAAAA,OAAAA,CAAQxB,EAAE,GACzD4B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGf,EAAAA,gBAAAA,CAAiBZ,EAAE,CAAEC,IAC9B4B,KAAK,CAAC,EADwB5B,CAGjC,CAHiCA,EAGP,GAAG,GAAd6B,IAAXrB,EAAiB,CACnB,OAAOL,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,6BACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTyB,IAAAA,CAAMtB,CAAU,CAAC,IAErB,CAAE,MAAOF,EAAO,CAEd,EAFOA,KACPyB,OAAAA,CAAQzB,KAAK,CAAC,mCAAoCA,GAC3CH,EAD2CG,CAAAA,WAC3CH,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,mCACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeyB,EACpBnC,CAAoB,CACpB,CAFoBmC,OAElBlC,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,IAAE,CAAE,CAAG,MAAMD,EACfE,EAAeC,EADAH,MACAG,CAASF,EAAAA,CAAAA,QAEtBQ,CAAM,CAAE,CADH,EACM0B,IAAAA,EADQ7B,IAAI,CAAZP,EAGnB,GAAIK,MAAMF,GACR,OAAOG,EADCH,CAAAA,EAAe,SAChBG,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,wBACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,GAAI,CAACA,GAAU,CAAC,CAAC,SAAU,WAAW,CAAC2B,QAAQ,CAAC3B,GAC9C,GAD8CA,CAAAA,EAAS,CAChDJ,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,gDACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAM4B,EAAqB,MAAM1B,EAAAA,EAAAA,CAC9BC,MAAM,GACNc,IAAI,CAACb,EAAAA,gBAAAA,CAAAA,CACLgB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACf,EAAAA,gBAAAA,CAAiBZ,EAAE,CAAEC,IAC9B4B,KAAK,CAAC,EADwB5B,CAAAA,CAAAA,EAGC,GAAG,CAAjCmC,EAAmBN,MAAM,CAC3B,OAAO1B,EADLgC,YACKhC,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,6BACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAM6B,EAAoB,MAAM3B,EAAAA,EAAAA,CAC7B4B,MAAM,CAAC1B,EAAAA,gBAAAA,CAAAA,CACP2B,GAAG,CAAC,QACH/B,EACAS,IADAT,KACAS,CAAW,IAAIuB,IAAAA,GAEhBZ,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGf,EAAAA,gBAAgBA,CAACZ,EAAE,CAAEC,IAC9BwC,SAAS,GAEZ,OAAOrC,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTyB,IAAAA,CAAMM,CAAiB,CAAC,EAAE,CAC1BK,OAAAA,CAAS,uCACX,EACF,CAAE,MAAOnC,EAAO,CAEd,EAFOA,KACPyB,OAAAA,CAAQzB,KAAK,CAAC,mCAAoCA,GAC3CH,EAD2CG,CAAAA,WAC3CH,CAAaC,IAAI,CACtB,CACEC,OAAAA,CAAS,GACTC,KAAAA,CAAO,oCACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAemC,EACpB7C,CAAoB,CACpB,CAAEC,GAFkB4C,KAEZ,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAE3C,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAeC,EADAH,MACAG,CAASF,EAAAA,CAAAA,GAE1BG,MAAMF,GACR,OAAOG,EAAAA,CADCH,EAAe,SAChBG,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,wBACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAM4B,EAAqB,MAAM1B,EAAAA,EAAAA,CAC9BC,MAAM,GACNc,IAAI,CAACb,EAAAA,gBAAAA,CAAAA,CACLgB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACf,EAAAA,gBAAAA,CAAiBZ,EAAE,CAAEC,IAC9B4B,KAAK,CAAC,EADwB5B,CAAAA,CAAAA,EAG7BmC,GAAiC,GAAdN,MAAM,CAC3B,KADEM,EACKhC,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,6BACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,GASlB,OAJA,MAAME,EAAAA,EAAAA,CACHkC,MAAM,CAAChC,EAAAA,gBAAAA,CAAAA,CACPgB,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGf,EAAAA,gBAAAA,CAAiBZ,EAAE,CAAEC,IAE1BG,EAAAA,MAF0BH,CAAAA,CAAAA,IAE1BG,CAAaC,IAAI,CAAC,CACvBC,OAAAA,CAAS,GACToC,OAAAA,CAAS,uCACX,EACF,CAAE,MAAOnC,EAAO,CAEd,EAFOA,KACPyB,OAAAA,CAAQzB,KAAK,CAAC,mCAAoCA,GAC3CH,EAD2CG,CAAAA,WAC3CH,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,oCACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC9LA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KACT,CAAE,KAAM,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,6BAA6B,SACjD,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAG5B,CAGK,IAAC,EAAM,CAAH,CAAeqC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAAf,MAA2C,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,yCACA,uCACA,iBACA,iDACA,CAAK,CACL,4JACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/src/app/api/class-enrollments/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?7072", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { classEnrollments, users, classes } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET - Get specific class enrollment by ID\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const enrollmentId = parseInt(id);\r\n\r\n    if (isNaN(enrollmentId)) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Invalid enrollment ID'\r\n        },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const enrollment = await db\r\n      .select({\r\n        id: classEnrollments.id,\r\n        studentId: classEnrollments.studentId,\r\n        classId: classEnrollments.classId,\r\n        enrolledAt: classEnrollments.enrolledAt,\r\n        status: classEnrollments.status,\r\n        createdAt: classEnrollments.createdAt,\r\n        updatedAt: classEnrollments.updatedAt,\r\n        studentName: users.name,\r\n        studentEmail: users.email,\r\n        className: classes.name\r\n      })\r\n      .from(classEnrollments)\r\n      .leftJoin(users, eq(classEnrollments.studentId, users.id))\r\n      .leftJoin(classes, eq(classEnrollments.classId, classes.id))\r\n      .where(eq(classEnrollments.id, enrollmentId))\r\n      .limit(1);\r\n\r\n    if (enrollment.length === 0) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Class enrollment not found'\r\n        },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: enrollment[0]\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching class enrollment:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to fetch class enrollment'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT - Update class enrollment status\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const enrollmentId = parseInt(id);\r\n    const body = await request.json();\r\n    const { status } = body;\r\n\r\n    if (isNaN(enrollmentId)) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Invalid enrollment ID'\r\n        },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    if (!status || !['active', 'inactive'].includes(status)) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Valid status is required (active or inactive)'\r\n        },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if enrollment exists\r\n    const existingEnrollment = await db\r\n      .select()\r\n      .from(classEnrollments)\r\n      .where(eq(classEnrollments.id, enrollmentId))\r\n      .limit(1);\r\n\r\n    if (existingEnrollment.length === 0) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Class enrollment not found'\r\n        },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Update enrollment\r\n    const updatedEnrollment = await db\r\n      .update(classEnrollments)\r\n      .set({\r\n        status,\r\n        updatedAt: new Date()\r\n      })\r\n      .where(eq(classEnrollments.id, enrollmentId))\r\n      .returning();\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: updatedEnrollment[0],\r\n      message: 'Class enrollment updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating class enrollment:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to update class enrollment'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE - Remove class enrollment\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const enrollmentId = parseInt(id);\r\n\r\n    if (isNaN(enrollmentId)) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Invalid enrollment ID'\r\n        },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if enrollment exists\r\n    const existingEnrollment = await db\r\n      .select()\r\n      .from(classEnrollments)\r\n      .where(eq(classEnrollments.id, enrollmentId))\r\n      .limit(1);\r\n\r\n    if (existingEnrollment.length === 0) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Class enrollment not found'\r\n        },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Delete enrollment\r\n    await db\r\n      .delete(classEnrollments)\r\n      .where(eq(classEnrollments.id, enrollmentId));\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Class enrollment deleted successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error deleting class enrollment:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to delete class enrollment'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/class-enrollments/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\class-enrollments\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/class-enrollments/[id]/route\",\n        pathname: \"/api/class-enrollments/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/class-enrollments/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\class-enrollments\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "enrollmentId", "parseInt", "isNaN", "NextResponse", "json", "success", "error", "status", "enrollment", "db", "select", "classEnrollments", "studentId", "classId", "enrolledAt", "createdAt", "updatedAt", "studentName", "users", "name", "studentEmail", "email", "className", "classes", "from", "leftJoin", "eq", "where", "limit", "length", "data", "console", "PUT", "body", "includes", "existingEnrollment", "updatedEnrollment", "update", "set", "Date", "returning", "message", "DELETE", "delete", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}