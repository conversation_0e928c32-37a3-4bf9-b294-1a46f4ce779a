try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="7ca2833c-7f7b-41ca-a31f-43bdcc9eb4c3",e._sentryDebugIdIdentifier="sentry-dbid-7ca2833c-7f7b-41ca-a31f-43bdcc9eb4c3")}catch(e){}(()=>{var e={};e.id=6100,e.ids=[6100],e.modules={1683:(e,r,s)=>{"use strict";s.d(r,{P:()=>o});var t=s(138);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let i=(0,t.lw)(process.env.DATABASE_URL);async function o(e,...r){return await i(e,...r)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=44725,e.exports=r},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},49776:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>O,routeModule:()=>g,serverHooks:()=>S,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>T});var t={};s.r(t),s.d(t,{DELETE:()=>v,GET:()=>h,HEAD:()=>A,OPTIONS:()=>N,PATCH:()=>w,POST:()=>m,PUT:()=>R});var i=s(3690),o=s(56947),u=s(75250),n=s(63033),a=s(62187),c=s(82446),d=s(1683),p=s(7688);async function l(e,{params:r}){try{let{id:e}=await r,s=parseInt(e);if(isNaN(s))return a.NextResponse.json({success:!1,error:"Invalid user ID"},{status:400});let t=await (0,d.P)`
      SELECT
        u.id,
        u.name,
        u.email,
        u.role,
        u.institution_id,
        u.created_at,
        u.updated_at,
        i.name as institution_name,
        i.type as institution_type
      FROM users u
      LEFT JOIN institutions i ON u.institution_id = i.id
      WHERE u.id = ${s}
    `;if(0===t.length)return a.NextResponse.json({success:!1,error:"User not found"},{status:404});return a.NextResponse.json({success:!0,data:t[0],message:"User retrieved successfully"})}catch(e){return console.error("Get user error:",e),a.NextResponse.json({success:!1,error:"Failed to retrieve user"},{status:500})}}async function x(e,{params:r}){try{let{id:s}=await r,t=parseInt(s),i=await e.json();if(isNaN(t))return a.NextResponse.json({success:!1,error:"Invalid user ID"},{status:400});let{name:o,email:u,password:n,role:p,institutionId:l}=i,x=await (0,d.P)`
      SELECT id, email FROM users WHERE id = ${t}
    `;if(0===x.length)return a.NextResponse.json({success:!1,error:"User not found"},{status:404});if(u&&u!==x[0].email&&(await (0,d.P)`
        SELECT id FROM users WHERE email = ${u} AND id != ${t}
      `).length>0)return a.NextResponse.json({success:!1,error:"Email already exists"},{status:400});let E=null;n&&(E=await c.Ay.hash(n,10));let f=await (0,d.P)`
      UPDATE users SET
        name = COALESCE(${o}, name),
        email = COALESCE(${u}, email),
        password = COALESCE(${E}, password),
        role = COALESCE(${p}, role),
        institution_id = ${void 0!==l?l:null},
        updated_at = NOW()
      WHERE id = ${t}
      RETURNING id, name, email, role, institution_id, created_at, updated_at
    `;return a.NextResponse.json({success:!0,data:f[0],message:"User updated successfully"})}catch(e){return console.error("Update user error:",e),a.NextResponse.json({success:!1,error:"Failed to update user"},{status:500})}}async function E(e,{params:r}){try{let{id:e}=await r,s=parseInt(e);if(isNaN(s))return a.NextResponse.json({success:!1,error:"Invalid user ID"},{status:400});let t=await (0,d.P)`
      SELECT id, role FROM users WHERE id = ${s}
    `;if(0===t.length)return a.NextResponse.json({success:!1,error:"User not found"},{status:404});if("super_admin"===t[0].role)return a.NextResponse.json({success:!1,error:"Cannot delete super admin users"},{status:403});return await (0,d.P)`
      DELETE FROM users WHERE id = ${s}
    `,a.NextResponse.json({success:!0,message:"User deleted successfully"})}catch(e){return console.error("Delete user error:",e),a.NextResponse.json({success:!1,error:"Failed to delete user"},{status:500})}}let f={...n},y="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let i;try{let e=y?.getStore();i=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/users/[id]",headers:i}).apply(s,t)}})}let h=q(l,"GET"),m=q(void 0,"POST"),R=q(x,"PUT"),w=q(void 0,"PATCH"),v=q(E,"DELETE"),A=q(void 0,"HEAD"),N=q(void 0,"OPTIONS"),g=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/users/[id]/route",pathname:"/api/users/[id]",filename:"route",bundlePath:"app/api/users/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\users\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:_,workUnitAsyncStorage:T,serverHooks:S}=g;function O(){return(0,u.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:T})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,2446],()=>s(49776));module.exports=t})();
//# sourceMappingURL=route.js.map