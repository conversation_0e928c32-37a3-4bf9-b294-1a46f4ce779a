{"version": 3, "file": "../app/dashboard/student/courses/[id]/page.js", "mappings": "ueAIe,SAASA,EAAc,UACpCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,oDCKI,sBAAsB,4tBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,EAd+C,CAc3C,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,iCAAiC,CACjD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,CAEjB,OAAO,EACf,CAAO,CAFc,CAEZ,KAAK,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,GAKC,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,EChF9B,qLCKA,IAAMC,EAAWC,EAAAA,UAAgB,CAAiH,CAAC,WACjJC,CAAS,OACTC,CAAK,CACL,GAAGC,EACJ,CAAEC,IAAQ,UAACC,EAAAA,EAAsB,EAACD,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEL,GAAa,GAAGE,CAAK,UAC7I,UAACE,EAAAA,EAA2B,EAACJ,UAAU,iDAAiDM,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,IAAON,EAAAA,GAAS,EAAG,EAAE,CAAC,OAGpDH,EAASU,WAAW,CAAGJ,EAAAA,EAAsB,CAACI,WAAW,wBCdzD,+JCEA,SAASC,EAAK,WACZT,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,OAAOX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBd,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,WACjBf,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,aAAaX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBhB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,mBAAmBX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBjB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,eAAeX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBlB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,8FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,gDCc3M,MAAc,cAAiB,eAjBD,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,GAAK,IAAI,IAAM,KAAK,SAAU,EACjE,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAS,UAAI,IAAM,IAAI,IAAM,KAAK,SAAU,EACvE,mBCPA,sCAAkL,yBCAlL,mECAA,0GCAA,4ECgBM,MAAO,cAAiB,QAbM,CAAC,CAAC,SAAW,EAAE,OAAQ,oBAAsB,KAAK,CAAS,QAAC,CAAC,2BCHjG,qOCiDe,SAASM,EAAwB,QAC9CC,CAAM,CAKP,EAEC,GAAM,IACJC,CAAE,CACH,CAAGC,CAAAA,EAAAA,EAAAA,GAAAA,CAAGA,CAACF,GAGFG,EAAiB,CACrBF,GAAIG,SAASH,GACbI,KAAM,0BACNC,YAAa,+CACbC,KAAM,aACNC,WAAY,UACZC,WAAY,YACZC,SAAU,GACVC,QAAS,CAAC,CACRV,GAAI,EACJI,KAAM,gCACNC,YAAa,6CACbM,YAAY,EACZF,SAAU,IACVG,SAAU,CAAC,CACTZ,GAAI,EACJI,KAAM,sBACNC,YAAa,qCACbQ,aAAa,EACbF,YAAY,EACZG,SAAS,EACTC,OAAQ,EACRC,eAAe,EACfC,UAAW,EACb,EAAG,CACDjB,GAAI,EACJI,KAAM,4BACNC,YAAa,mCACbQ,aAAa,EACbF,YAAY,EACZG,SAAS,EACTC,OAAQ,EACRC,eAAe,EACfC,UAAW,EACb,EAAG,CACDjB,GAAI,EACJI,KAAM,uCACNC,YAAa,yCACbQ,YAAa,GACbF,YAAY,EACZG,SAAS,EACTC,OAAQ,EACRC,eAAe,EACfC,UAAW,EACb,EAAE,CACFC,eAAe,EACfC,aAAc,EACdC,oBAAqB,GACrBC,gBAAiB,EACnB,EAAG,CACDrB,GAAI,EACJI,KAAM,4BACNC,YAAa,yCACbM,YAAY,EACZF,SAAU,GACVG,SAAU,CAAC,CACTZ,GAAI,EACJI,KAAM,sCACNC,YAAa,6BACbQ,YAAa,GACbF,YAAY,EACZG,SAAS,EACTC,OAAQ,EACRC,eAAe,EACfC,UAAW,EACb,EAAG,CACDjB,GAAI,EACJI,KAAM,yCACNC,YAAa,gCACbQ,aAAa,EACbF,YAAY,EACZG,SAAS,EACTC,OAAQ,EACRC,cAAe,GACfC,UAAW,IACb,EAAE,CACFC,eAAe,EACfC,aAAc,EACdC,qBAAqB,EACrBC,gBAAiB,IACnB,EAAG,CACDrB,GAAI,EACJI,KAAM,kBACNC,YAAa,6BACbM,YAAY,EACZF,SAAU,EACVG,SAAU,CAAC,CACTZ,GAAI,EACJI,KAAM,uBACNC,YAAa,8BACbQ,aAAa,EACbF,YAAY,EACZG,SAAS,EACTC,OAAQ,EACRC,eAAe,EACfC,UAAW,IACb,EAAE,CACFC,cAAe,GACfC,aAAc,EACdC,qBAAqB,EACrBC,gBAAiB,IACnB,EAAE,CACFC,cAAc,EACdC,YAAa,GACbC,mBAAmB,EACnBC,oBAAoB,EACpBC,eAAgB,IAClB,EACMC,EAAoBC,GACxB,EAAajB,EAAT,QAAmB,CACnBiB,CADqB,CACbf,WAAW,EAAIe,EAAQZ,aAAa,CAAS,CAAP,WAC9CY,EAAQf,WAAW,EAAI,CAACe,EAAQZ,aAAa,CAAS,CAAP,cAC5C,cAHyB,SAK5Ba,EAAkB,IACtB,GAAI,CAACC,EAAOnB,UAAU,CAAE,MAAO,SAC/B,IAAMoB,EAAuBD,EAAOlB,QAAQ,CAACoB,KAAK,CAAC,GAAgBC,EAAEpB,WAAW,EAAIoB,EAAEjB,aAAa,SACnG,GAA4Bc,EAAOV,mBAAmB,CAAS,CAAP,WACpDW,GAAwB,CAACD,EAAOV,mBAAmB,CAAS,CAAP,cAClD,aACT,EACA,MAAO,WAAC/B,MAAAA,CAAIV,UAAU,YAAYY,wBAAsB,0BAA0BC,0BAAwB,qBAEtG,WAACJ,EAAAA,EAAIA,CAAAA,CAAC8C,sBAAoB,OAAO1C,0BAAwB,qBACvD,UAACC,EAAAA,EAAUA,CAAAA,CAACyC,sBAAoB,aAAa1C,0BAAwB,oBACnE,WAACH,MAAAA,CAAIV,UAAU,6CACb,WAACU,MAAAA,CAAIV,UAAU,sBACb,WAACU,MAAAA,CAAIV,UAAU,wCACb,UAACwD,KAAAA,CAAGxD,UAAU,8BAAsBuB,EAAOE,IAAI,GAC/C,UAACgC,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,UAAUH,sBAAoB,QAAQ1C,0BAAwB,oBAAYU,EAAOI,IAAI,MAEtG,UAACgC,IAAAA,CAAE3D,UAAU,iCAAyBuB,EAAOG,WAAW,GACxD,WAAChB,MAAAA,CAAIV,UAAU,sEACb,WAAC4D,OAAAA,WAAK,SAAOrC,EAAOK,UAAU,IAC9B,UAACgC,OAAAA,UAAK,MACN,WAACA,OAAAA,WAAK,eAAarC,EAAOM,UAAU,UAGxC,WAACnB,MAAAA,CAAIV,UAAU,uBACb,WAACU,MAAAA,CAAIV,UAAU,4CACZuB,EAAOO,QAAQ,CAAC,OAEnB,UAAC6B,IAAAA,CAAE3D,UAAU,yCAAgC,qBAInD,UAACiB,EAAAA,EAAWA,CAAAA,CAACsC,sBAAoB,cAAc1C,0BAAwB,oBACrE,UAACf,EAAAA,CAAQA,CAAAA,CAACG,MAAOsB,EAAOO,QAAQ,CAAE9B,UAAU,MAAMuD,sBAAoB,WAAW1C,0BAAwB,kBAK7G,WAACH,MAAAA,CAAIV,UAAU,sBACZuB,EAAOQ,OAAO,CAAC8B,GAAG,CAAC,CAACV,EAAQW,KAC7B,IAAMC,EAAeb,EAAgBC,GACrC,MAAO,WAAC1C,EAAAA,EAAIA,CAAAA,CAAiBT,UAAW,GAAG,CAACmD,EAAOnB,UAAU,CAAG,aAAe,IAAI,WAC7E,UAAClB,EAAAA,EAAUA,CAAAA,UACT,WAACJ,MAAAA,CAAIV,UAAU,8CACb,WAACU,MAAAA,CAAIV,UAAU,wCACb,UAACU,MAAAA,CAAIV,UAAW,CAAC,wDAAwD,EAAmB,cAAjB+D,EAA+B,8BAAiD,iBAAjBA,EAAkC,gCAAkCA,aAA4B,4BAA8B,6BAA6B,UACjR,cAAjBA,EAA+B,UAACC,EAAAA,CAAWA,CAAAA,CAAChE,UAAU,YAAgC,WAAjB+D,EAA4B,UAACE,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,YAAe,UAACkE,EAAAA,CAAQA,CAAAA,CAAClE,UAAU,cAEvJ,WAACU,MAAAA,WACC,WAACK,EAAAA,EAASA,CAAAA,CAACf,UAAU,oBAAU,UACrB8D,EAAc,EAAE,KAAGX,EAAO1B,IAAI,IAExC,UAACT,EAAAA,EAAeA,CAAAA,UAAEmC,EAAOzB,WAAW,SAGxC,WAAChB,MAAAA,CAAIV,UAAU,uBACb,WAACU,MAAAA,CAAIV,UAAU,8BAAqBmD,EAAOrB,QAAQ,CAAC,OACpD,UAAChC,EAAAA,CAAQA,CAAAA,CAACG,MAAOkD,EAAOrB,QAAQ,CAAE9B,UAAU,qBAIlD,UAACiB,EAAAA,EAAWA,CAAAA,UACV,WAACP,MAAAA,CAAIV,UAAU,sBACZmD,EAAOlB,QAAQ,CAAC4B,GAAG,CAAC,CAACZ,EAASkB,KACjC,IAAMC,EAAgBpB,EAAiBC,GACvC,MAAO,WAACvC,MAAAA,CAAqBV,UAAW,CAAC,wDAAwD,EAAE,CAACiD,EAAQjB,UAAU,CAAG,aAAe,IAAI,WACpI,WAACtB,MAAAA,CAAIV,UAAU,wCACb,UAACU,MAAAA,CAAIV,UAAW,CAAC,sDAAsD,EAAoB,cAAlBoE,EAAgC,8BAAkD,iBAAlBA,EAAmC,gCAAkCA,aAA6B,4BAA8B,6BAA6B,UACjR,cAAlBA,EAAgC,UAACJ,EAAAA,CAAWA,CAAAA,CAAChE,UAAU,YAAiC,iBAAlBoE,EAAmC,UAACC,EAAAA,CAAWA,CAAAA,CAACrE,UAAU,YAAiC,WAAlBoE,EAA6B,UAACH,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,YAAe,UAACkE,EAAAA,CAAQA,CAAAA,CAAClE,UAAU,cAElO,WAACU,MAAAA,WACC,UAACiD,IAAAA,CAAE3D,UAAU,uBAAeiD,EAAQxB,IAAI,GACxC,UAACkC,IAAAA,CAAE3D,UAAU,yCACViD,EAAQvB,WAAW,GAErBuB,EAAQX,SAAS,EAAI,WAACqB,IAAAA,CAAE3D,UAAU,mCAAyB,eAC3CiD,EAAQX,SAAS,CAAC,aAIvC,WAAC5B,MAAAA,CAAIV,UAAU,wCACZiD,EAAQjB,UAAU,EAAI,iCACnB,WAACsC,EAAAA,CAAMA,CAAAA,CAACZ,QAAQ,UAAUa,KAAK,KAAKC,SAAU,CAACvB,EAAQjB,UAAU,WAC/D,UAACkC,EAAAA,CAAQA,CAAAA,CAAClE,UAAU,iBACnBiD,EAAQf,WAAW,CAAG,SAAW,WAEnCe,EAAQd,OAAO,EAAI,WAACmC,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKC,SAAU,CAACvB,EAAQf,WAAW,EAAIe,EAAQZ,aAAa,CAAEqB,QAAST,EAAQZ,aAAa,CAAG,UAAY,oBACxI,UAACoC,EAAAA,CAAQA,CAAAA,CAACzE,UAAU,iBACnBiD,EAAQZ,aAAa,CAAG,CAAC,MAAM,EAAEY,EAAQX,SAAS,CAAC,CAAC,CAAC,CAAG,kBAGhE,CAACW,EAAQjB,UAAU,EAAI,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,oBACnC,UAACO,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,iBAAiB,iBA3B9BiD,EAAQ5B,EAAE,CAgC7B,GAGK8B,EAAOZ,aAAa,EAAI,UAAC7B,MAAAA,CAAIV,UAAW,CAAC,2CAA2C,EAAmB,iBAAjB+D,EAAkC,iCAAmCZ,EAAOV,mBAAmB,CAAG,+BAAiC,8BAA8B,UACpP,WAAC/B,MAAAA,CAAIV,UAAU,8CACb,WAACU,MAAAA,CAAIV,UAAU,wCACb,UAACyE,EAAAA,CAAQA,CAAAA,CAACzE,UAAW,CAAC,QAAQ,EAAEmD,EAAOV,mBAAmB,CAAG,iBAAoC,iBAAjBsB,EAAkC,kBAAoB,iBAAiB,GACvJ,WAACrD,MAAAA,WACC,WAACkD,OAAAA,CAAK5D,UAAU,wBAAc,gBACdmD,EAAO1B,IAAI,IAE1B0B,EAAOT,eAAe,EAAI,WAACkB,OAAAA,CAAK5D,UAAU,wCAA8B,UAC7DmD,EAAOT,eAAe,CAAC,aAIvC,UAAC4B,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKC,SAA2B,iBAAjBT,EAAiCL,QAASP,EAAOV,mBAAmB,CAAG,UAAY,mBAC5GU,EAAOV,mBAAmB,CAAG,cAAgB,iCAzE9CU,EAAO9B,EAAE,CAgF7B,GAGGE,EAAOoB,YAAY,EAAI,UAAClC,EAAAA,EAAIA,CAAAA,CAACT,UAAW,CAAC,SAAS,EAAEuB,EAAOsB,iBAAiB,CAAG,iCAAmC,8BAA8B,UAC7I,UAAC5B,EAAAA,EAAWA,CAAAA,CAACjB,UAAU,gBACrB,WAACU,MAAAA,CAAIV,UAAU,8CACb,WAACU,MAAAA,CAAIV,UAAU,wCACb,UAACU,MAAAA,CAAIV,UAAW,CAAC,wDAAwD,EAAEuB,EAAOuB,kBAAkB,CAAG,8BAAgCvB,EAAOsB,iBAAiB,CAAG,gCAAkC,6BAA6B,UAC9NtB,EAAOuB,kBAAkB,CAAG,UAACkB,EAAAA,CAAWA,CAAAA,CAAChE,UAAU,YAAeuB,EAAOsB,iBAAiB,CAAG,UAAC6B,EAAAA,CAAKA,CAAAA,CAAC1E,UAAU,YAAe,UAACiE,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,cAEhJ,WAACU,MAAAA,WACC,UAACiE,KAAAA,CAAG3E,UAAU,iCAAwB,sBACtC,UAAC2D,IAAAA,CAAE3D,UAAU,yCAAgC,qDAG5C,CAACuB,EAAOsB,iBAAiB,EAAI,UAACc,IAAAA,CAAE3D,UAAU,wCAA+B,yCAK9E,UAACsE,EAAAA,CAAMA,CAAAA,CAACC,KAAK,KAAKC,SAAU,CAACjD,EAAOsB,iBAAiB,CAAEa,QAASnC,EAAOuB,kBAAkB,CAAG,UAAY,mBACrGvB,EAAOuB,kBAAkB,CAAG,iCACzB,UAAC4B,EAAAA,CAAKA,CAAAA,CAAC1E,UAAU,iBAAiB,sBAE9BuB,EAAOsB,iBAAiB,CAAG,iCAC/B,UAAC+B,EAAAA,CAAIA,CAAAA,CAAC5E,UAAU,iBAAiB,qBAE7B,iCACJ,UAACiE,EAAAA,CAAIA,CAAAA,CAACjE,UAAU,iBAAiB,0BASvD,0BC9UA,2CCAA,uCAAkL,yBCAlL,gDCAA,wGCAA,gECAA,kDCAA,iECAA,gDCAA,qCAAqK,yBCArK,uDCAA,sDCAA,+ECmBM,MAAO,cAAiB,QAhBM,CAClC,CAAC,MAAQ,EAAE,KAAO,MAAM,OAAQ,IAAM,GAAG,CAAK,KAAG,KAAM,CAAI,MAAK,GAAI,GAAK,KAAK,SAAU,EACxF,CAAC,MAAQ,EAAE,EAAG,CAA4B,8BAAK,SAAU,EAC3D,yBCNA,sDCAA,sECAA,oDCAA,kECAA,yDCAA,iEjCmBI,sBAAsB,gMkCbb6E,EAAqB,CAChCC,KADWD,CACJ,wBACPnD,WAAAA,CAAa,6BACf,EACe,eAAeqD,EAAgB,CAC5ClF,UAAQ,CAGT,CAJ6BkF,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkC/E,KAAAA,GAAU,OAChE,MAAOmF,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAK9B,qBAAAA,CAAoB,OAAO3C,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAyE,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBL,WAAAA,CAAaA,EAAa3B,SAAb2B,YAAa3B,CAAoB,kBAAkB1C,yBAAAA,CAAwB,uBACvGuE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAWjC,qBAAAA,CAAoB,aAAa1C,yBAAAA,CAAwB,eACrEyE,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAalC,qBAAAA,CAAoB,eAAe1C,yBAAAA,CAAwB,uBACvEuE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAOnC,qBAAAA,CAAoB,SAAS1C,yBAAAA,CAAwB,eAE7DuE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAK3F,SAAAA,CAAU,kDACbH,QAAAA,CAAAA,WAMb,ClCvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAIH,CALS,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,EAAI,OAC7D,EADsE,GACzC,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,CACvB,iBAAiB,iBACjB,UACA,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CAOxB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,ImChF9B,0YCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,UACA,CACA,UACA,OACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,sCAAkL,CA0BtM,iJAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA5CA,IAAsB,uCAAqK,CA4C3L,qIAIA,CAAO,CACP,CACA,QAnDA,IAAsB,uCAA4J,CAmDlL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApEA,IAAsB,sCAAiJ,CAoEvK,gHACA,gBApEA,IAAsB,uCAAuJ,CAoE7K,sHACA,aApEA,IAAsB,uCAAoJ,CAoE1K,mHACA,WApEA,IAAsB,4CAAgF,CAoEtG,+CACA,cApEA,IAAsB,4CAAmF,CAoEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,oJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,4CACA,2CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,2GCrGK,EAAgB,WAIhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GAIlE,CAAC,EAAkB,EAAkB,CACzC,EAA4C,EALuC,CAe/E,EAAiB,QAXoB,IAWpB,CACrB,CAAC,EAAmC,aAClC,GAAM,CACJ,kBACA,MAAO,EAAY,KACnB,IAAK,gBACL,EAAgB,EAChB,GAAG,EACL,CAAI,CAEJ,EAAK,GAAW,MAAY,EAAM,CAAC,EAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,CA8FQ,EA9FW,GAAG,EAAO,EA+FxC,CA/F4C,CA8FN,CA9FE,QAAc,CAAC,KA8FM,kBAC1B,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EA4F+I,GA5FnH,EAhCxB,EAgCoB,CAEhC,GA0FkK,EA5FpH,GAExB,GAAC,EAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,CA4FU,EA5FW,GAAG,EAAS,EA6F5C,CA7FgD,CA4FR,GA5FI,MAAc,CAAC,KA4FI,oBAC1B,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,EAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,EAAS,GAAS,EAAJ,EAAyB,GAAG,KAAI,EAEjE,MACE,UAAC,GAAiB,MAAO,QAAiB,MAAc,EACtD,mBAAC,IAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,EAAS,GAAS,EAAJ,KAAY,EACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,EAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAiB,oBAKjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,EAAmB,EAAgB,GACnD,CAF2C,KAGzC,MAFgE,EAEhE,EAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,EAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,EAAiB,EAAkC,GAAiC,OAC3E,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,EAAS,GAA6B,MACrB,UAAjB,OAAO,CAChB,CAEA,SAAS,EAAiB,GAAyB,OAG/C,EAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,EAAmB,EAAY,GAA8B,OAGlE,EAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,EAAkB,YAAc,EAiDhC,IAAM,EAAO,EACP,EAAY,0BCpJlB,qDCAA,4DCAA,wDCAA,0DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,wBCNjN,wDCAA,sDCAA,iDCAA,2DCAA,+ICIA,IAAM+F,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRpC,QAAS,CACPqC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfzC,QAAS,SACX,CACF,GACA,SAASD,EAAM,WACbzD,CAAS,SACT0D,CAAO,SACP0C,GAAU,CAAK,CACf,GAAGlG,EAGJ,EACC,IAAMmG,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAK1F,YAAU,QAAQX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuF,EAAc,SACzDlC,CACF,GAAI1D,GAAa,GAAGE,CAAK,CAAEqD,sBAAoB,OAAO3C,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,iDCAA,yDCAA,mFCmBM,MAAiB,cAAiB,kBAhBJ,CAClC,CAAC,MAAQ,EAAE,EAAG,CAAmC,qCAAK,SAAU,EAChE,CAAC,MAAQ,EAAE,EAAG,CAAkB,oBAAK,SAAU,EACjD,0BCNA,8DrDmBI,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CACJ,CAF4B,CAAC,CA7BL0F,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,csDvEtB,GtDgF8B,KsDhF9B,+BAAqK", "sources": ["webpack://terang-lms-ui/./src/app/dashboard/student/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/../../../src/icons/circle-alert.ts", "webpack://terang-lms-ui/?598a", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/../../../src/icons/play.ts", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/./src/app/dashboard/student/courses/[id]/page.tsx", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/?a862", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/?8902", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/../../../src/icons/lock.ts", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/?b3ff", "webpack://terang-lms-ui/../src/progress.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/./src/components/ui/badge.tsx", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/../../../src/icons/circle-check-big.ts", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/?d67a"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function StudentLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has student role\n    requireRole('student');\n  }, []);\n  return <>{children}</>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/student/courses/[id]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/student/courses/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/student/courses/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/student/courses/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('CircleAlert', __iconNode);\n\nexport default CircleAlert;\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\[id]\\\\page.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', __iconNode);\n\nexport default Play;\n", "module.exports = require(\"os\");", "'use client';\n\nimport { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { BookOpen, FileText, Clock, Award, Play, CheckCircle, Lock, AlertCircle } from 'lucide-react';\nimport Link from 'next/link';\nimport { use } from 'react';\n\n// Define interfaces for better type safety\ninterface Chapter {\n  id: number;\n  name: string;\n  description: string;\n  isCompleted: boolean;\n  isUnlocked: boolean;\n  hasQuiz: boolean;\n  quizId: number;\n  quizCompleted: boolean;\n  quizScore: number | null;\n}\ninterface Module {\n  id: number;\n  name: string;\n  description: string;\n  isUnlocked: boolean;\n  progress: number;\n  chapters: Chapter[];\n  hasModuleQuiz: boolean;\n  moduleQuizId: number;\n  moduleQuizCompleted: boolean;\n  moduleQuizScore: number | null;\n}\ninterface Course {\n  id: number;\n  name: string;\n  description: string;\n  type: string;\n  courseCode: string;\n  instructor: string;\n  progress: number;\n  modules: Module[];\n  hasFinalExam: boolean;\n  finalExamId: number;\n  finalExamUnlocked: boolean;\n  finalExamCompleted: boolean;\n  finalExamScore: number | null;\n}\nexport default function StudentCourseDetailPage({\n  params\n}: {\n  params: Promise<{\n    id: string;\n  }>;\n}) {\n  // Unwrap the Promise using React's use() hook\n  const {\n    id\n  } = use(params);\n\n  // Mock data - in real app, this would come from API\n  const course: Course = {\n    id: parseInt(id),\n    name: 'Introduction to Algebra',\n    description: 'Basic algebraic concepts and problem solving',\n    type: 'self_paced',\n    courseCode: 'MATH101',\n    instructor: 'Dr. Smith',\n    progress: 76,\n    modules: [{\n      id: 1,\n      name: 'Introduction and Fundamentals',\n      description: 'Basic concepts and introduction to algebra',\n      isUnlocked: true,\n      progress: 100,\n      chapters: [{\n        id: 1,\n        name: 'Chapter 1: Overview',\n        description: 'Introduction to algebraic thinking',\n        isCompleted: true,\n        isUnlocked: true,\n        hasQuiz: true,\n        quizId: 1,\n        quizCompleted: true,\n        quizScore: 92\n      }, {\n        id: 2,\n        name: 'Chapter 2: Basic Concepts',\n        description: 'Fundamental algebraic principles',\n        isCompleted: true,\n        isUnlocked: true,\n        hasQuiz: true,\n        quizId: 2,\n        quizCompleted: true,\n        quizScore: 88\n      }, {\n        id: 3,\n        name: 'Chapter 3: Variables and Expressions',\n        description: 'Working with variables and expressions',\n        isCompleted: true,\n        isUnlocked: true,\n        hasQuiz: true,\n        quizId: 3,\n        quizCompleted: true,\n        quizScore: 95\n      }],\n      hasModuleQuiz: true,\n      moduleQuizId: 4,\n      moduleQuizCompleted: true,\n      moduleQuizScore: 90\n    }, {\n      id: 2,\n      name: 'Core Algebraic Operations',\n      description: 'Essential operations and manipulations',\n      isUnlocked: true,\n      progress: 60,\n      chapters: [{\n        id: 4,\n        name: 'Chapter 4: Addition and Subtraction',\n        description: 'Basic algebraic operations',\n        isCompleted: true,\n        isUnlocked: true,\n        hasQuiz: true,\n        quizId: 5,\n        quizCompleted: true,\n        quizScore: 85\n      }, {\n        id: 5,\n        name: 'Chapter 5: Multiplication and Division',\n        description: 'Advanced algebraic operations',\n        isCompleted: false,\n        isUnlocked: true,\n        hasQuiz: true,\n        quizId: 6,\n        quizCompleted: false,\n        quizScore: null\n      }],\n      hasModuleQuiz: true,\n      moduleQuizId: 7,\n      moduleQuizCompleted: false,\n      moduleQuizScore: null\n    }, {\n      id: 3,\n      name: 'Advanced Topics',\n      description: 'Complex algebraic concepts',\n      isUnlocked: false,\n      progress: 0,\n      chapters: [{\n        id: 6,\n        name: 'Chapter 6: Equations',\n        description: 'Solving algebraic equations',\n        isCompleted: false,\n        isUnlocked: false,\n        hasQuiz: true,\n        quizId: 8,\n        quizCompleted: false,\n        quizScore: null\n      }],\n      hasModuleQuiz: true,\n      moduleQuizId: 9,\n      moduleQuizCompleted: false,\n      moduleQuizScore: null\n    }],\n    hasFinalExam: true,\n    finalExamId: 10,\n    finalExamUnlocked: false,\n    finalExamCompleted: false,\n    finalExamScore: null\n  };\n  const getChapterStatus = (chapter: Chapter) => {\n    if (!chapter.isUnlocked) return 'locked';\n    if (chapter.isCompleted && chapter.quizCompleted) return 'completed';\n    if (chapter.isCompleted && !chapter.quizCompleted) return 'quiz-pending';\n    return 'in-progress';\n  };\n  const getModuleStatus = (module: Module) => {\n    if (!module.isUnlocked) return 'locked';\n    const allChaptersCompleted = module.chapters.every((c: Chapter) => c.isCompleted && c.quizCompleted);\n    if (allChaptersCompleted && module.moduleQuizCompleted) return 'completed';\n    if (allChaptersCompleted && !module.moduleQuizCompleted) return 'quiz-pending';\n    return 'in-progress';\n  };\n  return <div className='space-y-6' data-sentry-component=\"StudentCourseDetailPage\" data-sentry-source-file=\"page.tsx\">\r\n      {/* Course Header */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <div className='flex items-start justify-between'>\r\n            <div className='space-y-2'>\r\n              <div className='flex items-center space-x-2'>\r\n                <h1 className='text-2xl font-bold'>{course.name}</h1>\r\n                <Badge variant='outline' data-sentry-element=\"Badge\" data-sentry-source-file=\"page.tsx\">{course.type}</Badge>\r\n              </div>\r\n              <p className='text-muted-foreground'>{course.description}</p>\r\n              <div className='text-muted-foreground flex items-center space-x-4 text-sm'>\r\n                <span>Code: {course.courseCode}</span>\r\n                <span>•</span>\r\n                <span>Instructor: {course.instructor}</span>\r\n              </div>\r\n            </div>\r\n            <div className='text-right'>\r\n              <div className='text-primary text-3xl font-bold'>\r\n                {course.progress}%\r\n              </div>\r\n              <p className='text-muted-foreground text-sm'>Complete</p>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <Progress value={course.progress} className='h-3' data-sentry-element=\"Progress\" data-sentry-source-file=\"page.tsx\" />\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Course Modules */}\r\n      <div className='space-y-6'>\r\n        {course.modules.map((module, moduleIndex) => {\n        const moduleStatus = getModuleStatus(module);\n        return <Card key={module.id} className={`${!module.isUnlocked ? 'opacity-60' : ''}`}>\r\n              <CardHeader>\r\n                <div className='flex items-center justify-between'>\r\n                  <div className='flex items-center space-x-3'>\r\n                    <div className={`flex h-10 w-10 items-center justify-center rounded-full ${moduleStatus === 'completed' ? 'bg-green-100 text-green-600' : moduleStatus === 'quiz-pending' ? 'bg-yellow-100 text-yellow-600' : moduleStatus === 'locked' ? 'bg-gray-100 text-gray-400' : 'bg-blue-100 text-blue-600'}`}>\r\n                      {moduleStatus === 'completed' ? <CheckCircle className='h-5 w-5' /> : moduleStatus === 'locked' ? <Lock className='h-5 w-5' /> : <BookOpen className='h-5 w-5' />}\r\n                    </div>\r\n                    <div>\r\n                      <CardTitle className='text-lg'>\r\n                        Module {moduleIndex + 1}: {module.name}\r\n                      </CardTitle>\r\n                      <CardDescription>{module.description}</CardDescription>\r\n                    </div>\r\n                  </div>\r\n                  <div className='text-right'>\r\n                    <div className='text-xl font-bold'>{module.progress}%</div>\r\n                    <Progress value={module.progress} className='h-2 w-20' />\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className='space-y-3'>\r\n                  {module.chapters.map((chapter, chapterIndex) => {\n                const chapterStatus = getChapterStatus(chapter);\n                return <div key={chapter.id} className={`flex items-center justify-between rounded-lg border p-3 ${!chapter.isUnlocked ? 'bg-gray-50' : ''}`}>\r\n                        <div className='flex items-center space-x-3'>\r\n                          <div className={`flex h-8 w-8 items-center justify-center rounded-full ${chapterStatus === 'completed' ? 'bg-green-100 text-green-600' : chapterStatus === 'quiz-pending' ? 'bg-yellow-100 text-yellow-600' : chapterStatus === 'locked' ? 'bg-gray-100 text-gray-400' : 'bg-blue-100 text-blue-600'}`}>\r\n                            {chapterStatus === 'completed' ? <CheckCircle className='h-4 w-4' /> : chapterStatus === 'quiz-pending' ? <AlertCircle className='h-4 w-4' /> : chapterStatus === 'locked' ? <Lock className='h-4 w-4' /> : <BookOpen className='h-4 w-4' />}\r\n                          </div>\r\n                          <div>\r\n                            <p className='font-medium'>{chapter.name}</p>\r\n                            <p className='text-muted-foreground text-sm'>\r\n                              {chapter.description}\r\n                            </p>\r\n                            {chapter.quizScore && <p className='text-xs text-green-600'>\r\n                                Quiz Score: {chapter.quizScore}%\r\n                              </p>}\r\n                          </div>\r\n                        </div>\r\n                        <div className='flex items-center space-x-2'>\r\n                          {chapter.isUnlocked && <>\r\n                              <Button variant='outline' size='sm' disabled={!chapter.isUnlocked}>\r\n                                <BookOpen className='mr-1 h-3 w-3' />\r\n                                {chapter.isCompleted ? 'Review' : 'Study'}\r\n                              </Button>\r\n                              {chapter.hasQuiz && <Button size='sm' disabled={!chapter.isCompleted || chapter.quizCompleted} variant={chapter.quizCompleted ? 'outline' : 'default'}>\r\n                                  <FileText className='mr-1 h-3 w-3' />\r\n                                  {chapter.quizCompleted ? `Quiz: ${chapter.quizScore}%` : 'Take Quiz'}\r\n                                </Button>}\r\n                            </>}\r\n                          {!chapter.isUnlocked && <Badge variant='outline'>\r\n                              <Lock className='mr-1 h-3 w-3' />\r\n                              Locked\r\n                            </Badge>}\r\n                        </div>\r\n                      </div>;\n              })}\r\n\r\n                  {/* Module Quiz */}\r\n                  {module.hasModuleQuiz && <div className={`mt-4 rounded-lg border-2 border-dashed p-3 ${moduleStatus === 'quiz-pending' ? 'border-yellow-300 bg-yellow-50' : module.moduleQuizCompleted ? 'border-green-300 bg-green-50' : 'border-gray-300 bg-gray-50'}`}>\r\n                      <div className='flex items-center justify-between'>\r\n                        <div className='flex items-center space-x-2'>\r\n                          <FileText className={`h-4 w-4 ${module.moduleQuizCompleted ? 'text-green-600' : moduleStatus === 'quiz-pending' ? 'text-yellow-600' : 'text-gray-400'}`} />\r\n                          <div>\r\n                            <span className='font-medium'>\r\n                              Module Quiz: {module.name}\r\n                            </span>\r\n                            {module.moduleQuizScore && <span className='ml-2 text-sm text-green-600'>\r\n                                Score: {module.moduleQuizScore}%\r\n                              </span>}\r\n                          </div>\r\n                        </div>\r\n                        <Button size='sm' disabled={moduleStatus !== 'quiz-pending'} variant={module.moduleQuizCompleted ? 'outline' : 'default'}>\r\n                          {module.moduleQuizCompleted ? 'Review Quiz' : 'Take Module Quiz'}\r\n                        </Button>\r\n                      </div>\r\n                    </div>}\r\n                </div>\r\n              </CardContent>\r\n            </Card>;\n      })}\r\n\r\n        {/* Final Exam */}\r\n        {course.hasFinalExam && <Card className={`border-2 ${course.finalExamUnlocked ? 'border-yellow-300 bg-yellow-50' : 'border-gray-300 bg-gray-50'}`}>\r\n            <CardContent className='pt-6'>\r\n              <div className='flex items-center justify-between'>\r\n                <div className='flex items-center space-x-3'>\r\n                  <div className={`flex h-12 w-12 items-center justify-center rounded-full ${course.finalExamCompleted ? 'bg-green-100 text-green-600' : course.finalExamUnlocked ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-400'}`}>\r\n                    {course.finalExamCompleted ? <CheckCircle className='h-6 w-6' /> : course.finalExamUnlocked ? <Award className='h-6 w-6' /> : <Lock className='h-6 w-6' />}\r\n                  </div>\r\n                  <div>\r\n                    <h3 className='text-lg font-semibold'>Final Examination</h3>\r\n                    <p className='text-muted-foreground text-sm'>\r\n                      Comprehensive exam covering all course materials\r\n                    </p>\r\n                    {!course.finalExamUnlocked && <p className='mt-1 text-xs text-yellow-600'>\r\n                        Complete all modules to unlock\r\n                      </p>}\r\n                  </div>\r\n                </div>\r\n                <Button size='lg' disabled={!course.finalExamUnlocked} variant={course.finalExamCompleted ? 'outline' : 'default'}>\r\n                  {course.finalExamCompleted ? <>\r\n                      <Award className='mr-2 h-4 w-4' />\r\n                      View Certificate\r\n                    </> : course.finalExamUnlocked ? <>\r\n                      <Play className='mr-2 h-4 w-4' />\r\n                      Take Final Exam\r\n                    </> : <>\r\n                      <Lock className='mr-2 h-4 w-4' />\r\n                      Locked\r\n                    </>}\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>}\r\n      </div>\r\n    </div>;\n}", "module.exports = require(\"util\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\[id]\\\\page.tsx\");\n", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\n", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '11', x: '3', y: '11', rx: '2', ry: '2', key: '1w4ew1' }],\n  ['path', { d: 'M7 11V7a5 5 0 0 1 10 0v4', key: 'fwvmzm' }],\n];\n\n/**\n * @component @name Lock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTEiIHg9IjMiIHk9IjExIiByeD0iMiIgcnk9IjIiIC8+CiAgPHBhdGggZD0iTTcgMTFWN2E1IDUgMCAwIDEgMTAgMHY0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lock = createLucideIcon('Lock', __iconNode);\n\nexport default Lock;\n", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\[id]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'student',\n        {\n        children: [\n        'courses',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\[id]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/student/courses/[id]/page\",\n        pathname: \"/dashboard/student/courses/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('CircleCheckBig', __iconNode);\n\nexport default CircleCheckBig;\n", "module.exports = require(\"events\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\n"], "names": ["StudentLayout", "children", "Progress", "React", "className", "value", "props", "ref", "ProgressPrimitive", "cn", "style", "transform", "displayName", "Card", "div", "data-slot", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "StudentCourseDetailPage", "params", "id", "use", "course", "parseInt", "name", "description", "type", "courseCode", "instructor", "progress", "modules", "isUnlocked", "chapters", "isCompleted", "hasQuiz", "quizId", "quizCompleted", "quizScore", "hasModuleQuiz", "moduleQuizId", "moduleQuizCompleted", "moduleQuizScore", "hasFinalExam", "finalExamId", "finalExamUnlocked", "finalExamCompleted", "finalExamScore", "getChapterStatus", "chapter", "getModuleStatus", "module", "allChaptersCompleted", "every", "c", "data-sentry-element", "h1", "Badge", "variant", "p", "span", "map", "moduleIndex", "moduleStatus", "CheckCircle", "Lock", "BookOpen", "chapterIndex", "chapterStatus", "AlertCircle", "<PERSON><PERSON>", "size", "disabled", "FileText", "Award", "h3", "Play", "metadata", "title", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "badgeVariants", "cva", "variants", "default", "secondary", "destructive", "outline", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "serverComponentModule.default"], "sourceRoot": ""}