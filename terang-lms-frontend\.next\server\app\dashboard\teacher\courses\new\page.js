try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="5821333d-c9c6-4550-af3e-9bed7fbcae99",e._sentryDebugIdIdentifier="sentry-dbid-5821333d-c9c6-4550-af3e-9bed7fbcae99")}catch(e){}(()=>{var e={};e.id=5544,e.ids=[5544],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7333:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(91754),o=t(21372),a=t(56682),i=t(4978),n=t(54384),d=t(81012),c=t(48071),u=t(16041),l=t.n(u),p=t(3240),m=t(93491);function h(){let e=(0,o.useRouter)(),{user:r}=(0,c.A)(),[t,u]=(0,m.useState)(!1),h=async e=>{try{u(!0),new FormData().append("file",e);let r=await fetch(`/api/upload?filename=${e.name}`,{method:"POST",body:e});if(!r.ok)throw Error("Failed to upload image");return(await r.json()).url}catch(e){return console.error("Error uploading image:",e),d.oR.error("Failed to upload cover image"),null}finally{u(!1)}},f=async t=>{if(!r)return void d.oR.error("Please log in to create courses");try{let s=null;if(t.coverImage&&(d.oR.loading("Uploading cover image...",{id:"course-creation"}),!(s=await h(t.coverImage))))return;d.oR.loading("Creating course...",{id:"course-creation"});let o=await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t.name,description:t.description,instructor:t.instructor,courseCode:t.courseCode,type:t.type,enrollmentType:t.enrollmentType,startDate:t.startDate,endDate:t.endDate,teacherId:r.id,institutionId:r.institutionId,coverPicture:s,isPurchasable:"purchase"===t.enrollmentType||"both"===t.enrollmentType,price:t.price,currency:t.currency,previewMode:t.previewMode,admissions:t.admissions,academics:t.academics,tuitionAndFinancing:t.tuitionAndFinancing,careers:t.careers,studentExperience:t.studentExperience})}),a=await o.json();if(!a.success)throw Error(a.error||"Failed to create course");let i=a.course;d.oR.loading("Creating modules...",{id:"course-creation"});let n=[];for(let e=0;e<t.modules.length;e++){let s=t.modules[e],o=await fetch("/api/modules",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,description:s.description,courseId:i.id,teacherId:r.id,orderIndex:s.orderIndex})}),a=await o.json();if(!a.module)throw Error(`Failed to create module: ${s.name}`);n.push({...a.module,originalData:s})}d.oR.loading("Creating chapters...",{id:"course-creation"});let c=[];for(let e of n){let t=e.originalData;for(let s=0;s<t.chapters.length;s++){let o=t.chapters[s],a=await fetch("/api/chapters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:o.name,content:o.content,moduleId:e.id,teacherId:r.id,orderIndex:o.orderIndex})}),i=await a.json();if(!i.chapter)throw Error(`Failed to create chapter: ${o.name}`);c.push({...i.chapter,originalData:o})}}if(t.modules.some(e=>e.chapters.some(e=>e.hasChapterQuiz&&e.chapterQuiz)))for(let e of(d.oR.loading("Creating quizzes...",{id:"course-creation"}),c)){let t=e.originalData;if(t.hasChapterQuiz&&t.chapterQuiz){let s=t.chapterQuiz,o=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,description:s.description,quizType:"chapter",timeLimit:s.timeLimit,minimumScore:s.minimumScore,chapterId:e.id,teacherId:r.id,questions:s.questions.map(e=>({type:e.type,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation,points:e.points,orderIndex:e.orderIndex}))})});(await o.json()).quiz||console.warn(`Failed to create quiz for chapter: ${t.name}`)}}if(t.modules.some(e=>e.hasModuleQuiz&&e.moduleQuiz))for(let e of(d.oR.loading("Creating module quizzes...",{id:"course-creation"}),n)){let t=e.originalData;if(t.hasModuleQuiz&&t.moduleQuiz){let s=t.moduleQuiz,o=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,description:s.description,quizType:"module",timeLimit:s.timeLimit,minimumScore:s.minimumScore,moduleId:e.id,teacherId:r.id,questions:s.questions.map(e=>({type:e.type,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation,points:e.points,orderIndex:e.orderIndex}))})});(await o.json()).quiz||console.warn(`Failed to create module quiz: ${s.name}`)}}if(t.finalExam){d.oR.loading("Creating final exam...",{id:"course-creation"});let e=t.finalExam,s=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,description:e.description,quizType:"final",timeLimit:e.timeLimit,minimumScore:e.minimumScore,courseId:i.id,teacherId:r.id,questions:e.questions.map(e=>({type:e.type,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation,points:e.points,orderIndex:e.orderIndex}))})});(await s.json()).quiz||console.warn("Failed to create final exam")}d.oR.success("Course created successfully with all modules, chapters, and quizzes!",{id:"course-creation"}),e.push("/dashboard/teacher/courses")}catch(e){console.error("Error creating course:",e),d.oR.error(e instanceof Error?e.message:"Failed to create course",{id:"course-creation"})}};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"NewCoursePage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(l(),{href:"/dashboard/teacher/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(a.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Create New Course"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Create a comprehensive course with our step-by-step wizard"})]})]}),(0,s.jsx)(l(),{href:"/dashboard/teacher/courses/generate","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(a.$,{variant:"outline","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Sparkles","data-sentry-source-file":"page.tsx"}),"Try AI Generator"]})})]}),(0,s.jsx)(p.q,{onComplete:f,onCancel:()=>e.push("/dashboard/teacher/courses"),"data-sentry-element":"CourseCreationWizard","data-sentry-source-file":"page.tsx"})]})}},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12966:(e,r,t)=>{Promise.resolve().then(t.bind(t,78668))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54384:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(55732).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78668:(e,r,t)=>{"use strict";let s;t.r(r),t.d(r,{default:()=>m,generateImageMetadata:()=>l,generateMetadata:()=>u,generateViewport:()=>p});var o=t(63033),a=t(1472),i=t(7688),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\new\\page.tsx","default");let d={...o},c="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s="function"==typeof n?new Proxy(n,{apply:(e,r,t)=>{let s,o,a;try{let e=c?.getStore();s=e?.headers.get("sentry-trace")??void 0,o=e?.headers.get("baggage")??void 0,a=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/teacher/courses/new",componentType:"Page",sentryTraceHeader:s,baggageHeader:o,headers:a}).apply(r,t)}}):n;let u=void 0,l=void 0,p=void 0,m=s},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},82702:(e,r,t)=>{Promise.resolve().then(t.bind(t,7333))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94237:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.default,__next_app__:()=>u,pages:()=>c,routeModule:()=>l,tree:()=>d});var s=t(95500),o=t(56947),a=t(26052),i=t(13636),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let d={children:["",{children:["dashboard",{children:["teacher",{children:["courses",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78668)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,64755)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\teacher\\courses\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/teacher/courses/new/page",pathname:"/dashboard/teacher/courses/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8103,4463,411,6429,8134,8634,5660,3163],()=>t(94237));module.exports=s})();
//# sourceMappingURL=page.js.map