{"version": 3, "file": "../app/(course-view)/my-courses/[courseId]/exam/page.js", "mappings": "8cAM+C,MAAQ,cAAC,0BAA0B,wDAAwD,IAAyB,uBCNnK,6GCAA,gVCiBA,OACA,UACA,GACA,CACA,UACA,gBACA,CACA,UACA,aACA,CACA,UACA,aACA,CACA,UACA,OACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,uCAA4L,CA0BhN,2JAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QApDA,IAAsB,sCAAgK,CAoDtL,+HACA,WApDA,IAAsB,4CAAgF,CAoDtG,+CACA,cApDA,IAAsB,4CAAmF,CAoDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAvEA,IAAsB,sCAAiJ,CAuEvK,gHACA,gBAvEA,IAAsB,uCAAuJ,CAuE7K,sHACA,aAvEA,IAAsB,uCAAoJ,CAuE1K,mHACA,WAvEA,IAAsB,4CAAgF,CAuEtG,+CACA,cAvEA,IAAsB,4CAAmF,CAuEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,8JAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,sDACA,uCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,mFC/GD,gBACA,MAAkB,eAAmB,IACrC,MACA,aAAY,QAAuB,EACnC,EAAkB,SAAa,yBAC/B,MAA2B,SAAG,mBAAqB,aAAiB,CACpE,SACA,2BAOA,GANA,YACA,MAAoB,YAAgB,IACpC,cACA,sBACA,kBAAyB,EAAa,2BAA2B,EAAkB,IACnF,EACA,CAEA,mBACA,SAqBA,OACA,eACa,eAAmB,KAEhC,mBACA,gBACA,OAAa,SAAa,CAC1B,MAAiB,WAAW,EAAU,IAAM,cAC5C,MAEA,CACA,EAEA,OADA,cACA,CAjCA,cACA,MAAwB,eAAmB,IAC3C,WACA,WACA,UACA,UAAc,mBAA8B,EAC5C,iBACA,EAAoB,SAAa,yBACjC,MAA6B,SAAG,mBAAqB,aAAiB,CACtE,SACA,2BAQA,GAPA,cACA,qBACA,EAAsB,YAAgB,IACtC,cACA,sBACA,kBAA2B,EAAa,2BAA2B,EAAkB,IACrF,EACA,EAeA,SAEA,MACA,WACA,yBACA,WACA,iBACA,aACA,sBACA,CAAK,EACL,mBACA,4BAA2D,cAAqB,IAEhF,QADA,EACA,WAAkD,EAAU,GAC5D,OAAiB,UACjB,CAAO,GAAI,EACX,OAAa,SAAa,OAAU,WAAW,YAAoB,MAAgB,KACnF,CACA,EAEA,OADA,wBACA,CACA,EArBA,QACA,0BCvDA,2ICMI,EAAC,CAAE,gBAAF,mBAAE,iHAAyK,ICApF,UAAgB,MAAQ,gBAAC,GAAG,kFAAkF,MAAM,OAAS,SAAS,CAAC,0DAA0D,MAAO,mBAAC,yBAAyB,mBAAC,IAAI,cAAc,kCAAkC,EAAE,wBAAwB,EAAE,SAA8B,wBCN7a,mECAA,0GCAA,oDCAA,0CCAA,uCAA4L,yBCA5L,4DCmBI,sBAAsB,gvBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJE,KALa,EAKN,GAA8B,EAAE,QAInB,CAAG,CAJD,GAIK,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,EAAI,OAC7D,EADsE,GACzC,EAAtB,KAA6B,CACpC,KAAM,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,2CAA2C,CAC3D,aAAa,CAAE,MAAM,mBACrB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BEA,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,GChF9B,gDCAA,gICM+C,MAAQ,cAAC,mCAAmC,qJAAqJ,WAAW,+FAA+F,IAAyB,wBCNnX,uFCM+C,MAAQ,cAAC,0BAA0B,oNAAoN,IAAyB,wBCN/T,kDCAA,0OCM+C,MAAQ,OAAC,yBAAyB,4cAA4c,WAAW,mIAAmI,WAAW,oDAAoD,IAAyB,0BCAptB,MAAQ,OAAC,wBAAwB,qUAAqU,IAAyB,0BCqlB9a,MA/kB2B,KACzB,CA8kBaC,GA9kBPC,EAASC,CAAAA,EAAAA,EA8kBO,SA9kBPA,CAASA,GAClBC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClBC,EAAeC,CAAAA,EAAAA,EAAAA,eAAAA,CAAeA,GAC9BC,EAAWN,EAAOM,QAAQ,CAC1BC,EAAWH,EAAaI,GAAG,CAAC,SAAW,QACvCC,CADgD,CACvCL,EAAaI,GAAG,CAAC,UAC1B,YACJE,CAAU,CAHyE,qBAInFC,CAAoB,CACrB,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,GACX,CAACC,EAAiBC,EAAmB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACjD,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAEnC,CAAC,GACE,CAACG,EAAUC,EAAY,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAClD,CAACK,EAAcC,EAAgB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACO,EAAmBC,EAAqB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrD,CAACS,EAAkBC,EAAoB,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,IAAIW,KACpE,CAACC,EAAaC,EAAe,CAAGb,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzC,CAACc,EAAaC,EAAe,CAAGf,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAOpC,MACJ,CAACgB,EAAaC,EAAe,CAAGjB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzC,CAACkB,EAAkBC,EAAoB,CAAGnB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACoB,EAAcC,EAAgB,CAAGrB,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAC/CsB,YAAY,EACZC,SAAS,EACTC,OAAQ,EACV,GACM,CAACC,EAAiBC,EAAmB,CAAG1B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAInD,CACD2B,MAAM,EACNC,QAAS,GACTC,KAAM,SACR,GAqBMC,EAAcC,CAlBG,KACrB,GAAiB,SAAS,CAAtBvC,EACF,OAAOG,EAAWqC,SAAS,CAI7B,IAAK,IAAMC,KAAgBtC,EAAWuC,OAAO,CAAE,CAC7C,GAAID,EAAaE,UAAU,CAACC,EAAE,GAAK1C,EACjC,MADyC,CAClCuC,EAAaE,UAAU,CAEhC,IAAK,IAAME,KAAWJ,EAAaK,QAAQ,CAAE,GACvCD,EAAQE,IAAI,CAACH,EAAE,GAAK1C,EACtB,MAD8B,CACvB2C,EAAQE,IAGrB,CACA,OAAO,KACT,IAIAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACJxB,GAAec,GAAaW,WAA0B,MAAM,CAAnBtC,GAC3CC,EAAoC,GAAxB0B,EAAYW,SAAS,CAErC,EAAG,CAACzB,EAAac,EAAa3B,EAAS,EAGvCqC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,GAAIxB,GAAeb,UAAqBA,EAAW,GAAK,CAACS,EAAa,CACpE,IAAM8B,EAAQC,YAAY,KACxBvC,EAAYwC,IACV,GAAa,OAATA,GAAiBA,GAAQ,EAE3B,CAF8B,MAC9BC,IACO,EAET,IAAMC,EAAUF,EAAO,EAmDvB,OAhDgB,MAAZE,CAAmB,EAAC1B,EAAaE,UAAU,EAAE,CAE/CD,EAAgBuB,GAAS,EACvB,EADuB,CACpBA,CAAI,CACPtB,YAAY,EACd,GACAI,EAAmB,CACjBC,MAAM,EACNC,QAAS,mCACTC,KAAM,SACR,GACAkB,WAAW,IAAMrB,EAAmBkB,GAAS,EAC3C,EAD2C,CACxCA,CAAI,CACPjB,KAAM,GACR,GAAK,MAES,MAAZmB,CAAmB,EAAC1B,EAAaG,OAAO,EAAE,CAE5CF,EAAgBuB,GAAS,EACvB,EADuB,CACpBA,CAAI,CACPrB,SAAS,EACX,GACAG,EAAmB,CACjBC,MAAM,EACNC,QAAS,kCACTC,KAAM,SACR,GACAkB,WAAW,IAAMrB,EAAmBkB,GAAS,EAC3C,EAD2C,CACxCA,CAAI,CACPjB,MAAM,EACR,GAAK,MAES,KAAZmB,CAAkB,EAAC1B,EAAaI,MAAM,EAAE,CAE1CH,EAAgBuB,GAAS,EACvB,EADuB,CACpBA,CAAI,CACPpB,QAAQ,CACV,IACAE,EAAmB,CACjBC,MAAM,EACNC,QAAS,yCACTC,KAAM,UACR,GACAkB,WAAW,IAAMrB,EAAmBkB,GAAS,EAC3C,EAD2C,CACxCA,CAAI,CACPjB,MAAM,CACR,IAAK,MAEAmB,CACT,EACF,EAAG,KACH,MAAO,IAAME,cAAcN,EAC7B,CACF,EAAG,CAAC1B,EAAab,EAAUS,EAAaQ,EAAa,EAGrDoB,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,GAAIxB,GAAe,CAACJ,EAAa,CAC/B,IAAMqC,EAAqB,IACzBC,EAAEC,cAAc,GAChBD,EAAEE,WAAW,CAAG,GACT,IAGT,OADAC,OAAOC,gBAAgB,CAAC,eAAgBL,GACjC,IAAMI,OAAOE,mBAAmB,CAAC,eAAgBN,EAC1D,CACF,EAAG,CAACjC,EAAaJ,EAAY,EAU7B,IAAM4C,EAAgB,IACpB,GAAI,CAAC1B,GAAaW,UAAW,MAAO,SAEpC,IAAMgB,EAAYC,GADA5B,KAAYW,EACFkB,OADc,SAE1C,GAAiB,GAAY,EAAP,SAClBF,CADqC,EACxB,IAAa,EAAP,QADkC,CAChB,OAE3C,EA2BMG,EAAmB,IACvBlD,CA9ByD,CA8BrCkC,IAClB,IAAMiB,EAAS,IAAIlD,IAAIiC,GAMvB,OALIiB,EAAOC,GAAG,CAACC,GACbF,EAAOG,MAAM,CAACD,GAEdF,CAH6B,CAGtBI,GAAG,CAACF,GAENF,CACT,EACF,EAUMhB,EAAe,KACnBqB,GACF,EAIMA,EAAmBC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,KACnC,GAAI9D,GAAgB,CAACyB,EAAa,OAClCxB,GAAgB,GAChBE,GAAqB,GAGrB,IAAI4D,EAAiB,EACfC,EAEF,CAAC,EACLvC,EAAYwC,SAAS,CAACC,OAAO,CAACC,IAE5B,IAAMC,EADaxE,CAAO,CAACuE,EAASpC,EAAE,CAAC,GACNoC,EAASE,aAAa,CACvDL,CAAO,CAACG,EAASpC,EAAE,CAAC,CAAGqC,EACnBA,GACFL,GAEJ,GACA,EAJiB,EAIXO,EAAQC,KAAKC,KAAK,CAACT,EAAiBtC,EAAYwC,SAAS,CAACQ,MAAM,CAAG,KAGzEC,eAAeC,OAAO,CAAC,CAAC,aAAa,EAAEtF,GAAU,SAAS,CAAEuF,KAAKC,SAAS,CAACjF,IAC3E8E,eAAeC,OAAO,CAAC,CAAC,aAAa,EAAEtF,GAAU,SAAS,CAAEuF,KAAKC,SAAS,CAACb,IAC3EU,eAAeC,OAAO,CAAC,CAAC,WAAW,EAAEtF,GAAU,SAAS,CAAEuF,KAAKC,SAAS,CAACC,MAAMC,IAAI,CAAC3E,KACpFM,EAAe,OACb4D,iBACAP,EACAiB,eAAgBvD,EAAYwC,SAAS,CAACQ,MAAM,SAC5CT,CACF,GAGAiB,GAAmBX,GAGnB,IAAMY,EAAa,CAAC,YAAY,EAAEhG,EAAS,mBAAmB,EAAEC,EAAS,QAAQ,EAAEE,GAAU,QAAQ,OAAO,EAAEiF,EAAM,SAAS,EAAEP,EAAe,OAAO,EAAEtC,EAAYwC,SAAS,CAACQ,MAAM,EAAE,CACrL3F,EAAOqG,IAAI,CAACD,EACd,EAAG,CAAClF,EAAcyB,EAAa7B,EAAQ,EACjCqF,GAAsBX,IAC1B,GAAI,CAAC7C,EAAa,OAClB,IAAM2D,EAAYR,KAAKS,KAAK,CAACT,KAAKC,SAAS,CAACvF,IAG5C,GAAiB,SAAS,CAAtBH,EACFiG,EAAUzD,SAAS,CAAC2D,QAAQ,EAAI,EAChCF,EAAUzD,SAAS,CAAC4D,SAAS,CAAGjB,EAChCc,EAAUzD,SAAS,CAAC6D,QAAQ,CAAGlB,GAASc,EAAUzD,SAAS,CAAC8D,YAAY,CAGpEL,EAAUzD,SAAS,CAAC6D,QAAQ,EAAE,EACM3D,OAAO,CAAC6D,KAAK,CAACC,GAAKA,EAAE1D,QAAQ,CAACyD,KAAK,CAACE,GAAMA,EAAGC,QAAQ,CAACH,KAAK,CAACI,GAAKA,EAAEC,WAAW,GAAKH,EAAG1D,IAAI,CAACsD,QAAQ,GAAKG,EAAE7D,UAAU,CAAC0D,QAAQ,IAEjKJ,EAAUY,WAAW,CAACC,UAAU,EAAG,EACnCb,EAAUY,WAAW,CAACE,cAAc,CAAG,IAAIC,OAAOC,WAAW,GAAGC,KAAK,CAAC,IAAI,CAAC,EAAE,CAC7EjB,EAAUkB,MAAM,CAAG,kBAKvB,IAAK,IAAM1E,KAAgBwD,EAAUvD,OAAO,CAAE,CAC5C,GAAID,EAAaE,UAAU,CAACC,EAAE,GAAK1C,EAAQ,CACzCuC,EAAaE,UAAU,CAACwD,QAAQ,EAAI,EACpC1D,EAAaE,UAAU,CAACyD,SAAS,CAAGjB,EACpC1C,EAAaE,UAAU,CAAC0D,QAAQ,CAAGlB,GAAS1C,EAAaE,UAAU,CAAC2D,YAAY,CAChF,KACF,CACA,IAAK,IAAMzD,KAAWJ,EAAaK,QAAQ,CAAE,GACvCD,EAAQE,IAAI,CAACH,EAAE,GAAK1C,EAAQ,CAC9B2C,EAAQE,IAAI,CAACoD,QAAQ,EAAI,EACzBtD,EAAQE,IAAI,CAACqD,SAAS,CAAGjB,EACzBtC,EAAQE,IAAI,CAACsD,QAAQ,CAAGlB,GAAStC,EAAQE,IAAI,CAACuD,YAAY,CAC1D,KACF,CAEJ,CAEFlG,EAAqB6F,EACvB,EAeMmB,GAAqB,KACzBzH,EAAOqG,IAAI,CAAC,CAAC,YAAY,EAAEjG,EAAAA,CAAU,CACvC,EAIA,GAAI,CAACuC,EACH,MAAO,KADS,GACT,EAAC+E,MAAAA,CAAIC,UAAU,oEAClB,UAACC,EAAAA,EAAIA,CAAAA,CAACD,UAAU,2BACd,WAACE,EAAAA,EAAWA,CAAAA,CAACF,UAAU,4BACrB,UAACG,EAAAA,CAAWA,CAAAA,CAACH,UAAU,wCACvB,UAACI,KAAAA,CAAGJ,UAAU,kDAAyC,0BACvD,UAACK,IAAAA,CAAEL,UAAU,8BAAqB,8CAClC,WAACM,EAAAA,CAAMA,CAAAA,CAACC,QAAST,aACf,UAACU,EAAAA,CAAaA,CAAAA,CAACR,UAAU,iBAAiB,8BAOtD,IAAMS,GAAoBC,CAhIG,KAC3B,IAAMC,EAAW,IAAI9G,IAMrB,OALAmB,GAAawC,UAAUC,QAAQ,CAACC,EAAUkD,UACXC,IAAzB1H,CAAO,CAACuE,EAASpC,EAAE,CAAC,EAA2C,IAAI,CAA7BnC,CAAO,CAACuE,EAASpC,EAAE,CAAC,EAC5DqF,EAASxD,GAAG,CAACyD,EAEjB,GACOD,EACT,IAyHMG,GAAqBL,GAAkBM,IAAI,CAAG/F,EAAYwC,SAAS,CAACQ,MAAM,CAAG,IAC7EgD,GAAYP,GAAkBM,IAAI,CAAG,EAC3C,MAAO,WAAChB,MAAAA,CAAIC,UAAU,0BAA0BiB,wBAAsB,WAAWC,0BAAwB,qBAErG,UAACnB,MAAAA,CAAIC,UAAU,yDACb,UAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,mDACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,WAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUJ,KAAK,KAAKR,QAAST,GAAoBE,UAAU,8BAA8BoB,sBAAoB,SAASF,0BAAwB,qBAC5J,UAACV,EAAAA,CAAaA,CAAAA,CAACR,UAAU,UAAUoB,sBAAoB,gBAAgBF,0BAAwB,aAC/F,UAACG,OAAAA,UAAK,eAER,UAACC,EAAAA,CAAUA,CAAAA,CAACtB,UAAU,oCAAoCoB,sBAAoB,aAAaF,0BAAwB,aACnH,WAACnB,MAAAA,WACC,UAACwB,KAAAA,CAAGvB,UAAU,+CAAuChF,EAAYwG,KAAK,GACtE,UAACnB,IAAAA,CAAEL,UAAU,iCAAyBnH,EAAW4I,IAAI,SAIxDvH,GAA4B,OAAbb,GAAqB,CAACS,GAAe,WAACiG,MAAAA,CAAIC,UAAU,oCAE/DhF,GAAaW,WAAa,WAACoE,MAAAA,CAAIC,UAAU,4DACtC,UAACD,MAAAA,CAAIC,UAAU,sCAA6B,eAG5C,UAACD,MAAAA,CAAIC,UAAU,+CACb,UAACD,MAAAA,CAAIC,UAAW,CAAC,8CAA8C,EAA8B,aAA5BtD,EAAcrD,GAA2B,aAAeqD,cAAcrD,GAA0B,eAAiB,eAAe,CAAEqI,MAAO,CAC9MC,MAAO,GAAGtI,GAAoC,GAAxB2B,EAAYW,GAAZX,MAAqB,CAAI,CAAK,IAAI,CAAC,CAAC,SAM5D,WAAC+E,MAAAA,CAAIC,UAAW,CAAC;;kBAEf,EAAE4B,CAzMG,IACnB,OAAQ/B,GACN,IAAK,WACH,MAAO,uCACT,KAAK,UACH,MAAO,6CACT,SACE,MAAO,0CACX,CACF,GAgM+BnD,EAAcrD,WAAW;kBACxC,EAAEqD,eAAcrD,GAA2B,0BAA4B,GAAG;gBAC5E,CAAC,WACC,UAACwI,EAAAA,CAASA,CAAAA,CAAC7B,UAAU,YACrB,WAACD,MAAAA,CAAIC,UAAU,uCACb,UAACqB,OAAAA,CAAKrB,UAAU,yBAAiB8B,CA/NhC,IACjB,IAAMC,EAAQjE,KAAKkE,KAAK,CAACpF,EAAU,MAC7BqF,EAAOnE,KAAKkE,KAAK,CAACpF,EAAU,KAAO,IACnCsF,EAAOtF,EAAU,UACvB,EAAY,EACH,CADM,EACHmF,EAAMI,QAAQ,GAAGC,QAAQ,CAAC,EAAG,KAAK,CAAC,EAAEH,EAAKE,QAAQ,GAAGC,QAAQ,CAAC,EAAG,KAAK,CAAC,EAAEF,EAAKC,QAAQ,GAAGC,QAAQ,CAAC,EAAG,MAAM,CAEhH,GAAGH,EAAKE,QAAQ,GAAGC,QAAQ,CAAC,EAAG,KAAK,CAAC,EAAEF,EAAKC,QAAQ,GAAGC,QAAQ,CAAC,EAAG,MAAM,CAClF,EAuN8D/I,KAC3C2B,GAAaW,WAAa,UAAC0F,OAAAA,CAAKrB,UAAU,4CACV,aAA5BtD,EAAcrD,GAA2B,gBAA8C,YAA5BqD,EAAcrD,GAA0B,YAAc,qBAM7H,CAACa,GAAeJ,EAAc,WAACiG,MAAAA,CAAIC,UAAU,2BAC1C,WAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUZ,QAAST,aACjC,UAACU,EAAAA,CAAaA,CAAAA,CAACR,UAAU,iBAAiB,uBAG5C,WAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUZ,QAxEjB,CAwE0B8B,IAvE/ChK,EAAOqG,IAAI,CAAC,cACd,YAuEgB,UAAC4D,EAAAA,CAAQA,CAAAA,CAACtC,UAAU,iBAAiB,kBAGhC,YAKjB,WAACD,MAAAA,CAAIC,UAAU,wDAEZ5F,GAAoB,WAAC6F,EAAAA,EAAIA,CAAAA,CAACD,UAAU,8BACjC,UAACuC,EAAAA,EAAUA,CAAAA,UACT,UAACC,EAAAA,EAASA,CAAAA,CAACxC,UAAU,uBAAc,sBAErC,WAACE,EAAAA,EAAWA,CAAAA,CAACF,UAAU,sBACrB,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACqB,OAAAA,CAAKrB,UAAU,6CAAoC,QAEtD,UAACK,IAAAA,CAAEL,UAAU,yBAAgB,6CAE/B,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACqB,OAAAA,CAAKrB,UAAU,6CAAoC,QAEtD,WAACK,IAAAA,CAAEL,UAAU,0BAAgB,uBACNhF,EAAYW,SAAS,CAAG,GAAGX,EAAYW,SAAS,CAAC,MAAM,CAAC,CAAG,iBAAiB,mCAGrG,WAACoE,MAAAA,CAAIC,UAAU,wCACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACqB,OAAAA,CAAKrB,UAAU,6CAAoC,QAEtD,WAACK,IAAAA,CAAEL,UAAU,0BAAgB,8BAA4BhF,EAAYgE,YAAY,CAAC,UAEpF,WAACe,MAAAA,CAAIC,UAAU,wCACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACqB,OAAAA,CAAKrB,UAAU,6CAAoC,QAEtD,WAACK,IAAAA,CAAEL,UAAU,0BAAgB,eAAahF,EAAYwC,SAAS,CAACQ,MAAM,OAExE,WAAC+B,MAAAA,CAAIC,UAAU,wCACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACqB,OAAAA,CAAKrB,UAAU,6CAAoC,QAEtD,WAACK,IAAAA,CAAEL,UAAU,0BAAgB,uBAAqBhF,EAAYyH,WAAW,UAI7E,WAAC1C,MAAAA,CAAIC,UAAU,+DACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAAC0C,CAAiBA,CAAAA,CAAC1C,UAAU,2BAC7B,UAACK,IAAAA,CAAEL,UAAU,sCAA6B,kBAE5C,UAACK,IAAAA,CAAEL,UAAU,uCAA8B,iIAM7C,UAACD,MAAAA,CAAIC,UAAU,uBACb,WAACM,EAAAA,CAAMA,CAAAA,CAACS,KAAK,KAAKI,QAAQ,MAAMZ,QAhRtB,CAgR+BoC,IA/QrDxI,GAAe,GACfE,EAAoB,IAChBW,GAAaW,WAAW,EACU,GAAxBX,EAAYW,SAAS,CAErC,YA2QgB,UAAC2F,EAAAA,CAAUA,CAAAA,CAACtB,UAAU,iBAAiB,yBAQhDlG,GAAeE,GAAe,UAAC+F,MAAAA,CAAIC,UAAU,uCAC1C,WAACC,EAAAA,EAAIA,CAAAA,CAACD,UAAW,CAAC,SAAS,EAAEhG,EAAY6D,KAAK,EAAI7C,EAAYgE,YAAY,CAAG,+BAAiC,4BAA4B,WACxI,WAACuD,EAAAA,EAAUA,CAAAA,CAACvC,UAAU,wBACpB,UAACD,MAAAA,CAAIC,UAAU,oCACZhG,EAAY6D,KAAK,EAAI7C,EAAYgE,YAAY,CAAG,UAAC4D,EAAAA,CAAgBA,CAAAA,CAAC5C,UAAU,6BAAgC,UAACG,EAAAA,CAAWA,CAAAA,CAACH,UAAU,6BAEtI,UAACwC,EAAAA,EAASA,CAAAA,CAACxC,UAAU,oBAClBhG,EAAY6D,KAAK,EAAI7C,EAAYgE,YAAY,CAAG,sBAAwB,8BAG7E,WAACkB,EAAAA,EAAWA,CAAAA,CAACF,UAAU,kCACrB,WAACD,MAAAA,CAAIC,UAAU,6CACZhG,EAAY6D,KAAK,CAAC,OAErB,WAACkC,MAAAA,CAAIC,UAAU,0BACZhG,EAAYsD,cAAc,CAAC,SAAOtD,EAAYuE,cAAc,CAAC,yBAEhE,WAACwB,MAAAA,CAAIC,UAAU,kCAAwB,8BACThF,EAAYgE,YAAY,CAAC,OAGvD,WAACe,MAAAA,CAAIC,UAAU,+CACZhG,EAAY6D,KAAK,CAAG7C,EAAYgE,YAAY,EAAIhE,EAAY6D,QAAQ,CAAG7D,EAAYyH,WAAW,EAAI,WAACnC,EAAAA,CAAMA,CAAAA,CAACC,QAvLlG,CAuL2GsC,IArLlI5J,EAAmB,GACnBG,EAAW,CAAC,GACZQ,EAAoB,IAAIC,KACxBE,GAAe,GACfE,EAAe,MACfT,EAAgB,IAChBE,GAAqB,GACrBS,GAAe,GACXa,GAAaW,WAAW,EACU,GAAxBX,EAAYW,SAAS,CAErC,YA2KoB,UAACmH,EAAAA,CAAaA,CAAAA,CAAC9C,UAAU,iBAAiB,kBAI9C,WAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUZ,QAAST,aACjC,UAACU,EAAAA,CAAaA,CAAAA,CAACR,UAAU,iBAAiB,kCASrD9F,GAAe,CAACJ,GAAe,WAACiG,MAAAA,CAAIC,UAAU,kDAE3C,WAACD,MAAAA,CAAIC,UAAU,oCAEb,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,uDACb,WAACqB,OAAAA,WAAK,QAAMrI,EAAkB,EAAE,SAAOgC,EAAYwC,SAAS,CAACQ,MAAM,IACnE,WAACqD,OAAAA,WAAMvD,KAAKC,KAAK,CAAC+C,IAAoB,kBAExC,UAACiC,EAAAA,CAAQA,CAAAA,CAACC,MAAOlC,GAAoBd,UAAU,WAIjD,UAACiD,EAAAA,EAAQA,CAAAA,CAACvF,SAAU1C,EAAYwC,SAAS,CAACxE,EAAgB,CAAEkK,eAAgBlK,EAAkB,EAAGuF,eAAgBvD,EAAYwC,SAAS,CAACQ,MAAM,CAAEmF,eAAgBhK,CAAO,CAAC6B,EAAYwC,SAAS,CAACxE,EAAgB,CAACsC,EAAE,CAAC,CAAE8H,eApUpM,CAACC,EAAoBC,KAC9ClK,EAAW0C,GAAS,EAClB,EADkB,CACfA,CAAI,CACP,CAACuH,EAAW,CAAEC,EAChB,EACF,EA+TmQC,SAAUhK,IAGjQ,WAACwG,MAAAA,CAAIC,UAAU,8CACb,UAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUZ,QAAS,IAAMtH,EAAmB6C,GAAQgC,KAAK0F,GAAG,CAAC,EAAG1H,EAAO,IAAKyH,SAA8B,IAApBvK,GAAyBO,WAAc,aAI7I,UAACwG,MAAAA,CAAIC,UAAU,uCACb,WAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUJ,KAAK,KAAKR,QAAS,IAAMzD,EAAiB9D,GAAkBuK,SAAUhK,EAAcyG,UAAWrG,EAAiBqD,GAAG,CAAChE,GAAmB,kCAAoC,aACnM,UAACyK,CAAQA,CAAAA,CAACzD,UAAW,CAAC,aAAa,EAAErG,EAAiBqD,GAAG,CAAChE,GAAmB,kBAAoB,IAAI,GAAI,YAK7G,UAACsH,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,MAAMZ,QAAS,IAAMtH,EAAmB6C,GAAQgC,KAAK4F,GAAG,CAAC1I,EAAYwC,SAAS,CAACQ,MAAM,CAAG,EAAGlC,EAAO,IAAKyH,SAAUvK,IAAoBgC,EAAYwC,SAAS,CAACQ,MAAM,CAAG,GAAKzE,WAAc,eAO3M,UAACwG,MAAAA,CAAIC,UAAU,yBACb,UAAC2D,EAAAA,EAAYA,CAAAA,CAACnG,UAAWxC,EAAYwC,SAAS,CAAExE,gBAAiBA,EAAiByH,kBAAmBA,GAAmBmD,iBArVvG,CAqVyHC,GApVpJ5K,EAAmBgE,EACrB,EAmV4KtD,iBAAkBA,EAAkBmK,aAAchH,EAAkBiH,SA3TpN,CA2T8NC,IA1TxPtK,GAAqB,EACvB,EAyT+QsH,UAAWA,GAAWzH,aAAcA,YAM9SE,GAAqB,UAACsG,MAAAA,CAAIC,UAAU,gFACjC,WAACC,EAAAA,EAAIA,CAAAA,CAACD,UAAU,qDACd,UAACuC,EAAAA,EAAUA,CAAAA,UACT,WAACC,EAAAA,EAASA,CAAAA,CAACxC,UAAU,uDACnB,UAAC0C,CAAiBA,CAAAA,CAAC1C,UAAU,YAC7B,UAACqB,OAAAA,UAAK,+BAGV,WAACnB,EAAAA,EAAWA,CAAAA,CAACF,UAAU,sBACrB,UAACK,IAAAA,CAAEL,UAAU,yBAAgB,mFAG7B,WAACD,MAAAA,CAAIC,UAAU,mCACb,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6CAAqCS,GAAkBM,IAAI,GAC1E,UAAChB,MAAAA,CAAIC,UAAU,iCAAwB,gBAEzC,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,4CACZhF,EAAYwC,SAAS,CAACQ,MAAM,CAAGyC,GAAkBM,IAAI,GAExD,UAAChB,MAAAA,CAAIC,UAAU,iCAAwB,gBAG3C,WAACD,MAAAA,CAAIC,UAAU,2BACb,UAACM,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUnB,UAAU,SAASO,QAAS,IAAM7G,GAAqB,YAAQ,UAGzF,UAAC4G,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,MAAMnB,UAAU,SAASO,QAASnD,EAAkBmG,SAAUhK,WAC3EA,EAAe,iBAAmB,4BAQ9CoB,EAAgBE,IAAI,EAAI,UAACkF,MAAAA,CAAIC,UAAU,mEACpC,WAACD,MAAAA,CAAIC,UAAW,CAAC,oEAAoE,EAA2B,aAAzBrF,EAAgBI,IAAI,CAAkB,wCAA0C,8CAA8C,cAAc,CAAC,WAClO,UAACgF,MAAAA,CAAIC,UAAW,CAAC,cAAc,EAA2B,aAAzBrF,EAAgBI,IAAI,CAAkB,gBAAkB,IAAI,UAC3F,UAAC8G,EAAAA,CAASA,CAAAA,CAAC7B,UAAW,CAAC,QAAQ,EAA2B,aAAzBrF,EAAgBI,IAAI,CAAkB,eAAiB,kBAAkB,KAE5G,WAACgF,MAAAA,WACC,UAACM,IAAAA,CAAEL,UAAU,iCAAyBrF,EAAgBG,OAAO,GAC7D,UAACuF,IAAAA,CAAEL,UAAU,mCACe,aAAzBrF,EAAgBI,IAAI,CAAkB,8BAAgC,4DAMvF,0BC1lBA,uDCAA,uDCAA,sDCAA,wDCAA,oDCAA,sECAA,qDCAA,kECAA,wDCAA,iDCAA,uCAA4L,yBCA5L,8LCUM,EAAgB,WAIhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GAIlE,CAAC,EAAkB,EAAkB,CACzC,EAA4C,EALuC,CAe/E,EAAiB,QAXoB,IAWpB,CACrB,CAAC,EAAmC,SAwGV,IAvGxB,GAAM,IAuGqC,aAtGzC,EACA,MAAO,EAAY,KACnB,IAAK,gBACL,EAAgB,EAChB,GAAG,EACL,CAAI,CAEJ,EAAK,GAAW,MAAY,EAAM,CAAC,EAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,GAAmB,GAAG,EAAO,EA+FxC,CA/F4C,EAAJ,QAAc,CAAC,uBA+FpB,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EA4F+I,GA5FnH,EAhCxB,EAgCoB,CAElB,GA0FoJ,EA5FpH,GAE9C,GAAuB,EAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,CA4FU,EA5FW,GAAG,EAAS,EA6F5C,CA7FgD,CA4FR,GA5FI,MAAc,CAAC,KA4FI,oBAC1B,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,EAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,EAAS,GAAS,EAAJ,EAAyB,GAAO,OAEjE,MACE,UAAC,GAAiB,MAAO,QAAiB,MAAc,EACtD,mBAAC,IAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,EAAS,GAAS,EAAJ,KAAY,EACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,EAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAiB,oBAKjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,EAAmB,EAAgB,GACnD,CAF2C,KAGzC,MAFgE,EAEhE,EAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,EAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,EAAiB,EAAkC,GAC1D,OAAgB,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,EAAS,GAA6B,MACrB,UAAjB,OAAO,CAChB,CAEA,SAAS,EAAiB,GAAyB,OAG/C,EAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,EAAmB,EAAY,GAA8B,OAGlE,EAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,EAAkB,YAAc,EAiDhC,IAAM,EAAO,EACP,EAAY,0BCpJlB,qDCAA,4DCAA,wDCAA,gECAA,wDCAA,sDCAA,gDCAA,4DCAA,2DCAA,iDCAA,yDCAA,6ICwBA,EAnBA,CACA,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,MAAe,QAAU,cAAc,EAAK,GAC5C,EAAe,YAAgB,SAC/B,YAAY,QAA6B,EAKzC,MAHA,4BACA,oCAE2B,SAAG,CAJ9B,MAI8B,CAAS,WAAsC,CAC7E,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EACL,gBACA,GAAc,WAAkB,wBAChC,iDCjC+C,MAAQ,cAAC,2BAA2B,sDAAsD,WAAW,kDAAkD,IAAyB,wBCN/N,2ECM+C,MAAQ,cAAC,wBAAwB,yHAAyH,WAAW,yiBAAyiB,IAAyB", "sources": ["webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/cancel_01_icon.js", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/?dfed", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-context/dist/index.mjs", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/defaultAttributes.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/create-hugeicon-component.js", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/?37ac", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/checkmark_circle_01_icon.js", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/rotate_01_icon.js", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/alert_01_icon.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/flag_01_icon.js", "webpack://terang-lms-ui/./src/app/(course-view)/my-courses/[courseId]/exam/page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/?ed98", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/progress.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/clock_01_icon.js", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/home_01_icon.js"], "sourcesContent": ["/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport o from\"../create-hugeicon-component.js\";const e=o(\"Cancel01Icon\",[[\"path\",{d:\"M19 5L5 19M5 5L19 19\",stroke:\"currentColor\",key:\"k0\"}]]);export{e as default};\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module7 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page8 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(course-view)',\n        {\n        children: [\n        'my-courses',\n        {\n        children: [\n        '[courseId]',\n        {\n        children: [\n        'exam',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\layout.tsx\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(course-view)/my-courses/[courseId]/exam/page\",\n        pathname: \"/my-courses/[courseId]/exam\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar e={xmlns:\"http://www.w3.org/2000/svg\",width:24,height:24,viewBox:\"0 0 24 24\",fill:\"none\",strokeWidth:1.5,strokeLinecap:\"round\",strokeLinejoin:\"round\"};export{e as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport{forwardRef as p,createElement as s}from\"react\";import u from\"./defaultAttributes.js\";const y=(a,c)=>{const t=p(({color:i=\"currentColor\",size:e=24,strokeWidth:l=1.5,className:m=\"\",children:r,...n},d)=>{const f={ref:d,...u,width:e,height:e,strokeWidth:l,color:i,className:m,...n};return s(\"svg\",f,c?.map(([h,o])=>s(h,{key:o.id,...o}))??[],...Array.isArray(r)?r:[r])});return t.displayName=`${a}Icon`,t};export{y as default};\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\page.tsx\");\n", "module.exports = require(\"util\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/(course-view)/my-courses/[courseId]/exam',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/(course-view)/my-courses/[courseId]/exam',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/(course-view)/my-courses/[courseId]/exam',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/(course-view)/my-courses/[courseId]/exam',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"CheckmarkCircle01Icon\",[[\"path\",{d:\"M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n", "module.exports = require(\"node:child_process\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport t from\"../create-hugeicon-component.js\";const o=t(\"Rotate01Icon\",[[\"path\",{d:\"M20.0092 2V5.13219C20.0092 5.42605 19.6418 5.55908 19.4537 5.33333C17.6226 3.2875 14.9617 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12\",stroke:\"currentColor\",key:\"k0\"}]]);export{o as default};\n", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Alert01Icon\",[[\"path\",{d:\"M5.32171 9.68293C7.73539 5.41199 8.94222 3.27651 10.5983 2.72681C11.5093 2.4244 12.4907 2.4244 13.4017 2.72681C15.0578 3.27651 16.2646 5.41199 18.6783 9.68293C21.092 13.9539 22.2988 16.0893 21.9368 17.8293C21.7376 18.7866 21.2469 19.6549 20.535 20.3097C19.241 21.5 16.8274 21.5 12 21.5C7.17265 21.5 4.75897 21.5 3.46496 20.3097C2.75308 19.6549 2.26239 18.7866 2.06322 17.8293C1.70119 16.0893 2.90803 13.9539 5.32171 9.68293Z\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M12.2422 17V13C12.2422 12.5286 12.2422 12.2929 12.0957 12.1464C11.9493 12 11.7136 12 11.2422 12\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M11.992 9H12.001\",stroke:\"currentColor\",key:\"k2\"}]]);export{o as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport o from\"../create-hugeicon-component.js\";const r=o(\"Flag01Icon\",[[\"path\",{d:\"M5.0249 21C5.04385 19.2643 5.04366 17.5541 5.0366 15.9209M5.0366 15.9209C5.01301 10.4614 4.91276 5.86186 5.19475 4.04271C5.5611 1.67939 9.39301 3.82993 13.9703 5.59842L16.0328 6.48729C17.5508 7.1415 19.7187 8.30352 18.7662 9.66084C18.3738 10.22 17.56 10.8596 16.0575 11.567L5.0366 15.9209Z\",stroke:\"currentColor\",key:\"k0\"}]]);export{r as default};\n", "'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useRouter, useSearchParams } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Clock01Icon as ClockIcon, Flag01Icon as FlagIcon, Alert01Icon as AlertTriangleIcon, CheckmarkCircle01Icon as CheckCircle2Icon, Cancel01Icon as XCircleIcon, Award01Icon as TrophyIcon, Rotate01Icon as RotateCcwIcon, ArrowLeft01Icon as ArrowLeftIcon, Home01Icon as HomeIcon } from 'hugeicons-react';\nimport { Course, Quiz } from '@/types/lms';\nimport { useEnrollment } from '@/contexts/enrollment-context';\nimport { Question, QuestionBank } from '@/components/lms/final-exam';\nconst ExamPage: React.FC = () => {\n  const params = useParams();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const courseId = params.courseId as string;\n  const examType = searchParams.get('type') || 'final'; // 'final', 'chapter', 'module'\n  const examId = searchParams.get('examId');\n  const {\n    courseData,\n    updateCourseProgress\n  } = useEnrollment();\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [answers, setAnswers] = useState<{\n    [key: string]: any;\n  }>({});\n  const [timeLeft, setTimeLeft] = useState<number | null>(null);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);\n  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());\n  const [showResults, setShowResults] = useState(false);\n  const [examResults, setExamResults] = useState<{\n    score: number;\n    correctAnswers: number;\n    totalQuestions: number;\n    results: {\n      [key: string]: boolean;\n    };\n  } | null>(null);\n  const [examStarted, setExamStarted] = useState(false);\n  const [showConfirmStart, setShowConfirmStart] = useState(true);\n  const [timeWarnings, setTimeWarnings] = useState({\n    fifteenMin: false,\n    fiveMin: false,\n    oneMin: false\n  });\n  const [showTimeWarning, setShowTimeWarning] = useState<{\n    show: boolean;\n    message: string;\n    type: 'warning' | 'critical';\n  }>({\n    show: false,\n    message: '',\n    type: 'warning'\n  });\n\n  // Get the current exam/quiz\n  const getCurrentExam = (): Quiz | null => {\n    if (examType === 'final') {\n      return courseData.finalExam;\n    }\n\n    // Find chapter or module quiz by examId\n    for (const courseModule of courseData.modules) {\n      if (courseModule.moduleQuiz.id === examId) {\n        return courseModule.moduleQuiz;\n      }\n      for (const chapter of courseModule.chapters) {\n        if (chapter.quiz.id === examId) {\n          return chapter.quiz;\n        }\n      }\n    }\n    return null;\n  };\n  const currentExam = getCurrentExam();\n\n  // Initialize timer when exam starts\n  useEffect(() => {\n    if (examStarted && currentExam?.timeLimit && timeLeft === null) {\n      setTimeLeft(currentExam.timeLimit * 60);\n    }\n  }, [examStarted, currentExam, timeLeft]);\n\n  // Timer countdown with warnings\n  useEffect(() => {\n    if (examStarted && timeLeft !== null && timeLeft > 0 && !showResults) {\n      const timer = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev === null || prev <= 1) {\n            handleTimeUp();\n            return 0;\n          }\n          const newTime = prev - 1;\n\n          // Time warnings with notifications\n          if (newTime === 900 && !timeWarnings.fifteenMin) {\n            // 15 minutes\n            setTimeWarnings(prev => ({\n              ...prev,\n              fifteenMin: true\n            }));\n            setShowTimeWarning({\n              show: true,\n              message: 'Peringatan: Sisa waktu 15 menit!',\n              type: 'warning'\n            });\n            setTimeout(() => setShowTimeWarning(prev => ({\n              ...prev,\n              show: false\n            })), 5000);\n          }\n          if (newTime === 300 && !timeWarnings.fiveMin) {\n            // 5 minutes\n            setTimeWarnings(prev => ({\n              ...prev,\n              fiveMin: true\n            }));\n            setShowTimeWarning({\n              show: true,\n              message: 'Peringatan: Sisa waktu 5 menit!',\n              type: 'warning'\n            });\n            setTimeout(() => setShowTimeWarning(prev => ({\n              ...prev,\n              show: false\n            })), 5000);\n          }\n          if (newTime === 60 && !timeWarnings.oneMin) {\n            // 1 minute\n            setTimeWarnings(prev => ({\n              ...prev,\n              oneMin: true\n            }));\n            setShowTimeWarning({\n              show: true,\n              message: 'PERINGATAN KRITIS: Sisa waktu 1 menit!',\n              type: 'critical'\n            });\n            setTimeout(() => setShowTimeWarning(prev => ({\n              ...prev,\n              show: false\n            })), 8000);\n          }\n          return newTime;\n        });\n      }, 1000);\n      return () => clearInterval(timer);\n    }\n  }, [examStarted, timeLeft, showResults, timeWarnings]);\n\n  // Prevent navigation during exam\n  useEffect(() => {\n    if (examStarted && !showResults) {\n      const handleBeforeUnload = (e: BeforeUnloadEvent) => {\n        e.preventDefault();\n        e.returnValue = '';\n        return '';\n      };\n      window.addEventListener('beforeunload', handleBeforeUnload);\n      return () => window.removeEventListener('beforeunload', handleBeforeUnload);\n    }\n  }, [examStarted, showResults]);\n  const formatTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600);\n    const mins = Math.floor(seconds % 3600 / 60);\n    const secs = seconds % 60;\n    if (hours > 0) {\n      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getTimeStatus = (seconds: number) => {\n    if (!currentExam?.timeLimit) return 'normal';\n    const totalTime = currentExam.timeLimit * 60;\n    const remaining = seconds / totalTime;\n    if (remaining <= 0.1) return 'critical'; // Less than 10%\n    if (remaining <= 0.25) return 'warning'; // Less than 25%\n    return 'normal';\n  };\n  const getTimeColor = (status: string) => {\n    switch (status) {\n      case 'critical':\n        return 'border-red-500 text-red-600 bg-red-50';\n      case 'warning':\n        return 'border-amber-500 text-amber-600 bg-amber-50';\n      default:\n        return 'border-blue-500 text-blue-600 bg-blue-50';\n    }\n  };\n  const handleStartExam = () => {\n    setExamStarted(true);\n    setShowConfirmStart(false);\n    if (currentExam?.timeLimit) {\n      setTimeLeft(currentExam.timeLimit * 60);\n    }\n  };\n  const handleAnswerChange = (questionId: string, answer: any) => {\n    setAnswers(prev => ({\n      ...prev,\n      [questionId]: answer\n    }));\n  };\n  const handleQuestionSelect = (questionIndex: number) => {\n    setCurrentQuestion(questionIndex);\n  };\n  const handleToggleFlag = (questionIndex: number) => {\n    setFlaggedQuestions(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(questionIndex)) {\n        newSet.delete(questionIndex);\n      } else {\n        newSet.add(questionIndex);\n      }\n      return newSet;\n    });\n  };\n  const getAnsweredQuestions = () => {\n    const answered = new Set<number>();\n    currentExam?.questions.forEach((question, index) => {\n      if (answers[question.id] !== undefined && answers[question.id] !== '') {\n        answered.add(index);\n      }\n    });\n    return answered;\n  };\n  const handleTimeUp = () => {\n    handleSubmitExam();\n  };\n  const handleSubmitConfirm = () => {\n    setShowConfirmSubmit(true);\n  };\n  const handleSubmitExam = useCallback(() => {\n    if (isSubmitting || !currentExam) return;\n    setIsSubmitting(true);\n    setShowConfirmSubmit(false);\n\n    // Calculate score and results\n    let correctAnswers = 0;\n    const results: {\n      [key: string]: boolean;\n    } = {};\n    currentExam.questions.forEach(question => {\n      const userAnswer = answers[question.id];\n      const isCorrect = userAnswer === question.correctAnswer;\n      results[question.id] = isCorrect;\n      if (isCorrect) {\n        correctAnswers++;\n      }\n    });\n    const score = Math.round(correctAnswers / currentExam.questions.length * 100);\n\n    // Store results in session storage for results page\n    sessionStorage.setItem(`exam_answers_${examId || 'final'}`, JSON.stringify(answers));\n    sessionStorage.setItem(`exam_results_${examId || 'final'}`, JSON.stringify(results));\n    sessionStorage.setItem(`exam_flags_${examId || 'final'}`, JSON.stringify(Array.from(flaggedQuestions)));\n    setExamResults({\n      score,\n      correctAnswers,\n      totalQuestions: currentExam.questions.length,\n      results\n    });\n\n    // Update course progress\n    updateExamProgress(score);\n\n    // Navigate to results page with score data\n    const resultsUrl = `/my-courses/${courseId}/exam/results?type=${examType}&examId=${examId || 'final'}&score=${score}&correct=${correctAnswers}&total=${currentExam.questions.length}`;\n    router.push(resultsUrl);\n  }, [isSubmitting, currentExam, answers]);\n  const updateExamProgress = (score: number) => {\n    if (!currentExam) return;\n    const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;\n\n    // Update the specific exam/quiz\n    if (examType === 'final') {\n      newCourse.finalExam.attempts += 1;\n      newCourse.finalExam.lastScore = score;\n      newCourse.finalExam.isPassed = score >= newCourse.finalExam.minimumScore;\n\n      // Update certificate eligibility if passed\n      if (newCourse.finalExam.isPassed) {\n        const allModulesCompleted = newCourse.modules.every(m => m.chapters.every(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed) && m.moduleQuiz.isPassed);\n        if (allModulesCompleted) {\n          newCourse.certificate.isEligible = true;\n          newCourse.certificate.completionDate = new Date().toISOString().split('T')[0];\n          newCourse.status = 'completed';\n        }\n      }\n    } else {\n      // Find and update chapter or module quiz\n      for (const courseModule of newCourse.modules) {\n        if (courseModule.moduleQuiz.id === examId) {\n          courseModule.moduleQuiz.attempts += 1;\n          courseModule.moduleQuiz.lastScore = score;\n          courseModule.moduleQuiz.isPassed = score >= courseModule.moduleQuiz.minimumScore;\n          break;\n        }\n        for (const chapter of courseModule.chapters) {\n          if (chapter.quiz.id === examId) {\n            chapter.quiz.attempts += 1;\n            chapter.quiz.lastScore = score;\n            chapter.quiz.isPassed = score >= chapter.quiz.minimumScore;\n            break;\n          }\n        }\n      }\n    }\n    updateCourseProgress(newCourse);\n  };\n  const handleRetakeExam = () => {\n    // Reset all exam state\n    setCurrentQuestion(0);\n    setAnswers({});\n    setFlaggedQuestions(new Set());\n    setShowResults(false);\n    setExamResults(null);\n    setIsSubmitting(false);\n    setShowConfirmSubmit(false);\n    setExamStarted(true);\n    if (currentExam?.timeLimit) {\n      setTimeLeft(currentExam.timeLimit * 60);\n    }\n  };\n  const handleBackToCourse = () => {\n    router.push(`/my-courses/${courseId}`);\n  };\n  const handleBackHome = () => {\n    router.push('/my-courses');\n  };\n  if (!currentExam) {\n    return <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\r\n        <Card className=\"w-full max-w-md\">\r\n          <CardContent className=\"p-6 text-center\">\r\n            <XCircleIcon className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\r\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Ujian Tidak Ditemukan</h3>\r\n            <p className=\"text-gray-600 mb-4\">Ujian yang diminta tidak dapat ditemukan.</p>\r\n            <Button onClick={handleBackToCourse}>\r\n              <ArrowLeftIcon className=\"mr-2 h-4 w-4\" />\r\n              Kembali ke Kursus\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n      </div>;\n  }\n  const answeredQuestions = getAnsweredQuestions();\n  const progressPercentage = answeredQuestions.size / currentExam.questions.length * 100;\n  const canSubmit = answeredQuestions.size > 0;\n  return <div className=\"min-h-screen bg-gray-50\" data-sentry-component=\"ExamPage\" data-sentry-source-file=\"page.tsx\">\r\n      {/* Header */}\r\n      <div className=\"bg-white border-b shadow-sm sticky top-0 z-10\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <Button variant=\"outline\" size=\"sm\" onClick={handleBackToCourse} className=\"flex items-center space-x-2\" data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                <ArrowLeftIcon className=\"h-4 w-4\" data-sentry-element=\"ArrowLeftIcon\" data-sentry-source-file=\"page.tsx\" />\r\n                <span>Kembali</span>\r\n              </Button>\r\n              <TrophyIcon className=\"h-6 w-6 text-[var(--iai-primary)]\" data-sentry-element=\"TrophyIcon\" data-sentry-source-file=\"page.tsx\" />\r\n              <div>\r\n                <h1 className=\"text-lg font-semibold text-gray-900\">{currentExam.title}</h1>\r\n                <p className=\"text-sm text-gray-600\">{courseData.name}</p>\r\n              </div>\r\n            </div>\r\n            \r\n            {examStarted && timeLeft !== null && !showResults && <div className=\"flex items-center gap-3\">\r\n                {/* Time Progress Bar */}\r\n                {currentExam?.timeLimit && <div className=\"hidden sm:flex flex-col items-end min-w-[120px]\">\r\n                    <div className=\"text-xs text-gray-500 mb-1\">\r\n                      Sisa Waktu\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                      <div className={`h-2 rounded-full transition-all duration-1000 ${getTimeStatus(timeLeft) === 'critical' ? 'bg-red-500' : getTimeStatus(timeLeft) === 'warning' ? 'bg-amber-500' : 'bg-blue-500'}`} style={{\n                  width: `${timeLeft / (currentExam.timeLimit * 60) * 100}%`\n                }}></div>\r\n                    </div>\r\n                  </div>}\r\n                \r\n                {/* Timer Display */}\r\n                <div className={`\n                  flex items-center gap-2 px-4 py-2 rounded-lg border-2 font-mono text-lg font-bold transition-all duration-300\n                  ${getTimeColor(getTimeStatus(timeLeft))}\n                  ${getTimeStatus(timeLeft) === 'critical' ? 'animate-pulse shadow-lg' : ''}\n                `}>\r\n                  <ClockIcon className=\"h-5 w-5\" />\r\n                  <div className=\"flex flex-col items-center\">\r\n                    <span className=\"leading-tight\">{formatTime(timeLeft)}</span>\r\n                    {currentExam?.timeLimit && <span className=\"text-xs opacity-75 leading-tight\">\r\n                        {getTimeStatus(timeLeft) === 'critical' ? 'SEGERA HABIS!' : getTimeStatus(timeLeft) === 'warning' ? 'Perhatian' : 'Tersisa'}\r\n                      </span>}\r\n                  </div>\r\n                </div>\r\n              </div>}\r\n\r\n            {!examStarted || showResults ? <div className=\"flex space-x-2\">\r\n                <Button variant=\"outline\" onClick={handleBackToCourse}>\r\n                  <ArrowLeftIcon className=\"mr-2 h-4 w-4\" />\r\n                  Kembali ke Kursus\r\n                </Button>\r\n                <Button variant=\"outline\" onClick={handleBackHome}>\r\n                  <HomeIcon className=\"mr-2 h-4 w-4\" />\r\n                  Dashboard\r\n                </Button>\r\n              </div> : null}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n        {/* Pre-exam Instructions */}\r\n        {showConfirmStart && <Card className=\"max-w-2xl mx-auto\">\r\n            <CardHeader>\r\n              <CardTitle className=\"text-center\">Instruksi Ujian</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-6\">\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">1</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Pastikan koneksi internet Anda stabil</p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">2</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">\r\n                    Anda memiliki waktu {currentExam.timeLimit ? `${currentExam.timeLimit} menit` : 'tidak terbatas'} untuk menyelesaikan ujian\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">3</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Nilai minimum untuk lulus: {currentExam.minimumScore}%</p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">4</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Total soal: {currentExam.questions.length}</p>\r\n                </div>\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-sm font-medium text-blue-600\">5</span>\r\n                  </div>\r\n                  <p className=\"text-gray-700\">Maksimal percobaan: {currentExam.maxAttempts}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <AlertTriangleIcon className=\"h-5 w-5 text-amber-600\" />\r\n                  <p className=\"font-medium text-amber-800\">Peringatan</p>\r\n                </div>\r\n                <p className=\"text-amber-700 text-sm mt-2\">\r\n                  Setelah ujian dimulai, jangan menutup browser atau meninggalkan halaman. \r\n                  Ujian akan otomatis diserahkan jika waktu habis.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"text-center\">\r\n                <Button size=\"lg\" variant=\"iai\" onClick={handleStartExam}>\r\n                  <TrophyIcon className=\"mr-2 h-5 w-5\" />\r\n                  Mulai Ujian\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>}\r\n\r\n        {/* Results Screen */}\r\n        {showResults && examResults && <div className=\"max-w-2xl mx-auto space-y-6\">\r\n            <Card className={`border-2 ${examResults.score >= currentExam.minimumScore ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>\r\n              <CardHeader className=\"text-center\">\r\n                <div className=\"flex justify-center mb-4\">\r\n                  {examResults.score >= currentExam.minimumScore ? <CheckCircle2Icon className=\"h-16 w-16 text-green-600\" /> : <XCircleIcon className=\"h-16 w-16 text-red-600\" />}\r\n                </div>\r\n                <CardTitle className=\"text-2xl\">\r\n                  {examResults.score >= currentExam.minimumScore ? 'Selamat! Anda Lulus' : 'Maaf, Anda Belum Lulus'}\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"text-center space-y-4\">\r\n                <div className=\"text-4xl font-bold text-gray-900\">\r\n                  {examResults.score}%\r\n                </div>\r\n                <div className=\"text-gray-600\">\r\n                  {examResults.correctAnswers} dari {examResults.totalQuestions} soal dijawab benar\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">\r\n                  Nilai minimum untuk lulus: {currentExam.minimumScore}%\r\n                </div>\r\n                \r\n                <div className=\"flex justify-center space-x-4 mt-6\">\r\n                  {examResults.score < currentExam.minimumScore && currentExam.attempts < currentExam.maxAttempts && <Button onClick={handleRetakeExam}>\r\n                      <RotateCcwIcon className=\"mr-2 h-4 w-4\" />\r\n                      Ulangi Ujian\r\n                    </Button>}\r\n                  \r\n                  <Button variant=\"outline\" onClick={handleBackToCourse}>\r\n                    <ArrowLeftIcon className=\"mr-2 h-4 w-4\" />\r\n                    Kembali ke Kursus\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>}\r\n\r\n        {/* Exam Interface */}\r\n        {examStarted && !showResults && <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-8\">\r\n            {/* Question Area */}\r\n            <div className=\"xl:col-span-3 space-y-6\">\r\n              {/* Progress */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between text-sm text-gray-600\">\r\n                  <span>Soal {currentQuestion + 1} dari {currentExam.questions.length}</span>\r\n                  <span>{Math.round(progressPercentage)}% Selesai</span>\r\n                </div>\r\n                <Progress value={progressPercentage} className=\"h-3\" />\r\n              </div>\r\n\r\n              {/* Current Question */}\r\n              <Question question={currentExam.questions[currentQuestion]} questionNumber={currentQuestion + 1} totalQuestions={currentExam.questions.length} selectedAnswer={answers[currentExam.questions[currentQuestion].id]} onAnswerChange={handleAnswerChange} disabled={isSubmitting} />\r\n\r\n              {/* Navigation */}\r\n              <div className=\"flex justify-between items-center\">\r\n                <Button variant=\"outline\" onClick={() => setCurrentQuestion(prev => Math.max(0, prev - 1))} disabled={currentQuestion === 0 || isSubmitting}>\r\n                  Previous\r\n                </Button>\r\n\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <Button variant=\"outline\" size=\"sm\" onClick={() => handleToggleFlag(currentQuestion)} disabled={isSubmitting} className={flaggedQuestions.has(currentQuestion) ? 'bg-yellow-100 border-yellow-400' : ''}>\r\n                    <FlagIcon className={`h-4 w-4 mr-2 ${flaggedQuestions.has(currentQuestion) ? 'text-yellow-600' : ''}`} />\r\n                    Flag\r\n                  </Button>\r\n                </div>\r\n\r\n                <Button variant=\"iai\" onClick={() => setCurrentQuestion(prev => Math.min(currentExam.questions.length - 1, prev + 1))} disabled={currentQuestion === currentExam.questions.length - 1 || isSubmitting}>\r\n                  Next\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Question Bank Sidebar */}\r\n            <div className=\"xl:col-span-1\">\r\n              <QuestionBank questions={currentExam.questions} currentQuestion={currentQuestion} answeredQuestions={answeredQuestions} onQuestionSelect={handleQuestionSelect} flaggedQuestions={flaggedQuestions} onToggleFlag={handleToggleFlag} onSubmit={handleSubmitConfirm} canSubmit={canSubmit} isSubmitting={isSubmitting} />\r\n            </div>\r\n          </div>}\r\n      </div>\r\n\r\n      {/* Submit Confirmation Dialog - Popup with Blurred Background */}\r\n      {showConfirmSubmit && <div className=\"fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50\">\r\n          <Card className=\"w-full max-w-md mx-4 shadow-2xl border-2\">\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center space-x-2 text-amber-700\">\r\n                <AlertTriangleIcon className=\"h-5 w-5\" />\r\n                <span>Konfirmasi Penyerahan</span>\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <p className=\"text-gray-600\">\r\n                Apakah Anda yakin ingin menyerahkan ujian? Pastikan semua jawaban sudah benar.\r\n              </p>\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-green-600\">{answeredQuestions.size}</div>\r\n                  <div className=\"text-xs text-gray-500\">Terjawab</div>\r\n                </div>\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-2xl font-bold text-gray-400\">\r\n                    {currentExam.questions.length - answeredQuestions.size}\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-500\">Belum</div>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex space-x-2\">\r\n                <Button variant=\"outline\" className=\"flex-1\" onClick={() => setShowConfirmSubmit(false)}>\r\n                  Batal\r\n                </Button>\r\n                <Button variant=\"iai\" className=\"flex-1\" onClick={handleSubmitExam} disabled={isSubmitting}>\r\n                  {isSubmitting ? 'Menyerahkan...' : 'Ya, Serahkan'}\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>}\r\n\r\n      {/* Time Warning Toast */}\r\n      {showTimeWarning.show && <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2\">\r\n          <div className={`flex items-center space-x-3 rounded-lg px-6 py-4 shadow-lg border-2 ${showTimeWarning.type === 'critical' ? 'bg-red-50 text-red-800 border-red-200' : 'bg-amber-50 text-amber-800 border-amber-200'} min-w-[320px]`}>\r\n            <div className={`flex-shrink-0 ${showTimeWarning.type === 'critical' ? 'animate-pulse' : ''}`}>\r\n              <ClockIcon className={`h-6 w-6 ${showTimeWarning.type === 'critical' ? 'text-red-600' : 'text-amber-600'}`} />\r\n            </div>\r\n            <div>\r\n              <p className=\"font-semibold text-sm\">{showTimeWarning.message}</p>\r\n              <p className=\"text-xs opacity-75 mt-1\">\r\n                {showTimeWarning.type === 'critical' ? 'Segera serahkan ujian Anda!' : 'Pastikan untuk menyerahkan ujian tepat waktu.'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>}\r\n    </div>;\n};\nexport default ExamPage;", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\page.tsx\");\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Clock01Icon\",[[\"circle\",{cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M12 8V12L14 14\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n", "module.exports = require(\"events\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport C from\"../create-hugeicon-component.js\";const o=C(\"Home01Icon\",[[\"path\",{d:\"M15.0002 17C14.2007 17.6224 13.1505 18 12.0002 18C10.85 18 9.79977 17.6224 9.00024 17\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M2.35164 13.2135C1.99862 10.9162 1.82211 9.76763 2.25641 8.74938C2.69071 7.73112 3.65427 7.03443 5.58138 5.64106L7.02123 4.6C9.41853 2.86667 10.6172 2 12.0002 2C13.3833 2 14.582 2.86667 16.9793 4.6L18.4191 5.64106C20.3462 7.03443 21.3098 7.73112 21.7441 8.74938C22.1784 9.76763 22.0019 10.9162 21.6489 13.2135L21.3478 15.1724C20.8474 18.4289 20.5972 20.0572 19.4292 21.0286C18.2613 22 16.5539 22 13.1391 22H10.8614C7.44658 22 5.73915 22 4.57124 21.0286C3.40333 20.0572 3.15311 18.4289 2.65267 15.1724L2.35164 13.2135Z\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n"], "names": ["serverComponentModule.default", "ExamPage", "params", "useParams", "router", "useRouter", "searchParams", "useSearchParams", "courseId", "examType", "get", "examId", "courseData", "updateCourseProgress", "useEnrollment", "currentQuestion", "setCurrentQuestion", "useState", "answers", "setAnswers", "timeLeft", "setTimeLeft", "isSubmitting", "setIsSubmitting", "showConfirmSubmit", "setShowConfirmSubmit", "flaggedQuestions", "setFlaggedQuestions", "Set", "showResults", "setShowResults", "examResults", "setExamResults", "examStarted", "setExamStarted", "showConfirmStart", "setShowConfirmStart", "timeWarnings", "setTimeWarnings", "fifteenMin", "fiveMin", "oneMin", "showTimeWarning", "setShowTimeWarning", "show", "message", "type", "currentExam", "getCurrentExam", "finalExam", "courseModule", "modules", "moduleQuiz", "id", "chapter", "chapters", "quiz", "useEffect", "timeLimit", "timer", "setInterval", "prev", "handleTimeUp", "newTime", "setTimeout", "clearInterval", "handleBeforeUnload", "e", "preventDefault", "returnValue", "window", "addEventListener", "removeEventListener", "getTimeStatus", "remaining", "seconds", "totalTime", "handleToggleFlag", "newSet", "has", "questionIndex", "delete", "add", "handleSubmitExam", "useCallback", "correctAnswers", "results", "questions", "for<PERSON>ach", "question", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "score", "Math", "round", "length", "sessionStorage", "setItem", "JSON", "stringify", "Array", "from", "totalQuestions", "updateExamProgress", "resultsUrl", "push", "newCourse", "parse", "attempts", "lastScore", "isPassed", "minimumScore", "every", "m", "ch", "contents", "c", "isCompleted", "certificate", "isEligible", "completionDate", "Date", "toISOString", "split", "status", "handleBackToCourse", "div", "className", "Card", "<PERSON><PERSON><PERSON><PERSON>", "XCircleIcon", "h3", "p", "<PERSON><PERSON>", "onClick", "ArrowLeftIcon", "answeredQuestions", "getAnsweredQuestions", "answered", "index", "undefined", "progressPercentage", "size", "canSubmit", "data-sentry-component", "data-sentry-source-file", "variant", "data-sentry-element", "span", "TrophyIcon", "h1", "title", "name", "style", "width", "getTimeColor", "ClockIcon", "formatTime", "hours", "floor", "mins", "secs", "toString", "padStart", "handleBackHome", "HomeIcon", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "maxAttempts", "AlertTriangleIcon", "handleStartExam", "CheckCircle2Icon", "handleRetakeExam", "RotateCcwIcon", "Progress", "value", "Question", "questionNumber", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "questionId", "answer", "disabled", "max", "FlagIcon", "min", "QuestionBank", "onQuestionSelect", "handleQuestionSelect", "onToggleFlag", "onSubmit", "handleSubmitConfirm"], "sourceRoot": ""}