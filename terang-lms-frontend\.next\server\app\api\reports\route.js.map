{"version": 3, "file": "../app/api/reports/route.js", "mappings": "qbAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,kcEiBO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IAAMC,EAAeD,EAAQE,KAARF,EAAe,CAA9BC,YAA2C,CAC3CE,EAAOF,EAAPE,GAAuB,CAAC,MAAjBF,EACPG,EAAYH,EAAaI,GAAG,CAAC,CAA7BD,KAAYH,OACZK,EAAWL,EAAaI,GAAG,CAAC,MAAjBJ,MACXM,EAAUN,EAAaI,GAAvBE,CAA2B,MAAjBN,KACVO,EAAYP,EAAaI,GAAG,CAAC,CAA7BG,KAAYP,OAElB,GAAI,CAACG,EACH,OADGA,EAAW,YACPK,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAG3E,GAAa,aAATT,EAAqB,CAOvB,IAAMU,EAAYC,CALK,MAAMC,EAAAA,EAAAA,CAC1BC,EAIeF,IAJT,CAAC,CAAEG,EAAAA,CAAIC,EAAAA,OAAOA,CAACD,EAAAA,CAAG,EACxBE,IAAI,CAACD,EAAAA,OAAAA,CAAAA,CACLE,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,IAAAA,CAAAA,CAEPmB,GAAG,CAACC,CAAAA,EAAKA,EAAEP,EAAE,EAE9C,GAAIJ,GAAwB,GAAdY,GAAVZ,GAAgB,CAClB,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBgB,YAAAA,CAAc,EACdC,aAAAA,CAAe,EACfC,YAAAA,CAAc,EACdC,eAAAA,CAAiB,EACjBC,cAAAA,CAAgB,KAKpB,IAAMJ,EAAeb,EAAUY,MAGA,EAHzBC,KAG+BX,EAAAA,EAAAA,CAClCC,MAAM,CAAC,CAAEe,KAAAA,CDlDT,MCkDgBA,CDlDhB,CAAG,SAAS,EAAc,EAAG,UAAU,kBCkDf,GACxBZ,IAAI,CAACa,EAAAA,kBAAAA,CAAAA,CACLZ,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAEO,CAAS,CAAC,EAAE,GAGrD,IAAMoB,EAAiB,MAAMlB,EAAAA,EAAAA,CAC1BmB,CADGD,aACW,CAAC,CAAEzB,SAAAA,CAAWwB,EAAAA,kBAAkBA,CAACxB,SAAAA,GAC/CW,IAAI,CAACa,EAAAA,kBAAAA,CAAAA,CACLZ,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAEO,CAAS,CAAC,EAAE,GAG/CsB,EAAkB,MAAMpB,EAAAA,EAAAA,CAC3BmB,EADGC,YACW,CAAC,CAAE5B,OAAAA,CAAS6B,EAAAA,iBAAiBA,CAAC7B,OAAAA,GAC5CY,IAAI,CAACiB,EAAAA,iBAAAA,CAAAA,CACLhB,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,iBAAAA,CAAkB9B,QAAQ,CAAEO,CAAS,CAAC,EAAE,GAG9CwB,EAAiB,MAAMtB,EAAAA,EAAAA,CAC1BC,CADGqB,KACG,CAAC,CACNC,WAAAA,CAAaN,EAAAA,kBAAkBA,CAACM,WAAAA,GAEjCnB,IAAI,CAACa,EAAAA,kBAAAA,CAAAA,CACLZ,KAAK,CAACmB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAAA,GAAM1B,EAAUU,GAAG,CAACN,EAAAA,CAAMI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAEW,EAAAA,CAAAA,CAAAA,CAAAA,CAG7Da,EAAiB,MAAMf,EAAAA,EAAAA,CAC1BC,CADGc,KACG,CAAC,CACNb,EAAAA,CAAIuB,EAAAA,YAAYA,CAACvB,EAAE,CACnBwB,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBC,UAAAA,CAAY1B,EAAAA,OAAOA,CAACyB,IAAI,CACxBE,KAAAA,CAAOL,EAAAA,YAAYA,CAACK,KAAK,CACzBC,WAAAA,CAAaN,EAAAA,YAAYA,CAACM,WAAW,CACrCR,WAAAA,CAAaE,EAAAA,YAAYA,CAACF,WAAAA,GAE3BnB,IAAI,CAACqB,EAAAA,YAAAA,CAAAA,CACLO,QAAQ,CAACL,EAAAA,KAAAA,CAAOrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGmB,EAAAA,YAAAA,CAAahC,SAAS,CAAEkC,EAAAA,KAAAA,CAAMzB,EAAE,GACnD8B,QAAQ,CAACC,EAAAA,OAAAA,CAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGmB,EAAAA,YAAAA,CAAaS,MAAM,CAAED,EAAAA,OAAAA,CAAQ/B,EAAE,GACpD8B,QAAQ,CAACG,EAAAA,QAAAA,CAAU7B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG2B,EAAAA,OAAAA,CAAQG,SAAS,CAAED,EAAAA,QAAAA,CAASjC,EAAE,GACpD8B,QAAQ,CAACK,EAAAA,OAAAA,CAAS/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG6B,EAAAA,QAAAA,CAASG,QAAQ,CAAED,EAAAA,OAAOA,CAACnC,EAAE,GAClD8B,QAAQ,CAAC7B,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG+B,EAAAA,OAAAA,CAAQ9C,QAAQ,CAAEY,EAAAA,OAAAA,CAAQD,EAAE,GACjDG,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,KACrCkD,IADqClD,CAAAA,CAAAA,CAAAA,CAC7BmD,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAKf,EAAAA,YAAAA,CAAaF,WAAW,GACrCkB,KAAK,CAAC,IAEHC,EAAuBpB,EAAeqB,MAAM,CAACC,GAAuB,EAA7CtB,IAAvBoB,CAAkDE,CAAAA,CAAErB,WAAW,EAAWb,MAAM,CAChFI,EAAkBQ,EAAeZ,MAAM,CAAG,EAAI,EAAwBY,CAApDA,CAAmEZ,MAAM,CAAI,IAAM,CAA/BY,CAE5E,GAFqDoB,IAE9ChD,EAAAA,YAAAA,CAAaC,IAAI,CAAC,cACvBgB,EACAC,UADAD,GACAC,CAAeM,EAAeR,MAAM,CACpCG,YAAAA,CAAcO,EAAgBV,MAAM,CACpCI,eAAAA,kBACAC,CACF,EACF,CAAO,GAAa,WAAT3B,GAAqBG,CAArBH,CAA+B,CAExC,IAAMyD,CAFwBtD,CAEf,IAATsD,EAAe7C,EAAAA,EAAAA,CAClBC,MAAM,GACNG,IAAI,CAACD,EAAAA,OAAAA,CAAAA,CACLE,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAOA,CAACD,EAAE,CAAEK,QAAAA,CAAShB,IACxBe,CAAAA,EAAAA,CADwBf,CACxBe,CADwBf,CACxBe,CAAAA,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,MAGlCoD,GAHkCpD,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAsB,GAAG,CAArBwD,EAAOnC,IAAPmC,EAAa,CACf,OAAOnD,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oCAAoC,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzF,IAAMkD,EAAmB,MAAM/C,EAAAA,EAAAA,CAC5BC,GADG8C,GACG,CAAC,CACN7C,EAAAA,CAAIe,EAAAA,kBAAkBA,CAACf,EAAE,CACzBT,SAAAA,CAAWwB,EAAAA,kBAAkBA,CAACxB,SAAS,CACvCiC,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBoB,YAAAA,CAAcrB,EAAAA,KAAKA,CAACsB,KAAK,CACzBC,UAAAA,CAAYjC,EAAAA,kBAAkBA,CAACiC,UAAU,CACzC3B,WAAAA,CAAaN,EAAAA,kBAAkBA,CAACM,WAAW,CAC3C4B,UAAAA,CAAYlC,EAAAA,kBAAkBA,CAACkC,UAAAA,GAEhC/C,IAAI,CAACa,EAAAA,kBAAAA,CAAAA,CACLe,QAAQ,CAACL,EAAAA,KAAAA,CAAOrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACW,EAAAA,kBAAAA,CAAmBxB,SAAS,CAAEkC,EAAAA,KAAAA,CAAMzB,EAAE,GACzDG,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAEgB,QAAAA,CAAShB,KAG5C6D,EAAkB,CAH0B7D,CAAAA,CAAAA,CAAAA,EAGpBS,EAAAA,EAAAA,CAC3BC,EADGmD,IACG,CAAC,CACN3D,SAAAA,CAAWgC,EAAAA,YAAYA,CAAChC,SAAS,CACjCiC,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBM,MAAAA,CAAQT,EAAAA,YAAYA,CAACS,MAAM,CAC3BJ,KAAAA,CAAOL,EAAAA,YAAYA,CAACK,KAAK,CACzBC,WAAAA,CAAaN,EAAAA,YAAYA,CAACM,WAAW,CACrCR,WAAAA,CAAaE,EAAAA,YAAYA,CAACF,WAAW,CACrC8B,WAAAA,CAAalB,EAAAA,QAAQA,CAACP,IAAAA,CACxB,EACCxB,IAAI,CAACqB,EAAAA,YAAAA,CAAAA,CACLO,QAAQ,CAACL,EAAAA,KAAKA,CAAErB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGmB,EAAAA,YAAAA,CAAahC,SAAS,CAAEkC,EAAAA,KAAAA,CAAMzB,EAAE,GACnD8B,QAAQ,CAACC,EAAAA,OAAAA,CAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGmB,EAAAA,YAAAA,CAAaS,MAAM,CAAED,EAAAA,OAAAA,CAAQ/B,EAAE,GACpD8B,QAAQ,CAACG,EAAAA,QAAAA,CAAU7B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG2B,EAAAA,OAAOA,CAACG,SAAS,CAAED,EAAAA,QAAAA,CAASjC,EAAE,GACpD8B,QAAQ,CAACK,EAAAA,OAAAA,CAAS/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG6B,EAAAA,QAAAA,CAASG,QAAQ,CAAED,EAAAA,OAAAA,CAAQnC,EAAE,GAClDG,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG+B,EAAAA,OAAAA,CAAQ9C,QAAQ,CAAEgB,QAAAA,CAAShB,KACpCgD,GADoChD,CAAAA,CAAAA,CAAAA,CAC7B,CAACiD,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAKf,EAAAA,YAAAA,CAAaF,WAAW,GAGlCX,EAAgBmC,EAAiBrC,MAAM,CACvC4C,EADA1C,EACqC+B,MAAM,CAACY,GAAmC,GAA/ED,CAAoBP,GAAmCQ,EAAQhC,KAARgC,MAAmB,EAAW7C,MAAM,CAIjG,OAAOhB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBkD,MAAAA,CAAQA,CAAM,CAAC,EAAE,CACjBjC,aAAAA,GACAE,eAAAA,CANsBF,EAAgB,EAAI,EAAqBA,EAAiB,IAAM,CAAhEA,CAOtB4C,OAP2CF,OAO3CE,CANqB5C,EAAgB,EAAI,EAAqBA,EAAiB,IAAM,CAAhEA,QAAqB0C,UAO1CP,gBAAAA,EACAK,CACF,EACF,CAAO,GAAa,UAAThE,GAAoBI,CAApBJ,CAA6B,CAEtC,IAAMqE,EAAY,MAAMzD,EAAAA,EAAAA,CACrBC,MAAM,GACNG,IAAI,CAACsD,EAAAA,OAAAA,CAAAA,CACLrD,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACoD,EAAAA,OAAAA,CAAQxD,EAAE,CAAEK,QAAAA,CAASf,KAC9BiD,EAD8BjD,CAAAA,CAAAA,CAAAA,CACxB,GAET,GAAyB,GAAG,CAAxBiE,EAAU/C,MAAM,CAAhB+C,OACK/D,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIvE,IAAM8D,EAAgB,MAAM3D,EAAAA,EAAAA,CAAtB2D,MACG,CAAC,CACNzD,EAAAA,CAAIyB,EAAAA,KAAKA,CAACzB,EAAE,CACZ0B,IAAAA,CAAMD,EAAAA,KAAKA,CAACC,IAAI,CAChBqB,KAAAA,CAAOtB,EAAAA,KAAKA,CAACsB,KAAAA,GAEd7C,IAAI,CAACuB,EAAAA,KAAAA,CAAAA,CACLiC,SAAS,CAAC3C,EAAAA,kBAAAA,CAAoBX,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGqB,EAAAA,KAAAA,CAAMzB,EAAE,CAAEe,EAAAA,kBAAAA,CAAmBxB,SAAS,GACvEmE,SAAS,CAACvC,EAAAA,iBAAAA,CAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAE8B,EAAAA,iBAAAA,CAAkB9B,QAAQ,GACvFc,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,iBAAAA,CAAkB7B,OAAO,CAAEe,QAAAA,CAASf,IACvCc,CAAAA,EAAAA,CADuCd,CACvCc,EAAAA,CAAAA,CAAGqB,EAAAA,KAAAA,CAAMkC,IAAI,CAAE,aAKfC,EAAkB,MAAM9D,EAAAA,EAAAA,CAC3BC,EADG6D,IACG,CAAC,CACNvE,QAAAA,CAAU8B,EAAAA,iBAAiBA,CAAC9B,QAAQ,CACpCsC,UAAAA,CAAY1B,EAAAA,OAAOA,CAACyB,IAAI,CACxBmC,UAAAA,CAAY5D,EAAAA,OAAOA,CAAC4D,UAAU,CAC9Bb,UAAAA,CAAY7B,EAAAA,iBAAiBA,CAAC6B,UAAAA,CAChC,EACC9C,IAAI,CAACiB,EAAAA,iBAAAA,CAAAA,CACLW,QAAQ,CAAC7B,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,iBAAAA,CAAkB9B,QAAQ,CAAEY,EAAAA,OAAAA,CAAQD,EAAE,GAC3DG,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,iBAAAA,CAAkB7B,OAAO,CAAEe,QAAAA,CAASf,IACvCc,CAAAA,EADuCd,CAAAA,CACvCc,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,MAK/B2E,EAAkB,CALa3E,CAAAA,CAAAA,CAAAA,CAAAA,CAKPW,EAAAA,EAAAA,CAC3BC,EADG+D,IACG,CAAC,CACNvE,SAAAA,CAAWwB,EAAAA,kBAAkBA,CAACxB,SAAS,CACvCiC,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBrC,QAAAA,CAAU0B,EAAAA,kBAAkBA,CAAC1B,QAAQ,CACrCsC,UAAAA,CAAY1B,EAAAA,OAAOA,CAACyB,IAAI,CACxBsB,UAAAA,CAAYjC,EAAAA,kBAAkBA,CAACiC,UAAU,CACzC3B,WAAAA,CAAaN,EAAAA,kBAAkBA,CAACM,WAAW,CAC3C4B,UAAAA,CAAYlC,EAAAA,kBAAkBA,CAACkC,UAAAA,CACjC,EACC/C,IAAI,CAACa,EAAAA,kBAAAA,CAAAA,CACLe,QAAQ,CAACL,EAAAA,KAAAA,CAAOrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAkBA,CAACxB,SAAS,CAAEkC,EAAAA,KAAKA,CAACzB,EAAE,GACzD8B,QAAQ,CAAC7B,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAEY,EAAAA,OAAAA,CAAQD,EAAE,GAC5D0D,SAAS,CAACvC,EAAAA,iBAAAA,CAAmBf,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAE8B,EAAAA,iBAAAA,CAAkB9B,QAAQ,GACvFc,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDxC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGe,EAAAA,iBAAAA,CAAkB7B,OAAO,CAAEe,QAAAA,CAASf,IACvCc,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,MAIrC,GAJqCA,CAAAA,CAAAA,CAAAA,CAAAA,EAI9BK,YAAAA,CAAaC,IAAI,CAAC,CACvBsE,KAAAA,CAAOR,CAAS,CAAC,EAAE,CACnB7C,aAAAA,CAAe+C,EAAcjD,MAAM,CACnCwD,QAAAA,CAAUP,aAAAA,KACVG,EACAE,aADAF,IAEF,EACF,KAA4C,CAArC,GAAa,YAAT1E,IAAAA,EAsFT,OAtF+BK,EAsFxBC,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,4CAA4C,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GApF/F,IAAM0D,EAAU,KAAVA,CAAgBvD,EAAAA,EAAAA,CACnBC,MAAM,GACNG,IAAI,CAACuB,EAAAA,KAAAA,CAAAA,CACLtB,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACqB,EAAAA,KAAAA,CAAMzB,EAAE,CAAEK,QAAAA,CAASd,IACtBa,CAAAA,EAAAA,EADsBb,CAAAA,CAAAA,CACtBa,CAAGqB,EAAAA,KAAKA,CAACkC,IAAI,CAAE,aAGlBpB,KAAK,CAAC,GAET,GAAuB,GAAG,CAAtBc,EAAQ7C,KAAR6C,CAAc,CAChB,OAAO7D,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,oBAAoB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIzE,IAAMsE,EAAiB,MAAMnE,EAAAA,EAAAA,CAC1BC,CADGkE,KACG,CAAC,CACNC,YAAAA,CAAcnD,EAAAA,kBAAkBA,CAACf,EAAE,CACnCX,QAAAA,CAAU0B,EAAAA,kBAAkBA,CAAC1B,QAAQ,CACrCsC,UAAAA,CAAY1B,EAAAA,OAAOA,CAACyB,IAAI,CACxBmC,UAAAA,CAAY5D,EAAAA,OAAOA,CAAC4D,UAAU,CAC9Bb,UAAAA,CAAYjC,EAAAA,kBAAkBA,CAACiC,UAAU,CACzC3B,WAAAA,CAAaN,EAAAA,kBAAkBA,CAACM,WAAW,CAC3C4B,UAAAA,CAAYlC,EAAAA,kBAAkBA,CAACkC,UAAAA,CACjC,EACC/C,IAAI,CAACa,EAAAA,kBAAAA,CAAAA,CACLe,QAAQ,CAAC7B,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAAA,CAAmB1B,QAAQ,CAAEY,EAAAA,OAAAA,CAAQD,EAAE,GAC5DG,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGW,EAAAA,kBAAAA,CAAmBxB,SAAS,CAAEc,QAAAA,CAASd,IAC1Ca,CAAAA,EAAAA,EAD0Cb,CAAAA,CAAAA,CAC1Ca,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,MAK/BgF,EAAqB,CALUhF,CAAAA,CAAAA,CAAAA,CAAAA,CAKJW,EAAAA,EAAAA,CAC9BC,KADGoE,CACG,CAAC,CACNnE,EAAAA,CAAIuB,EAAAA,YAAYA,CAACvB,EAAE,CACnBgC,MAAAA,CAAQT,EAAAA,YAAYA,CAACS,MAAM,CAC3BJ,KAAAA,CAAOL,EAAAA,YAAYA,CAACK,KAAK,CACzBC,WAAAA,CAAaN,EAAAA,YAAYA,CAACM,WAAW,CACrCR,WAAAA,CAAaE,EAAAA,YAAYA,CAACF,WAAW,CACrC8B,WAAAA,CAAalB,EAAAA,QAAQA,CAACP,IAAI,CAC1BC,UAAAA,CAAY1B,EAAAA,OAAOA,CAACyB,IAAAA,GAErBxB,IAAI,CAACqB,EAAAA,YAAAA,CAAAA,CACLO,QAAQ,CAACC,EAAAA,OAAAA,CAAS3B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGmB,EAAAA,YAAAA,CAAaS,MAAM,CAAED,EAAAA,OAAAA,CAAQ/B,EAAE,GACpD8B,QAAQ,CAACG,EAAAA,QAAAA,CAAU7B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG2B,EAAAA,OAAAA,CAAQG,SAAS,CAAED,EAAAA,QAAAA,CAASjC,EAAE,GACpD8B,QAAQ,CAACK,EAAAA,OAAAA,CAAS/B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG6B,EAAAA,QAAAA,CAASG,QAAQ,CAAED,EAAAA,OAAAA,CAAQnC,EAAE,GAClD8B,QAAQ,CAAC7B,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAG+B,EAAAA,OAAAA,CAAQ9C,QAAQ,CAAEY,EAAAA,OAAAA,CAAQD,EAAE,GACjDG,KAAK,CACJyC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGmB,EAAAA,YAAAA,CAAahC,SAAS,CAAEc,QAAAA,CAASd,IACpCa,CAAAA,EAAAA,EADoCb,CAAAA,CAAAA,CACpCa,CAAGH,EAAAA,OAAAA,CAAQd,SAAS,CAAEkB,QAAAA,CAASlB,MAGlCkD,GAHkClD,CAAAA,CAAAA,CAAAA,CAAAA,CAG1BmD,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,CAAKf,EAAAA,YAAYA,CAACF,WAAW,GAGlCZ,EAAewD,EAAezD,MAAM,CACpC4D,CADA3D,CACmBwD,EAAexB,MAAM,CAACE,GAAiC,EAA1EyB,KAAmDzB,EAAOtB,IAAPsB,OAAkB,EAAWnC,MAAM,CAGtF6D,EAAeF,EAAmB3D,MAAM,CACxC8D,CADAD,CACmBA,EAAe,EACpCF,EAAmBI,MADEF,CACK,CAACG,EAAKC,CAALD,GAAKC,CAC9B,EAD8BA,EACxB7C,EAAQ8C,GAAR9C,OAAQ8C,CAAWD,EAAK7C,EAAL6C,GAAU,EAAI,KACjC5C,EAAc6C,SAAd7C,CAAc6C,CAAWD,EAAK5C,EAAL4C,SAAgB,EAAI,KACnD,OAAOD,EAAO,CAAPA,CAAgB3C,EAAe,CAAvBD,EACjB,EAAG,GAAKyC,CADiBxC,CAEzB,EAEJ,OAAOrC,CAHK6E,CAGL7E,YAAAA,CAAaC,IAAI,CAAC,CACvB4D,OAAAA,CAASA,CAAO,CAAC,EAAE,cACnB5C,YAAAA,OACA2D,EACAxD,cADAwD,CACAxD,CAfsBH,EAAe,EAAI,EAAoBA,EAAgB,IAAvDA,EAgBtB4D,MAhB0CD,MAgB1CC,GACAC,gBAAAA,GACAnD,iBAAAA,CAAmB8C,EACnBU,WAAAA,CADmBV,CAErB,EACF,CAGF,CAAE,KAHO,CAGAvE,EAAO,CAEd,EAFOA,KACPkF,OAAAA,CAAQlF,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,CADkCE,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC9UA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,cAAc,SAClC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CAMxB,IAAC,EAAM,CAAH,CAAekF,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAAf,KAA8C,EAAH,MAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,4IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/./node_modules/drizzle-orm/sql/functions/aggregate.js", "webpack://terang-lms-ui/src/app/api/reports/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?0a62", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { Column } from \"../../column.js\";\nimport { is } from \"../../entity.js\";\nimport { sql } from \"../sql.js\";\nfunction count(expression) {\n  return sql`count(${expression || sql.raw(\"*\")})`.mapWith(Number);\n}\nfunction countDistinct(expression) {\n  return sql`count(distinct ${expression})`.mapWith(Number);\n}\nfunction avg(expression) {\n  return sql`avg(${expression})`.mapWith(String);\n}\nfunction avgDistinct(expression) {\n  return sql`avg(distinct ${expression})`.mapWith(String);\n}\nfunction sum(expression) {\n  return sql`sum(${expression})`.mapWith(String);\n}\nfunction sumDistinct(expression) {\n  return sql`sum(distinct ${expression})`.mapWith(String);\n}\nfunction max(expression) {\n  return sql`max(${expression})`.mapWith(is(expression, Column) ? expression : String);\n}\nfunction min(expression) {\n  return sql`min(${expression})`.mapWith(is(expression, Column) ? expression : String);\n}\nexport {\n  avg,\n  avgDistinct,\n  count,\n  countDistinct,\n  max,\n  min,\n  sum,\n  sumDistinct\n};\n//# sourceMappingURL=aggregate.js.map", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { \r\n  courses, \r\n  classes, \r\n  users, \r\n  studentEnrollments, \r\n  courseEnrollments,\r\n  studentProgress,\r\n  quizAttempts,\r\n  modules,\r\n  chapters,\r\n  quizzes\r\n} from '@/lib/db/schema';\r\nimport { eq, and, or, count, sum, desc } from 'drizzle-orm';\r\n\r\n// GET /api/reports - Get various analytics and reports\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const type = searchParams.get('type'); // 'overview', 'course', 'class', 'student'\r\n    const teacherId = searchParams.get('teacherId');\r\n    const courseId = searchParams.get('courseId');\r\n    const classId = searchParams.get('classId');\r\n    const studentId = searchParams.get('studentId');\r\n    \r\n    if (!teacherId) {\r\n      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });\r\n    }\r\n\r\n    if (type === 'overview') {\r\n      // Get teacher overview analytics\r\n      const teacherCourses = await db\r\n        .select({ id: courses.id })\r\n        .from(courses)\r\n        .where(eq(courses.teacherId, parseInt(teacherId)));\r\n      \r\n      const courseIds = teacherCourses.map(c => c.id);\r\n      \r\n      if (courseIds.length === 0) {\r\n        return NextResponse.json({\r\n          totalCourses: 0,\r\n          totalStudents: 0,\r\n          totalClasses: 0,\r\n          averageProgress: 0,\r\n          recentActivity: []\r\n        });\r\n      }\r\n\r\n      // Total courses\r\n      const totalCourses = courseIds.length;\r\n\r\n      // Total students enrolled\r\n      const studentEnrollmentCount = await db\r\n        .select({ count: count() })\r\n        .from(studentEnrollments)\r\n        .where(eq(studentEnrollments.courseId, courseIds[0])); // This needs to be fixed for multiple courses\r\n      \r\n      // Get unique students across all courses\r\n      const uniqueStudents = await db\r\n        .selectDistinct({ studentId: studentEnrollments.studentId })\r\n        .from(studentEnrollments)\r\n        .where(eq(studentEnrollments.courseId, courseIds[0])); // This needs to be fixed\r\n\r\n      // Total classes with enrolled courses\r\n      const enrolledClasses = await db\r\n        .selectDistinct({ classId: courseEnrollments.classId })\r\n        .from(courseEnrollments)\r\n        .where(eq(courseEnrollments.courseId, courseIds[0])); // This needs to be fixed\r\n\r\n      // Calculate completion rate across all students\r\n      const allEnrollments = await db\r\n        .select({\r\n          completedAt: studentEnrollments.completedAt\r\n        })\r\n        .from(studentEnrollments)\r\n        .where(or(...courseIds.map(id => eq(studentEnrollments.courseId, id))));\r\n\r\n      // Recent quiz attempts\r\n      const recentActivity = await db\r\n        .select({\r\n          id: quizAttempts.id,\r\n          studentName: users.name,\r\n          courseName: courses.name,\r\n          score: quizAttempts.score,\r\n          totalPoints: quizAttempts.totalPoints,\r\n          completedAt: quizAttempts.completedAt\r\n        })\r\n        .from(quizAttempts)\r\n        .leftJoin(users, eq(quizAttempts.studentId, users.id))\r\n        .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(eq(courses.teacherId, parseInt(teacherId)))\r\n        .orderBy(desc(quizAttempts.completedAt))\r\n        .limit(10);\r\n\r\n      const completedEnrollments = allEnrollments.filter(e => e.completedAt !== null).length;\r\n      const averageProgress = allEnrollments.length > 0 ? (completedEnrollments / allEnrollments.length) * 100 : 0;\r\n\r\n      return NextResponse.json({\r\n        totalCourses,\r\n        totalStudents: uniqueStudents.length,\r\n        totalClasses: enrolledClasses.length,\r\n        averageProgress,\r\n        recentActivity\r\n      });\r\n    } else if (type === 'course' && courseId) {\r\n      // Get course-specific analytics\r\n      const course = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.id, parseInt(courseId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (course.length === 0) {\r\n        return NextResponse.json({ error: 'Course not found or access denied' }, { status: 403 });\r\n      }\r\n\r\n      // Get enrolled students\r\n      const enrolledStudents = await db\r\n        .select({\r\n          id: studentEnrollments.id,\r\n          studentId: studentEnrollments.studentId,\r\n          studentName: users.name,\r\n          studentEmail: users.email,\r\n          enrolledAt: studentEnrollments.enrolledAt,\r\n          completedAt: studentEnrollments.completedAt,\r\n          finalScore: studentEnrollments.finalScore\r\n        })\r\n        .from(studentEnrollments)\r\n        .leftJoin(users, eq(studentEnrollments.studentId, users.id))\r\n        .where(eq(studentEnrollments.courseId, parseInt(courseId)));\r\n\r\n      // Get quiz performance for this course\r\n      const quizPerformance = await db\r\n        .select({\r\n          studentId: quizAttempts.studentId,\r\n          studentName: users.name,\r\n          quizId: quizAttempts.quizId,\r\n          score: quizAttempts.score,\r\n          totalPoints: quizAttempts.totalPoints,\r\n          completedAt: quizAttempts.completedAt,\r\n          chapterName: chapters.name\r\n        })\r\n        .from(quizAttempts)\r\n        .leftJoin(users, eq(quizAttempts.studentId, users.id))\r\n        .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .where(eq(modules.courseId, parseInt(courseId)))\r\n        .orderBy(desc(quizAttempts.completedAt));\r\n\r\n      // Calculate course statistics\r\n      const totalStudents = enrolledStudents.length;\r\n      const completedStudents = enrolledStudents.filter(student => student.completedAt !== null).length;\r\n      const averageProgress = totalStudents > 0 ? (completedStudents / totalStudents) * 100 : 0;\r\n      const completionRate = totalStudents > 0 ? (completedStudents / totalStudents) * 100 : 0;\r\n\r\n      return NextResponse.json({\r\n        course: course[0],\r\n        totalStudents,\r\n        averageProgress,\r\n        completionRate,\r\n        enrolledStudents,\r\n        quizPerformance\r\n      });\r\n    } else if (type === 'class' && classId) {\r\n      // Get class-specific analytics\r\n      const classData = await db\r\n        .select()\r\n        .from(classes)\r\n        .where(eq(classes.id, parseInt(classId)))\r\n        .limit(1);\r\n\r\n      if (classData.length === 0) {\r\n        return NextResponse.json({ error: 'Class not found' }, { status: 404 });\r\n      }\r\n\r\n      // Get students enrolled in courses that are assigned to this class\r\n      const classStudents = await db\r\n        .select({\r\n          id: users.id,\r\n          name: users.name,\r\n          email: users.email\r\n        })\r\n        .from(users)\r\n        .innerJoin(studentEnrollments, eq(users.id, studentEnrollments.studentId))\r\n        .innerJoin(courseEnrollments, eq(studentEnrollments.courseId, courseEnrollments.courseId))\r\n        .where(\r\n          and(\r\n            eq(courseEnrollments.classId, parseInt(classId)),\r\n            eq(users.role, 'student')\r\n          )\r\n        );\r\n\r\n      // Get courses enrolled by this class\r\n      const enrolledCourses = await db\r\n        .select({\r\n          courseId: courseEnrollments.courseId,\r\n          courseName: courses.name,\r\n          courseCode: courses.courseCode,\r\n          enrolledAt: courseEnrollments.enrolledAt\r\n        })\r\n        .from(courseEnrollments)\r\n        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(courseEnrollments.classId, parseInt(classId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        );\r\n\r\n      // Get student progress in teacher's courses for this class\r\n      const studentProgress = await db\r\n        .select({\r\n          studentId: studentEnrollments.studentId,\r\n          studentName: users.name,\r\n          courseId: studentEnrollments.courseId,\r\n          courseName: courses.name,\r\n          enrolledAt: studentEnrollments.enrolledAt,\r\n          completedAt: studentEnrollments.completedAt,\r\n          finalScore: studentEnrollments.finalScore\r\n        })\r\n        .from(studentEnrollments)\r\n        .leftJoin(users, eq(studentEnrollments.studentId, users.id))\r\n        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))\r\n        .innerJoin(courseEnrollments, eq(studentEnrollments.courseId, courseEnrollments.courseId))\r\n        .where(\r\n          and(\r\n            eq(courseEnrollments.classId, parseInt(classId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        );\r\n\r\n      return NextResponse.json({\r\n        class: classData[0],\r\n        totalStudents: classStudents.length,\r\n        students: classStudents,\r\n        enrolledCourses,\r\n        studentProgress\r\n      });\r\n    } else if (type === 'student' && studentId) {\r\n      // Get student-specific analytics\r\n      const student = await db\r\n        .select()\r\n        .from(users)\r\n        .where(\r\n          and(\r\n            eq(users.id, parseInt(studentId)),\r\n            eq(users.role, 'student')\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (student.length === 0) {\r\n        return NextResponse.json({ error: 'Student not found' }, { status: 404 });\r\n      }\r\n\r\n      // Get student's course enrollments in teacher's courses\r\n      const studentCourses = await db\r\n        .select({\r\n          enrollmentId: studentEnrollments.id,\r\n          courseId: studentEnrollments.courseId,\r\n          courseName: courses.name,\r\n          courseCode: courses.courseCode,\r\n          enrolledAt: studentEnrollments.enrolledAt,\r\n          completedAt: studentEnrollments.completedAt,\r\n          finalScore: studentEnrollments.finalScore\r\n        })\r\n        .from(studentEnrollments)\r\n        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(studentEnrollments.studentId, parseInt(studentId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        );\r\n\r\n      // Get student's quiz attempts in teacher's courses\r\n      const quizAttemptHistory = await db\r\n        .select({\r\n          id: quizAttempts.id,\r\n          quizId: quizAttempts.quizId,\r\n          score: quizAttempts.score,\r\n          totalPoints: quizAttempts.totalPoints,\r\n          completedAt: quizAttempts.completedAt,\r\n          chapterName: chapters.name,\r\n          courseName: courses.name\r\n        })\r\n        .from(quizAttempts)\r\n        .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(quizAttempts.studentId, parseInt(studentId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .orderBy(desc(quizAttempts.completedAt));\r\n\r\n      // Calculate student statistics\r\n      const totalCourses = studentCourses.length;\r\n      const completedCourses = studentCourses.filter(course => course.completedAt !== null).length;\r\n      const averageProgress = totalCourses > 0 ? (completedCourses / totalCourses) * 100 : 0;\r\n\r\n      const totalQuizzes = quizAttemptHistory.length;\r\n      const averageQuizScore = totalQuizzes > 0\r\n        ? quizAttemptHistory.reduce((sum, quiz) => {\r\n            const score = parseFloat(quiz.score || '0');\r\n            const totalPoints = parseFloat(quiz.totalPoints || '1');\r\n            return sum + ((score / totalPoints) * 100);\r\n          }, 0) / totalQuizzes\r\n        : 0;\r\n\r\n      return NextResponse.json({\r\n        student: student[0],\r\n        totalCourses,\r\n        completedCourses,\r\n        averageProgress,\r\n        totalQuizzes,\r\n        averageQuizScore,\r\n        courseEnrollments: studentCourses,\r\n        quizHistory: quizAttemptHistory\r\n      });\r\n    } else {\r\n      return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching reports:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/reports',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\reports\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/reports/route\",\n        pathname: \"/api/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/reports/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\reports\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "searchParams", "nextUrl", "type", "teacherId", "get", "courseId", "classId", "studentId", "NextResponse", "json", "error", "status", "courseIds", "teacherCourses", "db", "select", "id", "courses", "from", "where", "eq", "parseInt", "map", "c", "length", "totalCourses", "totalStudents", "totalClasses", "averageProgress", "recentActivity", "count", "studentEnrollments", "uniqueStudents", "selectDistinct", "enrolledClasses", "courseEnrollments", "allEnrollments", "completedAt", "or", "quizAttempts", "studentName", "users", "name", "courseName", "score", "totalPoints", "leftJoin", "quizzes", "quizId", "chapters", "chapterId", "modules", "moduleId", "orderBy", "desc", "limit", "completedEnrollments", "filter", "e", "course", "and", "enrolledStudents", "studentEmail", "email", "enrolledAt", "finalScore", "quizPerformance", "chapterName", "completedStudents", "student", "completionRate", "classData", "classes", "classStudents", "innerJoin", "role", "enrolledCourses", "courseCode", "studentProgress", "class", "students", "studentCourses", "enrollmentId", "quizAttemptHistory", "completedCourses", "totalQuizzes", "averageQuizScore", "reduce", "sum", "quiz", "parseFloat", "quizHistory", "console", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}