{"version": 3, "file": "3077.js", "mappings": "idAoBM,MAAQ,cAAiB,SAjBK,CAiBI,CAAU,MAhBvC,EAAE,KAAO,MAAM,OAAQ,IAAM,GAAG,CAAK,KAAG,IAAK,CAAI,MAAK,GAAI,GAAK,KAAK,SAAU,EACvF,CAAC,QAAU,EAAE,EAAI,KAAK,CAAI,MAAK,CAAG,KAAK,GAAK,UAAU,EACtD,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC5E,+SCeM,EAAc,SAGd,CAAC,EAAqB,EAAiB,CAAI,OAAkB,CAAC,GAc9D,CAAC,EAdsC,EAcN,CAAI,EAAwC,GAU7E,EAAgC,IACpC,EAXqC,CAW/B,eACJ,EACA,WACA,KAAM,cACN,eACA,QACA,GAAQ,EACV,CAAI,EACE,EAAmB,SAA0B,IAAI,EACjD,EAAmB,SAA6B,IAAI,EACpD,CAAC,EAAM,EAAO,CAAI,IAAJ,CAAI,EAAoB,CAAC,CAC3C,KAAM,EACN,YAAa,IAAe,EAC5B,SAAU,EACV,OAAQ,CACV,CAAC,EAED,MACE,UAAC,GACC,MAAO,aACP,aACA,EACA,UAAW,OAAK,CAAC,EACjB,QAAS,OAAK,CAAC,EACf,cAAe,OAAK,CAAC,EACrB,OACA,aAAc,EACd,aAAoB,cAAY,IAAM,EAAS,GAAa,CAAC,GAAW,CAAC,EAAQ,EAAZ,GAAW,GAChF,WAEC,GAGP,EAEA,EAAO,YAAc,EAMrB,IAAM,EAAe,gBAMf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,GAAM,eAAE,EAAe,GAAG,EAAa,CAAI,EACrC,EAAU,EAAiB,EAAc,EADR,CAEjC,EAAqB,OAAe,CAAC,EAAc,EAAQ,UAAU,EAC3E,MACE,UAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,gBAAc,SACd,gBAAe,EAAQ,KACvB,gBAAe,EAAQ,UACvB,aAAY,EAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAM,QAAS,EAAQ,YAAY,GAGvE,GAGF,EAAc,YAAc,EAM5B,IAAM,EAAc,eAGd,CAAC,EAAgB,EAAgB,CAAI,EAAwC,EAAa,CAC9F,QADqC,GACzB,MACd,CAAC,EAgBK,EAA4C,IAChD,GAAM,eAAE,aAAe,WAAY,YAAU,EAAU,CAAI,EACrD,EAAU,EAAiB,EAAa,GAC9C,MACE,IAFyD,CAEzD,KAAC,GAAe,MAAO,aAAe,EACnC,SAAM,WAAS,IAAI,EAAU,GAC5B,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACvC,mBAAC,GAAe,CAAf,CAAgB,QAAO,GAAC,YACtB,WACH,EACF,CACD,EACH,CAEJ,EAEA,EAAa,YAAc,EAM3B,IAAM,EAAe,gBAWf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,IAAM,EAAgB,EAAiB,EAAc,EAAM,aAAa,EAClE,YAAE,EAAa,EAAc,WAAY,GAAG,EAAa,CAAI,EAC7D,EAAU,EAAiB,EAAc,EAAM,aAAa,EAClE,OAAO,EAAQ,MACb,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACvC,mBAAC,GAAmB,GAAG,EAAc,IAAK,EAAc,EAC1D,EACE,IACN,GAGF,EAAc,YAAc,EAM5B,IAAM,EAAO,QAAU,CAAC,4BAA4B,EAE9C,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,eAAE,EAAe,GAAG,EAAa,CAAI,EACrC,EAAU,EAAiB,EAAc,EADR,CAEvC,MAGE,IAJ0D,CAI1D,KAAC,GAAY,CAAZ,CAAa,GAAI,EAAM,gBAAc,EAAC,OAAQ,CAAC,EAAQ,UAAU,EAChE,mBAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EAEL,MAAO,CAAE,cAAe,OAAQ,GAAG,EAAa,MAAM,EACxD,CACF,CAEJ,GAOI,EAAe,gBAWf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,IAAM,EAAgB,EAAiB,EAAc,EAAM,aAAa,EAClE,YAAE,EAAa,EAAc,WAAY,GAAG,EAAa,CAAI,EAC7D,EAAU,EAAiB,EAAc,EADgB,aACG,EAClE,MACE,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACtC,WAAQ,MACP,UAAC,GAAoB,GAAG,EAAc,IAAK,EAAc,EAEzD,UAAC,GAAuB,GAAG,EAAc,IAAK,EAAc,EAEhE,CAEJ,GAGF,EAAc,YAAc,EAQ5B,IAAM,EAA2B,aAC/B,CAAC,EAA4C,KAC3C,IAAM,EAAU,EAAiB,EAAc,EAAM,aAAa,EAC5D,EAAmB,SAAuB,IAAI,EAC9C,EAAe,OAAe,CAAC,EAAc,EAAQ,WAAY,GAQvE,OARiF,EAG3E,UAAU,KACd,IAAM,EAAU,EAAW,QAC3B,GAAI,EAAS,MAAO,QAAU,CAAC,EACjC,EAAG,CAAC,CAAC,CADmC,CAItC,UAAC,GACE,GAAG,EACJ,IAAK,EAGL,UAAW,EAAQ,KACnB,6BAA2B,EAC3B,iBAAkB,OAAoB,CAAC,EAAM,iBAAmB,IAC9D,EAAM,eAAe,EACrB,EAAQ,WAAW,SAAS,MAAM,CACpC,CAAC,EACD,qBAAsB,OAAoB,CAAC,EAAM,qBAAsB,IACrE,IAAM,EAAgB,EAAM,OAAO,cAC7B,EAAyC,IAAzB,EAAc,SAA0C,IAA1B,EAAc,SACpB,IAAzB,EAAc,KAIjB,CAJiB,EAAgB,IAIjC,EAAM,eAAe,CACzC,CAAC,EAGD,eAAgB,OAAoB,CAAC,EAAM,eAAgB,GACzD,EAAM,eAAe,EACvB,EAGN,GAKI,EAA8B,aAClC,CAAC,EAA4C,KAC3C,IAAM,EAAU,EAAiB,EAAc,EAAM,aAAa,EAC5D,EAAgC,SAAO,IACvC,CAD4C,CACX,UAAO,GAE9C,EAFmD,IAGjD,UAAC,GACE,GAAG,EACJ,IAAK,EACL,WAAW,EACX,6BAA6B,EAC7B,iBAAmB,IACjB,EAAM,mBAAmB,GAEpB,EAFyB,gBAEnB,EAAkB,CACvB,EAAyB,QAAS,GAAQ,WAAW,SAAS,MAAM,EAExE,EAAM,eAAe,GAGvB,EAAwB,SAAU,EAClC,EAAyB,SAAU,CACrC,EACA,kBAAmB,IACjB,EAAM,oBAAoB,GAErB,EAF0B,gBAEpB,EAAkB,CAC3B,EAAwB,SAAU,EACM,eAAe,CAAnD,EAAM,OAAO,cAAc,OAC7B,EAAyB,SAAU,IAOvC,IAAM,EAAS,EAAM,OACG,EAAQ,WAAW,EACtB,KADsB,EAAS,SAAS,IACxC,EAD8C,cACxC,CAAe,EAMtC,cAAM,OAAO,cAAc,MAAsB,EAAyB,SAAS,EAC/E,eAAe,CAEzB,GAGN,GA6BI,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,CAAE,0BAAe,kBAAW,mBAAiB,EAAkB,GAAG,EAAa,CAAI,EACnF,EAAU,EAAiB,EAAc,EADsC,CAE/E,EAAmB,QADmC,CACZ,IAAI,EAC9C,EAAe,OAAe,CAAC,EAAc,GAMnD,MAFA,CAJ6D,EAI7D,KAAc,CAAC,EAGb,uBACE,oBAAC,GAAU,CAAV,CACC,SAAO,EACP,MAAI,EACJ,QAAS,EACT,iBAAkB,EAClB,mBAAoB,EAEpB,mBAAC,IAAgB,CAAhB,CACC,KAAK,SACL,GAAI,EAAQ,UACZ,mBAAkB,EAAQ,cAC1B,kBAAiB,EAAQ,QACzB,aAAY,EAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EACL,UAAW,IAAM,EAAQ,cAAa,EAAK,EAC7C,CAD6C,EAI7C,uBACE,oBAAC,GAAa,QAAS,EAAQ,QAAS,EACxC,UAAC,cAAmB,EAAwB,cAAe,EAAQ,cAAe,GACpF,GAEJ,CAEJ,GAOI,EAAa,cAMb,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAU,EAAiB,EADI,GAErC,MAAO,IADmD,CACnD,KAAC,IAAS,CAAC,GAAV,CAAa,GAAI,EAAQ,QAAU,GAAG,EAAY,IAAK,EAAc,CAC/E,GAGF,EAAY,YAAc,EAM1B,IAAM,EAAmB,oBAMnB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,eAAE,EAAe,GAAG,EAAiB,CAAI,EACzC,EAAU,EAAiB,EAAkB,GACnD,GAF2C,GAEpC,IADyD,CACzD,KAAC,IAAS,CAAC,EAAV,CAAY,GAAI,EAAQ,cAAgB,GAAG,EAAkB,IAAK,EAAc,CAC1F,EAGF,GAAkB,YAAc,EAMhC,IAAM,EAAa,cAKb,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAU,EAAiB,EAAY,GAC7C,MACE,IAFwD,CAExD,KAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACJ,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAM,QAAS,IAAM,EAAQ,cAAa,GAAM,EAAD,CAAC,EAUtF,SAAS,EAAS,GAAe,OACxB,EAAO,OAAS,QACzB,CANA,EAAY,YAAc,EAQ1B,IAAM,EAAqB,qBAErB,CAAC,EAAiB,EAAiB,CAAI,OAAa,CAAC,EAAoB,CAC7E,GADuC,SAC1B,EACb,UAAW,EACX,SAAU,QACZ,CAAC,EAIK,EAA4C,CAAC,SAAE,EAAQ,IAC3D,IAAM,EAAsB,EAAkB,GAExC,EAAU,KAAK,EAAoB,MAFuB,KAEZ,mBAAmB,EAAoB,SAAS;;0BAAA,EAE1E,EAAoB,SAAS;;0EAAA,EAEmB,EAAoB,QAAQ,GAStG,OAPM,YAAU,KACV,IACe,CACZ,IAFM,IACe,eAAe,IAC1B,GADiC,KACzB,MAAM,EAAxB,CAET,CAFmB,CAEhB,CAAC,CAFoC,CAE3B,EAAQ,EAEd,GAFa,CAGtB,EASM,EAAwD,CAAC,YAAE,EAAY,gBAAc,IACzF,IAAM,EAA4B,EARH,gBAQqB,YAC9C,EAAU,UAD4D,iEAC5D,EAA6E,EAA0B,WAAW,KAWlI,OATM,YAAU,KACd,IAAM,EAAgB,EAAW,SAAS,aAAa,kBAAkB,EAErE,GAAiB,IAEf,CAAC,QAD2B,EADE,YACF,CAAe,IAC1B,QAAQ,CAD+B,GAC/B,CAAK,EAA7B,CAET,CAFyB,CAEtB,CAAC,CAFyC,CAEhC,EAAY,EAAc,EAEhC,IACT,EAEM,EAAO,CAL2B,CAMlC,EAAU,EACV,GAAS,EACT,GAAU,EACV,GAAU,EACV,GAAQ,EACR,GAAc,EACd,GAAQ,oCCzhBR,MAAI,cAAiB,KAhBS,CAgBJ,CAf7B,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC3C,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC7C,oCCUM,MAAc,cAAiB,eAbD,CAAC,CAAC,MAAQ,EAAE,EAAG,gBAAkB,KAAK,CAAS,QAAC,CAAC,qCCsB/E,MAAW,cAAiB,YAtBE,CAClC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CACE,OACA,CACE,CAAG,sIACH,GAAK,QACP,EACF,CACF,oCCIM,MAAe,cAAiB,gBAbF,CAAC,CAAC,MAAQ,EAAE,EAAG,eAAiB,KAAK,CAAS,QAAC,CAAC,qCCqB9E,MAAO,cAAiB,QArBM,CAqBE,CAAU,OAlB5C,CACE,CAAG,0FACH,GAAK,SACP,EACF,CACF,oCCcM,MAAQ,cAAiB,SAtBK,CAClC,CACE,OACA,CACE,CAAG,0HACH,GAAK,SACP,EACF,CACA,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,MAAK,CAAG,KAAK,GAAK,UAAU,EACzD,oCCSM,MAAc,cAAiB,eAlBD,CAClC,CAAC,MAAQ,EAAE,EAAG,CAA4B,8BAAK,SAAU,EACzD,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC3C,oCCcM,MAAW,cAAiB,YAnBE,CAmBU,CAAU,MAlB7C,EAAE,EAAG,CAA8D,gEAAK,SAAU,EAC3F,CAAC,MAAQ,EAAE,EAAG,CAA2B,6BAAK,SAAU,EACxD,CAAC,MAAQ,EAAE,EAAG,CAAW,aAAK,SAAU,EACxC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EACzC,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC3C,oLCIM,EAAc,gCACd,EAAgB,CAAE,SAAS,EAAO,WAAY,EAAK,EAMnD,EAAa,mBAGb,CAAC,EAAY,EAAe,EAAqB,CAAI,OAAgB,CAGzE,GAGI,CAAC,EAA+B,EAA2B,CAAI,OAAkB,CACrF,EACA,CAAC,EAAqB,EA+BlB,CAAC,EAAqB,EAAqB,CAC/C,EAAkD,CAlCa,EAuC3D,EAAyB,IArCP,CAgCsC,MADb,CAMlB,CAC7B,CAAC,EAA2C,IAExC,UAAC,EAAW,SAAX,CAAoB,MAAO,EAAM,wBAChC,mBAAC,EAAW,KAAX,CAAgB,MAAO,EAAM,wBAC5B,mBAAC,GAAsB,GAAG,EAAO,IAAK,EAAc,EACtD,EACF,EAKN,GAAiB,YAAc,EAgB/B,IAAM,EAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,yBACJ,cACA,OACA,GAAO,MACP,EACA,iBAAkB,0BAClB,2BACA,eACA,EACA,4BAA4B,GAC5B,GAAG,EACL,CAAI,EACE,EAAY,SAAoC,IAAI,EACpD,EAAe,OAAe,CAAC,EAAc,GAAG,EACpC,QAAY,CAAC,GAAG,CAC3B,EAAkB,EAAmB,CAAI,OAAoB,CAAC,CACnE,KAAM,EACN,YAAa,GAA2B,KACxC,SAAU,EACV,OAAQ,CACV,CAAC,EACK,CAAC,EAAkB,EAAmB,CAAU,YAAS,GACzD,CADsC,CACnB,OAAc,CAAC,GAClC,EAAW,EAAc,GACzB,EAF8C,EAEtB,QAAO,GAC/B,CAAC,CADmC,CACd,EAF0B,CAEM,WAAS,CAAC,EAUtE,KAVkD,EAWhD,EATI,UAAU,KACd,IAAM,EAAO,EAAI,QACjB,GAAI,EAEF,IAFQ,GACR,EAAK,iBAAiB,EAAa,GAC5B,IAAM,EAAK,OADiC,YACjC,CAAoB,EAAa,EAEvD,EAAG,CAAC,EAAiB,EAGnB,OALqE,CAKrE,EAAC,GACC,MAAO,cACP,EACA,IAAK,OACL,mBACA,EACA,YAAmB,cACjB,GAAe,EAAoB,GACnC,CAAC,EAAmB,EAEtB,CAH8C,cACxB,EAEA,YAAY,IAAM,GAAoB,GAAO,CAAH,CAAK,EACrE,mBAA0B,cACxB,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAEH,sBAA6B,cAC3B,IAAM,EAAuB,GAAe,EAAY,CAAC,EACzD,CAAC,GAGH,mBAAC,IAAS,CAAC,IAAV,CACC,SAAU,GAA4C,IAAxB,EAA4B,GAAK,EAC/D,mBAAkB,EACjB,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAM,OACnC,YAAa,OAAoB,CAAC,EAAM,YAAa,KACnD,EAAgB,SAAU,CAC5B,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAK3C,IAAM,EAAkB,CAAC,EAAgB,QAEzC,GAAI,EAAM,SAAW,EAAM,eAAiB,GAAmB,CAAC,EAAkB,CAChF,IAAM,EAAkB,IAAI,YAAY,EAAa,GAGrD,GAFA,EAAM,KAD4D,QAC5D,CAAc,cAAc,GAE9B,CAAC,EAAgB,SAF4B,OAE5B,CAAkB,CACrC,IAAM,EAAQ,IAAW,KAAF,CAAE,CAAO,GAAU,EAAK,SAAS,EAOxD,EAJuB,CAFJ,EAAM,KAAK,CAMnB,EAN6B,EAAK,MAAM,EAC/B,EAAM,KAAK,GAAU,EAAK,KAAO,MACD,EAAK,CAAE,EAAF,IAAE,CADU,SAI/B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAC1C,EAC7B,CACF,CAEA,EAAgB,SAAU,CAC5B,CAAC,EACD,MAN0D,CAMlD,OAAoB,CAAC,EAAM,OAAQ,IAAM,GAAoB,GAAM,EAAD,CAAC,CAInF,CAAC,EAMK,EAAY,uBAaZ,EAA6B,aACjC,CAAC,EAA0C,KACzC,GAAM,yBACJ,YACA,GAAY,SACZ,GAAS,YACT,WACA,EACA,GAAG,EACL,CAAI,EACE,EAAS,OAAK,CAAC,EACf,EAAK,GAAa,EAClB,EAAU,EAAsB,EAAW,GAC3C,EAAmB,EAAQ,gBADuC,GAClB,EAChD,EAAW,EAAc,GAEzB,oBAFgD,CAE9C,uBAAoB,mBAAuB,EAAiB,CAAI,EASxE,OAPM,YAAU,KACd,GAAI,EAEF,OADA,EADa,EAEN,IAAM,GAEjB,EAAG,CAAC,EAAW,EAAoB,CAHZ,CAGkC,EAGvD,UAAC,EAAW,KAH0C,GAG1C,CAAX,CACC,MAAO,KACP,YACA,SACA,EAEA,mBAAC,IAAS,CAAC,KAAV,CACC,SAAU,EAAmB,EAAI,GACjC,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,EACL,YAAa,OAAoB,CAAC,EAAM,YAAa,IAG9C,EAEA,EAAQ,MAFG,KAEH,CAAY,EAAE,CAFX,EAAM,eAAe,CAGvC,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAAM,EAAQ,YAAY,EAAE,CAAC,CAC1E,UAAW,OAAoB,CAAC,EAAM,UAAW,IAC/C,GAAI,UAAM,KAAiB,EAAM,SAAU,YACzC,EAAQ,eAAe,EAIzB,GAAI,EAAM,SAAW,EAAM,cAAe,OAE1C,IAAM,EAAc,SAqDvB,CAAe,CAA4B,EAA2B,GAAiB,MAC9F,IAAM,GARsB,EAQK,CAArB,CAA2B,CARE,EAQF,CAP3B,EAD8C,IACvC,EAOyB,EAPlB,CAOqB,CANhC,cAAR,EAAsB,aAAe,iBAAuB,YAAc,GAOjF,KAAoB,aAAhB,GAA8B,CAAC,YAAa,YAAY,EAAE,SAAS,EAAG,EAAG,GACzD,KADgE,UAChF,GAAgC,CAAC,UAAW,WAAW,EAAE,SAAS,EAAG,EAAG,OACrE,CAD4E,CACpD,EACjC,CADoC,CAzDW,EAAO,EAAQ,YAAa,EAAQ,GAAG,EAE1E,GAAoB,SAAhB,EAA2B,CAC7B,GAAI,EAAM,SAAW,EAAM,SAAW,EAAM,QAAU,EAAM,SAAU,OACtE,EAAM,eAAe,EAErB,IAAI,EADU,IAAW,KAAF,CAAE,CAAQ,GAAS,EAAK,SAAS,EAC7B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAE1D,GAAoB,OAAQ,EAAxB,EAAwB,EAAe,QAAQ,UAC1B,SAAhB,GAA0C,SAAhB,EAAwB,CACrC,OAAQ,EAAxB,GAAwB,EAAe,QAAQ,EACnD,IAAM,EAAe,EAAe,QAAQ,EAAM,aAAa,EAC/D,EAAiB,EAAQ,KACrB,SA6DX,CAAa,CAAY,GAAoB,OAC7C,EAAM,IAAO,CAAC,EAAG,IAAU,KAAoB,GAAS,EAAM,MAAM,CAAE,CAC/E,EA/D8B,EAAgB,EAAe,CAAC,EAC1C,EAAe,MAAM,EAAe,CAAC,CAC3C,CAMA,WAAW,IAAM,EAAW,GAC9B,CACF,CAAC,EAEA,OAJ6C,CAAC,CAI1B,YAApB,OAAO,EACJ,EAAS,kBAAE,EAAkB,WAAgC,MAApB,CAAyB,CAAC,EACnE,GACN,EAGN,GAGF,EAAqB,YAAc,EAKnC,IAAM,EAAuD,CAC3D,UAAW,OAAQ,QAAS,OAC5B,WAAY,OAAQ,UAAW,OAC/B,OAAQ,QAAS,KAAM,QACvB,SAAU,OAAQ,IAAK,MACzB,EAgBA,SAAS,EAAW,EAA2B,GAAgB,GAAO,IAC9D,EAA6B,SAAS,cAC5C,QAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,EAAU,MAAM,eAAE,CAAc,CAAC,EAC7B,SAAS,gBAAkB,GAFe,MAIlD,CAUA,IAAM,EAAO,EACP,EAAO,OAbkD,yOC1TzD,CAAC,EAAsB,EAAkB,CAAI,OAAkB,CAAC,OAAvB,GAAkC,CAC/E,IAAiB,CAClB,EACK,EAAiB,QAAiB,CAAC,EAMnC,EAAgB,kBAEhB,EAAe,eAYf,CAAC,EAAgC,EAAyB,CAC9D,EAAkD,GAqB9C,EAAkD,IAGtD,GAAM,CAxByD,OADD,QA0B5D,gBACA,EAxC2B,GAwCX,mBAChB,EAAoB,4BACpB,EAA0B,YAC1B,EACF,CAAI,EACE,EAAyB,UAAO,GAChC,CADoC,CACN,UAAO,GACrC,EAA0B,SAAO,CAAC,EAOxC,OALM,YAAU,KACd,IAAM,EAAiB,EAAkB,QACzC,MAAO,IAAM,OAAO,aAAa,EACnC,EAAG,CAAC,CAAC,EAGH,MAJ+C,EAI/C,EAAC,GACC,MAAO,mBACP,gBACA,EACA,OAAc,cAAY,KACxB,OAAO,aAAa,EAAkB,OAAO,EAC7C,EAAiB,SAAU,CAC7B,EAAG,CAAC,CAAC,EACL,QAAe,cAAY,KACzB,OAAO,aAAa,EAAkB,OAAO,EAC7C,EAAkB,QAAU,OAAO,WACjC,IAAO,EAAiB,SAAU,EAClC,EAEJ,EAAG,CAAC,EAAkB,eAAD,SACrB,EACA,yBAAgC,cAAY,IAC1C,EAAsB,QAAU,CAClC,EAAG,CAAC,CAAC,EACL,0BAEC,YAGP,EAEA,EAAgB,YAAc,EAM9B,IAAM,EAAe,UAef,CAAC,EAAwB,EAAiB,CAC9C,EAA0C,GAoBtC,EAAkC,IACtC,GAtB8C,gBAuB5C,WACA,EACA,KAAM,cACN,EACA,eACA,wBAAyB,EACzB,cAAe,EACjB,CAAI,EACE,EAAkB,EAA0B,EAAc,EAAM,cAAc,EAC9E,EAAc,EAAe,GAC7B,CAAC,EAAS,EAAU,CAAU,KADa,EACvB,GAAU,CAAmC,IAAI,EACrE,EAAY,OAAK,CAAC,EAClB,EAAqB,SAAO,CAAC,EAC7B,EACJ,GAA+B,EAAgB,wBAC3C,EAAgB,GAAqB,EAAgB,cACrD,EAA0B,UAAO,GACjC,CAAC,CADqC,CAC/B,EAAO,CAAI,IAAJ,CAAI,EAAoB,CAAC,CAC3C,KAAM,EACN,YAAa,IAAe,EAC5B,SAAU,IACJA,GACF,EAAgB,EADR,IACQ,CAAO,EAIvB,SAAS,cAAc,IAAI,YAAY,KAEvC,EAAgB,KAFmC,CAAC,CAEpC,CAAQ,EAE1B,IAAeA,EACjB,EACA,CAFqB,MAEb,CACV,CAAC,EACK,EAAuB,UAAQ,IAC5B,EAAQ,EAAkB,QAAU,eAAiB,eAAkB,SAC7E,CAAC,EAAK,EAAD,EAEiB,cAAY,KACnC,OAAO,aAAa,EAAa,OAAO,EACxC,EAAa,QAAU,EACvB,EAAkB,SAAU,EAC5B,GAAQ,EACV,EADc,CACV,EAAQ,EAEN,EAAoB,CAFf,CAEe,YAAY,KACpC,OAAO,aAAa,EAAa,OAAO,EACxC,EAAa,QAAU,EACvB,GAAQ,EACV,EAAG,CADY,EACH,EAEN,EAA0B,CAFrB,CAEqB,YAAY,KAC1C,OAAO,aAAa,EAAa,OAAO,EACxC,EAAa,QAAU,OAAO,WAAW,KACvC,EAAkB,SAAU,EAC5B,EAAQ,IAAI,EACC,QAAU,CACzB,EAAG,EACL,EAAG,CAAC,EAAe,EAAQ,EAW3B,EAZkB,CACQ,IAEpB,YAAU,IACP,KACD,EAAa,SAAS,CACxB,OAAO,aAAa,EAAa,OAAO,EACxC,EAAa,QAAU,EAE3B,EACC,CAAC,CAAC,EAGH,UAAiB,KAAhB,CAAsB,GAAG,EACxB,mBAAC,GACC,MAAO,YACP,OACA,EACA,yBACA,EACA,gBAAiB,EACjB,eAAsB,cAAY,KAC5B,EAAgB,iBAAiB,QAAS,IACzC,GACP,EAAG,CAAC,EAAgB,GADF,GADgD,UAE9C,CAAkB,EAAmB,EAAW,EACpE,MADmE,SAC7C,cAAY,KAC5B,EACF,KAGA,OAHY,WADe,CAIpB,CAAa,EAAa,OAAO,EACxC,EAAa,QAAU,EAE3B,EAAG,CAAC,EAAa,EAAwB,EACzC,OAAQ,EACR,QAAS,EAF+B,wBAGxC,WAEC,GACH,CACF,CAEJ,CAEA,GAAQ,YAAc,EAMtB,IAAM,EAAe,iBAMf,EAAuB,aAC3B,CAAC,EAAyC,KACxC,GAAM,gBAAE,EAAgB,GAAG,EAAa,CAAI,EACtC,EAAU,EAAkB,EAAc,EADR,CAElC,EAAkB,EAA0B,EAAc,GAC1D,EAFwD,EAE3B,GAC7B,EAAY,EAF4D,MAE5D,CAD+B,IACG,EAC9C,EAAe,OAAe,CAAC,EAAc,EAAK,EAAQ,eAAe,EACzE,EAAyB,UAAO,GAChC,EADqC,EACL,QAAO,GACvC,EAAwB,cAAY,IAAO,EAAiB,SAAU,EAAQ,CAAC,CAAC,EAMtF,OAJM,YAAU,IACP,IAAM,SAAS,oBAAoB,YAAa,GACtD,CAAC,EAAgB,EAGlB,OAJsE,CAItE,EAAiB,CAHA,CAGA,GAAhB,CAAuB,SAAO,EAAE,GAAG,EAClC,mBAAC,IAAS,CAAC,OAAV,CAGC,mBAAkB,EAAQ,KAAO,EAAQ,UAAY,OACrD,aAAY,EAAQ,eACnB,GAAG,EACJ,IAAK,EACL,cAAe,OAAoB,CAAC,EAAM,cAAe,IAC7B,QAAS,EAA/B,EAAM,cAEP,EAAwB,SACxB,EAAD,qBAAiB,CAAsB,SACvC,CACA,EAAQ,eAAe,EACvB,EAAwB,QAAU,IAEtC,CAAC,EACD,eAAgB,OAAoB,CAAC,EAAM,eAAgB,KACzD,EAAQ,eAAe,EACvB,EAAwB,SAAU,CACpC,CAAC,EACD,cAAe,OAAoB,CAAC,EAAM,cAAe,KACnD,EAAQ,MAAM,EACR,QAAQ,EAElB,EAAiB,SAAU,EAC3B,SAAS,iBAAiB,YAAa,EAAiB,CAAE,MAAM,CAAK,CAAC,CACxE,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,KACvC,EAAkB,QAAS,GAAQ,OAAO,CAChD,CAAC,EACD,OAAQ,OAAoB,CAAC,EAAM,OAAQ,EAAQ,OAAO,EAC1D,QAAS,OAAoB,CAAC,EAAM,QAAS,EAAQ,OAAO,GAC9D,CACF,CAEJ,EAGF,GAAe,YAAc,EAM7B,IAAM,EAAc,gBAGd,CAAC,EAAgB,EAAgB,CAAI,EAAyC,EAAa,CAC/F,QADqC,GACzB,MACd,CAAC,EAgBK,EAA8C,IAClD,GAAM,gBAAE,aAAgB,WAAY,YAAU,EAAU,CAAI,EACtD,EAAU,EAAkB,EAAa,GAC/C,MACE,KAF2D,GAE3D,EAAC,GAAe,MAAO,aAAgB,EACrC,mBAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACvC,mBAAC,GAAe,CAAf,CAAgB,QAAO,aAAC,WACtB,EACH,EACF,EACF,CAEJ,EAEA,EAAc,YAAc,EAM5B,IAAM,EAAe,iBAWf,EAAuB,aAC3B,CAAC,EAAyC,KACxC,IAAM,EAAgB,EAAiB,EAAc,EAAM,cAAc,EACnE,YAAE,EAAa,EAAc,gBAAY,EAAO,MAAO,GAAG,EAAa,CAAI,EAC3E,EAAU,EAAkB,EAAc,EAAM,cAAc,EAEpE,MACE,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACtC,WAAQ,wBACP,UAAC,QAAmB,EAAa,GAAG,EAAc,IAAK,EAAc,EAErE,UAAC,QAAwB,EAAa,GAAG,EAAc,IAAK,EAAc,EAE9E,CAEJ,GASI,EAAgC,aAGpC,CAAC,EAAkD,KACnD,IAAM,EAAU,EAAkB,EAAc,EAAM,cAAc,EAC9D,EAAkB,EAA0B,EAAc,EAAM,cAAc,EAC9E,EAAY,SAAuC,IAAI,EACvD,EAAe,OAAe,CAAC,EAAc,GAAG,CAC/C,EAAkB,EAAmB,CAAU,WAAyB,IAAI,CAAvC,CAEtC,SAAE,UAAS,EAAQ,CAAI,EACvB,EAAU,EAAI,QAEd,0BAAE,EAAyB,CAAI,EAE/B,EAA8B,cAAY,KAC9C,EAAoB,IAAI,EACxB,GAAyB,EAC3B,EAAG,CAD6B,EACH,EAEvB,EAA8B,cAClC,CAAC,EAAqB,CAHI,IAIxB,IAAM,EAAgB,EAAM,cACtB,EAAY,CAAE,EAAG,EAAM,QAAS,EAAG,EAAM,SACzC,EAAW,SAqLd,CAAoB,CAAc,GAAqB,IACxD,EAAM,KAAK,IAAI,EAAK,IAAM,EAAM,CAAC,EACjC,EAAS,KAAK,IAAI,EAAK,OAAS,EAAM,CAAC,EACvC,EAAQ,KAAK,IAAI,EAAK,MAAQ,EAAM,CAAC,EACrC,EAAO,KAAK,IAAI,EAAK,KAAO,EAAM,CAAC,EAEzC,OAAQ,KAAK,IAAI,EAAK,EAAQ,EAAO,IAAI,GAAG,EACrC,EACH,MAAO,MACT,MAAK,EACH,MAAO,OACT,MAAK,EACH,MAAO,KACT,MAAK,EACH,MAAO,QACT,SACE,MAAM,MAAU,aAAa,CACjC,CACF,EAvM2C,EAAW,EAAc,sBAAsB,CAAC,EAIrF,EADkB,SAsQf,CAAyB,EAAsC,IAChE,EAAsB,EAAO,MAAM,CAtQR,CA8QjC,OAPA,EAAU,KAAK,CAAC,EAAU,IACxB,EAAM,EAAI,EAAE,EAAG,GACN,EAAE,EAAI,EAAE,EAAG,EACX,EAAE,EAAI,EAAE,EAAG,GACO,KAAlB,EAAE,EAAI,EAAE,EAAG,EAGf,SAIA,CAAkC,EAAsC,GAC3E,EAAO,QAAU,EAAG,OAAO,EAAO,MAAM,EAE5C,IAAM,EAAsB,CAAC,EAC7B,QAAS,EAAI,EAAG,EAAI,EAAO,OAAQ,IAAK,CACtC,IAAM,EAAI,EAAO,CAAC,EAClB,KAAO,EAAU,QAAU,GAAG,CAC5B,IAAM,EAAI,EAAU,EAAU,OAAS,CAAC,EAClC,EAAI,EAAU,EAAU,OAAS,CAAC,EACxC,IAAK,EAAE,EAAI,MAAQ,EAAE,EAAI,MAAE,CAAO,EAAE,EAAI,GAAE,GAAM,EAAE,EAAI,KAAM,EAAU,IAAI,OACrE,KACP,CACA,EAAU,KAAK,CAAC,CAClB,CACA,EAAU,IAAI,EAEd,IAAM,EAAsB,CAAC,EAC7B,QAAS,EAAI,EAAO,OAAS,EAAG,GAAK,EAAG,IAAK,CAC3C,IAAM,EAAI,EAAO,CAAC,EAClB,KAAO,EAAU,QAAU,GAAG,CAC5B,IAAM,EAAI,EAAU,EAAU,OAAS,CAAC,EAClC,EAAI,EAAU,EAAU,OAAS,CAAC,EACxC,IAAK,EAAE,EAAI,MAAQ,EAAE,EAAI,MAAE,CAAO,EAAE,EAAI,MAAQ,EAAE,EAAI,KAAM,EAAU,IAAI,OACrE,KACP,CACA,EAAU,KAAK,CAAC,CAClB,OAGA,CAFA,EAAU,IAAI,EAGZ,MAAU,QACW,IAArB,EAAU,QACV,EAAU,CAAC,EAAG,IAAM,EAAU,CAAC,EAAG,GAClC,EAAU,CAAC,EAAG,IAAM,EAAU,CAAC,EAAG,GAClC,EAGO,EAAU,OAAO,EAE5B,EA3C0B,EAC1B,EAhRgC,CAAC,GAsMjC,CAyEmC,QAzE1B,CAAoB,CAAkB,EAAgB,EAAU,GAAG,IACpE,EAA4B,CAAC,EACnC,OAAQ,GACN,IAAK,GADW,GAEd,EAAiB,KACf,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,EACrD,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,GAEvD,KACF,KAAK,SACH,EAAiB,KACf,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,EACrD,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,GAEvD,KACF,KAAK,OACH,EAAiB,KACf,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,EACrD,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,GAEvD,KACF,KAAK,QACH,EAAiB,KACf,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,EACrD,CAAE,EAAG,EAAU,EAAI,EAAS,EAAG,EAAU,EAAI,CAAQ,EAG3D,CACA,OAAO,CACT,EArOmD,EAAW,MAC9B,EADsC,OAuO3C,GAAe,GAClC,KAAE,QAAK,SAAO,OAAQ,EAAK,CAAI,EACrC,MAAO,CACL,CAAE,EAAG,EAAM,EAAG,CAAI,EAClB,CAAE,EAAG,EAAO,EAAG,CAAI,EACnB,CAAE,EAAG,EAAO,EAAG,CAAO,EACtB,CAAE,EAAG,EAAM,EAAG,CAAO,EAEzB,EA9OkD,EAAY,sBAAsB,CAAC,EACV,GAErE,GAAyB,EAC3B,EAD+B,CAE9B,EAAwB,EAyC3B,OAAO,EAtCD,UAAU,CAHW,GAIlB,IAAM,IACZ,CAAC,EAAsB,EAEpB,YAAU,CAHqB,IACZ,GAGnB,GAAW,EAAS,CACtB,IAAM,EAAqB,GAAyB,EAAsB,EAAO,GAC3E,EAAqB,EAD6D,CACpC,EAAsB,EAAO,GAIjF,IAJwF,GAExF,EAAQ,iBAAiB,eAAgB,GACzC,EAAQ,aADmD,GACnD,CAAiB,eAAgB,GAClC,KACL,EAAQ,QAFiD,WAEjD,CAAoB,eAAgB,GAC5C,EAAQ,aADsD,MACtD,CAAoB,eAAgB,EAC9C,CACF,CACF,EAAG,CAAC,EAAS,EAAS,EAAuB,EAAsB,EAE7D,CAL8D,CAK9D,UAAU,KAFkD,GAG5D,EAAkB,CACpB,IAAM,EAA2B,IAC/B,IAAM,EAAS,EAAM,OACf,EAAkB,CAAE,EAAG,EAAM,QAAS,EAAG,EAAM,SAC/C,EAAmB,GAAS,SAAS,IAAW,EAAL,CAAc,SAAS,GAClE,EAA4B,CAkN1C,SAAS,CAAiB,CAAc,GAAkB,GAClD,GAAE,IAAG,EAAE,CAAI,EACb,GAAS,EACb,QAAS,EAAI,EAAG,EAAI,EAAQ,OAAS,EAAG,EAAI,EAAQ,OAAQ,EAAI,IAAK,CACnE,IAAM,EAAK,EAAQ,CAAC,EACd,EAAK,EAAQ,CAAC,EACd,EAAK,EAAG,EACR,EAAK,EAAG,EACR,EAAK,EAAG,EACR,EAAK,EAAG,EAGM,EAAK,GAAQ,EAAK,EACvB,CAD+B,GAAK,EAAK,IAAO,EAAI,IAAO,EAAK,GAAM,GACtE,GAAS,CAAC,EAC3B,CAEA,OAAO,CACT,EAnO4D,EAAiB,GAEjE,EACF,IACS,IACT,GALmF,CAMnF,IAJoB,EAQxB,EAJY,EAHc,GAM1B,KAJ0B,CADc,GAK/B,iBAAiB,cAAe,GAClC,IAAM,SAAS,OAD0C,YAC1C,CAAoB,cAAe,EAC3D,CACF,EAAG,CAAC,EAAS,EAAS,EAAkB,EAAS,EAAsB,EAEhE,KAJ6E,GAI7E,EAAC,GAAoB,GAAG,CAFuC,CAEhC,IAAK,EAAc,CAC3D,CAAC,EAEK,CAAC,EAAsC,EAA+B,CAC1E,EAAqB,EAAc,CAAE,UAAU,CAAM,CAAC,EAuBlD,EAAY,OAxB0D,CAwB3C,CAAC,gBAAgB,EAE5C,EAA2B,aAC/B,CAAC,EAA6C,KAC5C,GAAM,gBACJ,WACA,EACA,aAAc,kBACd,uBACA,EACA,GAAG,EACL,CAAI,EACE,EAAU,EAAkB,EAAc,GAC1C,EAAc,EAAe,GAC7B,IAFwD,KAEtD,EADyC,CAC7B,EAoBpB,OACE,EAlBI,UAAU,KACd,SAAS,iBAAiB,EAAc,GACjC,IADwC,SACzB,oBAAoB,EAAc,IACvD,CAAC,EAD6D,EAI3D,GAHK,QAGL,CAAU,KACd,GAAI,EAAQ,QAAS,CACnB,IAAM,EAAe,IACnB,IAAM,EAAS,EAAM,OACjB,GAAQ,SAAS,EAAQ,OAAO,EAAG,IACzC,EAEA,GAHiD,IAEjD,OAAO,iBAAiB,SAAU,EAAc,CAAE,QAAS,EAAK,CAAC,EAC1D,IAAM,OAAO,oBAAoB,SAAU,EAAc,CAAE,SAAS,CAAK,CAAC,CACnF,CACF,EAAG,CAAC,EAAQ,QAAS,EAAQ,EAG3B,UAAC,IAAgB,CAAhB,CACC,QAAO,GACP,6BAA6B,kBAC7B,uBACA,EACA,eAAgB,GAAW,EAAM,eAAe,EAChD,UAAW,EAEX,oBAAiB,KAAhB,CACC,aAAY,EAAQ,eACnB,GAAG,EACH,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAa,MAGd,2CAA4C,uCAC5C,0CAA2C,sCAC3C,2CAA4C,uCAC5C,gCAAiC,mCACjC,iCAAkC,mCAEtC,EAEA,oBAAC,YAAW,EAAS,EACrB,UAAC,GAAqC,MAAO,EAAgB,UAAU,EACrE,mBAAyB,KAAxB,CAA6B,GAAI,EAAQ,UAAW,KAAK,UACvD,YAAa,EAChB,EACF,IACF,EAGN,GAGF,EAAe,YAAc,EAM7B,IAAM,EAAa,eAMb,EAAqB,aACzB,CAAC,EAAuC,KACtC,GAAM,gBAAE,EAAgB,GAAG,EAAW,CAAI,EACpC,EAAc,EAAe,EADG,CAQtC,OANqC,EACnC,EACA,GAIkC,SAAW,KAC7C,UAAiB,KAAhB,CAAuB,GAAG,EAAc,GAAG,EAAY,IAAK,EAAc,CAE/E,GAGF,EAAa,YAAc,EAgJ3B,IAAM,EAAW,EACXC,EAAO,EACP,EAAU,EACV,EAAS,EACTC,EAAU,EACVC,EAAQ", "sources": ["webpack://terang-lms-ui/../../../src/icons/image.ts", "webpack://terang-lms-ui/../src/dialog.tsx", "webpack://terang-lms-ui/../../../src/icons/x.ts", "webpack://terang-lms-ui/../../../src/icons/chevron-left.ts", "webpack://terang-lms-ui/../../../src/icons/book-open.ts", "webpack://terang-lms-ui/../../../src/icons/chevron-right.ts", "webpack://terang-lms-ui/../../../src/icons/book.ts", "webpack://terang-lms-ui/../../../src/icons/award.ts", "webpack://terang-lms-ui/../../../src/icons/chart-column.ts", "webpack://terang-lms-ui/../../../src/icons/file-text.ts", "webpack://terang-lms-ui/../src/roving-focus-group.tsx", "webpack://terang-lms-ui/../src/tooltip.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', ry: '2', key: '1m3agn' }],\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'm21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21', key: '1xmnt7' }],\n];\n\n/**\n * @component @name Image\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iOSIgcj0iMiIgLz4KICA8cGF0aCBkPSJtMjEgMTUtMy4wODYtMy4wODZhMiAyIDAgMCAwLTIuODI4IDBMNiAyMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/image\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Image = createLucideIcon('Image', __iconNode);\n\nexport default Image;\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('X', __iconNode);\n\nexport default X;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm15 18-6-6 6-6', key: '1wnfg3' }]];\n\n/**\n * @component @name ChevronLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtNi02IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronLeft = createLucideIcon('ChevronLeft', __iconNode);\n\nexport default ChevronLeft;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 7v14', key: '1akyts' }],\n  [\n    'path',\n    {\n      d: 'M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z',\n      key: 'ruj8y',\n    },\n  ],\n];\n\n/**\n * @component @name BookOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgN3YxNCIgLz4KICA8cGF0aCBkPSJNMyAxOGExIDEgMCAwIDEtMS0xVjRhMSAxIDAgMCAxIDEtMWg1YTQgNCAwIDAgMSA0IDQgNCA0IDAgMCAxIDQtNGg1YTEgMSAwIDAgMSAxIDF2MTNhMSAxIDAgMCAxLTEgMWgtNmEzIDMgMCAwIDAtMyAzIDMgMyAwIDAgMC0zLTN6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpen = createLucideIcon('BookOpen', __iconNode);\n\nexport default BookOpen;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('ChevronRight', __iconNode);\n\nexport default ChevronRight;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20',\n      key: 'k3hazp',\n    },\n  ],\n];\n\n/**\n * @component @name Book\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxOS41di0xNUEyLjUgMi41IDAgMCAxIDYuNSAySDE5YTEgMSAwIDAgMSAxIDF2MThhMSAxIDAgMCAxLTEgMUg2LjVhMSAxIDAgMCAxIDAtNUgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Book = createLucideIcon('Book', __iconNode);\n\nexport default Book;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526',\n      key: '1yiouv',\n    },\n  ],\n  ['circle', { cx: '12', cy: '8', r: '6', key: '1vp47v' }],\n];\n\n/**\n * @component @name Award\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUuNDc3IDEyLjg5IDEuNTE1IDguNTI2YS41LjUgMCAwIDEtLjgxLjQ3bC0zLjU4LTIuNjg3YTEgMSAwIDAgMC0xLjE5NyAwbC0zLjU4NiAyLjY4NmEuNS41IDAgMCAxLS44MS0uNDY5bDEuNTE0LTguNTI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iOCIgcj0iNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/award\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Award = createLucideIcon('Award', __iconNode);\n\nexport default Award;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['path', { d: 'M18 17V9', key: '2bz60n' }],\n  ['path', { d: 'M13 17V5', key: '1frdt8' }],\n  ['path', { d: 'M8 17v-3', key: '17ska0' }],\n];\n\n/**\n * @component @name ChartColumn\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHBhdGggZD0iTTE4IDE3VjkiIC8+CiAgPHBhdGggZD0iTTEzIDE3VjUiIC8+CiAgPHBhdGggZD0iTTggMTd2LTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chart-column\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartColumn = createLucideIcon('ChartColumn', __iconNode);\n\nexport default ChartColumn;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('FileText', __iconNode);\n\nexport default FileText;\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlottable } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P = {}> = P & { __scopeTooltip?: Scope };\nconst [createTooltipContext, createTooltipScope] = createContextScope('Tooltip', [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'TooltipProvider';\nconst DEFAULT_DELAY_DURATION = 700;\nconst TOOLTIP_OPEN = 'tooltip.open';\n\ntype TooltipProviderContextValue = {\n  isOpenDelayedRef: React.MutableRefObject<boolean>;\n  delayDuration: number;\n  onOpen(): void;\n  onClose(): void;\n  onPointerInTransitChange(inTransit: boolean): void;\n  isPointerInTransitRef: React.MutableRefObject<boolean>;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipProviderContextProvider, useTooltipProviderContext] =\n  createTooltipContext<TooltipProviderContextValue>(PROVIDER_NAME);\n\ninterface TooltipProviderProps {\n  children: React.ReactNode;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst TooltipProvider: React.FC<TooltipProviderProps> = (\n  props: ScopedProps<TooltipProviderProps>\n) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children,\n  } = props;\n  const isOpenDelayedRef = React.useRef(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n\n  return (\n    <TooltipProviderContextProvider\n      scope={__scopeTooltip}\n      isOpenDelayedRef={isOpenDelayedRef}\n      delayDuration={delayDuration}\n      onOpen={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        isOpenDelayedRef.current = false;\n      }, [])}\n      onClose={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => (isOpenDelayedRef.current = true),\n          skipDelayDuration\n        );\n      }, [skipDelayDuration])}\n      isPointerInTransitRef={isPointerInTransitRef}\n      onPointerInTransitChange={React.useCallback((inTransit: boolean) => {\n        isPointerInTransitRef.current = inTransit;\n      }, [])}\n      disableHoverableContent={disableHoverableContent}\n    >\n      {children}\n    </TooltipProviderContextProvider>\n  );\n};\n\nTooltipProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Tooltip\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLTIP_NAME = 'Tooltip';\n\ntype TooltipContextValue = {\n  contentId: string;\n  open: boolean;\n  stateAttribute: 'closed' | 'delayed-open' | 'instant-open';\n  trigger: TooltipTriggerElement | null;\n  onTriggerChange(trigger: TooltipTriggerElement | null): void;\n  onTriggerEnter(): void;\n  onTriggerLeave(): void;\n  onOpen(): void;\n  onClose(): void;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipContextProvider, useTooltipContext] =\n  createTooltipContext<TooltipContextValue>(TOOLTIP_NAME);\n\ninterface TooltipProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened. This will\n   * override the prop with the same name passed to Provider.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst Tooltip: React.FC<TooltipProps> = (props: ScopedProps<TooltipProps>) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp,\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState<HTMLButtonElement | null>(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent =\n    disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: (open) => {\n      if (open) {\n        providerContext.onOpen();\n\n        // as `onChange` is called within a lifecycle method we\n        // avoid dispatching via `dispatchDiscreteCustomEvent`.\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open);\n    },\n    caller: TOOLTIP_NAME,\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? (wasOpenDelayedRef.current ? 'delayed-open' : 'instant-open') : 'closed';\n  }, [open]);\n\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    setOpen(false);\n  }, [setOpen]);\n\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n      openTimerRef.current = 0;\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n\n  React.useEffect(() => {\n    return () => {\n      if (openTimerRef.current) {\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n      }\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <TooltipContextProvider\n        scope={__scopeTooltip}\n        contentId={contentId}\n        open={open}\n        stateAttribute={stateAttribute}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        onTriggerEnter={React.useCallback(() => {\n          if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n          else handleOpen();\n        }, [providerContext.isOpenDelayedRef, handleDelayedOpen, handleOpen])}\n        onTriggerLeave={React.useCallback(() => {\n          if (disableHoverableContent) {\n            handleClose();\n          } else {\n            // Clear the timer in case the pointer leaves the trigger before the tooltip is opened.\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n          }\n        }, [handleClose, disableHoverableContent])}\n        onOpen={handleOpen}\n        onClose={handleClose}\n        disableHoverableContent={disableHoverableContent}\n      >\n        {children}\n      </TooltipContextProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nTooltip.displayName = TOOLTIP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TooltipTrigger';\n\ntype TooltipTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TooltipTriggerProps extends PrimitiveButtonProps {}\n\nconst TooltipTrigger = React.forwardRef<TooltipTriggerElement, TooltipTriggerProps>(\n  (props: ScopedProps<TooltipTriggerProps>, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef<TooltipTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => (isPointerDownRef.current = false), []);\n\n    React.useEffect(() => {\n      return () => document.removeEventListener('pointerup', handlePointerUp);\n    }, [handlePointerUp]);\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          // We purposefully avoid adding `type=button` here because tooltip triggers are also\n          // commonly anchors and the anchor `type` attribute signifies MIME type.\n          aria-describedby={context.open ? context.contentId : undefined}\n          data-state={context.stateAttribute}\n          {...triggerProps}\n          ref={composedRefs}\n          onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n            if (event.pointerType === 'touch') return;\n            if (\n              !hasPointerMoveOpenedRef.current &&\n              !providerContext.isPointerInTransitRef.current\n            ) {\n              context.onTriggerEnter();\n              hasPointerMoveOpenedRef.current = true;\n            }\n          })}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, () => {\n            context.onTriggerLeave();\n            hasPointerMoveOpenedRef.current = false;\n          })}\n          onPointerDown={composeEventHandlers(props.onPointerDown, () => {\n            if (context.open) {\n              context.onClose();\n            }\n            isPointerDownRef.current = true;\n            document.addEventListener('pointerup', handlePointerUp, { once: true });\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            if (!isPointerDownRef.current) context.onOpen();\n          })}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          onClick={composeEventHandlers(props.onClick, context.onClose)}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nTooltipTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'TooltipPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createTooltipContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface TooltipPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipPortal: React.FC<TooltipPortalProps> = (props: ScopedProps<TooltipPortalProps>) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return (\n    <PortalProvider scope={__scopeTooltip} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nTooltipPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TooltipContent';\n\ntype TooltipContentElement = TooltipContentImplElement;\ninterface TooltipContentProps extends TooltipContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipContent = React.forwardRef<TooltipContentElement, TooltipContentProps>(\n  (props: ScopedProps<TooltipContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = 'top', ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.disableHoverableContent ? (\n          <TooltipContentImpl side={side} {...contentProps} ref={forwardedRef} />\n        ) : (\n          <TooltipContentHoverable side={side} {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\n\ntype TooltipContentHoverableElement = TooltipContentImplElement;\ninterface TooltipContentHoverableProps extends TooltipContentImplProps {}\n\nconst TooltipContentHoverable = React.forwardRef<\n  TooltipContentHoverableElement,\n  TooltipContentHoverableProps\n>((props: ScopedProps<TooltipContentHoverableProps>, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef<TooltipContentHoverableElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState<Polygon | null>(null);\n\n  const { trigger, onClose } = context;\n  const content = ref.current;\n\n  const { onPointerInTransitChange } = providerContext;\n\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n\n  const handleCreateGraceArea = React.useCallback(\n    (event: PointerEvent, hoverTarget: HTMLElement) => {\n      const currentTarget = event.currentTarget as HTMLElement;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event: PointerEvent) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event: PointerEvent) => handleCreateGraceArea(event, trigger);\n\n      trigger.addEventListener('pointerleave', handleTriggerLeave);\n      content.addEventListener('pointerleave', handleContentLeave);\n      return () => {\n        trigger.removeEventListener('pointerleave', handleTriggerLeave);\n        content.removeEventListener('pointerleave', handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event: PointerEvent) => {\n        const target = event.target as HTMLElement;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener('pointermove', handleTrackPointerGrace);\n      return () => document.removeEventListener('pointermove', handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n\n  return <TooltipContentImpl {...props} ref={composedRefs} />;\n});\n\nconst [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] =\n  createTooltipContext(TOOLTIP_NAME, { isInside: false });\n\ntype TooltipContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface TooltipContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * A more descriptive label for accessibility purpose\n   */\n  'aria-label'?: string;\n\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `Tooltip`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n}\n\nconst Slottable = createSlottable('TooltipContent');\n\nconst TooltipContentImpl = React.forwardRef<TooltipContentImplElement, TooltipContentImplProps>(\n  (props: ScopedProps<TooltipContentImplProps>, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      'aria-label': ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n\n    // Close this tooltip if another one opens\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n\n    // Close the tooltip if the trigger is scrolled\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event: Event) => {\n          const target = event.target as HTMLElement;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener('scroll', handleScroll, { capture: true });\n        return () => window.removeEventListener('scroll', handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n\n    return (\n      <DismissableLayer\n        asChild\n        disableOutsidePointerEvents={false}\n        onEscapeKeyDown={onEscapeKeyDown}\n        onPointerDownOutside={onPointerDownOutside}\n        onFocusOutside={(event) => event.preventDefault()}\n        onDismiss={onClose}\n      >\n        <PopperPrimitive.Content\n          data-state={context.stateAttribute}\n          {...popperScope}\n          {...contentProps}\n          ref={forwardedRef}\n          style={{\n            ...contentProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n              '--radix-tooltip-content-transform-origin': 'var(--radix-popper-transform-origin)',\n              '--radix-tooltip-content-available-width': 'var(--radix-popper-available-width)',\n              '--radix-tooltip-content-available-height': 'var(--radix-popper-available-height)',\n              '--radix-tooltip-trigger-width': 'var(--radix-popper-anchor-width)',\n              '--radix-tooltip-trigger-height': 'var(--radix-popper-anchor-height)',\n            },\n          }}\n        >\n          <Slottable>{children}</Slottable>\n          <VisuallyHiddenContentContextProvider scope={__scopeTooltip} isInside={true}>\n            <VisuallyHiddenPrimitive.Root id={context.contentId} role=\"tooltip\">\n              {ariaLabel || children}\n            </VisuallyHiddenPrimitive.Root>\n          </VisuallyHiddenContentContextProvider>\n        </PopperPrimitive.Content>\n      </DismissableLayer>\n    );\n  }\n);\n\nTooltipContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'TooltipArrow';\n\ntype TooltipArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface TooltipArrowProps extends PopperArrowProps {}\n\nconst TooltipArrow = React.forwardRef<TooltipArrowElement, TooltipArrowProps>(\n  (props: ScopedProps<TooltipArrowProps>, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    // if the arrow is inside the `VisuallyHidden`, we don't want to render it all to\n    // prevent issues in positioning the arrow due to the duplicate\n    return visuallyHiddenContentContext.isInside ? null : (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    );\n  }\n);\n\nTooltipArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype Side = NonNullable<TooltipContentProps['side']>;\n\nfunction getExitSideFromRect(point: Point, rect: DOMRect): Side {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return 'left';\n    case right:\n      return 'right';\n    case top:\n      return 'top';\n    case bottom:\n      return 'bottom';\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nfunction getPaddedExitPoints(exitPoint: Point, exitSide: Side, padding = 5) {\n  const paddedExitPoints: Point[] = [];\n  switch (exitSide) {\n    case 'top':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'bottom':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case 'left':\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'right':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\n\nfunction getPointsFromRect(rect: DOMRect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n  ];\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\n// Returns a new array of points representing the convex hull of the given set of points.\n// https://www.nayuki.io/page/convex-hull-algorithm\nfunction getHull<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  const newPoints: Array<P> = points.slice();\n  newPoints.sort((a: Point, b: Point) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return +1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return +1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\n\n// Returns the convex hull, assuming that each points[i] <= points[i + 1]. Runs in O(n) time.\nfunction getHullPresorted<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  if (points.length <= 1) return points.slice();\n\n  const upperHull: Array<P> = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i]!;\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1]!;\n      const r = upperHull[upperHull.length - 2]!;\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n\n  const lowerHull: Array<P> = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i]!;\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1]!;\n      const r = lowerHull[lowerHull.length - 2]!;\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n\n  if (\n    upperHull.length === 1 &&\n    lowerHull.length === 1 &&\n    upperHull[0]!.x === lowerHull[0]!.x &&\n    upperHull[0]!.y === lowerHull[0]!.y\n  ) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\n\nconst Provider = TooltipProvider;\nconst Root = Tooltip;\nconst Trigger = TooltipTrigger;\nconst Portal = TooltipPortal;\nconst Content = TooltipContent;\nconst Arrow = TooltipArrow;\n\nexport {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  TooltipProviderProps,\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n};\n"], "names": ["open", "Root", "Content", "Arrow"], "sourceRoot": ""}