{"version": 3, "file": "../app/dashboard/product/[productId]/page.js", "mappings": "4lCAqBO,SAASA,IAEZ,MAAM,qBAEL,CAFK,MACH,+GADG,+<PERSON><PERSON><PERSON>,EAOJ,sFAXgBA,qCAAAA,KAFEC,EAhBX,OAgBWA,8BAA8B,GAAC,4OCnBjD,iHCEA,YAAkB,IAElB,SAAe,eACf,SACA,sCAEA,gBACA,SAGA,iBACA,6BACA,wBACA,0BACA,mCAEA,kBACA,4BACQ,iBAER,0BAGA,KACA,CAAK,CACL,CAEA,QACA,gGCzBA,IAAMC,EAAWC,EAAAA,UAAgB,CAAiH,CAAC,WACjJC,CAAS,OACTC,CAAK,CACL,GAAGC,EACJ,CAAEC,IAAQ,UAACC,EAAAA,EAAsB,EAACD,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEL,GAAa,GAAGE,CAAK,UAC7I,UAACE,EAAAA,EAA2B,EAACJ,UAAU,iDAAiDM,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,KAAON,CAAAA,GAAS,EAAG,EAAE,CAAC,OAGpDH,EAASU,WAAW,CAAGJ,EAAAA,EAAsB,CAACI,WAAW,wBCdzD,0ECoBM,MAAS,cAAiB,UAjBI,CAClC,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,UAAY,EAAE,OAAQ,CAAiB,mBAAK,SAAU,EACvD,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,GAAK,IAAI,IAAM,KAAK,SAAU,EACnE,qICLA,SAASC,EAAK,CACZT,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,OAAOX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBd,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,WACjBf,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,aAAaX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBhB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,mBAAmBX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBjB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,eAAeX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBlB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,8FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,0ECLjN,SAASM,EAAS,WAChBnB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,WAAWX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,WAAWC,0BAAwB,gBACvK,0BCNA,mECAA,0GCAA,+ICsBgBO,qCAAAA,KAFhB,IAAMC,EAAU,GAAExB,EAjBX,OAiBWA,8BAA8B,CAAC,OAE1C,SAASuB,IAEd,IAAME,EAAQ,qBAAiB,CAAjB,MAAUD,GAAV,+DAAgB,EAG9B,OAFEC,EAAkCC,MAAM,CAAGF,EAEvCC,CACR,yTCvBA,SAASE,EAAM,WACbxB,CAAS,CACT,GAAGE,EAC8C,EACjD,MAAO,UAACuB,EAAAA,CAAmB,EAACd,YAAU,QAAQX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sNAAuNL,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,sBAAsBd,wBAAsB,QAAQC,0BAAwB,aAC5Y,0BCVA,gDCAA,kDCAA,gDCAA,iGCAA,uCAAiM,CAEjM,sCAA+M,yBCF/M,gECAA,kDCAA,iECAA,uDCAA,gDCAA,uCAAiM,CAEjM,uCAA+M,8InBA/M,SAASJ,EAAK,WACZT,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,OAAOX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBd,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,WACjBf,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,aAAaX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBhB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,mBAAmBX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,kBAAkBC,0BAAwB,YACjL,CACA,SAASc,EAAW,WAClB3B,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACxM,CACA,SAASI,EAAY,WACnBjB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,eAAeX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBlB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACjL,0BoB3CA,sDCAA,0JCOae,qCAAAA,KAAN,IAAMA,EAGLC,EAAAA,OAAAA,QAFN,KAA6B,GAEvBA,CACgB,GAEhBA,CACgB,0OCdxB,gJCKA,SAASC,EAAW,CAClB9B,WAAS,UACT+B,CAAQ,CACR,GAAG7B,EACmD,EACtD,MAAO,WAAC8B,EAAAA,EAAwB,EAACrB,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYL,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,2BAA2Bd,wBAAsB,aAAaC,0BAAwB,4BAChN,UAACmB,EAAAA,EAA4B,EAACrB,YAAU,uBAAuBX,UAAU,qJAAqJ0B,sBAAoB,+BAA+Bb,0BAAwB,2BACtSkB,IAEH,UAACE,EAAAA,CAAUP,sBAAoB,YAAYb,0BAAwB,oBACnE,UAACmB,EAAAA,EAA0B,EAACN,sBAAoB,6BAA6Bb,0BAAwB,sBAE3G,CACA,SAASoB,EAAU,WACjBjC,CAAS,aACTkC,EAAc,UAAU,CACxB,GAAGhC,EACkE,EACrE,MAAO,UAAC8B,EAAAA,EAAuC,EAACrB,YAAU,wBAAwBuB,YAAaA,EAAalC,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsE,aAAhB6B,GAA8B,6CAA8D,eAAhBA,GAAgC,+CAAgDlC,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,0CAA0Cd,wBAAsB,YAAYC,0BAAwB,2BACvd,UAACmB,EAAAA,EAAmC,EAACrB,YAAU,oBAAoBX,UAAU,yCAAyC0B,sBAAoB,sCAAsCb,0BAAwB,qBAE9M,0BC1BA,oICEe,SAASsB,EAAc,UACpCJ,CAAQ,YACRK,GAAa,CAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAACN,EAAAA,UAAUA,CAAAA,CAAC9B,UAAU,iCAChC,UAACU,MAAAA,CAAIV,UAAU,mCAA2B+B,MAC5B,UAACrB,MAAAA,CAAIV,UAAU,mCAA2B+B,KAElE,0BCdA,kKCAO,eAEP,uDACA,8BACA,sBACA,sBACA,oBACA,sBACA,oBACA,qCACA,uCACA,sCACA,uCACA,sBACA,uCACA,uCACA,gCACA,6CACA,oBACA,+CACA,uCACA,mCACA,oCACA,sBACA,qCACA,mCACA,qCACA,wCACA,yBACA,uBACA,wBACA,wBACA,sEACA,kCACA,oCACA,oBACA,kDACA,sBACA,mCACA,+CACA,yCACA,gCACA,4BACA,oCACA,yBACA,qBACA,4CACA,yBACA,kCACA,gCACA,sCACA,8CACA,sCACA,+CACA,oBACA,0BACA,sBACA,gCACA,gDACA,gDACA,2CACA,uCACA,4BACA,mCACA,gCACA,iCACA,0CACA,8BACA,sCACA,2CACA,mCACA,8BACA,gCACA,8BACA,6CACA,oBACA,sCACA,6CACA,8BACA,mCACA,oCACA,8CACA,0BACA,sCACA,4BACA,8BACA,iBACA,wCACA,wCACA,wCACA,wCACA,wCACA,0DACA,8DACA,4CACA,sBACA,uCACA,mCACA,wCACA,4BACA,4BACA,4BACA,4BACA,4BACA,kBACA,8BACA,iCACA,kCACA,2CACA,4BACA,+BACA,gCACA,+CACA,wCACA,uCACA,oCACA,oCACA,mCACA,0BACA,yBACA,yCACA,qCACA,gCACA,uCACA,oBACA,8BACA,sCACA,sCACA,yBACA,iEACA,sCACA,2BACA,mCACA,qCACA,kDACA,iDACA,kDACA,kDACA,yCACA,+BACA,sCACA,2BACA,yBACA,kDACA,sBACA,kCACA,+BACA,mCACA,sBACA,8BACA,mBACA,qCACA,mCACA,+BACA,qCACA,yCACA,gDACA,4BACA,mDACA,2BACA,sCACA,mCACA,mBACA,iCACA,mBACA,8BACA,yBACA,8BACA,iCACA,mBACA,gCACA,qCACA,gCACA,yCACA,wCACA,8BACA,kCACA,iCACA,gCACA,sCACA,wCACA,2CACA,2BACA,uCACA,qBACA,sCACA,qCACA,wCACA,uCACA,mBACA,iCACA,qCACA,gEACA,oCACA,qCACA,yBACA,0BACA,mCACA,wCACA,mCACA,uCACA,mCACA,8BACA,6BACA,4DACA,mFACA,6BACA,4DACA,mFACA,iCACA,kCACA,wBACA,2BACA,6BACA,gCACA,mCACA,8BACA,wBACA,6BACA,oCACA,6BACA,4BACA,mCACA,wBACA,wBACA,wBACA,uCACA,iCACA,mCACA,0CACA,0CACA,0CACA,kCACA,uCACA,uCACA,kCACA,oCACA,mCACA,oBACA,yBACA,gCACA,0CACA,mCACA,kCACA,wCACA,iCACA,gCACA,gCACA,uCACA,yCACA,oCACA,uCACA,wBACA,4BACA,8BACA,mCACA,0BACA,8BACA,qBACA,uCACA,kCACA,sCACA,wCACA,uBACA,oBACA,yBACA,yBACA,iCACA,mDACA,mCACA,8BACA,8BACA,uDACA,0CACA,iCACA,0BACA,2BACA,2BACA,2BACA,2BACA,6BACA,sBACA,wBACA,sBACA,yCACA,sBACA,oCACA,gCACA,uBACA,oCACA,sCACA,qDACA,yBACA,wBACA,uCACA,wCACA,wBACA,wCACA,8DACA,wBACA,oCACA,qCACA,qCACA,kCACA,qBACA,mCACA,yCACA,6BACA,wCACA,uCACA,wBACA,gDACA,kCACA,mCACA,4CACA,wCACA,wCACA,sCACA,oBACA,kDACA,4BACA,2BACA,8BACA,8BACA,sCACA,+BACA,qCACA,8BACA,iCACA,iCACA,4BACA,sCACA,4CACA,0CACA,iCACA,yCACA,qDACA,uDACA,8BACA,8CACA,wBACA,2BACA,0BACA,kCACA,0BACA,4BACA,iBACA,sBACA,sBACA,sBACA,kCACA,gCACA,qCACA,uCACA,4BACA,sBACA,gCACA,sBACA,gCACA,uBACA,qCACA,kBACA,8BACA,6BACA,mCACA,mCACA,iCACA,mCACA,sBACA,2BACA,sCACA,oBACA,qBACA,wCACA,0CACA,2CACA,mCACA,qCACA,kCACA,qCACA,uBACA,wBACA,oBACA,wBACA,kDACA,sBACA,mCACA,qCACA,qBACA,yCACA,qDACA,mCACA,4CACA,iCACA,oBACA,qBACA,gCACA,kCACA,iDACA,iDACA,8BACA,iDACA,gDACA,kDACA,sCACA,sDACA,8BACA,0CACA,0CACA,2CACA,qBACA,8BACA,mCACA,8CACA,8BACA,qBACA,gCACA,oBACA,kCACA,sBACA,wCACA,8CACA,oBACA,qBACA,sBACA,oBACA,qBACA,qBACA,qBACA,sBACA,oBACA,oBACA,oBACA,gCACA,4BACA,8BACA,iCAEA,8BACA,qCACA,mBACA,oBACA,sBACA,sBACA,oBACA,sBACA,sBACA,sBACA,qBACA,wCACA,mCACA,kCACA,6CACA,uCACA,uCACA,+CACA,2CACA,gCACA,gCACA,sCACA,yCACA,yCACA,uCACA,sCACA,kCACA,oBACA,sBACA,kCACA,oCACA,oCACA,yCACA,gCACA,6DACA,kEACA,4CACA,qBACA,8BACA,mCACA,gDACA,sBACA,0CACA,yCACA,kCACA,oCACA,qBACA,mCACA,mCACA,iCACA,sCACA,qBACA,sCACA,iCACA,wCACA,mCACA,qBACA,qBACA,qBACA,qBACA,qBACA,yCACA,sBACA,0BACA,4BACA,kCACA,sBACA,oCACA,oCACA,2BACA,iCACA,gCACA,mCACA,uCACA,uCACA,qBACA,mCACA,2BACA,mCACA,6BACA,oCACA,iCACA,qCACA,4BACA,sCACA,8BACA,gCACA,uBACA,iCACA,4BACA,mBACA,oBACA,sBACA,sCACA,wCACA,gCACA,+BACA,oCACA,iDACA,2CACA,qBACA,sBACA,4BACA,8BACA,0BACA,oBACA,qBACA,iCACA,4BACA,2BACA,0BACA,2BACA,2BACA,oCACA,+CACA,+BACA,sBACA,yCACA,sBACA,gCACA,0CACA,gCACA,0BACA,8BACA,qBACA,sBACA,qBACA,oBACA,qBACA,2BACA,qBACA,4BACA,6CACA,+BACA,qBACA,sBACA,qBACA,qBACA,sBACA,+CACA,4CACA,6CACA,qCACA,qCACA,sCACA,qCACA,2BACA,mCACA,oBACA,+CACA,uCACA,gCACA,oCACA,qCACA,qBACA,mCACA,qCACA,mCACA,mCACA,uCACA,oBACA,wBACA,mCACA,mCACA,sDACA,oCACA,6CACA,+BACA,0BACA,6CACA,4BACA,8BACA,uCACA,4BACA,0DACA,iBACA,iCACA,yCACA,8BACA,mCACA,qBACA,8CACA,gCACA,4CACA,kCACA,6CACA,0CACA,uCACA,4BACA,6BACA,mCACA,sCACA,+BACA,+BACA,qDACA,4BACA,yCACA,yCACA,wCACA,iCACA,6CACA,oBACA,0BACA,sDACA,mDACA,qDACA,+DACA,sDACA,mDACA,yDACA,0DACA,yDACA,kDACA,oBACA,6BACA,oBACA,oBACA,0BACA,kCACA,iCACA,iCACA,iCACA,kCACA,wCACA,uBACA,gCACA,qBACA,qBACA,iDACA,+DACA,iDACA,4DACA,mBACA,+DACA,sDACA,4DACA,mEACA,kEACA,2DACA,uCACA,uCACA,8BACA,4BACA,kDACA,sBACA,wCACA,2CACA,iCACA,iCACA,0CACA,sCACA,2BACA,+BACA,+BACA,4CACA,+CACA,wBACA,oCACA,wCACA,kCACA,wCACA,iCACA,iCACA,qCACA,uBACA,uCACA,sBACA,8BACA,4BACA,0BACA,uCACA,mCACA,mCACA,mCACA,iCACA,+BACA,mCACA,kCACA,0BACA,kCACA,mCACA,mCACA,0CACA,oCACA,uBACA,mCACA,8BACA,uCACA,0CACA,4BACA,4CACA,qCACA,sCACA,8BACA,4BACA,oCACA,oBACA,kCACA,+CACA,wCACA,sEACA,iFACA,wCACA,+DACA,mCACA,kCACA,wCACA,mEACA,kFACA,iCACA,sEACA,qFACA,+BACA,8BACA,0CACA,iCACA,uCACA,gCACA,4CACA,kCACA,uCACA,mCACA,wBACA,qCACA,oCACA,0CACA,2CACA,2CACA,2CACA,2CACA,mCACA,mCACA,gDACA,yBACA,4CACA,4CACA,4CACA,4CACA,4CACA,4CACA,2BACA,+BACA,iCACA,qCACA,4BACA,6BACA,sDACA,8BACA,0CACA,uCACA,0CACA,wCACA,sBACA,kCACA,wBACA,8CACA,wCACA,yCACA,8CACA,8BACA,qBACA,sCACA,gDACA,4CACA,8CACA,0BACA,+BACA,sBACA,sCACA,sCACA,+CACA,8CACA,kCACA,sCACA,8BACA,qCACA,8BACA,0CACA,8BACA,mBACA,wBACA,iCACA,qCACA,gCACA,mBACA,oBACA,4CACA,uBACA,gCACA,8CACA,mCACA,0CACA,sCACA,uCACA,uBACA,gCACA,4CACA,4CACA,+CACA,2CACA,2CACA,0BACA,8CACA,mCACA,kCACA,qCACA,gCACA,gCACA,gCACA,mCACA,qCACA,6CACA,gDACA,qDACA,qDACA,uCACA,qBACA,oBACA,qDACA,oBACA,qBACA,0BACA,8BACA,qBACA,8BACA,sBACA,8BACA,8BACA,oCACA,qBACA,sBACA,0CACA,2CACA,gCACA,kCACA,4BACA,+BACA,+BACA,+BACA,+BACA,+DACA,8EACA,qBACA,oBACA,uCACA,qCACA,6CACA,4CACA,2BACA,4BACA,sBACA,8CACA,sBACA,iCACA,kCACA,2CACA,qBACA,6CACA,qCACA,8BACA,uCACA,sCACA,oBACA,4BACA,oCACA,+BACA,8BACA,yCACA,gCACA,2CACA,oCACA,gCACA,mCACA,8CACA,gDACA,gDACA,iCACA,mDACA,kCACA,oBACA,0BACA,+BACA,0BACA,oCACA,kDACA,uBACA,yBACA,gCACA,uCACA,wCACA,oCACA,kCACA,sCACA,8BACA,wBACA,yBACA,iCACA,wCACA,6CACA,mCACA,uCACA,uCACA,gDACA,0CACA,uCACA,yCACA,mBACA,kCACA,oBACA,mCACA,oDACA,gCACA,4BACA,sCACA,4BACA,wCACA,4CACA,8BACA,oCACA,4BACA,iCACA,oCACA,sBACA,iCACA,gCACA,wBACA,sBACA,4BACA,0CACA,qBACA,sBACA,2BACA,yCACA,4BACA,uCACA,+CACA,mCACA,oBACA,kCACA,4BACA,mCACA,oBACA,uCACA,oCACA,0BACA,mBACA,sBACA,gCACA,6CACA,8CACA,2CACA,qCACA,qBACA,2CACA,mCACA,oDACA,2BACA,uCACA,6BACA,wCACA,+BACA,gCACA,8BACA,iCACA,qCACA,oCACA,wBACA,yBACA,yBACA,8BACA,gCACA,oCACA,yBACA,+BACA,oCACA,oCACA,iCACA,4BACA,iCACA,gCACA,4BACA,4BACA,wCACA,6BACA,+BACA,gCACA,qCACA,qCACA,kCACA,6BACA,kCACA,iCACA,6BACA,6BACA,yCACA,8BACA,gCACA,4CACA,oCACA,2CACA,mCACA,yCACA,yDACA,uBACA,+BACA,uBACA,uCACA,2BACA,8BACA,uCACA,4BACA,uCACA,oCACA,yBACA,+BACA,yCACA,yBACA,8CACA,uCACA,sBACA,gCACA,8BACA,gCACA,gCACA,gCACA,yCACA,mBACA,wBACA,oCACA,iCACA,6BACA,wCACA,mCACA,4BACA,sBACA,yBACA,8BACA,gDACA,8BACA,mCACA,mCACA,6BACA,sBACA,iDACA,sBACA,4CACA,sBACA,oCACA,6BACA,mCACA,uBACA,yBACA,+BACA,oBACA,2BACA,4BACA,kCACA,2CACA,yBACA,yBACA,mCACA,qBACA,uBACA,8BACA,sCACA,iCACA,mCACA,8BACA,gCACA,qBACA,8BACA,gCACA,wCACA,mCACA,yBACA,wBACA,iCACA,6BACA,0BACA,2BACA,yBACA,uCACA,8CACA,4CACA,gCACA,wCACA,+BACA,mCACA,iCACA,qDACA,0BACA,oCACA,mCACA,oCACA,wCACA,wCACA,iCACA,8CACA,kCACA,gCACA,0CACA,sCACA,gCACA,gCACA,kCACA,+BACA,yBACA,2BACA,mCACA,0DACA,mCACA,gCACA,mCACA,mCACA,iEACA,0DACA,6EACA,mCACA,6DACA,gFACA,mCACA,kBACA,0BACA,kCACA,oCACA,8BACA,kCACA,gCACA,0BACA,iCACA,yCACA,2CACA,2CACA,0BACA,0BACA,gCACA,qCACA,gCACA,0CACA,6BACA,8BACA,8BACA,yBACA,0BACA,qBACA,4BACA,8BACA,oBACA,0BACA,+BACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,gCACA,yCACA,0BACA,8BACA,+BACA,qDACA,2BACA,EACO,kBACP,MAyBA,YACA,SAAY,GAAO,EAEnB,GADA,4BACA,SACA,mBACA,oBACA,UACA,IACA,gCACA,QACA,YACA,gBACA,aACA,CAAa,CAEb,CACA,QACA,EA1CA,GACA,oBAAY,GAAqB,EACjC,qBACA,EAIA,+BACA,EACA,KAAmB,OAAU,EAc7B,MAbA,YAAsC,KAAtC,QACA,cAEA,YACA,kCACA,QACA,YACA,gBACA,aACA,CAAS,EAGT,sBACA,CACA,CAmBA,kBACA,2BACA,QACA,YACA,gBACA,aACA,CAAK,CACL,CCpuCA,OAEA,YACA,YACA,CA+BA,cACA,kCACA,CA8BA,cACA,0CACA,CAKA,cACA,YACA,SAEA,SAEA,YAAoB,WAAkB,KACtC,WACA,SACA,CACA,QACA,CAEA,cACA,yCACA,YAEA,kCAIA,iBACA,KAEA,MACA,CAOA,gBACA,MAAW,QAAS,gCACpB,MAOA,2EACA,sCACA,YACA,eAAmC,GAAM,gBAIzC,eACA,wBAEA,OADA,WACuB,EAAc,EACrC,CACA,CACA,oBACA,MACA,eAA+B,GAAM,gBAGrC,OADoB,EAAc,+CAElC,CAAK,CACL,CAEA,cACA,MAAW,QAAS,gCACpB,mCAoCA,GACA,MAAW,QAAS,gCACpB,2BACA,WAEA,EAD4B,EAAc,cAE1C,CAAa,KACb,IACA,CAAa,CACb,CAAS,CACT,CAAK,CACL,EA/CA,EACA,CAAK,CACL,CAEA,cACA,uBACA,2BACA,UACA,aAGA,iBAA0C,QAAS,gCACnD,YAUA,CACA,4BACA,UAEA,GACA,MAbA,IACA,2BACA,IACA,CACA,SACA,IACA,CAQA,CAAa,MACb,IACA,CAAa,CACb,GAEA,CAAK,CACL,eE/KA,cAAmC,gBAMnC,GAAmC,iCANA,aAInC,GAAkC,qGAJC,qBAEH,0JAFG,CAQnC,gBAA2C,qBAAgC,iCAAoC,sCAAoD,2BAA6D,wDAAiE,oBAAsC,SAEvU,cAAiC,YAAgB,mBAAsB,KAAO,yCAAuD,wCAA6D,YAA4C,mIAAoK,gEAAmF,EAAK,SAE1e,kBAAoM,EAAxJ,YAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,EAIpM,gBAAkC,OAUlC,YAAgC,8BAVE,aAQlC,KAAyC,IAAgL,IAAhL,+EAA0G,YAAwB,SAAe,KAAe,KAA4B,IAAZ,IAAkB,YAAwB,yBAA4C,gBAAqB,kBAAlC,MAAkC,CAAuC,SAAc,KAAW,KAAY,OAAU,IAAM,+BAAmD,OAAU,cAAsB,WARjd,yBAEJ,+JAFI,CAIlC,gBAAkD,MAAgB,oCAAgE,oDAAqH,CAA7D,sDAA6D,0CAAsD,sFAE7S,gBAAuC,oCAAuD,uBAAuC,IAAS,IAAO,UAAoB,SAOzK,yBAAqB,EAAQ,EAA0B,EAAG,OAAgB,CAiBnE,CAjBqE,CAiBrE,WACP,gEACA,eACA,iDACA,OACA,KApBO,oBAqBP,sCACA,CACA,EACO,cACP,OACA,KAzBO,iBA0BP,yEACA,CACA,EACO,cACP,OACA,KA9BO,iBA+BP,0EACA,CACA,EACO,GACP,KAlCO,iBAmCP,wBACA,EAYO,gBACP,gDACA,sBAEO,kBACP,aACA,gBACA,4BACA,6BACM,sCAAqG,uCAG3G,gBAGA,cACA,cACA,CA4CO,oBACP,0CACA,yBACI,yBACJ,eAKO,qBACP,eAMA,2DACA,+CACA,CAAG,EAPH,4BAQA,CAKO,cACP,kBACA,CAyBO,aACP,0CAAqE,IAAa,IAClF,kBAGA,mBACA,kDAA+F,IAAe,IAC9G,oBAGA,0BAKA,MAJA,UACA,8BAGA,IACA,CAAK,CACL,CACA,CA0GO,cACP,8GACA,CAMO,cACP,2BACA,CCxUA,mBACA,WACA,+GACA,kCAUA,SAAS,EAAc,KAAW,OAAO,EAAlB,OAUd,CAAe,EAAQ,KAVwB,IAUxB,KAAR,EAAQ,cAVwB,IAAS,SAQnC,KAAW,IAAgL,IAAhL,CAR6C,CAQ7C,6EAA0G,YAAwB,SAAe,KAAe,KAA4B,IAAM,CAAlB,GAAkB,YAAwB,yBAA4C,gBAAqB,kBAAlC,MAAkC,CAAuC,SAAc,KAAW,KAAY,OAAU,IAAM,+BAAmD,OAAU,cAAsB,WAR7Z,MAAY,EAA2B,MAAY,SAEhI,EAAqB,OAF2H,GAA5B,MAEpG,4IAAK,GAF2H,CAIzJ,SAAS,EAA2B,KAAc,MAAgB,gBAA9B,IAA8B,SAAkC,EAAiB,KAAa,YAAb,CAAa,uCAAqH,CAA7D,sDAA6D,0CAAsD,8EAAoF,EAAiB,MAElZ,SAAS,EAAiB,KAAa,YAAb,EAAa,sBAAuD,uBAAuC,IAAS,IAAO,UAAoB,SAMzK,SAAS,EAAO,KAA2B,GAA3B,CAA2B,iBAAgC,iCAAoC,sCAAoD,2BAA6D,wDAAiE,oBAAsC,SAEvU,SAAS,EAAa,GAAW,YAAgB,mBAAsB,KAAO,yCAAuD,IAAQ,EAAO,kCAA8C,EAAe,UAA6B,KAA7B,IAA6B,yFAAmH,EAAO,+BAA0C,gEAAmF,EAAK,SAE1e,SAAS,EAAe,OAA4K,EAAxJ,MAApB,IAAoB,EAAkB,2BAAkC,kDAAoE,EAAY,GAAP,CAAO,GAAoB,EAEpM,gBAAsD,oBAA+B,IAA8D,IAA9D,WAErF,KAA2D,oBAA+B,IAAuD,IAAvD,KAAiB,iBAAkD,MAAZ,EAAwB,WAAuB,IAAO,OAAqB,kBAA0C,WAA6B,UAF9M,KAA0E,QAAZ,EAAY,uBAAoC,sCAA6D,QAAY,WAA6B,IAAO,QAA2B,mBAA0C,kDAAwE,WAA+B,SAwB5d,OAA4B,gBAAU,eACtC,iBAGA,aA8XA,gEAEA,EAA4B,EAAc,EAAa,EAAG,QAC1D,CADyC,CACzC,CADuD,CACvD,OACA,aACA,sBACA,YACA,YACA,aACA,aACA,gBACA,gBACA,eACA,WACA,mBACA,mBACA,uBACA,qBACA,mBACA,cACA,0BACA,YACA,eACA,WACA,yBACA,YACA,cAEA,EAAmB,aAAO,mBDpM1B,ECqMiC,GDpMjC,eCoMiC,GDpMjC,qBACA,aACA,OACA,OAEA,8BACA,CAAK,KACL,mBACA,iBACA,CAAK,kBC4LL,CAAG,MACH,EAAoB,aAAO,YAC3B,ODlPA,ECkPkC,GDxNlC,EAEA,gBCsNkC,IDrNlC,OA5BA,eCiPkC,GDjPlC,mBACA,aACA,OACA,OAEA,KAYA,OAVA,OACA,4MACA,MAGA,+BACA,wFACA,MAGA,CACA,CAAK,uBACL,aACA,OACA,OAEA,aAA2C,MAAU,KAAoB,MACzE,CAAK,GAAI,CAKT,CAAK,ECoN6B,CAClC,CAAG,MACH,EAA2B,aAAO,YAClC,+BACA,CAAG,MACH,EAA6B,aAAO,YACpC,+BACA,CAAG,MAMH,EAAgB,YAAM,OACtB,GAAiB,YAAM,OAGvB,GAAqB,EADD,cACe,CADf,CAAU,QACK,GACnC,SACA,SAEA,gBACA,yBACA,GAA4B,YAAM,wDDpRlC,eCoRgH,QDpRhH,QCsRA,SAFyI,KAAK,CAI9I,gBACA,sBACA,aACA,iBAEA,SACA,IACA,kBACA,CAAa,EACb,KAGA,CAAO,KAEP,EAEE,eAAS,YAEX,OADA,uCACA,WACA,yCACA,CACA,CAAG,eACH,OAAuB,YAAM,KAE7B,eACA,0CAKA,mBACA,cACA,EAEE,eAAS,YAMX,OALA,IACA,qCAA4C,EAAkB,IAC9D,YAD8D,aAC9D,gBAGA,WACA,IACA,wCAAiD,GACjD,eADmE,aACnE,YAEA,CACA,CAAG,QAED,CAFsC,EAEtC,YAAS,YAKX,MAJA,kBACA,kBAGA,YACA,CAAG,UACH,OAAgB,iBAAW,aAC3B,EACA,KAGA,gBAEA,CAAG,MACH,GAAsB,iBAAW,iBA9iBN,EA+iB3B,KA/iBmC,WA+iBnC,GAEA,CAF4B,CAE5B,UACA,MACA,qBA7iBA,SAAS,CAAkB,EAAQ,QANyB,CAMzB,QAAR,GAAQ,OAA+B,EAAiB,IANxD,EAmjB8B,WA7iB0B,EAFnF,SAAS,CAAgB,EAAS,MAJmD,SAI5D,CAAS,qFAJmD,IAAS,EAA2B,IAAS,SAEzH,EAAuB,SAFoH,GAA3B,IAEzF,EAAL,qIAAK,IAijByB,YAEjD,EAAc,IACtB,QADsB,OACtB,wBACA,IAAY,EAAoB,OAIhC,ID9cO,EACP,EACA,EACA,CCucgC,CDtchC,EACA,EACA,EACA,ECucA,WACA,QD9cA,CC8c4C,CD9c5C,CADO,EC+cqD,CAC5D,QACA,GAF4D,IAE5D,EACA,UACA,UACA,WACA,WACA,WACA,CAAS,EDtdT,MACA,WACA,YACA,YACA,aACA,aACA,cAEA,oDAIA,oBACA,IAEA,IAFA,OACA,EACA,IAIA,EADA,EADA,SACA,EACA,IAEA,cACA,eACA,CAAG,GCicH,IACA,eACA,aAHA,QAIA,gBACA,sBACA,CAAS,EAET,GACA,KAEA,CAAO,oBACP,YACA,CAAO,CAEP,CAAG,yBACH,GAAqB,iBAAW,aAChC,mBACA,YACA,MACA,MAAmB,EAAc,GAEjC,SAFiC,WAEjC,CACA,IACA,gCACA,CAAQ,UASR,OAJA,MACA,KAGA,EACA,CAAG,QACH,GAAsB,iBAAW,aACjC,mBACA,YACA,MAEA,KAF4B,CAE5B,8BACA,uCACA,CAAK,EAGL,CAHQ,CAGR,mBAEA,SACA,cAGA,cAEA,eAIA,IACA,uBACA,gBACA,gBACA,eACA,CAAK,EAEG,EAAc,OACtB,KADsB,CAGnB,UACH,GAAiB,iBAAW,eAC5B,SACA,KACA,sBACA,IACA,EAA2B,EADD,EAAY,KACG,GACzC,EAFsC,CAEtC,CADyC,EACzC,CACA,OAGA,EAA4B,EADD,EAAa,OACE,GAC1C,CAFwC,CACE,CAC1C,IACA,OAEA,cAEA,YACA,cACQ,CACR,YAEA,GACA,gBAGA,QACA,OACA,4BACA,QACA,CAAW,CACX,CAAS,CACT,CACA,CAAK,EAEL,wCAEA,sBACA,QACA,OACA,QAAmB,EAAwB,CAClC,CACT,CAAO,EACP,aAGA,IACA,gBACA,iBACA,wBACA,eACA,CAAK,EAEL,GACA,SAGA,eACA,OAGA,eACA,MAEA,CAAG,yBACH,GAAiB,iBAAW,aAC5B,mBAEA,CAF4B,CAE5B,UACA,MACA,cAEQ,EAAc,IACtB,QADsB,OACtB,0BACY,EAAoB,QAIhC,OACA,CAAO,EALyB,KAKzB,aACP,YACA,CAAO,EAGP,IACA,YACA,CAAK,CACL,CAAG,cAEH,CAFoE,EAE7C,iBAAW,YAGlC,eACA,IACA,iBACA,CAAO,EACP,IAMA,kBAN4B,OAM5B,CAJA,CACA,WACA,OACA,GACA,iBACA,WACA,CAAO,mBACP,WACA,IACA,kBACA,CAAS,CACT,CAAO,oBDldP,0ECqdA,KACA,IACA,kBACA,CAAW,GD7cX,+DC8ckC,ED9clC,eC+cA,cAGA,IAH+C,MAG/C,EACA,sBACA,oBAEA,4KAGA,KAEA,CAAO,EACP,MACA,CAEA,aACA,IACA,iBACA,CAAO,EACP,IACA,sBACA,mBAEA,CAAG,uBAEH,CAFsH,EAElG,iBAAW,aAE/B,4CAIA,iEACA,mBACA,KAEA,CAAG,SAEH,CAFiC,EAEf,iBAAW,YAC7B,IACA,YACA,CAAK,CACL,CAAG,KACH,GAAiB,iBAAW,YAC5B,IACA,WACA,CAAK,CACL,CAAG,KAEH,CAFU,EAEQ,iBAAW,YAC7B,IDloBO,UCyoBW,CDxoBlB,wFACA,OATA,KASA,EATA,sBASA,EATA,qBAIA,KAKA,EALA,gBAMA,ICuoBA,iBAEA,KAEA,CAAG,SAEH,eACA,eACA,EAEA,eACA,mBACA,EAEA,eACA,mBACA,EAEA,eACA,GACA,mBAEA,EAEA,GAAqB,aAAO,YAC5B,kBACA,+DAAwF,CACxF,WAEA,SACA,cACA,YACA,WACA,YACA,gBACA,eACA,gBACA,WACA,SAEA,OAAa,EAAc,EAAc,EAAe,CACxD,QAD0B,EAAc,GACE,CADc,CACM,OAC9D,WAAwC,EAAoB,OAC5D,UAAuC,CADqB,CACD,OAC3D,WAD2D,EACP,OACpD,WADoD,CACpD,GAAwC,EAAoB,OAC5D,WAD4D,GACrB,EAAoB,OAC3D,WAD2D,CAC3D,GAAwC,EAAoB,OAC5D,UAAmC,CADyB,CACL,OACvD,WADuD,IACvD,iCACA,CAAO,CAtBP,mBAsBO,QAEC,EAAI,CAFL,CACP,UACA,EAAQ,CAAI,EACZ,CACA,CAAG,oCACH,GAA4B,iBAAW,aACvC,mBACA,CAAG,KACH,GAAsB,aAAO,YAC7B,kBACA,+DAAwF,CACxF,WAEA,aACA,YACA,SAuBA,OAAa,EAAc,EAAa,EAAG,CArBpB,EAAe,CACtC,KAoB0B,EApB1B,EACA,KAFsC,IAEtC,EACA,YACA,OACA,SACA,wBACA,sBACA,aACA,uBACA,kBACA,UACA,oBACA,YACA,mBACA,CAAS,CACT,YAAiC,EAAoB,OACrD,WADqD,EACD,OACpD,WADoD,CAE7C,CAxBP,mBAwBO,KAEoC,EAC3C,CACA,CAAG,gBACH,OAAS,EAAc,EAAa,EAAG,OAAY,EAA7B,EAAc,QACpC,OACA,gBACA,iBACA,UACA,YACA,WACA,CAAG,CACH,EAv2BA,QAGA,SACA,SAQA,MANE,yBAAmB,cACrB,OACA,MACA,CACA,CAAG,MAEmB,CAFR,CAEQ,aAAmB,CAAC,UAAQ,QAAiB,EAAc,EAAa,EAAG,MAAY,EAC7G,CADgF,EAAc,EAC9F,CACA,CAAG,GACH,CAAC,EACD,0BAEA,CAFmC,GAEnC,IACA,YACA,kBH5DO,CG4Dc,QH5Dd,CG4DuB,EH3D9B,MAAW,QAAS,oCAgBpB,EAfA,SAaA,EAbA,gBACA,OA8BA,cACA,MAAW,QAAS,gCAGpB,YACA,iBACA,iCAGA,WACA,EAGA,WAuCA,KACA,2BACA,KACA,0BACA,IACA,EA7CA,+BAEA,CACA,oBACA,OAAyB,EAAc,IACvC,CAAK,CACL,EAhDA,IA8CuC,UA9CvC,SAEA,GAaA,EADA,EAZA,IAaA,YAZA,OAkBA,yBAAkD,EAAc,WAhBhE,CAgBgE,KAhBhE,oEACA,SAkBA,GACA,MAAW,QAAS,gCAEpB,OADA,0CACA,OAAiC,EAAc,GAC/C,CAAK,CACL,EAvBA,GAEA,EAmB+C,CAlB1C,CACL,EGgDA,YACA,UACA,YACA,WACA,yBACA,WACA,cACA,UACA,wBACA,eACA,kBACA,YACA,EACA,mBACA,cAgBA,SAAY,MAAc,CAS1B,OAAU,UAAkB,CAAC,SAAiB,CAAC,QAAgB,GAK/D,SAAY,MAAc,CAK1B,sBAAyB,MAAc,CAKvC,QAAW,MAAc,CAMzB,WAAc,MAAc,CAK5B,OAAU,MAAc,CAKxB,qBAAwB,MAAc,CAKtC,QAAW,QAAgB,CAK3B,QAAW,QAAgB,CAM3B,SAAY,QAAgB,CAK5B,SAAY,MAAc,CAO1B,kBAAqB,MAAc,CAKnC,mBAAsB,MAAc,CAKpC,iBAAoB,MAAc,CAMlC,eAAkB,MAAc,CAKhC,UAAa,MAAc,CAO3B,YAAe,MAAc,CAO7B,YAAe,MAAc,CAO7B,WAAc,MAAc,CAgC5B,OAAU,MAAc,CASxB,eAAkB,MAAc,CAShC,eAAkB,MAAc,CAOhC,QAAW,MAAc,CAOzB,UAAa,MACb,EAuEA,QACA,aACA,sBACA,gBACA,gBACA,gBACA,iBACA,mBA8jBA,iBAEA,eACA,YACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,OACxC,EACA,CAAO,CAEP,YACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,OACxC,EACA,CAAO,CAEP,kBACA,OAAa,EAAc,EAAa,EAAG,OAAmB,EAApC,EAAc,iBACxC,EACA,CAAO,CAEP,mBACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,gBACxC,EACA,CAAO,CAEP,uBACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,UACxC,eACA,4BACA,4BACO,CAEP,gBACA,OAAa,EAAc,EAAa,EAAG,MAAY,EACvD,CAD0B,EAAc,WACxC,gBACA,gCACA,4BACO,CAEP,aACA,OAAa,EAAa,EAAG,IAE7B,OAF0B,EAG1B,QACA,CACA,CAEA,0FE53BO,SAASM,GAAanC,CAAwB,EACnD,GAAM,CACJD,MAAOqC,CAAS,eAChBC,CAAa,CACbC,UAAQ,YACRC,CAAU,QACVC,EAAS,CACP,UAAW,EACb,CAAC,SACDC,EAAU,OAAO,OAAO,GACxBC,EAAW,CAAC,UACZC,EAAW,EAAK,UAChBC,GAAW,CAAK,CAChB9C,WAAS,CACT,GAAG+C,EACJ,CAAG7C,EACE,CAAC8C,EAAOC,EAAS,CD3FzB,SAASC,CACPC,MAAI,IC0F0CD,SDzF9CE,CAAW,UACXC,EAAW,KAAO,CAAC,CACW,EAC9B,GAAM,CAACC,EAAkBC,EAAoB,CAAGC,SAkBzCA,aACPJ,CAAW,CACXC,UAAQ,CACoC,EAC5C,IAAMI,EAAoB1D,EAAAA,QAAc,CAAgBqD,GAClD,CAACnD,EAAM,CAAGwD,EACVC,EAAe3D,EAAAA,MAAY,CAACE,GAC5B0D,EAAeC,CAAAA,EAAAA,GAAAA,CAAAA,CAAcA,CAACP,GAOpC,OANAtD,EAAAA,SAAe,CAAC,KACV2D,EAAaG,OAAO,GAAK5D,IAC3B0D,EAAa1D,CADqB,EAElCyD,EAAaG,OAAO,CAAG5D,EAE3B,EAAG,CAACA,EAAOyD,EAAcC,EAAa,EAC/BF,CACT,EAjCuE,CACnEL,uBACAC,CACF,GACMS,EAAeX,KAASY,MACxB9D,EAAQ6D,EAAeX,EAAOG,EAC9BK,EAAeC,CAAAA,EAAAA,GAAAA,CAAAA,CAAcA,CAACP,GAUpC,MAAO,CAACpD,EAT8DF,EAAAA,WAAiB,CAACiE,IACtF,GAAIF,EAAc,CAEhB,IAAM7D,EAA6B,YAArB,OAAO+D,EADNA,EACwCb,GAAQa,CAC3D/D,KAAUkD,GAAMQ,EAAa1D,EACnC,MACEsD,CADK,CACeS,EAExB,EAAG,CAACF,EAAcX,EAAMI,EAAqBI,EAAa,EAClC,ECsEuB,CAC7CR,KAAMb,EACNe,SAAUd,CACZ,GACM0B,EAASlE,EAAAA,WAAiB,CAAC,CAACmE,EAAuBC,KACvD,GAAI,CAACtB,GAAyB,IAAbD,GAAkBsB,EAAcE,MAAM,CAAG,EAAG,YAC3DC,GAAAA,EAAKA,CAAC/C,KAAK,CAAC,4CAGd,GAAI,CAAC0B,GAAOoB,SAAU,EAAKF,EAAcE,MAAM,CAAGxB,EAAU,YAC1DyB,GAAAA,EAAKA,CAAC/C,KAAK,CAAC,CAAC,wBAAwB,EAAEsB,EAAS,MAAM,CAAC,EAGzD,IAAM0B,EAAWJ,EAAcK,GAAG,CAACC,GAAQC,OAAOC,MAAM,CAACF,EAAM,CAC7DG,QAASC,IAAIC,eAAe,CAACL,EAC/B,IACMM,EAAe9B,EAAQ,IAAIA,KAAUsB,EAAS,CAAGA,EASvD,GARArB,EAAS6B,GACLX,EAAcC,MAAM,CAAG,GAAG,EACdW,OAAO,CAAC,CAAC,MACrBP,CAAI,CACL,IACCH,GAAAA,EAAKA,CAAC/C,KAAK,CAAC,CAAC,KAAK,EAAEkD,EAAKQ,IAAI,CAAC,aAAa,CAAC,CAC9C,GAEExC,GAAYsC,EAAaV,MAAM,CAAG,GAAKU,EAAaV,MAAM,EAAIxB,EAAU,CAC1E,IAAMqC,EAASH,EAAaV,MAAM,CAAG,EAAI,GAAGU,EAAaV,MAAM,CAAC,MAAM,CAAC,CAAG,CAAC,IAAI,CAAC,CAChFC,GAAAA,EAAKA,CAACa,OAAO,CAAC1C,EAASsC,GAAe,CACpCK,QAAS,CAAC,UAAU,EAAEF,EAAO,GAAG,CAAC,CACjCG,QAAS,KACPnC,EAAS,EAAE,EACJ,GAAGgC,EAAO,SAAS,CAAC,EAE7B3D,MAAO,CAAC,iBAAiB,EAAE2D,EAAAA,CAC7B,EACF,CACF,EAAG,CAACjC,EAAOJ,EAAUC,EAAUL,EAAUS,EAAS,EASlDlD,EAAAA,SAAe,CAAC,IACP,KACAiD,GACLA,EAAM+B,EADM,KACC,CAACP,IACRa,GAAkBb,IACpBI,GAD2B,CACvBU,eAAe,CAACd,EAAKG,OAAO,CAEpC,EACF,EAEC,EAAE,EACL,IAAMY,EAAazC,GAAY,CAACE,GAAOoB,SAAU,GAAMxB,EACvD,MAAO,WAAClC,MAAAA,CAAIV,UAAU,+CAA+CY,wBAAsB,eAAeC,0BAAwB,8BAC9H,UAAC2E,GAAQA,CAACvB,GAADuB,CF0HQ,EAAC,CE1HAvB,EAAQvB,OAAQA,EAAQC,QAASA,EAASC,SAAUA,EAAUC,SAAUD,EAAW,GAAKC,EAAUC,SAAUyC,EAAY7D,sBAAoB,WAAWb,0BAAwB,6BAC9L,CAAC,cACF4E,CAAY,eACZC,CAAa,cACbC,CAAY,CACb,GAAK,WAACjF,MAAAA,CAAK,GAAG+E,GAAc,CAAEzF,UAAWK,CAAAA,EAAAA,GAAAA,EAAAA,CAAEA,CAAC,sLAAuL,+HAAgIsF,GAAgB,6BAA8BJ,GAAc,iCAAkCvF,GAAa,GAAG+C,CAAa,WACzd,UAAC6C,QAAAA,CAAO,GAAGF,GAAe,GACzBC,EAAe,WAACjF,MAAAA,CAAIV,UAAU,oEAC3B,UAACU,MAAAA,CAAIV,UAAU,iDACb,UAAC6F,EAAAA,CAAMA,CAAAA,CAAC7F,UAAU,+BAA+B8F,cAAY,WAE/D,UAACC,IAAAA,CAAE/F,UAAU,6CAAoC,2BAG1C,WAACU,MAAAA,CAAIV,UAAU,oEACtB,UAACU,MAAAA,CAAIV,UAAU,iDACb,UAAC6F,EAAAA,CAAMA,CAAAA,CAAC7F,UAAU,+BAA+B8F,cAAY,WAE/D,WAACpF,MAAAA,CAAIV,UAAU,uBACb,WAAC+F,IAAAA,CAAE/F,UAAU,8CAAoC,QACzC,CAAC,GAAG,CAAC,CAAC,gDAEd,WAAC+F,IAAAA,CAAE/F,UAAU,6CAAmC,iBAE7C4C,EAAW,EAAI,CAAC,CAAC,EAAEA,IAAaoD,IAAW,WAAapD,SAAS;mCACnD,EAAEqD,CAAAA,EAAAA,GAAAA,CAAAA,CAAWA,CAACtD,GAAS,MAAM,CAAC,CAAG,CAAC,aAAa,EAAEsD,CAAAA,EAAAA,GAAAA,CAAAA,CAAWA,CAACtD,GAAAA,CAAU,eAMnGK,GAAOoB,OAAS,UAACtC,GAAAA,UAAUA,CAAAA,CAAC9B,UAAU,6BACnC,UAACU,MAAAA,CAAIV,UAAU,8BACZgD,GAAOuB,IAAI,CAACC,EAAM0B,IAAU,UAACC,GAAAA,CAAqB3B,KAAMA,EAAM4B,SAAU,IAAMA,CAtDzF,SAAkBF,CAAa,EAC7B,GAAI,CAAClD,EAAO,OACZ,IAAMsB,EAAWtB,EAAMqD,MAAM,CAAC,CAACC,EAAGC,IAAMA,IAAML,GAC9CjD,EAASqB,GACT/B,IAAgB+B,GAClB,EAiDkG4B,GAAQM,SAAU/D,GAAY,CAAC+B,EAAKQ,IAAI,CAAC,EAArFkB,QAEhC,OAExB,CAMA,SAASC,GAAS,MAChB3B,CAAI,UACJgC,CAAQ,UACRJ,CAAQ,CACM,EACd,MAAO,WAAC1F,MAAAA,CAAIV,UAAU,uCAAuCY,wBAAsB,WAAWC,0BAAwB,8BAClH,WAACH,MAAAA,CAAIV,UAAU,kCACZqF,GAAkBb,GAAQ,UAACiC,EAAAA,OAAKA,CAAAA,CAACC,IAAKlC,EAAKG,OAAO,CAAEgC,IAAKnC,EAAKQ,IAAI,CAAE4B,MAAO,GAAIC,OAAQ,GAAI1B,QAAQ,OAAOnF,UAAU,mDAAsD,KAC3K,WAACU,MAAAA,CAAIV,UAAU,uCACb,WAACU,MAAAA,CAAIV,UAAU,uBACb,UAAC+F,IAAAA,CAAE/F,UAAU,+DACVwE,EAAKQ,IAAI,GAEZ,UAACe,IAAAA,CAAE/F,UAAU,yCACViG,CAAAA,EAAAA,GAAAA,CAAAA,CAAWA,CAACzB,EAAKsC,IAAI,OAGzBN,EAAW,UAAC1G,GAAAA,CAAQA,CAAAA,CAACG,MAAOuG,IAAe,WAGhD,UAAC9F,MAAAA,CAAIV,UAAU,mCACb,WAAC+G,GAAAA,CAAMA,CAAAA,CAACC,KAAK,SAASC,QAAQ,QAAQH,KAAK,OAAOI,QAASd,EAAUtD,cAAuBiB,IAAbyC,GAA0BA,EAAW,IAAKxG,UAAU,sBAAsB0B,sBAAoB,SAASb,0BAAwB,8BAC5M,UAACsG,EAAAA,CAACA,CAAAA,CAACnH,UAAU,wBAAwB0B,sBAAoB,IAAIb,0BAAwB,sBACrF,UAACuG,OAAAA,CAAKpH,UAAU,mBAAU,uBAIpC,CACA,SAASqF,GAAkBb,CAAU,EAGnC,MAAO,YAAaA,GAAQ,iBAAOA,EAAKG,OAAO,4BC1OjD,0BAEA,wBAEA,cAEA,6BACA,IAAI,GAAQ,WACZ,IADY,GACZ,YACA,OACA,OAEA,MAA+B,GAAQ,aACvC,EADuC,CACvC,UACA,iBACA,eACA,EAEA,gDAEA,uBAEA,OACA,6CACA,OAAY,GAAQ,qCACpB,EAEA,+BACA,6BACA,6BAEA,eAEA,IADA,EACA,mBACA,sDACA,qBACA,mBAEA,oCACA,IAAoB,GAAQ,KAc5B,UAd4B,IAE5B,GADA,+CACA,SAIA,eACA,qBACA,qBALA,IAaA,QACA,CAEA,0BAEA,iBAEA,4CAEA,mDAEA,aACA,QAAkB,GAAQ,GAC1B,SAEA,GAH0B,CAG1B,kDACA,oBACA,SACA,EACA,KACA,CACA,EAEA,0BAEA,aACA,SACA,kBACA,WACA,MACA,aACA,WACA,IACA,UACA,WACA,EACgB,GAAQ,qBACxB,EACA,eAEA,GADA,EAEA,CACA,uDACA,MAEA,QACA,OAEA,EAEA,QACA,YACA,qBACA,eACA,EACA,IACA,gBACA,oBACA,oBACA,sBACA,SACA,EACA,IACA,UACA,UACA,sBACA,sBACA,kBACA,oBACA,mBACA,EAEA,GAAwB,eAAmB,OAC3C,iCA+BA,WAA6B,YAAgB,KAoC7C,sBACA,OACA,gCAEA,eACA,2BACA,SAEA,kBADA,EACA,WACA,mBAFA,EAEA,aAEA,MAJA,EAIA,KACA,EALA,EAKA,CAEA,CAAS,EAET,QACA,EAEA,kCAAkE,iBAAqB,CAAG,WAAe,CAgCzG,eACA,WACA,SAAY,uCAAmD,MAC/D,MAAyC,UAAc,eACvD,EAAiC,QAAY,EAC7C,WACA,aACA,eACA,iBACA,oBACA,gBACA,WACA,SACA,CAAK,EAgBL,OAfA,qBACA,OACA,oBACA,QACA,aACA,GACA,GACA,gBACA,IACA,CAAiB,CACjB,CAAS,CACJ,WACD,WAAe,MACnB,kCACA,CAAK,MACM,SAAa,gCACxB,CAEA,6BAEA,gBACA,OACA,kBACA,WAEA,iBACA,4BACA,WAEA,mBACA,GAGA,oBAEA,+BACA,gBACA,aAEA,gBACA,iCAEA,qBACA,iBACA,uBACA,SAEA,sBACA,SAIA,aAFA,SACA,SACA,IACA,WACA,kBACA,SAEA,cACA,WACA,iBACiB,GAAQ,IAAU,GAAQ,IAC3C,IADyB,EACzB,KAD2C,EAC3C,sBACA,WACA,MACA,QAEA,CACA,CACA,QACA,CAmPA,mBAtKA,YACA,WACA,MAAY,oEAA6E,EACzF,uBACA,EAA6B,SAAa,2DAC1C,EAhEA,YACA,WACA,SAAY,gEAA2E,MACvF,EAA0B,QAAY,IACtC,EAAqB,QAAY,IACjC,EAA+B,QAAY,QAC3C,aACA,MAA6B,SAAa,qCAC1C,MAAiC,UAAc,2BAwB/C,OAvBA,qBACA,OACA,WACA,SACA,CAAS,CACT,QACA,aACA,OACA,0DACA,cACA,mBACA,kBACA,KACA,YAEA,MAEA,IAEA,CACA,CAAS,CACJ,aACD,WAAe,2BACnB,CACA,EA+BA,CACA,UACA,OACA,eACA,QACA,CAAK,EACL,MACA,UACA,OACA,QACA,CAAK,EACL,EAAmB,QAAY,IAC/B,EAA2B,QAAY,eACvC,WACA,QACA,mBAA0C,qBAA2B,EAAI,CACpE,EACL,aACA,MAAuB,SAAa,+BAAiC,EACrE,SACA,cACA,wBACA,CAAS,CACT,SACA,cACA,6BACA,CAAS,CACT,WACA,cACA,+BACA,CAAS,CACT,cACA,cACA,kCACA,CAAS,CACT,OACA,cACA,sBACA,CAAS,CACJ,SACL,EAAqB,aAAiB,wBACtC,QACA,YACA,MACA,CAAS,CACT,eACK,OACL,EAAmB,aAAiB,uBACpC,QACA,0BACA,MACA,CAAS,CACT,aACK,qBACL,EAAgB,aAAiB,KACjC,qBACA,OACA,WACA,6BACA,gCACA,4CACA,qCACA,EAEA,CAAK,gBACL,EAAkB,SAAa,OAC/B,OACA,QACA,qBACA,CAAgB,wBAChB,EAAgB,CAChB,WACA,SACA,KACA,EAAK,2BAsCL,OArCI,WAAe,MACnB,qCACA,cACA,mBACA,0BACA,CAAoB,6BACpB,EAAoB,CACX,EACT,cACA,qBACA,UACA,cAEA,EAEA,GADA,QACA,GACA,yCACA,yBACA,yBACA,qBAEA,CAEA,OADA,iBACA,KACA,GACA,oBACA,GACA,gBACA,OACA,CACA,CAAK,YACD,WAAe,MACnB,qBACA,WACA,MACA,CAAS,CACT,CAAK,UACM,SAAa,OACxB,QACA,YACA,YACA,EAAK,SACL,EA4CA,IAuHA,sBACA,CACA,QACA,OACA,iCAA4E,CAC5E,SACA,CAAS,EAET,GAEA,6BAEA,QACA,SAiBA,OACA,gBACA,QACA,CAAS,CACT,KApBA,IACA,eACA,iBAEA,EAiBA,UAhBA,IACA,UACA,CACA,iBACA,oBACA,CAAa,GAYb,YATA,KACA,KASA,CACA,EAEA,MAA+B,GAAQ,2BAEvC,sBAEA,2BAEA,OACA,OACA,SAEA,0BACA,mBACA,wDACA,EAEA,iCAEA,uBAEA,mBAEA,2BAkBA,iBACA,uBACA,EACA,MACA,IACA,MACA,0BAtBA,KACA,2BACA,IACA,UACA,sBAEA,QACA,EAeA,KACA,aACA,OASA,OARA,GACA,YAEA,OACA,IAAkB,WAClB,IADkB,EAClB,YAtBA,YACA,eACA,kCACA,SAGA,QACA,EAeA,KACA,oBAEA,CACA,CAEA,WACA,eACA,YACA,SAGA,QACA,EAEA,kBAA0C,EAC1C,uBACA,GAAQ,GAAQ,MAChB,SADgB,IAChB,EACA,qBACiB,GAAQ,kBACzB,+BACA,eAEA,UACA,UAIA,QACA,CAwBA,eAvBA,kBACA,uBACA,GAAQ,GAAQ,MAChB,SADgB,IAChB,EACA,qBACiB,GAAQ,iBACzB,OACA,SACA,yBACA,YACA,CAA4B,aAG5B,gBAAkG,WAIlG,oBAIA,QACA,GACA,WAEA,QACA,SACA,UACA,EACA,IAAsB,qBACtB,WACA,qBACA,eACA,QACA,qCACA,gBACA,OAAqB,2BACrB,CACA,oCAEA,4CACA,gCACA,GACA,CAA4B,6BAC5B,GACA,EACA,CACA,SACA,EAEA,qBAAgC,6BAAwC,SACxE,EACA,EACA,OACA,IACA,EACA,GACA,EACA,SACA,YACA,EACA,KACA,EAEA,QACA,WACA,UACA,EACA,2BACA,0CACA,CACA,WACA,eAEA,MACA,GAEA,eACA,mBACA,MACA,QAEA,MACA,iBAEA,MACA,mCAA+C,EAAO,MAEtD,MACA,iBAEA,qCACA,CAEA,mBACA,SACA,gBACA,aACA,gBACA,CACA,OACA,eACA,aACA,SACA,2BACA,CACA,EAEA,0BAEA,YACA,EACA,MACA,SACU,GAAQ,GAClB,YADkB,EAElB,aACA,QACA,EAEA,QACA,+BACA,uBACA,2BACA,mBACA,2BACA,CAAC,CAED,uBACA,eACA,cACA,mBACA,kCACS,GAAQ,aACjB,EADiB,KACjB,qDAEA,eACA,aACA,OACA,OACA,aACA,aACA,WACA,YAEA,gBACA,aACA,gBACA,sCACA,mCAEA,mBACA,gCACA,cACA,MACA,OAAoB,QAAsB,EAC1C,KACA,0CACA,cAEA,8BACA,cAGA,WACA,KAEA,MAEA,GAAqB,GAAQ,IAC7B,QACA,GAF6B,EAK7B,CACA,CAEA,EAEA,mBACA,cACA,YACA,OACA,QACA,MACA,EAEA,mBACA,gBACA,kBACA,UACA,UACA,+BACA,MAEA,GAFqB,GAErB,OACA,OACA,OACA,OACA,EAEA,0BACA,OACA,QAAyB,EAAU,OACnC,cAGA,OACA,CACA,OACA,MACA,CACA,CAEA,mBACA,KACA,SAAY,QAAqB,EACjC,cACA,8CACA,8BACA,aACA,EAEA,gBACA,IACA,OACA,iBACA,GACA,MACA,iBACA,kBAEA,gBACA,aAGA,gBACA,QAEA,0BACA,GAEA,iCACA,GAKA,uCAEA,aACA,kBAGA,OAFA,kBACA,UACA,CACA,EAEA,YAEA,8BACA,UACA,+BACA,UACA,OACA,OACA,mBACA,KACA,CAEA,CAEA,UAA6C,GAAQ,WACrD,EACA,CACA,CAHqD,KAGrD,EACA,UACA,EAEA,wBACA,QAAY,6GAAsG,KAClH,UACA,gBACA,SAEA,eACA,MACA,sBACA,oCACA,mBAEA,EACA,KACA,QACA,QAEA,cACA,aACA,OACA,qBACA,QACA,4BACA,sBACA,0CACA,WACA,OACA,WACA,UACA,MACA,cAEA,EACA,KACA,6BACA,GACA,EApBA,QAoBA,YACA,WACA,mBACA,oBACA,UAAgB,aAAiB,MACjC,CAAgB,qBAChB,MACA,OACA,MACA,iBACA,UACA,MACA,qBAEA,IAEA,OADA,KACA,CAGA,CACA,yBAGA,IAFA,EACA,EACA,QACA,QACA,mBAUA,CACA,iCACA,+CACA,iBACA,iBACA,gBACA,KACA,gBACA,EACA,UACA,qBAEA,gBACA,KACA,gBACA,EACA,UACA,oBAEA,KA7BA,CACA,uBACA,QACA,cACA,cAEA,aACA,aAEA,CAqBA,YACA,yCACA,IAEA,OADA,gBACA,CAGA,CACA,WACA,IACA,8BACA,YACA,QACA,gBACA,kBACA,gBACA,kBACA,YACA,yBACA,IAEA,OADA,gBACA,CAGA,CACA,iBACA,IAAgB,mBAA+B,MAC/C,wBACA,MACA,gBACA,UACA,MACA,oBAEA,IAEA,OADA,KACA,CAGA,CACA,KACA,WAEA,SADA,aACA,GACA,OACA,MACA,KACA,6BAEA,IAEA,OADA,aACA,CAGA,MACA,GAAiB,GAAQ,IACzB,SACA,EAFyB,EAEzB,YACA,cACA,MAEA,8BACA,IACA,GACA,KACA,iBACA,EACA,aACA,GACA,SAGA,CACA,YACA,MACA,MACA,MAEA,IACA,QAGA,EAGA,OADA,MACA,CACA,EAEA,QACA,iBACA,2BACA,mBACA,EA0jCA,gBChzEA,IAAM0C,GDwLN,IACA,ECzLUA,CDyLV,ECzLaC,QDyLD,ICzLaA,CDyLb,GAAoB,EAChC,OAAY,eAAmB,cAA6B,QAAa,GACzE,ECvLMC,GAAmBxH,EAAAA,aAAmB,CAAwB,CAAC,GAC/DyH,GAAY,CAAkH,CAClI,GAAGtH,EACkC,GAC9B,UAACqH,GAAiBE,QAAQ,EAACxH,MAAO,CACvC+E,KAAM9E,EAAM8E,IACd,EAAGtD,sBAAoB,4BAA4Bd,wBAAsB,YAAYC,0BAAwB,oBACzG,UAAC6G,GAAUA,CAAE,GAAGxH,CAAK,CAAEwB,CAAZgG,qBAAgC,aAAa7G,0BAAwB,eAGhF8G,GAAe,KACnB,IAAMC,EAAe7H,EAAAA,UAAgB,CAACwH,IAChCM,EAAc9H,EAAAA,UAAgB,CAAC+H,IAC/B,eACJC,CAAa,CACd,CAAGC,KACEC,EAAYC,GAAa,CAC7BlD,GAFgBgD,EAEVJ,EAAa5C,CADSkD,GACL,GAEnBC,EAAaJ,EAAcH,EAAa5C,IAAI,CAAEiD,GACpD,GAAI,CAACL,EACH,MAAM,MADW,kDAGnB,GAAM,IACJQ,CAAE,CACH,CAAGP,EACJ,MAAO,IACLO,EACApD,KAAM4C,EAAa5C,IAAI,CACvBqD,WAAY,GAAGD,EAAG,UAAU,CAAC,CAC7BE,kBAAmB,GAAGF,EAAG,sBAAsB,CAAC,CAChDG,cAAe,GAAGH,EAAG,kBAAkB,CAAC,CACxC,GAAGD,CAAU,CAEjB,EAIML,GAAkB/H,EAAAA,aAAmB,CAAuB,CAAC,GACnE,SAASyI,GAAS,WAChBxI,CAAS,CACT,GAAGE,EACyB,EAC5B,IAAMkI,EAAKrI,EAAAA,KAAW,GACtB,MAAO,UAAC+H,GAAgBL,QAAQ,EAACxH,MAAO,CACtCmI,IACF,EAAG1G,sBAAoB,2BAA2Bd,wBAAsB,WAAWC,0BAAwB,oBACvG,UAACH,MAAAA,CAAIC,YAAU,YAAYX,UAAWK,CAAAA,EAAAA,GAAAA,EAAAA,CAAEA,CAAC,aAAcL,GAAa,GAAGE,CAAK,IAElF,CACA,SAASuI,GAAU,CACjBzI,WAAS,CACT,GAAGE,EAC8C,EACjD,GAAM,OACJoB,CAAK,YACL+G,CAAU,CACX,CAAGV,KACJ,MAAO,UAACnG,GAAAA,CAAKA,CAAAA,CAACb,YAAU,aAAa+H,aAAY,CAAC,CAACpH,EAAOtB,UAAWK,CAAAA,EAAAA,GAAAA,EAAAA,CAAEA,CAAC,qCAAsCL,GAAY2I,QAASN,EAAa,GAAGnI,CAAK,CAAEwB,sBAAoB,QAAQd,wBAAsB,YAAYC,0BAAwB,YAClP,CACA,SAAS+H,GAAY,CACnB,GAAG1I,EAC+B,EAClC,GAAM,OACJoB,CAAK,YACL+G,CAAU,mBACVC,CAAiB,eACjBC,CAAa,CACd,CAAGZ,KACJ,MAAO,UAACkB,GAAAA,EAAIA,CAAAA,CAAClI,YAAU,eAAeyH,GAAIC,EAAYS,mBAAkB,EAAkC,GAAGR,EAAkB,CAAC,EAAEC,EAAAA,CAAe,CAAhE,GAAGD,EAAAA,CAAmB,CAA4CS,eAAc,CAAC,CAACzH,EAAQ,GAAGpB,CAAK,CAAEwB,sBAAoB,OAAOd,wBAAsB,cAAcC,0BAAwB,YAC9Q,CAUA,SAASmI,GAAY,WACnBhJ,CAAS,CACT,GAAGE,EACuB,EAC1B,GAAM,OACJoB,CAAK,eACLiH,CAAa,CACd,CAAGZ,KACEsB,EAAO3H,EAAQ4H,OAAO5H,GAAO6H,SAAW,IAAMjJ,EAAM6B,QAAQ,QAClE,EAGO,EAHH,CAGG,CAHI,CAGJ,KAACgE,IAAAA,CAAEpF,YAAU,eAAeyH,GAAIG,EAAevI,UAAWK,CAAAA,EAAAA,GAAAA,EAAAA,CAAEA,CAAC,2BAA4BL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,cAAcC,0BAAwB,oBAC9KoI,IAHI,IAKX,yCC3G+C,iBAAkB,4BAA4B,MAAQ,GAAC,KAAM,0DAA0D,CAAC,GAAC,YAAS,mBAAyB,kBAAoB,uFAAqF,CAAC,GAAC,UAAS,2BAA6B,GAAC,KAAM,GAAN,CAAM,KAAW,gBAAkB,MAAQ,GAAC,oCAAqC,EAAE,aAAa,EAAE,kCAAiC,sBAAwB,CAAC,GAAC,MAAO,GAAC,YAAa,GAAC,OAAQ,KAAK,GAAC,OAAQ,SAAS,0CAA+F,ICA/gB,iBAAoB,aAAa,SAAS,EAAE,mDAAmD,+BAA+B,iCAAiC,MAAM,+BAA+B,WAAW,kBAAkB,wDAAwD,oCAAoC,iBAAiB,EAAE,KAAK,gCAAgC,KAAK,GAAC,4CAA6C,UAAU,SAAS,aCczjB,IAAMG,GAAuB,CAAC,aAAc,YAAa,YAAa,aAAa,CAC7EC,GAAaC,GAAAA,EAAQ,CAAC,CAC1BC,MAAOD,GAAAA,EAAK,GAAGE,MAAM,CAACxG,GAASA,GAAOoB,QAAU,EAAG,sBAAsBoF,MAAM,CAACxG,GAASA,GAAO,CAAC,EAAE,EAAE8D,MAHjF,EAGyF2C,EAAe,CAAC,qBAAqB,CAAC,EAAED,MAAM,CAACxG,GAASoG,GAAqBM,QAAQ,CAAC1G,GAAO,CAAC,EAAE,EAAEgE,MAAO,mDACtNhC,KAAMsE,GAAAA,EAAQ,GAAGK,GAAG,CAAC,EAAG,CACtBR,QAAS,6CACX,GACAS,SAAUN,GAAAA,EAAQ,GAClBO,MAAOP,GAAAA,EAAQ,GACfQ,YAAaR,GAAAA,EAAQ,GAAGK,GAAG,CAAC,GAAI,CAC9BR,QAAS,6CACX,EACF,GACe,SAASY,GAAY,aAClCC,CAAW,WACXC,CAAS,CAIV,MDhCwjB,ECiCvjB,CDjC0kB,GCiCpkBC,EAAgB,CACpBlF,KAAMgF,GAAahF,MAAQ,GAC3B4E,SAAUI,GAAaJ,UAAY,GACnCC,MAAOG,GAAaH,OAAS,EAC7BC,YAAaE,GAAaF,aAAe,EAC3C,EACMK,EJkkFR,OIlkFsBC,EJkkFtB,IAA2B,EAC3B,MAAyB,QAAY,SACrC,EAAoB,QAAY,SAChC,MAAyC,UAAc,EACvD,WACA,gBACA,8BACA,eACA,gBACA,sBACA,WACA,cACA,cAAuB,CACvB,gBAAyB,CACzB,mBAA4B,CAC5B,mBAAkC,CAClC,wBACA,WACA,kCACA,OACA,eACA,CAAK,EACL,cACA,iBACA,WACA,iBACA,WACA,EACA,uCACA,wDAGA,CACA,gBAAoB,QAAuB,SA34C3C,IAAqC,EACrC,IAuCA,EAvCA,GACA,MACA,MAEA,GACA,cACA,WACA,WACA,8BACA,gBACA,eACA,gBACA,sBACA,WACA,gBAAyB,CACzB,cAAuB,CACvB,mBAA4B,CAC5B,mBAAqC,CACrC,uBACA,EACA,KACA,GAAyB,GAAQ,kBAA4B,GAAQ,YACrE,GADqE,EACrE,6BAEA,qBACA,GACA,MACA,GACA,UACA,SACA,QACA,EACA,GACA,cACA,iBACA,gBACA,cACA,aACA,EAEA,IACA,GACA,WACA,eACA,oBACA,iBACA,gBACA,WACA,SACA,EACA,GACA,MAEA,GACA,WACA,UACA,EACA,0BACA,SACA,gBACA,iBACA,EACA,YACA,gBACA,YACA,WACA,IACA,iBACA,uBACA,aACA,gBACA,cACA,SACA,CAAiB,CAEjB,CACA,EACA,UACA,aACA,iBACA,oBACA,gBACA,sBACA,qCACA,GACA,GACA,2BACA,yBAEA,CAAa,EACb,cACA,oCACA,oCACA,CAAa,EAEb,EAoCA,UACA,iBACA,cACA,gBACS,CACT,EAQA,cACA,cACA,MACA,6BACA,QACA,qBACA,EACA,qBACA,OACA,YACA,CACA,EACA,gBACA,SACA,KACA,GACA,MACA,EACA,gBACA,UACA,yBACA,YACA,wBACA,iBAEA,oBACA,wBACA,EACA,oBACA,uBACA,4BACA,EACA,GACA,gBACA,gBACA,MACA,CACA,MACA,4BACA,IACA,wBACA,gCACA,EACA,GACA,kBACA,kBACA,MAEA,CACA,qBACA,CACA,aACA,EACA,cACA,qBACA,0BACA,OACA,cAYA,GAXA,gBACA,kBACA,eAGA,gBACA,OACA,EACA,iBACA,gBAEA,gBACA,QACA,GACA,OACA,KACA,qBAAgE,GAAU,EAAI,CAC9E,gBACA,MACA,EACA,GACA,KACA,MAEA,eACA,CACA,EACA,YACA,QACA,gGAEA,OADA,KACA,CACA,EACA,YACA,WAAgB,GAAS,WACzB,KACA,gBACA,aACA,GACA,iBACA,cACA,MAGA,WAEA,QACA,EACA,eACA,QACA,CAAK,IACL,gBACA,WACA,MACA,IAAwB,WAAoB,EAC5C,MACA,0BACA,gBACA,wBACA,UAEA,mEAIA,GAHA,uBACA,OAEA,YACA,WACA,GACA,KAGA,IACA,cACA,EACA,sBACA,8BACA,oBACA,CACA,OACA,cACA,CACA,CACA,gBAaA,sBACA,iBACA,YACA,mBACA,WACA,EACA,MACA,EACA,MACA,CAAwB,OACxB,EACK,MAEL,WAAoD,IACpD,cACA,IACA,MACA,WACA,IACA,YACA,gBACA,EACA,iBACA,GACA,EACA,UACA,8DAEA,OACA,UACA,mBACA,+BACA,iBACA,mCAGA,UACA,iBAGA,CAAyB,EAGzB,yCAGA,UACA,gBAGA,cACA,YACA,cACA,OACA,YACA,CAAyB,GAIzB,CACA,gCACA,sCACA,sBACA,EACA,YACA,gBACA,wBACA,OAEA,WACA,UACA,SACA,kBACgB,GAAQ,IACxB,WADwB,CAExB,MACA,SACA,QACA,CACA,EACA,WAA+C,IAC/C,cACA,iBACA,QACA,UACA,GACA,cACA,OACA,YACA,CAAa,EACb,YACA,eACA,WACA,gBACA,eACA,cACA,OACA,oBACA,cACA,CAAiB,GAIjB,gBAEA,SADA,SAGA,uBAA0D,YAAqB,EAC/E,cACA,sBACA,YACA,CAAS,CACT,EACA,YACA,WACA,eACA,SACA,KACA,UACA,MACA,EACA,iBACA,2BACA,eACA,EACA,aACA,uBACA,MAGA,IAFA,EACA,EACA,SACA,SACA,MACA,0CACA,aACA,aACA,iBACA,YACA,8CACA,YACA,UACA,EACA,gBACA,4BACA,SAGA,eACA,iBAEA,eACA,YAOA,GANA,GACA,cACA,OACA,YACA,YACA,CAAiB,EACjB,EAWA,MAVA,yBACA,kBACA,GACA,IAGA,GACA,KAGA,GACA,mBAA2C,UAAuB,EAAe,EAGjF,GADA,qBAA8D,KAAe,EAC7E,YACA,WAAwB,GAAS,aAEjC,GADA,KACA,GACA,uBACA,oBACA,UACA,SACA,OACA,CACA,MAEA,UACA,8DACA,OACA,KACA,IACA,EACA,KAEA,YACA,YACA,mBAIA,IACA,WACA,aACA,WAEA,CACA,EACA,UACA,2BAEA,OADA,UACA,CAGA,EACA,cAA6C,IAG7C,IAFA,EACA,EACA,QACA,eACA,yBACA,QACA,IACA,oBACA,CACA,MACA,EAKA,CAJA,sCACA,cACA,wBAA4E,OAAqB,EACjG,CAAa,qBACb,gBAGA,eAcA,OAZA,cACA,WACA,wBACA,cACA,GACA,MAAoB,EAAM,CAC1B,mBAA+C,WAAU,EAAI,CAC7D,gBACS,EACT,eACA,IACA,oBACA,CACA,EACA,MACA,OACA,gBAEA,aACA,EACA,MACA,QACA,iBACA,EACA,WACA,8BACA,mCACA,0BACA,wCACA,sCACA,EAAK,CAQL,YACA,eAA0C,MAAQ,SAAU,KAG5D,CAAgB,6BAFhB,mBAGA,eACA,KACA,KACA,KACA,CAAS,EACT,cACA,OACA,gBACA,UACA,CAAS,EACT,uCACA,EAOA,wBACA,SACA,2BACA,uCACA,YACA,QAA8B,KAAgB,CAC9C,KACA,KACA,eACA,CAAiB,CAEjB,CAAS,CACJ,cAYL,SAA0C,IAC1C,6BACA,kBACA,kBACA,cACA,QACA,SAEA,4BACA,iCACA,qCACA,oBACA,yBACA,oBACA,oBACA,QAEA,cACA,YACA,CAAS,EACT,cACA,KACA,mBAAwC,CAAI,YAAsB,CACzD,EACT,kBACA,EACA,aAAiC,SAAiB,IAClD,iBACA,GACA,oBACA,0CAEA,EACA,SAAwC,IACxC,cACA,iCAsBA,OArBA,QACA,QAA2B,CAC3B,IACA,iBAAqD,UAAO,GAAQ,CACpE,OACA,SACA,KACa,CACJ,EACT,eACA,EACA,GACA,wBACA,WACA,WACA,MACA,CAAa,EAGb,gBAEA,CACA,KACA,CAAoB,iCACpB,EAAoB,CACpB,iBACA,CACA,sBACA,cACA,cACA,0BACA,0BACA,qBACA,EACA,EAAoB,CACpB,OACA,WACA,SACA,QACA,MACA,OACA,UACA,mBACA,oBACA,kDAGA,QACA,gBACA,GACA,iBACA,gBAGA,QACA,IACA,QACA,KACA,CACA,SACA,aACA,KACA,0BAA0F,KAC1F,CACA,KAA2C,mBAA2B,EAEtE,CAAoC,MAAe,CAC1B,CACJ,EACrB,iBACA,KAGA,CADA,YAAiD,EACjD,IACA,gBAEA,0CACA,4BACA,gBAEA,CAAa,CAEb,EACA,0BACA,gBAiBA,uBACA,EACA,IACA,qCACA,WACA,aAEA,YAIA,GAHA,cACA,eACA,CAAS,EACT,YACA,WAAoB,YAAiB,SACrC,YACA,OACA,MAEA,WAEA,mBACA,wBACA,QAIA,GADA,oBACA,cACA,cACA,SAA0B,CACb,EACb,IACA,YACA,CACA,SACA,GACA,CACA,MAEA,GACA,SAAkC,YAAsB,IAExD,IACA,cASA,GAPA,cACA,eACA,gBACA,oCACA,4BACA,gBACS,EACT,EACA,OAEA,EA0BA,SAAqD,IACrD,gBACA,QACA,QACA,QAIA,GAHA,qBACA,MAEA,eACA,qBAKA,wBAJA,YACA,WACA,qBACA,GAEA,oBACA,gBACA,iBAGA,CACA,aACA,sBACA,cACA,YACA,+BACA,aACA,SACA,UACA,wBACA,MACA,UACA,KACA,CACA,CACA,CACA,CAEA,mBACA,qBACA,kBAIA,IAEA,CACA,qBACA,oBACA,MACA,GACA,MACA,cACA,QAA0B,KAAW,CACxB,EACb,cACA,QAA0B,KAC1B,CAAa,CACb,CACA,GACA,wCACA,gBACA,cACA,iBACA,cACA,YACA,QACA,EACA,QACA,YACA,iBACA,oBACA,6BACA,cACA,8BACA,cACA,EACA,YAEA,aACA,UACA,wBACA,WACA,iCACA,cAEA,cACA,GACA,kBACA,uBACA,QACA,cACA,uBACA,QACA,YACA,cACA,EAAgC,CAChC,4BACA,gBACA,EAAoB,CACpB,+BAAwE,CACxE,+CACA,qBAEA,gBACA,eACA,CAAS,CACT,EACA,iBACA,KACA,KAgBA,MACA,GACA,KACA,KAEA,EAQA,GACA,SACA,WACA,aACA,gBACA,eACA,WACA,aACA,aACA,cACA,YACA,YACA,YACA,eAx3BA,yBACA,sBAEA,GADA,YACA,2BACA,8BACA,aACA,CACA,MACA,+BACA,qCACA,qBACA,cACA,CACA,qBACA,kBACA,GACA,sCACA,4CACA,2BACA,CACA,gCACA,wBAEA,cACA,OACA,eACA,0BACA,gBACA,kBACa,CACb,MAEA,SAEA,EAu1BA,oBACA,WAj1BA,IACA,WACA,cACA,gBACA,UACA,CAAS,CACT,EA40BA,eAtqBA,0DAuqBA,SACA,oBAzBA,yBACA,2BACA,oBACA,cACA,YACA,CAAa,CACb,CAAS,EAoBT,iBAhsBA,KACA,wBACA,aACA,IACA,WACA,2BACA,gBACA,IACA,CACA,iBACA,EAurBA,aA9PA,IACA,QACA,cAAmC,WAAU,EAC7C,aACA,cACA,IACA,4BACA,0BACA,sBACA,2BACA,CAAyB,EAGzB,CAAa,OAEb,EAgPA,YACA,kBACA,cACA,QACA,CAAa,CACb,kBACA,QACA,CAAa,CACb,aACA,QACA,CAAa,CACb,kBACA,OACA,CAAa,CACb,qBACA,QACA,CAAa,CACb,aACA,QACA,CAAa,CACb,kBACA,OACA,CAAa,CACb,iBACA,QACA,CAAa,CACb,eACA,QACA,CAAa,CACb,oBACA,GACA,KACA,SAEA,CAAa,CACJ,CACT,UAvaA,IACA,WACA,GACA,KACA,gBAEA,GACA,KACA,WACA,CAAS,GA+ZT,UACA,WACA,eACA,MA9bA,aACA,mBACA,sBACA,gBACA,CAAS,EACT,UA0bA,WACA,YACA,QACA,WArOA,OAA0C,IAC1C,UACA,mBACA,kBAGA,oBACA,4BAEA,eACA,sBAEA,cACA,oBACA,yBACA,iBACA,KAEA,eACA,eACA,gBAEA,cAAmC,KAAe,EAElD,EA8MA,YA3dA,IACA,GACA,iCACA,cACA,oBAAiD,CACxC,CACT,EAsdA,aACA,WACA,SAjGA,OAAwC,IACxC,cACA,UACA,MACA,aACA,UACA,MACA,UACA,UACA,gBACA,cACA,WAEA,CACA,EAoFA,eACA,EACA,OACA,KACA,aACA,CACA,EAmX2C,EAC3C,YACA,KACA,WACA,CACA,CAEA,wBAuEA,OAtEA,aACA,QACA,oBACA,4BACA,gBAA8C,gBAAuB,EACrE,eACA,CAAS,EAMT,OALA,OACA,KACA,UACA,EAAS,EACT,wBACA,CACA,CAAK,MACD,WAAe,gDACf,WAAe,MACnB,QACA,yBAEA,kBACA,4CAEA,CAAK,8BACD,WAAe,MACnB,WACA,uBACA,gBAEA,CAAK,eACD,WAAe,MACnB,oBACA,wBACA,oBACA,CAAa,CACb,CAAK,yBACD,WAAe,MACnB,8BACA,mBACA,gBACA,wBACA,SACA,CAAiB,CAEjB,CACA,CAAK,gBACD,WAAe,MACnB,mCACA,mBACA,iBACA,0BACA,CAAa,EACb,mBACA,OAA0C,MAAU,GAGpD,uBAEA,CAAK,eACD,WAAe,MACnB,iBACA,cACA,mBAEA,iBACA,kBACA,wBAA2C,gBAAuB,GAElE,oBACA,CAAK,EACL,4BACA,WIjrFmD,CAC/CC,QAAAA,EDxCwkB,CCwCnjBC,IDxCmjB,aAAwB,iBAAkB,IAAI,qCAAqC,IAAI,iEAA1G,SAA0G,iBAAqF,oCAAoC,GAAC,EAAG,KAAK,CAAR,MAAQ,EAAS,mBAAmB,EAAE,SAAS,YAAY,oCAAoC,eAAe,GAAe,oBAA8C,EAA9C,iBAA8C,OAAW,SAAS,QAAQ,GAAC,qEAAsE,SAAQ,GAAG,SAAS,4BAAqD,OCyCxmCJ,CACV,GAIA,MAAO,WAACzJ,GAAAA,EAAIA,CAAAA,CAACT,UAAU,iBAAiB0B,sBAAoB,OAAOd,wBAAsB,cAAcC,0BAAwB,6BAC3H,UAACC,GAAAA,EAAUA,CAAAA,CAACY,sBAAoB,aAAab,0BAAwB,4BACnE,UAACE,GAAAA,EAASA,CAAAA,CAACf,UAAU,+BAA+B0B,sBAAoB,YAAYb,0BAAwB,4BACzGoJ,MAGL,UAAChJ,GAAAA,EAAWA,CAAAA,CAACS,sBAAoB,cAAcb,0BAAwB,4BACrE,UAACwG,GAAIA,CAAE,GAAG8C,CAAI,CAAT9C,sBAA+B,OAAOxG,0BAAwB,4BACjE,WAACsJ,OAAAA,CAAKI,SAAUJ,EAAKK,YAAY,CAACD,SAXjCA,CAA2C,EAEpD,GASqDvK,UAAU,sBACrD,UAACwH,GAASA,CAACiD,KAADjD,GAAU2C,EAAKM,OAAO,CAAEzF,KAAK,QAAQ0F,OAAQ,CAAC,CACxDC,OAAK,CACN,GAAK,UAACjK,MAAAA,CAAIV,UAAU,qBACb,WAACwI,GAAQA,CAACxI,IAADwI,MAAW,mBAClB,UAACC,GAASA,MAAAA,IAAC,WACX,UAACG,GAAWA,QAAAA,EACV,UAACvG,GAAYA,CAACpC,MAAO0K,EAARtI,KAAmB,CAAEE,cAAeoI,EAAMtH,QAAQ,CAAET,SAAU,EAAGD,QAAS,IAAI,OAAO,CAQpG,UAACqG,GAAWA,CAAAA,OAAAA,CAERtH,sBAAoB,YAAYb,0BAAwB,qBAEpE,WAACH,MAAAA,CAAIV,UAAU,kDACb,UAACwH,GAASA,CAACiD,KAADjD,GAAU2C,EAAKM,OAAO,CAAEzF,KAAK,OAAO0F,OAAQ,CAAC,OACvDC,CAAK,CACN,GAAK,WAACnC,GAAQA,KAAAA,MACP,UAACC,GAASA,MAAAA,IAAC,iBACX,UAACG,GAAWA,QAAAA,EACV,UAACgC,GAAAA,CAAKA,CAAAA,CAACC,YAAY,qBAAsB,GAAGF,CAAK,KAEnD,UAAC3B,GAAWA,CAAAA,MACDtH,CADCsH,qBACmB,YAAYnI,0BAAwB,qBACzE,UAAC2G,GAASA,CAACiD,KAADjD,GAAU2C,EAAKM,OAAO,CAAEzF,KAAK,WAAW0F,OAAQ,CAAC,OAC3DC,CAAK,CACN,GAAK,WAACnC,GAAQA,KAAAA,MACP,UAACC,GAASA,MAAAA,IAAC,aACX,WAACqC,GAAAA,EAAMA,CAAAA,CAACvI,cAAetC,GAAS0K,EAAMtH,QAAQ,CAACpD,GAAQA,MAAO0K,EAAM1K,KAAK,CAAC0K,EAAM1K,KAAK,CAACmE,MAAM,CAAG,EAAE,WAC/F,UAACwE,GAAWA,QAAAA,EACV,UAACmC,GAAAA,EAAaA,CAAAA,UACZ,UAACC,GAAAA,EAAWA,CAAAA,CAACH,YAAY,0BAG7B,WAACI,GAAAA,EAAaA,CAAAA,WACZ,UAACC,GAAAA,EAAUA,CAAAA,CAACjL,MAAM,kBAAS,oBAC3B,UAACiL,GAAAA,EAAUA,CAAAA,CAACjL,MAAM,uBAAc,gBAChC,UAACiL,GAAAA,EAAUA,CAAAA,CAACjL,MAAM,oBAAW,aAC7B,UAACiL,GAAAA,EAAUA,CAAAA,CAACjL,MAAM,gBAAO,kBACzB,UAACiL,GAAAA,EAAUA,CAAAA,CAACjL,MAAM,kBAAS,4BAK/B,UAAC+I,GAAWA,CAAAA,MACDtH,CADCsH,qBACmB,YAAYnI,0BAAwB,qBACzE,UAAC2G,GAASA,CAACiD,KAADjD,GAAU2C,EAAKM,OAAO,CAAEzF,KAAK,QAAQ0F,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,WAACnC,GAAQA,KAAAA,MACP,UAACC,GAASA,MAAAA,IAAC,UACX,UAACG,GAAWA,QAAAA,EACV,UAACgC,GAAAA,CAAKA,CAAAA,CAAC5D,KAAK,SAASmE,KAAK,OAAON,YAAY,cAAe,GAAGF,CAAK,KAEtE,UAAC3B,GAAWA,CAAAA,MACDtH,CADCsH,qBACmB,YAAYnI,0BAAwB,wBAE3E,UAAC2G,GAASA,CAACiD,KAADjD,GAAU2C,EAAKM,OAAO,CAAEzF,KAAK,cAAc0F,OAAQ,CAAC,OAC9DC,CAAK,CACN,GAAK,WAACnC,GAAQA,KAAAA,MACP,UAACC,GAASA,MAAAA,IAAC,gBACX,UAACG,GAAWA,QAAAA,EACV,UAACwC,GAAAA,CAAQA,CAAAA,CAACP,YAAY,4BAA4B7K,UAAU,cAAe,GAAG2K,CAAK,KAErF,UAAC3B,GAAWA,CAAAA,MACDtH,CADCsH,qBACmB,YAAYnI,0BAAwB,qBACzE,UAACkG,GAAAA,CAAMA,CAAAA,CAACC,KAAK,SAAStF,sBAAoB,SAASb,0BAAwB,4BAAmB,yBAK1G,0BCjIA,kECAA,wDCAA,sHCEA,SAASuK,EAAS,WAChBpL,CAAS,CACT,GAAGE,EAC8B,EACjC,MAAO,UAACmL,WAAAA,CAAS1K,YAAU,WAAWX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,WAAWC,0BAAwB,gBAC7kB,oCEYI,sBAAsB,gMDbbyK,EAAqB,CAChCC,KADWD,CACJ,wBACPxB,WAAAA,CAAa,6BACf,EACe,eAAe0B,EAAgB,UAC5CzJ,CAAQ,CAGT,CAJ6ByJ,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkCxL,KAAAA,GAAU,OAChE,MAAO4L,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAKpK,qBAAAA,CAAoB,OAAOd,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAkL,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBL,WAAAA,CAAaA,EAAajK,SAAbiK,YAAajK,CAAoB,kBAAkBb,yBAAAA,CAAwB,uBACvGgL,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAWvK,qBAAAA,CAAoB,aAAab,yBAAAA,CAAwB,eACrEkL,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAaxK,qBAAAA,CAAoB,eAAeb,yBAAAA,CAAwB,uBACvEgL,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAOzK,qBAAAA,CAAoB,SAASb,yBAAAA,CAAwB,eAE7DgL,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAKpM,SAAAA,CAAU,kDACb+B,QAAAA,CAAAA,WAMb,CCvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZsK,EAO8B,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,CACvB,iBAAiB,iBACjB,UACA,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CAOxB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,8LCUM,EAAgB,WAIhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GAIlE,CAAC,EAAkB,EAAkB,CACzC,EAA4C,EALuC,CAe/E,EAAiB,QAXoB,IAWpB,CACrB,CAAC,EAAmC,SAwGV,IAvGxB,GAAM,CACJ,GAsGyC,cAtGzC,CACA,MAAO,EAAY,KACnB,IAAK,gBACL,EAAgB,EAChB,GAAG,EACL,CAAI,CAEJ,CAAK,IAAW,CAAY,GAAZ,IAAkB,CAAC,EAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,GAAmB,GAAG,EAAO,EA+FxC,CA/F4C,EAAJ,QAAc,CAAC,uBA+FpB,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EAAiB,GAAW,EAhCxB,EAgCoB,CAElB,GA0FoJ,EA5FpH,GAExB,GAAC,EAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,CA4FU,EA5FW,GAAG,EAAS,EA6F5C,CA7FgD,CA4FR,GA5FI,MAAc,CAAC,KA4FI,oBAC1B,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,EAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,EAAS,GAAS,EAAc,EAAO,GAAG,KAAI,EAEjE,MACE,UAAC,GAAiB,MAAO,QAAiB,EAAc,MACtD,mBAAC,IAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,EAAS,GAAS,EAAJ,KAAY,EACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,EAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,EAGF,GAAS,YAAc,EAMvB,IAAM,EAAiB,oBAKjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,EAAmB,EAAgB,GACnD,CAF2C,KAGzC,MAFgE,EAEhE,EAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,EAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,EAAiB,EAAkC,GAAiC,OAC3E,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,EAAS,GAA6B,MACrB,UAAjB,OAAO,CAChB,CAEA,SAAS,EAAiB,GAAyB,OAG/C,EAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,EAAmB,EAAY,GAA8B,OAGlE,EAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,EAAkB,YAAc,EAiDhC,IAAM,EAAO,EACP,EAAY,gCC9HX,SAASC,IAEZ,MAAM,qBAEL,CAFSC,MACP,+GADG,+DAEN,EAOJ,yFAXgBD,qCAAAA,KAFEzM,EAjBX,OAiBWA,8BAA8B,GAAC,+UCbjC+B,qCAAAA,SAAAA,EAAiBN,CAAc,EAC7C,GACEkL,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBlL,IAClBmL,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBnL,IACpBoL,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBpL,IACrBqL,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBrL,IAClBsL,CAAAA,EAAAA,EAAAA,UAAAA,EAAWtL,IACXuL,CAAAA,EAAAA,EAAAA,8BAAAA,EAA+BvL,GAE/B,KADA,CACMA,CAGJA,cAAiBiL,OAAS,UAAWjL,GACvCM,EAAiBN,EAD6B,KAClB,CAEhC,aAtB+C,UACpB,WACS,WACF,WACA,UACG,qYCCrC,SAASwJ,EAAO,CACd,GAAG5K,EAC+C,EAClD,MAAO,UAAC4M,EAAAA,EAAoB,EAACnM,YAAU,SAAU,GAAGT,CAAK,CAAEwB,sBAAoB,uBAAuBd,wBAAsB,SAASC,0BAAwB,cAC/J,CAMA,SAASmK,EAAY,CACnB,GAAG9K,EACgD,EACnD,MAAO,UAAC4M,EAAAA,EAAqB,EAACnM,YAAU,eAAgB,GAAGT,CAAK,CAAEwB,sBAAoB,wBAAwBd,wBAAsB,cAAcC,0BAAwB,cAC5K,CACA,SAASkK,EAAc,WACrB/K,CAAS,CACT8G,OAAO,SAAS,UAChB/E,CAAQ,CACR,GAAG7B,EAGJ,EACC,MAAO,WAAC4M,EAAAA,EAAuB,EAACnM,YAAU,iBAAiBoM,YAAWjG,EAAM9G,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+yBAAgzBL,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,0BAA0Bd,wBAAsB,gBAAgBC,0BAAwB,uBACxgCkB,EACD,UAAC+K,EAAAA,EAAoB,EAACE,OAAO,IAACtL,sBAAoB,uBAAuBb,0BAAwB,sBAC/F,UAACoM,EAAAA,CAAeA,CAAAA,CAACjN,UAAU,oBAAoB0B,sBAAoB,kBAAkBb,0BAAwB,mBAGrH,CACA,SAASoK,EAAc,WACrBjL,CAAS,UACT+B,CAAQ,UACRmL,EAAW,QAAQ,CACnB,GAAGhN,EACkD,EACrD,MAAO,UAAC4M,EAAAA,EAAsB,EAACpL,sBAAoB,yBAAyBd,wBAAsB,gBAAgBC,0BAAwB,sBACtI,WAACiM,EAAAA,EAAuB,EAACnM,YAAU,iBAAiBX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gjBAAijB6M,cAAyB,kIAAmIlN,GAAYkN,SAAUA,EAAW,GAAGhN,CAAK,CAAEwB,sBAAoB,0BAA0Bb,0BAAwB,uBAC93B,UAACsM,EAAAA,CAAqBzL,sBAAoB,uBAAuBb,0BAAwB,eACzF,UAACiM,EAAAA,EAAwB,EAAC9M,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,MAAoB,WAAb6M,GAAyB,uGAAwGxL,sBAAoB,2BAA2Bb,0BAAwB,sBACpPkB,IAEH,UAACqL,EAAAA,CAAuB1L,sBAAoB,yBAAyBb,0BAAwB,mBAGrG,CAOA,SAASqK,EAAW,WAClBlL,CAAS,UACT+B,CAAQ,CACR,GAAG7B,EAC+C,EAClD,MAAO,WAAC4M,EAAAA,EAAoB,EAACnM,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4aAA6aL,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,uBAAuBd,wBAAsB,aAAaC,0BAAwB,uBACzmB,UAACuG,OAAAA,CAAKpH,UAAU,sEACd,UAAC8M,EAAAA,EAA6B,EAACpL,sBAAoB,gCAAgCb,0BAAwB,sBACzG,UAACwM,EAAAA,CAASA,CAAAA,CAACrN,UAAU,SAAS0B,sBAAoB,YAAYb,0BAAwB,mBAG1F,UAACiM,EAAAA,EAAwB,EAACpL,sBAAoB,2BAA2Bb,0BAAwB,sBAAckB,MAErH,CAOA,SAASoL,EAAqB,WAC5BnN,CAAS,CACT,GAAGE,EACyD,EAC5D,MAAO,UAAC4M,EAAAA,EAA8B,EAACnM,YAAU,0BAA0BX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDL,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,iCAAiCd,wBAAsB,uBAAuBC,0BAAwB,sBAC9R,UAACyM,EAAAA,CAAaA,CAAAA,CAACtN,UAAU,SAAS0B,sBAAoB,gBAAgBb,0BAAwB,gBAEpG,CACA,SAASuM,EAAuB,WAC9BpN,CAAS,CACT,GAAGE,EAC2D,EAC9D,MAAO,UAAC4M,EAAAA,EAAgC,EAACnM,YAAU,4BAA4BX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDL,GAAa,GAAGE,CAAK,CAAEwB,sBAAoB,mCAAmCd,wBAAsB,yBAAyBC,0BAAwB,sBACtS,UAACoM,EAAAA,CAAeA,CAAAA,CAACjN,UAAU,SAAS0B,sBAAoB,kBAAkBb,0BAAwB,gBAExG,mFCtFO,IAAM0M,EAAQ,GACnB,IAAIC,QAAQ,GAAaC,WAAWC,EAASC,IAelCC,CAfuC,CAexB,CAC1BC,QAAS,EAAE,CAGXC,aACE,IAAMC,EAA4B,EAAE,CA4BpC,IAAK,IAAIxH,EAAI,EAAGA,GAAK,GAAIA,IAAK,OAC5BwH,EAAeC,IAAI,CAhBZ,CACL5F,EAAAA,CAb+BA,EAAU,EAczCpD,KAAMiJ,EAAAA,CAAKA,CAACC,QAAQ,CAACC,WAAW,GAChCrE,YAAamE,EAAAA,CAAKA,CAACC,QAAQ,CAACE,kBAAkB,GAC9CC,WAAYJ,EAAAA,CAAKA,CAACK,IAAI,CACnBC,OAAO,CAAC,CAAEC,KAAM,aAAcC,GAAI,YAAa,GAC/CC,WAAW,GACd7E,MAAO8E,WAAWV,EAAAA,CAAKA,CAACC,QAAQ,CAACrE,KAAK,CAAC,CAAEF,IAAK,EAAGiF,IAAK,IAAKC,IAAK,CAAE,IAClEC,UAAW,CAAC,oDAAoD,EAAE1G,EAAG,IAAI,CAAC,CAC1EwB,SAAUqE,EAAAA,CAAKA,CAACc,OAAO,CAACC,YAAY,CApBnB,CACjB,cACA,YACA,WACA,OACA,YACA,QACA,UACA,kBACD,EAYCC,WAAYhB,EAAAA,CAAKA,CAACK,IAAI,CAACY,MAAM,GAAGR,WAAW,EAC7C,EAK8CnI,CAGhD,IAAI,CAACsH,OAAO,CAAGE,CACjB,EAGA,MAAMoB,OAAO,YACXC,EAAa,EAAE,QACfC,CAAM,CAIP,EACC,IAAIC,EAAW,IAAI,IAAI,CAACzB,OAAO,CAAC,CAgBhC,OAbIuB,EAAWhL,MAAM,CAAG,GAAG,CACzBkL,EAAWA,EAASjJ,MAAM,CAAC,GACzB+I,EAAW1F,QAAQ,CAAC6F,EAAQ3F,QAAQ,IAKpCyF,IACFC,EAAWE,CAAAA,CADD,CACCA,EAAAA,EAAAA,CAAWA,CAACF,EAAUD,EAAQ,CACvCI,KAAM,CAAC,OAAQ,cAAe,WAAW,EAC3C,EAGKH,CACT,EAGA,MAAMI,YAAY,MAChBC,EAAO,CAAC,OACRC,EAAQ,EAAE,YACVR,CAAU,QACVC,CAAM,CAMP,EACC,MAAM9B,EAAM,KACZ,IAAMsC,EAAkBT,EAAaA,EAAWU,KAAK,CAAC,KAAO,EAAE,CACzDC,EAAc,MAAM,IAAI,CAACZ,MAAM,CAAC,CACpCC,WAAYS,SACZR,CACF,GACMW,EAAgBD,EAAY3L,MAAM,CAGlC6L,EAAS,CAACN,GAAO,EAAKC,EACtBM,EAAoBH,EAAYI,KAAK,CAACF,EAAQA,EAASL,GAM7D,MAAO,CACLxK,QAAS,GACTgL,KALkB,CAKZC,GALgBC,OAAO5B,WAAW,GAMxCvF,QAAS,gDACToH,eAAgBP,SAChBC,QACAL,EACAN,SAAUY,CACZ,CACF,EAGA,MAAMM,eAAepI,CAAU,EAC7B,MAAMmF,EAAM,KAGZ,EAHmB,EAGbgC,EAAU,IAAI,CAAC1B,OAAO,CAAC4C,EAHS,EAGL,CAAC,GAAalB,EAAQnH,EAAE,GAAKA,UAE9D,EAUO,CACLhD,CAXE,KAAU,GAWH,EACTgL,KAJkB,CAIZC,GAJgBC,OAAO5B,WAAW,GAKxCvF,QAAS,CAAC,gBAAgB,EAAEf,EAAG,MAAM,CAAC,SACtCmH,CACF,EAdS,CACLnK,SAAS,EACT+D,QAAS,CAAC,gBAAgB,EAAEf,EAAG,UAAU,CAAC,CAahD,CACF,EAAE,EAGW0F,UAAU,wKC1Hd4C,uBAAuB,mBAAvBA,GALAC,YAAY,mBAAZA,EAAAA,YAAY,EAEZ/Q,SAAS,mBAATA,EAAAA,SAAS,EADTwB,QAAQ,mBAARA,EAAAA,QAAQ,EAFEwP,iBAAiB,mBAAjBA,EAAAA,iBAAiB,EAA3BC,QAAQ,mBAARA,EAAAA,QAAQ,EAIRvE,YAAY,mBAAZA,EAAAA,YAAY,EACZ1K,gBAAgB,mBAAhBA,EAAAA,gBAAgB,YALmB,WACf,UACJ,WACC,UACG,WACI,MAhCjC,OAAMkP,UAAqCvE,MACzCwE,aAAc,CACZ,KAAK,CACH,0JAEJ,CACF,CAEA,MAAML,UAAgCM,gBAEpCC,QAAS,CACP,MAAM,IAAIH,CACZ,CAEAI,QAAS,CACP,MAAM,IAAIJ,CACZ,CAEAK,KAAM,CACJ,MAAM,IAAIL,CACZ,CAEAM,MAAO,CACL,MAAM,IAAIN,CACZ,CACF,0PPPI,sBAAsB,8JQhBX,SAASO,IACtB,MAAO,WAAC5Q,EAAAA,EAAIA,CAAAA,CAACT,UAAU,iBAAiB0B,sBAAoB,OAAOd,wBAAsB,mBAAmBC,0BAAwB,mCAChI,WAACC,EAAAA,EAAUA,CAAAA,CAACY,sBAAoB,aAAab,0BAAwB,mCACnE,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,WAAW0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,OAEnH,UAACI,EAAAA,EAAWA,CAAAA,CAACS,sBAAoB,cAAcb,0BAAwB,kCACrE,WAACH,MAAAA,CAAIV,UAAU,sBAEb,WAACU,MAAAA,CAAIV,UAAU,sBACb,UAACmB,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,WAAW0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,IACjH,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,yBAAyB0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,OAIjI,WAACH,MAAAA,CAAIV,UAAU,kDAEb,WAACU,MAAAA,CAAIV,UAAU,sBACb,UAACmB,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,WAAW0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,IACjH,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,cAAc0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,OAItH,WAACH,MAAAA,CAAIV,UAAU,sBACb,UAACmB,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,WAAW0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,IACjH,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,cAAc0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,OAItH,WAACH,MAAAA,CAAIV,UAAU,sBACb,UAACmB,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,WAAW0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,IACjH,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,cAAc0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,UAKxH,WAACH,MAAAA,CAAIV,UAAU,sBACb,UAACmB,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,WAAW0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,IACjH,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,cAAc0B,sBAAoB,WAAWb,0BAAwB,2BAA2B,OAItH,UAACM,EAAAA,CAAQA,CAAAA,CAACnB,UAAU,YAAY0B,sBAAoB,WAAWb,0BAAwB,kCAIjG,gDE1Ce,eAAeyQ,EAAgB,WAC5CC,CAAS,CACa,EACtB,IAAIhC,EAAU,KACVtF,EAAY,qBAShB,MARkB,OAAO,CAArBsH,IAGE,CADJhC,EAAUiC,CADG,MAAM5D,EAAAA,CAAYA,CAAC4C,cAAc,CAACiB,OAAOF,GAAAA,EACvChC,OAAAA,GAEbnO,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAEV6I,EAAY,CAAC,YAAY,CAAC,EAErB,UAACF,EAAAA,OAAWA,CAAAA,CAACC,YAAauF,EAAStF,UAAWA,EAAWvI,sBAAoB,cAAcd,wBAAsB,kBAAkBC,0BAAwB,yBACpK,mBChBayK,EAAW,CACtBC,KAAAA,CAAO,0BACT,EAMe,eAAemG,EAAKxR,CAAgB,EACjD,IAAMyR,EAAS,IAATA,EAAezR,EAAMyR,GAANzR,GAAY,CACjC,MAAO2L,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAAC1J,EAAAA,CAAAA,CAAAA,CAAcC,UAAU,IAACV,qBAAAA,CAAoB,gBAAgBd,uBAAAA,CAAsB,OAAOC,yBAAAA,CAAwB,WACtH,SAAAgL,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACnL,KAAAA,CAAAA,CAAIV,SAAAA,CAAU,mBACb,SAAA6L,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAAC+F,EAAAA,QAAAA,CAAAA,CAASC,QAAAA,CAAUhG,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACwF,EAAAA,CAAAA,CAAAA,CAAAA,CAAqB3P,UAArB2P,WAAqB3P,CAAoB,WAAWb,yBAAAA,CAAwB,WAC/F,SAAAgL,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACyF,EAAAA,CAAgBC,SAAAA,CAAWI,EAAOJ,SAAS,CAAE7P,qBAAAA,CAAoB,kBAAkBb,yBAAAA,CAAwB,kBAItH,CXbA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,CARiD,IAQ5C,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,gCAAgC,CAChD,aAAa,CAAE,MAAM,mBACrB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,CAKC,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IYhF9B,qDCAA,4DCAA,uDCAA,2DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,yBCNjN,uDCAA,mVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,cACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAAgL,CAuBpM,+IAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QAzCA,IAAsB,uCAA4J,CAyClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QA1DA,IAAsB,sCAAiJ,CA0DvK,gHACA,gBA1DA,IAAsB,uCAAuJ,CA0D7K,sHACA,aA1DA,IAAsB,uCAAoJ,CA0D1K,mHACA,WA1DA,IAAsB,4CAAgF,CA0DtG,+CACA,cA1DA,IAAsB,4CAAmF,CA0DzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,kJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,2CACA,0CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCrGD,iDCAA,2DCAA,wMCegBiR,gBAAgB,mBAAhBA,GA6EAC,8BAA8B,mBAA9BA,GARAC,wBAAwB,mBAAxBA,GARAC,uBAAuB,mBAAvBA,GAhBArB,iBAAiB,mBAAjBA,GAvBAC,QAAQ,mBAARA,aArCmB,WAM5B,MAEDqB,EAGErQ,EAAAA,OAAAA,UAFN,KAA6B,GAEvBA,CACkB,GACpBkC,CAASA,KAEC+N,EACdK,CAAW,CACXnL,CAAkB,CAClBoL,CAAqE,EAArEA,KAAAA,IAAAA,IAAAA,EAAiCC,EAAAA,kBAAkB,CAACC,iBAAAA,EAEpD,IAAMhR,EAAQ,qBAA8B,CAA1BiL,MAAMgG,EAAAA,mBAAmB,EAA7B,+DAA6B,GAE3C,OADAjR,EAAMC,MAAM,CAAMgR,EAAAA,mBAAmB,CAAC,IAAGvL,EAAK,IAAGmL,EAAI,IAAGC,EAAW,IAC5D9Q,CACT,CAcO,SAASuP,EAEdsB,CAAW,CACXnL,CAAmB,IAFnB,EAISkL,CAIT,OAJAlL,MAAAA,CAAAA,GAAAA,EAASkL,CAAAA,IAJkB,EAIlBA,CAAAA,EAA4B,GAA5BA,IAAAA,EAAAA,EAAoBM,QAAQ,WAA5BN,EAAgCO,QAAAA,EACrC9B,EAAAA,YAAY,CAAC3C,IAAI,CACjB2C,EAAAA,YAAY,CAAC+B,OAAO,EAElBZ,EAAiBK,EAAKnL,EAAMqL,EAAAA,kBAAkB,CAACC,iBAAiB,CACxE,CAaO,SAAS1B,EAEduB,CAAW,CACXnL,CAAyC,EAEzC,MAFAA,KAAAA,IAAAA,IAAAA,EAAqB2J,EAAAA,YAAY,CAAC+B,EAFP,KAEOA,EAE5BZ,EAAiBK,EAAKnL,EAAMqL,EAAAA,kBAAkB,CAACM,iBAAiB,CACxE,CAUO,SAASV,EAAwB3Q,CAAc,QACpD,CAAKsR,EAAAA,CAAD,CAACA,eAAAA,EAAgBtR,GAIdA,EAAMC,GAJgB,GAIV,CAACuO,KAAK,CAAC,KAAKK,KAAK,CAAC,EAAG,CAAC,GAAG0C,IAAI,CAAC,KAJb,IAKtC,CAEO,SAASb,EAAyB1Q,CAAoB,EAC3D,GAAI,CAACsR,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBtR,GACnB,KAD2B,CACrB,qBAAiC,CAAjC,MAAU,wBAAV,+DAAgC,GAGxC,OAAOA,EAAMC,MAAM,CAACuO,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAG/B,SAASiC,EAA+BzQ,CAAoB,EACjE,GAAI,CAACsR,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBtR,GACnB,KAD2B,CACrB,qBAAiC,CAAjC,MAAU,wBAAV,+DAAgC,GAGxC,OAAOmQ,OAAOnQ,EAAMC,MAAM,CAACuO,KAAK,CAAC,KAAKgD,EAAE,CAAC,CAAC,GAC5C,gPClGA,gDCAA,0DCAA,kGCUA,SAASlP,EAAwDmP,CAAuB,EACtF,IAAMC,EAAcjT,EAAAA,MAAY,CAACgT,GAMjC,OALAhT,EAAAA,SAAe,CAAC,KACdiT,EAAYnP,OAAO,CAAGkP,CACxB,GAGOhT,EAAAA,OAAa,CAAC,IAAO,CAAC,GAAGkT,IAASD,EAAYnP,OAAO,MAAMoP,GAAa,EAAE,CACnF,0BClBA", "sources": ["webpack://terang-lms-ui/../../../src/client/components/forbidden.ts", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/./node_modules/attr-accept/dist/es/index.js", "webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/../../../src/icons/upload.ts", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/./src/components/ui/skeleton.tsx", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/../../../src/client/components/not-found.ts", "webpack://terang-lms-ui/./src/components/ui/label.tsx", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/?096a", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/?1513", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/../../../src/client/components/unstable-rethrow.ts", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/./src/components/ui/scroll-area.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/./src/components/layout/page-container.tsx", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/./node_modules/file-selector/dist/es2015/file.js", "webpack://terang-lms-ui/./node_modules/file-selector/dist/es2015/file-selector.js", "webpack://terang-lms-ui/./node_modules/file-selector/dist/es2015/index.js", "webpack://terang-lms-ui/./node_modules/react-dropzone/dist/es/utils/index.js", "webpack://terang-lms-ui/./node_modules/react-dropzone/dist/es/index.js", "webpack://terang-lms-ui/./src/hooks/use-controllable-state.tsx", "webpack://terang-lms-ui/./src/components/file-uploader.tsx", "webpack://terang-lms-ui/./node_modules/react-hook-form/dist/index.esm.mjs", "webpack://terang-lms-ui/./src/components/ui/form.tsx", "webpack://terang-lms-ui/./node_modules/@hookform/resolvers/dist/resolvers.mjs", "webpack://terang-lms-ui/./node_modules/@hookform/resolvers/zod/dist/zod.mjs", "webpack://terang-lms-ui/./src/features/products/components/product-form.tsx", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/./src/components/ui/textarea.tsx", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/progress.tsx", "webpack://terang-lms-ui/../../../src/client/components/unauthorized.ts", "webpack://terang-lms-ui/../../../src/client/components/unstable-rethrow.server.ts", "webpack://terang-lms-ui/./src/components/ui/select.tsx", "webpack://terang-lms-ui/./src/constants/mock-api.ts", "webpack://terang-lms-ui/../../../src/client/components/navigation.react-server.ts", "webpack://terang-lms-ui/./src/components/form-card-skeleton.tsx", "webpack://terang-lms-ui/./node_modules/next/dist/api/navigation.react-server.js", "webpack://terang-lms-ui/./src/features/products/components/product-view-page.tsx", "webpack://terang-lms-ui/src/app/dashboard/product/[productId]/page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/?9921", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/../../../src/client/components/redirect.ts", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/./src/hooks/use-callback-ref.tsx", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "\"use strict\";\n\nexports.__esModule = true;\n\nexports.default = function (file, acceptedFiles) {\n  if (file && acceptedFiles) {\n    var acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n\n    if (acceptedFilesArray.length === 0) {\n      return true;\n    }\n\n    var fileName = file.name || '';\n    var mimeType = (file.type || '').toLowerCase();\n    var baseMimeType = mimeType.replace(/\\/.*$/, '');\n    return acceptedFilesArray.some(function (type) {\n      var validType = type.trim().toLowerCase();\n\n      if (validType.charAt(0) === '.') {\n        return fileName.toLowerCase().endsWith(validType);\n      } else if (validType.endsWith('/*')) {\n        // This is something like a image/* mime type\n        return baseMimeType === validType.replace(/\\/.*$/, '');\n      }\n\n      return mimeType === validType;\n    });\n  }\n\n  return true;\n};", "\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "module.exports = require(\"module\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '17 8 12 3 7 8', key: 't8dd8p' }],\n  ['line', { x1: '12', x2: '12', y1: '3', y2: '15', key: 'widbto' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNyA4IDEyIDMgNyA4IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMyIgeTI9IjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('Upload', __iconNode);\n\nexport default Upload;\n", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "import { cn } from '@/lib/utils';\nfunction Skeleton({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='skeleton' className={cn('bg-accent animate-pulse rounded-md', className)} {...props} data-sentry-component=\"Skeleton\" data-sentry-source-file=\"skeleton.tsx\" />;\n}\nexport { Skeleton };", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cn } from '@/lib/utils';\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return <LabelPrimitive.Root data-slot='label' className={cn('flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50', className)} {...props} data-sentry-element=\"LabelPrimitive.Root\" data-sentry-component=\"Label\" data-sentry-source-file=\"label.tsx\" />;\n}\nexport { Label };", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\products\\\\components\\\\product-form.tsx\");\n", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "import(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\products\\\\components\\\\product-form.tsx\");\n", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n", "module.exports = require(\"node:os\");", "'use client';\n\nimport * as React from 'react';\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\nimport { cn } from '@/lib/utils';\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.Root\" data-sentry-component=\"ScrollArea\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.Viewport data-slot='scroll-area-viewport' className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1' data-sentry-element=\"ScrollAreaPrimitive.Viewport\" data-sentry-source-file=\"scroll-area.tsx\">\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n      <ScrollAreaPrimitive.Corner data-sentry-element=\"ScrollAreaPrimitive.Corner\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.Root>;\n}\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return <ScrollAreaPrimitive.ScrollAreaScrollbar data-slot='scroll-area-scrollbar' orientation={orientation} className={cn('flex touch-none p-px transition-colors select-none', orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent', orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaScrollbar\" data-sentry-component=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.ScrollAreaThumb data-slot='scroll-area-thumb' className='bg-border relative flex-1 rounded-full' data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaThumb\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>;\n}\nexport { ScrollArea, ScrollBar };", "module.exports = require(\"node:diagnostics_channel\");", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}", "module.exports = require(\"crypto\");", "export const COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nexport function toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map", "import { __awaiter } from \"tslib\";\nimport { toFileWithPath } from './file';\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nexport function fromEvent(evt) {\n    return __awaiter(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => toFileWithPath(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => toFileWithPath(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return __awaiter(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => toFileWithPath(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return toFileWithPath(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = toFileWithPath(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => __awaiter(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return __awaiter(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = toFileWithPath(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map", "export { fromEvent } from './file-selector';\n//# sourceMappingURL=index.js.map", "function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nimport _accepts from \"attr-accept\";\nvar accepts = typeof _accepts === \"function\" ? _accepts : _accepts.default; // Error codes\n\nexport var FILE_INVALID_TYPE = \"file-invalid-type\";\nexport var FILE_TOO_LARGE = \"file-too-large\";\nexport var FILE_TOO_SMALL = \"file-too-small\";\nexport var TOO_MANY_FILES = \"too-many-files\";\nexport var ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nexport var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nexport var getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nexport var TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nexport function fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nexport function fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nexport function allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nexport function isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nexport function isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nexport function isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nexport function onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nexport function isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nexport function composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nexport function canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nexport function pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nexport function acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nexport function isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nexport function isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nexport function isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nexport function isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */", "var _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\nimport React, { forwardRef, Fragment, useCallback, useEffect, useImperativeHandle, useMemo, useReducer, useRef } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { fromEvent } from \"file-selector\";\nimport { acceptPropAsAcceptAttr, allFilesAccepted, composeEventHandlers, fileAccepted, fileMatchSize, canUseFileSystemAccessAPI, isAbort, isEvtWithFiles, isIeOrEdge, isPropagationStopped, isSecurityError, onDocumentDragOver, pickerOptionsFromAccept, TOO_MANY_FILES_REJECTION } from \"./utils/index.js\";\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/forwardRef(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  useImperativeHandle(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/React.createElement(Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: PropTypes.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: PropTypes.objectOf(PropTypes.arrayOf(PropTypes.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: PropTypes.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: PropTypes.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: PropTypes.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: PropTypes.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: PropTypes.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: PropTypes.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: PropTypes.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: PropTypes.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: PropTypes.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: PropTypes.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: PropTypes.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: PropTypes.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: PropTypes.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: PropTypes.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: PropTypes.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: PropTypes.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: PropTypes.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: PropTypes.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: PropTypes.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: PropTypes.func\n};\nexport default Dropzone;\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nexport function useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = useMemo(function () {\n    return acceptPropAsAcceptAttr(accept);\n  }, [accept]);\n  var pickerTypes = useMemo(function () {\n    return pickerOptionsFromAccept(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = useMemo(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = useMemo(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = useRef(null);\n  var inputRef = useRef(null);\n\n  var _useReducer = useReducer(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = useRef(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && canUseFileSystemAccessAPI()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  useEffect(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = useRef([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  useEffect(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  useEffect(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = useCallback(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && allFilesAccepted({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = isEvtWithFiles(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = useCallback(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if (isEvtWithFiles(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = useCallback(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = fileAccepted(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = useCallback(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if (isEvtWithFiles(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if (isPropagationStopped(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = useCallback(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if (isAbort(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if (isSecurityError(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = useCallback(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = useCallback(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = useCallback(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = useCallback(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if (isIeOrEdge()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = useMemo(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler(composeEventHandlers(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler(composeEventHandlers(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler(composeEventHandlers(onBlur, onBlurCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onClickCb)),\n        onDragEnter: composeDragHandler(composeEventHandlers(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler(composeEventHandlers(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler(composeEventHandlers(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler(composeEventHandlers(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = useCallback(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = useMemo(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler(composeEventHandlers(onChange, onDropCb)),\n        onClick: composeHandler(composeEventHandlers(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\nexport { ErrorCode } from \"./utils/index.js\";", "import * as React from 'react';\nimport { useCallbackRef } from '@/hooks/use-callback-ref';\n\n/**\r\n * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\r\n */\n\ntype UseControllableStateParams<T> = {\n  prop?: T | undefined;\n  defaultProp?: T | undefined;\n  onChange?: (state: T) => void;\n};\ntype SetStateFn<T> = (prevState?: T) => T;\nfunction useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {}\n}: UseControllableStateParams<T>) {\n  const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n  const handleChange = useCallbackRef(onChange);\n  const setValue: React.Dispatch<React.SetStateAction<T | undefined>> = React.useCallback(nextValue => {\n    if (isControlled) {\n      const setter = nextValue as SetStateFn<T>;\n      const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n      if (value !== prop) handleChange(value as T);\n    } else {\n      setUncontrolledProp(nextValue);\n    }\n  }, [isControlled, prop, setUncontrolledProp, handleChange]);\n  return [value, setValue] as const;\n}\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange\n}: Omit<UseControllableStateParams<T>, 'prop'>) {\n  const uncontrolledState = React.useState<T | undefined>(defaultProp);\n  const [value] = uncontrolledState;\n  const prevValueRef = React.useRef(value);\n  const handleChange = useCallbackRef(onChange);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      handleChange(value as T);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef, handleChange]);\n  return uncontrolledState;\n}\nexport { useControllableState };", "'use client';\n\nimport { X, Upload } from 'lucide-react';\nimport Image from 'next/image';\nimport * as React from 'react';\nimport Dropzone, { type DropzoneProps, type FileRejection } from 'react-dropzone';\nimport { toast } from 'sonner';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { useControllableState } from '@/hooks/use-controllable-state';\nimport { cn, formatBytes } from '@/lib/utils';\ninterface FileUploaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  /**\r\n   * Value of the uploader.\r\n   * @type File[]\r\n   * @default undefined\r\n   * @example value={files}\r\n   */\n  value?: File[];\n\n  /**\r\n   * Function to be called when the value changes.\r\n   * @type React.Dispatch<React.SetStateAction<File[]>>\r\n   * @default undefined\r\n   * @example onValueChange={(files) => setFiles(files)}\r\n   */\n  onValueChange?: React.Dispatch<React.SetStateAction<File[]>>;\n\n  /**\r\n   * Function to be called when files are uploaded.\r\n   * @type (files: File[]) => Promise<void>\r\n   * @default undefined\r\n   * @example onUpload={(files) => uploadFiles(files)}\r\n   */\n  onUpload?: (files: File[]) => Promise<void>;\n\n  /**\r\n   * Progress of the uploaded files.\r\n   * @type Record<string, number> | undefined\r\n   * @default undefined\r\n   * @example progresses={{ \"file1.png\": 50 }}\r\n   */\n  progresses?: Record<string, number>;\n\n  /**\r\n   * Accepted file types for the uploader.\r\n   * @type { [key: string]: string[]}\r\n   * @default\r\n   * ```ts\r\n   * { \"image/*\": [] }\r\n   * ```\r\n   * @example accept={[\"image/png\", \"image/jpeg\"]}\r\n   */\n  accept?: DropzoneProps['accept'];\n\n  /**\r\n   * Maximum file size for the uploader.\r\n   * @type number | undefined\r\n   * @default 1024 * 1024 * 2 // 2MB\r\n   * @example maxSize={1024 * 1024 * 2} // 2MB\r\n   */\n  maxSize?: DropzoneProps['maxSize'];\n\n  /**\r\n   * Maximum number of files for the uploader.\r\n   * @type number | undefined\r\n   * @default 1\r\n   * @example maxFiles={5}\r\n   */\n  maxFiles?: DropzoneProps['maxFiles'];\n\n  /**\r\n   * Whether the uploader should accept multiple files.\r\n   * @type boolean\r\n   * @default false\r\n   * @example multiple\r\n   */\n  multiple?: boolean;\n\n  /**\r\n   * Whether the uploader is disabled.\r\n   * @type boolean\r\n   * @default false\r\n   * @example disabled\r\n   */\n  disabled?: boolean;\n}\nexport function FileUploader(props: FileUploaderProps) {\n  const {\n    value: valueProp,\n    onValueChange,\n    onUpload,\n    progresses,\n    accept = {\n      'image/*': []\n    },\n    maxSize = 1024 * 1024 * 2,\n    maxFiles = 1,\n    multiple = false,\n    disabled = false,\n    className,\n    ...dropzoneProps\n  } = props;\n  const [files, setFiles] = useControllableState({\n    prop: valueProp,\n    onChange: onValueChange\n  });\n  const onDrop = React.useCallback((acceptedFiles: File[], rejectedFiles: FileRejection[]) => {\n    if (!multiple && maxFiles === 1 && acceptedFiles.length > 1) {\n      toast.error('Cannot upload more than 1 file at a time');\n      return;\n    }\n    if ((files?.length ?? 0) + acceptedFiles.length > maxFiles) {\n      toast.error(`Cannot upload more than ${maxFiles} files`);\n      return;\n    }\n    const newFiles = acceptedFiles.map(file => Object.assign(file, {\n      preview: URL.createObjectURL(file)\n    }));\n    const updatedFiles = files ? [...files, ...newFiles] : newFiles;\n    setFiles(updatedFiles);\n    if (rejectedFiles.length > 0) {\n      rejectedFiles.forEach(({\n        file\n      }) => {\n        toast.error(`File ${file.name} was rejected`);\n      });\n    }\n    if (onUpload && updatedFiles.length > 0 && updatedFiles.length <= maxFiles) {\n      const target = updatedFiles.length > 0 ? `${updatedFiles.length} files` : `file`;\n      toast.promise(onUpload(updatedFiles), {\n        loading: `Uploading ${target}...`,\n        success: () => {\n          setFiles([]);\n          return `${target} uploaded`;\n        },\n        error: `Failed to upload ${target}`\n      });\n    }\n  }, [files, maxFiles, multiple, onUpload, setFiles]);\n  function onRemove(index: number) {\n    if (!files) return;\n    const newFiles = files.filter((_, i) => i !== index);\n    setFiles(newFiles);\n    onValueChange?.(newFiles);\n  }\n\n  // Revoke preview url when component unmounts\n  React.useEffect(() => {\n    return () => {\n      if (!files) return;\n      files.forEach(file => {\n        if (isFileWithPreview(file)) {\n          URL.revokeObjectURL(file.preview);\n        }\n      });\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const isDisabled = disabled || (files?.length ?? 0) >= maxFiles;\n  return <div className='relative flex flex-col gap-6 overflow-hidden' data-sentry-component=\"FileUploader\" data-sentry-source-file=\"file-uploader.tsx\">\r\n      <Dropzone onDrop={onDrop} accept={accept} maxSize={maxSize} maxFiles={maxFiles} multiple={maxFiles > 1 || multiple} disabled={isDisabled} data-sentry-element=\"Dropzone\" data-sentry-source-file=\"file-uploader.tsx\">\r\n        {({\n        getRootProps,\n        getInputProps,\n        isDragActive\n      }) => <div {...getRootProps()} className={cn('group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition', 'ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden', isDragActive && 'border-muted-foreground/50', isDisabled && 'pointer-events-none opacity-60', className)} {...dropzoneProps}>\r\n            <input {...getInputProps()} />\r\n            {isDragActive ? <div className='flex flex-col items-center justify-center gap-4 sm:px-5'>\r\n                <div className='rounded-full border border-dashed p-3'>\r\n                  <Upload className='text-muted-foreground size-7' aria-hidden='true' />\r\n                </div>\r\n                <p className='text-muted-foreground font-medium'>\r\n                  Drop the files here\r\n                </p>\r\n              </div> : <div className='flex flex-col items-center justify-center gap-4 sm:px-5'>\r\n                <div className='rounded-full border border-dashed p-3'>\r\n                  <Upload className='text-muted-foreground size-7' aria-hidden='true' />\r\n                </div>\r\n                <div className='space-y-px'>\r\n                  <p className='text-muted-foreground font-medium'>\r\n                    Drag {`'n'`} drop files here, or click to select files\r\n                  </p>\r\n                  <p className='text-muted-foreground/70 text-sm'>\r\n                    You can upload\r\n                    {maxFiles > 1 ? ` ${maxFiles === Infinity ? 'multiple' : maxFiles}\n                      files (up to ${formatBytes(maxSize)} each)` : ` a file with ${formatBytes(maxSize)}`}\r\n                  </p>\r\n                </div>\r\n              </div>}\r\n          </div>}\r\n      </Dropzone>\r\n      {files?.length ? <ScrollArea className='h-fit w-full px-3'>\r\n          <div className='max-h-48 space-y-4'>\r\n            {files?.map((file, index) => <FileCard key={index} file={file} onRemove={() => onRemove(index)} progress={progresses?.[file.name]} />)}\r\n          </div>\r\n        </ScrollArea> : null}\r\n    </div>;\n}\ninterface FileCardProps {\n  file: File;\n  onRemove: () => void;\n  progress?: number;\n}\nfunction FileCard({\n  file,\n  progress,\n  onRemove\n}: FileCardProps) {\n  return <div className='relative flex items-center space-x-4' data-sentry-component=\"FileCard\" data-sentry-source-file=\"file-uploader.tsx\">\r\n      <div className='flex flex-1 space-x-4'>\r\n        {isFileWithPreview(file) ? <Image src={file.preview} alt={file.name} width={48} height={48} loading='lazy' className='aspect-square shrink-0 rounded-md object-cover' /> : null}\r\n        <div className='flex w-full flex-col gap-2'>\r\n          <div className='space-y-px'>\r\n            <p className='text-foreground/80 line-clamp-1 text-sm font-medium'>\r\n              {file.name}\r\n            </p>\r\n            <p className='text-muted-foreground text-xs'>\r\n              {formatBytes(file.size)}\r\n            </p>\r\n          </div>\r\n          {progress ? <Progress value={progress} /> : null}\r\n        </div>\r\n      </div>\r\n      <div className='flex items-center gap-2'>\r\n        <Button type='button' variant='ghost' size='icon' onClick={onRemove} disabled={progress !== undefined && progress < 100} className='size-8 rounded-full' data-sentry-element=\"Button\" data-sentry-source-file=\"file-uploader.tsx\">\r\n          <X className='text-muted-foreground' data-sentry-element=\"X\" data-sentry-source-file=\"file-uploader.tsx\" />\r\n          <span className='sr-only'>Remove file</span>\r\n        </Button>\r\n      </div>\r\n    </div>;\n}\nfunction isFileWithPreview(file: File): file is File & {\n  preview: string;\n} {\n  return 'preview' in file && typeof file.preview === 'string';\n}", "import React from 'react';\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : Object.create(Object.getPrototypeOf(data));\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar isUndefined = (val) => val === undefined;\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = (isKey(path) ? [path] : stringToPath(path)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = React.createContext(null);\nHookFormContext.displayName = 'HookFormContext';\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (React.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = React.useState(control._formState);\n    const _localProxyFormState = React.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: _localProxyFormState.current,\n        exact,\n        callback: (formState) => {\n            !disabled &&\n                updateFormState({\n                    ...control._formState,\n                    ...formState,\n                });\n        },\n    }), [name, disabled, exact]);\n    React.useEffect(() => {\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [control]);\n    return React.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName),\n            get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2, _internal_visited = new WeakSet()) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    if (_internal_visited.has(object1) || _internal_visited.has(object2)) {\n        return true;\n    }\n    _internal_visited.add(object1);\n    _internal_visited.add(object2);\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2, _internal_visited)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, compute, } = props || {};\n    const _defaultValue = React.useRef(defaultValue);\n    const _compute = React.useRef(compute);\n    const _computeFormValues = React.useRef(undefined);\n    _compute.current = compute;\n    const defaultValueMemo = React.useMemo(() => control._getWatch(name, _defaultValue.current), [control, name]);\n    const [value, updateValue] = React.useState(_compute.current ? _compute.current(defaultValueMemo) : defaultValueMemo);\n    useIsomorphicLayoutEffect(() => control._subscribe({\n        name,\n        formState: {\n            values: true,\n        },\n        exact,\n        callback: (formState) => {\n            if (!disabled) {\n                const formValues = generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current);\n                if (_compute.current) {\n                    const computedFormValues = _compute.current(formValues);\n                    if (!deepEqual(computedFormValues, _computeFormValues.current)) {\n                        updateValue(computedFormValues);\n                        _computeFormValues.current = computedFormValues;\n                    }\n                }\n                else {\n                    updateValue(formValues);\n                }\n            }\n        },\n    }), [control, disabled, name, exact]);\n    React.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister, defaultValue, } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const defaultValueMemo = React.useMemo(() => get(control._formValues, name, get(control._defaultValues, name, defaultValue)), [control, name, defaultValue]);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: defaultValueMemo,\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _props = React.useRef(props);\n    const _registerProps = React.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    _props.current = props;\n    const fieldState = React.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const onChange = React.useCallback((event) => _registerProps.current.onChange({\n        target: {\n            value: getEventValue(event),\n            name: name,\n        },\n        type: EVENTS.CHANGE,\n    }), [name]);\n    const onBlur = React.useCallback(() => _registerProps.current.onBlur({\n        target: {\n            value: get(control._formValues, name),\n            name: name,\n        },\n        type: EVENTS.BLUR,\n    }), [name, control._formValues]);\n    const ref = React.useCallback((elm) => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: () => elm.focus && elm.focus(),\n                select: () => elm.select && elm.select(),\n                setCustomValidity: (message) => elm.setCustomValidity(message),\n                reportValidity: () => elm.reportValidity(),\n            };\n        }\n    }, [control._fields, name]);\n    const field = React.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange,\n        onBlur,\n        ref,\n    }), [name, disabled, formState.disabled, onChange, onBlur, ref, value]);\n    React.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...(isBoolean(_props.current.disabled)\n                ? { disabled: _props.current.disabled }\n                : {}),\n        });\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    React.useEffect(() => {\n        control._setDisabledField({\n            disabled,\n            name,\n        });\n    }, [disabled, name, control]);\n    return React.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = React.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType && encType !== 'multipart/form-data'\n                                ? { 'Content-Type': encType }\n                                : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    React.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (React.createElement(React.Fragment, null, render({\n        submit,\n    }))) : (React.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar isRegex = (value) => value instanceof RegExp;\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        if (foundError && foundError.root && foundError.root.type) {\n            return {\n                name: `${fieldName}.root`,\n                error: foundError.root,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isMessage = (value) => isString(value);\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState,\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _setValid = async (shouldUpdateValid) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValid ||\n                _proxySubscribeFormState.isValid ||\n                shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _runSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating ||\n                _proxyFormState.validatingFields ||\n                _proxySubscribeFormState.isValidating ||\n                _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields ||\n                _proxySubscribeFormState.touchedFields) &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        ((_proxyFormState.dirtyFields ||\n                            _proxySubscribeFormState.dirtyFields) &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            ((_proxyFormState.touchedFields ||\n                                _proxySubscribeFormState.touchedFields) &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef) => {\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data) => data === checkboxRef.value);\n                                }\n                                else {\n                                    checkboxRef.checked =\n                                        fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues),\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            if (!value.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value[fieldKey];\n            const fieldName = name + '.' + fieldKey;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues),\n            });\n            if ((_proxyFormState.isDirty ||\n                _proxyFormState.dirtyFields ||\n                _proxySubscribeFormState.isDirty ||\n                _proxySubscribeFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState, name });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues),\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type\n                ? getFieldValue(field._f)\n                : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                if (!target || !target.readOnly) {\n                    field._f.onBlur && field._f.onBlur(event);\n                    delayErrorCallback && delayErrorCallback(0);\n                }\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.state.next({\n                    name,\n                    type: event.type,\n                    values: cloneObject(_formValues),\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === 'onBlur') {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    }\n                    else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid ||\n                        _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                ((_proxyFormState.isValid || _proxySubscribeFormState.isValid) &&\n                    isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.state.subscribe({\n            next: (payload) => 'values' in payload &&\n                name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const _subscribe = (props) => _subjects.state.subscribe({\n        next: (formState) => {\n            if (shouldSubscribeByName(props.name, formState.name, props.exact) &&\n                shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                props.callback({\n                    values: { ..._formValues },\n                    ..._formState,\n                    ...formState,\n                    defaultValues: _defaultValues,\n                });\n            }\n        },\n    }).unsubscribe;\n    const subscribe = (props) => {\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState,\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState,\n        });\n    };\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues),\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist &&\n                e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _runSchema();\n            _formState.errors = errors;\n            fieldValues = cloneObject(values);\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                unset(fieldValues, name);\n            }\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (keepStateOptions.keepFieldsRef) {\n                    for (const fieldName of _names.mount) {\n                        setValue(fieldName, get(values, fieldName));\n                    }\n                }\n                else {\n                    _fields = {};\n                }\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.state.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n            defaultValues: _defaultValues,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _focusError,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n    return {\n        ...methods,\n        formControl: methods,\n    };\n}\n\nvar generateId = () => {\n    if (typeof crypto !== 'undefined' && crypto.randomUUID) {\n        return crypto.randomUUID();\n    }\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = React.useState(control._getFieldArray(name));\n    const ids = React.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = React.useRef(fields);\n    const _actioned = React.useRef(false);\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    React.useMemo(() => rules &&\n        control.register(name, rules), [control, rules, name]);\n    useIsomorphicLayoutEffect(() => control._subjects.array.subscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === name || !fieldArrayName) {\n                const fieldValues = get(values, name);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n    }).unsubscribe, [control, name]);\n    const updateValues = React.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._setFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    React.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted) &&\n            !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues),\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._setValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    React.useEffect(() => {\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return () => {\n            const updateMounted = (name, value) => {\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: React.useCallback(swap, [updateValues, name, control]),\n        move: React.useCallback(move, [updateValues, name, control]),\n        prepend: React.useCallback(prepend, [updateValues, name, control]),\n        append: React.useCallback(append, [updateValues, name, control]),\n        remove: React.useCallback(remove, [updateValues, name, control]),\n        insert: React.useCallback(insert$1, [updateValues, name, control]),\n        update: React.useCallback(update, [updateValues, name, control]),\n        replace: React.useCallback(replace, [updateValues, name, control]),\n        fields: React.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = React.useRef(undefined);\n    const _values = React.useRef(undefined);\n    const [formState, updateFormState] = React.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        if (props.formControl) {\n            _formControl.current = {\n                ...props.formControl,\n                formState,\n            };\n            if (props.defaultValues && !isFunction(props.defaultValues)) {\n                props.formControl.reset(props.defaultValues, props.resetOptions);\n            }\n        }\n        else {\n            const { formControl, ...rest } = createFormControl(props);\n            _formControl.current = {\n                ...rest,\n                formState,\n            };\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(() => {\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: () => updateFormState({ ...control._formState }),\n            reRenderRoot: true,\n        });\n        updateFormState((data) => ({\n            ...data,\n            isReady: true,\n        }));\n        control._formState.isReady = true;\n        return sub;\n    }, [control]);\n    React.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    React.useEffect(() => {\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n    }, [control, props.mode, props.reValidateMode]);\n    React.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n            control._focusError();\n        }\n    }, [control, props.errors]);\n    React.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.state.next({\n                values: control._getWatch(),\n            });\n    }, [control, props.shouldUnregister]);\n    React.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    React.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, {\n                keepFieldsRef: true,\n                ...control._options.resetOptions,\n            });\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [control, props.values]);\n    React.useEffect(() => {\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\nexport { Controller, Form, FormProvider, appendErrors, createFormControl, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };\n//# sourceMappingURL=index.esm.mjs.map\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport { Controller, FormProvider, useFormContext, useFormState, type ControllerProps, type FieldPath, type FieldValues } from 'react-hook-form';\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\nconst Form = FormProvider;\ntype FormFieldContextValue<TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>> = {\n  name: TName;\n};\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\nconst FormField = <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return <FormFieldContext.Provider value={{\n    name: props.name\n  }} data-sentry-element=\"FormFieldContext.Provider\" data-sentry-component=\"FormField\" data-sentry-source-file=\"form.tsx\">\r\n      <Controller {...props} data-sentry-element=\"Controller\" data-sentry-source-file=\"form.tsx\" />\r\n    </FormFieldContext.Provider>;\n};\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const {\n    getFieldState\n  } = useFormContext();\n  const formState = useFormState({\n    name: fieldContext.name\n  });\n  const fieldState = getFieldState(fieldContext.name, formState);\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n  const {\n    id\n  } = itemContext;\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\ntype FormItemContextValue = {\n  id: string;\n};\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\nfunction FormItem({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  const id = React.useId();\n  return <FormItemContext.Provider value={{\n    id\n  }} data-sentry-element=\"FormItemContext.Provider\" data-sentry-component=\"FormItem\" data-sentry-source-file=\"form.tsx\">\r\n      <div data-slot='form-item' className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>;\n}\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const {\n    error,\n    formItemId\n  } = useFormField();\n  return <Label data-slot='form-label' data-error={!!error} className={cn('data-[error=true]:text-destructive', className)} htmlFor={formItemId} {...props} data-sentry-element=\"Label\" data-sentry-component=\"FormLabel\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormControl({\n  ...props\n}: React.ComponentProps<typeof Slot>) {\n  const {\n    error,\n    formItemId,\n    formDescriptionId,\n    formMessageId\n  } = useFormField();\n  return <Slot data-slot='form-control' id={formItemId} aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`} aria-invalid={!!error} {...props} data-sentry-element=\"Slot\" data-sentry-component=\"FormControl\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormDescription({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    formDescriptionId\n  } = useFormField();\n  return <p data-slot='form-description' id={formDescriptionId} className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"FormDescription\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormMessage({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    error,\n    formMessageId\n  } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n  if (!body) {\n    return null;\n  }\n  return <p data-slot='form-message' id={formMessageId} className={cn('text-destructive text-sm', className)} {...props} data-sentry-component=\"FormMessage\" data-sentry-source-file=\"form.tsx\">\r\n      {body}\r\n    </p>;\n}\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField };", "import{get as t,set as e}from\"react-hook-form\";const s=(e,s,o)=>{if(e&&\"reportValidity\"in e){const r=t(o,s);e.setCustomValidity(r&&r.message||\"\"),e.reportValidity()}},o=(t,e)=>{for(const o in e.fields){const r=e.fields[o];r&&r.ref&&\"reportValidity\"in r.ref?s(r.ref,o,t):r.refs&&r.refs.forEach(e=>s(e,o,t))}},r=(s,r)=>{r.shouldUseNativeValidation&&o(s,r);const f={};for(const o in s){const n=t(r.fields,o),a=Object.assign(s[o]||{},{ref:n&&n.ref});if(i(r.names||Object.keys(s),o)){const s=Object.assign({},t(f,o));e(s,\"root\",a),e(f,o,s)}else e(f,o,a)}return f},i=(t,e)=>t.some(t=>t.startsWith(e+\".\"));export{r as toNestErrors,o as validateFieldsNatively};\n//# sourceMappingURL=resolvers.mjs.map\n", "import{validateFieldsNatively as r,toNestErrors as e}from\"@hookform/resolvers\";import{appendErrors as o}from\"react-hook-form\";var n=function(r,e){for(var n={};r.length;){var t=r[0],s=t.code,i=t.message,a=t.path.join(\".\");if(!n[a])if(\"unionErrors\"in t){var u=t.unionErrors[0].errors[0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:s};if(\"unionErrors\"in t&&t.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return r.push(e)})}),e){var c=n[a].types,f=c&&c[t.code];n[a]=o(a,e,n,s,f?[].concat(f,t.message):t.message)}r.shift()}return n},t=function(o,t,s){return void 0===s&&(s={}),function(i,a,u){try{return Promise.resolve(function(e,n){try{var a=Promise.resolve(o[\"sync\"===s.mode?\"parse\":\"parseAsync\"](i,t)).then(function(e){return u.shouldUseNativeValidation&&r({},u),{errors:{},values:s.raw?i:e}})}catch(r){return n(r)}return a&&a.then?a.then(void 0,n):a}(0,function(r){if(function(r){return Array.isArray(null==r?void 0:r.errors)}(r))return{values:{},errors:e(n(r.errors,!u.shouldUseNativeValidation&&\"all\"===u.criteriaMode),u)};throw r}))}catch(r){return Promise.reject(r)}}};export{t as zodResolver};\n//# sourceMappingURL=zod.module.js.map\n", "'use client';\n\nimport { FileUploader } from '@/components/file-uploader';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Product } from '@/constants/mock-api';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { useForm } from 'react-hook-form';\nimport * as z from 'zod';\nconst MAX_FILE_SIZE = 5000000;\nconst ACCEPTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\nconst formSchema = z.object({\n  image: z.any().refine(files => files?.length == 1, 'Image is required.').refine(files => files?.[0]?.size <= MAX_FILE_SIZE, `Max file size is 5MB.`).refine(files => ACCEPTED_IMAGE_TYPES.includes(files?.[0]?.type), '.jpg, .jpeg, .png and .webp files are accepted.'),\n  name: z.string().min(2, {\n    message: 'Product name must be at least 2 characters.'\n  }),\n  category: z.string(),\n  price: z.number(),\n  description: z.string().min(10, {\n    message: 'Description must be at least 10 characters.'\n  })\n});\nexport default function ProductForm({\n  initialData,\n  pageTitle\n}: {\n  initialData: Product | null;\n  pageTitle: string;\n}) {\n  const defaultValues = {\n    name: initialData?.name || '',\n    category: initialData?.category || '',\n    price: initialData?.price || 0,\n    description: initialData?.description || ''\n  };\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    values: defaultValues\n  });\n  function onSubmit(values: z.infer<typeof formSchema>) {\n    // Form submission logic would be implemented here\n  }\n  return <Card className='mx-auto w-full' data-sentry-element=\"Card\" data-sentry-component=\"ProductForm\" data-sentry-source-file=\"product-form.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"product-form.tsx\">\r\n        <CardTitle className='text-left text-2xl font-bold' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"product-form.tsx\">\r\n          {pageTitle}\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"product-form.tsx\">\r\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"product-form.tsx\">\r\n          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>\r\n            <FormField control={form.control} name='image' render={({\n            field\n          }) => <div className='space-y-6'>\r\n                  <FormItem className='w-full'>\r\n                    <FormLabel>Images</FormLabel>\r\n                    <FormControl>\r\n                      <FileUploader value={field.value} onValueChange={field.onChange} maxFiles={4} maxSize={4 * 1024 * 1024}\n                // disabled={loading}\n                // progresses={progresses}\n                // pass the onUpload function here for direct upload\n                // onUpload={uploadFiles}\n                // disabled={isUploading}\n                />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                </div>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n\r\n            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n              <FormField control={form.control} name='name' render={({\n              field\n            }) => <FormItem>\r\n                    <FormLabel>Product Name</FormLabel>\r\n                    <FormControl>\r\n                      <Input placeholder='Enter product name' {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n              <FormField control={form.control} name='category' render={({\n              field\n            }) => <FormItem>\r\n                    <FormLabel>Category</FormLabel>\r\n                    <Select onValueChange={value => field.onChange(value)} value={field.value[field.value.length - 1]}>\r\n                      <FormControl>\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder='Select categories' />\r\n                        </SelectTrigger>\r\n                      </FormControl>\r\n                      <SelectContent>\r\n                        <SelectItem value='beauty'>Beauty Products</SelectItem>\r\n                        <SelectItem value='electronics'>Electronics</SelectItem>\r\n                        <SelectItem value='clothing'>Clothing</SelectItem>\r\n                        <SelectItem value='home'>Home & Garden</SelectItem>\r\n                        <SelectItem value='sports'>\r\n                          Sports & Outdoors\r\n                        </SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <FormMessage />\r\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n              <FormField control={form.control} name='price' render={({\n              field\n            }) => <FormItem>\r\n                    <FormLabel>Price</FormLabel>\r\n                    <FormControl>\r\n                      <Input type='number' step='0.01' placeholder='Enter price' {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n            </div>\r\n            <FormField control={form.control} name='description' render={({\n            field\n          }) => <FormItem>\r\n                  <FormLabel>Description</FormLabel>\r\n                  <FormControl>\r\n                    <Textarea placeholder='Enter product description' className='resize-none' {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"product-form.tsx\" />\r\n            <Button type='submit' data-sentry-element=\"Button\" data-sentry-source-file=\"product-form.tsx\">Add Product</Button>\r\n          </form>\r\n        </Form>\r\n      </CardContent>\r\n    </Card>;\n}", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n", "import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n", "'use client';\n\nimport * as React from 'react';\nimport * as SelectPrimitive from '@radix-ui/react-select';\nimport { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot='select' {...props} data-sentry-element=\"SelectPrimitive.Root\" data-sentry-component=\"Select\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot='select-group' {...props} data-sentry-element=\"SelectPrimitive.Group\" data-sentry-component=\"SelectGroup\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot='select-value' {...props} data-sentry-element=\"SelectPrimitive.Value\" data-sentry-component=\"SelectValue\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectTrigger({\n  className,\n  size = 'default',\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: 'sm' | 'default';\n}) {\n  return <SelectPrimitive.Trigger data-slot='select-trigger' data-size={size} className={cn(\"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"SelectPrimitive.Trigger\" data-sentry-component=\"SelectTrigger\" data-sentry-source-file=\"select.tsx\">\r\n      {children}\r\n      <SelectPrimitive.Icon asChild data-sentry-element=\"SelectPrimitive.Icon\" data-sentry-source-file=\"select.tsx\">\r\n        <ChevronDownIcon className='size-4 opacity-50' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>;\n}\nfunction SelectContent({\n  className,\n  children,\n  position = 'popper',\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return <SelectPrimitive.Portal data-sentry-element=\"SelectPrimitive.Portal\" data-sentry-component=\"SelectContent\" data-sentry-source-file=\"select.tsx\">\r\n      <SelectPrimitive.Content data-slot='select-content' className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md', position === 'popper' && 'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1', className)} position={position} {...props} data-sentry-element=\"SelectPrimitive.Content\" data-sentry-source-file=\"select.tsx\">\r\n        <SelectScrollUpButton data-sentry-element=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\" />\r\n        <SelectPrimitive.Viewport className={cn('p-1', position === 'popper' && 'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1')} data-sentry-element=\"SelectPrimitive.Viewport\" data-sentry-source-file=\"select.tsx\">\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton data-sentry-element=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>;\n}\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return <SelectPrimitive.Label data-slot='select-label' className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)} {...props} data-sentry-element=\"SelectPrimitive.Label\" data-sentry-component=\"SelectLabel\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return <SelectPrimitive.Item data-slot='select-item' className={cn(\"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\", className)} {...props} data-sentry-element=\"SelectPrimitive.Item\" data-sentry-component=\"SelectItem\" data-sentry-source-file=\"select.tsx\">\r\n      <span className='absolute right-2 flex size-3.5 items-center justify-center'>\r\n        <SelectPrimitive.ItemIndicator data-sentry-element=\"SelectPrimitive.ItemIndicator\" data-sentry-source-file=\"select.tsx\">\r\n          <CheckIcon className='size-4' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"select.tsx\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText data-sentry-element=\"SelectPrimitive.ItemText\" data-sentry-source-file=\"select.tsx\">{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>;\n}\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return <SelectPrimitive.Separator data-slot='select-separator' className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)} {...props} data-sentry-element=\"SelectPrimitive.Separator\" data-sentry-component=\"SelectSeparator\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return <SelectPrimitive.ScrollUpButton data-slot='select-scroll-up-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollUpButton\" data-sentry-component=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronUpIcon className='size-4' data-sentry-element=\"ChevronUpIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollUpButton>;\n}\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return <SelectPrimitive.ScrollDownButton data-slot='select-scroll-down-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollDownButton\" data-sentry-component=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronDownIcon className='size-4' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollDownButton>;\n}\nexport { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectScrollDownButton, SelectScrollUpButton, SelectSeparator, SelectTrigger, SelectValue };", "////////////////////////////////////////////////////////////////////////////////\r\n// 🛑 Nothing in here has anything to do with Nextjs, it's just a fake database\r\n////////////////////////////////////////////////////////////////////////////////\r\n\r\nimport { faker } from '@faker-js/faker';\r\nimport { matchSorter } from 'match-sorter'; // For filtering\r\n\r\nexport const delay = (ms: number) =>\r\n  new Promise((resolve) => setTimeout(resolve, ms));\r\n\r\n// Define the shape of Product data\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n// Mock product data store\r\nexport const fakeProducts = {\r\n  records: [] as Product[], // Holds the list of product objects\r\n\r\n  // Initialize with sample data\r\n  initialize() {\r\n    const sampleProducts: Product[] = [];\r\n    function generateRandomProductData(id: number): Product {\r\n      const categories = [\r\n        'Electronics',\r\n        'Furniture',\r\n        'Clothing',\r\n        'Toys',\r\n        'Groceries',\r\n        'Books',\r\n        'Jewelry',\r\n        'Beauty Products'\r\n      ];\r\n\r\n      return {\r\n        id,\r\n        name: faker.commerce.productName(),\r\n        description: faker.commerce.productDescription(),\r\n        created_at: faker.date\r\n          .between({ from: '2022-01-01', to: '2023-12-31' })\r\n          .toISOString(),\r\n        price: parseFloat(faker.commerce.price({ min: 5, max: 500, dec: 2 })),\r\n        photo_url: `https://api.slingacademy.com/public/sample-products/${id}.png`,\r\n        category: faker.helpers.arrayElement(categories),\r\n        updated_at: faker.date.recent().toISOString()\r\n      };\r\n    }\r\n\r\n    // Generate remaining records\r\n    for (let i = 1; i <= 20; i++) {\r\n      sampleProducts.push(generateRandomProductData(i));\r\n    }\r\n\r\n    this.records = sampleProducts;\r\n  },\r\n\r\n  // Get all products with optional category filtering and search\r\n  async getAll({\r\n    categories = [],\r\n    search\r\n  }: {\r\n    categories?: string[];\r\n    search?: string;\r\n  }) {\r\n    let products = [...this.records];\r\n\r\n    // Filter products based on selected categories\r\n    if (categories.length > 0) {\r\n      products = products.filter((product) =>\r\n        categories.includes(product.category)\r\n      );\r\n    }\r\n\r\n    // Search functionality across multiple fields\r\n    if (search) {\r\n      products = matchSorter(products, search, {\r\n        keys: ['name', 'description', 'category']\r\n      });\r\n    }\r\n\r\n    return products;\r\n  },\r\n\r\n  // Get paginated results with optional category filtering and search\r\n  async getProducts({\r\n    page = 1,\r\n    limit = 10,\r\n    categories,\r\n    search\r\n  }: {\r\n    page?: number;\r\n    limit?: number;\r\n    categories?: string;\r\n    search?: string;\r\n  }) {\r\n    await delay(1000);\r\n    const categoriesArray = categories ? categories.split('.') : [];\r\n    const allProducts = await this.getAll({\r\n      categories: categoriesArray,\r\n      search\r\n    });\r\n    const totalProducts = allProducts.length;\r\n\r\n    // Pagination logic\r\n    const offset = (page - 1) * limit;\r\n    const paginatedProducts = allProducts.slice(offset, offset + limit);\r\n\r\n    // Mock current time\r\n    const currentTime = new Date().toISOString();\r\n\r\n    // Return paginated response\r\n    return {\r\n      success: true,\r\n      time: currentTime,\r\n      message: 'Sample data for testing and learning purposes',\r\n      total_products: totalProducts,\r\n      offset,\r\n      limit,\r\n      products: paginatedProducts\r\n    };\r\n  },\r\n\r\n  // Get a specific product by its ID\r\n  async getProductById(id: number) {\r\n    await delay(1000); // Simulate a delay\r\n\r\n    // Find the product by its ID\r\n    const product = this.records.find((product) => product.id === id);\r\n\r\n    if (!product) {\r\n      return {\r\n        success: false,\r\n        message: `Product with ID ${id} not found`\r\n      };\r\n    }\r\n\r\n    // Mock current time\r\n    const currentTime = new Date().toISOString();\r\n\r\n    return {\r\n      success: true,\r\n      time: currentTime,\r\n      message: `Product with ID ${id} found`,\r\n      product\r\n    };\r\n  }\r\n};\r\n\r\n// Initialize sample products\r\nfakeProducts.initialize();\r\n", "/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n", "import React from 'react';\nimport { Card, CardContent, CardHeader } from './ui/card';\nimport { Skeleton } from './ui/skeleton';\nexport default function FormCardSkeleton() {\n  return <Card className='mx-auto w-full' data-sentry-element=\"Card\" data-sentry-component=\"FormCardSkeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\">\r\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"form-card-skeleton.tsx\">\r\n        <Skeleton className='h-8 w-48' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Title */}\r\n      </CardHeader>\r\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"form-card-skeleton.tsx\">\r\n        <div className='space-y-8'>\r\n          {/* Image upload area skeleton */}\r\n          <div className='space-y-6'>\r\n            <Skeleton className='h-4 w-16' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Label */}\r\n            <Skeleton className='h-32 w-full rounded-lg' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Upload area */}\r\n          </div>\r\n\r\n          {/* Grid layout for form fields */}\r\n          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n            {/* Product Name field */}\r\n            <div className='space-y-2'>\r\n              <Skeleton className='h-4 w-24' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Label */}\r\n              <Skeleton className='h-10 w-full' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Input */}\r\n            </div>\r\n\r\n            {/* Category field */}\r\n            <div className='space-y-2'>\r\n              <Skeleton className='h-4 w-20' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Label */}\r\n              <Skeleton className='h-10 w-full' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Select */}\r\n            </div>\r\n\r\n            {/* Price field */}\r\n            <div className='space-y-2'>\r\n              <Skeleton className='h-4 w-16' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Label */}\r\n              <Skeleton className='h-10 w-full' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Input */}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Description field */}\r\n          <div className='space-y-2'>\r\n            <Skeleton className='h-4 w-24' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Label */}\r\n            <Skeleton className='h-32 w-full' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" /> {/* Textarea */}\r\n          </div>\r\n\r\n          {/* Submit button */}\r\n          <Skeleton className='h-10 w-28' data-sentry-element=\"Skeleton\" data-sentry-source-file=\"form-card-skeleton.tsx\" />\r\n        </div>\r\n      </CardContent>\r\n    </Card>;\n}", "export * from '../client/components/navigation.react-server';\n\n//# sourceMappingURL=navigation.react-server.js.map", "import { fakeProducts, Product } from '@/constants/mock-api';\nimport { notFound } from 'next/navigation';\nimport ProductForm from './product-form';\ntype TProductViewPageProps = {\n  productId: string;\n};\nexport default async function ProductViewPage({\n  productId\n}: TProductViewPageProps) {\n  let product = null;\n  let pageTitle = 'Create New Product';\n  if (productId !== 'new') {\n    const data = await fakeProducts.getProductById(Number(productId));\n    product = data.product as Product;\n    if (!product) {\n      notFound();\n    }\n    pageTitle = `Edit Product`;\n  }\n  return <ProductForm initialData={product} pageTitle={pageTitle} data-sentry-element=\"ProductForm\" data-sentry-component=\"ProductViewPage\" data-sentry-source-file=\"product-view-page.tsx\" />;\n}", "import FormCardSkeleton from '@/components/form-card-skeleton';\nimport Page<PERSON>ontainer from '@/components/layout/page-container';\nimport { Suspense } from 'react';\nimport ProductViewPage from '@/features/products/components/product-view-page';\nexport const metadata = {\n  title: 'Dashboard : Product View'\n};\ntype PageProps = {\n  params: Promise<{\n    productId: string;\n  }>;\n};\nexport default async function Page(props: PageProps) {\n  const params = await props.params;\n  return <PageContainer scrollable data-sentry-element=\"PageContainer\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex-1 space-y-4'>\r\n        <Suspense fallback={<FormCardSkeleton />} data-sentry-element=\"Suspense\" data-sentry-source-file=\"page.tsx\">\r\n          <ProductViewPage productId={params.productId} data-sentry-element=\"ProductViewPage\" data-sentry-source-file=\"page.tsx\" />\r\n        </Suspense>\r\n      </div>\r\n    </PageContainer>;\n}", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\product\\\\[productId]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'product',\n        {\n        children: [\n        '[productId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\product\\\\[productId]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\product\\\\[productId]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/product/[productId]/page\",\n        pathname: \"/dashboard/product/[productId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import * as React from 'react';\n\n/**\r\n * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-callback-ref/src/useCallbackRef.tsx\r\n */\n\n/**\r\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\r\n * prop or avoid re-executing effects when passed as a dependency\r\n */\nfunction useCallbackRef<T extends (...args: never[]) => unknown>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\nexport { useCallbackRef };", "module.exports = require(\"events\");"], "names": ["forbidden", "HTTP_ERROR_FALLBACK_ERROR_CODE", "Progress", "React", "className", "value", "props", "ref", "ProgressPrimitive", "cn", "style", "transform", "displayName", "Card", "div", "data-slot", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "Skeleton", "notFound", "DIGEST", "error", "digest", "Label", "LabelPrimitive", "data-sentry-element", "CardAction", "unstable_rethrow", "require", "ScrollArea", "children", "ScrollAreaPrimitive", "<PERSON><PERSON>Bar", "orientation", "<PERSON><PERSON><PERSON><PERSON>", "scrollable", "FileUploader", "valueProp", "onValueChange", "onUpload", "progresses", "accept", "maxSize", "maxFiles", "multiple", "disabled", "dropzoneProps", "files", "setFiles", "useControllableState", "prop", "defaultProp", "onChange", "uncontrolledProp", "setUncontrolledProp", "useUncontrolledState", "uncontrolledState", "prevValueRef", "handleChange", "useCallbackRef", "current", "isControlled", "undefined", "nextValue", "onDrop", "acceptedFiles", "rejectedFiles", "length", "toast", "newFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "updatedFiles", "for<PERSON>ach", "name", "target", "promise", "loading", "success", "isFileWithPreview", "revokeObjectURL", "isDisabled", "Dropzone", "getRootProps", "getInputProps", "isDragActive", "input", "Upload", "aria-hidden", "p", "Infinity", "formatBytes", "index", "FileCard", "onRemove", "filter", "_", "i", "progress", "Image", "src", "alt", "width", "height", "size", "<PERSON><PERSON>", "type", "variant", "onClick", "X", "span", "Form", "FormProvider", "FormFieldContext", "FormField", "Provider", "Controller", "useFormField", "fieldContext", "itemContext", "FormItemContext", "getFieldState", "useFormContext", "formState", "useFormState", "fieldState", "id", "formItemId", "formDescriptionId", "formMessageId", "FormItem", "FormLabel", "data-error", "htmlFor", "FormControl", "Slot", "aria-<PERSON><PERSON>", "aria-invalid", "FormMessage", "body", "String", "message", "ACCEPTED_IMAGE_TYPES", "formSchema", "z", "image", "refine", "MAX_FILE_SIZE", "includes", "min", "category", "price", "description", "ProductForm", "initialData", "pageTitle", "defaultValues", "form", "useForm", "resolver", "zodResolver", "onSubmit", "handleSubmit", "control", "render", "field", "Input", "placeholder", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "step", "Textarea", "textarea", "metadata", "title", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "serverComponentModule.default", "unauthorized", "Error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "SelectPrimitive", "data-size", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDownIcon", "position", "SelectScrollUpButton", "SelectScrollDownButton", "CheckIcon", "ChevronUpIcon", "delay", "Promise", "setTimeout", "resolve", "ms", "fakeProducts", "records", "initialize", "sampleProducts", "push", "faker", "commerce", "productName", "productDescription", "created_at", "date", "between", "from", "to", "toISOString", "parseFloat", "max", "dec", "photo_url", "helpers", "arrayElement", "updated_at", "recent", "getAll", "categories", "search", "products", "product", "matchSorter", "keys", "getProducts", "page", "limit", "categoriesArray", "split", "allProducts", "totalProducts", "offset", "paginatedProducts", "slice", "time", "currentTime", "Date", "total_products", "getProductById", "find", "ReadonlyURLSearchParams", "RedirectType", "permanentRedirect", "redirect", "ReadonlyURLSearchParamsError", "constructor", "URLSearchParams", "append", "delete", "set", "sort", "FormCardSkeleton", "ProductViewPage", "productId", "data", "Number", "Page", "params", "Suspense", "fallback", "getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "actionAsyncStorage", "url", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "REDIRECT_ERROR_CODE", "getStore", "isAction", "replace", "PermanentRedirect", "isRedirectError", "join", "at", "callback", "callback<PERSON><PERSON>", "args"], "sourceRoot": ""}