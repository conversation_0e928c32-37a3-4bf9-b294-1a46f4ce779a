try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="ba98b2c5-a3a0-41e6-9f78-71925562048b",e._sentryDebugIdIdentifier="sentry-dbid-ba98b2c5-a3a0-41e6-9f78-71925562048b")}catch(e){}"use strict";(()=>{var e={};e.id=1880,e.ids=[1880],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},28500:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>T,routeModule:()=>N,serverHooks:()=>P,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>b,GET:()=>I,HEAD:()=>v,OPTIONS:()=>E,PATCH:()=>w,POST:()=>g,PUT:()=>y});var o=t(3690),n=t(56947),a=t(75250),i=t(63033),u=t(62187),d=t(18621),p=t(32230),c=t(74683),l=t(7688);async function h(e){try{let r=e.nextUrl.searchParams,t=r.get("moduleId"),s=r.get("teacherId");if(!t)return u.NextResponse.json({error:"Module ID required"},{status:400});if(s){let e=await d.db.select({moduleId:p.modules.id,courseId:p.modules.courseId,teacherId:p.courses.teacherId}).from(p.modules).leftJoin(p.courses,(0,c.eq)(p.modules.courseId,p.courses.id)).where((0,c.Uo)((0,c.eq)(p.modules.id,parseInt(t)),(0,c.eq)(p.courses.teacherId,parseInt(s)))).limit(1);if(0===e.length)return u.NextResponse.json({error:"Module not found or access denied"},{status:403})}let o=await d.db.select().from(p.chapters).where((0,c.eq)(p.chapters.moduleId,parseInt(t))),n=await Promise.all(o.map(async e=>{let r=await d.db.select({count:p.quizzes.id}).from(p.quizzes).where((0,c.eq)(p.quizzes.chapterId,e.id));return{...e,content:e.content?JSON.parse(e.content):null,quizCount:r.length}}));return u.NextResponse.json({chapters:n})}catch(e){return console.error("Error fetching chapters:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let{name:r,content:t,moduleId:s,teacherId:o,orderIndex:n}=await e.json();if(!r||!s)return u.NextResponse.json({error:"Name and module ID are required"},{status:400});let a=await d.db.select({moduleId:p.modules.id,courseId:p.modules.courseId,teacherId:p.courses.teacherId}).from(p.modules).leftJoin(p.courses,(0,c.eq)(p.modules.courseId,p.courses.id)).where((0,c.eq)(p.modules.id,s)).limit(1);if(0===a.length)return u.NextResponse.json({error:"Module not found"},{status:404});if(o&&a[0].teacherId!==o)return u.NextResponse.json({error:"Not authorized to add chapters to this module"},{status:403});let i=n;if(void 0===i){let e=await d.db.select({orderIndex:p.chapters.orderIndex}).from(p.chapters).where((0,c.eq)(p.chapters.moduleId,s));i=e.length>0?Math.max(...e.map(e=>e.orderIndex||0))+1:1}let l=await d.db.insert(p.chapters).values({name:r,content:t?JSON.stringify(t):null,moduleId:s,orderIndex:i}).returning();return u.NextResponse.json({chapter:{...l[0],content:l[0].content},message:"Chapter created successfully"},{status:201})}catch(e){return console.error("Error creating chapter:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...i},f="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function m(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=f?.getStore();o=e?.headers}catch{}return l.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/chapters",headers:o}).apply(t,s)}})}let I=m(h,"GET"),g=m(x,"POST"),y=m(void 0,"PUT"),w=m(void 0,"PATCH"),b=m(void 0,"DELETE"),v=m(void 0,"HEAD"),E=m(void 0,"OPTIONS"),N=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/chapters/route",pathname:"/api/chapters",filename:"route",bundlePath:"app/api/chapters/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\chapters\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:A,serverHooks:P}=N;function T(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:A})}},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(28500));module.exports=s})();
//# sourceMappingURL=route.js.map