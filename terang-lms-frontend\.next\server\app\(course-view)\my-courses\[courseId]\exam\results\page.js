try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="a9518007-90c0-4333-bf06-932dc3dac37f",e._sentryDebugIdIdentifier="sentry-dbid-a9518007-90c0-4333-bf06-932dc3dac37f")}catch(e){}(()=>{var e={};e.id=315,e.ids=[315],e.modules={2086:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Cancel01Icon",[["path",{d:"M19 5L5 19M5 5L19 19",stroke:"currentColor",key:"k0"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3464:(e,s,t)=>{Promise.resolve().then(t.bind(t,49455))},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15349:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(93491),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"};let n=(e,s)=>{let t=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=1.5,className:i="",children:o,...l},d)=>{let c={ref:d,...a,width:t,height:t,strokeWidth:n,color:e,className:i,...l};return(0,r.createElement)("svg",c,s?.map(([e,s])=>(0,r.createElement)(e,{key:s.id,...s}))??[],...Array.isArray(o)?o:[o])});return t.displayName=`${e}Icon`,t}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30010:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("CheckmarkCircle01Icon",[["path",{d:"M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M8 12.75C8 12.75 9.6 13.6625 10.4 15C10.4 15 12.8 9.75 16 8",stroke:"currentColor",key:"k1"}]])},31333:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var r=t(95500),a=t(56947),n=t(26052),i=t(13636),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let l={children:["",{children:["(course-view)",{children:["my-courses",{children:["[courseId]",{children:["exam",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,83873)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\results\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,6294)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e),async e=>(await Promise.resolve().then(t.bind(t,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\results\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(course-view)/my-courses/[courseId]/exam/results/page",pathname:"/my-courses/[courseId]/exam/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},31421:e=>{"use strict";e.exports=require("node:child_process")},32911:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Rotate01Icon",[["path",{d:"M20.0092 2V5.13219C20.0092 5.42605 19.6418 5.55908 19.4537 5.33333C17.6226 3.2875 14.9617 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12",stroke:"currentColor",key:"k0"}]])},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},39912:(e,s,t)=>{Promise.resolve().then(t.bind(t,83873))},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},49455:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(91754),a=t(93491),n=t(21372),i=t(56682),o=t(9260),l=t(2086),d=t(37615),c=t(68451),u=t(99803),m=t(30010),x=t(32911),p=t(74829);let h=()=>{let e=(0,n.useParams)(),s=(0,n.useRouter)(),t=(0,n.useSearchParams)(),h=e.courseId,f=t.get("type")||"final",g=t.get("examId"),y=parseInt(t.get("score")||"0"),v=parseInt(t.get("correct")||"0"),j=parseInt(t.get("total")||"0"),{courseData:b,updateCourseProgress:w}=(0,p.q)(),[N,A]=(0,a.useState)({}),[C,k]=(0,a.useState)({}),[_,q]=(0,a.useState)(new Set),I=(()=>{if("final"===f)return b.finalExam;for(let e of b.modules){if(e.moduleQuiz.id===g)return e.moduleQuiz;for(let s of e.chapters)if(s.quiz.id===g)return s.quiz}return null})();(0,a.useEffect)(()=>{let e=sessionStorage.getItem(`exam_answers_${g}`),s=sessionStorage.getItem(`exam_results_${g}`),t=sessionStorage.getItem(`exam_flags_${g}`);e&&A(JSON.parse(e)),s&&k(JSON.parse(s)),t&&q(new Set(JSON.parse(t)))},[g]);let S=()=>{sessionStorage.removeItem(`exam_answers_${g}`),sessionStorage.removeItem(`exam_results_${g}`),sessionStorage.removeItem(`exam_flags_${g}`),s.push(`/my-courses/${h}/exam?type=${f}&examId=${g}`)},P=()=>{s.push(`/my-courses/${h}`)},E=()=>{s.push("/my-courses")};if(!I)return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)(o.Zp,{className:"w-full max-w-md",children:(0,r.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,r.jsx)(l.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Hasil Ujian Tidak Ditemukan"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Hasil ujian yang diminta tidak dapat ditemukan."}),(0,r.jsxs)(i.$,{onClick:P,children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Kembali ke Kursus"]})]})})});let D=y>=I.minimumScore,G=j-v;return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50","data-sentry-component":"ExamResultsPage","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"bg-white border-b shadow-sm sticky top-0 z-10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-red-600","data-sentry-element":"TrophyIcon","data-sentry-source-file":"page.tsx"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-lg font-semibold text-gray-900",children:["Hasil ",I.title]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:b.name})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(i.$,{variant:"outline",onClick:P,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeftIcon","data-sentry-source-file":"page.tsx"}),"Kembali ke Kursus"]}),(0,r.jsxs)(i.$,{variant:"outline",onClick:E,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"HomeIcon","data-sentry-source-file":"page.tsx"}),"Dashboard"]})]})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"max-w-4xl mx-auto mb-8",children:(0,r.jsxs)(o.Zp,{className:`border-2 ${D?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)(o.aR,{className:"text-center","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("div",{className:"flex justify-center mb-4",children:D?(0,r.jsx)(m.A,{className:"h-16 w-16 text-green-600"}):(0,r.jsx)(l.A,{className:"h-16 w-16 text-red-600"})}),(0,r.jsx)(o.ZB,{className:"text-2xl","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:D?"Selamat! Anda Lulus":"Maaf, Anda Belum Lulus"})]}),(0,r.jsxs)(o.Wu,{className:"text-center space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:[y,"%"]}),(0,r.jsxs)("div",{className:"text-gray-600",children:[v," dari ",j," soal dijawab benar"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Nilai minimum untuk lulus: ",I.minimumScore,"%"]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:j}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Soal"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:v}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Benar"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:G}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Salah"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 border",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[Math.round(v/j*100),"%"]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Akurasi"})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-3 mt-6",children:[D&&"final"===f&&(0,r.jsxs)(i.$,{onClick:()=>s.push(`/my-courses/${h}?tab=certificate`),className:"bg-yellow-600 hover:bg-yellow-700 text-white",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Cek Sertifikat"]}),!D&&I.attempts<I.maxAttempts&&(0,r.jsxs)(i.$,{onClick:S,children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Retake Ujian"]})]})]})]})}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto mt-8 text-center",children:(0,r.jsx)(o.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:(0,r.jsxs)(o.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Sudah selesai mereview?"}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-3",children:[!D&&I.attempts<I.maxAttempts&&(0,r.jsxs)(i.$,{onClick:S,children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Retake Ujian"]}),(0,r.jsxs)(i.$,{variant:"iai",onClick:P,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4","data-sentry-element":"ArrowLeftIcon","data-sentry-source-file":"page.tsx"}),"Kembali ke Kursus"]}),(0,r.jsxs)(i.$,{variant:"outline",onClick:E,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"HomeIcon","data-sentry-source-file":"page.tsx"}),"Dashboard"]})]})]})})})]})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83873:(e,s,t)=>{"use strict";let r;t.r(s),t.d(s,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var a=t(63033),n=t(1472),i=t(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(course-view)\\\\my-courses\\\\[courseId]\\\\exam\\\\results\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\(course-view)\\my-courses\\[courseId]\\exam\\results\\page.tsx","default");let l={...a},d="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;r="function"==typeof o?new Proxy(o,{apply:(e,s,t)=>{let r,a,n;try{let e=d?.getStore();r=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/(course-view)/my-courses/[courseId]/exam/results",componentType:"Page",sentryTraceHeader:r,baggageHeader:a,headers:n}).apply(s,t)}}):o;let c=void 0,u=void 0,m=void 0,x=r},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},99803:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(15349).A)("Home01Icon",[["path",{d:"M15.0002 17C14.2007 17.6224 13.1505 18 12.0002 18C10.85 18 9.79977 17.6224 9.00024 17",stroke:"currentColor",key:"k0"}],["path",{d:"M2.35164 13.2135C1.99862 10.9162 1.82211 9.76763 2.25641 8.74938C2.69071 7.73112 3.65427 7.03443 5.58138 5.64106L7.02123 4.6C9.41853 2.86667 10.6172 2 12.0002 2C13.3833 2 14.582 2.86667 16.9793 4.6L18.4191 5.64106C20.3462 7.03443 21.3098 7.73112 21.7441 8.74938C22.1784 9.76763 22.0019 10.9162 21.6489 13.2135L21.3478 15.1724C20.8474 18.4289 20.5972 20.0572 19.4292 21.0286C18.2613 22 16.5539 22 13.1391 22H10.8614C7.44658 22 5.73915 22 4.57124 21.0286C3.40333 20.0572 3.15311 18.4289 2.65267 15.1724L2.35164 13.2135Z",stroke:"currentColor",key:"k1"}]])}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[5250,7688,881,7969,8134,2095,8560],()=>t(31333));module.exports=r})();
//# sourceMappingURL=page.js.map