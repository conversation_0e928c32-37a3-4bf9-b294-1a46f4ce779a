{"version": 3, "file": "../app/api/quizzes/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,4aCMO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IA0HIC,EAWAC,EArIEC,EAAeH,EAAQI,EAqIzBF,GArIiBF,CA0HjBC,CA1HgC,CAA9BE,YAA2C,CAC3CE,EAAYF,EAAaG,GAAG,CAAC,CAA7BD,KAAYF,OACZI,EAAWJ,EAAaG,GAAG,CAA3BC,MAAWJ,MACXK,EAAWL,EAAaG,GAAG,CAA3BE,MAAWL,MACXM,EAAWN,EAAaG,GAAG,CAAC,MAAjBH,MACXO,EAAYP,EAAaG,GAAG,CAAC,CAA7BI,KAAYP,OAElB,GAAI,CAACO,EACH,OADGA,EAAW,YACPC,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAG3E,IAAIC,EAAkB,EAAE,CACpBC,GAAkB,EAEtB,GAAIX,EAHAU,CAKF,IAJEC,EAEAX,MAE8BY,EAAAA,EAAAA,CAC7BC,IADGC,EACG,CAAC,CACNd,SAAAA,CAAWe,EAAAA,QAAQA,CAACC,EAAE,CACtBd,QAAAA,CAAUa,EAAAA,QAAQA,CAACb,QAAQ,CAC3BC,QAAAA,CAAUc,EAAAA,OAAOA,CAACd,QAAQ,CAC1BE,SAAAA,CAAWa,EAAAA,OAAOA,CAACb,SAAAA,GAEpBc,IAAI,CAACJ,EAAAA,QAAAA,CAAAA,CACLK,QAAQ,CAACH,EAAAA,OAAOA,CAAEI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,QAAAA,CAASb,QAAQ,CAAEe,EAAAA,OAAOA,CAACD,EAAE,GAClDI,QAAQ,CAACF,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACJ,EAAAA,OAAAA,CAAQd,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,GACjDM,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,QAAQA,CAACC,EAAE,CAAEQ,QAAAA,CAASxB,IACzBqB,CAAAA,EAAAA,EAAAA,CADyBrB,CACzBqB,CAAEA,CAACH,EAAAA,OAAAA,CAAQb,SAAS,CAAEmB,QAAAA,CAASnB,MAGlCoB,GAHkCpB,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAiC,GAAG,CAAhCS,EAAkBY,MAAM,CAC1B,OAAOpB,CADLQ,CACKR,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,qCAAqC,CAC9C,CAAEC,MAAAA,CAAQ,GAAI,GAIlBC,EAAgBiB,IAAI,CAACN,CAAAA,EAAAA,EAAAA,EAAAA,CAArBX,CAAwBkB,EAAAA,OAAAA,CAAQ5B,SAAS,CAAEwB,QAAAA,CAASxB,KACpDW,GAAkB,CADkCX,CAAAA,CAAAA,CAAAA,GAE/C,GAAIE,EADTS,CAGA,IAAMkB,CAFG3B,CAEgB,MAAMU,EAAAA,EAAAA,CAC5BC,GADGgB,GACG,CAAC,CACN3B,QAAAA,CAAUe,EAAAA,OAAOA,CAACD,EAAE,CACpBb,QAAAA,CAAUc,EAAAA,OAAOA,CAACd,QAAQ,CAC1BE,SAAAA,CAAWa,EAAAA,OAAOA,CAACb,SAAAA,GAEpBc,IAAI,CAACF,EAAAA,OAAAA,CAAAA,CACLG,QAAQ,CAACF,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQd,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,GACjDM,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQD,EAAE,CAAEQ,QAAAA,CAAStB,IACxBmB,CAAAA,EAAAA,CADwBnB,CACxBmB,CADwBnB,CACxBmB,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEmB,QAAAA,CAASnB,MAGlCoB,GAHkCpB,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAgC,GAAG,CAA/BwB,EAAiBH,MAAM,CACzB,OAAOpB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,GAIlBC,EAAgBiB,IAAI,CAACN,CAAAA,EAAAA,EAAAA,EAAAA,CAArBX,CAAwBkB,EAAAA,OAAAA,CAAQ1B,QAAQ,CAAEsB,QAAAA,CAAStB,KACnDS,GAAkB,CACpB,CAFqDT,CAAAA,IAE9C,GAAIC,EAAU,CAEnB,IAAM2B,CAFG3B,CAEM,IAAT2B,EAAelB,EAAAA,EAAAA,CAClBC,MAAM,GACNM,IAAI,CAACD,EAAAA,OAAAA,CAAAA,CACLI,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAOA,CAACF,EAAE,CAAEQ,QAAAA,CAASrB,IACxBkB,CAAAA,EAAAA,CADwBlB,CACxBkB,CADwBlB,CACxBkB,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEmB,QAAAA,CAASnB,MAGlCoB,GAHkCpB,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAsB,GAAG,CAArByB,EAAOJ,IAAPI,EAAa,CACf,OAAOxB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,GAKdL,IACFM,EAAgBiB,EADdvB,EAAU,CACSiB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQxB,QAAQ,CAAEA,IACzB,IADyBA,CAAAA,CAAAA,GAChB,CAAtBA,GACFM,EAAgBiB,IAAI,CAACN,CAAAA,EAAAA,EAAAA,EAAAA,CAArBX,CAAwBkB,EAAAA,OAAAA,CAAQzB,QAAQ,CAAEqB,QAAAA,CAASrB,MAIvDQ,EAJuDR,CAAAA,CAAAA,CAAAA,IAKlD,CAOL,IAAM4B,CARNpB,CAQkBqB,CALK,MAAMpB,EAAAA,EAAAA,CAC1BC,EAIemB,IAJT,CAAC,CAAEhB,EAAAA,CAAIE,EAAAA,OAAOA,CAACF,EAAAA,CAAG,EACxBG,IAAI,CAACD,EAAAA,OAAAA,CAAAA,CACLI,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEmB,QAAAA,CAASnB,IAAAA,CAAAA,CAEP4B,GAFO5B,CAAAA,CAEH6B,EAAKA,EAAElB,EAAE,EAC9C,GAAyB,GAAG,CAAxBe,EAAUL,MAAM,CAAhBK,OACKzB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEqB,OAAAA,CAAS,KAGtCjB,GAAkB,CACpB,CAEA,GAAI,CAACA,EACH,IAJAA,GAIOL,EAAAA,IADJK,EAAiB,MACbL,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,gBAAgB,CACzB,CAAEC,MAAAA,CAAQ,GAAI,GAOhBb,EAD6B,GAAG,CAA9Bc,EAAgBgB,MAClB9B,CAAiBc,CAAe,CAAC,EAAE,CAC1BA,CAFPA,CAEuBgB,MAAM,CAAG,EACjBH,CADoB,EACpBA,CADRb,CACQa,EAAAA,CAAAA,CAAAA,GAAOb,GAGPW,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,CAHIR,CAAAA,KAGJQ,CAAQb,SAAS,CAAEmB,QAAAA,CAASnB,IAQhDR,EAFEa,EAAgBgB,CAN8BrB,CAAAA,CAAAA,CAQhDR,EAFwB,CAAG,EAEhB,CAFmB,GAA5Ba,EAEeE,EAAAA,EAAAA,CACdC,MAAM,CAAC,CACNG,EAAAA,CAAIY,EAAAA,OAAOA,CAACZ,EAAE,CACdmB,IAAAA,CAAMP,EAAAA,OAAOA,CAACO,IAAI,CAClBC,WAAAA,CAAaR,EAAAA,OAAOA,CAACQ,WAAW,CAChChC,QAAAA,CAAUwB,EAAAA,OAAOA,CAACxB,QAAQ,CAC1BiC,SAAAA,CAAWT,EAAAA,OAAOA,CAACS,SAAS,CAC5BC,YAAAA,CAAcV,EAAAA,OAAOA,CAACU,YAAY,CAClCC,QAAAA,CAAUX,EAAAA,OAAOA,CAACW,QAAQ,CAC1BvC,SAAAA,CAAW4B,EAAAA,OAAOA,CAAC5B,SAAS,CAC5BE,QAAAA,CAAU0B,EAAAA,OAAOA,CAAC1B,QAAQ,CAC1BC,QAAAA,CAAUyB,EAAAA,OAAOA,CAACzB,QAAQ,CAC1BqC,SAAAA,CAAWZ,EAAAA,OAAOA,CAACY,SAAS,CAC5BC,SAAAA,CAAWb,EAAAA,OAAOA,CAACa,SAAS,CAC5BC,WAAAA,CAAa3B,EAAAA,QAAQA,CAACoB,IAAI,CAC1BQ,UAAAA,CAAY1B,EAAAA,OAAOA,CAACkB,IAAI,CACxBS,UAAAA,CAAY1B,EAAAA,OAAOA,CAACiB,IAAAA,GAErBhB,IAAI,CAACS,EAAAA,OAAAA,CAAAA,CACLR,QAAQ,CAACL,EAAAA,QAAAA,CAAUM,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQ5B,SAAS,CAAEe,EAAAA,QAAAA,CAASC,EAAE,GACpDI,QAAQ,CAACH,EAAAA,OAAAA,CACR4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,QAAAA,CAASb,QAAQ,CAAEe,EAAAA,OAAOA,CAACD,EAAE,EAChCK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQ1B,QAAQ,CAAEe,EAAAA,OAAAA,CAAQD,EAAE,IAGlCI,QAAQ,CAACF,EAAAA,OAAAA,CACR2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQd,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,EAC/BK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,OAAAA,CAAQzB,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,IAGlCM,KAAK,CAAC1B,GAGE,MAAMgB,EAAAA,EAAAA,CACdC,CAJMjB,KAIA,CAAC,CACNoB,EAAAA,CAAIY,EAAAA,OAAOA,CAACZ,EAAE,CACdmB,IAAAA,CAAMP,EAAAA,OAAOA,CAACO,IAAI,CAClBC,WAAAA,CAAaR,EAAAA,OAAOA,CAACQ,WAAW,CAChChC,QAAAA,CAAUwB,EAAAA,OAAOA,CAACxB,QAAQ,CAC1BiC,SAAAA,CAAWT,EAAAA,OAAOA,CAACS,SAAS,CAC5BC,YAAAA,CAAcV,EAAAA,OAAOA,CAACU,YAAY,CAClCC,QAAAA,CAAUX,EAAAA,OAAOA,CAACW,QAAQ,CAC1BvC,SAAAA,CAAW4B,EAAAA,OAAOA,CAAC5B,SAAS,CAC5BE,QAAAA,CAAU0B,EAAAA,OAAOA,CAAC1B,QAAQ,CAC1BC,QAAAA,CAAUyB,EAAAA,OAAOA,CAACzB,QAAQ,CAC1BqC,SAAAA,CAAWZ,EAAAA,OAAOA,CAACY,SAAS,CAC5BC,SAAAA,CAAWb,EAAAA,OAAOA,CAACa,SAAS,CAC5BC,WAAAA,CAAa3B,EAAAA,QAAQA,CAACoB,IAAI,CAC1BQ,UAAAA,CAAY1B,EAAAA,OAAOA,CAACkB,IAAI,CACxBS,UAAAA,CAAY1B,EAAAA,OAAOA,CAACiB,IAAAA,GAErBhB,IAAI,CAACS,EAAAA,OAAAA,CAAAA,CACLR,QAAQ,CAACL,EAAAA,QAAAA,CAAUM,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQ5B,SAAS,CAAEe,EAAAA,QAAAA,CAASC,EAAE,GACpDI,QAAQ,CAACH,EAAAA,OAAAA,CACR4B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,QAAAA,CAASb,QAAQ,CAAEe,EAAAA,OAAOA,CAACD,EAAE,EAChCK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQ1B,QAAQ,CAAEe,EAAAA,OAAAA,CAAQD,EAAE,IAGlCI,QAAQ,CAACF,EAAAA,OAAAA,CACR2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQd,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,EAC/BK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,EAAAA,OAAAA,CAAQzB,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,IAGlCM,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEmB,QAAAA,CAASnB,KAI1C,IAJ0CA,CAAAA,CAAAA,CAAAA,KAIHyC,OAAAA,CAAQC,GAAG,CAChDlD,EAASoC,EADLe,CACQ,CAAC,EAAbnD,IAAoBoD,IAAAA,GAAAA,CACZC,EAAgB,MAAMtC,EAAAA,EAAAA,CAAtBsC,MACG,CAAC,CAAEC,KAAAA,CAAOC,EAAAA,SAASA,CAACpC,EAAAA,CAAG,EAC7BG,IAAI,CAACiC,EAAAA,SAAAA,CAAAA,CACL9B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC+B,EAAAA,SAAAA,CAAUC,MAAM,CAAEJ,EAAKjC,EAALiC,GAE9B,MAAO,CACL,GAAGA,CAAI,CACPC,aAAAA,CAAeA,EAAcxB,MAC/B,CACF,IAGF,OAAOpB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEqB,OAAAA,CAASoB,CAAyB,EAC/D,CAAE,MAAOxC,EAAO,CAEd,EAFOA,KACP8C,OAAAA,CAAQ9C,KAAK,CAAC,0BAA2BA,GAClCF,EADkCE,CAAAA,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAe8C,EAAK5D,CAAoB,EAC7C,GAAI,CAEF,IAkDI6D,EAlDE,MACJrB,CAAI,OAiDFqB,MAhDFpB,CAAW,UACXhC,EAAW,MAAXA,GAAoB,WACpBiC,CAAS,cACTC,EAAe,EAAE,QAAjBA,EACAC,GAAW,CAAI,IAAfA,OACAvC,CAAS,CACTE,UAAQ,CACRC,UAAQ,CACRE,WAAS,CACT+C,SAAAA,CAAWK,EAAgB,EAAE,CAC9B,CAbY,EAaTC,IAAAA,CADSD,CAZclD,IAAI,CAAZZ,EAgBnB,GAAI,CAACwC,GAAQ,CAAC9B,EACZ,OADYA,EACLC,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,mCAAmC,CAC5C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,GAAI,CAAC,CAAC,UAAW,SAAU,QAAQ,CAACkD,QAAQ,CAACvD,GAC3C,KAD2CA,CAAAA,CACpCE,CAD+C,CAC/CA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,uDAAuD,CAChE,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,GAAiB,YAAbL,IAA2BF,GAAYC,CAAvCC,CAA8C,CAChD,EAD6BF,KACtBI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oEAAoE,CAC7E,CAAEC,MAAAA,CAAQ,GAAI,GAGlB,GAAiB,WAAbL,IAA0BJ,GAAaG,CAAvCC,CAA8C,CAChD,EADoD,CAAxBJ,IACrBM,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,mEAAmE,CAC5E,CAAEC,MAAAA,CAAQ,GAAI,GAGlB,GAAiB,UAAbL,IAAyBJ,GAAaE,CAAtCE,CAA6C,CAC/C,EADmD,CAAxBJ,IACpBM,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,kEAAkE,CAC3E,CAAEC,MAAAA,CAAQ,GAAI,GAOlB,GAAIL,cAAwB,CAC1B,GAAI,CAACJ,EACH,OADGA,EAAW,YACPM,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,0CAA0C,CACnD,CAAEC,MAAAA,CAAQ,GAAI,GAuBlB,GAlBA+C,EAAmB,MAAM5C,EAAAA,EAAAA,CACtBC,GADH2C,GACS,CAAC,CACNxD,SAAAA,CAAWe,EAAAA,QAAQA,CAACC,EAAE,CACtBd,QAAAA,CAAUa,EAAAA,QAAQA,CAACb,QAAQ,CAC3BC,QAAAA,CAAUc,EAAAA,OAAOA,CAACd,QAAQ,CAC1BE,SAAAA,CAAWa,EAAAA,OAAOA,CAACb,SAAAA,GAEpBc,IAAI,CAACJ,EAAAA,QAAAA,CAAAA,CACLK,QAAQ,CAACH,EAAAA,OAAOA,CAAEI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,QAAAA,CAASb,QAAQ,CAAEe,EAAAA,OAAOA,CAACD,EAAE,GAClDI,QAAQ,CAACF,EAAAA,OAAOA,CAAEG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQd,QAAQ,CAAEe,EAAAA,OAAOA,CAACF,EAAE,GACjDM,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,QAAAA,CAASC,EAAE,CAAEhB,GAChBqB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEA,KAGzBoB,IAHyBpB,CAAAA,CAAAA,CAAAA,EAKI,GAAG,CAA/BmD,EAAiB9B,MAAM,CACzB,OADE8B,EACKlD,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,qCAAqC,CAC9C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,MAAO,GAAiB,WAAbL,EAAuB,CAChC,GAAI,CAACF,EACH,MADGA,CACII,CADM,CACNA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wCAAwC,CACjD,CAAEC,MAAAA,CAAQ,GAAI,GAqBlB,GAhBA+C,EAAmB,MAAM5C,EAAAA,EAAAA,CACtBC,GADH2C,GACS,CAAC,CACNtD,QAAAA,CAAUe,EAAAA,OAAOA,CAACD,EAAE,CACpBb,QAAAA,CAAUc,EAAAA,OAAOA,CAACd,QAAQ,CAC1BE,SAAAA,CAAWa,EAAAA,OAAOA,CAACb,SAAAA,CACrB,EACCc,IAAI,CAACF,EAAAA,OAAAA,CAAAA,CACLG,QAAQ,CAACF,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQd,QAAQ,CAAEe,EAAAA,OAAAA,CAAQF,EAAE,GACjDM,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGJ,EAAAA,OAAAA,CAAQD,EAAE,CAAEd,GACfmB,CAAAA,EAAAA,EAAAA,CADenB,CACfmB,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEA,KAGzBoB,IAHyBpB,CAAAA,CAAAA,CAAAA,EAKI,GAAG,CAA/BmD,EAAiB9B,MAAM,CACzB,OAAOpB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,KAAiC,CAA1B,GAAiB,UAAbL,EA8BT,OAAOE,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,uDAAuD,CAChE,CAAEC,MAAAA,CAAQ,GAAI,GA/BhB,GAAI,CAACN,EACH,MADGA,CACIG,CADM,CACNA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,uCAAuC,CAChD,CAAEC,MAAAA,CAAQ,GAAI,GAmBlB,GAdA+C,EAAmB,MAAM5C,EAAAA,EAAAA,CACtBC,GADH2C,GACS,CAAC,CACNrD,QAAAA,CAAUe,EAAAA,OAAOA,CAACF,EAAE,CACpBX,SAAAA,CAAWa,EAAAA,OAAOA,CAACb,SAAAA,GAEpBc,IAAI,CAACD,EAAAA,OAAAA,CAAAA,CACLI,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQF,EAAE,CAAEb,GACfkB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAAA,CAAQb,SAAS,CAAEA,KAGzBoB,IAHyBpB,CAAAA,CAAAA,CAAAA,EAKI,GAAG,CAA/BmD,EAAiB9B,MAAM,CACzB,OAAOpB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAQA,GAAiB,GARV,MAQmB,CAAtBL,EAaF,IAAIwD,CAXsB,MAAMhD,EAAAA,EAAAA,CAC7BC,KAUC+C,CAVK,CAAC,CAAE5C,EAAAA,CAAIY,EAAAA,OAAOA,CAACZ,EAAAA,GACrBG,IAAI,CAACS,EAAAA,OAAAA,CAAAA,CACLN,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQzB,QAAQ,CAAEA,GACrBkB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQxB,QAAQ,CAAE,WAGxBqB,KAAK,CAAC,IAEaC,MAAM,CAAG,EAC7B,CADgC,MACzBpB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,8CAA8C,CACvD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,MACK,GAAiB,UAAU,CAAvBL,GAaLyD,CAXuB,MAAMjD,EAAAA,EAAAA,CAC9BC,MAAM,CAAC,CAAEG,EAAAA,CAAIY,EAAAA,OAAOA,CAACZ,EAAAA,GACrBG,IAAI,CAACS,EAAAA,OAAAA,CAAAA,CACLN,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAGA,CACDF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQ1B,QAAQ,CAAEA,GACrBmB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,OAAAA,CAAQxB,QAAQ,CAAE,YAGxBqB,KAAK,CAAC,IAEcC,MAAM,CAAG,EAC9B,CADiC,MAC1BpB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,+CAA+C,CACxD,CAAEC,MAAAA,CAAQ,GAAI,GAMpB,IAAMqD,EAAgB,MACpB3B,IAAAA,UACAC,WACAhC,QAAAA,IACAiC,EACAC,OADAD,KACAC,CAAcA,EAAayB,QAAQ,YACnCxB,EACAvC,MADAuC,GACAvC,CAAwB,YAAbI,EAAyBJ,EAAY,IAArCI,CACXF,EADoCF,MACpCE,CAAuB,QAAbE,KAAwBF,EAAW,KAC7CC,CADkCD,OAClCC,CAAuB,UAAbC,EAAuBD,EAAW,IAAlCC,EAAuBD,EAInB,KAAV6D,CAAgBpD,EAAAA,EAAAA,CACnBqD,MAAM,CAACrC,EAAAA,OAAAA,CAAAA,CACPsC,MAAM,CAACJ,GACPK,KADOL,CAAAA,GACE,GAENT,EAASW,CAAO,CAAC,EAAjBX,CAAoBrC,EAAE,CAG5B,GAAIyC,EAAc/B,MAAM,CAAG,EAAG,CAC5B,CADE+B,GACIW,EAAoBX,EAAcxB,GAAG,CAAC,CAACoC,EAAeC,IAAlCb,CAAqD,CAAlCY,OAC3ChB,EACAkB,IADAlB,CACMgB,EAASE,IAAI,EAAI,kBACvBF,QAAAA,CAAUG,IAAAA,CAAKC,SAAS,CAACJ,EAASA,MAATA,EAAiB,EAC1CK,OAAAA,CAASL,EAASK,MAATL,CAAgB,CAAGG,IAAAA,CAAKC,SAAS,CAACJ,EAASK,MAATL,CAAgB,EAAI,KAC/DM,WAAAA,CAAsC,KAAzBN,EAASM,WAAW,CAAU,KAAON,EAASM,WAAW,CACtEC,WAAAA,CAAsC,KAAzBP,EAASO,WAAW,CAAU,KAAOP,EAASO,WAAW,CACtEC,MAAAA,CAAQR,EAASQ,MAATR,EAAmB,EAC3BS,UAAAA,CAAYT,EAASS,MAATT,IAAmB,EAAIC,EAAQ,GAARA,CAGrC,OAAM1D,EAAAA,EAAAA,CAAGqD,MAAM,CAACb,EAAAA,SAAAA,CAAAA,CAAWc,MAAM,CAACE,EACpC,CAEA,OAAO9D,EAAAA,KAH6B8D,CAAAA,MAG7B9D,CAAaC,IAAI,CACtB,CAAE0C,IAAAA,CAAMe,CAAO,CAAC,EAAE,CAAEe,OAAAA,CAAS,4BAA4B,CACzD,CAAEtE,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOD,EAAO,CAEd,EAFOA,KACP8C,OAAAA,CAAQ9C,KAAK,CAAC,uBAAwBA,GAC/BF,EAD+BE,CAAAA,WAC/BF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC7dA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,cAAc,CAClC,OAAO,EACf,CAAO,CAAC,CAAC,KAAK,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CAMxB,IAAC,EAAM,CAAH,CAAeuE,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAAf,KAA8C,EAAhC,MAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,4IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/quizzes/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?a227", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { quizzes, questions, chapters, modules, courses, users } from '@/lib/db/schema';\r\nimport { eq, and, or } from 'drizzle-orm';\r\n\r\n// GET /api/quizzes - Get quizzes for a chapter or course\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const chapterId = searchParams.get('chapterId');\r\n    const moduleId = searchParams.get('moduleId');\r\n    const courseId = searchParams.get('courseId');\r\n    const quizType = searchParams.get('quizType');\r\n    const teacherId = searchParams.get('teacherId');\r\n    \r\n    if (!teacherId) {\r\n      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });\r\n    }\r\n\r\n    let whereConditions = []; // Array to hold multiple conditions\r\n    let accessValidated = false;\r\n    \r\n    if (chapterId) {\r\n      // Get quizzes for a specific chapter\r\n      const chapterWithCourse = await db\r\n        .select({\r\n          chapterId: chapters.id,\r\n          moduleId: chapters.moduleId,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(chapters)\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(chapters.id, parseInt(chapterId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (chapterWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Chapter not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n\r\n      whereConditions.push(eq(quizzes.chapterId, parseInt(chapterId)));\r\n      accessValidated = true;\r\n    } else if (moduleId) {\r\n      // Get quizzes for a specific module\r\n      const moduleWithCourse = await db\r\n        .select({\r\n          moduleId: modules.id,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(modules)\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(modules.id, parseInt(moduleId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (moduleWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Module not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n\r\n      whereConditions.push(eq(quizzes.moduleId, parseInt(moduleId)));\r\n      accessValidated = true;\r\n    } else if (courseId) {\r\n      // Get quizzes for a specific course\r\n      const course = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.id, parseInt(courseId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (course.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Course not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n\r\n      // If quizType is specified, filter by type\r\n      if (quizType) {\r\n        whereConditions.push(eq(quizzes.quizType, quizType));\r\n        if (quizType === 'final') {\r\n          whereConditions.push(eq(quizzes.courseId, parseInt(courseId)));\r\n        }\r\n      }\r\n      \r\n      accessValidated = true;\r\n    } else {\r\n      // Get all quizzes for teacher's courses\r\n      const teacherCourses = await db\r\n        .select({ id: courses.id })\r\n        .from(courses)\r\n        .where(eq(courses.teacherId, parseInt(teacherId)));\r\n      \r\n      const courseIds = teacherCourses.map(c => c.id);\r\n      if (courseIds.length === 0) {\r\n        return NextResponse.json({ quizzes: [] });\r\n      }\r\n\r\n      accessValidated = true;\r\n    }\r\n\r\n    if (!accessValidated) {\r\n      return NextResponse.json(\r\n        { error: 'Access denied' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Build the where condition\r\n    let whereCondition;\r\n    if (whereConditions.length === 1) {\r\n      whereCondition = whereConditions[0];\r\n    } else if (whereConditions.length > 1) {\r\n      whereCondition = and(...whereConditions);\r\n    } else {\r\n      // For getting all teacher's quizzes, we'll filter in the main query\r\n      whereCondition = eq(courses.teacherId, parseInt(teacherId));\r\n    }\r\n\r\n    // Get quizzes with related information based on quiz type\r\n    let quizList;\r\n    \r\n    if (whereConditions.length > 0) {\r\n      // Specific query based on conditions\r\n      quizList = await db\r\n        .select({\r\n          id: quizzes.id,\r\n          name: quizzes.name,\r\n          description: quizzes.description,\r\n          quizType: quizzes.quizType,\r\n          timeLimit: quizzes.timeLimit,\r\n          minimumScore: quizzes.minimumScore,\r\n          isActive: quizzes.isActive,\r\n          chapterId: quizzes.chapterId,\r\n          moduleId: quizzes.moduleId,\r\n          courseId: quizzes.courseId,\r\n          createdAt: quizzes.createdAt,\r\n          updatedAt: quizzes.updatedAt,\r\n          chapterName: chapters.name,\r\n          moduleName: modules.name,\r\n          courseName: courses.name\r\n        })\r\n        .from(quizzes)\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, \r\n          or(\r\n            eq(chapters.moduleId, modules.id), // For chapter quizzes\r\n            eq(quizzes.moduleId, modules.id)    // For module quizzes\r\n          )\r\n        )\r\n        .leftJoin(courses, \r\n          or(\r\n            eq(modules.courseId, courses.id),  // For chapter and module quizzes\r\n            eq(quizzes.courseId, courses.id)    // For final exams\r\n          )\r\n        )\r\n        .where(whereCondition);\r\n    } else {\r\n      // Get all quizzes for teacher's courses\r\n      quizList = await db\r\n        .select({\r\n          id: quizzes.id,\r\n          name: quizzes.name,\r\n          description: quizzes.description,\r\n          quizType: quizzes.quizType,\r\n          timeLimit: quizzes.timeLimit,\r\n          minimumScore: quizzes.minimumScore,\r\n          isActive: quizzes.isActive,\r\n          chapterId: quizzes.chapterId,\r\n          moduleId: quizzes.moduleId,\r\n          courseId: quizzes.courseId,\r\n          createdAt: quizzes.createdAt,\r\n          updatedAt: quizzes.updatedAt,\r\n          chapterName: chapters.name,\r\n          moduleName: modules.name,\r\n          courseName: courses.name\r\n        })\r\n        .from(quizzes)\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, \r\n          or(\r\n            eq(chapters.moduleId, modules.id), // For chapter quizzes\r\n            eq(quizzes.moduleId, modules.id)    // For module quizzes\r\n          )\r\n        )\r\n        .leftJoin(courses, \r\n          or(\r\n            eq(modules.courseId, courses.id),  // For chapter and module quizzes\r\n            eq(quizzes.courseId, courses.id)    // For final exams\r\n          )\r\n        )\r\n        .where(eq(courses.teacherId, parseInt(teacherId)));\r\n    }\r\n\r\n    // Get question count for each quiz\r\n    const quizzesWithQuestionCount = await Promise.all(\r\n      quizList.map(async (quiz) => {\r\n        const questionCount = await db\r\n          .select({ count: questions.id })\r\n          .from(questions)\r\n          .where(eq(questions.quizId, quiz.id));\r\n\r\n        return {\r\n          ...quiz,\r\n          questionCount: questionCount.length\r\n        };\r\n      })\r\n    );\r\n\r\n    return NextResponse.json({ quizzes: quizzesWithQuestionCount });\r\n  } catch (error) {\r\n    console.error('Error fetching quizzes:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/quizzes - Create a new quiz\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      quizType = 'chapter',\r\n      timeLimit,\r\n      minimumScore = 70,\r\n      isActive = true,\r\n      chapterId,\r\n      moduleId,\r\n      courseId,\r\n      teacherId,\r\n      questions: quizQuestions = []\r\n    } = body;\r\n\r\n    // Validate required fields based on quiz type\r\n    if (!name || !teacherId) {\r\n      return NextResponse.json(\r\n        { error: 'Name and teacher ID are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Validate quiz type\r\n    if (!['chapter', 'module', 'final'].includes(quizType)) {\r\n      return NextResponse.json(\r\n        { error: 'Invalid quiz type. Must be chapter, module, or final' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Validate that only the correct ID is provided for each quiz type\r\n    if (quizType === 'chapter' && (moduleId || courseId)) {\r\n      return NextResponse.json(\r\n        { error: 'Chapter quiz should only have chapterId, not moduleId or courseId' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n    if (quizType === 'module' && (chapterId || courseId)) {\r\n      return NextResponse.json(\r\n        { error: 'Module quiz should only have moduleId, not chapterId or courseId' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n    if (quizType === 'final' && (chapterId || moduleId)) {\r\n      return NextResponse.json(\r\n        { error: 'Final exam should only have courseId, not chapterId or moduleId' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    let validationResult;\r\n    \r\n    // Validate based on quiz type\r\n    if (quizType === 'chapter') {\r\n      if (!chapterId) {\r\n        return NextResponse.json(\r\n          { error: 'Chapter ID is required for chapter quiz' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n      \r\n      // Verify chapter exists and teacher has access\r\n      validationResult = await db\r\n        .select({\r\n          chapterId: chapters.id,\r\n          moduleId: chapters.moduleId,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(chapters)\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(chapters.id, chapterId),\r\n            eq(courses.teacherId, teacherId)\r\n          )\r\n        )\r\n        .limit(1);\r\n        \r\n      if (validationResult.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Chapter not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    } else if (quizType === 'module') {\r\n      if (!moduleId) {\r\n        return NextResponse.json(\r\n          { error: 'Module ID is required for module quiz' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n      \r\n      // Verify module exists and teacher has access\r\n      validationResult = await db\r\n        .select({\r\n          moduleId: modules.id,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(modules)\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(modules.id, moduleId),\r\n            eq(courses.teacherId, teacherId)\r\n          )\r\n        )\r\n        .limit(1);\r\n        \r\n      if (validationResult.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Module not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    } else if (quizType === 'final') {\r\n      if (!courseId) {\r\n        return NextResponse.json(\r\n          { error: 'Course ID is required for final exam' },\r\n          { status: 400 }\r\n        );\r\n      }\r\n      \r\n      // Verify course exists and teacher has access\r\n      validationResult = await db\r\n        .select({\r\n          courseId: courses.id,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.id, courseId),\r\n            eq(courses.teacherId, teacherId)\r\n          )\r\n        )\r\n        .limit(1);\r\n        \r\n      if (validationResult.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Course not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    } else {\r\n      return NextResponse.json(\r\n        { error: 'Invalid quiz type. Must be chapter, module, or final' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Additional validation: Check for duplicate quizzes\r\n    if (quizType === 'final') {\r\n      // Only one final exam per course\r\n      const existingFinalExam = await db\r\n        .select({ id: quizzes.id })\r\n        .from(quizzes)\r\n        .where(\r\n          and(\r\n            eq(quizzes.courseId, courseId),\r\n            eq(quizzes.quizType, 'final')\r\n          )\r\n        )\r\n        .limit(1);\r\n        \r\n      if (existingFinalExam.length > 0) {\r\n        return NextResponse.json(\r\n          { error: 'A final exam already exists for this course' },\r\n          { status: 409 }\r\n        );\r\n      }\r\n    } else if (quizType === 'module') {\r\n      // Only one module quiz per module\r\n      const existingModuleQuiz = await db\r\n        .select({ id: quizzes.id })\r\n        .from(quizzes)\r\n        .where(\r\n          and(\r\n            eq(quizzes.moduleId, moduleId),\r\n            eq(quizzes.quizType, 'module')\r\n          )\r\n        )\r\n        .limit(1);\r\n        \r\n      if (existingModuleQuiz.length > 0) {\r\n        return NextResponse.json(\r\n          { error: 'A module quiz already exists for this module' },\r\n          { status: 409 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Prepare quiz data based on type\r\n    const quizData: any = {\r\n      name,\r\n      description,\r\n      quizType,\r\n      timeLimit,\r\n      minimumScore: minimumScore.toString(),\r\n      isActive,\r\n      chapterId: quizType === 'chapter' ? chapterId : null,\r\n      moduleId: quizType === 'module' ? moduleId : null,\r\n      courseId: quizType === 'final' ? courseId : null\r\n    };\r\n\r\n    // Create the quiz\r\n    const newQuiz = await db\r\n      .insert(quizzes)\r\n      .values(quizData)\r\n      .returning();\r\n\r\n    const quizId = newQuiz[0].id;\r\n\r\n    // Create questions if provided\r\n    if (quizQuestions.length > 0) {\r\n      const questionsToInsert = quizQuestions.map((question: any, index: number) => ({\r\n        quizId,\r\n        type: question.type || 'multiple_choice',\r\n        question: JSON.stringify(question.question),\r\n        options: question.options ? JSON.stringify(question.options) : null,\r\n        essayAnswer: question.essayAnswer === '' ? null : question.essayAnswer,\r\n        explanation: question.explanation === '' ? null : question.explanation,\r\n        points: question.points || 1,\r\n        orderIndex: question.orderIndex || index + 1\r\n      }));\r\n\r\n      await db.insert(questions).values(questionsToInsert);\r\n    }\r\n\r\n    return NextResponse.json(\r\n      { quiz: newQuiz[0], message: 'Quiz created successfully' },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating quiz:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/quizzes',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\quizzes\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/quizzes/route\",\n        pathname: \"/api/quizzes\",\n        filename: \"route\",\n        bundlePath: \"app/api/quizzes/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON><PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\quizzes\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "whereCondition", "quizList", "searchParams", "nextUrl", "chapterId", "get", "moduleId", "courseId", "quizType", "teacherId", "NextResponse", "json", "error", "status", "whereConditions", "accessValidated", "db", "select", "chapterWithCourse", "chapters", "id", "modules", "courses", "from", "leftJoin", "eq", "where", "and", "parseInt", "limit", "length", "push", "quizzes", "moduleWithCourse", "course", "courseIds", "teacherCourses", "map", "c", "name", "description", "timeLimit", "minimumScore", "isActive", "createdAt", "updatedAt", "chapterName", "moduleName", "courseName", "or", "Promise", "all", "quizzesWithQuestionCount", "quiz", "questionCount", "count", "questions", "quizId", "console", "POST", "validationResult", "quizQuestions", "body", "includes", "existingFinalExam", "existingModuleQuiz", "quizData", "toString", "newQuiz", "insert", "values", "returning", "questionsToInsert", "question", "index", "type", "JSON", "stringify", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "points", "orderIndex", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}