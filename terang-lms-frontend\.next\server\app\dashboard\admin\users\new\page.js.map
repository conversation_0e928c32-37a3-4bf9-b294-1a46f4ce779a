{"version": 3, "file": "../app/dashboard/admin/users/new/page.js", "mappings": "ubAAA,oICmBM,MAAY,cAAiB,aAhBC,CAgBY,CAAU,MAf/C,EAAE,EAAG,CAAkB,oBAAK,SAAU,EAC/C,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC3C,kBCNA,uCAAmK,wBCAnK,4CCAA,uCAA6K,oICE7K,SAASA,EAAK,WACZC,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,OAAOH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBP,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,CACjBR,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,aAAaH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,mBAAmBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBV,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,CAClBX,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,8FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,wBCNjN,oECAA,0GCAA,+DCmBI,sBAAsB,ktBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAAE,OAAhC,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,4BAA4B,CAC5C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,CA/BoBM,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,6EC3E9B,SAASC,EAAM,WACbb,CAAS,CACT,GAAGC,EAC8C,EACjD,MAAO,UAACa,EAAAA,CAAmB,EAACX,YAAU,QAAQH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sNAAuNJ,GAAa,GAAGC,CAAK,CAAEc,sBAAoB,sBAAsBV,wBAAsB,QAAQC,0BAAwB,aAC5Y,0BCVA,yCCAA,uCAA6K,yBCA7K,4DJmBI,sBAAsB,0rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,kBAAkB,CAClC,aAAa,CAAE,QAAQ,mBACvB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BEM,EAoCnB,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IKhF9B,gDCAA,wGCAA,gECAA,kDCAA,iECAA,gQCgBe,SAASI,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,OACJC,CAAK,CACN,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACN,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACC,EAAcC,EAAgB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACG,EAAcC,EAAgB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EAC5D,CAACK,EAAUC,EAAY,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACvCO,KAAM,GACNC,MAAO,GACPC,SAAU,GACVC,KAAM,UACNC,cAAe,MACjB,GAiBMC,EAAe,MAAOC,IAC1BA,EAAEC,cAAc,GAChBf,GAAa,GACb,GAAI,CACF,IAAMgB,EAAa,CACjB,GAAGV,CAAQ,CACXM,cAAeN,EAASM,aAAa,EAAIN,WAASM,aAAa,CAAcK,SAASX,EAASM,aAAa,EAAI,IAClH,EACMM,EAAW,MAAMC,MAAM,aAAc,CACzCC,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMC,KAAKC,SAAS,CAACR,EACvB,GACMS,EAAO,MAAMP,EAASQ,IAAI,GAC5BD,EAAKE,OAAO,EAAE,EACV,CACJC,MAAO,UACPC,YAAa,2BACf,GACAlC,EAAOmC,IAAI,CAAC,2BAEZjC,EAAM,CACJ+B,MAAO,QACPC,YAAaJ,EAAKM,KAAK,EAAI,wBAC3BC,QAAS,aACX,EAEJ,CAAE,MAAOD,EAAO,CACdE,QAAQF,KAAK,CAAC,uBAAwBA,GACtClC,EAAM,CACJ+B,MAAO,QACPC,YAAa,wBACbG,QAAS,aACX,EACF,QAAU,CACRhC,GAAa,EACf,CACF,EACMkC,EAAoB,CAACC,EAAeC,KACxC7B,EAAY8B,GAAS,EACnB,EADmB,CAChBA,CAAI,CACP,CAACF,EAAM,CAAEC,EACX,EACF,EASA,MAAO,WAACxD,MAAAA,CAAIF,UAAU,YAAYK,wBAAsB,cAAcC,0BAAwB,qBAC1F,WAACJ,MAAAA,CAAIF,UAAU,wCACb,UAAC4D,IAAIA,CAACC,KAAK,yBAAyB9C,cAA/B6C,QAAmD,OAAOtD,0BAAwB,oBACrF,WAACwD,EAAAA,CAAMA,CAAAA,CAACR,QAAQ,UAAUS,KAAK,KAAKhD,sBAAoB,SAAST,0BAAwB,qBACvF,UAAC0D,EAAAA,CAASA,CAAAA,CAAChE,UAAU,eAAee,sBAAoB,YAAYT,0BAAwB,aAAa,YAI7G,WAACJ,MAAAA,WACC,UAAC+D,KAAAA,CAAGjE,UAAU,6CAAoC,iBAGlD,UAACkE,IAAAA,CAAElE,UAAU,iCAAwB,oDAMzC,WAACD,EAAAA,EAAIA,CAAAA,CAACgB,sBAAoB,OAAOT,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACQ,sBAAoB,aAAaT,0BAAwB,qBACnE,UAACE,EAAAA,EAASA,CAAAA,CAACO,sBAAoB,YAAYT,0BAAwB,oBAAW,iBAC9E,UAACG,EAAAA,EAAeA,CAAAA,CAACM,sBAAoB,kBAAkBT,0BAAwB,oBAAW,oDAI5F,UAACI,EAAAA,EAAWA,CAAAA,CAACK,sBAAoB,cAAcT,0BAAwB,oBACrE,WAAC6D,OAAAA,CAAKC,SAAUjC,EAAcnC,UAAU,sBACtC,WAACE,MAAAA,CAAIF,UAAU,kDACb,WAACE,MAAAA,CAAIF,UAAU,sBACb,UAACa,EAAAA,CAAKA,CAAAA,CAACwD,QAAQ,OAAOtD,sBAAoB,QAAQT,0BAAwB,oBAAW,cACrF,UAACgE,EAAAA,CAAKA,CAAAA,CAACC,GAAG,OAAOb,MAAO9B,EAASE,IAAI,CAAE0C,SAAUpC,GAAKoB,EAAkB,OAAQpB,EAAEqC,MAAM,CAACf,KAAK,EAAGgB,YAAY,kBAAkBC,QAAQ,IAAC5D,sBAAoB,QAAQT,0BAAwB,gBAG9L,WAACJ,MAAAA,CAAIF,UAAU,sBACb,UAACa,EAAAA,CAAKA,CAAAA,CAACwD,QAAQ,QAAQtD,sBAAoB,QAAQT,0BAAwB,oBAAW,kBACtF,UAACgE,EAAAA,CAAKA,CAAAA,CAACC,GAAG,QAAQK,KAAK,QAAQlB,MAAO9B,EAASG,KAAK,CAAEyC,SAAUpC,GAAKoB,EAAkB,QAASpB,EAAEqC,MAAM,CAACf,KAAK,EAAGgB,YAAY,sBAAsBC,QAAQ,IAAC5D,sBAAoB,QAAQT,0BAAwB,gBAGlN,WAACJ,MAAAA,CAAIF,UAAU,sBACb,UAACa,EAAAA,CAAKA,CAAAA,CAACwD,QAAQ,WAAWtD,sBAAoB,QAAQT,0BAAwB,oBAAW,aACzF,WAACJ,MAAAA,CAAIF,UAAU,qBACb,UAACsE,EAAAA,CAAKA,CAAAA,CAACC,GAAG,WAAWK,KAAMpD,EAAe,OAAS,WAAYkC,MAAO9B,EAASI,QAAQ,CAAEwC,SAAUpC,GAAKoB,EAAkB,WAAYpB,EAAEqC,MAAM,CAACf,KAAK,EAAGgB,YAAY,iBAAiBC,QAAQ,IAAC3E,UAAU,QAAQe,sBAAoB,QAAQT,0BAAwB,aACnQ,UAACJ,MAAAA,CAAIF,UAAU,uEACb,UAAC8D,EAAAA,CAAMA,CAAAA,CAACc,KAAK,SAAStB,QAAQ,QAAQS,KAAK,KAAKc,QAAS,IAAMpD,EAAgB,CAACD,GAAexB,UAAU,cAAce,sBAAoB,SAAST,0BAAwB,oBACzKkB,EAAe,UAACsD,EAAAA,CAAMA,CAAAA,CAAC9E,UAAU,YAAe,UAAC+E,EAAAA,CAAGA,CAAAA,CAAC/E,UAAU,mBAItE,UAAC8D,EAAAA,CAAMA,CAAAA,CAACc,KAAK,SAAStB,QAAQ,UAAUS,KAAK,KAAKc,QAxDvC,CAwDgDG,IAvDvE,IAAMC,EAAQ,yEACVjD,EAAW,GACf,IAAK,IAAIkD,EAAI,EAAGA,EAAI,GAAIA,IAAK,GACfD,EAAME,MAAM,CAACC,KAAKC,KAAK,CAACD,KAAKE,MAAM,GAAKL,EAAMM,MAAM,GAElE/B,EAAkB,WAAYxB,EAChC,EAiD2FhC,UAAU,OAAOe,sBAAoB,SAAST,0BAAwB,oBAAW,yBAKhK,WAACJ,MAAAA,CAAIF,UAAU,sBACb,UAACa,EAAAA,CAAKA,CAAAA,CAACwD,QAAQ,OAAOtD,sBAAoB,QAAQT,0BAAwB,oBAAW,cACrF,WAACkF,EAAAA,EAAMA,CAAAA,CAAC9B,MAAO9B,EAASK,IAAI,CAAEwD,cAAe/B,GAASF,EAAkB,OAAQE,GAAQiB,QAAQ,IAAC5D,sBAAoB,SAAST,0BAAwB,qBACpJ,UAACoF,EAAAA,EAAaA,CAAAA,CAAC3E,sBAAoB,gBAAgBT,0BAAwB,oBACzE,UAACqF,EAAAA,EAAWA,CAAAA,CAACjB,YAAY,mBAAmB3D,sBAAoB,cAAcT,0BAAwB,eAExG,WAACsF,EAAAA,EAAaA,CAAAA,CAAC7E,sBAAoB,gBAAgBT,0BAAwB,qBACzE,UAACuF,EAAAA,EAAUA,CAAAA,CAACnC,MAAM,UAAU3C,sBAAoB,aAAaT,0BAAwB,oBAAW,YAChG,UAACuF,EAAAA,EAAUA,CAAAA,CAACnC,MAAM,UAAU3C,sBAAoB,aAAaT,0BAAwB,oBAAW,YAChG,UAACuF,EAAAA,EAAUA,CAAAA,CAACnC,MAAM,cAAc3C,sBAAoB,aAAaT,0BAAwB,oBAAW,yBAK1G,WAACJ,MAAAA,CAAIF,UAAU,oCACb,UAACa,EAAAA,CAAKA,CAAAA,CAACwD,QAAQ,cAActD,sBAAoB,QAAQT,0BAAwB,oBAAW,2BAC5F,WAACkF,EAAAA,EAAMA,CAAAA,CAAC9B,MAAO9B,EAASM,aAAa,CAAEuD,cAAe/B,GAASF,EAAkB,gBAAiBE,GAAQ3C,sBAAoB,SAAST,0BAAwB,qBAC7J,UAACoF,EAAAA,EAAaA,CAAAA,CAAC3E,sBAAoB,gBAAgBT,0BAAwB,oBACzE,UAACqF,EAAAA,EAAWA,CAAAA,CAACjB,YAAY,gCAAgC3D,sBAAoB,cAAcT,0BAAwB,eAErH,WAACsF,EAAAA,EAAaA,CAAAA,CAAC7E,sBAAoB,gBAAgBT,0BAAwB,qBACzE,UAACuF,EAAAA,EAAUA,CAAAA,CAACnC,MAAM,OAAO3C,sBAAoB,aAAaT,0BAAwB,oBAAW,mBAC5FoB,EAAaoE,GAAG,CAACC,GAAe,UAACF,EAAAA,EAAUA,CAAAA,CAAsBnC,MAAOqC,EAAYxB,EAAE,CAACyB,QAAQ,YAC3FD,EAAYjE,IAAI,EAD6BiE,EAAYxB,EAAE,eASxE,WAACxE,EAAAA,EAAIA,CAAAA,CAACgB,sBAAoB,OAAOT,0BAAwB,qBACvD,UAACC,EAAAA,EAAUA,CAAAA,CAACQ,sBAAoB,aAAaT,0BAAwB,oBACnE,UAACE,EAAAA,EAASA,CAAAA,CAACR,UAAU,UAAUe,sBAAoB,YAAYT,0BAAwB,oBAAW,uBAEpG,UAACI,EAAAA,EAAWA,CAAAA,CAACK,sBAAoB,cAAcT,0BAAwB,oBACrE,WAACJ,MAAAA,CAAIF,UAAU,8BACM,YAAlB4B,EAASK,IAAI,EAAkB,WAAC/B,MAAAA,WAC7B,UAAC+F,KAAAA,CAAGjG,UAAU,yBAAgB,kBAC9B,WAACkG,KAAAA,CAAGlG,UAAU,iDACZ,UAACmG,KAAAA,UAAG,sDACJ,UAACA,KAAAA,UAAG,8CACJ,UAACA,KAAAA,UAAG,yCACJ,UAACA,KAAAA,UAAG,2DAGS,YAAlBvE,EAASK,IAAI,EAAkB,WAAC/B,MAAAA,WAC7B,UAAC+F,KAAAA,CAAGjG,UAAU,yBAAgB,kBAC9B,WAACkG,KAAAA,CAAGlG,UAAU,iDACZ,UAACmG,KAAAA,UAAG,oCACJ,UAACA,KAAAA,UAAG,yCACJ,UAACA,KAAAA,UAAG,6CACJ,UAACA,KAAAA,UAAG,yDAGS,gBAAlBvE,EAASK,IAAI,EAAsB,WAAC/B,MAAAA,WACjC,UAAC+F,KAAAA,CAAGjG,UAAU,yBAAgB,sBAC9B,WAACkG,KAAAA,CAAGlG,UAAU,iDACZ,UAACmG,KAAAA,UAAG,2CACJ,UAACA,KAAAA,UAAG,4CACJ,UAACA,KAAAA,UAAG,oDACJ,UAACA,KAAAA,UAAG,iEAOhB,WAACjG,MAAAA,CAAIF,UAAU,uCACb,UAAC4D,IAAIA,CAACC,KAAK,yBAAyB9C,cAA/B6C,QAAmD,OAAOtD,0BAAwB,oBACrF,UAACwD,EAAAA,CAAMA,CAAAA,CAACR,QAAQ,UAAUsB,KAAK,SAAS7D,sBAAoB,SAAST,0BAAwB,oBAAW,aAI1G,WAACwD,EAAAA,CAAMA,CAAAA,CAACc,KAAK,SAASwB,SAAU/E,EAAWN,sBAAoB,SAAST,0BAAwB,qBAC9F,UAAC+F,EAAAA,CAAIA,CAAAA,CAACrG,UAAU,eAAee,sBAAoB,OAAOT,0BAAwB,aACjFe,EAAY,cAAgB,8BAO7C,0BC7OA,6FCUO,SAASD,IAad,MAAO,CAAED,MAZK,CAAC,OAAE+B,CAAK,aAAEC,CAAW,SAAEG,EAAU,SAAS,CAAc,IACpD,eAAe,CAA3BA,EACFgD,EAAAA,EAAWA,CAACjD,KAAK,CAACH,EAAO,aACvBC,CACF,GAEAmD,EAAAA,EAAWA,CAACrD,OAAO,CAACC,EAAO,CACzBC,aACF,EAEJ,CAEe,CACjB,0BCxBA,sDCAA,iDCAA,uCAAmK,yBCAnK,wGCIe,SAASoD,EAAY,UAClCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,kECmBM,MAAS,cAAiB,UA9BI,CAClC,CACE,OACA,CACE,CAAG,kGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAwC,0CAAK,SAAU,EACrE,CACE,OACA,CACE,CAAG,gGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC7C,0BCpBA,sECAA,oDCAA,iECAA,0DCAA,iExBmBI,sBAAsB,gMyBbbC,EAAqB,CAChCvD,KADWuD,CACJ,wBACPtD,WAAAA,CAAa,6BACf,EACe,eAAeuD,EAAgB,UAC5CF,CAAQ,CAGT,CAJ6BE,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkCjD,KAAAA,GAAU,OAChE,MAAOqD,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAKjG,qBAAAA,CAAoB,OAAOV,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAA2G,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBL,WAAAA,CAAaA,EAAa9F,SAAb8F,YAAa9F,CAAoB,kBAAkBT,yBAAAA,CAAwB,uBACvGyG,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAWpG,qBAAAA,CAAoB,aAAaT,yBAAAA,CAAwB,eACrE2G,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAarG,qBAAAA,CAAoB,eAAeT,yBAAAA,CAAwB,uBACvEyG,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAOtG,qBAAAA,CAAoB,SAAST,yBAAAA,CAAwB,eAE7DyG,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAKtH,SAAAA,CAAU,kDACbwG,QAAAA,CAAAA,WAMb,CzBvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZ5F,EAO8B,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CARc,GAQV,CACrC,IAAI,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAExE,CAAE,KAAM,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,iS0BhE9B,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,QACA,CACA,UACA,QACA,CACA,UACA,MACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,uCAA6K,CA0BjM,4IAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA5CA,IAAsB,uCAAmK,CA4CzL,mIAGA,CACO,CACP,CACA,QAnDA,IAAsB,uCAA4J,CAmDlL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApEA,IAAsB,sCAAiJ,CAoEvK,gHACA,gBApEA,IAAsB,uCAAuJ,CAoE7K,sHACA,aApEA,IAAsB,uCAAoJ,CAoE1K,mHACA,WApEA,IAAsB,4CAAgF,CAoEtG,+CACA,cApEA,IAAsB,4CAAmF,CAoEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,+IAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,uCACA,sCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC/GD,yKCaM,EAAc,aAAqC,CAAC,EAAO,IAE7D,UAAC,IAAS,CAAC,MAAV,CACE,GAAG,EACJ,IAAK,EACL,YAAa,IAEI,EAAM,OACV,QAAQ,iCAAiC,EAAG,EAEvD,EAAM,cAAc,GAEhB,CAAC,CAFoB,CAEd,kBAAoB,EAAM,OAAS,EAAG,GAAM,eAAe,EACxE,KAKN,EAAM,YAxBO,EAwBO,MAIpB,IAAM,EAAO,yKC7Bb,SAAS4E,EAAO,CACd,GAAGvF,EAC+C,EAClD,MAAO,UAACsH,EAAAA,EAAoB,EAACpH,YAAU,SAAU,GAAGF,CAAK,CAAEc,sBAAoB,uBAAuBV,wBAAsB,SAASC,0BAAwB,cAC/J,CAMA,SAASqF,EAAY,CACnB,GAAG1F,EACgD,EACnD,MAAO,UAACsH,EAAAA,EAAqB,EAACpH,YAAU,eAAgB,GAAGF,CAAK,CAAEc,sBAAoB,wBAAwBV,wBAAsB,cAAcC,0BAAwB,cAC5K,CACA,SAASoF,EAAc,WACrB1F,CAAS,MACT+D,EAAO,SAAS,UAChByC,CAAQ,CACR,GAAGvG,EAGJ,EACC,MAAO,WAACsH,EAAAA,EAAuB,EAACpH,YAAU,iBAAiBqH,YAAWzD,EAAM/D,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+yBAAgzBJ,GAAa,GAAGC,CAAK,CAAEc,sBAAoB,0BAA0BV,wBAAsB,gBAAgBC,0BAAwB,uBACxgCkG,EACD,UAACe,EAAAA,EAAoB,EAACE,OAAO,IAAC1G,sBAAoB,uBAAuBT,0BAAwB,sBAC/F,UAACoH,EAAAA,CAAeA,CAAAA,CAAC1H,UAAU,oBAAoBe,sBAAoB,kBAAkBT,0BAAwB,mBAGrH,CACA,SAASsF,EAAc,WACrB5F,CAAS,UACTwG,CAAQ,UACRmB,EAAW,QAAQ,CACnB,GAAG1H,EACkD,EACrD,MAAO,UAACsH,EAAAA,EAAsB,EAACxG,sBAAoB,yBAAyBV,wBAAsB,gBAAgBC,0BAAwB,sBACtI,WAACiH,EAAAA,EAAuB,EAACpH,YAAU,iBAAiBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gjBAAijBuH,cAAyB,kIAAmI3H,GAAY2H,SAAUA,EAAW,GAAG1H,CAAK,CAAEc,sBAAoB,0BAA0BT,0BAAwB,uBAC93B,UAACsH,EAAAA,CAAqB7G,sBAAoB,uBAAuBT,0BAAwB,eACzF,UAACiH,EAAAA,EAAwB,EAACvH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,MAAoB,WAAbuH,GAAyB,uGAAwG5G,sBAAoB,2BAA2BT,0BAAwB,sBACpPkG,IAEH,UAACqB,EAAAA,CAAuB9G,sBAAoB,yBAAyBT,0BAAwB,mBAGrG,CAOA,SAASuF,EAAW,WAClB7F,CAAS,UACTwG,CAAQ,CACR,GAAGvG,EAC+C,EAClD,MAAO,WAACsH,EAAAA,EAAoB,EAACpH,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4aAA6aJ,GAAa,GAAGC,CAAK,CAAEc,sBAAoB,uBAAuBV,wBAAsB,aAAaC,0BAAwB,uBACzmB,UAACwH,OAAAA,CAAK9H,UAAU,sEACd,UAACuH,EAAAA,EAA6B,EAACxG,sBAAoB,gCAAgCT,0BAAwB,sBACzG,UAACyH,EAAAA,CAASA,CAAAA,CAAC/H,UAAU,SAASe,sBAAoB,YAAYT,0BAAwB,mBAG1F,UAACiH,EAAAA,EAAwB,EAACxG,sBAAoB,2BAA2BT,0BAAwB,sBAAckG,MAErH,CAOA,SAASoB,EAAqB,WAC5B5H,CAAS,CACT,GAAGC,EACyD,EAC5D,MAAO,UAACsH,EAAAA,EAA8B,EAACpH,YAAU,0BAA0BH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDJ,GAAa,GAAGC,CAAK,CAAEc,sBAAoB,iCAAiCV,wBAAsB,uBAAuBC,0BAAwB,sBAC9R,UAAC0H,EAAAA,CAAaA,CAAAA,CAAChI,UAAU,SAASe,sBAAoB,gBAAgBT,0BAAwB,gBAEpG,CACA,SAASuH,EAAuB,WAC9B7H,CAAS,CACT,GAAGC,EAC2D,EAC9D,MAAO,UAACsH,EAAAA,EAAgC,EAACpH,YAAU,4BAA4BH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDJ,GAAa,GAAGC,CAAK,CAAEc,sBAAoB,mCAAmCV,wBAAsB,yBAAyBC,0BAAwB,sBACtS,UAACoH,EAAAA,CAAeA,CAAAA,CAAC1H,UAAU,SAASe,sBAAoB,kBAAkBT,0BAAwB,gBAExG,0BC7FA,4EC0BM,MAAO,cAAiB,QAvBM,CAClC,CACE,OACA,CACE,CAAG,sGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,MAAQ,EAAE,EAAG,CAA0B,4BAAK,SAAU,EACzD,0BCbA,4DCAA,wDCAA,0DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,yBCNjN,uDCAA,qDCAA,kDCAA,0DCAA,mFCyBM,MAAM,cAAiB,OAtBO,CAClC,CACE,OACA,CACE,CAAG,yGACH,GAAK,SACP,EACF,CACA,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EAC1D,0BCZA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/../../../src/icons/arrow-left.ts", "webpack://terang-lms-ui/?3e35", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/?3664", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/./src/components/ui/label.tsx", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/?c604", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/./src/app/dashboard/admin/users/new/page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/./src/hooks/use-toast.ts", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/?10f1", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/./src/app/dashboard/admin/layout.tsx", "webpack://terang-lms-ui/../../../src/icons/eye-off.ts", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/?4f63", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/label.tsx", "webpack://terang-lms-ui/./src/components/ui/select.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/../../../src/icons/save.ts", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/../../../src/icons/eye.ts", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('ArrowLeft', __iconNode);\n\nexport default ArrowLeft;\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\n", "module.exports = require(\"module\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\new\\\\page.tsx\");\n", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/admin/users/new',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/admin/users/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/admin/users/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/admin/users/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cn } from '@/lib/utils';\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return <LabelPrimitive.Root data-slot='label' className={cn('flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50', className)} {...props} data-sentry-element=\"LabelPrimitive.Root\" data-sentry-component=\"Label\" data-sentry-source-file=\"label.tsx\" />;\n}\nexport { Label };", "module.exports = require(\"os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\new\\\\page.tsx\");\n", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { ArrowLeft, Save, Eye, EyeOff } from 'lucide-react';\nimport Link from 'next/link';\nimport { useToast } from '@/hooks/use-toast';\ninterface Institution {\n  id: number;\n  name: string;\n}\nexport default function NewUserPage() {\n  const router = useRouter();\n  const {\n    toast\n  } = useToast();\n  const [isLoading, setIsLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [institutions, setInstitutions] = useState<Institution[]>([]);\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    role: 'student',\n    institutionId: 'none'\n  });\n\n  // Fetch institutions for dropdown\n  useEffect(() => {\n    const fetchInstitutions = async () => {\n      try {\n        const response = await fetch('/api/institutions');\n        const data = await response.json();\n        if (data.success) {\n          setInstitutions(data.data.institutions);\n        }\n      } catch (error) {\n        console.error('Error fetching institutions:', error);\n      }\n    };\n    fetchInstitutions();\n  }, []);\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      const submitData = {\n        ...formData,\n        institutionId: formData.institutionId && formData.institutionId !== 'none' ? parseInt(formData.institutionId) : null\n      };\n      const response = await fetch('/api/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(submitData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        toast({\n          title: 'Success',\n          description: 'User created successfully'\n        });\n        router.push('/dashboard/admin/users');\n      } else {\n        toast({\n          title: 'Error',\n          description: data.error || 'Failed to create user',\n          variant: 'destructive'\n        });\n      }\n    } catch (error) {\n      console.error('Error creating user:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to create user',\n        variant: 'destructive'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const generatePassword = () => {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\n    let password = '';\n    for (let i = 0; i < 12; i++) {\n      password += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    handleInputChange('password', password);\n  };\n  return <div className='space-y-6' data-sentry-component=\"NewUserPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center space-x-4'>\r\n        <Link href='/dashboard/admin/users' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button variant='outline' size='sm' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <ArrowLeft className='mr-2 h-4 w-4' data-sentry-element=\"ArrowLeft\" data-sentry-source-file=\"page.tsx\" />\r\n            Back\r\n          </Button>\r\n        </Link>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>\r\n            Add New User\r\n          </h1>\r\n          <p className='text-muted-foreground'>\r\n            Create a new user account on the platform\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">User Details</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\r\n            Enter the basic information for the new user\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <form onSubmit={handleSubmit} className='space-y-6'>\r\n            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='name' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Full Name</Label>\r\n                <Input id='name' value={formData.name} onChange={e => handleInputChange('name', e.target.value)} placeholder='Enter full name' required data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='email' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Email Address</Label>\r\n                <Input id='email' type='email' value={formData.email} onChange={e => handleInputChange('email', e.target.value)} placeholder='Enter email address' required data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='password' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Password</Label>\r\n                <div className='relative'>\r\n                  <Input id='password' type={showPassword ? 'text' : 'password'} value={formData.password} onChange={e => handleInputChange('password', e.target.value)} placeholder='Enter password' required className='pr-20' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n                  <div className='absolute inset-y-0 right-0 flex items-center space-x-1 pr-2'>\r\n                    <Button type='button' variant='ghost' size='sm' onClick={() => setShowPassword(!showPassword)} className='h-7 w-7 p-0' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                      {showPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                <Button type='button' variant='outline' size='sm' onClick={generatePassword} className='mt-2' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                  Generate Password\r\n                </Button>\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='role' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">User Role</Label>\r\n                <Select value={formData.role} onValueChange={value => handleInputChange('role', value)} required data-sentry-element=\"Select\" data-sentry-source-file=\"page.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectValue placeholder='Select user role' data-sentry-element=\"SelectValue\" data-sentry-source-file=\"page.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectItem value='student' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">Student</SelectItem>\r\n                    <SelectItem value='teacher' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">Teacher</SelectItem>\r\n                    <SelectItem value='super_admin' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">Super Admin</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className='space-y-2 md:col-span-2'>\r\n                <Label htmlFor='institution' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Institution (Optional)</Label>\r\n                <Select value={formData.institutionId} onValueChange={value => handleInputChange('institutionId', value)} data-sentry-element=\"Select\" data-sentry-source-file=\"page.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectValue placeholder='Select institution (optional)' data-sentry-element=\"SelectValue\" data-sentry-source-file=\"page.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectItem value='none' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">No Institution</SelectItem>\r\n                    {institutions.map(institution => <SelectItem key={institution.id} value={institution.id.toString()}>\r\n                        {institution.name}\r\n                      </SelectItem>)}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Role Information */}\r\n            <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n              <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n                <CardTitle className='text-lg' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Role Information</CardTitle>\r\n              </CardHeader>\r\n              <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n                <div className='space-y-2 text-sm'>\r\n                  {formData.role === 'student' && <div>\r\n                      <h4 className='font-semibold'>Student Role:</h4>\r\n                      <ul className='mt-1 space-y-1 text-muted-foreground'>\r\n                        <li>• Can enroll in courses and view course materials</li>\r\n                        <li>• Can take quizzes and submit assignments</li>\r\n                        <li>• Can view their progress and grades</li>\r\n                        <li>• Limited to their assigned institution (if any)</li>\r\n                      </ul>\r\n                    </div>}\r\n                  {formData.role === 'teacher' && <div>\r\n                      <h4 className='font-semibold'>Teacher Role:</h4>\r\n                      <ul className='mt-1 space-y-1 text-muted-foreground'>\r\n                        <li>• Can create and manage courses</li>\r\n                        <li>• Can create quizzes and assignments</li>\r\n                        <li>• Can view and grade student submissions</li>\r\n                        <li>• Can manage students within their institution</li>\r\n                      </ul>\r\n                    </div>}\r\n                  {formData.role === 'super_admin' && <div>\r\n                      <h4 className='font-semibold'>Super Admin Role:</h4>\r\n                      <ul className='mt-1 space-y-1 text-muted-foreground'>\r\n                        <li>• Full access to all platform features</li>\r\n                        <li>• Can manage all institutions and users</li>\r\n                        <li>• Can view billing and subscription information</li>\r\n                        <li>• Can access system-wide analytics and reports</li>\r\n                      </ul>\r\n                    </div>}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            <div className='flex justify-end space-x-4'>\r\n              <Link href='/dashboard/admin/users' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n                <Button variant='outline' type='button' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                  Cancel\r\n                </Button>\r\n              </Link>\r\n              <Button type='submit' disabled={isLoading} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                <Save className='mr-2 h-4 w-4' data-sentry-element=\"Save\" data-sentry-source-file=\"page.tsx\" />\r\n                {isLoading ? 'Creating...' : 'Create User'}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "module.exports = require(\"node:zlib\");", "'use client';\r\n\r\nimport { toast as sonnerToast } from 'sonner';\r\n\r\ninterface ToastProps {\r\n  title: string;\r\n  description?: string;\r\n  variant?: 'default' | 'destructive';\r\n}\r\n\r\nexport function useToast() {\r\n  const toast = ({ title, description, variant = 'default' }: ToastProps) => {\r\n    if (variant === 'destructive') {\r\n      sonnerToast.error(title, {\r\n        description\r\n      });\r\n    } else {\r\n      sonnerToast.success(title, {\r\n        description\r\n      });\r\n    }\r\n  };\r\n\r\n  return { toast };\r\n}\r\n", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\n", "module.exports = require(\"node:os\");", "'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function AdminLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has super_admin role\n    requireRole('super_admin');\n  }, []);\n  return <>{children}</>;\n}", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49',\n      key: 'ct8e1f',\n    },\n  ],\n  ['path', { d: 'M14.084 14.158a3 3 0 0 1-4.242-4.242', key: '151rxh' }],\n  [\n    'path',\n    {\n      d: 'M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143',\n      key: '13bj9a',\n    },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n];\n\n/**\n * @component @name EyeOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNzMzIDUuMDc2YTEwLjc0NCAxMC43NDQgMCAwIDEgMTEuMjA1IDYuNTc1IDEgMSAwIDAgMSAwIC42OTYgMTAuNzQ3IDEwLjc0NyAwIDAgMS0xLjQ0NCAyLjQ5IiAvPgogIDxwYXRoIGQ9Ik0xNC4wODQgMTQuMTU4YTMgMyAwIDAgMS00LjI0Mi00LjI0MiIgLz4KICA8cGF0aCBkPSJNMTcuNDc5IDE3LjQ5OWExMC43NSAxMC43NSAwIDAgMS0xNS40MTctNS4xNTEgMSAxIDAgMCAxIDAtLjY5NiAxMC43NSAxMC43NSAwIDAgMSA0LjQ0Ni01LjE0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EyeOff = createLucideIcon('EyeOff', __iconNode);\n\nexport default EyeOff;\n", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\new\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'users',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\users\\\\new\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/admin/users/new/page\",\n        pathname: \"/dashboard/admin/users/new\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n", "'use client';\n\nimport * as React from 'react';\nimport * as SelectPrimitive from '@radix-ui/react-select';\nimport { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot='select' {...props} data-sentry-element=\"SelectPrimitive.Root\" data-sentry-component=\"Select\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot='select-group' {...props} data-sentry-element=\"SelectPrimitive.Group\" data-sentry-component=\"SelectGroup\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot='select-value' {...props} data-sentry-element=\"SelectPrimitive.Value\" data-sentry-component=\"SelectValue\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectTrigger({\n  className,\n  size = 'default',\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: 'sm' | 'default';\n}) {\n  return <SelectPrimitive.Trigger data-slot='select-trigger' data-size={size} className={cn(\"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"SelectPrimitive.Trigger\" data-sentry-component=\"SelectTrigger\" data-sentry-source-file=\"select.tsx\">\r\n      {children}\r\n      <SelectPrimitive.Icon asChild data-sentry-element=\"SelectPrimitive.Icon\" data-sentry-source-file=\"select.tsx\">\r\n        <ChevronDownIcon className='size-4 opacity-50' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>;\n}\nfunction SelectContent({\n  className,\n  children,\n  position = 'popper',\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return <SelectPrimitive.Portal data-sentry-element=\"SelectPrimitive.Portal\" data-sentry-component=\"SelectContent\" data-sentry-source-file=\"select.tsx\">\r\n      <SelectPrimitive.Content data-slot='select-content' className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md', position === 'popper' && 'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1', className)} position={position} {...props} data-sentry-element=\"SelectPrimitive.Content\" data-sentry-source-file=\"select.tsx\">\r\n        <SelectScrollUpButton data-sentry-element=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\" />\r\n        <SelectPrimitive.Viewport className={cn('p-1', position === 'popper' && 'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1')} data-sentry-element=\"SelectPrimitive.Viewport\" data-sentry-source-file=\"select.tsx\">\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton data-sentry-element=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>;\n}\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return <SelectPrimitive.Label data-slot='select-label' className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)} {...props} data-sentry-element=\"SelectPrimitive.Label\" data-sentry-component=\"SelectLabel\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return <SelectPrimitive.Item data-slot='select-item' className={cn(\"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\", className)} {...props} data-sentry-element=\"SelectPrimitive.Item\" data-sentry-component=\"SelectItem\" data-sentry-source-file=\"select.tsx\">\r\n      <span className='absolute right-2 flex size-3.5 items-center justify-center'>\r\n        <SelectPrimitive.ItemIndicator data-sentry-element=\"SelectPrimitive.ItemIndicator\" data-sentry-source-file=\"select.tsx\">\r\n          <CheckIcon className='size-4' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"select.tsx\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText data-sentry-element=\"SelectPrimitive.ItemText\" data-sentry-source-file=\"select.tsx\">{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>;\n}\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return <SelectPrimitive.Separator data-slot='select-separator' className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)} {...props} data-sentry-element=\"SelectPrimitive.Separator\" data-sentry-component=\"SelectSeparator\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return <SelectPrimitive.ScrollUpButton data-slot='select-scroll-up-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollUpButton\" data-sentry-component=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronUpIcon className='size-4' data-sentry-element=\"ChevronUpIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollUpButton>;\n}\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return <SelectPrimitive.ScrollDownButton data-slot='select-scroll-down-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollDownButton\" data-sentry-component=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronDownIcon className='size-4' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollDownButton>;\n}\nexport { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectScrollDownButton, SelectScrollUpButton, SelectSeparator, SelectTrigger, SelectValue };", "module.exports = require(\"node:fs\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('Save', __iconNode);\n\nexport default Save;\n", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('Eye', __iconNode);\n\nexport default Eye;\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["Card", "className", "props", "div", "data-slot", "cn", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "serverComponentModule.default", "Label", "LabelPrimitive", "data-sentry-element", "NewUserPage", "router", "useRouter", "toast", "useToast", "isLoading", "setIsLoading", "useState", "showPassword", "setShowPassword", "institutions", "setInstitutions", "formData", "setFormData", "name", "email", "password", "role", "institutionId", "handleSubmit", "e", "preventDefault", "submitData", "parseInt", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "success", "title", "description", "push", "error", "variant", "console", "handleInputChange", "field", "value", "prev", "Link", "href", "<PERSON><PERSON>", "size", "ArrowLeft", "h1", "p", "form", "onSubmit", "htmlFor", "Input", "id", "onChange", "target", "placeholder", "required", "type", "onClick", "Eye<PERSON>ff", "Eye", "generatePassword", "chars", "i", "char<PERSON>t", "Math", "floor", "random", "length", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "map", "institution", "toString", "h4", "ul", "li", "disabled", "Save", "sonnerToast", "AdminLayout", "children", "metadata", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "SelectPrimitive", "data-size", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDownIcon", "position", "SelectScrollUpButton", "SelectScrollDownButton", "span", "CheckIcon", "ChevronUpIcon"], "sourceRoot": ""}