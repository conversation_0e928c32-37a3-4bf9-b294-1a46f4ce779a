{"version": 3, "file": "../app/api/questions/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,oXCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAaC,EADEH,MACFG,CAASF,EAAAA,CAE5B,GAAIG,MAAMF,GACR,OAAOG,CADCH,CACDG,CADc,WACdA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI3E,IAAMC,EAAe,MAAMC,EAAAA,EAAAA,CACxBC,MAAM,GACNC,IAAI,CAACC,EAAAA,SAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,SAAAA,CAAUZ,EAAE,CAAEC,IACvBc,KAAK,CAAC,CADiBd,CAAAA,CAG1B,GAA4B,GAAG,CAA3BO,EAAaQ,MAAM,CACrB,GADER,IACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAG1E,IAAMU,EAAWT,CAAY,CAAC,EAAE,CAG1BU,CAHAD,CAGyB,CAC7B,GAAGA,CAAQ,CACXA,QAAAA,CAAUA,EAASA,GAFfC,KAEuB,CAC3BC,OAAAA,CAASF,EAASE,OAAO,CACzBC,WAAAA,CAAaH,EAASG,WAAW,CACjCC,WAAAA,CAAaJ,EAASI,WAAAA,EAGxB,OAAOjB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEY,QAAAA,CAAUC,CAAuB,EAC9D,CAAE,MAAOZ,EAAO,CAEd,EAFOA,KACPgB,OAAAA,CAAQhB,KAAK,CAAC,2BAA4BA,GACnCF,EAAAA,CADmCE,WACnCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAegB,EACpBzB,CAAoB,CACpB,CAFoByB,OAElBxB,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,IAAE,CAAE,CAAG,MAAMD,EACfE,EAAaC,EADEH,MACFG,CAASF,EAAAA,CAAAA,GAExBG,MAAMF,GACR,OADQA,CAAAA,CACDG,CADc,WACdA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI3E,GAAM,MACJiB,CAAI,UACJP,CAAQ,SACRE,CAAO,aACPC,CAAW,aACXC,CAAW,QACXI,CAAM,YACNC,CAAU,CACVC,WAAS,CACV,CAVY,EAUTC,IAAAA,EAVuBvB,IAAI,CAAZP,EAab+B,EAAmB,MAAMpB,EAAAA,EAAAA,CAC5BC,MAAM,GACNC,IAAI,CAACC,EAAAA,SAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,SAAAA,CAAUZ,EAAE,CAAEC,IACvBc,KAAK,CADkBd,CAAAA,CAAAA,CAG1B,GAAgC,GAAG,CAA/B4B,EAAiBb,MAAM,CACzB,OADEa,EACKzB,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,GAAIoB,EAAW,CACb,IAAMG,EAAqB,MAAMrB,EAAAA,EAAAA,CAC9BC,KADGoB,CACG,CAAC,CACN7B,UAAAA,CAAYW,EAAAA,SAASA,CAACZ,EAAE,CACxB+B,MAAAA,CAAQnB,EAAAA,SAASA,CAACmB,MAAM,CACxBJ,SAAAA,CAAWK,EAAAA,OAAOA,CAACL,SAAAA,CACrB,EACChB,IAAI,CAACC,EAAAA,SAAAA,CAAAA,CACLqB,QAAQ,CAACC,EAAAA,OAAAA,CAASpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,SAAAA,CAAUmB,MAAM,CAAEG,EAAAA,OAAAA,CAAQlC,EAAE,GACjDiC,QAAQ,CAACE,EAAAA,QAAAA,CAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGoB,EAAAA,OAAAA,CAAQE,SAAS,CAAED,EAAAA,QAAAA,CAASnC,EAAE,GACpDiC,QAAQ,CAACI,EAAAA,OAAAA,CAASvB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGqB,EAAAA,QAAAA,CAASG,QAAQ,CAAED,EAAAA,OAAAA,CAAQrC,EAAE,GAClDiC,QAAQ,CAACD,EAAAA,OAAAA,CAASlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGuB,EAAAA,OAAAA,CAAQE,QAAQ,CAAEP,EAAAA,OAAAA,CAAQhC,EAAE,GACjDa,KAAK,CACJ2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACE1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,SAAAA,CAAUZ,EAAE,CAAEC,GACjBa,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQL,SAAS,CAAEA,KAGzBZ,IAHyBY,CAAAA,CAAAA,CAAAA,EAK5B,GAAkC,GAAG,CAAjCG,EAAmBd,MAAM,CAC3B,OAAOZ,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,yCAAyC,CAClD,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,IAAMkC,EAAkB,MAAMhC,EAAAA,EAAAA,CAC3BiC,MAAM,CAAC9B,EAAAA,SAAAA,CAAAA,CACP+B,GAAG,CAAC,CACH,GAAInB,GAAQ,CAARA,OAAgB,CACpB,GAAIP,GAAY,CAAEA,IAAdA,IAAcA,CAAU2B,IAAAA,CAAKC,SAAS,CAAC5B,GAAW,CACtD,GAAIE,CADuCF,EAC5B,CAAEE,GAAbA,IAAaA,CAASyB,IAAAA,CAAKC,SAAS,CAAC1B,GAAU,CACnD,GADyCA,KACrB2B,OAAa,CAAE1B,CAAf0B,UAAe1B,CAA6B,KAAhBA,EAAqB,KAAOA,EAAa,CACzF,CADgDA,OAC5B0B,IAAhBzB,GAA6B,CAAEA,CAAfyB,UAAezB,CAA6B,KAAhBA,EAAqB,KAAOA,EAAa,CACzF,CADgDA,EAC5CI,SAAWqB,GAAa,CAAErB,MAAAA,CAAQA,EAAOsB,QAAQ,GAAI,CACzD,QAAmBD,IAAfpB,GAA4B,EAAboB,UAAepB,EAAY,CAC9CsB,SAAAA,CAAW,IAAIC,IAAAA,GAEhBpC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,SAASA,CAACZ,EAAE,CAAEC,IACvBiD,SAAS,GAGNhC,EAAyB,CAC7B,GAAGuB,CAAe,CAAC,EAAE,CACrBxB,QAAAA,CAAUwB,CAAe,CAFrBvB,EAEwB,CAACD,QAAQ,CACrCE,OAAAA,CAASsB,CAAe,CAAC,EAAE,CAACtB,OAAO,CACnCC,WAAAA,CAAaqB,CAAe,CAAC,EAAE,CAACrB,WAAW,CAC3CC,WAAAA,CAAaoB,CAAe,CAAC,EAAE,CAACpB,WAAAA,EAGlC,OAAOjB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBY,QAAAA,CAAUC,EACViC,OAAAA,CAAS,YADCjC,mBAEZ,EACF,CAAE,MAAOZ,EAAO,CAEd,EAFOA,KACPgB,OAAAA,CAAQhB,KAAK,CAAC,2BAA4BA,GACnCF,EADmCE,CAAAA,WACnCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAe6C,EACpBtD,CAAoB,CACpB,IAFoBsD,IAElBrD,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAaC,EADEH,MACFG,CAASF,EAAAA,CAAAA,GAExBG,MAAMF,GACR,OADQA,CAAAA,CACDG,CADc,WACdA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI3E,IAAMoB,EADe7B,EAAQuD,KAARvD,EAAe,CAACwD,EACnBA,UAD+B,CAClBC,GAAG,CAAC,aAG7B1B,EAAmB,MAAMpB,EAAAA,EAAAA,CAC5BC,MAAM,GACNC,IAAI,CAACC,EAAAA,SAAAA,CAAAA,CACLC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACF,EAAAA,SAAAA,CAAUZ,EAAE,CAAEC,IACvBc,KAAK,CADkBd,CAAAA,CAAAA,CAG1B,GAAgC,GAAG,CAA/B4B,EAAiBb,MAAM,CACzB,OADEa,EACKzB,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,GAAIoB,EAAW,CACb,IAAMG,EADJH,MAC+BlB,EAAAA,EAAAA,CAC9BC,KADGoB,CACG,CAAC,CACN7B,UAAAA,CAAYW,EAAAA,SAASA,CAACZ,EAAE,CACxB+B,MAAAA,CAAQnB,EAAAA,SAASA,CAACmB,MAAM,CACxBJ,SAAAA,CAAWK,EAAAA,OAAOA,CAACL,SAAAA,CACrB,EACChB,IAAI,CAACC,EAAAA,SAAAA,CAAAA,CACLqB,QAAQ,CAACC,EAAAA,OAAAA,CAASpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,SAAAA,CAAUmB,MAAM,CAAEG,EAAAA,OAAAA,CAAQlC,EAAE,GACjDiC,QAAQ,CAACE,EAAAA,QAAAA,CAAUrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGoB,EAAAA,OAAAA,CAAQE,SAAS,CAAED,EAAAA,QAAAA,CAASnC,EAAE,GACpDiC,QAAQ,CAACI,EAAAA,OAAAA,CAASvB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGqB,EAAAA,QAAAA,CAASG,QAAQ,CAAED,EAAAA,OAAAA,CAAQrC,EAAE,GAClDiC,QAAQ,CAACD,EAAAA,OAAAA,CAASlB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGuB,EAAAA,OAAAA,CAAQE,QAAQ,CAAEP,EAAAA,OAAAA,CAAQhC,EAAE,GACjDa,KAAK,CACJ2B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACE1B,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,SAAAA,CAAUZ,EAAE,CAAEC,GACjBa,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkB,EAAAA,OAAAA,CAAQL,SAAS,CAAEzB,QAAAA,CAASyB,MAGlCZ,GAHkCY,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAkC,GAAG,CAAjCG,EAAmBd,MAAM,CAC3B,OAAOZ,EADL0B,YACK1B,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,yCAAyC,CAClD,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAKA,OAFA,MAAME,EAAAA,EAAAA,CAAG+C,MAAM,CAAC5C,EAAAA,SAAAA,CAAAA,CAAWC,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGF,EAAAA,SAAAA,CAAUZ,EAAE,CAAEC,IAE3CG,EAAAA,IAF2CH,CAAAA,CAAAA,MAE3CG,CAAaC,IAAI,CAAC,CAAE8C,OAAAA,CAAS,+BAAgC,EACtE,CAAE,MAAO7C,EAAO,CAEd,EAFOA,KACPgB,OAAAA,CAAQhB,KAAK,CAAC,2BAA4BA,GACnCF,EADmCE,CAAAA,WACnCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCnNA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,CAC1D,MAAM,GACN,IAFuD,cAErC,CAAE,qBAAqB,SACzC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAFmB,CAMjB,IAAC,EAAM,CAAH,CAAekD,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,iCACA,+BACA,iBACA,yCACA,CAAK,CACL,oJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/src/app/api/questions/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?5587", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { questions, quizzes, chapters, modules, courses } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/questions/[id] - Get a specific question\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const questionId = parseInt(id);\r\n    \r\n    if (isNaN(questionId)) {\r\n      return NextResponse.json({ error: 'Invalid question ID' }, { status: 400 });\r\n    }\r\n\r\n    // Get question\r\n    const questionData = await db\r\n      .select()\r\n      .from(questions)\r\n      .where(eq(questions.id, questionId))\r\n      .limit(1);\r\n\r\n    if (questionData.length === 0) {\r\n      return NextResponse.json({ error: 'Question not found' }, { status: 404 });\r\n    }\r\n\r\n    const question = questionData[0];\r\n\r\n    // Parse options\r\n    const questionWithParsedData = {\r\n      ...question,\r\n      question: question.question,\r\n      options: question.options,\r\n      essayAnswer: question.essayAnswer, // Ensure this maps correctly from DB\r\n      explanation: question.explanation, // Ensure this maps correctly from DB\r\n    };\r\n\r\n    return NextResponse.json({ question: questionWithParsedData });\r\n  } catch (error) {\r\n    console.error('Error fetching question:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/questions/[id] - Update a question\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const questionId = parseInt(id);\r\n    \r\n    if (isNaN(questionId)) {\r\n      return NextResponse.json({ error: 'Invalid question ID' }, { status: 400 });\r\n    }\r\n\r\n    const body = await request.json();\r\n    const {\r\n      type,\r\n      question,\r\n      options,\r\n      essayAnswer, // Renamed from correctAnswer\r\n      explanation, // New field\r\n      points,\r\n      orderIndex,\r\n      teacherId\r\n    } = body;\r\n\r\n    // Check if question exists\r\n    const existingQuestion = await db\r\n      .select()\r\n      .from(questions)\r\n      .where(eq(questions.id, questionId))\r\n      .limit(1);\r\n\r\n    if (existingQuestion.length === 0) {\r\n      return NextResponse.json({ error: 'Question not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to update this question\r\n    if (teacherId) {\r\n      const questionWithCourse = await db\r\n        .select({\r\n          questionId: questions.id,\r\n          quizId: questions.quizId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(questions)\r\n        .leftJoin(quizzes, eq(questions.quizId, quizzes.id))\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(questions.id, questionId),\r\n            eq(courses.teacherId, teacherId)\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (questionWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to update this question' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Update the question\r\n    const updatedQuestion = await db\r\n      .update(questions)\r\n      .set({\r\n        ...(type && { type }),\r\n        ...(question && { question: JSON.stringify(question) }),\r\n        ...(options && { options: JSON.stringify(options) }),\r\n        ...(essayAnswer !== undefined && { essayAnswer: essayAnswer === '' ? null : essayAnswer }),\r\n        ...(explanation !== undefined && { explanation: explanation === '' ? null : explanation }),\r\n        ...(points !== undefined && { points: points.toString() }),\r\n        ...(orderIndex !== undefined && { orderIndex }),\r\n        updatedAt: new Date()\r\n      })\r\n      .where(eq(questions.id, questionId))\r\n      .returning();\r\n\r\n    // Parse options for response\r\n    const questionWithParsedData = {\r\n      ...updatedQuestion[0],\r\n      question: updatedQuestion[0].question,\r\n      options: updatedQuestion[0].options,\r\n      essayAnswer: updatedQuestion[0].essayAnswer,\r\n      explanation: updatedQuestion[0].explanation,\r\n    };\r\n\r\n    return NextResponse.json({\r\n      question: questionWithParsedData,\r\n      message: 'Question updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating question:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/questions/[id] - Delete a question\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const questionId = parseInt(id);\r\n    \r\n    if (isNaN(questionId)) {\r\n      return NextResponse.json({ error: 'Invalid question ID' }, { status: 400 });\r\n    }\r\n\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n\r\n    // Check if question exists\r\n    const existingQuestion = await db\r\n      .select()\r\n      .from(questions)\r\n      .where(eq(questions.id, questionId))\r\n      .limit(1);\r\n\r\n    if (existingQuestion.length === 0) {\r\n      return NextResponse.json({ error: 'Question not found' }, { status: 404 });\r\n    }\r\n\r\n    // Verify teacher has permission to delete this question\r\n    if (teacherId) {\r\n      const questionWithCourse = await db\r\n        .select({\r\n          questionId: questions.id,\r\n          quizId: questions.quizId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(questions)\r\n        .leftJoin(quizzes, eq(questions.quizId, quizzes.id))\r\n        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))\r\n        .leftJoin(modules, eq(chapters.moduleId, modules.id))\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(questions.id, questionId),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (questionWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to delete this question' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Delete the question\r\n    await db.delete(questions).where(eq(questions.id, questionId));\r\n\r\n    return NextResponse.json({ message: 'Question deleted successfully' });\r\n  } catch (error) {\r\n    console.error('Error deleting question:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/questions/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\questions\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/questions/[id]/route\",\n        pathname: \"/api/questions/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/questions/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\questions\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "questionId", "parseInt", "isNaN", "NextResponse", "json", "error", "status", "questionData", "db", "select", "from", "questions", "where", "eq", "limit", "length", "question", "questionWithParsedData", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "console", "PUT", "type", "points", "orderIndex", "teacherId", "body", "existingQuestion", "questionWithCourse", "quizId", "courses", "leftJoin", "quizzes", "chapters", "chapterId", "modules", "moduleId", "courseId", "and", "updatedQuestion", "update", "set", "JSON", "stringify", "undefined", "toString", "updatedAt", "Date", "returning", "message", "DELETE", "nextUrl", "searchParams", "get", "delete", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}