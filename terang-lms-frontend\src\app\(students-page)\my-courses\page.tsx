'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Book02Icon, UserIcon, Clock01Icon, PlayIcon } from 'hugeicons-react';
import Link from 'next/link';
import { useEnrollment } from '@/contexts/enrollment-context';
import { Breadcrumbs } from '@/components/breadcrumbs';

const ModulesPage: React.FC = () => {
  const { isEnrolled, courseData, enrolledCourses } = useEnrollment();

  // Use enrolled courses if available, fallback to empty array
  const courses = enrolledCourses.length > 0 ? enrolledCourses : (isEnrolled ? [courseData] : []);

  if (!isEnrolled && courses.length === 0) {
    return (
      <div className='min-h-screen bg-gray-50 p-8'>
        <div className='mx-auto max-w-6xl space-y-6 pb-8'>
          {/* Breadcrumbs - Top Level */}
          <Breadcrumbs />
          
          {/* Header Section */}
          <div className='flex items-center space-x-3'>
            <Book02Icon className='h-8 w-8 text-[var(--iai-primary)]' />
            <div>
              <h1 className='text-3xl font-bold text-gray-900'>
                Kursus Saya
              </h1>
              <p className='text-gray-600'>Lanjutkan perjalanan belajar Anda</p>
            </div>
          </div>

          {/* Empty State */}
          <div className='py-16 text-center'>
            <Book02Icon className='mx-auto mb-6 h-16 w-16 text-gray-400' />
            <h3 className='mb-4 text-xl font-semibold text-gray-900'>
              Tidak ada kursus tersedia
            </h3>
            <p className='mb-8 text-gray-600'>
              Anda belum terdaftar dalam kursus apapun.
            </p>
            <Link href="/courses">
              <Button variant="iai">
                <Book02Icon className='mr-2 h-4 w-4' />
                Jelajahi Kursus
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 p-8'>
      <div className='mx-auto max-w-6xl space-y-6 pb-8'>
        {/* Breadcrumbs - Top Level */}
        <Breadcrumbs />
        
        {/* Header Section */}
        <div className='flex items-center space-x-3'>
          <Book02Icon className='h-8 w-8 text-[var(--iai-primary)]' />
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>
              Kursus Saya
            </h1>
            <p className='text-gray-600'>Lanjutkan perjalanan belajar Anda</p>
          </div>
        </div>

        {/* Course Cards Grid */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {courses.map((course) => {
            const completedChapters = course.modules.reduce(
              (total, module) =>
                total +
                module.chapters.filter(
                  (ch) =>
                    ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
                ).length,
              0
            );

            const totalChapters = course.modules.reduce(
              (total, module) => total + module.chapters.length,
              0
            );
            const overallProgress =
              totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

            const completedModules = course.modules.filter(
              (m) =>
                m.chapters.every(
                  (ch) =>
                    ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
                ) && m.moduleQuiz.isPassed
            ).length;

            return (
              <Card
                key={course.id}
                className='group transition-shadow hover:shadow-lg'
              >
                <CardHeader className='border-b'>
                  <div className='space-y-3'>
                    <div className='flex items-start justify-between'>
                      <CardTitle className='text-lg transition-colors group-hover:text-blue-600'>
                        {course.name}
                      </CardTitle>
                      <Badge
                        variant={
                          course.status === 'completed'
                            ? 'default'
                            : 'secondary'
                        }
                      >
                        {course.status === 'completed'
                          ? 'Selesai'
                          : 'Sedang Belajar'}
                      </Badge>
                    </div>
                    <p className='line-clamp-2 text-sm text-gray-600'>
                      {course.description}
                    </p>
                    <div className='flex flex-wrap gap-4 text-xs text-gray-500'>
                      <div className='flex items-center gap-1'>
                        <UserIcon className='h-3 w-3' />
                        <span>{course.instructor}</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Clock01Icon className='h-3 w-3' />
                        <span>{course.modules.length} Modul</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className='pt-4'>
                  <div className='space-y-4'>
                    {/* Progress */}
                    <div>
                      <div className='mb-2 flex items-center justify-between'>
                        <span className='text-sm font-medium'>Kemajuan</span>
                        <span className='text-sm text-gray-500'>
                          {Math.round(overallProgress)}%
                        </span>
                      </div>
                      <Progress value={overallProgress} className='h-2' />
                    </div>

                    {/* Quick Stats */}
                    <div className='grid grid-cols-2 gap-3 text-center'>
                      <div>
                        <div className='text-lg font-bold text-blue-600'>
                          {completedModules}
                        </div>
                        <div className='text-xs text-gray-500'>Modul</div>
                      </div>
                      <div>
                        <div className='text-lg font-bold text-green-600'>
                          {completedChapters}
                        </div>
                        <div className='text-xs text-gray-500'>Bab</div>
                      </div>
                    </div>

                    {/* Action Button */}
                    <Link href={`/my-courses/${course.id}`} className='block'>
                      <Button variant="iai" className='w-full' size='sm'>
                        <PlayIcon className='mr-2 h-4 w-4' />
                        Lanjutkan Belajar
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* Add more course placeholder cards */}
          {[...Array(5)].map((_, index) => (
            <Card
              key={`placeholder-${index}`}
              className='border-dashed opacity-50'
            >
              <CardHeader className='border-b border-dashed'>
                <div className='space-y-3'>
                  <div className='flex items-start justify-between'>
                    <CardTitle className='text-lg text-gray-400'>
                      Course {index + 2}
                    </CardTitle>
                    <Badge variant='outline'>Coming Soon</Badge>
                  </div>
                  <p className='text-sm text-gray-400'>
                    More courses will be available soon.
                  </p>
                </div>
              </CardHeader>
              <CardContent className='pt-4'>
                <div className='space-y-4'>
                  <div className='h-12 animate-pulse rounded bg-gray-100'></div>
                  <div className='grid grid-cols-2 gap-3'>
                    <div className='h-8 animate-pulse rounded bg-gray-100'></div>
                    <div className='h-8 animate-pulse rounded bg-gray-100'></div>
                  </div>
                  <Button className='w-full' size='sm' disabled>
                    <Book02Icon className='mr-2 h-4 w-4' />
                    Not Available
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ModulesPage;
