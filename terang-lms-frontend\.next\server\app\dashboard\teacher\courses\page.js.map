{"version": 3, "file": "../app/dashboard/teacher/courses/page.js", "mappings": "qbAAA,wRCmBM,MAAO,cAAiB,QAhBM,CAClC,CAAC,MAAQ,EAAE,KAAO,MAAM,OAAQ,IAAM,GAAG,CAAK,KAAG,IAAK,CAAI,MAAK,GAAI,GAAK,KAAK,SAAU,EACvF,CAAC,MAAQ,EAAE,EAAG,CAA2D,6DAAK,SAAU,EAC1F,2FCqBe,SAASA,IACtB,GAAM,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7C,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACK,EAAkBC,EAAoB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAIlEO,EAAe,UACnB,GAAI,CACF,IAAMC,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAChC,GAAI,CAACF,EAAM,YACTG,EAAAA,EAAKA,CAACC,KAAK,CAAC,iCAGd,IAAMC,EAAW,MAAMC,MAAM,CAAC,uBAAuB,EAAEN,EAAKO,EAAE,EAAE,EAC1DC,EAAO,MAAMH,EAASI,IAAI,GAC5BD,EAAKE,OAAO,CACdhB,CADgB,CACLc,EAAKf,OAAO,EAAI,EAAE,EAE7BU,EAAAA,EAAKA,CAACC,KAAK,CAACI,EAAKJ,KAAK,EAAI,0BAE9B,CAAE,MAAOA,EAAO,CACdO,QAAQP,KAAK,CAAC,0BAA2BA,GACzCD,EAAAA,EAAKA,CAACC,KAAK,CAAC,0BACd,QAAU,CACRR,GAAa,EACf,CACF,EACMgB,EAAqB,MAAOC,IAChC,GAAKC,CAAD,OAAS,+EAA+E,EAGxED,GACpB,GAAI,CACF,IAAMb,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAChC,GAAI,CAACF,EAAM,YACTG,EAAAA,EAAKA,CAACC,KAAK,CAAC,mCAGd,IAAMC,EAAW,MAAMC,MAAM,CAAC,aAAa,EAAEO,EAAS,WAAW,EAAEb,EAAKO,EAAE,EAAE,CAAE,CAC5EQ,OAAQ,QACV,GACMP,EAAO,MAAMH,EAASI,IAAI,GAC5BD,EAAKE,OAAO,EACdP,EAAAA,EAAKA,CAACO,OAAO,CAAC,gCACdX,KAEAI,EAAAA,EAAKA,CAACC,KAAK,CAACI,EAAKJ,KAAK,EAAI,0BAE9B,CAAE,MAAOA,EAAO,CACdO,QAAQP,KAAK,CAAC,yBAA0BA,GACxCD,EAAAA,EAAKA,CAACC,KAAK,CAAC,0BACd,QAAU,CACRN,EAAoB,KACtB,EACF,EACMkB,EAAkBvB,EAAQwB,MAAM,CAACC,GAAUA,EAAOC,IAAI,CAACC,WAAW,GAAGC,QAAQ,CAAC/B,EAAW8B,WAAW,KAAOF,EAAOI,WAAW,CAACF,WAAW,GAAGC,QAAQ,CAAC/B,EAAW8B,WAAW,KAAOF,EAAOK,UAAU,CAACH,WAAW,GAAGC,QAAQ,CAAC/B,EAAW8B,WAAW,KACjPI,EAAkB,IACtBC,UAAUC,SAAS,CAACC,SAAS,CAACC,GAC9BzB,EAAAA,EAAKA,CAACO,OAAO,CAAC,mCAChB,SAoBA,EACS,WAACmB,MAAAA,CAAIC,UAAU,sBAClB,WAACD,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,UAACE,KAAAA,CAAGD,UAAU,6CAAoC,eAClD,UAACE,IAAAA,CAAEF,UAAU,iCAAwB,kDAIvC,UAACD,MAAAA,CAAIC,UAAU,0BACb,UAACG,IAAIA,CAACC,KAAK,KAAND,qCACH,WAACE,EAAAA,CAAMA,CAAAA,WACL,UAACC,EAAAA,CAAIA,CAAAA,CAACN,UAAU,iBAAiB,0BAMzC,UArCkB,IAAM,UAACD,MAAAA,CAAIC,UAAU,uDAAuDO,wBAAsB,kBAAkBC,0BAAwB,oBAC/J,IAAIC,MAAM,GAAG,CAACC,GAAG,CAAC,CAACC,EAAGC,IAAM,WAACC,EAAAA,EAAIA,CAAAA,CAASb,UAAU,4BACjD,UAACD,MAAAA,CAAIC,UAAU,iCACb,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,oBAEtB,WAACe,EAAAA,EAAWA,CAAAA,CAACf,UAAU,gBACrB,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,mBACpB,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,oBACpB,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,mBACpB,WAACD,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,CAAIC,UAAU,2BACb,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,aACpB,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,gBAEtB,UAACc,EAAAA,CAAQA,CAAAA,CAACd,UAAU,iCAbYY,MAoCrCI,CAAAA,MAGA,WAACjB,MAAAA,CAAIC,UAAU,YAAYO,wBAAsB,cAAcC,0BAAwB,qBAC1F,WAACT,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,UAACE,KAAAA,CAAGD,UAAU,6CAAoC,eAClD,UAACE,IAAAA,CAAEF,UAAU,iCAAwB,kDAIvC,WAACD,MAAAA,CAAIC,UAAU,2BACb,UAACG,IAAIA,CAACC,KAAK,KAAND,iCAA4Cc,sBAAoB,OAAOT,0BAAwB,oBAClG,WAACH,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUD,sBAAoB,SAAST,0BAAwB,qBAC7E,UAACW,EAAAA,CAAGA,CAAAA,CAACnB,UAAU,eAAeiB,sBAAoB,MAAMT,0BAAwB,aAAa,oBAIjG,UAACL,IAAIA,CAACC,KAAK,KAAND,4BAAuCc,sBAAoB,OAAOT,0BAAwB,oBAC7F,WAACH,EAAAA,CAAMA,CAAAA,CAACY,sBAAoB,SAAST,0BAAwB,qBAC3D,UAACF,EAAAA,CAAIA,CAAAA,CAACN,UAAU,eAAeiB,sBAAoB,OAAOT,0BAAwB,aAAa,2BAOvG,WAACT,MAAAA,CAAIC,UAAU,sBACb,UAACD,MAAAA,CAAIC,UAAU,uCACb,WAACD,MAAAA,CAAIC,UAAU,4BACb,UAACoB,EAAAA,CAAMA,CAAAA,CAACpB,UAAU,wDAAwDiB,sBAAoB,SAAST,0BAAwB,aAC/H,UAACa,EAAAA,CAAKA,CAAAA,CAACC,YAAY,oBAAoBC,MAAO/D,EAAYgE,SAAUC,GAAKhE,EAAcgE,EAAEC,MAAM,CAACH,KAAK,EAAGvB,UAAU,OAAOiB,sBAAoB,QAAQT,0BAAwB,kBAIjL,UAACT,MAAAA,CAAIC,UAAU,gEACZd,EAAgBwB,GAAG,CAACtB,GAAU,WAACyB,EAAAA,EAAIA,CAAAA,CAAiBb,UAAU,8DAC3D,UAAC2B,EAAAA,EAAUA,CAAAA,CAAC3B,UAAU,eAEpB,UAACD,MAAAA,CAAIC,UAAU,oBACb,WAACD,MAAAA,CAAIC,UAAU,4DACZZ,EAAOwC,YAAY,CAAG,UAACC,MAAAA,CAAIC,IAAK1C,EAAOwC,YAAY,CAAEG,IAAK3C,EAAOC,IAAI,CAAE2C,QAAQ,OAAOhC,UAAU,+BAAkC,UAACD,MAAAA,CAAIC,UAAU,uGAC9I,UAACiC,EAAAA,CAAQA,CAAAA,CAACjC,UAAU,8BAExB,UAACD,MAAAA,CAAIC,UAAU,kCACb,UAACkC,EAAAA,CAAKA,CAAAA,CAAChB,QAAS9B,gBAAO+C,MAAM,CAAmB,UAAY,mBACzD/C,EAAO+C,MAAM,YAMxB,UAACpB,EAAAA,EAAWA,CAAAA,CAACf,UAAU,oBACrB,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,WACC,UAACqC,KAAAA,CAAGpC,UAAU,8CAAsCZ,EAAOC,IAAI,GAC/D,UAACa,IAAAA,CAAEF,UAAU,2DACVZ,EAAOI,WAAW,MAIvB,WAACO,MAAAA,CAAIC,UAAU,sDACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACqC,OAAAA,CAAKrC,UAAU,8CACbZ,EAAOK,UAAU,GAEpB,UAACY,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,QAAQoB,KAAK,KAAKC,QAAS,IAAM7C,EAAgBN,EAAOK,UAAU,EAAGO,UAAU,uBAC7F,UAACwC,EAAIA,CAACxC,CAADwC,SAAW,iBAGpB,UAACN,EAAAA,CAAKA,CAAAA,CAAChB,QAAyB,aAAhB9B,EAAOqD,IAAI,CAAkB,UAAY,qBACtDrD,EAAOqD,IAAI,MAIhB,WAAC1C,MAAAA,CAAIC,UAAU,4EACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACiC,EAAAA,CAAQA,CAAAA,CAACjC,UAAU,YACpB,WAAC0C,OAAAA,WAAMtD,EAAOuD,WAAW,CAAC,iBAE5B,WAAC5C,MAAAA,CAAIC,UAAU,wCACb,UAAC4C,EAAAA,CAAKA,CAAAA,CAAC5C,UAAU,YACjB,WAAC0C,OAAAA,WAAMtD,EAAOyD,YAAY,CAAC,qBAI/B,WAAC9C,MAAAA,CAAIC,UAAU,mDACb,UAACD,MAAAA,CAAIC,UAAU,0BACb,UAACG,IAAIA,CAACC,KAAM,CAAC,IAARD,uBAAmC,EAAEf,EAAOX,EAAE,EAAE,UACnD,WAAC4B,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUoB,KAAK,eAC7B,UAACQ,EAAAA,CAAIA,CAAAA,CAAC9C,UAAU,iBAAiB,cAKvC,WAAC+C,EAAAA,EAAYA,CAAAA,WACX,UAACC,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,aAC1B,UAAC5C,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,QAAQoB,KAAK,KAAKtC,UAAU,uBAC1C,UAACkD,EAAAA,CAAcA,CAAAA,CAAClD,UAAU,gBAG9B,WAACmD,EAAAA,EAAmBA,CAAAA,CAACC,MAAM,gBACzB,UAACC,EAAAA,EAAgBA,CAAAA,CAACJ,OAAO,aACvB,WAAC9C,IAAIA,CAACC,KAAM,CAAC,IAARD,uBAAmC,EAAEf,EAAOX,EAAE,CAAC,SAAS,CAAC,WAC5D,UAACmE,EAAAA,CAAKA,CAAAA,CAAC5C,UAAU,iBAAiB,qBAItC,WAACqD,EAAAA,EAAgBA,CAAAA,CAACrD,UAAU,eAAeuC,QAAS,IAAMzD,EAAmBM,EAAOX,EAAE,EAAG6E,SAAUvF,IAAqBqB,EAAOX,EAAE,WAC9HV,IAAqBqB,EAAOX,EAAE,CAAG,UAACsB,MAAAA,CAAIC,UAAU,wFAA2F,UAACuD,EAAAA,CAAMA,CAAAA,CAACvD,UAAU,iBAC7JjC,IAAqBqB,EAAOX,EAAE,CAAG,cAAgB,4BA1ExBW,EAAOX,EAAE,KAoFzB,IAA3BS,EAAgBsE,MAAM,EAAU,WAACzD,MAAAA,CAAIC,UAAU,8BAC5C,UAACiC,EAAAA,CAAQA,CAAAA,CAACjC,UAAU,4CACpB,UAACoC,KAAAA,CAAGpC,UAAU,sCAA6B,qBAC3C,UAACE,IAAAA,CAAEF,UAAU,+DACVxC,EAAa,0EAA6E,0FAE5F,CAACA,GAAc,WAACuC,MAAAA,CAAIC,UAAU,+CAC3B,UAACG,IAAIA,CAACC,KAAK,KAAND,0CACH,WAACE,EAAAA,CAAMA,CAAAA,CAACa,QAAQ,UAAUoB,KAAK,eAC7B,UAACnB,EAAAA,CAAGA,CAAAA,CAACnB,UAAU,iBAAiB,oBAIpC,UAACG,IAAIA,CAACC,KAAK,KAAND,qCACH,WAACE,EAAAA,CAAMA,CAAAA,CAACiC,KAAK,eACX,UAAChC,EAAAA,CAAIA,CAAAA,CAACN,UAAU,iBAAiB,gCAQrD,yBC7QA,+JCEA,SAASa,EAAK,WACZb,CAAS,CACT,GAAGyD,EACyB,EAC5B,MAAO,UAAC1D,MAAAA,CAAI2D,YAAU,OAAO1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqF3D,GAAa,GAAGyD,CAAK,CAAElD,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASmB,EAAW,WAClB3B,CAAS,CACT,GAAGyD,EACyB,EAC5B,MAAO,UAAC1D,MAAAA,CAAI2D,YAAU,cAAc1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8J3D,GAAa,GAAGyD,CAAK,CAAElD,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASoD,EAAU,WACjB5D,CAAS,CACT,GAAGyD,EACyB,EAC5B,MAAO,UAAC1D,MAAAA,CAAI2D,YAAU,aAAa1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8B3D,GAAa,GAAGyD,CAAK,CAAElD,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASqD,EAAgB,WACvB7D,CAAS,CACT,GAAGyD,EACyB,EAC5B,MAAO,UAAC1D,MAAAA,CAAI2D,YAAU,mBAAmB1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiC3D,GAAa,GAAGyD,CAAK,CAAElD,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASO,EAAY,WACnBf,CAAS,CACT,GAAGyD,EACyB,EAC5B,MAAO,UAAC1D,MAAAA,CAAI2D,YAAU,eAAe1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQ3D,GAAa,GAAGyD,CAAK,CAAElD,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASsD,EAAW,WAClB9D,CAAS,CACT,GAAGyD,EACyB,EAC5B,MAAO,UAAC1D,MAAAA,CAAI2D,YAAU,cAAc1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2C3D,GAAa,GAAGyD,CAAK,CAAElD,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,8FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,4ECFlM,SAASuD,EAAc,UACpCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,2CCdA,mECAA,0GCAA,8CCAA,uCAA4K,yBCA5K,0DCmBI,sBAAsB,8sBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,4BAA4B,CAC5C,aAAa,CAAE,MAAM,CACrB,iBAAiB,iBACjB,UACA,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CA/BoBC,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,2BC7DxB,MAAS,cAAiB,UAhBI,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EACxD,CAAC,MAAQ,EAAE,EAAG,CAAkB,oBAAK,SAAU,EACjD,0BCNA,kDCAA,+CCAA,yGCAA,gECAA,kDCAA,gECAA,wDCAA,uDCAA,sDCAA,wDCAA,8CCAA,uCAAqK,wBCArK,uECAA,oDCAA,kECAA,yDCAA,iElBmBI,sBAAsB,gMmBbbC,EAAqB,CAChCC,KAAAA,CAAO,wBACP3E,WAAAA,CAAa,6BACf,EACe,eAAe4E,EAAgB,UAC5CJ,CAAQ,CAGT,CAJ6BI,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkC9C,KAAAA,GAAU,OAChE,MAAOkD,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAKzD,qBAAAA,CAAoB,OAAOV,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAmE,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBL,WAAAA,CAAaA,EAAatD,SAAbsD,YAAatD,CAAoB,kBAAkBT,yBAAAA,CAAwB,uBACvGiE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAW5D,qBAAAA,CAAoB,aAAaT,yBAAAA,CAAwB,eACrEmE,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAa7D,qBAAAA,CAAoB,eAAeT,yBAAAA,CAAwB,uBACvEiE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAO9D,qBAAAA,CAAoB,SAAST,yBAAAA,CAAwB,eAE7DiE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAKhF,SAAAA,CAAU,kDACbgE,QAAAA,CAAAA,WAMb,CnBvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,CAKC,IAAC,EAOF,OAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IoBhF9B,uHpBmBI,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAIH,CALS,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BEC,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,cqBvEtB,GrBgF8B,KqBhF9B,8BAA4K,yBCA5K,qDCAA,4DCAA,wDCAA,0DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,yBCNjN,sDCAA,uDCAA,iDCAA,2DCAA,+ICIA,IAAMgB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRjE,QAAS,CACPkE,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACftE,QAAS,SACX,CACF,GACA,SAASgB,EAAM,CACblC,WAAS,SACTkB,CAAO,SACP+B,GAAU,CAAK,CACf,GAAGQ,EAGJ,EACC,IAAMgC,EAAOxC,EAAUyC,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAK/B,YAAU,QAAQ1D,UAAW2D,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACsB,EAAc,SACzD/D,CACF,GAAIlB,GAAa,GAAGyD,CAAK,CAAExC,sBAAoB,OAAOV,wBAAsB,QAAQC,0BAAwB,aAC9G,mBC7BA,uCAAqK,yBCArK,gDCAA,0DCAA,yVCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAA4K,CAuBhM,2IAES,EACF,CACP,CAGA,EAEA,CAAO,CACP,CACA,QAnCA,IAAsB,uCAAqK,CAmC3L,qIAGA,CACO,CACP,CACA,QA1CA,IAAsB,uCAA4J,CA0ClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QA3DA,IAAsB,sCAAiJ,CA2DvK,gHACA,gBA3DA,IAAsB,uCAAuJ,CA2D7K,sHACA,aA3DA,IAAsB,uCAAoJ,CA2D1K,mHACA,WA3DA,IAAsB,4CAAgF,CA2DtG,+CACA,cA3DA,IAAsB,4CAAmF,CA2DzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,8IAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,uCACA,sCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCtGD,2ECyBM,MAAY,cAAiB,aAtBC,CAClC,CAAC,MAAQ,EAAE,EAAG,CAA8D,gEAAK,SAAU,EAC3F,CACE,OACA,CACE,CAAG,2HACH,GAAK,SACP,EACF,CACF", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/../../../src/icons/copy.ts", "webpack://terang-lms-ui/./src/app/dashboard/teacher/courses/page.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/./src/app/dashboard/teacher/layout.tsx", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/?d8ab", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/../../../src/icons/search.ts", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/?15fa", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/?f2e2", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/./src/components/ui/badge.tsx", "webpack://terang-lms-ui/?69ba", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/?e77f", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/../../../src/icons/square-pen.ts"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '14', height: '14', x: '8', y: '8', rx: '2', ry: '2', key: '17jyea' }],\n  ['path', { d: 'M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2', key: 'zix9uf' }],\n];\n\n/**\n * @component @name Copy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHg9IjgiIHk9IjgiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNCAxNmMtMS4xIDAtMi0uOS0yLTJWNGMwLTEuMS45LTIgMi0yaDEwYzEuMSAwIDIgLjkgMiAyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Copy = createLucideIcon('Copy', __iconNode);\n\nexport default Copy;\n", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { BookOpen, Plus, Search, MoreHorizontal, Edit, Trash2, Users, Bot, Copy } from 'lucide-react';\nimport Link from 'next/link';\nimport { authStorage } from '@/lib/auth';\nimport { toast } from 'sonner';\ninterface Course {\n  id: number;\n  name: string;\n  description: string;\n  type: string;\n  courseCode: string;\n  moduleCount: number;\n  studentCount: number;\n  status: string;\n  createdAt: string;\n  startDate: string;\n  endDate: string;\n  coverPicture?: string;\n}\nexport default function CoursesPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [courses, setCourses] = useState<Course[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isDeletingCourse, setIsDeletingCourse] = useState<number | null>(null);\n  useEffect(() => {\n    fetchCourses();\n  }, []);\n  const fetchCourses = async () => {\n    try {\n      const user = authStorage.getUser();\n      if (!user) {\n        toast.error('Please log in to view courses');\n        return;\n      }\n      const response = await fetch(`/api/courses?teacherId=${user.id}`);\n      const data = await response.json();\n      if (data.success) {\n        setCourses(data.courses || []);\n      } else {\n        toast.error(data.error || 'Failed to fetch courses');\n      }\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      toast.error('Failed to fetch courses');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleDeleteCourse = async (courseId: number) => {\n    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {\n      return;\n    }\n    setIsDeletingCourse(courseId);\n    try {\n      const user = authStorage.getUser();\n      if (!user) {\n        toast.error('Please log in to delete courses');\n        return;\n      }\n      const response = await fetch(`/api/courses/${courseId}?teacherId=${user.id}`, {\n        method: 'DELETE'\n      });\n      const data = await response.json();\n      if (data.success) {\n        toast.success('Course deleted successfully!');\n        fetchCourses();\n      } else {\n        toast.error(data.error || 'Failed to delete course');\n      }\n    } catch (error) {\n      console.error('Error deleting course:', error);\n      toast.error('Failed to delete course');\n    } finally {\n      setIsDeletingCourse(null);\n    }\n  };\n  const filteredCourses = courses.filter(course => course.name.toLowerCase().includes(searchTerm.toLowerCase()) || course.description.toLowerCase().includes(searchTerm.toLowerCase()) || course.courseCode.toLowerCase().includes(searchTerm.toLowerCase()));\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n    toast.success('Course code copied to clipboard!');\n  };\n  const LoadingSkeleton = () => <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' data-sentry-component=\"LoadingSkeleton\" data-sentry-source-file=\"page.tsx\">\r\n      {[...Array(6)].map((_, i) => <Card key={i} className='overflow-hidden'>\r\n          <div className='aspect-video bg-muted'>\r\n            <Skeleton className='w-full h-full' />\r\n          </div>\r\n          <CardContent className='p-4'>\r\n            <Skeleton className='h-6 w-3/4 mb-2' />\r\n            <Skeleton className='h-4 w-full mb-2' />\r\n            <Skeleton className='h-4 w-2/3 mb-4' />\r\n            <div className='flex items-center justify-between'>\r\n              <div className='flex space-x-4'>\r\n                <Skeleton className='h-4 w-16' />\r\n                <Skeleton className='h-4 w-16' />\r\n              </div>\r\n              <Skeleton className='h-8 w-8 rounded-full' />\r\n            </div>\r\n          </CardContent>\r\n        </Card>)}\r\n    </div>;\n  if (isLoading) {\n    return <div className='space-y-6'>\r\n        <div className='flex items-center justify-between'>\r\n          <div>\r\n            <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>\r\n            <p className='text-muted-foreground'>\r\n              Create and manage your educational courses\r\n            </p>\r\n          </div>\r\n          <div className='flex space-x-2'>\r\n            <Link href='/dashboard/teacher/courses/new'>\r\n              <Button>\r\n                <Plus className='mr-2 h-4 w-4' />\r\n                Create Course\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n        <LoadingSkeleton />\r\n      </div>;\n  }\n  return <div className='space-y-6' data-sentry-component=\"CoursesPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>\r\n          <p className='text-muted-foreground'>\r\n            Create and manage your educational courses\r\n          </p>\r\n        </div>\r\n        <div className='flex space-x-2'>\r\n          <Link href='/dashboard/teacher/courses/generate' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n            <Button variant='outline' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n              <Bot className='mr-2 h-4 w-4' data-sentry-element=\"Bot\" data-sentry-source-file=\"page.tsx\" />\r\n              AI Generator\r\n            </Button>\r\n          </Link>\r\n          <Link href='/dashboard/teacher/courses/new' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n            <Button data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n              <Plus className='mr-2 h-4 w-4' data-sentry-element=\"Plus\" data-sentry-source-file=\"page.tsx\" />\r\n              Create Course\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      <div className='space-y-6'>\r\n        <div className='flex items-center space-x-2'>\r\n          <div className='relative flex-1'>\r\n            <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' data-sentry-element=\"Search\" data-sentry-source-file=\"page.tsx\" />\r\n            <Input placeholder='Search courses...' value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className='pl-8' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>\r\n          {filteredCourses.map(course => <Card key={course.id} className='overflow-hidden hover:shadow-lg transition-shadow'>\r\n              <CardHeader className='p-0'>\r\n                {/* Cover Image Section */}\r\n                <div className=\"p-6 pb-0\">\r\n                  <div className=\"h-48 w-full overflow-hidden rounded-lg relative\">\r\n                    {course.coverPicture ? <img src={course.coverPicture} alt={course.name} loading='lazy' className='h-full w-full object-cover' /> : <div className='h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>\r\n                        <BookOpen className='h-12 w-12 text-gray-400' />\r\n                      </div>}\r\n                    <div className='absolute top-2 right-2'>\r\n                      <Badge variant={course.status === 'published' ? 'default' : 'outline'}>\r\n                        {course.status}\r\n                      </Badge>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className='p-6 pt-4'>\r\n                <div className='space-y-3'>\r\n                  <div>\r\n                    <h3 className='font-semibold text-lg line-clamp-1'>{course.name}</h3>\r\n                    <p className='text-muted-foreground text-sm line-clamp-2 mt-1'>\r\n                      {course.description}\r\n                    </p>\r\n                  </div>\r\n                  \r\n                  <div className='flex items-center justify-between text-sm'>\r\n                    <div className='flex items-center space-x-2'>\r\n                      <code className='bg-muted rounded px-2 py-1 text-xs'>\r\n                        {course.courseCode}\r\n                      </code>\r\n                      <Button variant='ghost' size='sm' onClick={() => copyToClipboard(course.courseCode)} className='h-6 w-6 p-0'>\r\n                        <Copy className='h-3 w-3' />\r\n                      </Button>\r\n                    </div>\r\n                    <Badge variant={course.type === 'verified' ? 'default' : 'secondary'}>\r\n                      {course.type}\r\n                    </Badge>\r\n                  </div>\r\n                  \r\n                  <div className='flex items-center justify-between text-sm text-muted-foreground'>\r\n                    <div className='flex items-center space-x-1'>\r\n                      <BookOpen className='h-4 w-4' />\r\n                      <span>{course.moduleCount} modules</span>\r\n                    </div>\r\n                    <div className='flex items-center space-x-1'>\r\n                      <Users className='h-4 w-4' />\r\n                      <span>{course.studentCount} students</span>\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div className='flex items-center justify-between pt-2'>\r\n                    <div className='flex space-x-1'>\r\n                      <Link href={`/dashboard/teacher/courses/${course.id}`}>\r\n                        <Button variant='outline' size='sm'>\r\n                          <Edit className='h-3 w-3 mr-1' />\r\n                          Edit\r\n                        </Button>\r\n                      </Link>\r\n                    </div>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>\r\n                          <MoreHorizontal className='h-4 w-4' />\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align='end'>\r\n                        <DropdownMenuItem asChild>\r\n                          <Link href={`/dashboard/teacher/courses/${course.id}/students`}>\r\n                            <Users className='mr-2 h-4 w-4' />\r\n                            View Students\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem className='text-red-600' onClick={() => handleDeleteCourse(course.id)} disabled={isDeletingCourse === course.id}>\r\n                          {isDeletingCourse === course.id ? <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent' /> : <Trash2 className='mr-2 h-4 w-4' />}\r\n                          {isDeletingCourse === course.id ? 'Deleting...' : 'Delete'}\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>)}\r\n        </div>\r\n\r\n        {filteredCourses.length === 0 && <div className='py-16 text-center'>\r\n            <BookOpen className='text-muted-foreground mx-auto h-16 w-16' />\r\n            <h3 className='mt-4 text-lg font-semibold'>No courses found</h3>\r\n            <p className='text-muted-foreground mt-2 text-sm max-w-sm mx-auto'>\r\n              {searchTerm ? 'Try adjusting your search terms to find the courses you\\'re looking for.' : 'Get started by creating your first course using our intuitive wizard or AI generator.'}\r\n            </p>\r\n            {!searchTerm && <div className='mt-8 flex justify-center space-x-3'>\r\n                <Link href='/dashboard/teacher/courses/generate'>\r\n                  <Button variant='outline' size='lg'>\r\n                    <Bot className='mr-2 h-4 w-4' />\r\n                    AI Generator\r\n                  </Button>\r\n                </Link>\r\n                <Link href='/dashboard/teacher/courses/new'>\r\n                  <Button size='lg'>\r\n                    <Plus className='mr-2 h-4 w-4' />\r\n                    Create Course\r\n                  </Button>\r\n                </Link>\r\n              </div>}\r\n          </div>}\r\n      </div>\r\n    </div>;\n}", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function TeacherLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has teacher role\n    requireRole('teacher');\n  }, []);\n  return <>{children}</>;\n}", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\page.tsx\");\n", "module.exports = require(\"os\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/teacher/courses',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/teacher/courses',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/teacher/courses',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/teacher/courses',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('Search', __iconNode);\n\nexport default Search;\n", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\page.tsx\");\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'teacher',\n        {\n        children: [\n        'courses',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\courses\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/teacher/courses/page\",\n        pathname: \"/dashboard/teacher/courses\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"events\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('SquarePen', __iconNode);\n\nexport default SquarePen;\n"], "names": ["CoursesPage", "searchTerm", "setSearchTerm", "useState", "courses", "setCourses", "isLoading", "setIsLoading", "isDeletingCourse", "setIsDeletingCourse", "fetchCourses", "user", "authStorage", "getUser", "toast", "error", "response", "fetch", "id", "data", "json", "success", "console", "handleDeleteCourse", "courseId", "confirm", "method", "filteredCourses", "filter", "course", "name", "toLowerCase", "includes", "description", "courseCode", "copyToClipboard", "navigator", "clipboard", "writeText", "text", "div", "className", "h1", "p", "Link", "href", "<PERSON><PERSON>", "Plus", "data-sentry-component", "data-sentry-source-file", "Array", "map", "_", "i", "Card", "Skeleton", "<PERSON><PERSON><PERSON><PERSON>", "LoadingSkeleton", "data-sentry-element", "variant", "Bot", "Search", "Input", "placeholder", "value", "onChange", "e", "target", "<PERSON><PERSON><PERSON><PERSON>", "coverPicture", "img", "src", "alt", "loading", "BookOpen", "Badge", "status", "h3", "code", "size", "onClick", "Copy", "type", "span", "moduleCount", "Users", "studentCount", "Edit", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "MoreHorizontal", "DropdownMenuContent", "align", "DropdownMenuItem", "disabled", "Trash2", "length", "props", "data-slot", "cn", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON>er", "TeacherLayout", "children", "serverComponentModule.default", "metadata", "title", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "badgeVariants", "cva", "variants", "default", "secondary", "destructive", "outline", "defaultVariants", "Comp", "Slot"], "sourceRoot": ""}