{"version": 3, "file": "../app/api/institutions/[id]/route.js", "mappings": "2dAEA,GAAI,CAACA,QAAQC,GAAG,CAACC,YAAY,CAC3B,CAD6B,KACvB,MAAU,iDAGlB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAACJ,QAAQC,GAAG,CAACC,YAAY,EAElC,eAAeG,EAAMC,CAA0B,CAAE,GAAGC,CAAa,EAEtE,OAAOC,MADcL,EAAIG,KAASC,EAEpC,wBCXA,8GCAA,oDCAA,oGCAA,oECAA,qDCAA,gDCAA,kDCAA,gDCAA,wGCAA,+DCAA,mDCAA,iECAA,uDCAA,uDCAA,2WCKO,eAAeE,EACpBC,CAAoB,CACpB,CAFoBD,OAElBF,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEI,EAAAA,CAAIC,CAAO,CAAE,CAAG,MAAML,EACxBI,EAAKE,EADmBN,MACnBM,CAASD,GAEpB,GAAIE,CAFgBF,CAAAA,IAEVD,EAAAA,CAAAA,EAAK,KACNI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,yBAAyB,CAClD,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,IAAMX,EAAS,IAATA,EAAeH,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;;iBAcd,EAAEM,EAAAA;IACf,CAAC,CAED,GAAsB,GAAG,CAArBH,EAAOY,IAAPZ,EAAa,CACf,OAAOO,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTI,IAAAA,CAAMb,CAAM,CAAC,EAAE,CACfc,OAAAA,CAAS,oCACX,EACF,CAAE,MAAOJ,EAAO,CAEd,EAFOA,KACPK,OAAAA,CAAQL,KAAK,CAAC,yBAA0BA,GACjCH,EAAAA,CADiCG,WACjCH,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CAAO,iCACT,CACA,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeK,EACpBd,CAAoB,CACpB,CAFoBc,OAElBjB,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEI,EAAAA,CAAIC,CAAO,CAAE,CAAG,MAAML,EACxBI,EAAKE,EADmBN,MACnBM,CAASD,GACda,EAAO,EADOb,CAAAA,GACDF,EAAQM,IAAI,CAAZN,EAEnB,GAAII,MAAMH,EAAAA,CAAAA,EAAK,KACNI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,yBAAyB,CAClD,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,GAAM,CACJO,MAAI,MACJC,CAAI,kBACJC,CAAgB,cAChBC,CAAY,cACZC,CAAY,cACZC,CAAY,eACZC,CAAa,gBACbC,CAAc,CACf,CAAGR,EAGES,EAHFT,MAGyBpB,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;6CACM,EAAEM,EAAAA;IAC3C,CAAC,CAED,GAA8B,GAAG,CAA7BuB,EAAed,MAAM,CACvB,KADEc,EACKnB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMX,EAAS,IAATA,EAAeH,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;wBAGP,EAAEqB,EAAK,EAALA;wBACF,EAAEC,EAAK,EAALA;qCACW,EAAEC,EAAiB,cAAjBA;iCACN,EAAEC,EAAa,UAAbA;iCACF,EAAEC,EAAa,UAAbA;iCACF,EAAEC,EAAa,UAAbA;kCACD,EAAEC,EAAc,WAAdA;oCACA,EAAEC,EAAe,YAAfA;;iBAErB,EAAEtB,EAAG;;IAElB,CAAC,CAED,OAAOI,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTI,IAAAA,CAAMb,CAAM,CAAC,EAAE,CACfc,OAAAA,CAAS,kCACX,EACF,CAAE,MAAOJ,EAAO,CAEd,EAFOA,KACPK,OAAAA,CAAQL,KAAK,CAAC,4BAA6BA,GACpCH,EADoCG,CAAAA,WACpCH,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,+BAA+B,CACxD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAegB,EACpBC,CAAqB,CACrB,IAFoBD,IAElB5B,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEI,EAAAA,CAAIC,CAAO,CAAE,CAAG,MAAML,EACxBI,EAAKE,EADmBN,MACnBM,CAASD,GAEpB,GAAIE,CAFgBF,CAAAA,IAEVD,EAAAA,CAAAA,EAAK,KACNI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,yBAAyB,CAClD,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMe,EAAiB,MAAM7B,CAAAA,EAAAA,EAAAA,CAAvB6B,CAA4B,CAAC;6CACM,EAAEvB,EAAAA;IAC3C,CAAC,CAED,GAA8B,GAAG,CAA7BuB,EAAed,MAAM,CACvB,KADEc,EACKnB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,GAYlB,IAAMkB,EAAeC,CAPM,MAAMjC,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;2DAEgB,EAAEM,EAAAA;6DACA,EAAEA,EAAAA;6DACF,EAAEA,EAAAA;KAC3D,CAEuC,CAAC,EAAE,CAC1C,GACE0B,EAAaE,UAAbF,CAA0B,GAC1BA,EAAaG,UAAbH,CAAwB,CAAG,GAC3BA,EAAaI,UAAbJ,EAAyB,CAAG,EAE5B,CADA,MACOtB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTC,KAAAA,CACE,gGACJ,CACA,CAAEC,MAAAA,CAAQ,GAAI,GAQlB,OAJA,MAAMd,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;0CAC0B,EAAEM,EAAAA;IACxC,CAAC,CAEMI,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTK,OAAAA,CAAS,kCACX,EACF,CAAE,MAAOJ,EAAO,CAEd,EAFOA,KACPK,OAAAA,CAAQL,KAAK,CAAC,4BAA6BA,GACpCH,EAAAA,CADoCG,WACpCH,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,+BAA+B,CACxD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC9LA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KACT,CAAC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,wBAAwB,SAC5C,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAIC,IAAC,EAAM,CAAH,CAAeuB,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAAf,MAA2C,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,oCACA,kCACA,iBACA,4CACA,CAAK,CACL,uJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,0BC5BA,2CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,4GCAA,sDCAA,4DCAA,wDCAA,iECAA,uDCAA,mECAA,iDCAA,2DCAA,2DCAA,iDCAA,wDCAA,6DCAA", "sources": ["webpack://terang-lms-ui/./src/lib/db/raw.ts", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/src/app/api/institutions/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?2053", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nconst sql = neon(process.env.DATABASE_URL);\r\n\r\nexport async function query(text: TemplateStringsArray, ...params: any[]) {\r\n  const result = await sql(text, ...params);\r\n  return result;\r\n}\r\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { query } from '@/lib/db/raw';\r\nimport { ApiResponse } from '@/types/database';\r\n\r\n// GET /api/institutions/[id] - Get single institution\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id: idParam } = await params;\r\n    const id = parseInt(idParam);\r\n\r\n    if (isNaN(id)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid institution ID' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const result = await query`\r\n      SELECT \r\n        id,\r\n        name,\r\n        type,\r\n        subscription_plan,\r\n        billing_cycle,\r\n        payment_status,\r\n        payment_due_date,\r\n        student_count,\r\n        teacher_count,\r\n        created_at,\r\n        updated_at\r\n      FROM institutions\r\n      WHERE id = ${id}\r\n    `;\r\n\r\n    if (result.length === 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Institution not found' } as ApiResponse,\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: result[0],\r\n      message: 'Institution retrieved successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Get institution error:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to retrieve institution'\r\n      } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/institutions/[id] - Update institution\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id: idParam } = await params;\r\n    const id = parseInt(idParam);\r\n    const body = await request.json();\r\n\r\n    if (isNaN(id)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid institution ID' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const {\r\n      name,\r\n      type,\r\n      subscriptionPlan,\r\n      billingCycle,\r\n      studentCount,\r\n      teacherCount,\r\n      paymentStatus,\r\n      paymentDueDate\r\n    } = body;\r\n\r\n    // Check if institution exists\r\n    const existingResult = await query`\r\n      SELECT id FROM institutions WHERE id = ${id}\r\n    `;\r\n\r\n    if (existingResult.length === 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Institution not found' } as ApiResponse,\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Update institution with provided fields\r\n    const result = await query`\r\n      UPDATE institutions\r\n      SET\r\n        name = COALESCE(${name}, name),\r\n        type = COALESCE(${type}, type),\r\n        subscription_plan = COALESCE(${subscriptionPlan}, subscription_plan),\r\n        billing_cycle = COALESCE(${billingCycle}, billing_cycle),\r\n        student_count = COALESCE(${studentCount}, student_count),\r\n        teacher_count = COALESCE(${teacherCount}, teacher_count),\r\n        payment_status = COALESCE(${paymentStatus}, payment_status),\r\n        payment_due_date = COALESCE(${paymentDueDate}, payment_due_date),\r\n        updated_at = NOW()\r\n      WHERE id = ${id}\r\n      RETURNING *\r\n    `;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: result[0],\r\n      message: 'Institution updated successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Update institution error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Failed to update institution' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/institutions/[id] - Delete institution\r\nexport async function DELETE(\r\n  _request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id: idParam } = await params;\r\n    const id = parseInt(idParam);\r\n\r\n    if (isNaN(id)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid institution ID' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if institution exists\r\n    const existingResult = await query`\r\n      SELECT id FROM institutions WHERE id = ${id}\r\n    `;\r\n\r\n    if (existingResult.length === 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Institution not found' } as ApiResponse,\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Check if institution has users or classes\r\n    const dependenciesResult = await query`\r\n      SELECT \r\n        (SELECT COUNT(*) FROM users WHERE institution_id = ${id}) as user_count,\r\n        (SELECT COUNT(*) FROM classes WHERE institution_id = ${id}) as class_count,\r\n        (SELECT COUNT(*) FROM courses WHERE institution_id = ${id}) as course_count\r\n    `;\r\n\r\n    const dependencies = dependenciesResult[0];\r\n    if (\r\n      dependencies.user_count > 0 ||\r\n      dependencies.class_count > 0 ||\r\n      dependencies.course_count > 0\r\n    ) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error:\r\n            'Cannot delete institution with existing users, classes, or courses. Please remove them first.'\r\n        } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    await query`\r\n      DELETE FROM institutions WHERE id = ${id}\r\n    `;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Institution deleted successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Delete institution error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Failed to delete institution' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/institutions/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\institutions\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/institutions/[id]/route\",\n        pathname: \"/api/institutions/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/institutions/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\institutions\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["process", "env", "DATABASE_URL", "sql", "neon", "query", "text", "params", "result", "GET", "request", "id", "idParam", "parseInt", "isNaN", "NextResponse", "json", "success", "error", "status", "length", "data", "message", "console", "PUT", "body", "name", "type", "subscriptionPlan", "billingCycle", "studentCount", "teacherCount", "paymentStatus", "paymentDueDate", "existingResult", "DELETE", "_request", "dependencies", "dependenciesResult", "user_count", "class_count", "course_count", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}