{"version": 3, "file": "../app/api/subscriptions/route.js", "mappings": "ubAAA,iaCGA,GAAI,CAACA,OAAAA,CAAQC,GAAG,CAACC,YAAY,CAC3B,CAD6B,KACnBC,KAAAA,CAAM,iDAGlB,IAAMC,EAAMC,CAAND,EAAMC,EAAAA,EAAAA,CAAAA,CAAKL,OAAAA,CAAQC,GAAG,CAACC,YAAY,EASlC,eAAeI,EAAIC,CAAoB,EAC5C,GAAI,CACF,IA+BIC,EAAoBC,EA/BlB,SA+BkBA,KA/BhBC,CAAY,CAAE,CAAG,IAAIC,GAAAA,CAAIJ,EAAQK,GAAG,EACtCC,EAASH,EAAaI,EAAtBD,CAAyB,CAAC,MAAjBH,KAA8B,GACvCK,EAASL,EAAaI,EAAtBC,CAAyB,CAAC,MAAjBL,KAA8B,MACvCM,EAAON,EAAPM,GAAuB,CAAC,MAAjBN,GAA4B,MACnCO,EAAQC,GAARD,KAAQC,CAASR,EAAaI,GAAG,CAAC,MAAjBJ,IAA6B,MAC9CS,EAASD,IAATC,IAASD,CAASR,EAAaI,GAAG,CAAC,MAAjBJ,KAA8B,KAGhDU,EAAa,EAAE,CACfC,EAAS,EAAE,CACbC,EAAa,EAEbT,IACFO,EAHEE,EAEQ,EACK,CAAC,CAAC,EAAjBF,aAAgC,EAAEE,EAAW,QAAXA,UAA6B,EAAEA,EAAa,EAAE,CAAC,CAAC,EAClFD,EADiEC,IACjED,CAAY,CAAC,CAAC,EAAER,EAAO,CAAC,CAAC,CAAE,CAAXA,CAAa,EAAEA,EAAO,CAAC,CAAC,EAATA,GACjB,GAGD,IAHbS,GAGoB,CAAlBP,IACFK,EAAWG,IAAI,CAAC,CAAC,EAAjBH,kBAAqC,EAAEE,EAAAA,CAAY,EACnDD,EAAOE,GADgCD,CACvCD,CAAYN,GACZO,GADYP,CAAAA,CAID,KAHXO,EAGkB,CAAhBN,IACFI,EAAWG,IAAI,CAAC,CAAC,EAAjBH,qBAAwC,EAAEE,EAAAA,CAAY,EACtDD,EAAOE,GADmCD,CAC1CD,CAAYL,GACZM,CADYN,CAAAA,GAOVH,GAAqB,EANvBS,CAMET,CANFS,IAMYP,GAA6B,GAA7BA,IAAoC,CAAhBC,EAEhC,CAACR,CAF+BQ,CAEXP,EAAY,CAAG,MAAMe,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;8BASkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;mCAClC,EAAEE,EAAO,IAAPA;sCACC,EAAEC,EAAK,EAALA;;;;;;gBAMxB,EAAEC,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;8BAIkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;mCAClC,EAAEE,EAAO,IAAPA;sCACC,EAAEC,EAAK,EAALA;QAChC,EACD,EACQH,GAAqB,GAArBA,IAA4B,CAAlBE,EAEnB,CAACP,EAAoBC,CAFFM,CAEc,CAAG,MAAMS,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;8BASkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;mCAClC,EAAEE,EAAO,IAAPA;;;;;;gBAMrB,EAAEE,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;8BAIkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;mCAClC,EAAEE,EAAO,IAAPA;QAC7B,EACD,EACQF,GAAmB,GAAnBA,IAA0B,CAAhBG,EAEnB,CAACR,CAFkBQ,CAEEP,EAAY,CAAG,MAAMe,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;8BASkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;sCAC/B,EAAEG,EAAK,EAALA;;;;;;gBAMxB,EAAEC,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;8BAIkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;sCAC/B,EAAEG,EAAK,EAALA;QAChC,EACD,EACmB,QAAXD,GAA6B,GAA7BA,IAAoC,CAAhBC,EAE7B,CAACR,CAF4BQ,CAERP,EAAY,CAAG,MAAMe,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;mCASuB,EAAEW,EAAO,IAAPA;sCACC,EAAEC,EAAK,EAALA;;;;;;gBAMxB,EAAEC,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;mCAIuB,EAAEW,EAAO,IAAPA;sCACC,EAAEC,EAAK,EAALA;QAChC,EACD,EACQH,EAET,CAACL,EAAoBC,CAFZI,CAEwB,CAAG,MAAMW,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;8BASkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;;;;;;gBAMrD,EAAEI,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;8BAIkB,EAAE,CAAC,CAAC,EAAES,EAAO,CAAC,CAAC,CAAC,CAAVA,gBAA2B,EAAE,CAAC,CAAC,EAAEA,EAAO,CAAC,CAAC,CAAC,CAAVA;QAC7D,EACD,EACmB,OAAO,CAAlBE,EAET,CAACP,EAAoBC,EAAY,CAAG,MAAMe,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;mCASuB,EAAEW,EAAO,IAAPA;;;;;;gBAMrB,EAAEE,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;mCAIuB,EAAEW,EAAO,IAAPA;QAC7B,EACD,EACQC,OAAgB,GAEzB,CAACR,EAAoBC,EAAY,CAAG,MAAMe,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;sCAS0B,EAAEY,EAAK,EAALA;;;;;;gBAMxB,EAAEC,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;sCAI0B,EAAEY,EAAK,EAALA;QAChC,EACD,EAGD,CAACR,EAAoBC,EAAY,CAAG,MAAMe,EAArBf,KAAqBe,CAAQC,GAAG,CAAC,CACpDrB,CAAG,CAAC;;;;;;;;;;;;;;gBAcI,EAAEa,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CACDf,CAAG,CAAC;;;;QAIJ,EACD,EAGH,IAAMsB,EAAelB,EACfmB,EAAQT,MADRQ,EACQR,CAAST,CAAW,CAAC,EAAE,CADhBD,CACkBmB,KAAAA,EAAS,KAa1CC,EAAUC,CAVM,IAUhBD,EAVsBxB,CAAG,CAAC;;;;;;;;;IAShC,CAAC,CAC4B,CAAC,EAAE,CAE1B0B,EAAwB,CAC5BC,KADID,EACJC,EAAS,EACTC,IAAAA,CAAM,cACJN,QACAC,EACAC,EAFAF,CACAC,IACAC,CAAS,CACPK,iBAAAA,CAAmBf,QAAAA,CAASU,EAAQM,KAARN,aAA0B,EAAI,KAC1DO,gBAAAA,CAAkBjB,QAAAA,CAASU,EAAQQ,KAARR,YAAyB,EAAI,KACxDS,kBAAAA,CAAoBnB,QAAAA,CAASU,EAAQU,KAARV,cAA2B,EAAI,KAC5DW,mBAAAA,CAAqBrB,QAAAA,CAASU,EAAQY,KAARZ,eAA4B,EAAI,KAC9Da,aAAAA,CAAevB,QAAAA,CAASU,EAAQc,KAARd,SAAsB,EAAI,KAClDe,aAAAA,CAAezB,QAAAA,CAASU,EAAQgB,KAARhB,SAAsB,EAAI,IACpD,CACF,CACF,EAEA,OAAOiB,EAAAA,YAAAA,CAAaC,IAAI,CAAChB,EAC3B,CAAE,KADyBA,CAAAA,EACX,CAMd,EANOiB,KACPC,OAAAA,CAAQD,KAAK,CAAC,gCAAiCA,GAKxCF,EALwCE,CAAAA,WAKxCF,CAAaC,IAAI,CAJM,CAC5Bf,OAAAA,EAAS,EACTgB,KAAAA,CAAO,mCACT,EACmC,CAAEhC,MAAAA,CAAQ,GAAI,EACnD,CACF,CAGO,eAAekC,EAAI1C,CAAoB,EAC5C,GAAI,CAEF,IA6BI2C,EA7BE,IA6BFA,YA7BIC,CAAc,eAAEC,CAAa,gBAAEC,CAAc,CAAE,CAD1C,EAC6CC,IAAAA,EAD/BR,IAAI,CAAZvC,EAInB,GAAI,CAAC4C,GAAkB,CAACI,KAAAA,CAAMC,IAAzBL,GAAgC,CAACA,IAA6C,GAAG,CAA7BA,EAAeM,IAAlCN,CAAAA,CAAwC,CAK5E,KALuDA,EAKhDN,EAAAA,YAAAA,CAAaC,IAAI,CAJM,CAC5Bf,OAGuBD,EAHd,EACTiB,KAAAA,CAAO,mDACT,EACmC,CAAEhC,MAAAA,CAAQ,GAAI,GAGnD,GAAI,CAACqC,GAAiB,CAAC,CAAC,OAAQ,SAAS,CAACM,QAAQ,CAACN,GAKjD,OAAOP,EAAAA,CAL0CO,CAAAA,EAAgB,QAK1DP,CAAaC,IAAI,CAAChB,CAHvBC,OAAAA,EAAS,EACTgB,KAAAA,CAAO,mDACT,EACmC,CAAEhC,MAAAA,CAAQ,GAAI,GAInD,IAAM4C,EAAWR,EAAeS,GAAG,CAA7BD,EAA8BE,EAClC,CADkCA,GAAnBV,EACAjC,QAAAA,CAAS2C,EAAAA,CACxB,GAAIC,MAAMC,GACR,GADQA,CAAAA,EACF,KAAI5D,CAAM,CAAC,wBAAwB,EAAE0D,EAAAA,CAAI,EAEjD,OAAOE,CACT,GAiCA,EAlCSA,CAOPb,EADEG,EACO,EAATH,IAAe9C,CAAG,CAAC,IADjBiD,EAAgB;;;2BAIG,EAAED,EAAc,WAAdA;6BACA,EAAEC,EAAe,YAAfA;;uBAER,EAAEM,EAAS,MAATA;MACnB,CAAC,CAC0B,QAAQ,CAA1BP,EACA,MAAMhD,CAAG,CAAC;;;2BAGE,EAAEgD,EAAc,WAAdA;;;uBAGN,EAAEO,EAAS,MAATA;MACnB,CAAC,CAEQ,MAAMvD,CAAG,CAAC;;;2BAGE,EAAEgD,EAAc,WAAdA;;uBAEN,EAAEO,EAAS,MAATA;MACnB,CAAC,CAGmB,GAAG,CAArBT,EAAOO,IAAPP,EAAa,CAKf,OAAOL,EAAAA,YAAAA,CAAaC,IAAI,CAAChB,CAHvBC,OAAAA,EAAS,EACTgB,KAAAA,CAAO,6CACT,EACmC,CAAEhC,MAAAA,CAAQ,GAAI,GAGnD,IAAMe,EAAwB,CAC5BC,KADID,EACJC,EAAS,EACTC,IAAAA,CAAM,CACJgC,OAAAA,CAAS,CAAC,qBAAqB,EAAEL,EAASF,MAAM,CAAC,eAAe,CAAC,CACjEQ,YAAAA,CAAcN,EAASF,MAAAA,CAE3B,EAEA,OAAOZ,EAAAA,YAAAA,CAAaC,IAAI,CAAChB,EAC3B,CAAE,KADyBA,CAAAA,EACX,CACdkB,EADOD,KACPC,CAAQD,KAAK,CAAC,sCAAuCA,GACrD,EADqDA,CAAAA,CAC/CjB,EAAwB,CAC5BC,KADID,EACJC,EAAS,EACTgB,KAAAA,CAAOA,KAAAA,QAAiB5C,KAAAA,CAAQ4C,EAAMiB,GAANjB,IAAa,CAAG,sCAClD,EACA,OAAOF,EAAAA,YAAAA,CAAaC,IAAI,CAAChB,EAAU,CAAEf,KAAZe,CAAYf,CAAQ,GAAI,EACnD,CACF,CC/YA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAIH,EAAoB,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,oBAAoB,SACxC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAIC,IAAC,EAAM,CAAH,CAAemD,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAAsC,EAEzD,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,OAA4C,EAA9B,IAAoC,EAEtD,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,gCACA,8BACA,iBACA,wCACA,CAAK,CACL,kJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,yBC5BA,oDCAA,qGCAA,mECAA,qDCAA,gDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,2CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,6GCAA,qDCAA,4DCAA,uDCAA,kECAA,sDCAA,oECAA,iDCAA,2DCAA,2DCAA,gDCAA,0DCAA,4DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/subscriptions/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?d979", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { neon } from '@neondatabase/serverless';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nconst sql = neon(process.env.DATABASE_URL);\r\n\r\ninterface ApiResponse<T = any> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n}\r\n\r\n// GET /api/subscriptions - Get all institutions with billing information\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const { searchParams } = new URL(request.url);\r\n    const search = searchParams.get('search') || '';\r\n    const status = searchParams.get('status') || 'all';\r\n    const plan = searchParams.get('plan') || 'all';\r\n    const limit = parseInt(searchParams.get('limit') || '50');\r\n    const offset = parseInt(searchParams.get('offset') || '0');\r\n\r\n    // Build parameterized query conditions\r\n    const conditions = [];\r\n    const params = [];\r\n    let paramIndex = 1;\r\n\r\n    if (search) {\r\n      conditions.push(`(i.name ILIKE $${paramIndex} OR i.type ILIKE $${paramIndex + 1})`);\r\n      params.push(`%${search}%`, `%${search}%`);\r\n      paramIndex += 2;\r\n    }\r\n    \r\n    if (status !== 'all') {\r\n      conditions.push(`i.payment_status = $${paramIndex}`);\r\n      params.push(status);\r\n      paramIndex++;\r\n    }\r\n    \r\n    if (plan !== 'all') {\r\n      conditions.push(`i.subscription_plan = $${paramIndex}`);\r\n      params.push(plan);\r\n      paramIndex++;\r\n    }\r\n\r\n    // Execute queries based on filters\r\n    let institutionsResult, countResult;\r\n    \r\n    if (search && status !== 'all' && plan !== 'all') {\r\n      // All three filters\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n            AND i.payment_status = ${status}\r\n            AND i.subscription_plan = ${plan}\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n            AND i.payment_status = ${status}\r\n            AND i.subscription_plan = ${plan}\r\n        `\r\n      ]);\r\n    } else if (search && status !== 'all') {\r\n      // Search and status filters\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n            AND i.payment_status = ${status}\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n            AND i.payment_status = ${status}\r\n        `\r\n      ]);\r\n    } else if (search && plan !== 'all') {\r\n      // Search and plan filters\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n            AND i.subscription_plan = ${plan}\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n            AND i.subscription_plan = ${plan}\r\n        `\r\n      ]);\r\n    } else if (status !== 'all' && plan !== 'all') {\r\n      // Status and plan filters\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE i.payment_status = ${status}\r\n            AND i.subscription_plan = ${plan}\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE i.payment_status = ${status}\r\n            AND i.subscription_plan = ${plan}\r\n        `\r\n      ]);\r\n    } else if (search) {\r\n      // Search filter only\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})\r\n        `\r\n      ]);\r\n    } else if (status !== 'all') {\r\n      // Status filter only\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE i.payment_status = ${status}\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE i.payment_status = ${status}\r\n        `\r\n      ]);\r\n    } else if (plan !== 'all') {\r\n      // Plan filter only\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE i.subscription_plan = ${plan}\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          WHERE i.subscription_plan = ${plan}\r\n        `\r\n      ]);\r\n    } else {\r\n      // No filters\r\n      [institutionsResult, countResult] = await Promise.all([\r\n        sql`\r\n          SELECT \r\n            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,\r\n            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,\r\n            i.created_at, i.updated_at,\r\n            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,\r\n            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, \r\n                   i.payment_status, i.payment_due_date, i.student_count, \r\n                   i.teacher_count, i.created_at, i.updated_at\r\n          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,\r\n                   i.payment_due_date ASC NULLS LAST, i.name ASC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `,\r\n        sql`\r\n          SELECT COUNT(DISTINCT i.id) as total\r\n          FROM institutions i\r\n          LEFT JOIN users u ON u.institution_id = i.id\r\n        `\r\n      ]);\r\n    }\r\n\r\n    const institutions = institutionsResult;\r\n    const total = parseInt(countResult[0]?.total || '0');\r\n\r\n    // Calculate billing summary\r\n    const summaryResult = await sql`\r\n      SELECT \r\n        COUNT(*) as total_institutions,\r\n        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_institutions,\r\n        COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_institutions,\r\n        COUNT(CASE WHEN payment_status = 'unpaid' AND payment_due_date < NOW() THEN 1 END) as overdue_institutions,\r\n        SUM(student_count) as total_students,\r\n        SUM(teacher_count) as total_teachers\r\n      FROM institutions\r\n    `;\r\n    const summary = summaryResult[0];\r\n\r\n    const response: ApiResponse = {\r\n      success: true,\r\n      data: {\r\n        institutions,\r\n        total,\r\n        summary: {\r\n          totalInstitutions: parseInt(summary.total_institutions || '0'),\r\n          paidInstitutions: parseInt(summary.paid_institutions || '0'),\r\n          unpaidInstitutions: parseInt(summary.unpaid_institutions || '0'),\r\n          overdueInstitutions: parseInt(summary.overdue_institutions || '0'),\r\n          totalStudents: parseInt(summary.total_students || '0'),\r\n          totalTeachers: parseInt(summary.total_teachers || '0')\r\n        }\r\n      }\r\n    };\r\n\r\n    return NextResponse.json(response);\r\n  } catch (error) {\r\n    console.error('Error fetching subscriptions:', error);\r\n    const response: ApiResponse = {\r\n      success: false,\r\n      error: 'Failed to fetch subscription data'\r\n    };\r\n    return NextResponse.json(response, { status: 500 });\r\n  }\r\n}\r\n\r\n// PUT /api/subscriptions - Update payment status for multiple institutions\r\nexport async function PUT(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const { institutionIds, paymentStatus, paymentDueDate } = body;\r\n\r\n    // Validation\r\n    if (!institutionIds || !Array.isArray(institutionIds) || institutionIds.length === 0) {\r\n      const response: ApiResponse = {\r\n        success: false,\r\n        error: 'Institution IDs are required and must be an array'\r\n      };\r\n      return NextResponse.json(response, { status: 400 });\r\n    }\r\n\r\n    if (!paymentStatus || !['paid', 'unpaid'].includes(paymentStatus)) {\r\n      const response: ApiResponse = {\r\n        success: false,\r\n        error: 'Valid payment status is required (paid or unpaid)'\r\n      };\r\n      return NextResponse.json(response, { status: 400 });\r\n    }\r\n\r\n    // Validate and sanitize institution IDs\r\n    const validIds = institutionIds.map(id => {\r\n      const parsed = parseInt(id);\r\n      if (isNaN(parsed)) {\r\n        throw new Error(`Invalid institution ID: ${id}`);\r\n      }\r\n      return parsed;\r\n    });\r\n\r\n    // Update payment status for multiple institutions\r\n    let result;\r\n    \r\n    if (paymentDueDate) {\r\n      result = await sql`\r\n        UPDATE institutions \r\n        SET \r\n          payment_status = ${paymentStatus},\r\n          payment_due_date = ${paymentDueDate},\r\n          updated_at = NOW()\r\n        WHERE id = ANY(${validIds})\r\n      `;\r\n    } else if (paymentStatus === 'paid') {\r\n      result = await sql`\r\n        UPDATE institutions \r\n        SET \r\n          payment_status = ${paymentStatus},\r\n          payment_due_date = NULL,\r\n          updated_at = NOW()\r\n        WHERE id = ANY(${validIds})\r\n      `;\r\n    } else {\r\n      result = await sql`\r\n        UPDATE institutions \r\n        SET \r\n          payment_status = ${paymentStatus},\r\n          updated_at = NOW()\r\n        WHERE id = ANY(${validIds})\r\n      `;\r\n    }\r\n\r\n    if (result.length === 0) {\r\n      const response: ApiResponse = {\r\n        success: false,\r\n        error: 'No institutions found with the provided IDs'\r\n      };\r\n      return NextResponse.json(response, { status: 404 });\r\n    }\r\n\r\n    const response: ApiResponse = {\r\n      success: true,\r\n      data: {\r\n        message: `Successfully updated ${validIds.length} institution(s)`,\r\n        updatedCount: validIds.length\r\n      }\r\n    };\r\n\r\n    return NextResponse.json(response);\r\n  } catch (error) {\r\n    console.error('Error updating subscription status:', error);\r\n    const response: ApiResponse = {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : 'Failed to update subscription status'\r\n    };\r\n    return NextResponse.json(response, { status: 500 });\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/subscriptions',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\subscriptions\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/subscriptions/route\",\n        pathname: \"/api/subscriptions\",\n        filename: \"route\",\n        bundlePath: \"app/api/subscriptions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\subscriptions\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["process", "env", "DATABASE_URL", "Error", "sql", "neon", "GET", "request", "institutionsResult", "count<PERSON><PERSON><PERSON>", "searchParams", "URL", "url", "search", "get", "status", "plan", "limit", "parseInt", "offset", "conditions", "params", "paramIndex", "push", "Promise", "all", "institutions", "total", "summary", "summaryResult", "response", "success", "data", "totalInstitutions", "total_institutions", "paidInstitutions", "paid_institutions", "unpaidInstitutions", "unpaid_institutions", "overdueInstitutions", "overdue_institutions", "totalStudents", "total_students", "totalTeachers", "total_teachers", "NextResponse", "json", "error", "console", "PUT", "result", "institutionIds", "paymentStatus", "paymentDueDate", "body", "Array", "isArray", "length", "includes", "validIds", "map", "id", "isNaN", "parsed", "message", "updatedCount", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.OPTIONS"], "sourceRoot": ""}