{"version": 3, "file": "../app/auth/sign-up/[[...sign-up]]/page.js", "mappings": "ubAAA,6GCAA,oDCAA,0hCCcA,OACA,UACA,GACA,CACA,UACA,OACA,CACA,UACA,UACA,CACA,UACA,iBACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAA8K,CAuBlM,6IAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CAEA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAzDA,IAAsB,sCAAiJ,CAyDvK,gHACA,gBAzDA,IAAsB,uCAAuJ,CAyD7K,sHACA,aAzDA,IAAsB,uCAAoJ,CAyD1K,mHACA,WAzDA,IAAsB,4CAAgF,CAyDtG,+CACA,cAzDA,IAAsB,4CAAmF,CAyDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,gJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,yCACA,wCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCpGD,kECAA,2GCAA,qDCAA,0DEmBI,sBAAsB,sJDjBbA,EAAqB,CAChCC,KADWD,CACJ,mBACPE,WAAAA,CAAa,8EACf,ECGM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CDlBrB,SAASC,CCkBA,CDjBtB,EADsBA,ECkB4B,CDlB5BA,CACfC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAeC,qBAAAA,CAAoB,iBAAiBC,uBAAAA,CAAsB,OAAOC,yBAAAA,CAAwB,YACnH,ECgBsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,8BAA8B,CAC9C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CAOxB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,kDCAA,+CCAA,yGCAA,gECAA,kDCAA,iECAA,uDCAA,sDCAA,uDCAA,wDCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,2PCM+C,MAAQ,OAAC,yBAAyB,kTAAkT,WAAW,wPAAwP,aAAa,0DAA0D,IAAyB,2DCAvrB,IAAM,EAAE,OAAC,YAAF,QAAE,UAA8B,2SAA2S,WAAW,6CAA6C,WAAW,mOAAmO,IAAyB,mECShsB,IAAMC,EAAkB,KACtB,GAAM,CAACC,EAAcC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC3CC,EAAW,CAAC,CAChBC,KAAMC,CAASA,CACfd,MAAO,kBACPC,YAAa,2FACbc,UAAW,mBACb,EAAG,CACDF,KAAMG,EAAAA,CAASA,CACfhB,MAAO,uBACPC,YAAa,kGACbc,UAAW,iBACb,EAAG,CACDF,KAAMI,EAAAA,CAAWA,CACjBjB,MAAO,yBACPC,YAAa,yFACbc,UAAW,uBACb,EAAE,CACFG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAAQC,YAAY,KACxBV,EAAgBW,GAAQ,CAACA,GAAO,EAAKT,EAASU,MAAM,CACtD,EAAG,KACH,MAAO,IAAMC,cAAcJ,EAC7B,EAAG,CAACP,EAASU,MAAM,CAAC,EAOpB,IAAME,EAAiBZ,CAAQ,CAACH,EAAa,CACvCgB,EAAOD,EAAeX,IAAI,CAChC,MAAO,WAACa,MAAAA,CAAIC,UAAU,4DAA4DrB,wBAAsB,kBAAkBC,0BAAwB,6BAC9I,WAACmB,MAAAA,CAAIC,UAAU,mDACb,UAACC,SAAAA,CAAOC,QAPI,CAOKC,IANrBpB,EAAgBW,GAAQ,CAACA,EAAO,EAAIT,EAASU,MAAAA,EAAUV,EAASU,MAAM,CACxE,EAKkCK,UAAU,4EACpC,UAACI,EAAAA,CAAeA,CAAAA,CAACJ,UAAU,qBAAqBtB,sBAAoB,kBAAkBE,0BAAwB,uBAGhH,UAACmB,MAAAA,CAAIC,UAAU,0BACZf,EAASoB,GAAG,CAAC,CAACC,EAAGC,IAAU,UAACN,SAAAA,CAAmBC,QAAS,IAAMnB,EAAgBwB,GAAQP,UAAW,CAAC,uCAAuC,EAAEO,IAAUzB,EAAe,WAAa,eAAe,EAAxJyB,MAG3C,UAACN,SAAAA,CAAOC,QAlBI,CAkBKM,IAjBrBzB,EAAgBW,GAAQ,CAACA,GAAO,EAAKT,EAASU,MAAM,CACtD,EAgBkCK,UAAU,4EACpC,UAACS,EAAAA,CAAgBA,CAAAA,CAACT,UAAU,qBAAqBtB,sBAAoB,mBAAmBE,0BAAwB,0BAIpH,WAACmB,MAAAA,CAAIC,UAAU,kCACb,UAACD,MAAAA,CAAIC,UAAU,+BACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACF,EAAAA,CAAKE,UAAU,qBAAqBtB,sBAAoB,OAAOE,0BAAwB,yBAI5F,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACU,KAAAA,CAAGV,UAAU,wCAAgCH,EAAexB,KAAK,GAClE,UAACsC,IAAAA,CAAEX,UAAU,iDAAyCH,EAAevB,WAAW,GAChF,UAACyB,MAAAA,CAAIC,UAAU,2DACb,UAACY,OAAAA,CAAKZ,UAAU,0CAAkCH,EAAeT,SAAS,aAKtF,EACe,SAASX,IACtB,GAAM,CAACoC,EAAcC,EAAgB,CAAG9B,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAAC+B,EAAqBC,EAAuB,CAAGhC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzD,CAACiC,EAAUC,EAAY,CAAGlC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACvCmC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,MAAO,GACPC,cAAe,GACfC,aAAc,GACdC,SAAU,GACVC,gBAAiB,GACjBC,YAAY,EACZC,iBAAkB,EACpB,GACMC,EAAoB,IACxB,GAAM,MACJC,CAAI,OACJC,CAAK,MACLC,CAAI,SACJC,CAAO,CACR,CAAGC,EAAEC,MAAM,CACZjB,EAAYxB,GAAS,EACnB,EADmB,CAChBA,CAAI,CACP,CAACoC,EAAK,CAAW,aAATE,EAAsBC,EAAUF,EAC1C,EACF,EACMK,EAAqB,CAACN,EAAcC,KACxCb,EAAYxB,GAAS,EACnB,EADmB,CAChBA,CAAI,CACP,CAACoC,EAAK,CAAEC,EACV,EACF,EACM,CAACM,EAASC,EAAW,CAAGtD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACuD,EAAOC,EAAS,CAAGxD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC7ByD,EAAe,MAAOP,IAM1B,GALAA,EAAEQ,cAAc,GAChBJ,GAAW,GACXE,EAAS,IAGLvB,EAASQ,QAAQ,GAAKR,EAASS,eAAe,CAAE,CAClDc,EAAS,wBACTF,EAAW,IACX,MACF,CACA,GAAI,CACF,IAAMK,EAAW,MAAMC,MAAM,qBAAsB,CACjDC,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMC,KAAKC,SAAS,CAAC,CACnB9B,UAAWF,EAASE,SAAS,CAC7BC,SAAUH,EAASG,QAAQ,CAC3BC,MAAOJ,EAASI,KAAK,CACrBC,MAAOL,EAASK,KAAK,CACrBG,SAAUR,EAASQ,QAAQ,CAC3BF,cAAeN,EAASM,aAAa,CACrCC,aAAcP,EAASO,YAAY,EAEvC,GACM0B,EAAO,MAAMP,EAASQ,IAAI,GAChC,GAAI,CAACR,EAASS,EAAE,CACd,CADgB,KACV,MAAUF,EAAKX,KAAK,EAAI,uBAEhCc,EAAAA,EAAKA,CAACC,OAAO,CAAC,uBAAwB,CACpChF,YAAa,2DACbiF,SAAU,GACZ,GAGArC,EAAY,CACVC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,MAAO,GACPC,cAAe,GACfC,aAAc,GACdC,SAAU,GACVC,gBAAiB,GACjBC,WAAY,GACZC,kBAAkB,CACpB,GAGA4B,WAAW,KACTC,OAAOC,QAAQ,CAACC,IAAI,CAAG,eACzB,EAAG,IACL,CAAE,MAAOC,EAAU,CACjBpB,EAASoB,EAAIC,OAAO,EAAI,oCAC1B,QAAU,CACRvB,GAAW,EACb,CACF,EACA,MAAO,UAACvC,MAAAA,CAAIC,UAAU,sEAAsErB,wBAAsB,iBAAiBC,0BAAwB,4BACvJ,WAACmB,MAAAA,CAAIC,UAAU,0BAEb,WAACD,MAAAA,CAAIC,UAAU,sGAEb,UAACD,MAAAA,CAAIC,UAAU,uCACb,WAAC8D,MAAAA,CAAIC,MAAM,OAAOC,OAAO,OAAOC,MAAM,6BAA6BvF,sBAAoB,MAAME,0BAAwB,6BACnH,UAACsF,OAAAA,CAAKxF,sBAAoB,OAAOE,0BAAwB,4BACvD,UAACuF,UAAAA,CAAQC,GAAG,OAAOL,MAAM,KAAKC,OAAO,KAAKK,aAAa,iBAAiB3F,sBAAoB,UAAUE,0BAAwB,4BAC5H,UAAC0F,OAAAA,CAAKC,EAAE,oBAAoBC,KAAK,OAAOC,OAAO,eAAeC,YAAY,IAAIhG,sBAAoB,OAAOE,0BAAwB,yBAGrI,UAAC+F,OAAAA,CAAKZ,MAAM,OAAOC,OAAO,OAAOQ,KAAK,aAAa9F,sBAAoB,OAAOE,0BAAwB,0BAI1G,UAACmB,MAAAA,CAAIC,UAAU,mEAEb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACD,MAAAA,CAAIC,UAAU,+BACb,UAAC4E,EAAAA,OAAKA,CAAAA,CAACC,IAAI,6BAA6BC,IAAI,WAAWf,MAAO,IAAKC,OAAQ,IAAKhE,UAAU,iBAAiBtB,sBAAoB,QAAQE,0BAAwB,uBAGjK,WAACmB,MAAAA,CAAIC,UAAU,4BACb,WAAC+E,KAAAA,CAAG/E,UAAU,6CAAmC,sBAC5B,UAACgF,KAAAA,CAAAA,GAAK,oBACR,UAACA,KAAAA,CAAAA,GAAK,2BAGzB,UAACrE,IAAAA,CAAEX,UAAU,iDAAwC,mHAOvD,UAACD,MAAAA,CAAIC,UAAU,yBACb,UAACnB,EAAAA,CAAgBH,sBAAoB,kBAAkBE,0BAAwB,+BAQvF,UAACmB,MAAAA,CAAIC,UAAU,kCACb,UAACD,MAAAA,CAAIC,UAAU,iEACb,WAACD,MAAAA,CAAIC,UAAU,wDAEf,UAACD,MAAAA,CAAIC,UAAU,8CACb,UAACD,MAAAA,CAAIC,UAAU,4CACb,UAAC4E,EAAAA,OAAKA,CAAAA,CAACC,IAAI,uBAAuBC,IAAI,WAAWf,MAAO,IAAKC,OAAQ,IAAKhE,UAAU,2CAA2CtB,sBAAoB,QAAQE,0BAAwB,yBAKvL,WAACqG,EAAAA,EAAIA,CAAAA,CAACjF,UAAU,qBAAqBtB,sBAAoB,OAAOE,0BAAwB,6BACtF,WAACsG,EAAAA,EAAUA,CAAAA,CAAClF,UAAU,qCAAqCtB,sBAAoB,aAAaE,0BAAwB,6BAClH,UAACuG,EAAAA,EAASA,CAAAA,CAACnF,UAAU,mCAAmCtB,sBAAoB,YAAYE,0BAAwB,4BAAmB,mBACnI,UAACwG,EAAAA,EAAeA,CAAAA,CAACpF,UAAU,gBAAgBtB,sBAAoB,kBAAkBE,0BAAwB,4BAAmB,kEAK9H,WAACyG,EAAAA,EAAWA,CAAAA,CAAC3G,sBAAoB,cAAcE,0BAAwB,6BACpE2D,GAAS,UAACxC,MAAAA,CAAIC,UAAU,+DACrB,UAACW,IAAAA,CAAEX,UAAU,gCAAwBuC,MAGzC,WAAC+C,OAAAA,CAAKC,SAAU9C,EAAczC,UAAU,sBAEtC,WAACD,MAAAA,CAAIC,UAAU,mCACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,iBAGhJ,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,YAAYtC,KAAK,YAAYE,KAAK,OAAO2D,YAAY,aAAa5D,MAAOd,EAASE,SAAS,CAAEyE,SAAU/D,EAAmB7B,UAAU,yFAAyF6F,QAAQ,IAACnH,sBAAoB,QAAQE,0BAAwB,wBAEtS,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,WAAWzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,oBAG/I,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,WAAWtC,KAAK,WAAWE,KAAK,OAAO2D,YAAY,gBAAgB5D,MAAOd,EAASG,QAAQ,CAAEwE,SAAU/D,EAAmB7B,UAAU,yFAAyF6F,QAAQ,IAACnH,sBAAoB,QAAQE,0BAAwB,2BAKxS,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,QAAQzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,YAG5I,WAACmB,MAAAA,CAAIC,UAAU,qBACb,UAAC8F,EAAAA,CAAQA,CAAAA,CAAC9F,UAAU,2EAA2EtB,sBAAoB,WAAWE,0BAAwB,qBACtJ,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,QAAQtC,KAAK,QAAQE,KAAK,QAAQ2D,YAAY,iBAAiB5D,MAAOd,EAASI,KAAK,CAAEuE,SAAU/D,EAAmB7B,UAAU,+FAA+F6F,QAAQ,IAACnH,sBAAoB,QAAQE,0BAAwB,2BAKvS,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,QAAQzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,oBAG5I,WAACmB,MAAAA,CAAIC,UAAU,qBACb,UAAC+F,EAASA,CAAC/F,UAAU,QAAX+F,mEAAsFrH,sBAAoB,YAAYE,0BAAwB,qBACxJ,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,QAAQtC,KAAK,QAAQE,KAAK,MAAM2D,YAAY,oBAAoB5D,MAAOd,EAASK,KAAK,CAAEsE,SAAU/D,EAAmB7B,UAAU,+FAA+F6F,QAAQ,IAACnH,sBAAoB,QAAQE,0BAAwB,2BAKxS,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,gBAAgBzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,2BAGpJ,WAACoH,EAAAA,EAAMA,CAAAA,CAACC,cAAelE,GAASK,EAAmB,gBAAiBL,GAAQrD,sBAAoB,SAASE,0BAAwB,6BAC/H,UAACsH,EAAAA,EAAaA,CAAAA,CAAClG,UAAU,yFAAyFtB,sBAAoB,gBAAgBE,0BAAwB,4BAC5K,UAACuH,EAAAA,EAAWA,CAAAA,CAACR,YAAY,2BAA2BjH,sBAAoB,cAAcE,0BAAwB,uBAEhH,WAACwH,EAAAA,EAAaA,CAAAA,CAAC1H,sBAAoB,gBAAgBE,0BAAwB,6BACzE,UAACyH,EAAAA,EAAUA,CAAAA,CAACtE,MAAM,gBAAgBrD,sBAAoB,aAAaE,0BAAwB,4BAAmB,kBAC9G,UAACyH,EAAAA,EAAUA,CAAAA,CAACtE,MAAM,eAAerD,sBAAoB,aAAaE,0BAAwB,4BAAmB,iBAC7G,UAACyH,EAAAA,EAAUA,CAAAA,CAACtE,MAAM,qBAAqBrD,sBAAoB,aAAaE,0BAAwB,4BAAmB,uBACnH,UAACyH,EAAAA,EAAUA,CAAAA,CAACtE,MAAM,gBAAgBrD,sBAAoB,aAAaE,0BAAwB,4BAAmB,mCAMpH,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAezF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,wBAGnJ,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,eAAetC,KAAK,eAAeE,KAAK,OAAO2D,YAAY,gCAAgC5D,MAAOd,EAASO,YAAY,CAAEoE,SAAU/D,EAAmB7B,UAAU,yFAAyFtB,sBAAoB,QAAQE,0BAAwB,wBAIzT,WAACmB,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,WAAWzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,eAG/I,WAACmB,MAAAA,CAAIC,UAAU,qBACb,UAACsG,EAAAA,CAAQA,CAAAA,CAACtG,UAAU,2EAA2EtB,sBAAoB,WAAWE,0BAAwB,qBACtJ,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,WAAWtC,KAAK,WAAWE,KAAMnB,EAAe,OAAS,WAAY8E,YAAY,qBAAqB5D,MAAOd,EAASQ,QAAQ,CAAEmE,SAAU/D,EAAmB7B,UAAU,qGAAqG6F,QAAQ,IAACnH,sBAAoB,QAAQE,0BAAwB,qBACnV,UAACqB,SAAAA,CAAO+B,KAAK,SAAS9B,QAAS,IAAMY,EAAgB,CAACD,GAAeb,UAAU,iGAC5Ea,EAAe,UAAC0F,EAAAA,CAAUA,CAAAA,CAACvG,UAAU,YAAe,UAACwG,EAAAA,CAAOA,CAAAA,CAACxG,UAAU,oBAK9E,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACwF,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,kBAAkBzF,UAAU,oCAAoCtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,0BAGtJ,WAACmB,MAAAA,CAAIC,UAAU,qBACb,UAACsG,EAAAA,CAAQA,CAAAA,CAACtG,UAAU,2EAA2EtB,sBAAoB,WAAWE,0BAAwB,qBACtJ,UAAC8G,EAAAA,CAAKA,CAAAA,CAACtB,GAAG,kBAAkBtC,KAAK,kBAAkBE,KAAMjB,EAAsB,OAAS,WAAY4E,YAAY,uBAAuB5D,MAAOd,EAASS,eAAe,CAAEkE,SAAU/D,EAAmB7B,UAAU,qGAAqG6F,QAAQ,IAACnH,sBAAoB,QAAQE,0BAAwB,qBACjX,UAACqB,SAAAA,CAAO+B,KAAK,SAAS9B,QAAS,IAAMc,EAAuB,CAACD,GAAsBf,UAAU,iGAC1Fe,EAAsB,UAACwF,EAAAA,CAAUA,CAAAA,CAACvG,UAAU,YAAe,UAACwG,EAAAA,CAAOA,CAAAA,CAACxG,UAAU,oBAMrF,WAACD,MAAAA,CAAIC,UAAU,2BACb,WAACD,MAAAA,CAAIC,UAAU,uCACb,UAACyG,EAAAA,CAAQA,CAAAA,CAACrC,GAAG,aAAatC,KAAK,aAAaG,QAAShB,EAASU,UAAU,CAAE+E,gBAAiBzE,GAAWf,EAAYxB,GAAS,EACzH,EADyH,CACtHA,CAAI,CACPiC,WAAYM,EACd,GAAKjC,UAAU,uGAAuG6F,QAAQ,IAACnH,sBAAoB,WAAWE,0BAAwB,qBACtL,WAAC4G,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAazF,UAAU,uDAAuDtB,sBAAoB,QAAQE,0BAAwB,6BAAmB,kBAClJ,IAChB,UAAC+H,IAAIA,CAAChD,KAAK,KAANgD,IAAe3G,UAAU,oFAAoFtB,sBAAoB,OAAOE,0BAAwB,4BAAmB,uBAEhL,IAAI,MACR,IACJ,UAAC+H,IAAIA,CAAChD,KAAK,KAANgD,MAAiB3G,UAAU,oFAAoFtB,sBAAoB,OAAOE,0BAAwB,4BAAmB,sBAElL,IAAI,kBAKhB,WAACmB,MAAAA,CAAIC,UAAU,uCACb,UAACyG,EAAAA,CAAQA,CAAAA,CAACrC,GAAG,mBAAmBtC,KAAK,mBAAmBG,QAAShB,EAASW,gBAAgB,CAAE8E,gBAAiBzE,GAAWf,EAAYxB,GAAS,EAC3I,EAD2I,CACxIA,CAAI,CACPkC,iBAAkBK,EACpB,GAAKjC,UAAU,uGAAuGtB,sBAAoB,WAAWE,0BAAwB,qBAC7K,UAAC4G,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,mBAAmBzF,UAAU,uDAAuDtB,sBAAoB,QAAQE,0BAAwB,4BAAmB,oFAO9K,UAACgI,EAAAA,CAAMA,CAAAA,CAAC5E,KAAK,SAAS6E,QAAQ,MAAM7G,UAAU,yCAAyC8G,SAAU,CAAC7F,EAASU,UAAU,EAAIU,EAAS3D,sBAAoB,SAASE,0BAAwB,4BACpLyD,EAAU,eAAiB,uBAKhC,WAAC1B,IAAAA,CAAEX,UAAU,mDAAyC,oBAClC,IAClB,UAAC2G,IAAIA,CAAChD,KAAK,KAANgD,WAAsB3G,UAAU,0EAA0EtB,sBAAoB,OAAOE,0BAAwB,4BAAmB,2BAS3L,UAACmB,MAAAA,CAAIC,UAAU,uDACb,WAACD,MAAAA,CAAIC,UAAU,qEACb,UAACY,OAAAA,UAAK,mCACN,UAACmG,MAAAA,CAAIlC,IAAI,uDAAuDC,IAAI,YAAY9E,UAAU,qCAQ1G,mBC3YA,uCAA2M,wKCM3M,SAASgG,EAAO,CACd,GAAGgB,EAC+C,EAClD,MAAO,UAACC,EAAAA,EAAoB,EAACC,YAAU,SAAU,GAAGF,CAAK,CAAEtI,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CAMA,SAASuH,EAAY,CACnB,GAAGa,EACgD,EACnD,MAAO,UAACC,EAAAA,EAAqB,EAACC,YAAU,eAAgB,GAAGF,CAAK,CAAEtI,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5K,CACA,SAASsH,EAAc,WACrBlG,CAAS,CACTmH,OAAO,SAAS,CAChBC,UAAQ,CACR,GAAGJ,EAGJ,EACC,MAAO,WAACC,EAAAA,EAAuB,EAACC,YAAU,iBAAiBG,YAAWF,EAAMnH,UAAWsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+yBAAgzBtH,GAAa,GAAGgH,CAAK,CAAEtI,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,uBACxgCwI,EACD,UAACH,EAAAA,EAAoB,EAACM,OAAO,IAAC7I,sBAAoB,uBAAuBE,0BAAwB,sBAC/F,UAAC4I,EAAAA,CAAeA,CAAAA,CAACxH,UAAU,oBAAoBtB,sBAAoB,kBAAkBE,0BAAwB,mBAGrH,CACA,SAASwH,EAAc,WACrBpG,CAAS,UACToH,CAAQ,UACRK,EAAW,QAAQ,CACnB,GAAGT,EACkD,EACrD,MAAO,UAACC,EAAAA,EAAsB,EAACvI,sBAAoB,yBAAyBC,wBAAsB,gBAAgBC,0BAAwB,sBACtI,WAACqI,EAAAA,EAAuB,EAACC,YAAU,iBAAiBlH,UAAWsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gjBAA8jB,WAAbG,GAAyB,kIAAmIzH,GAAYyH,SAAUA,EAAW,GAAGT,CAAK,CAAEtI,sBAAoB,0BAA0BE,0BAAwB,uBAC93B,UAAC8I,EAAAA,CAAqBhJ,sBAAoB,uBAAuBE,0BAAwB,eACzF,UAACqI,EAAAA,EAAwB,EAACjH,UAAWsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,MAAOG,cAAyB,uGAAwG/I,sBAAoB,2BAA2BE,0BAAwB,sBACpPwI,IAEH,UAACO,EAAAA,CAAuBjJ,sBAAoB,yBAAyBE,0BAAwB,mBAGrG,CAOA,SAASyH,EAAW,CAClBrG,WAAS,UACToH,CAAQ,CACR,GAAGJ,EAC+C,EAClD,MAAO,WAACC,EAAAA,EAAoB,EAACC,YAAU,cAAclH,UAAWsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4aAA6atH,GAAa,GAAGgH,CAAK,CAAEtI,sBAAoB,uBAAuBC,wBAAsB,aAAaC,0BAAwB,uBACzmB,UAACgC,OAAAA,CAAKZ,UAAU,sEACd,UAACiH,EAAAA,EAA6B,EAACvI,sBAAoB,gCAAgCE,0BAAwB,sBACzG,UAACgJ,EAAAA,CAASA,CAAAA,CAAC5H,UAAU,SAAStB,sBAAoB,YAAYE,0BAAwB,mBAG1F,UAACqI,EAAAA,EAAwB,EAACvI,sBAAoB,2BAA2BE,0BAAwB,sBAAcwI,MAErH,CAOA,SAASM,EAAqB,WAC5B1H,CAAS,CACT,GAAGgH,EACyD,EAC5D,MAAO,UAACC,EAAAA,EAA8B,EAACC,YAAU,0BAA0BlH,UAAWsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDtH,GAAa,GAAGgH,CAAK,CAAEtI,sBAAoB,iCAAiCC,wBAAsB,uBAAuBC,0BAAwB,sBAC9R,UAACiJ,EAAAA,CAAaA,CAAAA,CAAC7H,UAAU,SAAStB,sBAAoB,gBAAgBE,0BAAwB,gBAEpG,CACA,SAAS+I,EAAuB,WAC9B3H,CAAS,CACT,GAAGgH,EAC2D,EAC9D,MAAO,UAACC,EAAAA,EAAgC,EAACC,YAAU,4BAA4BlH,UAAWsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDtH,GAAa,GAAGgH,CAAK,CAAEtI,sBAAoB,mCAAmCC,wBAAsB,yBAAyBC,0BAAwB,sBACtS,UAAC4I,EAAAA,CAAeA,CAAAA,CAACxH,UAAU,SAAStB,sBAAoB,kBAAkBE,0BAAwB,gBAExG,0BC7FA,qDCAA,mFCM+C,MAAQ,cAAC,2BAA2B,iPAAiP,WAAW,4OAA4O,WAAW,qRAAqR,WAAW,0JAA0J,IAAyB,wBCNzhC,uDCAA,kECAA,uDCAA,+CCAA,uCAA2M,yBCA3M,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?8304", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/src/app/auth/sign-up/[[...sign-up]]/page.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/video_01_icon.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/smart_phone_01_icon.js", "webpack://terang-lms-ui/./src/features/auth/components/sign-up-view.tsx", "webpack://terang-lms-ui/?d853", "webpack://terang-lms-ui/./src/components/ui/select.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/user_group_icon.js", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/?20fa", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\auth\\\\sign-up\\\\[[...sign-up]]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'sign-up',\n        {\n        children: [\n        '[[...sign-up]]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\auth\\\\sign-up\\\\[[...sign-up]]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\auth\\\\sign-up\\\\[[...sign-up]]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/auth/sign-up/[[...sign-up]]/page\",\n        pathname: \"/auth/sign-up/[[...sign-up]]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import { Metadata } from 'next';\nimport SignUpViewPage from '@/features/auth/components/sign-up-view';\nexport const metadata: Metadata = {\n  title: 'IAI LMS | Daftar',\n  description: 'Daftar akun baru di platform pembelajaran IAI LMS - Ikatan Arsitek Indonesia'\n};\nexport default function Page() {\n  return <SignUpViewPage data-sentry-element=\"SignUpViewPage\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\" />;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/auth/sign-up/[[...sign-up]]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/auth/sign-up/[[...sign-up]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/auth/sign-up/[[...sign-up]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/auth/sign-up/[[...sign-up]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Video01Icon\",[[\"path\",{d:\"M2 11C2 7.70017 2 6.05025 3.02513 5.02513C4.05025 4 5.70017 4 9 4H10C13.2998 4 14.9497 4 15.9749 5.02513C17 6.05025 17 7.70017 17 11V13C17 16.2998 17 17.9497 15.9749 18.9749C14.9497 20 13.2998 20 10 20H9C5.70017 20 4.05025 20 3.02513 18.9749C2 17.9497 2 16.2998 2 13V11Z\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M17 8.90585L17.1259 8.80196C19.2417 7.05623 20.2996 6.18336 21.1498 6.60482C22 7.02628 22 8.42355 22 11.2181V12.7819C22 15.5765 22 16.9737 21.1498 17.3952C20.2996 17.8166 19.2417 16.9438 17.1259 15.198L17 15.0941\",stroke:\"currentColor\",key:\"k1\"}],[\"circle\",{cx:\"11.5\",cy:\"9.5\",r:\"1.5\",stroke:\"currentColor\",key:\"k2\"}]]);export{o as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"SmartPhone01Icon\",[[\"path\",{d:\"M5 9C5 5.70017 5 4.05025 6.02513 3.02513C7.05025 2 8.70017 2 12 2C15.2998 2 16.9497 2 17.9749 3.02513C19 4.05025 19 5.70017 19 9V15C19 18.2998 19 19.9497 17.9749 20.9749C16.9497 22 15.2998 22 12 22C8.70017 22 7.05025 22 6.02513 20.9749C5 19.9497 5 18.2998 5 15V9Z\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M11 19H13\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M9 2L9.089 2.53402C9.28188 3.69129 9.37832 4.26993 9.77519 4.62204C10.1892 4.98934 10.7761 5 12 5C13.2239 5 13.8108 4.98934 14.2248 4.62204C14.6217 4.26993 14.7181 3.69129 14.911 2.53402L15 2\",stroke:\"currentColor\",key:\"k2\"}]]);export{o as default};\n", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { toast } from 'sonner';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { ViewIcon as EyeIcon, ViewOffIcon as EyeOffIcon, Building02Icon as BuildingIcon, BookOpen01Icon as BookIcon, UserIcon, Mail01Icon as MailIcon, LockIcon, SmartPhone01Icon as PhoneIcon, ArrowLeft02Icon as ChevronLeftIcon, ArrowRight02Icon as ChevronRightIcon, Award01Icon as AwardIcon, Video01Icon as VideoIcon, Task01Icon as TaskIcon, UserGroupIcon as NetworkIcon } from 'hugeicons-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\n// Feature Carousel Component for Sign Up\nconst FeatureCarousel = () => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const features = [{\n    icon: VideoIcon,\n    title: 'Akses Unlimited',\n    description: 'Dapatkan akses tak terbatas ke seluruh koleksi kursus arsitektur terlengkap di Indonesia',\n    highlight: 'Ribuan Jam Konten'\n  }, {\n    icon: AwardIcon,\n    title: 'Sertifikat Resmi IAI',\n    description: 'Setiap kursus yang diselesaikan akan mendapatkan sertifikat resmi dari Ikatan Arsitek Indonesia',\n    highlight: 'Diakui Industri'\n  }, {\n    icon: NetworkIcon,\n    title: 'Networking Profesional',\n    description: 'Bergabung dengan komunitas lebih dari 10,000+ arsitek profesional di seluruh Indonesia',\n    highlight: '10,000+ Anggota Aktif'\n  }];\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % features.length);\n    }, 4000);\n    return () => clearInterval(timer);\n  }, [features.length]);\n  const nextSlide = () => {\n    setCurrentSlide(prev => (prev + 1) % features.length);\n  };\n  const prevSlide = () => {\n    setCurrentSlide(prev => (prev - 1 + features.length) % features.length);\n  };\n  const currentFeature = features[currentSlide];\n  const Icon = currentFeature.icon;\n  return <div className='bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]' data-sentry-component=\"FeatureCarousel\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n      <div className='flex items-center justify-between mb-4'>\r\n        <button onClick={prevSlide} className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'>\r\n          <ChevronLeftIcon className='w-4 h-4 text-white' data-sentry-element=\"ChevronLeftIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n        </button>\r\n        \r\n        <div className='flex space-x-2'>\r\n          {features.map((_, index) => <button key={index} onClick={() => setCurrentSlide(index)} className={`w-2 h-2 rounded-full transition-colors ${index === currentSlide ? 'bg-white' : 'bg-white/40'}`} />)}\r\n        </div>\r\n        \r\n        <button onClick={nextSlide} className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'>\r\n          <ChevronRightIcon className='w-4 h-4 text-white' data-sentry-element=\"ChevronRightIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n        </button>\r\n      </div>\r\n\r\n      <div className='text-center space-y-4'>\r\n        <div className='flex justify-center'>\r\n          <div className='w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center'>\r\n            <Icon className='w-8 h-8 text-white' data-sentry-element=\"Icon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n          </div>\r\n        </div>\r\n        \r\n        <div className='space-y-2'>\r\n          <h3 className='text-xl font-bold text-white'>{currentFeature.title}</h3>\r\n          <p className='text-white/90 text-sm leading-relaxed'>{currentFeature.description}</p>\r\n          <div className='inline-block bg-white/20 px-3 py-1 rounded-full'>\r\n            <span className='text-xs font-medium text-white'>{currentFeature.highlight}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>;\n};\nexport default function SignUpViewPage() {\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    iaiMembership: '',\n    organization: '',\n    password: '',\n    confirmPassword: '',\n    agreeTerms: false,\n    subscribeUpdates: false\n  });\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSelectChange = (name: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    // Validate password confirmation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Password tidak cocok');\n      setLoading(false);\n      return;\n    }\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          phone: formData.phone,\n          password: formData.password,\n          iaiMembership: formData.iaiMembership,\n          organization: formData.organization\n        })\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Registration failed');\n      }\n      toast.success('Registrasi berhasil!', {\n        description: 'Akun Anda telah dibuat. Silakan login untuk melanjutkan.',\n        duration: 4000\n      });\n\n      // Reset form\n      setFormData({\n        firstName: '',\n        lastName: '',\n        email: '',\n        phone: '',\n        iaiMembership: '',\n        organization: '',\n        password: '',\n        confirmPassword: '',\n        agreeTerms: false,\n        subscribeUpdates: false\n      });\n\n      // Optional: redirect to login page after a short delay\n      setTimeout(() => {\n        window.location.href = '/auth/sign-in';\n      }, 2000);\n    } catch (err: any) {\n      setError(err.message || 'Terjadi kesalahan saat registrasi');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return <div className='h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden' data-sentry-component=\"SignUpViewPage\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n      <div className='flex h-screen'>\r\n        {/* Left Side - Branding and Information */}\r\n        <div className='hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white'>\r\n          {/* Background Pattern */}\r\n          <div className='absolute inset-0 opacity-10'>\r\n            <svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" data-sentry-element=\"svg\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n              <defs data-sentry-element=\"defs\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                <pattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\" data-sentry-element=\"pattern\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                  <path d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\" data-sentry-element=\"path\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                </pattern>\r\n              </defs>\r\n              <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" data-sentry-element=\"rect\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n            </svg>\r\n          </div>\r\n\r\n          <div className='relative z-10 flex flex-col justify-between p-12 w-full'>\r\n            {/* Logo and Branding */}\r\n            <div className='space-y-4'>\r\n              <div className='flex justify-center'>\r\n                <Image src='/assets/logo-iai-putih.png' alt='IAI Logo' width={200} height={200} className='object-contain' data-sentry-element=\"Image\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n              </div>\r\n              \r\n              <div className='space-y-6 mt-12'>\r\n                <h2 className='text-3xl font-bold leading-tight'>\r\n                  Bergabunglah dengan<br />\r\n                  Komunitas Arsitek<br />\r\n                  Profesional Indonesia\r\n                </h2>\r\n                <p className='text-lg text-white/90 leading-relaxed'>\r\n                  Daftarkan diri Anda dan mulai perjalanan pembelajaran profesional \r\n                  bersama ribuan arsitek Indonesia lainnya.\r\n                </p>\r\n              </div>\r\n\r\n              {/* Features Carousel */}\r\n              <div className='relative mt-8'>\r\n                <FeatureCarousel data-sentry-element=\"FeatureCarousel\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Side - Registration Form */}\r\n        <div className='flex-1 overflow-y-auto'>\r\n          <div className='flex min-h-full items-start justify-center p-4 sm:p-8'>\r\n            <div className='w-full max-w-md space-y-4 sm:space-y-6 py-4'>\r\n            {/* Mobile Logo */}\r\n            <div className='lg:hidden text-center mb-4 sm:mb-8'>\r\n              <div className='flex justify-center mb-2 sm:mb-4'>\r\n                <Image src='/assets/logo-iai.png' alt='IAI Logo' width={150} height={150} className='object-contain sm:w-[200px] sm:h-[200px]' data-sentry-element=\"Image\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Registration Card */}\r\n            <Card className='shadow-xl border-0' data-sentry-element=\"Card\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n              <CardHeader className='text-center space-y-1 pb-4 sm:pb-6' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                <CardTitle className='text-2xl font-bold text-gray-900' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"sign-up-view.tsx\">Buat Akun Baru</CardTitle>\r\n                <CardDescription className='text-gray-600' data-sentry-element=\"CardDescription\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                  Lengkapi data diri untuk mendaftar sebagai anggota IAI LMS\r\n                </CardDescription>\r\n              </CardHeader>\r\n              \r\n              <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                {error && <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>\r\n                    <p className='text-sm text-red-600'>{error}</p>\r\n                  </div>}\r\n\r\n                <form onSubmit={handleSubmit} className='space-y-4'>\r\n                  {/* Name Fields */}\r\n                  <div className='grid grid-cols-2 gap-3'>\r\n                    <div className='space-y-2'>\r\n                      <Label htmlFor='firstName' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                        Nama Depan *\r\n                      </Label>\r\n                      <Input id='firstName' name='firstName' type='text' placeholder='Nama depan' value={formData.firstName} onChange={handleInputChange} className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                    </div>\r\n                    <div className='space-y-2'>\r\n                      <Label htmlFor='lastName' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                        Nama Belakang *\r\n                      </Label>\r\n                      <Input id='lastName' name='lastName' type='text' placeholder='Nama belakang' value={formData.lastName} onChange={handleInputChange} className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Email Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='email' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      Email *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <MailIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' data-sentry-element=\"MailIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <Input id='email' name='email' type='email' placeholder='<EMAIL>' value={formData.email} onChange={handleInputChange} className='pl-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Phone Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='phone' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      Nomor Telepon *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <PhoneIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' data-sentry-element=\"PhoneIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <Input id='phone' name='phone' type='tel' placeholder='+62 812 3456 7890' value={formData.phone} onChange={handleInputChange} className='pl-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* IAI Membership */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='iaiMembership' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      Status Keanggotaan IAI\r\n                    </Label>\r\n                    <Select onValueChange={value => handleSelectChange('iaiMembership', value)} data-sentry-element=\"Select\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      <SelectTrigger className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                        <SelectValue placeholder='Pilih status keanggotaan' data-sentry-element=\"SelectValue\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      </SelectTrigger>\r\n                      <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                        <SelectItem value='anggota-biasa' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"sign-up-view.tsx\">Anggota Biasa</SelectItem>\r\n                        <SelectItem value='anggota-muda' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"sign-up-view.tsx\">Anggota Muda</SelectItem>\r\n                        <SelectItem value='anggota-luar-biasa' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"sign-up-view.tsx\">Anggota Luar Biasa</SelectItem>\r\n                        <SelectItem value='belum-anggota' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"sign-up-view.tsx\">Belum Menjadi Anggota</SelectItem>\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  {/* Organization */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='organization' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      Instansi/Perusahaan\r\n                    </Label>\r\n                    <Input id='organization' name='organization' type='text' placeholder='Nama instansi atau perusahaan' value={formData.organization} onChange={handleInputChange} className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                  </div>\r\n\r\n                  {/* Password Fields */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='password' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      Password *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' data-sentry-element=\"LockIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <Input id='password' name='password' type={showPassword ? 'text' : 'password'} placeholder='Minimal 8 karakter' value={formData.password} onChange={handleInputChange} className='pl-10 pr-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <button type='button' onClick={() => setShowPassword(!showPassword)} className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'>\r\n                        {showPassword ? <EyeOffIcon className='w-4 h-4' /> : <EyeIcon className='w-4 h-4' />}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='confirmPassword' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                      Konfirmasi Password *\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' data-sentry-element=\"LockIcon\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <Input id='confirmPassword' name='confirmPassword' type={showConfirmPassword ? 'text' : 'password'} placeholder='Ketik ulang password' value={formData.confirmPassword} onChange={handleInputChange} className='pl-10 pr-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <button type='button' onClick={() => setShowConfirmPassword(!showConfirmPassword)} className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'>\r\n                        {showConfirmPassword ? <EyeOffIcon className='w-4 h-4' /> : <EyeIcon className='w-4 h-4' />}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Checkboxes */}\r\n                  <div className='space-y-3 pt-2'>\r\n                    <div className='flex items-start space-x-2'>\r\n                      <Checkbox id='agreeTerms' name='agreeTerms' checked={formData.agreeTerms} onCheckedChange={checked => setFormData(prev => ({\n                        ...prev,\n                        agreeTerms: checked as boolean\n                      }))} className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)] mt-0.5' required data-sentry-element=\"Checkbox\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <Label htmlFor='agreeTerms' className='text-sm text-gray-600 cursor-pointer leading-relaxed' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                        Saya menyetujui{' '}\r\n                        <Link href='/terms' className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium underline' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                          Syarat & Ketentuan\r\n                        </Link>{' '}\r\n                        dan{' '}\r\n                        <Link href='/privacy' className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium underline' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                          Kebijakan Privasi\r\n                        </Link>{' '}\r\n                        IAI LMS *\r\n                      </Label>\r\n                    </div>\r\n\r\n                    <div className='flex items-start space-x-2'>\r\n                      <Checkbox id='subscribeUpdates' name='subscribeUpdates' checked={formData.subscribeUpdates} onCheckedChange={checked => setFormData(prev => ({\n                        ...prev,\n                        subscribeUpdates: checked as boolean\n                      }))} className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)] mt-0.5' data-sentry-element=\"Checkbox\" data-sentry-source-file=\"sign-up-view.tsx\" />\r\n                      <Label htmlFor='subscribeUpdates' className='text-sm text-gray-600 cursor-pointer leading-relaxed' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                        Saya ingin menerima informasi kursus baru dan update terbaru dari IAI LMS\r\n                      </Label>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Register Button */}\r\n                  <Button type='submit' variant='iai' className='w-full h-12 text-base font-medium mt-6' disabled={!formData.agreeTerms || loading} data-sentry-element=\"Button\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                    {loading ? 'Mendaftar...' : 'Daftar Sekarang'}\r\n                  </Button>\r\n                </form>\r\n\r\n                {/* Sign In Link */}\r\n                <p className='text-center text-sm text-gray-600 mt-6'>\r\n                  Sudah punya akun?{' '}\r\n                  <Link href='/auth/sign-in' className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-up-view.tsx\">\r\n                    Masuk di sini\r\n                  </Link>\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n\r\n\r\n            {/* Footer */}\r\n            <div className='text-center text-xs text-gray-500 space-y-1'>\r\n              <div className='flex items-center justify-center space-x-2 text-gray-400'>\r\n                <span>© 2024 IAI LMS - Powered by</span>\r\n                <img src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\" alt=\"Terang AI\" className='h-4 inline-block' />\r\n              </div>\r\n            </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>;\n}", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\auth\\\\components\\\\sign-up-view.tsx\");\n", "'use client';\n\nimport * as React from 'react';\nimport * as SelectPrimitive from '@radix-ui/react-select';\nimport { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot='select' {...props} data-sentry-element=\"SelectPrimitive.Root\" data-sentry-component=\"Select\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot='select-group' {...props} data-sentry-element=\"SelectPrimitive.Group\" data-sentry-component=\"SelectGroup\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot='select-value' {...props} data-sentry-element=\"SelectPrimitive.Value\" data-sentry-component=\"SelectValue\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectTrigger({\n  className,\n  size = 'default',\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: 'sm' | 'default';\n}) {\n  return <SelectPrimitive.Trigger data-slot='select-trigger' data-size={size} className={cn(\"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"SelectPrimitive.Trigger\" data-sentry-component=\"SelectTrigger\" data-sentry-source-file=\"select.tsx\">\r\n      {children}\r\n      <SelectPrimitive.Icon asChild data-sentry-element=\"SelectPrimitive.Icon\" data-sentry-source-file=\"select.tsx\">\r\n        <ChevronDownIcon className='size-4 opacity-50' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>;\n}\nfunction SelectContent({\n  className,\n  children,\n  position = 'popper',\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return <SelectPrimitive.Portal data-sentry-element=\"SelectPrimitive.Portal\" data-sentry-component=\"SelectContent\" data-sentry-source-file=\"select.tsx\">\r\n      <SelectPrimitive.Content data-slot='select-content' className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md', position === 'popper' && 'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1', className)} position={position} {...props} data-sentry-element=\"SelectPrimitive.Content\" data-sentry-source-file=\"select.tsx\">\r\n        <SelectScrollUpButton data-sentry-element=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\" />\r\n        <SelectPrimitive.Viewport className={cn('p-1', position === 'popper' && 'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1')} data-sentry-element=\"SelectPrimitive.Viewport\" data-sentry-source-file=\"select.tsx\">\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton data-sentry-element=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>;\n}\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return <SelectPrimitive.Label data-slot='select-label' className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)} {...props} data-sentry-element=\"SelectPrimitive.Label\" data-sentry-component=\"SelectLabel\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return <SelectPrimitive.Item data-slot='select-item' className={cn(\"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\", className)} {...props} data-sentry-element=\"SelectPrimitive.Item\" data-sentry-component=\"SelectItem\" data-sentry-source-file=\"select.tsx\">\r\n      <span className='absolute right-2 flex size-3.5 items-center justify-center'>\r\n        <SelectPrimitive.ItemIndicator data-sentry-element=\"SelectPrimitive.ItemIndicator\" data-sentry-source-file=\"select.tsx\">\r\n          <CheckIcon className='size-4' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"select.tsx\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText data-sentry-element=\"SelectPrimitive.ItemText\" data-sentry-source-file=\"select.tsx\">{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>;\n}\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return <SelectPrimitive.Separator data-slot='select-separator' className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)} {...props} data-sentry-element=\"SelectPrimitive.Separator\" data-sentry-component=\"SelectSeparator\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return <SelectPrimitive.ScrollUpButton data-slot='select-scroll-up-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollUpButton\" data-sentry-component=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronUpIcon className='size-4' data-sentry-element=\"ChevronUpIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollUpButton>;\n}\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return <SelectPrimitive.ScrollDownButton data-slot='select-scroll-down-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollDownButton\" data-sentry-component=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronDownIcon className='size-4' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollDownButton>;\n}\nexport { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectScrollDownButton, SelectScrollUpButton, SelectSeparator, SelectTrigger, SelectValue };", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport C from\"../create-hugeicon-component.js\";const r=C(\"UserGroupIcon\",[[\"path\",{d:\"M20.774 18C21.5233 18 22.1193 17.5285 22.6545 16.8691C23.7499 15.5194 21.9513 14.4408 21.2654 13.9126C20.568 13.3756 19.7894 13.0714 19 13M18 11C19.3807 11 20.5 9.88071 20.5 8.5C20.5 7.11929 19.3807 6 18 6\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M3.22596 18C2.47666 18 1.88067 17.5285 1.34555 16.8691C0.250089 15.5194 2.04867 14.4408 2.73465 13.9126C3.43197 13.3756 4.21058 13.0714 5 13M5.5 11C4.11929 11 3 9.88071 3 8.5C3 7.11929 4.11929 6 5.5 6\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M8.0838 15.1112C7.06203 15.743 4.38299 17.0331 6.0147 18.6474C6.81178 19.436 7.69952 20 8.81563 20H15.1844C16.3005 20 17.1882 19.436 17.9853 18.6474C19.617 17.0331 16.938 15.743 15.9162 15.1112C13.5201 13.6296 10.4799 13.6296 8.0838 15.1112Z\",stroke:\"currentColor\",key:\"k2\"}],[\"path\",{d:\"M15.5 7.5C15.5 9.433 13.933 11 12 11C10.067 11 8.5 9.433 8.5 7.5C8.5 5.567 10.067 4 12 4C13.933 4 15.5 5.567 15.5 7.5Z\",stroke:\"currentColor\",key:\"k3\"}]]);export{r as default};\n", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\auth\\\\components\\\\sign-up-view.tsx\");\n", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["metadata", "title", "description", "Page", "_jsx", "SignUpViewPage", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "FeatureCarousel", "currentSlide", "setCurrentSlide", "useState", "features", "icon", "VideoIcon", "highlight", "AwardIcon", "NetworkIcon", "useEffect", "timer", "setInterval", "prev", "length", "clearInterval", "currentFeature", "Icon", "div", "className", "button", "onClick", "prevSlide", "ChevronLeftIcon", "map", "_", "index", "nextSlide", "ChevronRightIcon", "h3", "p", "span", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "formData", "setFormData", "firstName", "lastName", "email", "phone", "iaiMembership", "organization", "password", "confirmPassword", "agreeTerms", "subscribeUpdates", "handleInputChange", "name", "value", "type", "checked", "e", "target", "handleSelectChange", "loading", "setLoading", "error", "setError", "handleSubmit", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "toast", "success", "duration", "setTimeout", "window", "location", "href", "err", "message", "svg", "width", "height", "xmlns", "defs", "pattern", "id", "patternUnits", "path", "d", "fill", "stroke", "strokeWidth", "rect", "Image", "src", "alt", "h2", "br", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "form", "onSubmit", "Label", "htmlFor", "Input", "placeholder", "onChange", "required", "MailIcon", "PhoneIcon", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "LockIcon", "EyeOffIcon", "EyeIcon", "Checkbox", "onCheckedChange", "Link", "<PERSON><PERSON>", "variant", "disabled", "img", "props", "SelectPrimitive", "data-slot", "size", "children", "data-size", "cn", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDownIcon", "position", "SelectScrollUpButton", "SelectScrollDownButton", "CheckIcon", "ChevronUpIcon"], "sourceRoot": ""}