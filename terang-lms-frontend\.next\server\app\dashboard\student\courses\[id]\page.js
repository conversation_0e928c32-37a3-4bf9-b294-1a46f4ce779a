try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="eeba4e8e-f873-46a9-881e-c3f4ee0d56bf",e._sentryDebugIdIdentifier="sentry-dbid-eeba4e8e-f873-46a9-881e-c3f4ee0d56bf")}catch(e){}(()=>{var e={};e.id=858,e.ids=[858],e.modules={116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(91754);function a({children:e}){return(0,s.jsx)(s.Frag<PERSON>,{children:e})}r(93491),r(76328)},2796:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=r(63033),n=r(1472),i=r(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\[id]\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let s,a,n;try{let e=l?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student/courses/[id]",componentType:"Page",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}}):o;let c=void 0,u=void 0,p=void 0,m=s},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5827:(e,t,r)=>{"use strict";r.d(t,{k:()=>o});var s=r(91754),a=r(93491),n=r(66536),i=r(82233);let o=a.forwardRef(({className:e,value:t,...r},a)=>(0,s.jsx)(n.bL,{ref:a,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,s.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));o.displayName=n.bL.displayName},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var s=r(91754);r(93491);var a=r(82233);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},14908:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},15808:(e,t,r)=>{Promise.resolve().then(r.bind(r,2796))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21381:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},21820:e=>{"use strict";e.exports=require("os")},26954:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(91754),a=r(9260),n=r(56682),i=r(80601),o=r(5827),d=r(86857),l=r(47028),c=r(41867),u=r(14908),p=r(85629),m=r(69622),x=r(21381),h=r(93491);function f({params:e}){let{id:t}=(0,h.use)(e),r={id:parseInt(t),name:"Introduction to Algebra",description:"Basic algebraic concepts and problem solving",type:"self_paced",courseCode:"MATH101",instructor:"Dr. Smith",progress:76,modules:[{id:1,name:"Introduction and Fundamentals",description:"Basic concepts and introduction to algebra",isUnlocked:!0,progress:100,chapters:[{id:1,name:"Chapter 1: Overview",description:"Introduction to algebraic thinking",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:1,quizCompleted:!0,quizScore:92},{id:2,name:"Chapter 2: Basic Concepts",description:"Fundamental algebraic principles",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:2,quizCompleted:!0,quizScore:88},{id:3,name:"Chapter 3: Variables and Expressions",description:"Working with variables and expressions",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:3,quizCompleted:!0,quizScore:95}],hasModuleQuiz:!0,moduleQuizId:4,moduleQuizCompleted:!0,moduleQuizScore:90},{id:2,name:"Core Algebraic Operations",description:"Essential operations and manipulations",isUnlocked:!0,progress:60,chapters:[{id:4,name:"Chapter 4: Addition and Subtraction",description:"Basic algebraic operations",isCompleted:!0,isUnlocked:!0,hasQuiz:!0,quizId:5,quizCompleted:!0,quizScore:85},{id:5,name:"Chapter 5: Multiplication and Division",description:"Advanced algebraic operations",isCompleted:!1,isUnlocked:!0,hasQuiz:!0,quizId:6,quizCompleted:!1,quizScore:null}],hasModuleQuiz:!0,moduleQuizId:7,moduleQuizCompleted:!1,moduleQuizScore:null},{id:3,name:"Advanced Topics",description:"Complex algebraic concepts",isUnlocked:!1,progress:0,chapters:[{id:6,name:"Chapter 6: Equations",description:"Solving algebraic equations",isCompleted:!1,isUnlocked:!1,hasQuiz:!0,quizId:8,quizCompleted:!1,quizScore:null}],hasModuleQuiz:!0,moduleQuizId:9,moduleQuizCompleted:!1,moduleQuizScore:null}],hasFinalExam:!0,finalExamId:10,finalExamUnlocked:!1,finalExamCompleted:!1,finalExamScore:null},f=e=>e.isUnlocked?e.isCompleted&&e.quizCompleted?"completed":e.isCompleted&&!e.quizCompleted?"quiz-pending":"in-progress":"locked",g=e=>{if(!e.isUnlocked)return"locked";let t=e.chapters.every(e=>e.isCompleted&&e.quizCompleted);return t&&e.moduleQuizCompleted?"completed":t&&!e.moduleQuizCompleted?"quiz-pending":"in-progress"};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentCourseDetailPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(a.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(a.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:r.name}),(0,s.jsx)(i.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:r.type})]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:r.description}),(0,s.jsxs)("div",{className:"text-muted-foreground flex items-center space-x-4 text-sm",children:[(0,s.jsxs)("span",{children:["Code: ",r.courseCode]}),(0,s.jsx)("span",{children:"•"}),(0,s.jsxs)("span",{children:["Instructor: ",r.instructor]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-primary text-3xl font-bold",children:[r.progress,"%"]}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Complete"})]})]})}),(0,s.jsx)(a.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(o.k,{value:r.progress,className:"h-3","data-sentry-element":"Progress","data-sentry-source-file":"page.tsx"})})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[r.modules.map((e,t)=>{let r=g(e);return(0,s.jsxs)(a.Zp,{className:`${!e.isUnlocked?"opacity-60":""}`,children:[(0,s.jsx)(a.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:`flex h-10 w-10 items-center justify-center rounded-full ${"completed"===r?"bg-green-100 text-green-600":"quiz-pending"===r?"bg-yellow-100 text-yellow-600":"locked"===r?"bg-gray-100 text-gray-400":"bg-blue-100 text-blue-600"}`,children:"completed"===r?(0,s.jsx)(d.A,{className:"h-5 w-5"}):"locked"===r?(0,s.jsx)(l.A,{className:"h-5 w-5"}):(0,s.jsx)(c.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(a.ZB,{className:"text-lg",children:["Module ",t+1,": ",e.name]}),(0,s.jsx)(a.BT,{children:e.description})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-xl font-bold",children:[e.progress,"%"]}),(0,s.jsx)(o.k,{value:e.progress,className:"h-2 w-20"})]})]})}),(0,s.jsx)(a.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[e.chapters.map((e,t)=>{let r=f(e);return(0,s.jsxs)("div",{className:`flex items-center justify-between rounded-lg border p-3 ${!e.isUnlocked?"bg-gray-50":""}`,children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:`flex h-8 w-8 items-center justify-center rounded-full ${"completed"===r?"bg-green-100 text-green-600":"quiz-pending"===r?"bg-yellow-100 text-yellow-600":"locked"===r?"bg-gray-100 text-gray-400":"bg-blue-100 text-blue-600"}`,children:"completed"===r?(0,s.jsx)(d.A,{className:"h-4 w-4"}):"quiz-pending"===r?(0,s.jsx)(u.A,{className:"h-4 w-4"}):"locked"===r?(0,s.jsx)(l.A,{className:"h-4 w-4"}):(0,s.jsx)(c.A,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:e.description}),e.quizScore&&(0,s.jsxs)("p",{className:"text-xs text-green-600",children:["Quiz Score: ",e.quizScore,"%"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isUnlocked&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n.$,{variant:"outline",size:"sm",disabled:!e.isUnlocked,children:[(0,s.jsx)(c.A,{className:"mr-1 h-3 w-3"}),e.isCompleted?"Review":"Study"]}),e.hasQuiz&&(0,s.jsxs)(n.$,{size:"sm",disabled:!e.isCompleted||e.quizCompleted,variant:e.quizCompleted?"outline":"default",children:[(0,s.jsx)(p.A,{className:"mr-1 h-3 w-3"}),e.quizCompleted?`Quiz: ${e.quizScore}%`:"Take Quiz"]})]}),!e.isUnlocked&&(0,s.jsxs)(i.E,{variant:"outline",children:[(0,s.jsx)(l.A,{className:"mr-1 h-3 w-3"}),"Locked"]})]})]},e.id)}),e.hasModuleQuiz&&(0,s.jsx)("div",{className:`mt-4 rounded-lg border-2 border-dashed p-3 ${"quiz-pending"===r?"border-yellow-300 bg-yellow-50":e.moduleQuizCompleted?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(p.A,{className:`h-4 w-4 ${e.moduleQuizCompleted?"text-green-600":"quiz-pending"===r?"text-yellow-600":"text-gray-400"}`}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"font-medium",children:["Module Quiz: ",e.name]}),e.moduleQuizScore&&(0,s.jsxs)("span",{className:"ml-2 text-sm text-green-600",children:["Score: ",e.moduleQuizScore,"%"]})]})]}),(0,s.jsx)(n.$,{size:"sm",disabled:"quiz-pending"!==r,variant:e.moduleQuizCompleted?"outline":"default",children:e.moduleQuizCompleted?"Review Quiz":"Take Module Quiz"})]})})]})})]},e.id)}),r.hasFinalExam&&(0,s.jsx)(a.Zp,{className:`border-2 ${r.finalExamUnlocked?"border-yellow-300 bg-yellow-50":"border-gray-300 bg-gray-50"}`,children:(0,s.jsx)(a.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:`flex h-12 w-12 items-center justify-center rounded-full ${r.finalExamCompleted?"bg-green-100 text-green-600":r.finalExamUnlocked?"bg-yellow-100 text-yellow-600":"bg-gray-100 text-gray-400"}`,children:r.finalExamCompleted?(0,s.jsx)(d.A,{className:"h-6 w-6"}):r.finalExamUnlocked?(0,s.jsx)(m.A,{className:"h-6 w-6"}):(0,s.jsx)(l.A,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Final Examination"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Comprehensive exam covering all course materials"}),!r.finalExamUnlocked&&(0,s.jsx)("p",{className:"mt-1 text-xs text-yellow-600",children:"Complete all modules to unlock"})]})]}),(0,s.jsx)(n.$,{size:"lg",disabled:!r.finalExamUnlocked,variant:r.finalExamCompleted?"outline":"default",children:r.finalExamCompleted?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"View Certificate"]}):r.finalExamUnlocked?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Take Final Exam"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Locked"]})})]})})})]})]})}},28354:e=>{"use strict";e.exports=require("util")},28832:(e,t,r)=>{Promise.resolve().then(r.bind(r,26954))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38006:(e,t,r)=>{Promise.resolve().then(r.bind(r,116))},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},47028:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>g,generateMetadata:()=>f,generateViewport:()=>v,metadata:()=>p});var a=r(63033),n=r(18188),i=r(5434),o=r(45188),d=r(67999),l=r(4590),c=r(23064),u=r(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...a},h="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;s=new Proxy(m,{apply:(e,t,r)=>{let s,a,n;try{let e=h?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}});let f=void 0,g=void 0,v=void 0,y=s},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63845:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(95500),a=r(56947),n=r(26052),i=r(13636),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["student",{children:["courses",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2796)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,97890)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\courses\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/student/courses/[id]/page",pathname:"/dashboard/student/courses/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66536:(e,t,r)=>{"use strict";r.d(t,{C1:()=>j,bL:()=>b});var s=r(93491),a=r(10158),n=r(90604),i=r(91754),o="Progress",[d,l]=(0,a.A)(o),[c,u]=d(o),p=s.forwardRef((e,t)=>{var r,s;let{__scopeProgress:a,value:o=null,max:d,getValueLabel:l=h,...u}=e;(d||0===d)&&!v(d)&&console.error((r=`${d}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=v(d)?d:100;null===o||y(o,p)||console.error((s=`${o}`,`Invalid prop \`value\` of value \`${s}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=y(o,p)?o:null,x=g(m)?l(m,p):void 0;return(0,i.jsx)(c,{scope:a,value:m,max:p,children:(0,i.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":g(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":f(m,p),"data-value":m??void 0,"data-max":p,...u,ref:t})})});p.displayName=o;var m="ProgressIndicator",x=s.forwardRef((e,t)=>{let{__scopeProgress:r,...s}=e,a=u(m,r);return(0,i.jsx)(n.sG.div,{"data-state":f(a.value,a.max),"data-value":a.value??void 0,"data-max":a.max,...s,ref:t})});function h(e,t){return`${Math.round(e/t*100)}%`}function f(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function v(e){return g(e)&&!isNaN(e)&&e>0}function y(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=m;var b=p,j=x},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80601:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(91754);r(93491);var a=r(16435),n=r(25758),i=r(82233);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...n}){let d=r?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},86857:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55732).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")},97890:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var a=r(63033),n=r(1472),i=r(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let s,a,n;try{let e=l?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student",componentType:"Layout",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}}):o;let c=void 0,u=void 0,p=void 0,m=s},98254:(e,t,r)=>{Promise.resolve().then(r.bind(r,97890))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634],()=>r(63845));module.exports=s})();
//# sourceMappingURL=page.js.map