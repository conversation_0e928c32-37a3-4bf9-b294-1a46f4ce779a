try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ab8d04ef-403a-49b7-982c-2e12955fa5d9",e._sentryDebugIdIdentifier="sentry-dbid-ab8d04ef-403a-49b7-982c-2e12955fa5d9")}catch(e){}(()=>{var e={};e.id=7893,e.ids=[7893],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6902:(e,t,r)=>{Promise.resolve().then(r.bind(r,28782))},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var a=r(91754);r(93491);var s=r(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26711:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},27930:(e,t,r)=>{Promise.resolve().then(r.bind(r,50149))},28354:e=>{"use strict";e.exports=require("util")},28782:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var s=r(63033),n=r(1472),i=r(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx","default");let d={...s},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;a="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let a,s,n;try{let e=l?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}}):o;let c=void 0,u=void 0,p=void 0,m=a},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40254:(e,t,r)=>{"use strict";r.d(t,{d:()=>s});var a=r(81012);function s(){return{toast:({title:e,description:t,variant:r="default"})=>{"destructive"===r?a.oR.error(e,{description:t}):a.oR.success(e,{description:t})}}}},40636:(e,t,r)=>{"use strict";r.d(t,{Table:()=>n,TableBody:()=>o,TableCell:()=>c,TableHead:()=>l,TableHeader:()=>i,TableRow:()=>d});var a=r(91754);r(93491);var s=r(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function o({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function d({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function l({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46814:(e,t,r)=>{Promise.resolve().then(r.bind(r,49540))},48161:e=>{"use strict";e.exports=require("node:os")},49540:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(91754);function s({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}r(93491),r(76328)},50149:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(91754),s=r(93491),n=r(9260),i=r(56682),o=r(59672),d=r(80601),l=r(40636),c=r(92681),u=r(41939),p=r(26711),m=r(88373),x=r(96196),h=r(84795),f=r(57e3),y=r(99462),b=r(93626),g=r(16041),v=r.n(g),j=r(40254);function A(){let[e,t]=(0,s.useState)(""),[r,g]=(0,s.useState)([]),[A,w]=(0,s.useState)(!0),[N,T]=(0,s.useState)(null),{toast:C}=(0,j.d)();(0,s.useRef)(!0);let S=(0,s.useCallback)(async(e="")=>{try{w(!0);let t=await fetch(`/api/institutions?search=${encodeURIComponent(e)}`),r=await t.json();r.success?g(r.data.institutions):C({title:"Error",description:r.error||"Failed to fetch institutions",variant:"destructive"})}catch(e){console.error("Error fetching institutions:",e),C({title:"Error",description:"Failed to fetch institutions",variant:"destructive"})}finally{w(!1)}},[]),_=async t=>{if(confirm("Are you sure you want to delete this institution? This action cannot be undone."))try{T(t);let r=await fetch(`/api/institutions/${t}`,{method:"DELETE"}),a=await r.json();a.success?(C({title:"Success",description:"Institution deleted successfully"}),S(e)):C({title:"Error",description:a.error||"Failed to delete institution",variant:"destructive"})}catch(e){console.error("Error deleting institution:",e),C({title:"Error",description:"Failed to delete institution",variant:"destructive"})}finally{T(null)}},q=e=>(0,a.jsx)(d.E,{variant:"paid"===e?"default":"destructive","data-sentry-element":"Badge","data-sentry-component":"getStatusBadge","data-sentry-source-file":"page.tsx",children:e}),P=e=>(0,a.jsx)(d.E,{variant:"enterprise"===e?"default":"pro"===e?"secondary":"outline","data-sentry-element":"Badge","data-sentry-component":"getPlanBadge","data-sentry-source-file":"page.tsx",children:e});return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"InstitutionsPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Institutions"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage all educational institutions on the platform"})]}),(0,a.jsx)(v(),{href:"/dashboard/admin/institutions/new","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(i.$,{"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4","data-sentry-element":"Plus","data-sentry-source-file":"page.tsx"}),"Add Institution"]})})]}),(0,a.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"All Institutions"}),(0,a.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"View and manage all registered institutions"})]}),(0,a.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"mb-4 flex items-center space-x-2",children:(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(p.A,{className:"text-muted-foreground absolute top-2.5 left-2 h-4 w-4","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(o.p,{placeholder:"Search institutions...",value:e,onChange:e=>t(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]})}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(l.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,a.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Type"}),(0,a.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Plan"}),(0,a.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Students/Teachers"}),(0,a.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,a.jsx)(l.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Due Date"}),(0,a.jsx)(l.TableHead,{className:"w-[70px]","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Actions"})]})}),(0,a.jsx)(l.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:A?(0,a.jsx)(l.TableRow,{children:(0,a.jsxs)(l.TableCell,{colSpan:7,className:"py-8 text-center",children:[(0,a.jsx)(m.A,{className:"mx-auto h-6 w-6 animate-spin"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2 text-sm",children:"Loading institutions..."})]})}):0===r.length?(0,a.jsx)(l.TableRow,{children:(0,a.jsxs)(l.TableCell,{colSpan:7,className:"py-8 text-center",children:[(0,a.jsx)(x.A,{className:"text-muted-foreground mx-auto mb-4 h-12 w-12"}),(0,a.jsx)("h3",{className:"mb-2 text-sm font-semibold",children:"No institutions found"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:e?"Try adjusting your search terms.":"Get started by adding a new institution."}),!e&&(0,a.jsx)(v(),{href:"/dashboard/admin/institutions/new",children:(0,a.jsxs)(i.$,{children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Add Institution"]})})]})}):r.map(e=>(0,a.jsxs)(l.TableRow,{children:[(0,a.jsx)(l.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-muted-foreground text-sm",children:["ID: ",e.id]})]})]})}),(0,a.jsx)(l.TableCell,{children:(0,a.jsx)(d.E,{variant:"outline",children:e.type})}),(0,a.jsx)(l.TableCell,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[P(e.subscription_plan),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:e.billing_cycle})]})}),(0,a.jsx)(l.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(h.A,{className:"text-muted-foreground h-3 w-3"}),(0,a.jsxs)("span",{className:"text-sm",children:[e.student_count,"/",e.teacher_count]})]})}),(0,a.jsx)(l.TableCell,{children:q(e.payment_status)}),(0,a.jsx)(l.TableCell,{children:(0,a.jsx)("span",{className:"text-sm",children:e.payment_due_date?new Date(e.payment_due_date).toLocaleDateString():"N/A"})}),(0,a.jsx)(l.TableCell,{children:(0,a.jsxs)(c.rI,{children:[(0,a.jsx)(c.ty,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",className:"h-8 w-8 p-0",disabled:N===e.id,children:N===e.id?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(f.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(c.SQ,{align:"end",children:[(0,a.jsx)(c._2,{asChild:!0,children:(0,a.jsxs)(v(),{href:`/dashboard/admin/institutions/${e.id}`,children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Edit"]})}),(0,a.jsxs)(c._2,{className:"text-red-600",onClick:()=>_(e.id),disabled:N===e.id,children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id))})]})})]})]})]})}},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>g,generateImageMetadata:()=>y,generateMetadata:()=>f,generateViewport:()=>b,metadata:()=>p});var s=r(63033),n=r(18188),i=r(5434),o=r(45188),d=r(67999),l=r(4590),c=r(23064),u=r(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let x={...s},h="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;a=new Proxy(m,{apply:(e,t,r)=>{let a,s,n;try{let e=h?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let f=void 0,y=void 0,b=void 0,g=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80601:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(91754);r(93491);var s=r(16435),n=r(25758),i=r(82233);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...n}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},81074:(e,t,r)=>{Promise.resolve().then(r.bind(r,92627))},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},92627:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var s=r(63033),n=r(1472),i=r(7688),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\page.tsx","default");let d={...s},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;a="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let a,s,n;try{let e=l?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin/institutions",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}}):o;let c=void 0,u=void 0,p=void 0,m=a},93861:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var a=r(95500),s=r(56947),n=r(26052),i=r(13636),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["admin",{children:["institutions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92627)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,28782)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\institutions\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/admin/institutions/page",pathname:"/dashboard/admin/institutions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94735:e=>{"use strict";e.exports=require("events")},99462:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634],()=>r(93861));module.exports=a})();
//# sourceMappingURL=page.js.map