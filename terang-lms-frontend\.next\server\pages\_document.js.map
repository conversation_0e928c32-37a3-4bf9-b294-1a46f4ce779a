{"version": 3, "file": "../pages/_document.js", "mappings": "ubAAA,kDCAA,qCCAA,qFCAA,mDCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"react/jsx-runtime\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/pages.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"@opentelemetry/api\"", "webpack://terang-lms-ui/external commonjs \"react\""], "sourcesContent": ["module.exports = require(\"react/jsx-runtime\");", "module.exports = require(\"path\");", "module.exports = require(\"next/dist/compiled/next-server/pages.runtime.prod.js\");", "module.exports = require(\"@opentelemetry/api\");", "module.exports = require(\"react\");"], "names": [], "sourceRoot": ""}