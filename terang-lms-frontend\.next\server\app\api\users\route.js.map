{"version": 3, "file": "../app/api/users/route.js", "mappings": "ydAEA,GAAI,CAACA,QAAQC,GAAG,CAACC,YAAY,CAC3B,CAD6B,KACvB,MAAU,iDAGlB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAACJ,QAAQC,GAAG,CAACC,YAAY,EAElC,eAAeG,EAAMC,CAA0B,CAAE,GAAGC,CAAa,EAEtE,OADe,MAAMJ,EAAIG,KAASC,EAEpC,yVCLO,eAAeC,EAAIC,CAAoB,EAC5C,GAAI,CACF,IAQIC,EACAC,EATE,CAQFD,QACAC,KATIC,CAAY,CAAE,CAAG,IAAIC,GAAAA,CAAIJ,EAAQK,GAAG,EACtCC,EAASH,EAAaI,EAAtBD,CAAyB,CAAC,MAAjBH,IACTK,EAAOL,EAAaI,GAAG,CAAC,MAAjBJ,EACPM,EAAgBN,EAAaI,GAAG,CAAC,KAAjCE,CAAgBN,WAChBO,EAAiBP,EAAaI,GAAG,CAAC,MAAjBJ,YACjBQ,EAAQC,GAARD,KAAQC,CAAST,EAAaI,GAAG,CAAC,MAAjBJ,IAA6B,MAC9CU,EAASD,IAATC,IAASD,CAAST,EAAaI,GAAG,CAAC,MAAjBJ,KAA8B,KAKtD,GAAIG,GAAUE,GAAiB,QAATA,GAAkBC,CAAlBD,EAAqD,QAAlBC,GAA2BA,UAA3BA,OAA2D,CAElH,IAAMK,EAAgB,CAAC,CAAC,EAAER,EAAOS,KAA3BD,MAAsC,GAAG,CAAC,CAAC,CAC3CE,EAASJ,QAAAA,CAASH,GAExB,GAAIC,EAAgB,CAClB,IAHsBD,CAAAA,CAGNG,KADdF,GACcE,CAASF,GAEzBT,EAAQ,GAARA,GAAcL,CAAAA,EAFWc,CAAAA,CAEXd,CAAAA,CAAK,CAAC;;;;;;;;;;;;;mFAauD,EAAEqB,EAAQ,KAARA;oCACjD,EAAEH,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;mCACQ,EAAEQ,EAAO,IAAPA;;;gBAGrB,EAAEL,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;mFAGiD,EAAEqB,EAAQ,KAARA;oCACjD,EAAEH,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;mCACQ,EAAEQ,EAAO,IAAPA;;QAE7B,CAAC,MAEDf,CADK,CACG,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;oCAaQ,EAAEkB,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;mCACQ,EAAEQ,EAAO,IAAPA;;gBAErB,EAAEL,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;oCAGE,EAAEkB,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;mCACQ,EAAEQ,EAAO,IAAPA;QAC7B,CAAC,MAEE,GAAIV,GAAUE,GAAiB,CAAjBA,OAAQA,EAAgB,CAE3C,CAF2BA,GAErBM,EAAgB,CAAC,CAAC,EAAER,EAAOS,KAA3BD,MAAsC,GAAG,CAAC,CAAC,CAEjD,GAAIJ,EAAgB,CAClB,IAAMO,EAAUL,KADdF,GACcE,CAASF,GAEzBT,EAAQ,GAARA,GAAcL,CAAAA,EAFWc,CAAAA,CAEXd,CAAAA,CAAK,CAAC;;;;;;;;;;;;;mFAauD,EAAEqB,EAAQ,KAARA;oCACjD,EAAEH,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;;;gBAGX,EAAEG,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;mFAGiD,EAAEqB,EAAQ,KAARA;oCACjD,EAAEH,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;;QAEnB,CAAC,MAEDP,CADK,CACG,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;oCAaQ,EAAEkB,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;;gBAEX,EAAEG,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAApBM,EAAoBN,CAAAA,CAAK,CAAC;;;oCAGE,EAAEkB,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;yBACrD,EAAEN,EAAK,EAALA;QACnB,CAAC,MAEE,GAAIF,EAAQ,CAEjB,GAFSA,CAEHQ,EAAgB,CAAC,CAAC,EAAER,EAAOS,KAA3BD,MAAsC,GAAG,CAAC,CAAC,CAEjDb,EAAQ,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;iCAaO,EAAEkB,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;;cAE7D,EAAEH,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;MAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAApBM,EAAoBN,CAAAA,CAAK,CAAC;;;iCAGC,EAAEkB,EAAc,WAAdA,aAAsC,EAAEA,EAAc,WAAdA;MACrE,CAAC,MACI,GAAIN,GAAiB,CAAjBA,MAAwB,CAAhBA,EAEjB,EAFiBA,CAEbE,EAAgB,CAClB,IAAMO,EAAUL,KADdF,GACcE,CAASF,GAEzBT,EAAQ,GAARA,GAAcL,CAAAA,EAFWc,CAAAA,CAEXd,CAAAA,CAAK,CAAC;;;;;;;;;;;;;mFAauD,EAAEqB,EAAQ,KAARA;yBAC5D,EAAET,EAAK,EAALA;;;;gBAIX,EAAEG,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAApBM,EAAoBN,CAAAA,CAAK,CAAC;;;mFAGiD,EAAEqB,EAAQ,KAARA;yBAC5D,EAAET,EAAK,EAALA;;;QAGnB,CAAC,MAEDP,CADK,CACG,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;yBAaH,EAAEY,EAAK,EAALA;;;gBAGX,EAAEG,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAApBM,EAAoBN,CAAAA,CAAK,CAAC;;;yBAGT,EAAEY,EAAK,EAALA;;QAEnB,CAAC,MAEE,GAAIC,GAAiBA,UAAjBA,GAAiBA,CAAgC,GAE1DR,EAAQ,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;;;cAeZ,EAAEe,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;MAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAApBM,EAAoBN,CAAAA,CAAK,CAAC;;;;MAI1B,CAAC,MACI,GAAIa,GAAmC,QAAlBA,EAAjBA,CAET,IAAMO,EAASJ,IAFWH,IAEXG,CAASH,GAExB,GAAIC,EAAgB,CAClB,IAHsBD,CAAAA,CAGNG,KADdF,GACcE,CAASF,GAEzBT,EAAQ,GAARA,GAAcL,CAAAA,EAAAA,CAFWc,CAEXd,CAAAA,CAAK,CAAC;;;;;;;;;;;;;mFAauD,EAAEqB,EAAQ,KAARA;mCAClD,EAAED,EAAO,IAAPA;;;gBAGrB,EAAEL,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAApBM,EAAoBN,CAAAA,CAAK,CAAC;;;mFAGiD,EAAEqB,EAAQ,KAARA;mCAClD,EAAED,EAAO,IAAPA;;QAE7B,CAAC,MAEDf,CADK,CACG,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;mCAaO,EAAEoB,EAAO,IAAPA;;gBAErB,EAAEL,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;QAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;mCAGC,EAAEoB,EAAO,IAAPA;QAC7B,CAAC,MAIHf,CAFK,CAEG,GAARA,GAAcL,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;;cAcZ,EAAEe,EAAM,GAANA,KAAc,EAAEE,EAAO,IAAPA;MAC1B,CAAC,CAEDX,EAAc,MAAMN,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;MAG1B,CAAC,CAGH,OAAOsB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTC,IAAAA,CAAM,OACJpB,EACAqB,GADArB,EACAqB,CAAOV,QAAAA,CAASV,CAAW,CAAC,EAAE,CAACoB,KAAK,QACpCX,EACAE,GADAF,KAEF,EACAY,OAAAA,CAAS,8BACX,EACF,CAAE,MAAOC,EAAO,CAEd,EAFOA,KACPC,OAAAA,CAAQD,KAAK,CAAC,mBAAoBA,GAC3BN,EAD2BM,CAAAA,WAC3BN,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTI,KAAAA,CAAO,2BACT,CACA,CAAEE,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeC,EAAK3B,CAAoB,EAC7C,GAAI,CAEF,GAAM,MAAE4B,CAAI,OAAEC,CAAK,UAAEC,CAAQ,CAAEtB,MAAI,eAAEC,CAAa,CAAE,CADvC,EAC0CsB,IAAAA,EAD5BZ,IAAI,CAAZnB,EAInB,GAAI,CAAC4B,GAAQ,CAARA,GAAkB,CAACE,GAAY,CAACtB,EACnC,EADsBsB,EAAmB,GAClCZ,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOI,KAAAA,CAAO,+CAA+C,CACxE,CAAEE,MAAAA,CAAQ,GAAI,GAMlB,GAAI,CADe,UACdM,mBAAWC,IAAI,CAACJ,GACnB,EADmBA,CAAAA,EAAQ,EACpBX,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOI,KAAAA,CAAO,uBAAuB,CAChD,CAAEE,MAAAA,CAAQ,GAAI,GASlB,GAAIQ,CAJiB,MAAMtC,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;yCACI,EAAEiC,EAAM,GAANA;KACvC,EAEiBM,MAAM,CAAG,EACxB,CAD2B,MACpBjB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOI,KAAAA,CAAO,uBAAuB,CAChD,CAAEE,MAAAA,CAAQ,GAAI,GAMlB,IAAMU,EAAiB,MAAMC,EAAAA,EAAAA,CAAAA,CAAvBD,GAAkC,CAACN,EADtB,IAIbQ,EAHmCR,EAAUS,EAG7CD,EAAe1C,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;QAQvB,EAAEgC,EAAK,EAALA;QACF,EAAEC,EAAM,GAANA;QACF,EAAEO,EAAe,YAAfA;QACF,EAAE5B,EAAK,EAALA;QACF,EAAEC,GAAiB,KAAK;;IAE5B,CAAC,CAED,OAAOS,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTC,IAAAA,CAAMiB,CAAM,CAAC,EAAE,CACff,OAAAA,CAAS,2BACX,EACF,CAAE,MAAOC,EAAO,CAEd,EAFOA,KACPC,OAAAA,CAAQD,KAAK,CAAC,qBAAsBA,GAC7BN,EAD6BM,CAAAA,WAC7BN,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOI,KAAAA,CAAO,wBAAwB,CACjD,CAAEE,MAAAA,CAAQ,GAAI,EAElB,CACF,CC5aA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAIH,EAAoB,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,YAAY,CAChC,OAAO,EACf,CAAO,CAAC,CAAC,KAAK,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAec,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,EAAYC,CAAf,MAA6C,EAA/B,KAAsC,EAEzD,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,wBACA,sBACA,iBACA,gCACA,CAAK,CACL,0IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,yBC5BA,6GCAA,oDCAA,qGCAA,mECAA,oDCAA,iDCAA,iDCAA,iDCAA,wGCAA,gECAA,kDCAA,iECAA,sDCAA,wDCAA,sDCAA,2CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,6GCAA,qDCAA,4DCAA,uDCAA,kECAA,uDCAA,mECAA,gDCAA,4DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/./src/lib/db/raw.ts", "webpack://terang-lms-ui/src/app/api/users/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?36e0", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nconst sql = neon(process.env.DATABASE_URL);\r\n\r\nexport async function query(text: TemplateStringsArray, ...params: any[]) {\r\n  const result = await sql(text, ...params);\r\n  return result;\r\n}\r\n", "import { NextRequest, NextResponse } from 'next/server';\r\nimport bcrypt from 'bcryptjs';\r\nimport { query } from '@/lib/db/raw';\r\nimport { ApiResponse } from '@/types/database';\r\n\r\n// GET /api/users - Get all users\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const { searchParams } = new URL(request.url);\r\n    const search = searchParams.get('search');\r\n    const role = searchParams.get('role');\r\n    const institutionId = searchParams.get('institutionId');\r\n    const excludeClassId = searchParams.get('excludeClassId');\r\n    const limit = parseInt(searchParams.get('limit') || '50');\r\n    const offset = parseInt(searchParams.get('offset') || '0');\r\n\r\n    let users;\r\n    let countResult;\r\n\r\n    if (search && role && role !== 'all' && institutionId && institutionId !== 'all' && institutionId !== 'unassigned') {\r\n      // All filters applied\r\n      const searchPattern = `%${search.toLowerCase()}%`;\r\n      const instId = parseInt(institutionId);\r\n      \r\n      if (excludeClassId) {\r\n        const classId = parseInt(excludeClassId);\r\n        \r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n            AND u.institution_id = ${instId}\r\n            AND ce.student_id IS NULL\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n            AND u.institution_id = ${instId}\r\n            AND ce.student_id IS NULL\r\n        `;\r\n      } else {\r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n            AND u.institution_id = ${instId}\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n            AND u.institution_id = ${instId}\r\n        `;\r\n      }\r\n    } else if (search && role && role !== 'all') {\r\n      // Search and role filter\r\n      const searchPattern = `%${search.toLowerCase()}%`;\r\n      \r\n      if (excludeClassId) {\r\n        const classId = parseInt(excludeClassId);\r\n        \r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n            AND ce.student_id IS NULL\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n            AND ce.student_id IS NULL\r\n        `;\r\n      } else {\r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})\r\n            AND u.role = ${role}\r\n        `;\r\n      }\r\n    } else if (search) {\r\n      // Search filter only\r\n      const searchPattern = `%${search.toLowerCase()}%`;\r\n      \r\n      users = await query`\r\n        SELECT\r\n          u.id,\r\n          u.name,\r\n          u.email,\r\n          u.role,\r\n          u.institution_id,\r\n          u.created_at,\r\n          u.updated_at,\r\n          i.name as institution_name,\r\n          i.type as institution_type\r\n        FROM users u\r\n        LEFT JOIN institutions i ON u.institution_id = i.id\r\n        WHERE LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern}\r\n        ORDER BY u.created_at DESC\r\n        LIMIT ${limit} OFFSET ${offset}\r\n      `;\r\n\r\n      countResult = await query`\r\n        SELECT COUNT(*) as total\r\n        FROM users u\r\n        WHERE LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern}\r\n      `;\r\n    } else if (role && role !== 'all') {\r\n      // Role filter only\r\n      if (excludeClassId) {\r\n        const classId = parseInt(excludeClassId);\r\n        \r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE u.role = ${role}\r\n            AND u.institution_id IS NOT NULL\r\n            AND ce.student_id IS NULL\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE u.role = ${role}\r\n            AND u.institution_id IS NOT NULL\r\n            AND ce.student_id IS NULL\r\n        `;\r\n      } else {\r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          WHERE u.role = ${role}\r\n            AND u.institution_id IS NOT NULL\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          WHERE u.role = ${role}\r\n            AND u.institution_id IS NOT NULL\r\n        `;\r\n      }\r\n    } else if (institutionId && institutionId === 'unassigned') {\r\n      // Unassigned users only\r\n      users = await query`\r\n        SELECT\r\n          u.id,\r\n          u.name,\r\n          u.email,\r\n          u.role,\r\n          u.institution_id,\r\n          u.created_at,\r\n          u.updated_at,\r\n          i.name as institution_name,\r\n          i.type as institution_type\r\n        FROM users u\r\n        LEFT JOIN institutions i ON u.institution_id = i.id\r\n        WHERE u.institution_id IS NULL\r\n        ORDER BY u.created_at DESC\r\n        LIMIT ${limit} OFFSET ${offset}\r\n      `;\r\n\r\n      countResult = await query`\r\n        SELECT COUNT(*) as total\r\n        FROM users u\r\n        WHERE u.institution_id IS NULL\r\n      `;\r\n    } else if (institutionId && institutionId !== 'all') {\r\n      // Institution filter only\r\n      const instId = parseInt(institutionId);\r\n      \r\n      if (excludeClassId) {\r\n        const classId = parseInt(excludeClassId);\r\n        \r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE u.institution_id = ${instId}\r\n            AND ce.student_id IS NULL\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}\r\n          WHERE u.institution_id = ${instId}\r\n            AND ce.student_id IS NULL\r\n        `;\r\n      } else {\r\n        users = await query`\r\n          SELECT\r\n            u.id,\r\n            u.name,\r\n            u.email,\r\n            u.role,\r\n            u.institution_id,\r\n            u.created_at,\r\n            u.updated_at,\r\n            i.name as institution_name,\r\n            i.type as institution_type\r\n          FROM users u\r\n          LEFT JOIN institutions i ON u.institution_id = i.id\r\n          WHERE u.institution_id = ${instId}\r\n          ORDER BY u.created_at DESC\r\n          LIMIT ${limit} OFFSET ${offset}\r\n        `;\r\n\r\n        countResult = await query`\r\n          SELECT COUNT(*) as total\r\n          FROM users u\r\n          WHERE u.institution_id = ${instId}\r\n        `;\r\n      }\r\n    } else {\r\n      // No filters\r\n      users = await query`\r\n        SELECT\r\n          u.id,\r\n          u.name,\r\n          u.email,\r\n          u.role,\r\n          u.institution_id,\r\n          u.created_at,\r\n          u.updated_at,\r\n          i.name as institution_name,\r\n          i.type as institution_type\r\n        FROM users u\r\n        LEFT JOIN institutions i ON u.institution_id = i.id\r\n        ORDER BY u.created_at DESC\r\n        LIMIT ${limit} OFFSET ${offset}\r\n      `;\r\n\r\n      countResult = await query`\r\n        SELECT COUNT(*) as total\r\n        FROM users u\r\n      `;\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: {\r\n        users,\r\n        total: parseInt(countResult[0].total),\r\n        limit,\r\n        offset\r\n      },\r\n      message: 'Users retrieved successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Get users error:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to retrieve users'\r\n      } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/users - Create new user\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const { name, email, password, role, institutionId } = body;\r\n\r\n    // Validate required fields\r\n    if (!name || !email || !password || !role) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Name, email, password, and role are required' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Validate email format\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(email)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid email format' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if email already exists\r\n    const existingUser = await query`\r\n      SELECT id FROM users WHERE email = ${email}\r\n    `;\r\n\r\n    if (existingUser.length > 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Email already exists' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Hash password\r\n    const saltRounds = 10;\r\n    const hashedPassword = await bcrypt.hash(password, saltRounds);\r\n\r\n    // Create user\r\n    const result = await query`\r\n      INSERT INTO users (\r\n        name,\r\n        email,\r\n        password,\r\n        role,\r\n        institution_id\r\n      ) VALUES (\r\n        ${name},\r\n        ${email},\r\n        ${hashedPassword},\r\n        ${role},\r\n        ${institutionId || null}\r\n      ) RETURNING id, name, email, role, institution_id, created_at, updated_at\r\n    `;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: result[0],\r\n      message: 'User created successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Create user error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Failed to create user' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/users',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\users\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["process", "env", "DATABASE_URL", "sql", "neon", "query", "text", "params", "GET", "request", "users", "count<PERSON><PERSON><PERSON>", "searchParams", "URL", "url", "search", "get", "role", "institutionId", "excludeClassId", "limit", "parseInt", "offset", "searchPattern", "toLowerCase", "instId", "classId", "NextResponse", "json", "success", "data", "total", "message", "error", "console", "status", "POST", "name", "email", "password", "body", "emailRegex", "test", "existingUser", "length", "hashedPassword", "bcrypt", "result", "saltRounds", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}