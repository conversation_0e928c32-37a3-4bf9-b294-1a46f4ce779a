{"version": 3, "file": "../app/dashboard/admin/page.js", "mappings": "ubAAA,sGCAA,uCAAiK,iBCAjK,uCAAmK,uBCAnK,gKCEA,SAASA,EAAK,WACZC,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,OAAOH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,CAClBP,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,WACjBR,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,aAAaH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,CACvBT,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,mBAAmBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBV,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBX,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,yBC3CA,+FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,yBCNjN,kECAA,2GCAA,qDCAA,6UCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,QACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAiK,CAoBrL,gIAES,EACF,CACP,CACA,QA1BA,IAAsB,uCAAmK,CA0BzL,mIAGA,CACO,CACP,CACA,QAjCA,IAAsB,uCAA4J,CAiClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAlDA,IAAsB,sCAAiJ,CAkDvK,gHACA,gBAlDA,IAAsB,uCAAuJ,CAkD7K,sHACA,aAlDA,IAAsB,uCAAoJ,CAkD1K,mHACA,WAlDA,IAAsB,4CAAgF,CAkDtG,+CACA,cAlDA,IAAsB,4CAAmF,CAkDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,mIAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,6BACA,4BAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC7FD,4DCmBI,sBAAsB,0rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAAE,OAAhC,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,kBAAkB,CAClC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CACJ,CAAG,CAAC,CA/BoBM,EAoCnB,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,gDCAA,wGCAA,gECAA,kDCAA,iECAA,sDCAA,wDCAA,gERmBI,sBAAsB,srBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CACrC,IAAI,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,kBAAkB,CAClC,aAAa,CAAE,MAAM,mBACrB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CA7BLA,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IShF9B,iDCAA,uCAAmK,wBCAnK,+CCAA,uCAAiK,4ECIlJ,SAASC,EAAY,CAClCC,UAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,+NCEe,SAASC,IACtB,GAAM,CAACC,EAAoBC,EAAsB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EACxE,CAACC,EAASC,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAyBvC,MAAO,WAAChB,MAAAA,CAAIF,UAAU,YAAYK,wBAAsB,iBAAiBC,0BAAwB,qBAC7F,WAACJ,MAAAA,CAAIF,UAAU,8CACb,WAACE,MAAAA,WACC,UAACmB,KAAAA,CAAGrB,UAAU,6CAAoC,0BAGlD,UAACsB,IAAAA,CAAEtB,UAAU,iCAAwB,qEAIvC,UAACuB,IAAIA,CAACC,KAAK,oCAAoCC,GAA1CF,mBAA8D,OAAOjB,0BAAwB,oBAChG,WAACoB,EAAAA,CAAMA,CAAAA,CAACD,sBAAoB,SAASnB,0BAAwB,qBAC3D,UAACqB,EAAAA,CAAIA,CAAAA,CAAC3B,UAAU,eAAeyB,sBAAoB,OAAOnB,0BAAwB,aAAa,0BAOrG,WAACJ,MAAAA,CAAIF,UAAU,qDACb,WAACD,EAAAA,EAAIA,CAAAA,CAAC0B,sBAAoB,OAAOnB,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACP,UAAU,4DAA4DyB,sBAAoB,aAAanB,0BAAwB,qBACzI,UAACE,EAAAA,EAASA,CAAAA,CAACR,UAAU,sBAAsByB,sBAAoB,YAAYnB,0BAAwB,oBAAW,uBAG9G,UAACsB,EAAAA,CAASA,CAAAA,CAAC5B,UAAU,gCAAgCyB,sBAAoB,YAAYnB,0BAAwB,gBAE/G,WAACI,EAAAA,EAAWA,CAAAA,CAACe,sBAAoB,cAAcnB,0BAAwB,qBACrE,UAACJ,MAAAA,CAAIF,UAAU,8BAjDJ,CAiD0B6B,IACrC,EAD2CC,CAC3C,OAACR,IAAAA,CAAEtB,IADyD,MAC/C,yCAAgC,6BAIjD,WAACD,EAAAA,EAAIA,CAAAA,CAAC0B,sBAAoB,OAAOnB,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACP,UAAU,4DAA4DyB,sBAAoB,aAAanB,0BAAwB,qBACzI,UAACE,EAAAA,EAASA,CAAAA,CAACR,UAAU,sBAAsByB,sBAAoB,YAAYnB,0BAAwB,oBAAW,gBAC9G,UAACyB,EAAAA,CAAKA,CAAAA,CAAC/B,UAAU,gCAAgCyB,sBAAoB,QAAQnB,0BAAwB,gBAEvG,WAACI,EAAAA,EAAWA,CAAAA,CAACe,sBAAoB,cAAcnB,0BAAwB,qBACrE,UAACJ,MAAAA,CAAIF,UAAU,8BACZ6B,MAAMG,UAAU,IAAe,KAElC,UAACV,IAAAA,CAAEtB,UAAU,yCAAgC,+BAMjD,WAACD,EAAAA,EAAIA,CAAAA,CAAC0B,sBAAoB,OAAOnB,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACP,UAAU,4DAA4DyB,sBAAoB,aAAanB,0BAAwB,qBACzI,UAACE,EAAAA,EAASA,CAAAA,CAACR,UAAU,sBAAsByB,sBAAoB,YAAYnB,0BAAwB,oBAAW,kBAC9G,UAAC2B,EAAAA,CAAUA,CAAAA,CAACjC,UAAU,gCAAgCyB,sBAAoB,aAAanB,0BAAwB,gBAEjH,WAACI,EAAAA,EAAWA,CAAAA,CAACe,sBAAoB,cAAcnB,0BAAwB,qBACrE,WAACJ,MAAAA,CAAIF,UAAU,+BAAqB,MAC7B6B,CAAAA,MAAMK,CAAmC,OAEhD,IAFyB,CAEzB,EAF4B,CAE5B,EAACZ,IAAAA,CAAEtB,CAFkCmC,OAAO,CAAC,CAEhC,yCAAgC,+BAMjD,WAACpC,EAAAA,EAAIA,CAAAA,CAAC0B,sBAAoB,OAAOnB,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACP,UAAU,4DAA4DyB,sBAAoB,aAAanB,0BAAwB,qBACzI,UAACE,EAAAA,EAASA,CAAAA,CAACR,UAAU,sBAAsByB,sBAAoB,YAAYnB,0BAAwB,oBAAW,yBAG9G,UAAC8B,EAAAA,CAAUA,CAAAA,CAACpC,UAAU,gCAAgCyB,sBAAoB,aAAanB,0BAAwB,gBAEjH,WAACI,EAAAA,EAAWA,CAAAA,CAACe,sBAAoB,cAAcnB,0BAAwB,qBACrE,UAACJ,MAAAA,CAAIF,UAAU,8BAzFF,CA0FV6B,IAEH,EAFSQ,CAET,OAACf,IAAAA,CAAEtB,MAFyB,IAEf,yCAAgC,gCAMnD,WAACD,EAAAA,EAAIA,CAAAA,CAAC0B,sBAAoB,OAAOnB,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACkB,sBAAoB,aAAanB,0BAAwB,qBACnE,UAACE,EAAAA,EAASA,CAAAA,CAACiB,sBAAoB,YAAYnB,0BAAwB,oBAAW,wBAC9E,UAACG,EAAAA,EAAeA,CAAAA,CAACgB,sBAAoB,kBAAkBnB,0BAAwB,oBAAW,iDAI5F,WAACI,EAAAA,EAAWA,CAAAA,CAACe,sBAAoB,cAAcnB,0BAAwB,qBACrE,UAACJ,MAAAA,CAAIF,UAAU,qBACZmB,EAAU,WAACjB,MAAAA,CAAIF,UAAU,kDACtB,UAACsC,EAAAA,CAAOA,CAAAA,CAACtC,UAAU,8BACnB,UAACuC,OAAAA,UAAK,sCACCvB,EAAmBwB,GAAG,CAACC,GAAe,WAACvC,MAAAA,CAAyBF,UAAU,oEAC/E,WAACE,MAAAA,CAAIF,UAAU,sBACb,UAACsB,IAAAA,CAAEtB,UAAU,uBAAeyC,EAAYC,IAAI,GAC5C,WAACxC,MAAAA,CAAIF,UAAU,wCACb,UAAC2C,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,mBAAWH,EAAYI,IAAI,GAC1C,UAACF,EAAAA,CAAKA,CAAAA,CAACC,QAA2C,eAAlCH,EAAYK,iBAAiB,CAAoB,UAAY,qBAC1EL,EAAYK,iBAAiB,GAEhC,WAACP,OAAAA,CAAKvC,UAAU,0CACbyC,EAAYM,aAAa,CAAC,qBAIjC,WAAC7C,MAAAA,CAAIF,UAAU,wCACb,UAAC2C,EAAAA,CAAKA,CAAAA,CAACC,QAASH,WAAYO,cAAc,CAAc,UAAY,uBACjEP,EAAYO,cAAc,GAE7B,UAACzB,IAAIA,CAACC,KAAM,CAAC,8BAA8B,EAAEiB,EAAYQ,EAAE,EAAtD1B,UACH,UAACG,EAAAA,CAAMA,CAAAA,CAACkB,QAAQ,UAAUM,KAAK,cAAK,gBAlBaT,EAAYQ,EAAE,KAyB3E,UAAC/C,MAAAA,CAAIF,UAAU,gBACb,UAACuB,IAAIA,CAACC,KAAK,gCAAgCC,OAAtCF,eAA0D,OAAOjB,0BAAwB,oBAC5F,UAACoB,EAAAA,CAAMA,CAAAA,CAACkB,QAAQ,UAAU5C,UAAU,SAASyB,sBAAoB,SAASnB,0BAAwB,oBAAW,sCAQ3H,0BC1KA,sECAA,mDCAA,mECAA,yDCAA,iEnBmBI,sBAAsB,gMoBbb6C,EAAqB,CAChCC,KADWD,CACJ,wBACPE,WAAAA,CAAa,6BACf,EACe,eAAeC,EAAgB,UAC5CxC,CAAQ,CAGT,CAJ6BwC,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkCI,KAAAA,GAAU,OAChE,MAAOC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAKpC,qBAAAA,CAAoB,OAAOpB,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAwD,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBN,WAAAA,CAAaA,EAAahC,SAAbgC,YAAahC,CAAoB,kBAAkBnB,yBAAAA,CAAwB,uBACvGsD,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAWvC,qBAAAA,CAAoB,aAAanB,yBAAAA,CAAwB,eACrEwD,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAaxC,qBAAAA,CAAoB,eAAenB,yBAAAA,CAAwB,uBACvEsD,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAOzC,qBAAAA,CAAoB,SAASnB,yBAAAA,CAAwB,eAE7DsD,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAKnE,SAAAA,CAAU,kDACbc,QAAAA,CAAAA,WAMb,CpBvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAKC,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IqBhF9B,6GCAA,qDCAA,4DCAA,uDCAA,2DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,yBCNjN,uDCAA,sDCAA,iDCAA,0DCAA,gJCIA,IAAMsD,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACR1B,QAAS,CACP2B,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACf/B,QAAS,SACX,CACF,GACA,SAASD,EAAM,WACb3C,CAAS,SACT4C,CAAO,SACPgC,GAAU,CAAK,CACf,GAAG3E,EAGJ,EACC,IAAM4E,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAK1E,YAAU,QAAQH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACgE,EAAc,SACzDxB,CACF,GAAI5C,GAAa,GAAGC,CAAK,CAAEwB,sBAAoB,OAAOpB,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/?429e", "webpack://terang-lms-ui/?3e35", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/?b2dd", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/?10f1", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/?d57b", "webpack://terang-lms-ui/./src/app/dashboard/admin/layout.tsx", "webpack://terang-lms-ui/./src/app/dashboard/admin/page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/./src/components/ui/badge.tsx", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\");\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\n", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/admin/page\",\n        pathname: \"/dashboard/admin\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"util\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/admin',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/admin',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/admin',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/admin',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\n", "module.exports = require(\"node:os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\");\n", "'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function AdminLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has super_admin role\n    requireRole('super_admin');\n  }, []);\n  return <>{children}</>;\n}", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Building2, Users, CreditCard, TrendingUp, Plus, Loader2 } from 'lucide-react';\nimport Link from 'next/link';\ninterface Institution {\n  id: number;\n  name: string;\n  type: string;\n  subscription_plan: string;\n  student_count: number;\n  payment_status: string;\n}\nexport default function AdminDashboard() {\n  const [recentInstitutions, setRecentInstitutions] = useState<Institution[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  // Mock data - in real app, this would come from API\n  const stats = {\n    totalInstitutions: 24,\n    totalUsers: 1250,\n    totalRevenue: 125000000,\n    activeSubscriptions: 22\n  };\n  useEffect(() => {\n    const fetchRecentInstitutions = async () => {\n      try {\n        const response = await fetch('/api/institutions?limit=3');\n        const data = await response.json();\n        if (data.success) {\n          setRecentInstitutions(data.data.institutions);\n        }\n      } catch (error) {\n        console.error('Failed to fetch recent institutions', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchRecentInstitutions();\n  }, []);\n  return <div className='space-y-6' data-sentry-component=\"AdminDashboard\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>\r\n            Super Admin Dashboard\r\n          </h1>\r\n          <p className='text-muted-foreground'>\r\n            Manage institutions, subscriptions, and system-wide analytics\r\n          </p>\r\n        </div>\r\n        <Link href='/dashboard/admin/institutions/new' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <Plus className='mr-2 h-4 w-4' data-sentry-element=\"Plus\" data-sentry-source-file=\"page.tsx\" />\r\n            Add Institution\r\n          </Button>\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">\r\n              Total Institutions\r\n            </CardTitle>\r\n            <Building2 className='text-muted-foreground h-4 w-4' data-sentry-element=\"Building2\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>{stats.totalInstitutions}</div>\r\n            <p className='text-muted-foreground text-xs'>+2 from last month</p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Total Users</CardTitle>\r\n            <Users className='text-muted-foreground h-4 w-4' data-sentry-element=\"Users\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>\r\n              {stats.totalUsers.toLocaleString()}\r\n            </div>\r\n            <p className='text-muted-foreground text-xs'>\r\n              +180 from last month\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Total Revenue</CardTitle>\r\n            <CreditCard className='text-muted-foreground h-4 w-4' data-sentry-element=\"CreditCard\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>\r\n              Rp {(stats.totalRevenue / 1000000).toFixed(1)}M\r\n            </div>\r\n            <p className='text-muted-foreground text-xs'>\r\n              +12% from last month\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n            <CardTitle className='text-sm font-medium' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">\r\n              Active Subscriptions\r\n            </CardTitle>\r\n            <TrendingUp className='text-muted-foreground h-4 w-4' data-sentry-element=\"TrendingUp\" data-sentry-source-file=\"page.tsx\" />\r\n          </CardHeader>\r\n          <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n            <div className='text-2xl font-bold'>\r\n              {stats.activeSubscriptions}\r\n            </div>\r\n            <p className='text-muted-foreground text-xs'>+1 from last month</p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Recent Institutions */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Recent Institutions</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\r\n            Latest institutions added to the platform\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <div className='space-y-4'>\r\n            {loading ? <div className='flex items-center justify-center py-8'>\r\n                <Loader2 className='mr-2 h-6 w-6 animate-spin' />\r\n                <span>Loading recent institutions...</span>\r\n              </div> : recentInstitutions.map(institution => <div key={institution.id} className='flex items-center justify-between rounded-lg border p-4'>\r\n                  <div className='space-y-1'>\r\n                    <p className='font-medium'>{institution.name}</p>\r\n                    <div className='flex items-center space-x-2'>\r\n                      <Badge variant='outline'>{institution.type}</Badge>\r\n                      <Badge variant={institution.subscription_plan === 'enterprise' ? 'default' : 'secondary'}>\r\n                        {institution.subscription_plan}\r\n                      </Badge>\r\n                      <span className='text-muted-foreground text-sm'>\r\n                        {institution.student_count} students\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                  <div className='flex items-center space-x-2'>\r\n                    <Badge variant={institution.payment_status === 'paid' ? 'default' : 'destructive'}>\r\n                      {institution.payment_status}\r\n                    </Badge>\r\n                    <Link href={`/dashboard/admin/institutions/${institution.id}`}>\r\n                      <Button variant='outline' size='sm'>\r\n                        View\r\n                      </Button>\r\n                    </Link>\r\n                  </div>\r\n                </div>)}\r\n          </div>\r\n          <div className='mt-4'>\r\n            <Link href='/dashboard/admin/institutions' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n              <Button variant='outline' className='w-full' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                View All Institutions\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["Card", "className", "props", "div", "data-slot", "cn", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "serverComponentModule.default", "AdminLayout", "children", "AdminDashboard", "recentInstitutions", "setRecentInstitutions", "useState", "loading", "setLoading", "h1", "p", "Link", "href", "data-sentry-element", "<PERSON><PERSON>", "Plus", "Building2", "stats", "totalInstitutions", "Users", "totalUsers", "CreditCard", "totalRevenue", "toFixed", "TrendingUp", "activeSubscriptions", "Loader2", "span", "map", "institution", "name", "Badge", "variant", "type", "subscription_plan", "student_count", "payment_status", "id", "size", "metadata", "title", "description", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "value", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "badgeVariants", "cva", "variants", "default", "secondary", "destructive", "outline", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot"], "sourceRoot": ""}