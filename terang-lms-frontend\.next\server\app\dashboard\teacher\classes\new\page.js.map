{"version": 3, "file": "../app/dashboard/teacher/classes/new/page.js", "mappings": "qbAAA,oICmBM,MAAY,cAAiB,aAhBC,CAgBY,CAf7C,MAAQ,EAAE,EAAG,CAAkB,oBAAK,SAAU,EAC/C,CAAC,MAAQ,EAAE,EAAG,CAAY,cAAK,SAAU,EAC3C,kBCNA,uCAAiL,wBCAjL,0ECoBM,MAAS,cAAiB,UAjBI,CAClC,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,UAAY,EAAE,OAAQ,CAAiB,mBAAK,SAAU,EACvD,CAAC,OAAQ,CAAE,GAAI,CAAM,OAAI,CAAM,OAAI,GAAK,IAAI,IAAM,KAAK,SAAU,EACnE,qICLA,SAASA,EAAK,CACZC,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,OAAOH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBP,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,WACjBR,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,aAAaH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,mBAAmBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBV,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBX,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,8FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,4ECFlM,SAASM,EAAc,UACpCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,2CCdA,mECAA,0GCAA,8HCKA,SAASC,EAAM,WACbd,CAAS,CACT,GAAGC,EAC8C,EACjD,MAAO,UAACc,EAAAA,CAAmB,EAACZ,YAAU,QAAQH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sNAAuNJ,GAAa,GAAGC,CAAK,CAAEe,sBAAoB,sBAAsBX,wBAAsB,QAAQC,0BAAwB,aAC5Y,yBCVA,iDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,uDCAA,+CCAA,uCAAqK,yBCArK,sECAA,oDCAA,kECAA,yDCAA,qHCEA,SAASW,EAAS,WAChBjB,CAAS,CACT,GAAGC,EAC8B,EACjC,MAAO,UAACiB,WAAAA,CAASf,YAAU,WAAWH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,WAAWC,0BAAwB,gBAC7kB,oCEYI,sBAAsB,gMDbba,EAAqB,CAChCC,KADWD,CACJ,wBACPE,WAAAA,CAAa,6BACf,EACe,eAAeC,EAAgB,UAC5CT,CAAQ,CAGT,CAJ6BS,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkCI,KAAAA,GAAU,OAChE,MAAOC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAKb,qBAAAA,CAAoB,OAAOX,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAwB,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBN,WAAAA,CAAaA,EAAaT,SAAbS,YAAaT,CAAoB,kBAAkBV,yBAAAA,CAAwB,uBACvGsB,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAWhB,qBAAAA,CAAoB,aAAaV,yBAAAA,CAAwB,eACrEwB,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAajB,qBAAAA,CAAoB,eAAeV,yBAAAA,CAAwB,uBACvEsB,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAOlB,qBAAAA,CAAoB,SAASV,yBAAAA,CAAwB,eAE7DsB,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAKnC,SAAAA,CAAU,kDACba,QAAAA,CAAAA,WAMb,CCvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACpC,KAAM,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAOjB,IAAC,EAOF,OAEE,EAOF,KAhBkB,EAkBhB,EAOF,OAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,uHDmBI,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,mBACvB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,CA/BoBuB,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,gEEnExB,EAAc,aAAqC,CAAC,EAAO,IAE7D,UAAC,IAAS,CAAC,MAAV,CACE,GAAG,EACJ,IAAK,EACL,YAAa,IAEI,EAAM,OACV,QAAQ,iCAAiC,EAAG,EAEvD,EAAM,cAAc,GAEhB,CAAC,CAFoB,CAEd,kBAAoB,EAAM,OAAS,EAAG,GAAM,eAAe,EACxE,KAKN,EAAM,YAxBO,EAwBO,MAIpB,IAAM,EAAO,mBCnCb,uCAAiL,yBCAjL,4EC0BM,MAAO,cAAiB,QAvBM,CAClC,CACE,OACA,CACE,CAAG,sGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAA6C,+CAAK,SAAU,EAC1E,CAAC,MAAQ,EAAE,EAAG,CAA0B,4BAAK,SAAU,EACzD,0BCbA,4DCAA,wDCAA,0DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,yBCNjN,uDCAA,mVCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,UACA,CACA,UACA,MACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,uCAAiL,CA0BrM,gJAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA5CA,IAAsB,uCAAqK,CA4C3L,qIAGA,CACO,CACP,CACA,QAnDA,IAAsB,uCAA4J,CAmDlL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApEA,IAAsB,sCAAiJ,CAoEvK,gHACA,gBApEA,IAAsB,uCAAuJ,CAoE7K,sHACA,aApEA,IAAsB,uCAAoJ,CAoE1K,mHACA,WApEA,IAAsB,4CAAgF,CAoEtG,+CACA,cApEA,IAAsB,4CAAmF,CAoEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,mJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,2CACA,0CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC/GD,iDCAA,2DCAA,oDCAA,uCAAqK,uPCctJ,SAASC,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACC,EAAUC,EAAY,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACvCG,KAAM,GACNxB,YAAa,EACf,GACM,CAACyB,EAAYC,EAAc,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MACpD,CAACM,EAAcC,EAAgB,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC1D,CAACQ,EAAaC,EAAe,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzCU,EAAoB,MAAOC,IAC/B,GAAI,CACFF,GAAe,GACf,IAAMR,EAAW,IAAIW,SACrBX,EAASY,MAAM,CAAC,OAAQF,GACxB,IAAMG,EAAW,MAAMC,MAAM,cAAe,CAC1CC,OAAQ,OACRC,KAAMhB,CACR,GACA,GAAI,CAACa,EAASI,EAAE,CACd,CADgB,KACV,MAAU,0BAGlB,MAAOC,CADM,MAAML,EAASM,IAAI,IACpBC,GAAG,CACf,MAAOC,EAAO,CAGd,OAFAC,QAAQD,KAAK,CAAC,yBAA0BA,GACxCE,EAAAA,EAAKA,CAACF,KAAK,CAAC,0BACL,IACT,QAAU,CACRb,GAAe,EACjB,CACF,EACMgB,EAAe,MAAOC,IAC1BA,EAAEC,cAAc,GAChB5B,GAAa,GACb,GAAI,CACF,IAAM6B,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAChC,GAAI,CAACF,EAAM,YACTJ,EAAAA,EAAKA,CAACF,KAAK,CAAC,mCAGd,GAAI,CAACM,EAAKG,aAAa,CAAE,YACvBP,EAAAA,EAAKA,CAACF,KAAK,CAAC,4DAGd,GAAI,CAACrB,EAASE,IAAI,CAAC6B,IAAI,GAAI,YACzBR,EAAAA,EAAKA,CAACF,KAAK,CAAC,0BAGd,IAAIW,EAAkB,KAGtB,GAAI7B,GAEE,CAAC6B,CADLA,EAAkB,KADJ,CACUvB,EAAkBN,EAAAA,EAExC,EADoB,KAIxB,CAHY,GAGNU,EAAW,MAAMC,MAAM,eAAgB,CAHA,OAInC,OACRmB,QAAS,CACP,eAAgB,kBAClB,EACAjB,KAAMkB,KAAKC,SAAS,CAAC,CACnBjC,KAAMF,EAASE,IAAI,CAAC6B,IAAI,GACxBrD,YAAasB,EAAStB,WAAW,CAACqD,IAAI,GACtCK,UAAWT,EAAKU,EAAE,CAClBP,cAAeH,EAAKG,aAAa,CACjCQ,aAAcN,CAChB,EACF,GACMd,EAAO,MAAML,EAASM,IAAI,GAC5BD,EAAKqB,OAAO,EAAE,EAChBhB,EAAKA,CAACgB,OAAO,CAAC,+BACd5C,EAAO6C,IAAI,CAAC,+BAEZjB,EAAAA,EAAKA,CAACF,KAAK,CAACH,EAAKG,KAAK,EAAI,yBAE9B,CAAE,MAAOA,EAAO,CACdC,QAAQD,KAAK,CAAC,wBAAyBA,GACvCE,EAAAA,EAAKA,CAACF,KAAK,CAAC,yBACd,QAAU,CACRvB,EAAa,GACf,CACF,EACM2C,EAAoB,CAACC,EAAe1D,KACxCiB,EAAY0C,GAAS,EACnB,EADmB,CAChBA,CAAI,CACP,CAACD,EAAM,CAAE1D,CACX,GACF,EA6BA,MAAO,WAACzB,MAAAA,CAAIF,UAAU,YAAYK,wBAAsB,eAAeC,0BAAwB,qBAC3F,WAACJ,MAAAA,CAAIF,UAAU,wCACb,UAACuF,IAAIA,CAACC,KAAK,6BAA6BxE,UAAnCuE,YAAuD,OAAOjF,0BAAwB,oBACzF,WAACmF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUC,KAAK,KAAK3E,sBAAoB,SAASV,0BAAwB,qBACvF,UAACsF,EAAAA,CAASA,CAAAA,CAAC5F,UAAU,eAAegB,sBAAoB,YAAYV,0BAAwB,aAAa,YAI7G,WAACJ,MAAAA,WACC,UAAC2F,KAAAA,CAAG7F,UAAU,6CAAoC,qBAGlD,UAAC8F,IAAAA,CAAE9F,UAAU,iCAAwB,uDAMzC,WAACD,EAAAA,EAAIA,CAAAA,CAACiB,sBAAoB,OAAOV,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACS,sBAAoB,aAAaV,0BAAwB,qBACnE,UAACE,EAAAA,EAASA,CAAAA,CAACQ,sBAAoB,YAAYV,0BAAwB,oBAAW,kBAC9E,UAACG,EAAAA,EAAeA,CAAAA,CAACO,sBAAoB,kBAAkBV,0BAAwB,oBAAW,qDAI5F,UAACI,EAAAA,EAAWA,CAAAA,CAACM,sBAAoB,cAAcV,0BAAwB,oBACrE,WAACyF,OAAAA,CAAKC,SAAU7B,EAAcnE,UAAU,sBACtC,WAACE,MAAAA,CAAIF,UAAU,sBACb,WAACE,MAAAA,CAAIF,UAAU,sBACb,UAACc,EAAAA,CAAKA,CAAAA,CAACmF,QAAQ,OAAOjF,sBAAoB,QAAQV,0BAAwB,oBAAW,eACrF,UAAC4F,EAAAA,CAAKA,CAAAA,CAAClB,GAAG,OAAOrD,MAAOgB,EAASE,IAAI,CAAEsD,SAAU/B,GAAKgB,EAAkB,OAAQhB,EAAEgC,MAAM,CAACzE,KAAK,EAAG0E,YAAY,8BAA8BC,QAAQ,IAACtF,sBAAoB,QAAQV,0BAAwB,aACxM,UAACwF,IAAAA,CAAE9F,UAAU,yCAAgC,uEAM/C,WAACE,MAAAA,CAAIF,UAAU,sBACb,UAACc,EAAAA,CAAKA,CAAAA,CAACmF,QAAQ,cAAcjF,sBAAoB,QAAQV,0BAAwB,oBAAW,gBAC5F,UAACW,EAAAA,CAAQA,CAAAA,CAAC+D,GAAG,cAAcrD,MAAOgB,EAAStB,WAAW,CAAE8E,SAAU/B,GAAKgB,EAAkB,cAAehB,EAAEgC,MAAM,CAACzE,KAAK,EAAG0E,YAAY,oDAAoDE,KAAM,EAAGvF,sBAAoB,WAAWV,0BAAwB,aACzP,UAACwF,IAAAA,CAAE9F,UAAU,yCAAgC,6DAK/C,WAACE,MAAAA,CAAIF,UAAU,sBACb,UAACc,EAAAA,CAAKA,CAAAA,CAACmF,QAAQ,aAAajF,sBAAoB,QAAQV,0BAAwB,oBAAW,gBAC3F,UAACJ,MAAAA,CAAIF,UAAU,qBACZgD,EAAe,WAAC9C,MAAAA,CAAIF,UAAU,qCAC3B,UAACwG,EAAAA,OAAKA,CAAAA,CAACC,IAAKzD,EAAc0D,IAAI,gBAAgBC,MAAO,IAAKC,OAAQ,IAAK5G,UAAU,wCACjF,UAACyF,EAAAA,CAAMA,CAAAA,CAACoB,KAAK,SAASnB,QAAQ,cAAcC,KAAK,KAAK3F,UAAU,yBAAyB8G,QAtDzF,CAsDkGC,IArDpHhE,EAAc,MACdE,EAAgB,KAClB,WAoDsB,UAAC+D,EAAAA,CAACA,CAAAA,CAAChH,UAAU,iBAER,WAACE,MAAAA,CAAIF,UAAU,8EACtB,UAACiH,EAAAA,CAAMA,CAAAA,CAACjH,UAAU,oCAClB,WAACE,MAAAA,CAAIF,UAAU,iBACb,UAACc,EAAAA,CAAKA,CAAAA,CAACmF,QAAQ,aAAajG,UAAU,+JAAsJ,iBAG5L,UAACkG,EAAAA,CAAKA,CAAAA,CAAClB,GAAG,aAAa6B,KAAK,OAAOK,OAAO,UAAUf,SAvFhD,CAuF0DgB,GAtFlF,IAAM9D,EAAOe,EAAEgC,MAAM,CAACgB,KAAK,EAAE,CAAC,EAAE,CAChC,GAAI/D,EAAM,CAER,GAAI,CAACA,EAAKwD,IAAI,CAACQ,UAAU,CAAC,UAAW,YACnCnD,EAAAA,EAAKA,CAACF,KAAK,CAAC,oCAKd,GAAIX,EAAKsC,IAAI,CAAG,IAAI,IAAa,GAAN,SACzBzB,EAAAA,EAAKA,CAACF,KAAK,CAAC,oCAGdjB,EAAcM,GAGd,IAAMiE,EAAS,IAAIC,WACnBD,EAAOE,MAAM,CAAGpD,IACdnB,EAAgBmB,EAAEgC,MAAM,EAAEqB,OAC5B,EACAH,EAAOI,aAAa,CAACrE,EACvB,CACF,EAgEuGrD,UAAU,cAE7F,UAAC8F,IAAAA,CAAE9F,UAAU,sCAA6B,iCAKhD,UAAC8F,IAAAA,CAAE9F,UAAU,yCAAgC,yDAMjD,WAACE,MAAAA,CAAIF,UAAU,uCACb,UAACuF,IAAIA,CAACC,KAAK,6BAA6BxE,UAAnCuE,YAAuD,OAAOjF,0BAAwB,oBACzF,UAACmF,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUmB,KAAK,SAAS7F,sBAAoB,SAASV,0BAAwB,oBAAW,aAI1G,WAACmF,EAAAA,CAAMA,CAAAA,CAACoB,KAAK,SAASc,SAAUnF,GAAaU,EAAalC,sBAAoB,SAASV,0BAAwB,qBAC7G,UAACsH,EAAAA,CAAIA,CAAAA,CAAC5H,UAAU,eAAegB,sBAAoB,OAAOV,0BAAwB,aACjF4C,EAAc,qBAAuBV,EAAY,cAAgB,6BAQ5E,WAACzC,EAAAA,EAAIA,CAAAA,CAACiB,sBAAoB,OAAOV,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACS,sBAAoB,aAAaV,0BAAwB,qBACnE,UAACE,EAAAA,EAASA,CAAAA,CAACQ,sBAAoB,YAAYV,0BAAwB,oBAAW,eAC9E,UAACG,EAAAA,EAAeA,CAAAA,CAACO,sBAAoB,kBAAkBV,0BAAwB,oBAAW,2CAE5F,UAACI,EAAAA,EAAWA,CAAAA,CAACM,sBAAoB,cAAcV,0BAAwB,oBACrE,WAACuH,KAAAA,CAAG7H,UAAU,8BACZ,WAAC8H,KAAAA,CAAG9H,UAAU,wCACZ,UAAC+H,OAAAA,CAAK/H,UAAU,qCAChB,UAAC+H,OAAAA,UAAK,kCAER,WAACD,KAAAA,CAAG9H,UAAU,wCACZ,UAAC+H,OAAAA,CAAK/H,UAAU,qCAChB,UAAC+H,OAAAA,UAAK,6CAER,WAACD,KAAAA,CAAG9H,UAAU,wCACZ,UAAC+H,OAAAA,CAAK/H,UAAU,qCAChB,UAAC+H,OAAAA,UAAK,oDAER,WAACD,KAAAA,CAAG9H,UAAU,wCACZ,UAAC+H,OAAAA,CAAK/H,UAAU,qCAChB,UAAC+H,OAAAA,UAAK,wDAMpB,0BCxPA,iDCAA,mEnBmBI,sBAAsB,0tBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,gCAAgC,CAChD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CA7BL3F,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,iBASQ,IoBhF9B,4DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/../../../src/icons/arrow-left.ts", "webpack://terang-lms-ui/?0a4d", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/../../../src/icons/upload.ts", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/./src/app/dashboard/teacher/layout.tsx", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/./src/components/ui/label.tsx", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/?15fa", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/./src/components/ui/textarea.tsx", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/label.tsx", "webpack://terang-lms-ui/?4628", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/../../../src/icons/save.ts", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/?73ce", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/?69ba", "webpack://terang-lms-ui/./src/app/dashboard/teacher/classes/new/page.tsx", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('ArrowLeft', __iconNode);\n\nexport default ArrowLeft;\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\new\\\\page.tsx\");\n", "module.exports = require(\"module\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '17 8 12 3 7 8', key: 't8dd8p' }],\n  ['line', { x1: '12', x2: '12', y1: '3', y2: '15', key: 'widbto' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNyA4IDEyIDMgNyA4IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMyIgeTI9IjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('Upload', __iconNode);\n\nexport default Upload;\n", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function TeacherLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has teacher role\n    requireRole('teacher');\n  }, []);\n  return <>{children}</>;\n}", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cn } from '@/lib/utils';\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return <LabelPrimitive.Root data-slot='label' className={cn('flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50', className)} {...props} data-sentry-element=\"LabelPrimitive.Root\" data-sentry-component=\"Label\" data-sentry-source-file=\"label.tsx\" />;\n}\nexport { Label };", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\new\\\\page.tsx\");\n", "module.exports = require(\"node:fs\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('Save', __iconNode);\n\nexport default Save;\n", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\new\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'teacher',\n        {\n        children: [\n        'classes',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\classes\\\\new\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/teacher/classes/new/page\",\n        pathname: \"/dashboard/teacher/classes/new\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\teacher\\\\layout.tsx\");\n", "'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { ArrowLeft, Save, Upload, X } from 'lucide-react';\nimport Link from 'next/link';\nimport { authStorage } from '@/lib/auth';\nimport { toast } from 'sonner';\nimport Image from 'next/image';\nexport default function NewClassPage() {\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: ''\n  });\n  const [coverImage, setCoverImage] = useState<File | null>(null);\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\n  const [isUploading, setIsUploading] = useState(false);\n  const uploadImageToBlob = async (file: File): Promise<string | null> => {\n    try {\n      setIsUploading(true);\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Failed to upload image');\n      }\n      const data = await response.json();\n      return data.url;\n    } catch (error) {\n      console.error('Error uploading image:', error);\n      toast.error('Failed to upload image');\n      return null;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      const user = authStorage.getUser();\n      if (!user) {\n        toast.error('Please log in to create classes');\n        return;\n      }\n      if (!user.institutionId) {\n        toast.error('You must be assigned to an institution to create classes');\n        return;\n      }\n      if (!formData.name.trim()) {\n        toast.error('Class name is required');\n        return;\n      }\n      let coverPictureUrl = null;\n\n      // Upload image if selected\n      if (coverImage) {\n        coverPictureUrl = await uploadImageToBlob(coverImage);\n        if (!coverPictureUrl) {\n          return; // Upload failed, stop submission\n        }\n      }\n      const response = await fetch('/api/classes', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          name: formData.name.trim(),\n          description: formData.description.trim(),\n          teacherId: user.id,\n          institutionId: user.institutionId,\n          coverPicture: coverPictureUrl\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        toast.success('Class created successfully!');\n        router.push('/dashboard/teacher/classes');\n      } else {\n        toast.error(data.error || 'Failed to create class');\n      }\n    } catch (error) {\n      console.error('Error creating class:', error);\n      toast.error('Failed to create class');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        toast.error('Please select a valid image file');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        toast.error('Image size must be less than 5MB');\n        return;\n      }\n      setCoverImage(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = e => {\n        setImagePreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeImage = () => {\n    setCoverImage(null);\n    setImagePreview(null);\n  };\n  return <div className='space-y-6' data-sentry-component=\"NewClassPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center space-x-4'>\r\n        <Link href='/dashboard/teacher/classes' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button variant='outline' size='sm' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <ArrowLeft className='mr-2 h-4 w-4' data-sentry-element=\"ArrowLeft\" data-sentry-source-file=\"page.tsx\" />\r\n            Back\r\n          </Button>\r\n        </Link>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>\r\n            Create New Class\r\n          </h1>\r\n          <p className='text-muted-foreground'>\r\n            Create a new class to organize your students\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Class Details</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\r\n            Enter the basic information for the new class\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <form onSubmit={handleSubmit} className='space-y-6'>\r\n            <div className='space-y-4'>\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='name' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Class Name</Label>\r\n                <Input id='name' value={formData.name} onChange={e => handleInputChange('name', e.target.value)} placeholder='e.g., Mathematics Grade 10A' required data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n                <p className='text-muted-foreground text-sm'>\r\n                  Choose a descriptive name that includes subject and grade\r\n                  level\r\n                </p>\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='description' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Description</Label>\r\n                <Textarea id='description' value={formData.description} onChange={e => handleInputChange('description', e.target.value)} placeholder='Brief description of the class and its objectives' rows={4} data-sentry-element=\"Textarea\" data-sentry-source-file=\"page.tsx\" />\r\n                <p className='text-muted-foreground text-sm'>\r\n                  Provide a brief description of what this class covers\r\n                </p>\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='coverImage' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Cover Image</Label>\r\n                <div className='space-y-4'>\r\n                  {imagePreview ? <div className='relative w-full max-w-md'>\r\n                      <Image src={imagePreview} alt='Cover preview' width={400} height={200} className='rounded-lg object-cover w-full h-48' />\r\n                      <Button type='button' variant='destructive' size='sm' className='absolute top-2 right-2' onClick={removeImage}>\r\n                        <X className='h-4 w-4' />\r\n                      </Button>\r\n                    </div> : <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>\r\n                      <Upload className='mx-auto h-12 w-12 text-gray-400' />\r\n                      <div className='mt-4'>\r\n                        <Label htmlFor='coverImage' className='cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'>\r\n                          Choose Image\r\n                        </Label>\r\n                        <Input id='coverImage' type='file' accept='image/*' onChange={handleImageChange} className='hidden' />\r\n                      </div>\r\n                      <p className='mt-2 text-sm text-gray-500'>\r\n                        PNG, JPG, GIF up to 5MB\r\n                      </p>\r\n                    </div>}\r\n                </div>\r\n                <p className='text-muted-foreground text-sm'>\r\n                  Upload a cover image for your class (optional)\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className='flex justify-end space-x-4'>\r\n              <Link href='/dashboard/teacher/classes' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n                <Button variant='outline' type='button' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                  Cancel\r\n                </Button>\r\n              </Link>\r\n              <Button type='submit' disabled={isLoading || isUploading} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                <Save className='mr-2 h-4 w-4' data-sentry-element=\"Save\" data-sentry-source-file=\"page.tsx\" />\r\n                {isUploading ? 'Uploading Image...' : isLoading ? 'Creating...' : 'Create Class'}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Next Steps Card */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Next Steps</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">After creating your class, you can:</CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <ul className='space-y-2 text-sm'>\r\n            <li className='flex items-center space-x-2'>\r\n              <span className='h-2 w-2 rounded-full bg-blue-500'></span>\r\n              <span>Add students to your class</span>\r\n            </li>\r\n            <li className='flex items-center space-x-2'>\r\n              <span className='h-2 w-2 rounded-full bg-blue-500'></span>\r\n              <span>Create or assign courses to the class</span>\r\n            </li>\r\n            <li className='flex items-center space-x-2'>\r\n              <span className='h-2 w-2 rounded-full bg-blue-500'></span>\r\n              <span>Generate course codes for student enrollment</span>\r\n            </li>\r\n            <li className='flex items-center space-x-2'>\r\n              <span className='h-2 w-2 rounded-full bg-blue-500'></span>\r\n              <span>Track student progress and performance</span>\r\n            </li>\r\n          </ul>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["Card", "className", "props", "div", "data-slot", "cn", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "TeacherLayout", "children", "Label", "LabelPrimitive", "data-sentry-element", "Textarea", "textarea", "metadata", "title", "description", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "value", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "serverComponentModule.default", "NewClassPage", "router", "useRouter", "isLoading", "setIsLoading", "useState", "formData", "setFormData", "name", "coverImage", "setCoverImage", "imagePreview", "setImagePreview", "isUploading", "setIsUploading", "uploadImageToBlob", "file", "FormData", "append", "response", "fetch", "method", "body", "ok", "data", "json", "url", "error", "console", "toast", "handleSubmit", "e", "preventDefault", "user", "authStorage", "getUser", "institutionId", "trim", "coverPictureUrl", "headers", "JSON", "stringify", "teacherId", "id", "coverPicture", "success", "push", "handleInputChange", "field", "prev", "Link", "href", "<PERSON><PERSON>", "variant", "size", "ArrowLeft", "h1", "p", "form", "onSubmit", "htmlFor", "Input", "onChange", "target", "placeholder", "required", "rows", "Image", "src", "alt", "width", "height", "type", "onClick", "removeImage", "X", "Upload", "accept", "handleImageChange", "files", "startsWith", "reader", "FileReader", "onload", "result", "readAsDataURL", "disabled", "Save", "ul", "li", "span"], "sourceRoot": ""}