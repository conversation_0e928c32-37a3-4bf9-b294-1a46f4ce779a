try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="73c920a0-f5ac-44bf-aeb9-256a758c3aba",e._sentryDebugIdIdentifier="sentry-dbid-73c920a0-f5ac-44bf-aeb9-256a758c3aba")}catch(e){}"use strict";exports.id=6483,exports.ids=[6483],exports.modules={2656:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],f=[],d=new Set,p=new Set(c),m=function(e){!e||d.has(e)||(d.add(e),m(e.parentNode))};c.forEach(m);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),f.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),d.clear(),l++,function(){f.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},10158:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(93491),o=n(91754);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[l]||a,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},11806:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(93491),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},18682:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},19144:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(93491),o=n(42014),i=n(90604),a=n(62242),l=n(91754),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),S=(0,o.s)(t,e=>b(e)),C=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:m(R.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||m(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,C.paused]),r.useEffect(()=>{if(w){h.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(c,E),h.remove(C)},0)}}},[w,x,E,C]);let A=r.useCallback(e=>{if(!n&&!f||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,C.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:S,onKeyDown:A})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},21370:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e7,Bk:()=>eV});var r=n(93491);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function h(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>f[e])}let b=["left","right"],x=["right","left"],E=["top","bottom"],R=["bottom","top"];function S(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function C(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function A(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function T(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=h(y(t)),u=v(l),c=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let N=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=T(c,r,u),d=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:h}=l[n],{x:v,y:g,data:y,reset:w}=await h({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=T(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function L(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=d(t,e),h=C(m),v=l[p?"floating"===f?"reference":"floating":f],g=A(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),b=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=A(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-x.top+h.top)/b.y,bottom:(x.bottom-g.bottom+h.bottom)/b.y,left:(g.left-x.left+h.left)/b.x,right:(x.right-g.right+h.right)/b.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function P(e){return o.some(t=>e[t]>=0)}let M=new Set(["left","top"]);async function k(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),u="y"===y(n),c=M.has(a)?-1:1,s=i&&u?-1:1,f=d(t,e),{mainAxis:h,crossAxis:v,alignmentAxis:g}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*s,y:h*c}:{x:h*c,y:v*s}}function D(){return"undefined"!=typeof window}function W(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!D()&&(e instanceof Node||e instanceof j(e).Node)}function H(e){return!!D()&&(e instanceof Element||e instanceof j(e).Element)}function $(e){return!!D()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function B(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}let _=new Set(["inline","contents"]);function z(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!_.has(o)}let U=new Set(["table","td","th"]),X=[":popover-open",":modal"];function Y(e){return X.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let V=["transform","translate","scale","rotate","perspective"],G=["transform","translate","scale","rotate","perspective","filter"],q=["paint","layout","strict","content"];function K(e){let t=Z(),n=H(e)?ee(e):e;return V.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||G.some(e=>(n.willChange||"").includes(e))||q.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(W(e))}function ee(e){return j(e).getComputedStyle(e)}function et(e){return H(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===W(e))return e;let t=e.assignedSlot||e.parentNode||B(e)&&e.host||I(e);return B(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&z(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=j(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],z(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=$(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function ea(e){return H(e)?e:e.contextElement}function el(e){let t=ea(e);if(!$(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let eu=c(0);function ec(e){let t=j(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=c(1);t&&(r?H(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===j(a))&&o)?ec(a):c(0),s=(i.left+u.x)/l.x,f=(i.top+u.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=j(a),t=r&&H(r)?j(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=eo(n=j(o))}}return A({width:d,height:p,x:s,y:f})}function ef(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(I(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ef(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function em(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=j(e),r=I(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=Z();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ef(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(I(e));else if(H(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=$(e)?el(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return A(r)}function eh(e){return"static"===ee(e).position}function ev(e,t){if(!$(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return I(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=j(e);if(Y(e))return r;if(!$(e)){let t=en(e);for(;t&&!J(t);){if(H(t)&&!eh(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,U.has(W(n)))&&eh(o);)o=ev(o,t);return o&&J(o)&&eh(o)&&!K(o)?r:o||function(e){let t=en(e);for(;$(t)&&!J(t);){if(K(t))return t;if(Y(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),o=I(t),i="fixed"===n,a=es(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==W(t)||z(o))&&(l=et(t)),r){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ef(o));i&&!r&&o&&(u.x=ef(o));let s=!o||r||i?c(0):ed(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=I(r),l=!!t&&Y(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=$(r);if((d||!d&&!i)&&(("body"!==W(r)||z(a))&&(u=et(r)),$(r))){let e=es(r);s=el(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?c(0):ed(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?Y(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>H(e)&&"body"!==W(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;H(a)&&!J(a);){let t=ee(a),n=K(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||z(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!H(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=em(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},em(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:H,isRTL:function(e){return"rtl"===ee(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let g=C(p),w={x:n,y:r},b=h(y(o)),x=v(b),E=await u.getDimensions(f),R="y"===b,S=R?"clientHeight":"clientWidth",A=l.reference[x]+l.reference[b]-w[b]-l.floating[x],T=w[b]-l.reference[b],N=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),L=N?N[S]:0;L&&await (null==u.isElement?void 0:u.isElement(N))||(L=c.floating[S]||l.floating[x]);let O=L/2-E[x]/2-1,P=i(g[R?"top":"left"],O),M=i(g[R?"bottom":"right"],O),k=L-E[x]-M,D=L/2-E[x]/2+(A/2-T/2),W=a(P,i(D,k)),j=!s.arrow&&null!=m(o)&&D!==W&&l.reference[x]/2-(D<P?P:M)-E[x]/2<0,I=j?D<P?D-P:D-k:0;return{[b]:w[b]+I,data:{[b]:W,centerOffset:D-W-I,...j&&{alignmentOffset:I}},reset:j}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return N(e,t,{...o,platform:i})};var eR=n(52410),eS="undefined"!=typeof document?r.useLayoutEffect:function(){};function eC(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eC(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eC(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eA(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eT(e,t){let n=eA(e);return Math.round(t*n)/n}function eN(e){let t=r.useRef(e);return eS(()=>{t.current=e}),t}let eL=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),eO=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await k(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},m=await L(t,s),v=y(p(o)),g=h(v),w=f[g],b=f[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+m[e],r=w-m[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+m[e],r=b-m[t];b=a(n,i(b,r))}let x=c.fn({...t,[g]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),eM=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=y(o),m=h(f),v=s[m],g=s[f],w=d(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=M.has(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[m]:v,[f]:g}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:g}=t,{mainAxis:C=!0,crossAxis:A=!0,fallbackPlacements:T,fallbackStrategy:N="bestFit",fallbackAxisSideDirection:O="none",flipAlignment:P=!0,...M}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=p(l),D=y(s),W=p(s)===s,j=await (null==f.isRTL?void 0:f.isRTL(g.floating)),I=T||(W||!P?[S(s)]:function(e){let t=S(e);return[w(e),t,w(t)]}(s)),F="none"!==O;!T&&F&&I.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?x:b;return t?b:x;case"left":case"right":return t?E:R;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(s,P,O,j));let H=[s,...I],$=await L(t,M),B=[],_=(null==(r=u.flip)?void 0:r.overflows)||[];if(C&&B.push($[k]),A){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=h(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=S(a)),[a,S(a)]}(l,c,j);B.push($[e[0]],$[e[1]])}if(_=[..._,{placement:l,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=H[e];if(t&&("alignment"!==A||D===y(t)||_.every(e=>y(e.placement)!==D||e.overflows[0]>0)))return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(i=_.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(N){case"bestFit":{let e=null==(a=_.filter(e=>{if(F){let t=y(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:f}=t,{apply:h=()=>{},...v}=d(e,t),g=await L(t,v),w=p(u),b=m(u),x="y"===y(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let S=R-g.top-g.bottom,C=E-g.left-g.right,A=i(R-g[o],S),T=i(E-g[l],C),N=!t.middlewareData.shift,O=A,P=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=C),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=S),N&&!b){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?P=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):O=R-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await h({...t,availableWidth:P,availableHeight:O});let M=await s.getDimensions(f.floating);return E!==M.width||R!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=O(await L(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:P(e)}}}case"escaped":{let e=O(await L(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:P(e)}}}default:return{}}}}}(e),options:[e,t]}),ej=(e,t)=>({...eL(e),options:[e,t]});var eI=n(90604),eF=n(91754),eH=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eF.jsx)(eI.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eH.displayName="Arrow";var e$=n(42014),eB=n(10158),e_=n(62242),ez=n(89847),eU=n(96432),eX="Popper",[eY,eV]=(0,eB.A)(eX),[eG,eq]=eY(eX),eK=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eF.jsx)(eG,{scope:t,anchor:o,onAnchorChange:i,children:n})};eK.displayName=eX;var eZ="PopperAnchor",eQ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eq(eZ,n),l=r.useRef(null),u=(0,e$.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eF.jsx)(eI.sG.div,{...i,ref:u})});eQ.displayName=eZ;var eJ="PopperContent",[e0,e1]=eY(eJ),e4=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eq(eJ,n),[x,E]=r.useState(null),R=(0,e$.s)(t,e=>E(e)),[S,C]=r.useState(null),A=(0,eU.X)(S),T=A?.width??0,N=A?.height??0,L="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},O=Array.isArray(p)?p:[p],P=O.length>0,M={padding:L,boundary:O.filter(e5),altBoundary:P},{refs:k,floatingStyles:D,placement:W,isPositioned:j,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);eC(p,o)||m(o);let[h,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=a||h,E=l||g,R=r.useRef(null),S=r.useRef(null),C=r.useRef(f),A=null!=c,T=eN(c),N=eN(i),L=eN(s),O=r.useCallback(()=>{if(!R.current||!S.current)return;let e={placement:t,strategy:n,middleware:p};N.current&&(e.platform=N.current),eE(R.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};P.current&&!eC(C.current,t)&&(C.current=t,eR.flushSync(()=>{d(t)}))})},[p,t,n,N,L]);eS(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let P=r.useRef(!1);eS(()=>(P.current=!0,()=>{P.current=!1}),[]),eS(()=>{if(x&&(R.current=x),E&&(S.current=E),x&&E){if(T.current)return T.current(x,E,O);O()}},[x,E,O,T,A]);let M=r.useMemo(()=>({reference:R,floating:S,setReference:w,setFloating:b}),[w,b]),k=r.useMemo(()=>({reference:x,floating:E}),[x,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=eT(k.floating,f.x),r=eT(k.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eA(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,k.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:O,refs:M,elements:k,floatingStyles:D}),[f,O,M,k,D])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=ea(e),m=l||c?[...p?er(p):[],...er(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&f?function(e,t){let n,r=null,o=I(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:v}=d;if(s||t(),!h||!v)return;let g=u(m),y=u(o.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+v))+"px "+-u(p)+"px",threshold:a(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||eb(d,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?es(e):null;return d&&function t(){let r=es(e);y&&!eb(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[eO({mainAxis:l+N,alignmentAxis:s}),d&&eP({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eM():void 0,...M}),d&&ek({...M}),eD({...M,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&ej({element:S,padding:f}),e6({arrowWidth:T,arrowHeight:N}),v&&eW({strategy:"referenceHidden",...M})]}),[H,$]=e8(W),B=(0,e_.c)(y);(0,ez.N)(()=>{j&&B?.()},[j,B]);let _=F.arrow?.x,z=F.arrow?.y,U=F.arrow?.centerOffset!==0,[X,Y]=r.useState();return(0,ez.N)(()=>{x&&Y(window.getComputedStyle(x).zIndex)},[x]),(0,eF.jsx)("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:j?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(e0,{scope:n,placedSide:H,onArrowChange:C,arrowX:_,arrowY:z,shouldHideArrow:U,children:(0,eF.jsx)(eI.sG.div,{"data-side":H,"data-align":$,...w,ref:R,style:{...w.style,animation:j?void 0:"none"}})})})});e4.displayName=eJ;var e2="PopperArrow",e9={top:"bottom",right:"left",bottom:"top",left:"right"},e3=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e2,n),i=e9[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eH,{...r,ref:t,style:{...r.style,display:"block"}})})});function e5(e){return null!==e}e3.displayName=e2;var e6=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,c]=e8(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===u?(p=i?s:`${f}px`,m=`${-l}px`):"top"===u?(p=i?s:`${f}px`,m=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,m=i?s:`${d}px`):"left"===u&&(p=`${r.floating.width+l}px`,m=i?s:`${d}px`),{data:{x:p,y:m}}}});function e8(e){let[t,n="center"]=e.split("-");return[t,n]}var e7=eK,te=eQ,tt=e4,tn=e3},30670:(e,t,n)=>{n.d(t,{Cl:()=>r,Tt:()=>o,fX:()=>a,sH:()=>i});var r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function i(e,t,n,r){return new(n||(n=Promise))(function(o,i){function a(e){try{u(r.next(e))}catch(e){i(e)}}function l(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,l)}u((r=r.apply(e,t||[])).next())})}Object.create;function a(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},43748:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(93491),i=n(18682),a=n(90604),l=n(42014),u=n(62242),c=n(91754),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...w}=e,b=o.useContext(f),[x,E]=o.useState(null),R=x?.ownerDocument??globalThis?.document,[,S]=o.useState({}),C=(0,l.s)(t,e=>E(e)),A=Array.from(b.layers),[T]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),N=A.indexOf(T),L=x?A.indexOf(x):-1,O=b.layersWithOutsidePointerEventsDisabled.size>0,P=L>=N,M=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));P&&!n&&(h?.(e),g?.(e),e.defaultPrevented||y?.())},R),k=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...b.branches].some(e=>e.contains(t))&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},R);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L===b.layers.size-1&&(d?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},R),o.useEffect(()=>{if(x)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(x)),b.layers.add(x),p(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[x,R,n,b]),o.useEffect(()=>()=>{x&&(b.layers.delete(x),b.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,b]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...w,ref:C,style:{pointerEvents:O?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},53291:(e,t,n)=>{n.d(t,{Qg:()=>a,bL:()=>u});var r=n(93491),o=n(90604),i=n(91754),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...a,...e.style}}));l.displayName="VisuallyHidden";var u=l},55462:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(93491),o=n(42014),i=n(89847),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(u.current);s.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=u.current,n=c.current;if(n!==e){let r=s.current,o=l(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=l(u.current).includes(n.animationName);if(n.target===o&&r&&(d("ANIMATION_END"),!c.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(s.current=l(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),c=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:c}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},62242:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(93491);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},62253:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(93491),o=n(52410),i=n(90604),a=n(89847),l=n(91754),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[c,s]=r.useState(!1);(0,a.N)(()=>s(!0),[]);let f=n||c&&globalThis?.document?.body;return f?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),f):null});u.displayName="Portal"},62671:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(93491),o=n(10158),i=n(42014),a=n(16435),l=n(91754);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",p=(0,a.TL)(d),m=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),a=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(p,{ref:a,children:r})});m.displayName=d;let h=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,a.TL)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,u=r.useRef(null),c=(0,i.s)(t,u),f=s(h,n);return r.useEffect(()=>(f.itemMap.set(u,{ref:u,...a}),()=>void f.itemMap.delete(u))),(0,l.jsx)(g,{...{[v]:""},ref:c,children:o})});return y.displayName=h,[{Provider:f,Slot:m,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}var c=new WeakMap;function s(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=f(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function f(e){return e!=e||0===e?0:Math.trunc(e)}},62962:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(93491),i=n(89847),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},76322:(e,t,n)=>{n.d(t,{i:()=>l});var r,o=n(93491),i=n(89847),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[c,e,l,u])]}Symbol("RADIX:SYNC_STATE")},78011:(e,t,n)=>{n.d(t,{A:()=>X});var r,o,i=n(30670),a=n(93491),l="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=d),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,i.Cl)({async:!0,ssr:!1},e),a}(),m=function(){},h=a.forwardRef(function(e,t){var n,r,o,l,u=a.useRef(null),d=a.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),h=d[0],v=d[1],g=e.forwardProps,y=e.children,w=e.className,b=e.removeScrollBar,x=e.enabled,E=e.shards,R=e.sideCar,S=e.noRelative,C=e.noIsolation,A=e.inert,T=e.allowPinchZoom,N=e.as,L=e.gapMode,O=(0,i.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[u,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,s(function(){var e=f.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}f.set(l,n)},[n]),l),M=(0,i.Cl)((0,i.Cl)({},O),h);return a.createElement(a.Fragment,null,x&&a.createElement(R,{sideCar:p,removeScrollBar:b,shards:E,noRelative:S,noIsolation:C,inert:A,setCallbacks:v,allowPinchZoom:!!T,lockRef:u,gapMode:L}),g?a.cloneElement(a.Children.only(y),(0,i.Cl)((0,i.Cl)({},M),{ref:P})):a.createElement(void 0===N?"div":N,(0,i.Cl)({},M,{className:w,ref:P}),y))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:l};var v=function(e){var t=e.sideCar,n=(0,i.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,i.Cl)({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},S=w(),C="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(C,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(C,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(C)||"0",10);return isFinite(e)?e:0},N=function(){a.useEffect(function(){return document.body.setAttribute(C,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(C):document.body.setAttribute(C,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var i=a.useMemo(function(){return R(o)},[o]);return a.createElement(S,{styles:A(i,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var P=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",P,P),window.removeEventListener("test",P,P)}catch(e){O=!1}var M=!!O&&{passive:!1},k=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=j(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?k(t,"overflowY"):k(t,"overflowX")},j=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},I=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,f=l>0,d=0,p=0;do{if(!u)break;var m=j(e,u),h=m[0],v=m[1]-m[2]-a*h;(h||v)&&W(e,u)&&(d+=v,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},$=function(e){return e&&"current"in e?e.current:e},B=0,_=[];let z=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(B++)[0],l=a.useState(w)[0],u=a.useRef(e);a.useEffect(function(){u.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,i.fX)([e.lockRef.current],(e.shards||[]).map($),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,i=F(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=D(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=D(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return I(p,t,e,"h"===p?l:c,!0)},[]),s=a.useCallback(function(e){if(_.length&&_[_.length-1]===l){var n="deltaY"in e?H(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map($).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),p=a.useCallback(function(t){f(t.type,H(t),t.target,c(t,e.lockRef.current))},[]),m=a.useCallback(function(t){f(t.type,F(t),t.target,c(t,e.lockRef.current))},[]);a.useEffect(function(){return _.push(l),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:m}),document.addEventListener("wheel",s,M),document.addEventListener("touchmove",s,M),document.addEventListener("touchstart",d,M),function(){_=_.filter(function(e){return e!==l}),document.removeEventListener("wheel",s,M),document.removeEventListener("touchmove",s,M),document.removeEventListener("touchstart",d,M)}},[]);var h=e.removeScrollBar,v=e.inert;return a.createElement(a.Fragment,null,v?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(L,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},p.useMedium(r),v);var U=a.forwardRef(function(e,t){return a.createElement(h,(0,i.Cl)({},e,{ref:t,sideCar:z}))});U.classNames=h.classNames;let X=U},78283:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(93491);n(91754);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},89847:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(93491),o=globalThis?.document?r.useLayoutEffect:()=>{}},90604:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>l});var r=n(93491),o=n(52410),i=n(16435),a=n(91754),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},96432:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(93491),o=n(89847);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}}};
//# sourceMappingURL=6483.js.map