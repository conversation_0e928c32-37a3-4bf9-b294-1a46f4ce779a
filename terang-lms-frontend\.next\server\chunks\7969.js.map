{"version": 3, "file": "7969.js", "mappings": "scACA,sCASA,MARA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,0CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,eAAuB,QACvB,QAAuC,EAAQ,KAAgB,GAC/D,EAAmB,EAAQ,KAAc,EAgDzC,UAhD0B,OAgDH,CA/CvB,WACA,gBACA,aAAkC,MAClC,aAAkC,MAClC,gBACA,eACA,WACA,CAsCA,OArCA,4BACA,YAAwB,WAAoB,KAC5C,UACA,WACA,qIAEA,0CACA,oCACA,mBACa,CACb,CACA,WAA0B,cAC1B,EACA,+BACA,WAqBA,OApBA,sBACA,sBACA,MAGA,IADA,iBACA,WACA,cACA,MACA,MACA,wBACA,oBACA,kCACA,YACA,0BACA,CACA,oBACA,kCAEA,uBACA,CAAS,EACT,IAA0B,cAC1B,EACA,CACA,CAAC,oFCbD,aAlDA,aAeA,MAdA,8BACA,YAAoB,mBAAsB,KAC1C,mBAEA,eACA,2CACA,WAGA,CAEA,QACA,GAEA,qBACA,CAiBA,uDAgBA,UAGA,eACA,SACA,wBACA,IApBA,IAoBA,4BApBA,EAsBA,EAtBA,EAsBA,OArBA,YACA,OAGA,YACA,OAGA,mBACA,mBACG,IAYH,SACA,UAEA,CAAG,EACH,sBACA,gCACA,gBACA,CAAK,CACL,CAAG,EACH,iCACA,EAmCA,6BAAgE,iBAAqB,CAAG,WAAe,CAqDvG,gBACA,oBAEA,uCACA,EAGA,CACA,CAEA,iBACA,SACA,EAEA,cACA,QACA,EAEA,gBAEA,SADA,+BACA,EAGA,cAKA,QAJA,iCACA,sCACA,KAEA,IAAsB,KAAU,IAChC,UAGA,QACA,EACA,cAGA,IAFA,EAEA,SACA,iBACA,iBACA,iBACA,aACA,iBACA,iBACA,iBACA,eAEA,cACA,eACA,eACA,gBACA,gBACA,oBACA,mBACA,iBACA,iBACA,gBACA,iBACA,mBACA,iBACA,qBACA,6BACA,EAAkB,QAAY,EAC9B,eACA,gBACG,EAEH,EAAwB,UAAc,IACtC,OACA,MAEA,0BAGA,IACA,EADA,CAFA,GA3HA,cACA,YACA,IACA,QACA,QACA,GAGA,MAAwB,UAAc,YACtC,OACA,OAEA,EAA0B,YAAgB,MAC1C,OACA,OAEA,EAAuB,QAAY,KAgCnC,OA/BA,aACA,eACA,YAEA,CAAG,EACH,aACA,gBACA,aAIA,GACA,KAHA,yBAIA,CAAO,EAEP,CAAG,MACD,WAAe,YACjB,MAIA,IAzEA,EAyEA,GAzEA,EAyEA,YACA,GACA,MACA,CAAO,CACP,CAAK,CA5EL,CACA,mBACA,iBAEA,MAoEA,GAnEA,MAmEA,GAnEA,kBAEA,MAiEA,EAjEA,CACA,YACA,kBACA,cACS,EAGT,MACA,CAAK,CACL,qBACA,YAuDA,GArDA,MAEA,4BACA,+BAEA,CAF0D,CAE1D,2BAgDA,GA9CA,IAFoE,EAEpE,yBACA,CACA,CACA,GAiDA,OADA,YACA,WACA,aACA,EACA,CAAG,MACH,CACA,CA0EA,EAEA,IACA,IAEA,sBACA,MAA0B,aAAiB,aAC3C,WACA,gBAEA,CAAG,QACH,OACA,EAAe,aAAiB,aAChC,MACA,CAAG,QAEH,MAAyB,UAAc,GAAG,EAC1C,OACA,OAEA,EAAgB,aAAiB,YACjC,WAA8B,CAC9B,CAAG,KACH,EAAuC,QAAY,KACnD,EAAqB,SAAa,YAClC,yDACA,cAGA,QAFA,oCAEA,IAAsB,IAAU,KAChC,WACA,OAEA,sBAEA,4BAEA,KAEA,OACA,QACA,QACA,OACA,MACA,KACA,CACA,CAEA,QACA,CAAG,cACH,uCA5EA,gBA6EA,0BACA,sBACA,4BACA,EAA0B,QAAY,GACtC,aACA,aACA,kBACA,KAIA,kBAEA,EADA,4BAEA,EAOA,OALA,IACA,+BACA,WACA,UACA,CAAK,EACL,WACA,iCACA,CACA,CAAG,QAEH,gBAoKA,GAaA,IAZA,qBACA,cACA,iBACA,aAMA,QAJA,YACA,mBAGA,GACA,IAEA,mBACA,IAGA,OACA,QACA,KACA,CACA,EAzLA,WACA,YACA,UAEA,GAAgB,SAAa,YAC7B,UACA,SACA,OACA,WACA,aACA,CAAK,CACL,CAAG,uBACH,GAAuB,QAAY,GACnC,cACA,OAAqB,SAAa,YAiClC,QAhCA,KAgCA,gBAA0C,IAAS,KA9BnD,cACA,YACA,OAEA,QAAqC,MAAkB,EACvD,uBACA,MACA,sBAEA,eACA,6BAEA,WACA,gBAGA,kBACA,cACA,MAEA,aAA2C,MAAU,QAAiB,aACtE,CAAe,CACf,CACA,CACA,CACA,CAAO,EAEP,SACA,EAGA,GAGA,QACA,CAAG,aACH,GAAmB,QAAY,KAC/B,aACA,YACA,IAAyB,EAGzB,aACA,CAAG,MACH,OAAuB,aAAiB,eACxC,IACA,GADA,cAAsC,GACtC,MACA,uBAEA,YACA,iBACA,cAEA,aAEA,EADA,KACA,QACQ,OACR,MAEA,SAIA,YACA,KACM,UACN,OACM,cACN,QAEA,CAAG,MACH,GAAyB,aAAiB,eAC1C,oBAAuC,EACvC,UACA,sBACA,EA3XA,cACA,oBACA,IAEA,IAFA,KACA,iBAGA,QAAc,WAAuB,IAErC,UADA,SACA,GACA,YAGA,QACA,EA8WA,aAEA,YACA,iBACA,iBACA,cACA,iCAEA,MAIA,cACA,cACA,aACQ,iBAGR,OAFA,UAOA,GADA,sDACA,GACA,OACA,CAAK,KACL,CAAG,SAgBH,OACA,gBACA,YACA,kBACA,cAnBsB,aAAiB,YACvC,0CAAwE,IAAa,IACrF,kBAQA,mBACA,iCACA,kBACA,CAAK,CACL,CAAG,OAMH,SACA,CACA,CAEA,wBACA,YACA,gBACA,OAEA,OACA,WACM,UAGN,SAFA,MAIA,QAEA,IACA,IAEA,CAEA,wBCpdA,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,kBAA0B,QAC1B,QAAyB,EAAQ,KAAO,GACxC,EAAgB,EAAQ,KAAW,EAgBnC,UAhBuB,UAgBG,CAf1B,cACA,YAAmC,OACnC,4BAEA,uBAAmD,SAAiB,IACpE,uBACA,aAGA,2BACA,kBACA,GACA,EACA,CAAK,OACL,yBCrCA,sCASA,MARA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,SACA,CAAC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,mCACA,SACA,0EACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,EACA,qCAA6C,CAAE,SAAa,EAC5D,gBAAsB,QACtB,QAAyB,EAAQ,KAAO,GACxC,GACA,MAFgC,GAEhC,QACA,eACA,wBACA,wBACA,aACA,YACA,wBACA,EAIA,gBAAsB,4BACtB,uDACA,gCAAkD,YAJlD,QAAuC,IAIW,GAJX,CAIW,CAAkC,MACpF,CAAC,iBC3DD,qCAA6C,CAAE,SAAa,EAAC,EAC7D,WAAmB,QAEnB,YACA,6BACA,oBACA,+BACA,iBACA,CAAC,iBAAwC,aAAmB,IAAK,yBCRjE,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,WACA,CAAC,EACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,0CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,cAAsB,QACtB,QAAyB,EAAQ,KAAO,GACxC,IAAiC,EAAQ,GADT,EACqB,GACrD,EAAc,EAAQ,KADkB,EAExC,EAAgB,EAAQ,KAAW,CADd,CAErB,EAAc,EAAQ,KAAS,CADR,CASvB,UARqB,MAQC,CAPtB,eAYA,IACA,EAIS,UA4CT,EACA,EAyCA,EAES,IAuDT,EAIS,UAmDT,EACA,EAGS,IACT,EAvNA,OAYS,GAJT,4BAAuD,OACvD,0BACA,6CACA,oBACK,CAAI,8DACT,uBAEA,IADA,EACA,aACA,oCACA,yDACA,EAEA,2BACa,CACb,EACA,iBACA,IAGA,iCACA,wBACA,CADA,KAAkE,CAClE,eACA,WACA,sBAEA,mBACA,WACA,EACA,4DAGA,2DAEA,CAAa,CACb,qBACA,QACA,IACA,oBACA,mBACA,6DAEA,GACA,CAAa,CACb,IACA,kBACA,GACA,CACA,CAAK,uCACL,aACA,4BAEA,IADA,IACA,GACA,gCACA,iDAEA,gCACA,gDAEA,wBACA,gCACA,SAEA,4BACA,oCACA,sBACA,qBAIA,OAHA,0BACA,OAEA,CACA,CAAa,EACb,GACA,4BAEA,CAAS,GACT,CAAK,qFACL,uBACA,UACA,+BACA,gCACA,IAEA,CACA,CAAK,QASI,GAFT,4BAAuD,OACvD,0BACK,CAAI,2BACT,uBACA,0BAEA,iCAEA,IADA,sCACA,+BACA,gCAEA,kDACA,GAEA,iCAEA,sCACA,OAEA,2BACA,+CACA,8BACA,mDAGA,CAAK,EACL,sBACA,6BACA,EACA,EAgCS,GAJT,4BAAuD,OACvD,kBACA,2CACA,oBACK,CAAI,uDACT,uBAEA,WAIA,QALA,EAGA,iCAAoE,YAAsB,EAC1F,KACA,IAAsD,EAAtD,EAAsD,OAA2B,KACjF,MADA,CACA,IACA,wCAGA,SACA,CACA,uBAA2E,+DAAmE,EAoB9I,QAnBA,KACA,kBAjCA,CAmCA,GADA,qBACA,EAnCA,EAmCA,YACA,gBACA,iCAEA,mBACA,wCACA,6BACA,WACA,6DAGA,iCACA,sEAEA,CAAa,CAhDb,YACA,WAEA,KACA,SACA,EA4CA,EACA,QAAwE,WAAoC,KAC5G,WACA,IACA,CAtBA,IAuBA,0BACA,WACA,CAAS,EACT,kBACA,GACA,EACA,CAAK,wBAOL,eAIS,GAHT,4BAAuD,OACvD,iDACA,0CACK,CAAI,uBACT,iBACA,uBACA,cACA,aACA,MACA,CACA,MACA,iCACA,MACA,CAIA,6BACA,oDACA,SAEA,gBACA,UACA,SAEA,CAAK,MAGL,uBACA,cACA,kBACA,eACA,SAEA,CACA,KAEA,OADA,qCACA,WACA,uCACA,CAEA,CAAK,QA5PL,IACA,EA4IA,mCC/KA,oBACA,wBACA,kCACA,6BACA,wBACA,kCAKA,cACA,+CACA,kBACA,oBACA,OAA6B,kDAA4F,EAEzH,QACA,EACA,gCAAmG,GAKnG,oBAAuG,EAAvG,eAAyE,sBAA8B,EACvG,EACA,EAIA,KAvBA,SACA,eACA,OAA8B,uBAAkC,EAChE,EAqBA,GACA,gBACA,WACA,iCACA,CAAC,EACD,UATA,MAAoD,eAAkB,SAAa,EASnF,GAGA,QAAoB,EAAQ,KAAO,GACnC,IAAuB,EAAQ,GADJ,EACe,GAC1C,EAAwB,EAAQ,KAAsB,EACtD,EAAyB,EAAQ,KAAmB,CADrB,CAqB/B,EAnBA,CACA,IACA,GAHgC,MAIhC,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,oCAA8D,EAAK,GACnE,uBACA,YAAY,QAA6B,EAKzC,MAHA,4BACA,oCAEA,UAJA,MAIA,CAA+D,WAAsC,CACrG,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EACL,gBACA,sCACA,CACA,uBC3EA,qCAA6C,CAAE,SAAa,EAAC,IAM7D,mCAaA,8BACA,gDACA,OACA,UAKA,gBACA,6CACA,qBAEA,CA0HA,SAAe,CArDf,gBAEA,aAA8B,MAC9B,IAFA,IAEA,wBA5FA,IA6FA,sBAzFA,UA0FA,iCACA,SAhEA,OACA,WACA,gBACA,sBACA,UAEA,OADA,oBAAyC,qBAAsC,EAC/E,GACK,EAyDL,MACK,EACL,UACA,OACA,cAIA,6BAGA,sBACA,WACA,OAEA,EADA,UACA,EACA,OAjEA,8BAkEA,CAlEA,aAIA,CAIA,2CA0DA,CAzDA,MAyDA,EAzDA,MAyDA,CAvDA,qBACA,SAsDA,EAtDA,EACA,CAAS,GAIT,mBACA,OAgDA,CAhDA,6BAgDA,EAhDA,EACA,EAAS,EA0DT,WACA,qBAGA,YACA,MATA,YACA,WAUA,CAAS,EACT,GACA,gBAGA,gCACA,EAEA,OADA,wBACA,WACA,0BACA,CACA,mBC1JA,oBACA,wBACA,kCACA,6BACA,wBACA,kCAKA,cACA,+CACA,kBACA,oBACA,OAA6B,kDAA4F,EAEzH,QACA,EAYA,KAvBA,SACA,eACA,OAA8B,uBAAkC,EAChE,EAqBA,GACA,qBACA,CAAC,EACD,UAPA,MAAoD,eAAkB,SAAa,EAOnF,GAGA,OAlBA,8BAAmG,GAKnG,oBAAuG,EAAvG,eAAyE,sBAA8B,EACvG,EACA,GAWoB,EAAQ,KAAO,GACnC,SAD2B,KAC3B,gCACA,yBCtCA,sCASA,MARA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,0CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,QAAgB,QAChB,MAAoB,EAAQ,KAAa,EACzC,IAAyB,EAAQ,IADN,CACa,GACxC,IAAuC,EAAQ,GADf,EAC+B,GAC/D,EAAwB,EAAQ,IAA0B,CADZ,CAE9C,EAAoB,EAAQ,KAAsB,EADnB,EAEjB,EAAQ,KAAS,CADJ,CAiF3B,UAAgB,CA/EhB,YACA,kBAA6C,YAC7C,YACA,UACA,EAAW,aACX,uBACA,4CACA,uDACA,CAAS,CACT,CAAK,CAEL,IAEA,cACA,eACA,yBACA,iCACA,YAA4B,YAC5B,cACA,WACA,CAAK,gBACL,aACA,aACA,+BAAmD,iBAA2B,KAC9E,uBAAgD,gBAAiC,MACjF,uBACA,YACA,UACA,CAAK,QACL,gCAIA,OAHA,cACA,aAAuC,KAAY,iBAAwC,CAC3F,CAAS,EACT,WACA,cACA,aAA2C,KAAY,oBAA2C,CAClG,CAAa,CACb,CACA,CAAK,MACL,iBACA,4BA+BA,OACA,WACA,MAhCA,CACA,iCACA,cAA4C,aAA4B,KAAY,sBAA+B,EAAK,CACxH,CAAa,CACb,2BACA,cAA4C,aAA4B,KAAY,oDAAoE,EAAK,CAC7J,CAAa,CACb,sBACA,qBAAmD,aAA4B,KAAY,cAA0B,EAAK,CAC1H,CAAa,CACb,kBACA,kBACA,qBAAmD,aAA4B,KAAY,sFAC3F,0BACA,2BAA4D,EAAK,CACjE,CAAa,CACb,2BACA,qBAAmD,aAA4B,KAAY,kDAAkE,EAAK,CAClK,CAAa,CACb,2BACA,WACA,CAAa,CACb,oBAEA,MADA,qHACA,UACa,CACb,oBACA,cAA4C,aAA4B,KAAY,WAAmB,EAAK,CAC5G,CAAa,EAKb,kBACA,wBAAkD,wBAClD,CACA,CAAK,SACL,EAEA,iBACA,cACA,oBACA,eACA,CAkBA,OAjBA,oCACA,WACA,mBAAsD,uBAAqC,IAE3F,OADA,yBACA,6BACA,EACA,oCACA,4BACA,kCACA,QACA,mCAEA,CACA,EACA,8BACA,qCAAyD,mBAA8B,CACvF,EACA,CACA,CAAC,GACD,aACA,gBACA,iBACA,eACA,CAgBA,OAfA,+BACA,IAEA,sBACA,sCACA,iBACA,eACA,8BAGA,CACA,SACA,eACA,CACA,EACA,CACA,CAAC,0BCtKD,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,UAAkB,QAClB,MAAqB,EAAQ,KAAwB,EACrD,IAAyB,EAAQ,IADL,CACY,GACxC,EAAc,EAAQ,KAAS,EAC/B,EAAgB,EAAQ,KAAW,CADd,CAYrB,UAXuB,EAWL,CAVlB,YACA,qCACA,0BAA4D,OAC5D,6CACK,CAAI,UAIT,0BAAwD,YAAsB,IAF9E,IAGA,yBClCA,sCASA,OARA,6BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,WACA,CAAC,EACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,mCACA,SACA,0EACA,YACA,4DACA,8CAA6D,WAAc,IAC3E,uEACA,kBAEA,QACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,UAAkB,CAAG,kBAAwB,CAAG,cAAoB,QACpE,QAAyB,EAAQ,KAAO,GACxC,EAAc,EAAQ,KADU,EAEhC,EAAgB,EAAQ,KAAW,CADd,CAErB,UADuB,IACH,gBAEpB,kBAAwB,CADxB,YAAuC,8BAwCvC,YAAkB,CAtClB,YACA,gCAAuD,OACvD,qBACA,0CACA,kBACA,0BACA,8CACK,CAAI,mGACT,8BACA,uBACA,cACA,CAAK,QACL,yDACA,uBAGA,OAFA,gBACA,qBACA,WAA6B,uBAC7B,CAAK,QACL,2BACA,4CACA,eACA,UACA,CACA,CAAK,UACL,mCAAoD,IAAU,sOAC9D,SACA,mCACA,kBACA,+FACA,CAAS,uBACT,MAEA,GADA,mCACA,4BACA,kBACA,yBACA,CACA,EAAW,EACX,yBCtFA,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,yCACA,iFACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAChD,EAAQ,IAAmB,KACxC,EAAa,EAAQ,IADD,CACe,YAAf,ICGpB,UAZA,cACA,MAIA,YANA,mBAWA,yBCbA,8CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,UAAkB,QAClB,QAAuC,EAAQ,KAAgB,GAC/D,EAAgB,EAAQ,KADsB,EAE9C,EAAc,EAAQ,KAAU,CADT,CAMvB,UALqB,CAKrB,GACA,+CACA,QADA,iBACA,yCACA,EAkEA,YAAkB,CAjElB,WACA,gBACA,IACA,EADA,OAEA,gCACA,kBACA,iBACA,sBACA,aACA,iBACA,mBACA,gBAUA,GATA,aACA,GACA,eACA,mBAA2C,YAAwB,CAClD,EACjB,kBACiB,EAEjB,qDACA,UACA,wBACA,wGACA,gBACA,CACA,CAqCA,OApCA,iCAEA,0BAEA,IADA,4BACA,GACA,uBACA,qBAIA,qBACA,EACA,oCACA,WAEA,0BACA,SACA,0BAEA,YACA,+BACA,gBACA,CAAa,CAEb,EACA,sDAEA,eACA,+CACS,CACT,cACA,eACA,CAAK,EACL,uBACA,iBACA,EACA,CACA,CAAC,oBCxED,cACA,qBAEA,iBADA,uBAEA,+BAGA,UAcA,cACA,wBACA,CAEA,cACA,wBACA,CAWA,cACA,wBACA,CAOA,cACA,cACA,CAEA,cACA,uBAKA,cACA,eACA,WACA,qBACA,gBACA,iCACA,CAMA,SACA,yBAA2B,EAAI,EAE/B,KACA,iCAAmC,EAAI,GAEvC,gBAAkD,GAAM,iBAExD,KACA,6BAA+B,EAAI,8BAEnC,wCAEA,EACA,eACA,cACA,gBAEA,QAEA,cACA,WAEA,YAEA,mBACA,qBAEA,YACK,EAGL,uBACA,WACA,CAAK,CACL,CACA,OACA,sBACA,CACA,OACA,kBAEA,SACA,iCACA,CACA,CAEA,cACA,WACA,OACA,OACA,IACA,OAEA,cACA,IACA,OACA,WACI,CACJ,qBACA,uBAGA,aAGA,GAFA,IAEA,oBACA,cAEA,EACA,kBAIA,OACA,OACA,UAGA,YAAW,8BACX,CAEA,cACA,0BACA,CAEA,cACA,yBACA,CA2GA,OA5CA,mBAEA,gBAEA,QAEA,cAEA,cACA,wDApBA,kBAGA,kBAEA,qBAoBA,WAGA,aAMA,aAKA,qBAGA,MA5FA,cACA,SACA,KAEA,YACA,QAGA,QAGM,KA5IN,EAaA,EAkIA,QAFA,KAEA,CAEA,SACA,OAKA,GACA,gBACA,aAvJA,MAFA,EAyJA,IAtJA,QACA,EASA,EATA,IAUA,UAVA,wBAqJA,EAEA,OAvKA,MAuKA,EAvKA,YAVA,GAEA,sBACA,SAEA,WACA,4BACA,EA0KA,SACQ,SACR,KAEA,uBAA4C,IAAS,KACrD,aAEA,EAAQ,cAER,UAEA,MA3BA,SA4BA,EAKA,OAFA,2BAEA,UAqDA,kBAIA,mBAEA,iBAQA,EAEA,cAgCA,SACA,aACA,gBACA,oCACA,CAAI,EAAI,EACR,mBAjCA,SACA,cACA,iBAEA,OACA,OACA,wBAEA,YACA,gBAOA,4BAHA,mBAGA,MAIA,OAFA,WAEA,CACA,CAAK,CACL,QACA,SACA,CACA,CACA,EAOA,KACA,aACA,kBAEA,sBACA,CACA,iBACA,WACA,CACA,sBACA,cACA,CACA,cACA,YACA,iBACA,kBACA,qBACA,CAAK,CACL,CACA,UACA,mCAIA,kBAGA,gBACA,0BACA,oBACA,CAAO,EAGP,0BACA,oBACA,CAAO,EAGP,kBACA,CAEA,OACA,kBAEA,KACA,qBAEA,oBAEA,CAEA,YACA,yBAGA,0BAAyC,IAAS,KAClD,oBAEA,CACA,4BACA,2BAEA,OACA,2BAEA,gBACA,eACA,OAGA,OACA,IACA,IACA,kBACA,EAEA,oBACA,CACA,gBACA,OAAmB,UAGnB,0BACA,8CAEA,QAIA,UACA,SACA,IAAyB,0BAA2B,EAEpD,gBACA,mBAAkB,WAAwB,QAE1C,QAIA,gBACA,OACA,IACA,IACA,kBACA,EAEA,SACA,EAAY,UACZ,kBACA,QACA,iBACA,OACA,CAAe,CACf,CAAa,CAEb,CACA,QACA,EAAQ,oBACR,OACA,IACA,kBACA,CAEA,SACA,EACA,CAAK,EAEL,oBACA,CACA,SACA,OACA,eACA,qBAEA,CACA,CAEA,WACA,EACA,EACA,CAAI,qDAAiE,IAErE,mBAAkC,oBAAwB,EAI1D,OAHA,oBACA,gBACA,WACA,CACA,CAaA,WACA,EACA,CACA,WACA,oBACA,qBACA,sBACA,kCACA,CAAI,IAEJ,iBAEA,KACA,SAGA,2BAEA,EAKA,MAHA,KAIA,CAqOA,QACA,YACA,EACA,CACA,sBACA,wBACA,sBACA,kCACA,kCACA,0CACA,oCACA,kCACA,CAAM,GACN,CAgBA,GAfA,cACA,WACA,YACA,WACA,iBACA,iBACA,qBACA,kBACA,gBACA,EAEA,iCAEA,eAEA,qBACA,OAGA,cACA,kBACA,UACA,SA/CA,YACA,SAEA,uBAAwC,IAAS,MACjD,iBACA,wBACA,CAEA,QACA,EAsCA,GACA,YACA,CAAO,CACP,EAEA,sBAEA,KA/OA,GA+OA,CACA,QACA,IAjPA,GAkPA,MAEA,UACA,wBArPA,IAqPA,GACA,GAtPA,GAyPA,MACA,QA1PA,GA2PA,2BACA,CACA,EAAM,IACN,iBAEA,CAEA,YACA,oBAAY,oBAAkC,aAO9C,GALA,GACA,oBAIA,kBACA,OACA,WACA,OACA,EAMA,OAJA,GACA,6BAGA,CACA,CAGA,IACA,WACA,WACA,YACA,iBACA,qBACA,iBACA,CAAM,aAEN,KACA,IACA,KAEA,8BAA2B,0BAA+B,IAC1D,YAAc,qBAA0B,SAnSxC,EACA,EACA,EACA,CACA,sBACA,sBACA,wBACA,kCACA,0CACA,kCACA,kCACA,CAAI,QAsBJ,EApBA,YAhBA,GAiBA,cAjBA,KAoBA,eAEA,WAEA,4BAEA,IAEA,IAIA,SAEA,gBAKA,4BAWA,GAHA,WAPA,KACA,kBACA,mBACA,WACA,gBACA,CAAK,EAEL,GACA,MAEA,GACA,QACA,UACA,SACA,IAEA,CAIA,KAEA,SACA,IACA,MAEA,SAEA,YAAkB,IAAgB,MAIlC,QACA,IAEA,UASA,EARA,GACA,SACA,oBACA,mBACA,WACA,gBACA,CAAO,GAEP,EACA,IAEA,IAGA,wBAIA,IAEA,wBACA,IACA,EACA,kBAGA,YAEA,iBAEA,YAAyB,KAAY,MACrC,UACA,iBAgBA,GAdA,GAEA,YAIA,qBAGA,GACA,OACA,2BAGA,QACA,QACA,SACA,kBACA,mBACA,WACA,gBACA,EAAS,GAIT,GAMA,GAJA,IACA,OAGA,EACA,MAIA,mBACA,CAEA,CAWA,GARA,KACA,WACA,kBACA,mBACA,WACA,gBACA,CAAK,EAEL,EACA,MAGA,GACA,CAEA,OACA,aAEA,sBACA,EAEA,MACA,eA5MA,KACA,wBAEA,SACA,KACA,KACA,IAEA,mBAAmC,IAAS,MAC5C,WACA,UACA,IACM,YACN,QACA,QACA,cAEA,KAEA,CAOA,OAJA,gBACA,gBAGA,CACA,EAiLA,IACA,UAEM,GACN,cAFA,YAIA,CAEA,QACA,EA+GwC,OACxC,aACA,WACA,YACA,iBACA,qBACA,iBACA,gBACA,CAAO,CAEP,IACA,OAGA,KAEA,MACA,eAEA,CAAK,EAEL,OACA,UACA,8BACA,EAMA,OAJA,MACA,cAGA,CACA,CACA,CAEA,QACA,eACA,cACA,CACA,uBACA,2BACA,CACA,wBACA,4BACA,CACA,UACA,CAEA,gBACA,iBACA,kBACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,aACA,CACA,wBACA,iBACA,CACA,yBACA,eACA,CACA,UACA,uBAEA,OACA,UACA,UACA,kCAEA,CACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,qBACA,CACA,wBACA,iBACA,CACA,yBACA,eACA,CACA,UAEA,WADA,wBAGA,OACA,UACA,UACA,uBAEA,CACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,oBACA,CACA,wBACA,kBACA,CACA,yBACA,gBACA,CACA,UACA,iCAEA,OACA,UACA,UACA,kCAEA,CACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,4BACA,CACA,wBACA,mBACA,CACA,yBACA,iBACA,CACA,UACA,kCAEA,OACA,UACA,UACA,sBACA,CACA,CACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,oBACA,CACA,wBACA,kBACA,CACA,yBACA,gBACA,CACA,UACA,+BAEA,OACA,UACA,UACA,kDAEA,CACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,4BACA,CACA,wBACA,mBACA,CACA,yBACA,iBACA,CACA,UACA,gCACA,OACA,UACA,UACA,sBACA,CACA,CACA,CAEA,kBACA,YACA,EACA,CACA,sBACA,wBACA,sBACA,kCACA,kCACA,0CACA,oCACA,kCACA,CAAM,GACN,CACA,SACA,2BACA,WACA,YACA,WACA,iBACA,iBACA,qBACA,kBACA,gBACA,CAAK,CACL,CACA,kBACA,aACA,CACA,wBACA,gBACA,CACA,yBACA,cACA,CACA,UACA,oCACA,CACA,CAIA,kBACA,eACA,QACA,CACA,kBACA,eACA,CACA,wBACA,iBACA,CACA,yBACA,eACA,CACA,UACA,IACA,EADA,IAGA,KACA,sBAGA,uCACA,MACA,gBAGA,iBAEA,OACA,UACA,UACA,SACA,CACA,CACA,CAGA,OACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CAEA,WAGA,uCAmDA,0BA8BA,SACA,YACA,EACA,CACA,oCACA,kCACA,0CACA,kCACA,kCACA,sBACA,wBACA,sBACA,CAAM,GACN,CACA,gBACA,cACA,kBACA,iBACA,qBACA,iBACA,iBACA,WACA,YACA,UACA,EAEA,iCACA,WAtGA,eAAyC,EACzC,eANA,KAMA,QACA,QACA,OACA,SACA,yBAEA,KACA,uBAAwC,IAAS,MACjD,WAGA,KACA,KACA,iBACA,WACA,oBACA,IACA,mBACA,KAEA,CAEA,MAMA,IADA,KACA,QACA,WACA,qBACA,MACA,mBACA,KACA,CACA,CACA,CAEA,QACA,CAAG,CACH,EA6DA,0BACA,CAEA,sBACA,2BAGA,YACA,iBAEA,MACA,OACA,WACA,OACA,EAGA,mBAAY,qBAAkC,aAE9C,sBAEA,QACA,KACA,IAGA,uBAAyC,IAAU,MACnD,WAGA,WACA,IAGA,uBAA+C,IAAU,MACzD,WACA,SAAgB,qBAA0B,YAE1C,KAGA,IAFA,KACA,KACA,GACA,yBACA,SACA,cAEA,SAEA,MACU,CACV,IACA,IACA,WACA,KACA,CACA,CAGA,MACA,OACA,WACA,SACA,EAMA,OAJA,GACA,cAGA,CACA,CACA,CAGA,OACA,WACA,OACA,CACA,CACA,CAEA,SAMA,gBACA,uBAAoD,IAAS,MAC7D,WACA,oBACA,iBAEA,CAEA,iBACA,CAEA,OACA,WACA,QACA,EAEA,GACA,aACA,cACA,EAEA,KACA,sBAEA,iBAEA,KACA,mBAEA,OACA,gCACA,SACA,GACA,CAAC,CAID,gBAAiC,WAAc,EAAI,EACnD,UACA,qBAEA,OAEA,yBACA,eAGA,SACA,uBAEA,sBAEA,SACA,kBAGA,OACA,WACA,SACA,EAMA,OAJA,GACA,oBAGA,CACA,CAEA,OACA,YACA,eAaA,OAVA,cACA,WAEA,MACA,cACA,qBACA,CAAS,CAET,CAAK,EAEL,CACA,EAMA,OAJA,MACA,SAGA,IACA,CAuBA,gBACA,gBACA,aAEA,MAIA,cACA,oCACA,OAGA,YAAY,WAAiB,EAE7B,GACA,UACA,OACA,CAEA,QACA,kBAGA,UACA,mBAGA,iBACA,CAAG,CACH,CAEA,gBACA,gBAkCA,QACA,kBAAgC,IAChC,cAAqB,WAGrB,+BAMA,wCAEA,uBACA,CAEA,mBAGA,GAFA,aAEA,qBACA,YAz6CA,yBA46CA,eACA,GACA,gCACA,yBACA,6CACO,CACP,CAEA,OACA,OAIA,mBACA,qBACA,CAEA,iBACA,SAEA,gCAA6C,IAAS,MACtD,oBACA,SACA,iBACA,KACA,KAEA,UAEA,CAEA,QACA,CAEA,YACA,uBACA,yBACA,CAEA,WACA,qBAGA,gBAAkB,MAAa,EAAI,EACnC,IACA,iBACA,eACA,aACA,SACA,kBACA,CAAM,aAEN,OACA,iBACA,0BACA,0BACA,uBAYA,OAVA,SAvKA,EACA,CAAI,sCAEJ,cACA,QAEA,wBAA8B,iBAAkB,IAChD,sBAEA,YACA,0BACA,eAEA,CAAK,EAEL,SACA,CAAG,CACH,EAsJA,mBAA4B,EAAiB,EAE7C,GACA,UAGA,YACA,iBAvHA,SACA,EACA,EACA,CACA,kCACA,8BACA,CAAI,IAEJ,SAKA,OAHA,aACA,aAEA,UACA,QAAY,GAAM,EAElB,GACA,UACA,UACA,EAQA,OANA,UACA,cACA,MACA,CAAO,EAGP,CACA,CAAG,CACH,EA6FA,cACA,iBACA,cACA,CAAK,CACL,CAEA,qBACA,wBACA,SAAY,GAAU,cACtB,KAmBA,OAhBA,YAAuB,YAA0B,IACjD,SACA,OAGA,IAAc,6BAA0B,aAExC,IACA,QACA,OACA,MACA,gBAAsB,2BAAmC,CACzD,CAAS,CAET,CAAK,EAEL,CACA,CAEA,kBAEA,wBAEA,YACA,gBACA,UAAgB,cAAkB,EAElC,qBACA,0BACA,gDACA,UACA,CAAS,SAET,YACA,CACA,CACA,MACA,OACA,SACA,EACA,CAGA,GAGA,SACA,gCAAkD,IAAS,MAE3D,QADA,cACA,KACA,YACA,kBACU,sBACV,SAGA,QACA,EAEA,wBACA,KACA,KAmBA,OAjBA,YAAuB,QAAiB,IACxC,SACA,cAEA,YAEA,OACA,UAA+B,qBAC/B,cAEA,oBAAgC,EAAS,IACzC,uBACA,CAAW,EAEX,CACA,CAAK,EAEL,CACA,CAEA,qBACA,wBACA,MAAY,aAAgB,cAC5B,KA8BA,OA3BA,YAAuB,QAAiB,IACxC,SACA,OAGA,SAGA,kBACA,UACA,mBACA,MACA,WACA,UACA,CAAW,EAEX,CAAO,EAEP,UACA,QACA,MACA,OACA,SACA,CAAS,CAET,CAAK,EAEL,CACA,CACA,kBAAiB,qBAAsB,EACvC,SACA,SAGA,SAEA,QACA,YAAuB,YAA0B,IACjD,SACA,OAGA,YAAgB,qBAA0B,aAE1C,IACA,QACA,QACA,MACA,QACA,MACA,OACA,SACA,CAAW,CAEX,CAAO,MACD,CACN,IAAc,SAAmB,EAEjC,SAAc,qBAA0B,aAExC,IACA,cAAuB,iCAAwC,CAE/D,CAEA,QACA,CACA,CAEA,kBACA,gBACA,aArxCA,SACA,EACA,OAAI,+CAAiE,IAErE,SAAU,aAAgB,EAC1B,eAAkC,oBAAwB,EAG1D,OAFA,aACA,qBACA,CACA,EA6wCA,WAGA,eApcA,eACA,YACA,EAscA,0BC/uDA,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,SACA,CAAC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,qCAA6C,CAAE,SAAa,EAC5D,cAAoB,CAAG,aAAmB,QAC1C,MAAiB,EAAQ,KAAY,EACrC,IAAyB,EAAQ,IADT,CACgB,GACxC,EAAuB,EAAQ,KAAkB,EACjD,UAD8B,GACX,mBAAyB,EAO5C,cAAoB,CANpB,YACA,wBACA,+CAAgE,QAAqB,CACrF,uCACA,WACA,mBC9BA,oBACA,wBACA,kCACA,6BACA,wBACA,kCAKA,cACA,+CACA,kBACA,oBACA,OAA6B,kDAA4F,EAEzH,QACA,EAYA,KAvBA,SACA,eACA,OAA8B,uBAAkC,EAChE,EAqBA,GACA,WACA,WACA,gBACA,iBACA,qBACA,CAAC,EACD,UAXA,MAAoD,eAAkB,SAAa,EAWnF,GAGA,OAtBA,8BAAmG,GAKnG,oBAAuG,EAAvG,eAAyE,sBAA8B,EACvG,EACA,GAeoB,EAAQ,KAAO,GACnC,EAAgC,EAAQ,KADb,EAE3B,EAAyB,EAAQ,KAAmB,CADb,CAGvC,UAFgC,CAEhC,GACA,eAwBA,GACA,2BACA,aAAY,QAAyB,EACrC,4BAmDA,MACA,EACA,EApDA,GAkDA,EAlDA,EAqDA,CADA,GADA,wDACA,wCAEA,MAGA,IADA,kDACA,wCAEA,YAEA,oBA5DA,WAyBA,KACA,OAA0B,MAC1B,gBACA,WACA,OACA,mBAEA,KACA,cACA,cAEA,OADA,QACA,CACA,EACQ,GACR,SAEM,YACN,MAAkC,WAC5B,iBACN,sCAEA,CACA,OAAW,UACX,EAhDA,WAIA,OAHA,qBACA,mCAEA,mBACA,CACA,uDACA,CAAG,EAEH,OADA,iBAA6B,EAAU,YACvC,CACA,EAvCA,GACA,uBACA,aAAY,QAAyB,EACrC,wBACA,YACA,MACA,uBACA,WACA,MAIA,EAHA,4CACA,2CAKA,mBAAsE,wEAA0I,CAChN,CACA,mBAAoE,sBAA2C,CAC/G,CAAG,EAEH,OADA,iBAAyB,EAAU,OACnC,CACA,CACA,gBAkBA,4BAEA,cACA,iBAAwB,EAAU,GAClC,+BAAsF,EAAU,EAIhG,OAFA,iBAA8B,EAAU,YACxC,cACA,CACA,CACA,qBACA,cACA,iGACA,mBClGA,qCAA6C,CAAE,SAAa,EAC5D,SAAe,CAAG,iBAAuB,QACzC,MAAc,EAAQ,KAAU,EAChC,UADqB,GAErB,cACA,uBACA,qBAKA,OAHA,qBACA,eACA,EACA,CACA,CAAC,GACD,iBAAuB,GAwFvB,SAvFA,YACA,aAOA,OANA,kBACA,kBACA,aACA,gBACA,aAEA,WA6EA,OA3EA,4BACA,WACA,4BAEA,8CAEA,+CACA,iCAGA,IALA,EAKA,wCACA,gCACA,SAEA,oBACA,SAEA,CAAS,CACT,EACA,4BACA,kBAEA,OADA,uBACA,CACA,EACA,+BACA,2CAAgE,aAAoB,EACpF,sBACA,2BAGA,2CAAgE,aAAoB,CACpF,SACA,0BAEA,EACA,6BAEA,OACA,2BACA,MACA,OAGA,OAFA,oBACA,uBACA,CACA,CAEA,2CAA4D,aAAoB,EAChF,UAKA,OAHA,2BACA,WACA,uBACA,CACA,EACA,6BACA,OACA,2BACA,MACA,OAGA,OAFA,qBACA,uBACA,CACA,CACA,2CAA4D,aAAoB,EAChF,UAKA,OAHA,2BACA,YACA,uBACA,CACA,EACA,6BACA,yBACA,wBACA,EACA,CACA,CAAC,IAED,SAAe,GACf,gCCvGA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,OAAe,QA+Bf,SAAe,CA7Bf,cACA,UACA,cAAkC,MAClC,wBACA,kBAEA,yBAGA,gBACA,IAIA,eACA,wBAEA,qBACA,kBACA,QACA,CAAa,EACb,WACA,gBAAoC,6BAAyC,CAC7E,gBAAoC,6BAAyC,GAE7E,CACA,yBC7BA,sCASA,MARA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,YAAoB,QACpB,QAAyB,EAAQ,KAAO,GACxC,EAAc,EAAQ,KADU,EAEhC,EAAgB,EAAQ,KAAW,CADd,CAErB,EAAc,EAAQ,KAAS,CADR,CAEvB,GACA,CACA,MAHqB,EAGrB,EACA,sBACA,CAAK,CACL,CAAM,kCAAsC,CAC5C,CAAM,+BAAmC,CACzC,CACA,GACA,CACA,oBACA,CAAK,CACL,CACA,sBACA,CAAK,CACL,CACA,oBACA,CAAK,CACL,CA6FA,cAAoB,CA5FpB,YAEA,IADA,IACA,kEACA,4BAAuD,OACvD,0BACA,0CACK,CAAI,gEACT,iBACA,iBACA,8DACA,6DAEA,uBACA,8BAGA,wCACA,kBACA,gBACA,WACA,OAEA,oDACA,4DACA,eACA,CAAS,EACT,CAAK,YAEL,iBACA,uBAEA,8BACA,gBACA,YACA,SAGA,qCACA,YAAsD,EAAtD,EAAsD,OAAuB,KAE7E,OADA,IACA,YACA,WACA,qBAEA,WACA,CACA,qBACA,CAAyB,CACzB,CACA,oBACA,CAAyB,CACzB,EACA,aAEA,kBACA,eACA,CAAqB,EACrB,mBAEA,CAAa,EAEb,OADA,aACA,WACA,cACA,EACA,CACA,CAAK,YAEL,mBAsBA,OArBA,uBACA,cACA,aACA,MACA,CACA,eACA,IACA,aACA,WACA,iBACA,CAAa,CAEb,CAAK,QACL,iCACA,QACA,IAGA,6CACA,4DACA,CAAK,EACL,uBAAyC,oBAAmD,WAA8C,qBAAuB,cAAyB,CAC1L,uBAAqC,MAAe,IACpD,yBClJA,sCASA,MARA,8BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,4CACA,mDAA6E,IAAO,KACpF,YACA,yCACA,WAGA,iDACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,QAAgB,CAAG,UAAgB,CAAG,wBAA8B,CAAG,mBAAyB,CAAG,mBAAyB,CAAG,qBAA2B,CAAG,MAAY,CAAG,cAAoB,CAAG,UAAgB,CAAG,2BAAiC,CAAG,eAAqB,CAAG,cAAoB,QACtS,QAAyB,EAAQ,KAAO,GAyCxC,aACA,gDACA,CAMA,cA5CA,cAAoB,CAJpB,YACA,oBACA,kBACA,EAsBA,eAAqB,CApBrB,cACA,iBACA,aACA,uBACA,cACA,cACA,qCAGA,+DAGA,mBACA,oBACA,YACA,CAEA,OADA,4CACA,WAA6B,sDAC7B,CAAK,KACL,EAeA,2BAAiC,CAbjC,WACA,mCAUA,OATA,uBACA,aACA,KACA,CACA,MAEA,OADA,yCACA,WAAiC,mDAEjC,CAAK,MACL,CACA,EAKA,UAAgB,GAIhB,cAAoB,CAHpB,YACA,UAAsB,OAAgB,GACtC,EAGA,MAAY,GACZ,qBAA2B,gDAa3B,mBAAyB,CAXzB,WACA,mCACA,6BACA,0BACA,6BACA,oCACA,iBACA,kCAEA,OADA,4BACA,CACA,EAmBA,mBAAyB,CAjBzB,cACA,aAAyB,OACzB,kCACA,uBAYA,OAXA,uBACA,UAEA,4BACA,KACA,oBACA,CAAS,2BACT,kBACA,eACA,EACA,CAAK,QACL,SACA,EAkBA,wBAA8B,CAhB9B,YAGA,IAFA,MAEA,yBADuD,CAAvD,YAA+B,sBAAwB,qBACvD,oBACA,sBACA,CAAK,EACL,yBAQA,OAPA,GACA,0CACA,0EACA,0EACA,QACA,0EACA,iBAEA,EAGA,mBADA,eACA,yCAIA,QAAgB,CAHhB,YACA,4BACA,EAEA,UAAgB,EAChB,OACA,SACA,MACA,0KCvJA,kBAQA,gBACA,wBACA,CAOA,cACA,mDAQA,cACA,qCAQA,cACA,uBACA,CAuBA,MA7DA,2BA+DA,WACA,kBACA,EApBA,WACA,SACA,OACA,gBACA,SACA,CAAS,CACT,gBACA,wBACA,CACA,CACA,EAoBA,cACA,mBACA,WACA,uBACA,aAAoC,OACpC,8BACA,0BACA,SACA,kBACA,cACA,QACA,WAEA,IACA,SAEA,GACA,QAEA,CACA,eACA,CACA,CACA,CAwDA,4FAUA,oBACA,WACA,WACA,mBACA,SAEA,KAEA,IADA,aACA,SAEA,cADA,SACA,CACA,WACA,OACA,WACA,MACA,QAEA,CACA,4BACA,QAEA,CAEA,QACA,CAkDA,6BACA,yBACA,cACA,MAEA,qBACA,KACA,EAUA,kBACA,SACA,SAEA,iDACA,cACA,kBAEA,uBACA,0BACA,KACA,OA9JA,kBACA,eACA,gBACA,SAEA,aACA,mBACA,SAGA,QACA,EAmJA,UAEA,oBACA,oBACA,MACA,mCAEA,sBACA,sBACA,MACA,SA/EA,SA+EA,EA/EA,QACA,qBACA,6BACA,2BACA,uBA2EA,EA1EA,mBA0EA,EAzEA,wBA2EA,WACA,MAEA,IACA,mBACA,mBACA,MACA,OA5JA,kBACA,sBACA,cACA,SACA,wBACA,MACA,SACA,IACA,wBACA,WACA,GACA,qBAEA,UAGA,GACA,CAAiB,EACjB,GACA,CACA,CAAS,CACT,CACA,QACA,EAqIA,SAGA,IACA,mBACA,mBACA,MACA,gBA9EA,SACA,sBACA,cACA,SACA,sBACA,MACA,SACA,IACA,sBACA,WACA,cAEA,UAGA,GACA,CAAiB,EACjB,GACA,CACA,CAAS,CACT,CACA,QACA,EAwDA,SAGA,UACA,CACA,iBACA,CACA,QACA,CAEA,UACA,eAAkD,SAA4B,EAC9E,SACA,iCChTA,sCASA,OARA,6BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,WAAmB,QACnB,QAAyB,EAAQ,KAAO,GACxC,EAAsB,EAAQ,IAAe,CADb,CAEhC,EAAmB,EAAQ,KAAc,EADZ,EAEb,EAAQ,KAAW,CADT,CAE1B,EAAc,EAAQ,KAAS,CADR,GAkKvB,QAjKqB,GAiKF,CA/JnB,YACA,qBACA,iBAGA,mBACA,mBACA,wBACA,sBACA,WACA,CAAK,EACL,4BAAuD,OACvD,qBACA,0CACA,yBACA,CAAK,CAAI,2EACT,uBACA,kBACA,KACA,iBAGA,2CACA,mBACA,oBACA,6BACA,QA3BA,EA2BA,MAEA,kCACA,SACA,SACA,IACA,CACA,QACA,CAAiB,GAEjB,qBACA,wBACA,mBACA,oBACA,6BACA,iCAEA,kCACA,0BACA,SACA,IACA,CACA,QACA,CAAiB,GAEjB,kBACA,mBACA,oBAKA,gCAEA,EAEA,OADA,qCAAsD,WAAe,EACrE,WACA,+CAAoE,WAAe,CACnF,CACA,CAAK,MAGL,sBACA,uBACA,KAIA,uBACA,CAAS,CACT,CAAK,QAEL,uBACA,iBAEA,6BAlFA,EAkFA,EAGA,CAAK,UAEL,uBAEA,yBACA,GAFA,EAEA,SACA,OACA,qCACA,OAEA,mBACA,MACA,SACA,2BAVA,EAUA,EACA,MAXA,EAWA,CACA,OACA,gCACA,GAdA,EAcA,GAEA,MACA,MACA,+BACA,mBAEA,CACA,CAAK,gBACL,gCACA,OACA,sBAEA,WACA,qBACA,aAGA,gBACA,8BAEA,qEACA,CAAK,QACL,oCACA,8BAAyC,aACzC,2BACA,oBACA,eACA,EAAW,CACX,uBAAqC,wCACrC,wBACA,YACA,EAAe,gCACf,yBACA,uBACA,yBACA,UACA,aACA,yBACA,CAAiB,CACjB,yBAA6C,iCAAgD,CAC7F,mBAAuC,YAAuB,EAE9D,cACA,gCAA0D,mGAC1D,oBACA,MACA,OACA,aACA,qCACA,EAAmB,+BACnB,OACA,QACA,CAAa,GACb,gBACA,CAAa,EACb,CAAS,GACT,yBCpMA,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,0CACA,0BAA6C,UAC7C,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,cAAsB,CAAG,YAAkB,CAAG,UAAgB,QAC9D,QAAyB,EAAQ,KAAO,GACxC,EAAgB,EAAQ,KADQ,EAEhC,EAAc,EAAQ,KAAS,CADR,CAEvB,IAAgC,EAAQ,IADnB,CAC4B,GACjD,SADuC,CACvB,EAChB,YACA,4BAEA,OACA,MACA,CACA,YACA,SACA,CAAS,CACT,CACA,gBACA,kBAAqC,MAAQ,6CAA+E,CAC5H,SACA,CAAS,CACT,WACA,CACA,kBACA,gBACA,kBACA,aACA,oBACA,EACA,gBAIA,6BAKA,aACA,IAsHA,MACA,EAIA,MA3HA,4BAAuD,OACvD,qBACA,kBACA,mCACK,CAAI,0CACT,uBACA,sBACA,qBACA,WAIA,GAHA,aACA,UAEA,SACA,YAAgC,oBAA4B,IAC5D,sBAGA,QACA,CAAS,KACT,OACA,CAAK,QACL,4BAEA,QADA,KACA,IAAwB,WAAoB,IAC5C,aAEA,uBACA,aAAkC,KAClC,YAA4B,WAAoB,IAChD,2BAEA,QADA,gBACA,IAAsC,WAA6B,IACnE,aAEA,kBACA,CAEA,QACA,CAAS,GACT,CAAK,KACL,KACA,8BACA,EACA,EACA,IACA,CAAK,UACL,uBAA2C,0BAAsD,MACjG,GAuEA,EAvEA,EAuEA,EAvEA,EAuEA,EAvEA,EAwEA,uBAA4C,OAC5C,WACA,QACA,CAAK,CAAI,QACT,qDACA,qBACA,iBACA,yBAA6D,OAAU,iBAA0B,CAAI,EAErG,SAWA,OAPA,EAFA,UAEA,gBACA,uBACA,OACA,0BACA,QACA,CAAa,CACJ,CAET,CAAK,WA5FL,uBAoBA,QAnBA,IAOA,KAKA,KAMA,KACA,IAAwB,WAAoB,KAC5C,WACA,WACA,6BACA,GACA,gCACA,UACA,qDACA,oCACA,EACA,4CACA,CACA,aACA,aACA,WAEA,gBACA,sBACA,QACA,CAAa,CACb,CACA,4BAA0D,OAC1D,YACA,0CAAuE,gBAAqB,CAC5F,CAAS,CAAI,EAMb,QADA,KACA,IAAwB,WAAoB,KAC5C,WACA,0BACA,eACA,YAA8B,mBAA4B,IAC1D,oBAEA,CACA,QACA,CAAK,MAIL,uBAAuD,SAAsB,MAC7E,4BAAuC,OACvC,UACA,cACA,CAAK,CAAI,OACT,CACA,YAAkB,GA4BlB,gBAAsB,0BC9MtB,sCASA,OARA,6BACA,iCAAiD,IAAO,IAExD,aADA,eACA,2CACA,YAEA,QACA,GACA,qBACA,EACA,+DACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,8DACA,mCAA0C,sBAA4B,CACtE,CAAC,eACD,YACA,CAAC,CACD,uCACA,4BACA,SACA,6FAEA,OADA,OACA,CACA,EACA,qCAA6C,CAAE,SAAa,EAC5D,SAAe,QACf,QAAyB,EAAQ,KAAO,GACxC,EAA4B,EAAQ,KADJ,EAqBhC,SAAe,CApBoB,SACnC,GACA,mFACA,kCACA,cACA,4BAA6D,aAA4B,KAAgB,kBAAgC,EAAK,QAC9I,yCAYA,OAXA,uBACA,MAIA,OAHA,WACA,iBAAyD,oBAAuC,aAAyB,gBAAyC,EAElK,WACA,GACA,GAEA,CACA,CAAK,QACL,CACA,ocCrDA,yICAA,MAAuB,YACvB,EAAqB,YACrB,EAA0B,SAC1B,EAAgC,SAuBJ,WANtB,EAAe,aAAuC,CAAC,EAAO,KAClE,GAAM,CAAE,UAAW,EAAe,GAAG,EAAY,CAAI,EAC/C,CAAC,EAAS,EAAU,CAAU,CADa,CACb,KAAV,GAAU,EAAS,GAC7C,EADkD,CAClD,mBAAgB,IAAM,GAAW,GAAO,CAAH,CAAK,EAC1C,IAAM,EAAY,GAAkB,GAAW,YAAY,UAAU,KACrE,OAAO,EACH,EAAAA,OAAAA,CAAS,aAAa,UAAC,YAAU,IAAV,CAAe,GAAG,EAAa,IAAK,EAAc,EAAI,GAC7E,IACN,CAAC,CAF2F,GAIrF,YArBa,EAqBC,OAIrB,IAAM,EAAO,mBCjCb,oBACA,wBACA,kCACA,6BACA,wBACA,kCAKA,cACA,+CACA,kBACA,oBACA,OAA6B,kDAA4F,EAEzH,QACA,EAYA,KAvBA,SACA,eACA,OAA8B,uBAAkC,CAChE,GAqBA,GACA,kBACA,qBACA,CAAC,EACD,UARA,MAAoD,eAAkB,SAAa,EAQnF,GAGA,MAnBA,+BAAmG,GAKnG,oBAAuG,EAAvG,eAAyE,sBAA8B,EACvG,EACA,GAYoB,EAAQ,KAAO,GACnC,SAD2B,EAC3B,KACA,wBACA,WACI,UACJ,aAEA,CACA,iBACA,WACA,SACA,YACA,aAIA,OAHA,yBACA,OAEA,CACA,CAAK,EACL,KACA,WACA,YAAwB,WAAqB,KAC7C,WACA,qBACA,IAEA,YAEA,CACA,CAEA,CACA,CACA,iBACA,+BACA,yBCvEA,mEACA,kBACA,2BAAmC,6BAAoC,aAAgB,CACvF,CAAC,mBACD,kBACA,UACC,EACD,yCACA,iFACA,EACA,qCAA6C,CAAE,SAAa,EAAC,EAC7D,QAAgB,CAAG,cAAoB,QACvC,MAAc,EAAQ,KAAS,EAC/B,UADqB,WACrB,kBAA+C,CAAE,6BAAqC,uBAAgC,EACtH,mCAA2C,CAAE,6BAAqC,mBAA4B,EAAC,EAClG,EAAQ,KAAc,KACnC,EAAa,EAAQ,GADD,EACe,KACnC,EAAa,EAAQ,GADD,EACmB,KACvC,EAAa,EAAQ,GADD,EACe,KACnC,EAAa,EAAQ,GADD,EACgB,KACpC,EAAa,EAAQ,GADD,EACY,KAChC,EAAa,EAAQ,GADD,CACuB,KAC3C,EAAa,EAAQ,IADD,CACwB,KAC5C,EAAa,EAAQ,GADD,EACiB,KACrC,EAAa,EAAQ,GADD,EACU,KAC9B,EAAa,EAAQ,GADD,EACW", "sources": ["webpack://terang-lms-ui/./node_modules/kbar/lib/action/ActionInterface.js", "webpack://terang-lms-ui/./node_modules/react-virtual/dist/react-virtual.mjs", "webpack://terang-lms-ui/./node_modules/kbar/lib/useRegisterActions.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/KBarPositioner.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/types.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/InternalEvents.js", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-primitive/dist/index.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/tinykeys.js", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-use-layout-effect/dist/index.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/useStore.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/KBarPortal.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/KBarSearch.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/action/index.js", "webpack://terang-lms-ui/./node_modules/tiny-invariant/dist/tiny-invariant.cjs.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/action/ActionImpl.js", "webpack://terang-lms-ui/./node_modules/fuse.js/dist/fuse.esm.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/KBarContextProvider.js", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-slot/dist/index.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/action/HistoryImpl.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/action/Command.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/KBarAnimator.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/utils.js", "webpack://terang-lms-ui/./node_modules/fast-equals/dist/fast-equals.esm.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/KBarResults.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/useMatches.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/useKBar.js", "webpack://terang-lms-ui/../src/index.ts", "webpack://terang-lms-ui/../src/portal.tsx", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-compose-refs/dist/index.js", "webpack://terang-lms-ui/./node_modules/kbar/lib/index.js"], "sourcesContent": ["\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ActionInterface = void 0;\nvar tiny_invariant_1 = __importDefault(require(\"tiny-invariant\"));\nvar ActionImpl_1 = require(\"./ActionImpl\");\nvar ActionInterface = /** @class */ (function () {\n    function ActionInterface(actions, options) {\n        if (actions === void 0) { actions = []; }\n        if (options === void 0) { options = {}; }\n        this.actions = {};\n        this.options = options;\n        this.add(actions);\n    }\n    ActionInterface.prototype.add = function (actions) {\n        for (var i = 0; i < actions.length; i++) {\n            var action = actions[i];\n            if (action.parent) {\n                (0, tiny_invariant_1.default)(this.actions[action.parent], \"Attempted to create action \\\"\" + action.name + \"\\\" without registering its parent \\\"\" + action.parent + \"\\\" first.\");\n            }\n            this.actions[action.id] = ActionImpl_1.ActionImpl.create(action, {\n                history: this.options.historyManager,\n                store: this.actions,\n            });\n        }\n        return __assign({}, this.actions);\n    };\n    ActionInterface.prototype.remove = function (actions) {\n        var _this = this;\n        actions.forEach(function (action) {\n            var actionImpl = _this.actions[action.id];\n            if (!actionImpl)\n                return;\n            var children = actionImpl.children;\n            while (children.length) {\n                var child = children.pop();\n                if (!child)\n                    return;\n                delete _this.actions[child.id];\n                if (child.parentActionImpl)\n                    child.parentActionImpl.removeChild(child);\n                if (child.children)\n                    children.push.apply(children, child.children);\n            }\n            if (actionImpl.parentActionImpl) {\n                actionImpl.parentActionImpl.removeChild(actionImpl);\n            }\n            delete _this.actions[action.id];\n        });\n        return __assign({}, this.actions);\n    };\n    return ActionInterface;\n}());\nexports.ActionInterface = ActionInterface;\n", "import React from 'react';\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nvar props = ['bottom', 'height', 'left', 'right', 'top', 'width'];\n\nvar rectChanged = function rectChanged(a, b) {\n  if (a === void 0) {\n    a = {};\n  }\n\n  if (b === void 0) {\n    b = {};\n  }\n\n  return props.some(function (prop) {\n    return a[prop] !== b[prop];\n  });\n};\n\nvar observedNodes = /*#__PURE__*/new Map();\nvar rafId;\n\nvar run = function run() {\n  var changedStates = [];\n  observedNodes.forEach(function (state, node) {\n    var newRect = node.getBoundingClientRect();\n\n    if (rectChanged(newRect, state.rect)) {\n      state.rect = newRect;\n      changedStates.push(state);\n    }\n  });\n  changedStates.forEach(function (state) {\n    state.callbacks.forEach(function (cb) {\n      return cb(state.rect);\n    });\n  });\n  rafId = window.requestAnimationFrame(run);\n};\n\nfunction observeRect(node, cb) {\n  return {\n    observe: function observe() {\n      var wasEmpty = observedNodes.size === 0;\n\n      if (observedNodes.has(node)) {\n        observedNodes.get(node).callbacks.push(cb);\n      } else {\n        observedNodes.set(node, {\n          rect: undefined,\n          hasRectChanged: false,\n          callbacks: [cb]\n        });\n      }\n\n      if (wasEmpty) run();\n    },\n    unobserve: function unobserve() {\n      var state = observedNodes.get(node);\n\n      if (state) {\n        // Remove the callback\n        var index = state.callbacks.indexOf(cb);\n        if (index >= 0) state.callbacks.splice(index, 1); // Remove the node reference\n\n        if (!state.callbacks.length) observedNodes[\"delete\"](node); // Stop the loop\n\n        if (!observedNodes.size) cancelAnimationFrame(rafId);\n      }\n    }\n  };\n}\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n\nfunction useRect(nodeRef, initialRect) {\n  if (initialRect === void 0) {\n    initialRect = {\n      width: 0,\n      height: 0\n    };\n  }\n\n  var _React$useState = React.useState(nodeRef.current),\n      element = _React$useState[0],\n      setElement = _React$useState[1];\n\n  var _React$useReducer = React.useReducer(rectReducer, initialRect),\n      rect = _React$useReducer[0],\n      dispatch = _React$useReducer[1];\n\n  var initialRectSet = React.useRef(false);\n  useIsomorphicLayoutEffect(function () {\n    if (nodeRef.current !== element) {\n      setElement(nodeRef.current);\n    }\n  });\n  useIsomorphicLayoutEffect(function () {\n    if (element && !initialRectSet.current) {\n      initialRectSet.current = true;\n\n      var _rect = element.getBoundingClientRect();\n\n      dispatch({\n        rect: _rect\n      });\n    }\n  }, [element]);\n  React.useEffect(function () {\n    if (!element) {\n      return;\n    }\n\n    var observer = observeRect(element, function (rect) {\n      dispatch({\n        rect: rect\n      });\n    });\n    observer.observe();\n    return function () {\n      observer.unobserve();\n    };\n  }, [element]);\n  return rect;\n}\n\nfunction rectReducer(state, action) {\n  var rect = action.rect;\n\n  if (state.height !== rect.height || state.width !== rect.width) {\n    return rect;\n  }\n\n  return state;\n}\n\nvar defaultEstimateSize = function defaultEstimateSize() {\n  return 50;\n};\n\nvar defaultKeyExtractor = function defaultKeyExtractor(index) {\n  return index;\n};\n\nvar defaultMeasureSize = function defaultMeasureSize(el, horizontal) {\n  var key = horizontal ? 'offsetWidth' : 'offsetHeight';\n  return el[key];\n};\n\nvar defaultRangeExtractor = function defaultRangeExtractor(range) {\n  var start = Math.max(range.start - range.overscan, 0);\n  var end = Math.min(range.end + range.overscan, range.size - 1);\n  var arr = [];\n\n  for (var i = start; i <= end; i++) {\n    arr.push(i);\n  }\n\n  return arr;\n};\nfunction useVirtual(_ref) {\n  var _measurements;\n\n  var _ref$size = _ref.size,\n      size = _ref$size === void 0 ? 0 : _ref$size,\n      _ref$estimateSize = _ref.estimateSize,\n      estimateSize = _ref$estimateSize === void 0 ? defaultEstimateSize : _ref$estimateSize,\n      _ref$overscan = _ref.overscan,\n      overscan = _ref$overscan === void 0 ? 1 : _ref$overscan,\n      _ref$paddingStart = _ref.paddingStart,\n      paddingStart = _ref$paddingStart === void 0 ? 0 : _ref$paddingStart,\n      _ref$paddingEnd = _ref.paddingEnd,\n      paddingEnd = _ref$paddingEnd === void 0 ? 0 : _ref$paddingEnd,\n      parentRef = _ref.parentRef,\n      horizontal = _ref.horizontal,\n      scrollToFn = _ref.scrollToFn,\n      useObserver = _ref.useObserver,\n      initialRect = _ref.initialRect,\n      onScrollElement = _ref.onScrollElement,\n      scrollOffsetFn = _ref.scrollOffsetFn,\n      _ref$keyExtractor = _ref.keyExtractor,\n      keyExtractor = _ref$keyExtractor === void 0 ? defaultKeyExtractor : _ref$keyExtractor,\n      _ref$measureSize = _ref.measureSize,\n      measureSize = _ref$measureSize === void 0 ? defaultMeasureSize : _ref$measureSize,\n      _ref$rangeExtractor = _ref.rangeExtractor,\n      rangeExtractor = _ref$rangeExtractor === void 0 ? defaultRangeExtractor : _ref$rangeExtractor;\n  var sizeKey = horizontal ? 'width' : 'height';\n  var scrollKey = horizontal ? 'scrollLeft' : 'scrollTop';\n  var latestRef = React.useRef({\n    scrollOffset: 0,\n    measurements: []\n  });\n\n  var _React$useState = React.useState(0),\n      scrollOffset = _React$useState[0],\n      setScrollOffset = _React$useState[1];\n\n  latestRef.current.scrollOffset = scrollOffset;\n  var useMeasureParent = useObserver || useRect;\n\n  var _useMeasureParent = useMeasureParent(parentRef, initialRect),\n      outerSize = _useMeasureParent[sizeKey];\n\n  latestRef.current.outerSize = outerSize;\n  var defaultScrollToFn = React.useCallback(function (offset) {\n    if (parentRef.current) {\n      parentRef.current[scrollKey] = offset;\n    }\n  }, [parentRef, scrollKey]);\n  var resolvedScrollToFn = scrollToFn || defaultScrollToFn;\n  scrollToFn = React.useCallback(function (offset) {\n    resolvedScrollToFn(offset, defaultScrollToFn);\n  }, [defaultScrollToFn, resolvedScrollToFn]);\n\n  var _React$useState2 = React.useState({}),\n      measuredCache = _React$useState2[0],\n      setMeasuredCache = _React$useState2[1];\n\n  var measure = React.useCallback(function () {\n    return setMeasuredCache({});\n  }, []);\n  var pendingMeasuredCacheIndexesRef = React.useRef([]);\n  var measurements = React.useMemo(function () {\n    var min = pendingMeasuredCacheIndexesRef.current.length > 0 ? Math.min.apply(Math, pendingMeasuredCacheIndexesRef.current) : 0;\n    pendingMeasuredCacheIndexesRef.current = [];\n    var measurements = latestRef.current.measurements.slice(0, min);\n\n    for (var i = min; i < size; i++) {\n      var key = keyExtractor(i);\n      var measuredSize = measuredCache[key];\n\n      var _start = measurements[i - 1] ? measurements[i - 1].end : paddingStart;\n\n      var _size = typeof measuredSize === 'number' ? measuredSize : estimateSize(i);\n\n      var _end = _start + _size;\n\n      measurements[i] = {\n        index: i,\n        start: _start,\n        size: _size,\n        end: _end,\n        key: key\n      };\n    }\n\n    return measurements;\n  }, [estimateSize, measuredCache, paddingStart, size, keyExtractor]);\n  var totalSize = (((_measurements = measurements[size - 1]) == null ? void 0 : _measurements.end) || paddingStart) + paddingEnd;\n  latestRef.current.measurements = measurements;\n  latestRef.current.totalSize = totalSize;\n  var element = onScrollElement ? onScrollElement.current : parentRef.current;\n  var scrollOffsetFnRef = React.useRef(scrollOffsetFn);\n  scrollOffsetFnRef.current = scrollOffsetFn;\n  useIsomorphicLayoutEffect(function () {\n    if (!element) {\n      setScrollOffset(0);\n      return;\n    }\n\n    var onScroll = function onScroll(event) {\n      var offset = scrollOffsetFnRef.current ? scrollOffsetFnRef.current(event) : element[scrollKey];\n      setScrollOffset(offset);\n    };\n\n    onScroll();\n    element.addEventListener('scroll', onScroll, {\n      capture: false,\n      passive: true\n    });\n    return function () {\n      element.removeEventListener('scroll', onScroll);\n    };\n  }, [element, scrollKey]);\n\n  var _calculateRange = calculateRange(latestRef.current),\n      start = _calculateRange.start,\n      end = _calculateRange.end;\n\n  var indexes = React.useMemo(function () {\n    return rangeExtractor({\n      start: start,\n      end: end,\n      overscan: overscan,\n      size: measurements.length\n    });\n  }, [start, end, overscan, measurements.length, rangeExtractor]);\n  var measureSizeRef = React.useRef(measureSize);\n  measureSizeRef.current = measureSize;\n  var virtualItems = React.useMemo(function () {\n    var virtualItems = [];\n\n    var _loop = function _loop(k, len) {\n      var i = indexes[k];\n      var measurement = measurements[i];\n\n      var item = _extends(_extends({}, measurement), {}, {\n        measureRef: function measureRef(el) {\n          if (el) {\n            var measuredSize = measureSizeRef.current(el, horizontal);\n\n            if (measuredSize !== item.size) {\n              var _scrollOffset = latestRef.current.scrollOffset;\n\n              if (item.start < _scrollOffset) {\n                defaultScrollToFn(_scrollOffset + (measuredSize - item.size));\n              }\n\n              pendingMeasuredCacheIndexesRef.current.push(i);\n              setMeasuredCache(function (old) {\n                var _extends2;\n\n                return _extends(_extends({}, old), {}, (_extends2 = {}, _extends2[item.key] = measuredSize, _extends2));\n              });\n            }\n          }\n        }\n      });\n\n      virtualItems.push(item);\n    };\n\n    for (var k = 0, len = indexes.length; k < len; k++) {\n      _loop(k);\n    }\n\n    return virtualItems;\n  }, [indexes, defaultScrollToFn, horizontal, measurements]);\n  var mountedRef = React.useRef(false);\n  useIsomorphicLayoutEffect(function () {\n    if (mountedRef.current) {\n      setMeasuredCache({});\n    }\n\n    mountedRef.current = true;\n  }, [estimateSize]);\n  var scrollToOffset = React.useCallback(function (toOffset, _temp) {\n    var _ref2 = _temp === void 0 ? {} : _temp,\n        _ref2$align = _ref2.align,\n        align = _ref2$align === void 0 ? 'start' : _ref2$align;\n\n    var _latestRef$current = latestRef.current,\n        scrollOffset = _latestRef$current.scrollOffset,\n        outerSize = _latestRef$current.outerSize;\n\n    if (align === 'auto') {\n      if (toOffset <= scrollOffset) {\n        align = 'start';\n      } else if (toOffset >= scrollOffset + outerSize) {\n        align = 'end';\n      } else {\n        align = 'start';\n      }\n    }\n\n    if (align === 'start') {\n      scrollToFn(toOffset);\n    } else if (align === 'end') {\n      scrollToFn(toOffset - outerSize);\n    } else if (align === 'center') {\n      scrollToFn(toOffset - outerSize / 2);\n    }\n  }, [scrollToFn]);\n  var tryScrollToIndex = React.useCallback(function (index, _temp2) {\n    var _ref3 = _temp2 === void 0 ? {} : _temp2,\n        _ref3$align = _ref3.align,\n        align = _ref3$align === void 0 ? 'auto' : _ref3$align,\n        rest = _objectWithoutPropertiesLoose(_ref3, [\"align\"]);\n\n    var _latestRef$current2 = latestRef.current,\n        measurements = _latestRef$current2.measurements,\n        scrollOffset = _latestRef$current2.scrollOffset,\n        outerSize = _latestRef$current2.outerSize;\n    var measurement = measurements[Math.max(0, Math.min(index, size - 1))];\n\n    if (!measurement) {\n      return;\n    }\n\n    if (align === 'auto') {\n      if (measurement.end >= scrollOffset + outerSize) {\n        align = 'end';\n      } else if (measurement.start <= scrollOffset) {\n        align = 'start';\n      } else {\n        return;\n      }\n    }\n\n    var toOffset = align === 'center' ? measurement.start + measurement.size / 2 : align === 'end' ? measurement.end : measurement.start;\n    scrollToOffset(toOffset, _extends({\n      align: align\n    }, rest));\n  }, [scrollToOffset, size]);\n  var scrollToIndex = React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    // We do a double request here because of\n    // dynamic sizes which can cause offset shift\n    // and end up in the wrong spot. Unfortunately,\n    // we can't know about those dynamic sizes until\n    // we try and render them. So double down!\n    tryScrollToIndex.apply(void 0, args);\n    requestAnimationFrame(function () {\n      tryScrollToIndex.apply(void 0, args);\n    });\n  }, [tryScrollToIndex]);\n  return {\n    virtualItems: virtualItems,\n    totalSize: totalSize,\n    scrollToOffset: scrollToOffset,\n    scrollToIndex: scrollToIndex,\n    measure: measure\n  };\n}\n\nvar findNearestBinarySearch = function findNearestBinarySearch(low, high, getCurrentValue, value) {\n  while (low <= high) {\n    var middle = (low + high) / 2 | 0;\n    var currentValue = getCurrentValue(middle);\n\n    if (currentValue < value) {\n      low = middle + 1;\n    } else if (currentValue > value) {\n      high = middle - 1;\n    } else {\n      return middle;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nfunction calculateRange(_ref4) {\n  var measurements = _ref4.measurements,\n      outerSize = _ref4.outerSize,\n      scrollOffset = _ref4.scrollOffset;\n  var size = measurements.length - 1;\n\n  var getOffset = function getOffset(index) {\n    return measurements[index].start;\n  };\n\n  var start = findNearestBinarySearch(0, size, getOffset, scrollOffset);\n  var end = start;\n\n  while (end < size && measurements[end].end < scrollOffset + outerSize) {\n    end++;\n  }\n\n  return {\n    start: start,\n    end: end\n  };\n}\n\nexport { defaultRangeExtractor, useVirtual };\n//# sourceMappingURL=react-virtual.mjs.map\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useRegisterActions = void 0;\nvar React = __importStar(require(\"react\"));\nvar useKBar_1 = require(\"./useKBar\");\nfunction useRegisterActions(actions, dependencies) {\n    if (dependencies === void 0) { dependencies = []; }\n    var query = (0, useKBar_1.useKBar)().query;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    var actionsCache = React.useMemo(function () { return actions; }, dependencies);\n    React.useEffect(function () {\n        if (!actionsCache.length) {\n            return;\n        }\n        var unregister = query.registerActions(actionsCache);\n        return function () {\n            unregister();\n        };\n    }, [query, actionsCache]);\n}\nexports.useRegisterActions = useRegisterActions;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarPositioner = void 0;\nvar React = __importStar(require(\"react\"));\nvar defaultStyle = {\n    position: \"fixed\",\n    display: \"flex\",\n    alignItems: \"flex-start\",\n    justifyContent: \"center\",\n    width: \"100%\",\n    inset: \"0px\",\n    padding: \"14vh 16px 16px\",\n};\nfunction getStyle(style) {\n    return style ? __assign(__assign({}, defaultStyle), style) : defaultStyle;\n}\nexports.KBarPositioner = React.forwardRef(function (_a, ref) {\n    var style = _a.style, children = _a.children, props = __rest(_a, [\"style\", \"children\"]);\n    return (React.createElement(\"div\", __assign({ ref: ref, style: getStyle(style) }, props), children));\n});\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.VisualState = void 0;\nvar VisualState;\n(function (VisualState) {\n    VisualState[\"animatingIn\"] = \"animating-in\";\n    VisualState[\"showing\"] = \"showing\";\n    VisualState[\"animatingOut\"] = \"animating-out\";\n    VisualState[\"hidden\"] = \"hidden\";\n})(VisualState = exports.VisualState || (exports.VisualState = {}));\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.InternalEvents = void 0;\nvar React = __importStar(require(\"react\"));\nvar tinykeys_1 = __importDefault(require(\"./tinykeys\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nfunction InternalEvents() {\n    useToggleHandler();\n    useDocumentLock();\n    useShortcuts();\n    useFocusHandler();\n    return null;\n}\nexports.InternalEvents = InternalEvents;\n/**\n * `useToggleHandler` handles the keyboard events for toggling kbar.\n */\nfunction useToggleHandler() {\n    var _a, _b;\n    var _c = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n        showing: state.visualState !== types_1.VisualState.hidden,\n        disabled: state.disabled,\n    }); }), query = _c.query, options = _c.options, visualState = _c.visualState, showing = _c.showing, disabled = _c.disabled;\n    React.useEffect(function () {\n        var _a;\n        var close = function () {\n            query.setVisualState(function (vs) {\n                if (vs === types_1.VisualState.hidden || vs === types_1.VisualState.animatingOut) {\n                    return vs;\n                }\n                return types_1.VisualState.animatingOut;\n            });\n        };\n        if (disabled) {\n            close();\n            return;\n        }\n        var shortcut = options.toggleShortcut || \"$mod+k\";\n        var unsubscribe = (0, tinykeys_1.default)(window, (_a = {},\n            _a[shortcut] = function (event) {\n                var _a, _b, _c, _d;\n                if (event.defaultPrevented)\n                    return;\n                event.preventDefault();\n                query.toggle();\n                if (showing) {\n                    (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n                }\n                else {\n                    (_d = (_c = options.callbacks) === null || _c === void 0 ? void 0 : _c.onOpen) === null || _d === void 0 ? void 0 : _d.call(_c);\n                }\n            },\n            _a.Escape = function (event) {\n                var _a, _b;\n                if (showing) {\n                    event.stopPropagation();\n                    event.preventDefault();\n                    (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n                }\n                close();\n            },\n            _a));\n        return function () {\n            unsubscribe();\n        };\n    }, [options.callbacks, options.toggleShortcut, query, showing, disabled]);\n    var timeoutRef = React.useRef();\n    var runAnimateTimer = React.useCallback(function (vs) {\n        var _a, _b;\n        var ms = 0;\n        if (vs === types_1.VisualState.animatingIn) {\n            ms = ((_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs) || 0;\n        }\n        if (vs === types_1.VisualState.animatingOut) {\n            ms = ((_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs) || 0;\n        }\n        clearTimeout(timeoutRef.current);\n        timeoutRef.current = setTimeout(function () {\n            var backToRoot = false;\n            // TODO: setVisualState argument should be a function or just a VisualState value.\n            query.setVisualState(function () {\n                var finalVs = vs === types_1.VisualState.animatingIn\n                    ? types_1.VisualState.showing\n                    : types_1.VisualState.hidden;\n                if (finalVs === types_1.VisualState.hidden) {\n                    backToRoot = true;\n                }\n                return finalVs;\n            });\n            if (backToRoot) {\n                query.setCurrentRootAction(null);\n            }\n        }, ms);\n    }, [(_a = options.animations) === null || _a === void 0 ? void 0 : _a.enterMs, (_b = options.animations) === null || _b === void 0 ? void 0 : _b.exitMs, query]);\n    React.useEffect(function () {\n        switch (visualState) {\n            case types_1.VisualState.animatingIn:\n            case types_1.VisualState.animatingOut:\n                runAnimateTimer(visualState);\n                break;\n        }\n    }, [runAnimateTimer, visualState]);\n}\n/**\n * `useDocumentLock` is a simple implementation for preventing the\n * underlying page content from scrolling when kbar is open.\n */\nfunction useDocumentLock() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n    }); }), visualState = _a.visualState, options = _a.options;\n    React.useEffect(function () {\n        if (options.disableDocumentLock)\n            return;\n        if (visualState === types_1.VisualState.animatingIn) {\n            document.body.style.overflow = \"hidden\";\n            if (!options.disableScrollbarManagement) {\n                var scrollbarWidth = (0, utils_1.getScrollbarWidth)();\n                // take into account the margins explicitly added by the consumer\n                var mr = getComputedStyle(document.body)[\"margin-right\"];\n                if (mr) {\n                    // remove non-numeric values; px, rem, em, etc.\n                    scrollbarWidth += Number(mr.replace(/\\D/g, \"\"));\n                }\n                document.body.style.marginRight = scrollbarWidth + \"px\";\n            }\n        }\n        else if (visualState === types_1.VisualState.hidden) {\n            document.body.style.removeProperty(\"overflow\");\n            if (!options.disableScrollbarManagement) {\n                document.body.style.removeProperty(\"margin-right\");\n            }\n        }\n    }, [\n        options.disableDocumentLock,\n        options.disableScrollbarManagement,\n        visualState,\n    ]);\n}\n/**\n * Reference: https://github.com/jamiebuilds/tinykeys/issues/37\n *\n * Fixes an issue where simultaneous key commands for shortcuts;\n * ie given two actions with shortcuts ['t','s'] and ['s'], pressing\n * 't' and 's' consecutively will cause both shortcuts to fire.\n *\n * `wrap` sets each keystroke event in a WeakSet, and ensures that\n * if ['t', 's'] are pressed, then the subsequent ['s'] event will\n * be ignored. This depends on the order in which we register the\n * shortcuts to tinykeys, which is handled below.\n */\nvar handled = new WeakSet();\nfunction wrap(handler) {\n    return function (event) {\n        if (handled.has(event))\n            return;\n        handler(event);\n        handled.add(event);\n    };\n}\n/**\n * `useShortcuts` registers and listens to keyboard strokes and\n * performs actions for patterns that match the user defined `shortcut`.\n */\nfunction useShortcuts() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        actions: state.actions,\n        open: state.visualState === types_1.VisualState.showing,\n        disabled: state.disabled,\n    }); }), actions = _a.actions, query = _a.query, open = _a.open, options = _a.options, disabled = _a.disabled;\n    React.useEffect(function () {\n        var _a;\n        if (open || disabled)\n            return;\n        var actionsList = Object.keys(actions).map(function (key) { return actions[key]; });\n        var actionsWithShortcuts = [];\n        for (var _i = 0, actionsList_1 = actionsList; _i < actionsList_1.length; _i++) {\n            var action = actionsList_1[_i];\n            if (!((_a = action.shortcut) === null || _a === void 0 ? void 0 : _a.length)) {\n                continue;\n            }\n            actionsWithShortcuts.push(action);\n        }\n        actionsWithShortcuts = actionsWithShortcuts.sort(function (a, b) { return b.shortcut.join(\" \").length - a.shortcut.join(\" \").length; });\n        var shortcutsMap = {};\n        var _loop_1 = function (action) {\n            var shortcut = action.shortcut.join(\" \");\n            shortcutsMap[shortcut] = wrap(function (event) {\n                var _a, _b, _c, _d, _e, _f;\n                if ((0, utils_1.shouldRejectKeystrokes)())\n                    return;\n                event.preventDefault();\n                if ((_a = action.children) === null || _a === void 0 ? void 0 : _a.length) {\n                    query.setCurrentRootAction(action.id);\n                    query.toggle();\n                    (_c = (_b = options.callbacks) === null || _b === void 0 ? void 0 : _b.onOpen) === null || _c === void 0 ? void 0 : _c.call(_b);\n                }\n                else {\n                    (_d = action.command) === null || _d === void 0 ? void 0 : _d.perform();\n                    (_f = (_e = options.callbacks) === null || _e === void 0 ? void 0 : _e.onSelectAction) === null || _f === void 0 ? void 0 : _f.call(_e, action);\n                }\n            });\n        };\n        for (var _b = 0, actionsWithShortcuts_1 = actionsWithShortcuts; _b < actionsWithShortcuts_1.length; _b++) {\n            var action = actionsWithShortcuts_1[_b];\n            _loop_1(action);\n        }\n        var unsubscribe = (0, tinykeys_1.default)(window, shortcutsMap, {\n            timeout: 400,\n        });\n        return function () {\n            unsubscribe();\n        };\n    }, [actions, open, options.callbacks, query, disabled]);\n}\n/**\n * `useFocusHandler` ensures that focus is set back on the element which was\n * in focus prior to kbar being triggered.\n */\nfunction useFocusHandler() {\n    var rFirstRender = React.useRef(true);\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        isShowing: state.visualState === types_1.VisualState.showing ||\n            state.visualState === types_1.VisualState.animatingIn,\n    }); }), isShowing = _a.isShowing, query = _a.query;\n    var activeElementRef = React.useRef(null);\n    React.useEffect(function () {\n        if (rFirstRender.current) {\n            rFirstRender.current = false;\n            return;\n        }\n        if (isShowing) {\n            activeElementRef.current = document.activeElement;\n            return;\n        }\n        // This fixes an issue on Safari where closing kbar causes the entire\n        // page to scroll to the bottom. The reason this was happening was due\n        // to the search input still in focus when we removed it from the dom.\n        var currentActiveElement = document.activeElement;\n        if ((currentActiveElement === null || currentActiveElement === void 0 ? void 0 : currentActiveElement.tagName.toLowerCase()) === \"input\") {\n            currentActiveElement.blur();\n        }\n        var activeElement = activeElementRef.current;\n        if (activeElement && activeElement !== currentActiveElement) {\n            activeElement.focus();\n        }\n    }, [isShowing]);\n    // When focus is blurred from the search input while kbar is still\n    // open, any keystroke should set focus back to the search input.\n    React.useEffect(function () {\n        function handler(event) {\n            var input = query.getInput();\n            if (event.target !== input) {\n                input.focus();\n            }\n        }\n        if (isShowing) {\n            window.addEventListener(\"keydown\", handler);\n            return function () {\n                window.removeEventListener(\"keydown\", handler);\n            };\n        }\n    }, [isShowing, query]);\n}\n", "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  Primitive: () => Primitive,\n  Root: () => Root,\n  dispatchDiscreteCustomEvent: () => dispatchDiscreteCustomEvent\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// src/primitive.tsx\nvar React = __toESM(require(\"react\"));\nvar ReactDOM = __toESM(require(\"react-dom\"));\nvar import_react_slot = require(\"@radix-ui/react-slot\");\nvar import_jsx_runtime = require(\"react/jsx-runtime\");\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0, import_react_slot.createSlot)(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n//# sourceMappingURL=index.js.map\n", "\"use strict\";\n// Fixes special character issues; `?` -> `shift+/` + build issue\n// https://github.com/jamiebuilds/tinykeys\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * These are the modifier keys that change the meaning of keybindings.\n *\n * Note: Ignoring \"AltGraph\" because it is covered by the others.\n */\nvar KEYBINDING_MODIFIER_KEYS = [\"Shift\", \"Meta\", \"Alt\", \"Control\"];\n/**\n * Keybinding sequences should timeout if individual key presses are more than\n * 1s apart by default.\n */\nvar DEFAULT_TIMEOUT = 1000;\n/**\n * Keybinding sequences should bind to this event by default.\n */\nvar DEFAULT_EVENT = \"keydown\";\n/**\n * An alias for creating platform-specific keybinding aliases.\n */\nvar MOD = typeof navigator === \"object\" &&\n    /Mac|iPod|iPhone|iPad/.test(navigator.platform)\n    ? \"Meta\"\n    : \"Control\";\n/**\n * There's a bug in Chrome that causes event.getModifierState not to exist on\n * KeyboardEvent's for F1/F2/etc keys.\n */\nfunction getModifierState(event, mod) {\n    return typeof event.getModifierState === \"function\"\n        ? event.getModifierState(mod)\n        : false;\n}\n/**\n * Parses a \"Key Binding String\" into its parts\n *\n * grammar    = `<sequence>`\n * <sequence> = `<press> <press> <press> ...`\n * <press>    = `<key>` or `<mods>+<key>`\n * <mods>     = `<mod>+<mod>+...`\n */\nfunction parse(str) {\n    return str\n        .trim()\n        .split(\" \")\n        .map(function (press) {\n        var mods = press.split(/\\b\\+/);\n        var key = mods.pop();\n        mods = mods.map(function (mod) { return (mod === \"$mod\" ? MOD : mod); });\n        return [mods, key];\n    });\n}\n/**\n * This tells us if a series of events matches a key binding sequence either\n * partially or exactly.\n */\nfunction match(event, press) {\n    // Special characters; `?` `!`\n    if (/^[^A-Za-z0-9]$/.test(event.key) && press[1] === event.key) {\n        return true;\n    }\n    // prettier-ignore\n    return !(\n    // Allow either the `event.key` or the `event.code`\n    // MDN event.key: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key\n    // MDN event.code: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/code\n    (press[1].toUpperCase() !== event.key.toUpperCase() &&\n        press[1] !== event.code) ||\n        // Ensure all the modifiers in the keybinding are pressed.\n        press[0].find(function (mod) {\n            return !getModifierState(event, mod);\n        }) ||\n        // KEYBINDING_MODIFIER_KEYS (Shift/Control/etc) change the meaning of a\n        // keybinding. So if they are pressed but aren't part of the current\n        // keybinding press, then we don't have a match.\n        KEYBINDING_MODIFIER_KEYS.find(function (mod) {\n            return !press[0].includes(mod) && press[1] !== mod && getModifierState(event, mod);\n        }));\n}\n/**\n * Subscribes to keybindings.\n *\n * Returns an unsubscribe method.\n *\n * @example\n * ```js\n * import keybindings from \"../src/keybindings\"\n *\n * keybindings(window, {\n * \t\"Shift+d\": () => {\n * \t\talert(\"The 'Shift' and 'd' keys were pressed at the same time\")\n * \t},\n * \t\"y e e t\": () => {\n * \t\talert(\"The keys 'y', 'e', 'e', and 't' were pressed in order\")\n * \t},\n * \t\"$mod+d\": () => {\n * \t\talert(\"Either 'Control+d' or 'Meta+d' were pressed\")\n * \t},\n * })\n * ```\n */\nfunction keybindings(target, keyBindingMap, options) {\n    var _a, _b;\n    if (options === void 0) { options = {}; }\n    var timeout = (_a = options.timeout) !== null && _a !== void 0 ? _a : DEFAULT_TIMEOUT;\n    var event = (_b = options.event) !== null && _b !== void 0 ? _b : DEFAULT_EVENT;\n    var keyBindings = Object.keys(keyBindingMap).map(function (key) {\n        return [parse(key), keyBindingMap[key]];\n    });\n    var possibleMatches = new Map();\n    var timer = null;\n    var onKeyEvent = function (event) {\n        // Ensure and stop any event that isn't a full keyboard event.\n        // Autocomplete option navigation and selection would fire a instanceof Event,\n        // instead of the expected KeyboardEvent\n        if (!(event instanceof KeyboardEvent)) {\n            return;\n        }\n        keyBindings.forEach(function (keyBinding) {\n            var sequence = keyBinding[0];\n            var callback = keyBinding[1];\n            var prev = possibleMatches.get(sequence);\n            var remainingExpectedPresses = prev ? prev : sequence;\n            var currentExpectedPress = remainingExpectedPresses[0];\n            var matches = match(event, currentExpectedPress);\n            if (!matches) {\n                // Modifier keydown events shouldn't break sequences\n                // Note: This works because:\n                // - non-modifiers will always return false\n                // - if the current keypress is a modifier then it will return true when we check its state\n                // MDN: https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/getModifierState\n                if (!getModifierState(event, event.key)) {\n                    possibleMatches.delete(sequence);\n                }\n            }\n            else if (remainingExpectedPresses.length > 1) {\n                possibleMatches.set(sequence, remainingExpectedPresses.slice(1));\n            }\n            else {\n                possibleMatches.delete(sequence);\n                callback(event);\n            }\n        });\n        if (timer) {\n            clearTimeout(timer);\n        }\n        // @ts-ignore\n        timer = setTimeout(possibleMatches.clear.bind(possibleMatches), timeout);\n    };\n    target.addEventListener(event, onKeyEvent);\n    return function () {\n        target.removeEventListener(event, onKeyEvent);\n    };\n}\nexports.default = keybindings;\n", "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// packages/react/use-layout-effect/src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  useLayoutEffect: () => useLayoutEffect2\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\nvar React = __toESM(require(\"react\"));\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\n//# sourceMappingURL=index.js.map\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useStore = void 0;\nvar fast_equals_1 = require(\"fast-equals\");\nvar React = __importStar(require(\"react\"));\nvar tiny_invariant_1 = __importDefault(require(\"tiny-invariant\"));\nvar ActionInterface_1 = require(\"./action/ActionInterface\");\nvar HistoryImpl_1 = require(\"./action/HistoryImpl\");\nvar types_1 = require(\"./types\");\nfunction useStore(props) {\n    var optionsRef = React.useRef(__assign({ animations: {\n            enterMs: 200,\n            exitMs: 100,\n        } }, props.options));\n    var actionsInterface = React.useMemo(function () {\n        return new ActionInterface_1.ActionInterface(props.actions || [], {\n            historyManager: optionsRef.current.enableHistory ? HistoryImpl_1.history : undefined,\n        });\n    }, \n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // TODO: at this point useReducer might be a better approach to managing state.\n    var _a = React.useState({\n        searchQuery: \"\",\n        currentRootActionId: null,\n        visualState: types_1.VisualState.hidden,\n        actions: __assign({}, actionsInterface.actions),\n        activeIndex: 0,\n        disabled: false,\n    }), state = _a[0], setState = _a[1];\n    var currState = React.useRef(state);\n    currState.current = state;\n    var getState = React.useCallback(function () { return currState.current; }, []);\n    var publisher = React.useMemo(function () { return new Publisher(getState); }, [getState]);\n    React.useEffect(function () {\n        currState.current = state;\n        publisher.notify();\n    }, [state, publisher]);\n    var registerActions = React.useCallback(function (actions) {\n        setState(function (state) {\n            return __assign(__assign({}, state), { actions: actionsInterface.add(actions) });\n        });\n        return function unregister() {\n            setState(function (state) {\n                return __assign(__assign({}, state), { actions: actionsInterface.remove(actions) });\n            });\n        };\n    }, [actionsInterface]);\n    var inputRef = React.useRef(null);\n    return React.useMemo(function () {\n        var query = {\n            setCurrentRootAction: function (actionId) {\n                setState(function (state) { return (__assign(__assign({}, state), { currentRootActionId: actionId })); });\n            },\n            setVisualState: function (cb) {\n                setState(function (state) { return (__assign(__assign({}, state), { visualState: typeof cb === \"function\" ? cb(state.visualState) : cb })); });\n            },\n            setSearch: function (searchQuery) {\n                return setState(function (state) { return (__assign(__assign({}, state), { searchQuery: searchQuery })); });\n            },\n            registerActions: registerActions,\n            toggle: function () {\n                return setState(function (state) { return (__assign(__assign({}, state), { visualState: [types_1.VisualState.animatingOut, types_1.VisualState.hidden].includes(state.visualState)\n                        ? types_1.VisualState.animatingIn\n                        : types_1.VisualState.animatingOut })); });\n            },\n            setActiveIndex: function (cb) {\n                return setState(function (state) { return (__assign(__assign({}, state), { activeIndex: typeof cb === \"number\" ? cb : cb(state.activeIndex) })); });\n            },\n            inputRefSetter: function (el) {\n                inputRef.current = el;\n            },\n            getInput: function () {\n                (0, tiny_invariant_1.default)(inputRef.current, \"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input.\");\n                return inputRef.current;\n            },\n            disable: function (disable) {\n                setState(function (state) { return (__assign(__assign({}, state), { disabled: disable })); });\n            },\n        };\n        return {\n            getState: getState,\n            query: query,\n            options: optionsRef.current,\n            subscribe: function (collector, cb) { return publisher.subscribe(collector, cb); },\n        };\n    }, [getState, publisher, registerActions]);\n}\nexports.useStore = useStore;\nvar Publisher = /** @class */ (function () {\n    function Publisher(getState) {\n        this.subscribers = [];\n        this.getState = getState;\n    }\n    Publisher.prototype.subscribe = function (collector, onChange) {\n        var _this = this;\n        var subscriber = new Subscriber(function () { return collector(_this.getState()); }, onChange);\n        this.subscribers.push(subscriber);\n        return this.unsubscribe.bind(this, subscriber);\n    };\n    Publisher.prototype.unsubscribe = function (subscriber) {\n        if (this.subscribers.length) {\n            var index = this.subscribers.indexOf(subscriber);\n            if (index > -1) {\n                return this.subscribers.splice(index, 1);\n            }\n        }\n    };\n    Publisher.prototype.notify = function () {\n        this.subscribers.forEach(function (subscriber) { return subscriber.collect(); });\n    };\n    return Publisher;\n}());\nvar Subscriber = /** @class */ (function () {\n    function Subscriber(collector, onChange) {\n        this.collector = collector;\n        this.onChange = onChange;\n    }\n    Subscriber.prototype.collect = function () {\n        try {\n            // grab latest state\n            var recollect = this.collector();\n            if (!(0, fast_equals_1.deepEqual)(recollect, this.collected)) {\n                this.collected = recollect;\n                if (this.onChange) {\n                    this.onChange(this.collected);\n                }\n            }\n        }\n        catch (error) {\n            console.warn(error);\n        }\n    };\n    return Subscriber;\n}());\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarPortal = void 0;\nvar react_portal_1 = require(\"@radix-ui/react-portal\");\nvar React = __importStar(require(\"react\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nfunction KBarPortal(_a) {\n    var children = _a.children, container = _a.container;\n    var showing = (0, useKBar_1.useKBar)(function (state) { return ({\n        showing: state.visualState !== types_1.VisualState.hidden,\n    }); }).showing;\n    if (!showing) {\n        return null;\n    }\n    return React.createElement(react_portal_1.Portal, { container: container }, children);\n}\nexports.KBarPortal = KBarPortal;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarSearch = exports.getListboxItemId = exports.KBAR_LISTBOX = void 0;\nvar React = __importStar(require(\"react\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nexports.KBAR_LISTBOX = \"kbar-listbox\";\nvar getListboxItemId = function (id) { return \"kbar-listbox-item-\" + id; };\nexports.getListboxItemId = getListboxItemId;\nfunction KBarSearch(props) {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        currentRootActionId: state.currentRootActionId,\n        actions: state.actions,\n        activeIndex: state.activeIndex,\n        showing: state.visualState === types_1.VisualState.showing,\n    }); }), query = _a.query, search = _a.search, actions = _a.actions, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, showing = _a.showing, options = _a.options;\n    var _b = React.useState(search), inputValue = _b[0], setInputValue = _b[1];\n    React.useEffect(function () {\n        query.setSearch(inputValue);\n    }, [inputValue, query]);\n    var defaultPlaceholder = props.defaultPlaceholder, rest = __rest(props, [\"defaultPlaceholder\"]);\n    React.useEffect(function () {\n        query.setSearch(\"\");\n        query.getInput().focus();\n        return function () { return query.setSearch(\"\"); };\n    }, [currentRootActionId, query]);\n    var placeholder = React.useMemo(function () {\n        var defaultText = defaultPlaceholder !== null && defaultPlaceholder !== void 0 ? defaultPlaceholder : \"Type a command or search…\";\n        return currentRootActionId && actions[currentRootActionId]\n            ? actions[currentRootActionId].name\n            : defaultText;\n    }, [actions, currentRootActionId, defaultPlaceholder]);\n    return (React.createElement(\"input\", __assign({}, rest, { ref: query.inputRefSetter, autoFocus: true, autoComplete: \"off\", role: \"combobox\", spellCheck: \"false\", \"aria-expanded\": showing, \"aria-controls\": exports.KBAR_LISTBOX, \"aria-activedescendant\": (0, exports.getListboxItemId)(activeIndex), value: inputValue, placeholder: placeholder, onChange: function (event) {\n            var _a, _b, _c;\n            (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, event);\n            setInputValue(event.target.value);\n            (_c = (_b = options === null || options === void 0 ? void 0 : options.callbacks) === null || _b === void 0 ? void 0 : _b.onQueryChange) === null || _c === void 0 ? void 0 : _c.call(_b, event.target.value);\n        }, onKeyDown: function (event) {\n            var _a;\n            (_a = props.onKeyDown) === null || _a === void 0 ? void 0 : _a.call(props, event);\n            if (currentRootActionId && !search && event.key === \"Backspace\") {\n                var parent_1 = actions[currentRootActionId].parent;\n                query.setCurrentRootAction(parent_1);\n            }\n        } })));\n}\nexports.KBarSearch = KBarSearch;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n__exportStar(require(\"./ActionInterface\"), exports);\n__exportStar(require(\"./ActionImpl\"), exports);\n", "'use strict';\n\nvar isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nmodule.exports = invariant;\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ActionImpl = void 0;\nvar tiny_invariant_1 = __importDefault(require(\"tiny-invariant\"));\nvar Command_1 = require(\"./Command\");\nvar utils_1 = require(\"../utils\");\n/**\n * Extends the configured keywords to include the section\n * This allows section names to be searched for.\n */\nvar extendKeywords = function (_a) {\n    var _b = _a.keywords, keywords = _b === void 0 ? \"\" : _b, _c = _a.section, section = _c === void 0 ? \"\" : _c;\n    return (keywords + \" \" + (typeof section === \"string\" ? section : section.name)).trim();\n};\nvar ActionImpl = /** @class */ (function () {\n    function ActionImpl(action, options) {\n        var _this = this;\n        var _a;\n        this.priority = utils_1.Priority.NORMAL;\n        this.ancestors = [];\n        this.children = [];\n        Object.assign(this, action);\n        this.id = action.id;\n        this.name = action.name;\n        this.keywords = extendKeywords(action);\n        var perform = action.perform;\n        this.command =\n            perform &&\n                new Command_1.Command({\n                    perform: function () { return perform(_this); },\n                }, {\n                    history: options.history,\n                });\n        // Backwards compatibility\n        this.perform = (_a = this.command) === null || _a === void 0 ? void 0 : _a.perform;\n        if (action.parent) {\n            var parentActionImpl = options.store[action.parent];\n            (0, tiny_invariant_1.default)(parentActionImpl, \"attempted to create an action whos parent: \" + action.parent + \" does not exist in the store.\");\n            parentActionImpl.addChild(this);\n        }\n    }\n    ActionImpl.prototype.addChild = function (childActionImpl) {\n        // add all ancestors for the child action\n        childActionImpl.ancestors.unshift(this);\n        var parent = this.parentActionImpl;\n        while (parent) {\n            childActionImpl.ancestors.unshift(parent);\n            parent = parent.parentActionImpl;\n        }\n        // we ensure that order of adding always goes\n        // parent -> children, so no need to recurse\n        this.children.push(childActionImpl);\n    };\n    ActionImpl.prototype.removeChild = function (actionImpl) {\n        var _this = this;\n        // recursively remove all children\n        var index = this.children.indexOf(actionImpl);\n        if (index !== -1) {\n            this.children.splice(index, 1);\n        }\n        if (actionImpl.children) {\n            actionImpl.children.forEach(function (child) {\n                _this.removeChild(child);\n            });\n        }\n    };\n    Object.defineProperty(ActionImpl.prototype, \"parentActionImpl\", {\n        // easily access parentActionImpl after creation\n        get: function () {\n            return this.ancestors[this.ancestors.length - 1];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ActionImpl.create = function (action, options) {\n        return new ActionImpl(action, options);\n    };\n    return ActionImpl;\n}());\nexports.ActionImpl = ActionImpl;\n", "/**\n * Fuse.js v6.6.2 - Lightweight fuzzy-search (http://fusejs.io)\n *\n * Copyright (c) 2022 Kiro Risk (http://kiro.me)\n * All Rights Reserved. Apache Software License 2.0\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n */\n\nfunction isArray(value) {\n  return !Array.isArray\n    ? getTag(value) === '[object Array]'\n    : Array.isArray(value)\n}\n\n// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/baseToString.js\nconst INFINITY = 1 / 0;\nfunction baseToString(value) {\n  // Exit early for strings to avoid a performance hit in some environments.\n  if (typeof value == 'string') {\n    return value\n  }\n  let result = value + '';\n  return result == '0' && 1 / value == -INFINITY ? '-0' : result\n}\n\nfunction toString(value) {\n  return value == null ? '' : baseToString(value)\n}\n\nfunction isString(value) {\n  return typeof value === 'string'\n}\n\nfunction isNumber(value) {\n  return typeof value === 'number'\n}\n\n// Adapted from: https://github.com/lodash/lodash/blob/master/isBoolean.js\nfunction isBoolean(value) {\n  return (\n    value === true ||\n    value === false ||\n    (isObjectLike(value) && getTag(value) == '[object Boolean]')\n  )\n}\n\nfunction isObject(value) {\n  return typeof value === 'object'\n}\n\n// Checks if `value` is object-like.\nfunction isObjectLike(value) {\n  return isObject(value) && value !== null\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null\n}\n\nfunction isBlank(value) {\n  return !value.trim().length\n}\n\n// Gets the `toStringTag` of `value`.\n// Adapted from: https://github.com/lodash/lodash/blob/master/.internal/getTag.js\nfunction getTag(value) {\n  return value == null\n    ? value === undefined\n      ? '[object Undefined]'\n      : '[object Null]'\n    : Object.prototype.toString.call(value)\n}\n\nconst EXTENDED_SEARCH_UNAVAILABLE = 'Extended search is not available';\n\nconst INCORRECT_INDEX_TYPE = \"Incorrect 'index' type\";\n\nconst LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY = (key) =>\n  `Invalid value for key ${key}`;\n\nconst PATTERN_LENGTH_TOO_LARGE = (max) =>\n  `Pattern length exceeds max of ${max}.`;\n\nconst MISSING_KEY_PROPERTY = (name) => `Missing ${name} property in key`;\n\nconst INVALID_KEY_WEIGHT_VALUE = (key) =>\n  `Property 'weight' in key '${key}' must be a positive integer`;\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nclass KeyStore {\n  constructor(keys) {\n    this._keys = [];\n    this._keyMap = {};\n\n    let totalWeight = 0;\n\n    keys.forEach((key) => {\n      let obj = createKey(key);\n\n      totalWeight += obj.weight;\n\n      this._keys.push(obj);\n      this._keyMap[obj.id] = obj;\n\n      totalWeight += obj.weight;\n    });\n\n    // Normalize weights so that their sum is equal to 1\n    this._keys.forEach((key) => {\n      key.weight /= totalWeight;\n    });\n  }\n  get(keyId) {\n    return this._keyMap[keyId]\n  }\n  keys() {\n    return this._keys\n  }\n  toJSON() {\n    return JSON.stringify(this._keys)\n  }\n}\n\nfunction createKey(key) {\n  let path = null;\n  let id = null;\n  let src = null;\n  let weight = 1;\n  let getFn = null;\n\n  if (isString(key) || isArray(key)) {\n    src = key;\n    path = createKeyPath(key);\n    id = createKeyId(key);\n  } else {\n    if (!hasOwn.call(key, 'name')) {\n      throw new Error(MISSING_KEY_PROPERTY('name'))\n    }\n\n    const name = key.name;\n    src = name;\n\n    if (hasOwn.call(key, 'weight')) {\n      weight = key.weight;\n\n      if (weight <= 0) {\n        throw new Error(INVALID_KEY_WEIGHT_VALUE(name))\n      }\n    }\n\n    path = createKeyPath(name);\n    id = createKeyId(name);\n    getFn = key.getFn;\n  }\n\n  return { path, id, weight, src, getFn }\n}\n\nfunction createKeyPath(key) {\n  return isArray(key) ? key : key.split('.')\n}\n\nfunction createKeyId(key) {\n  return isArray(key) ? key.join('.') : key\n}\n\nfunction get(obj, path) {\n  let list = [];\n  let arr = false;\n\n  const deepGet = (obj, path, index) => {\n    if (!isDefined(obj)) {\n      return\n    }\n    if (!path[index]) {\n      // If there's no path left, we've arrived at the object we care about.\n      list.push(obj);\n    } else {\n      let key = path[index];\n\n      const value = obj[key];\n\n      if (!isDefined(value)) {\n        return\n      }\n\n      // If we're at the last value in the path, and if it's a string/number/bool,\n      // add it to the list\n      if (\n        index === path.length - 1 &&\n        (isString(value) || isNumber(value) || isBoolean(value))\n      ) {\n        list.push(toString(value));\n      } else if (isArray(value)) {\n        arr = true;\n        // Search each item in the array.\n        for (let i = 0, len = value.length; i < len; i += 1) {\n          deepGet(value[i], path, index + 1);\n        }\n      } else if (path.length) {\n        // An object. Recurse further.\n        deepGet(value, path, index + 1);\n      }\n    }\n  };\n\n  // Backwards compatibility (since path used to be a string)\n  deepGet(obj, isString(path) ? path.split('.') : path, 0);\n\n  return arr ? list : list[0]\n}\n\nconst MatchOptions = {\n  // Whether the matches should be included in the result set. When `true`, each record in the result\n  // set will include the indices of the matched characters.\n  // These can consequently be used for highlighting purposes.\n  includeMatches: false,\n  // When `true`, the matching function will continue to the end of a search pattern even if\n  // a perfect match has already been located in the string.\n  findAllMatches: false,\n  // Minimum number of characters that must be matched before a result is considered a match\n  minMatchCharLength: 1\n};\n\nconst BasicOptions = {\n  // When `true`, the algorithm continues searching to the end of the input even if a perfect\n  // match is found before the end of the same input.\n  isCaseSensitive: false,\n  // When true, the matching function will continue to the end of a search pattern even if\n  includeScore: false,\n  // List of properties that will be searched. This also supports nested properties.\n  keys: [],\n  // Whether to sort the result list, by score\n  shouldSort: true,\n  // Default sort function: sort by ascending score, ascending index\n  sortFn: (a, b) =>\n    a.score === b.score ? (a.idx < b.idx ? -1 : 1) : a.score < b.score ? -1 : 1\n};\n\nconst FuzzyOptions = {\n  // Approximately where in the text is the pattern expected to be found?\n  location: 0,\n  // At what point does the match algorithm give up. A threshold of '0.0' requires a perfect match\n  // (of both letters and location), a threshold of '1.0' would match anything.\n  threshold: 0.6,\n  // Determines how close the match must be to the fuzzy location (specified above).\n  // An exact letter match which is 'distance' characters away from the fuzzy location\n  // would score as a complete mismatch. A distance of '0' requires the match be at\n  // the exact location specified, a threshold of '1000' would require a perfect match\n  // to be within 800 characters of the fuzzy location to be found using a 0.8 threshold.\n  distance: 100\n};\n\nconst AdvancedOptions = {\n  // When `true`, it enables the use of unix-like search commands\n  useExtendedSearch: false,\n  // The get function to use when fetching an object's properties.\n  // The default will search nested paths *ie foo.bar.baz*\n  getFn: get,\n  // When `true`, search will ignore `location` and `distance`, so it won't matter\n  // where in the string the pattern appears.\n  // More info: https://fusejs.io/concepts/scoring-theory.html#fuzziness-score\n  ignoreLocation: false,\n  // When `true`, the calculation for the relevance score (used for sorting) will\n  // ignore the field-length norm.\n  // More info: https://fusejs.io/concepts/scoring-theory.html#field-length-norm\n  ignoreFieldNorm: false,\n  // The weight to determine how much field length norm effects scoring.\n  fieldNormWeight: 1\n};\n\nvar Config = {\n  ...BasicOptions,\n  ...MatchOptions,\n  ...FuzzyOptions,\n  ...AdvancedOptions\n};\n\nconst SPACE = /[^ ]+/g;\n\n// Field-length norm: the shorter the field, the higher the weight.\n// Set to 3 decimals to reduce index size.\nfunction norm(weight = 1, mantissa = 3) {\n  const cache = new Map();\n  const m = Math.pow(10, mantissa);\n\n  return {\n    get(value) {\n      const numTokens = value.match(SPACE).length;\n\n      if (cache.has(numTokens)) {\n        return cache.get(numTokens)\n      }\n\n      // Default function is 1/sqrt(x), weight makes that variable\n      const norm = 1 / Math.pow(numTokens, 0.5 * weight);\n\n      // In place of `toFixed(mantissa)`, for faster computation\n      const n = parseFloat(Math.round(norm * m) / m);\n\n      cache.set(numTokens, n);\n\n      return n\n    },\n    clear() {\n      cache.clear();\n    }\n  }\n}\n\nclass FuseIndex {\n  constructor({\n    getFn = Config.getFn,\n    fieldNormWeight = Config.fieldNormWeight\n  } = {}) {\n    this.norm = norm(fieldNormWeight, 3);\n    this.getFn = getFn;\n    this.isCreated = false;\n\n    this.setIndexRecords();\n  }\n  setSources(docs = []) {\n    this.docs = docs;\n  }\n  setIndexRecords(records = []) {\n    this.records = records;\n  }\n  setKeys(keys = []) {\n    this.keys = keys;\n    this._keysMap = {};\n    keys.forEach((key, idx) => {\n      this._keysMap[key.id] = idx;\n    });\n  }\n  create() {\n    if (this.isCreated || !this.docs.length) {\n      return\n    }\n\n    this.isCreated = true;\n\n    // List is Array<String>\n    if (isString(this.docs[0])) {\n      this.docs.forEach((doc, docIndex) => {\n        this._addString(doc, docIndex);\n      });\n    } else {\n      // List is Array<Object>\n      this.docs.forEach((doc, docIndex) => {\n        this._addObject(doc, docIndex);\n      });\n    }\n\n    this.norm.clear();\n  }\n  // Adds a doc to the end of the index\n  add(doc) {\n    const idx = this.size();\n\n    if (isString(doc)) {\n      this._addString(doc, idx);\n    } else {\n      this._addObject(doc, idx);\n    }\n  }\n  // Removes the doc at the specified index of the index\n  removeAt(idx) {\n    this.records.splice(idx, 1);\n\n    // Change ref index of every subsquent doc\n    for (let i = idx, len = this.size(); i < len; i += 1) {\n      this.records[i].i -= 1;\n    }\n  }\n  getValueForItemAtKeyId(item, keyId) {\n    return item[this._keysMap[keyId]]\n  }\n  size() {\n    return this.records.length\n  }\n  _addString(doc, docIndex) {\n    if (!isDefined(doc) || isBlank(doc)) {\n      return\n    }\n\n    let record = {\n      v: doc,\n      i: docIndex,\n      n: this.norm.get(doc)\n    };\n\n    this.records.push(record);\n  }\n  _addObject(doc, docIndex) {\n    let record = { i: docIndex, $: {} };\n\n    // Iterate over every key (i.e, path), and fetch the value at that key\n    this.keys.forEach((key, keyIndex) => {\n      let value = key.getFn ? key.getFn(doc) : this.getFn(doc, key.path);\n\n      if (!isDefined(value)) {\n        return\n      }\n\n      if (isArray(value)) {\n        let subRecords = [];\n        const stack = [{ nestedArrIndex: -1, value }];\n\n        while (stack.length) {\n          const { nestedArrIndex, value } = stack.pop();\n\n          if (!isDefined(value)) {\n            continue\n          }\n\n          if (isString(value) && !isBlank(value)) {\n            let subRecord = {\n              v: value,\n              i: nestedArrIndex,\n              n: this.norm.get(value)\n            };\n\n            subRecords.push(subRecord);\n          } else if (isArray(value)) {\n            value.forEach((item, k) => {\n              stack.push({\n                nestedArrIndex: k,\n                value: item\n              });\n            });\n          } else ;\n        }\n        record.$[keyIndex] = subRecords;\n      } else if (isString(value) && !isBlank(value)) {\n        let subRecord = {\n          v: value,\n          n: this.norm.get(value)\n        };\n\n        record.$[keyIndex] = subRecord;\n      }\n    });\n\n    this.records.push(record);\n  }\n  toJSON() {\n    return {\n      keys: this.keys,\n      records: this.records\n    }\n  }\n}\n\nfunction createIndex(\n  keys,\n  docs,\n  { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}\n) {\n  const myIndex = new FuseIndex({ getFn, fieldNormWeight });\n  myIndex.setKeys(keys.map(createKey));\n  myIndex.setSources(docs);\n  myIndex.create();\n  return myIndex\n}\n\nfunction parseIndex(\n  data,\n  { getFn = Config.getFn, fieldNormWeight = Config.fieldNormWeight } = {}\n) {\n  const { keys, records } = data;\n  const myIndex = new FuseIndex({ getFn, fieldNormWeight });\n  myIndex.setKeys(keys);\n  myIndex.setIndexRecords(records);\n  return myIndex\n}\n\nfunction computeScore$1(\n  pattern,\n  {\n    errors = 0,\n    currentLocation = 0,\n    expectedLocation = 0,\n    distance = Config.distance,\n    ignoreLocation = Config.ignoreLocation\n  } = {}\n) {\n  const accuracy = errors / pattern.length;\n\n  if (ignoreLocation) {\n    return accuracy\n  }\n\n  const proximity = Math.abs(expectedLocation - currentLocation);\n\n  if (!distance) {\n    // Dodge divide by zero error.\n    return proximity ? 1.0 : accuracy\n  }\n\n  return accuracy + proximity / distance\n}\n\nfunction convertMaskToIndices(\n  matchmask = [],\n  minMatchCharLength = Config.minMatchCharLength\n) {\n  let indices = [];\n  let start = -1;\n  let end = -1;\n  let i = 0;\n\n  for (let len = matchmask.length; i < len; i += 1) {\n    let match = matchmask[i];\n    if (match && start === -1) {\n      start = i;\n    } else if (!match && start !== -1) {\n      end = i - 1;\n      if (end - start + 1 >= minMatchCharLength) {\n        indices.push([start, end]);\n      }\n      start = -1;\n    }\n  }\n\n  // (i-1 - start) + 1 => i - start\n  if (matchmask[i - 1] && i - start >= minMatchCharLength) {\n    indices.push([start, i - 1]);\n  }\n\n  return indices\n}\n\n// Machine word size\nconst MAX_BITS = 32;\n\nfunction search(\n  text,\n  pattern,\n  patternAlphabet,\n  {\n    location = Config.location,\n    distance = Config.distance,\n    threshold = Config.threshold,\n    findAllMatches = Config.findAllMatches,\n    minMatchCharLength = Config.minMatchCharLength,\n    includeMatches = Config.includeMatches,\n    ignoreLocation = Config.ignoreLocation\n  } = {}\n) {\n  if (pattern.length > MAX_BITS) {\n    throw new Error(PATTERN_LENGTH_TOO_LARGE(MAX_BITS))\n  }\n\n  const patternLen = pattern.length;\n  // Set starting location at beginning text and initialize the alphabet.\n  const textLen = text.length;\n  // Handle the case when location > text.length\n  const expectedLocation = Math.max(0, Math.min(location, textLen));\n  // Highest score beyond which we give up.\n  let currentThreshold = threshold;\n  // Is there a nearby exact match? (speedup)\n  let bestLocation = expectedLocation;\n\n  // Performance: only computer matches when the minMatchCharLength > 1\n  // OR if `includeMatches` is true.\n  const computeMatches = minMatchCharLength > 1 || includeMatches;\n  // A mask of the matches, used for building the indices\n  const matchMask = computeMatches ? Array(textLen) : [];\n\n  let index;\n\n  // Get all exact matches, here for speed up\n  while ((index = text.indexOf(pattern, bestLocation)) > -1) {\n    let score = computeScore$1(pattern, {\n      currentLocation: index,\n      expectedLocation,\n      distance,\n      ignoreLocation\n    });\n\n    currentThreshold = Math.min(score, currentThreshold);\n    bestLocation = index + patternLen;\n\n    if (computeMatches) {\n      let i = 0;\n      while (i < patternLen) {\n        matchMask[index + i] = 1;\n        i += 1;\n      }\n    }\n  }\n\n  // Reset the best location\n  bestLocation = -1;\n\n  let lastBitArr = [];\n  let finalScore = 1;\n  let binMax = patternLen + textLen;\n\n  const mask = 1 << (patternLen - 1);\n\n  for (let i = 0; i < patternLen; i += 1) {\n    // Scan for the best match; each iteration allows for one more error.\n    // Run a binary search to determine how far from the match location we can stray\n    // at this error level.\n    let binMin = 0;\n    let binMid = binMax;\n\n    while (binMin < binMid) {\n      const score = computeScore$1(pattern, {\n        errors: i,\n        currentLocation: expectedLocation + binMid,\n        expectedLocation,\n        distance,\n        ignoreLocation\n      });\n\n      if (score <= currentThreshold) {\n        binMin = binMid;\n      } else {\n        binMax = binMid;\n      }\n\n      binMid = Math.floor((binMax - binMin) / 2 + binMin);\n    }\n\n    // Use the result from this iteration as the maximum for the next.\n    binMax = binMid;\n\n    let start = Math.max(1, expectedLocation - binMid + 1);\n    let finish = findAllMatches\n      ? textLen\n      : Math.min(expectedLocation + binMid, textLen) + patternLen;\n\n    // Initialize the bit array\n    let bitArr = Array(finish + 2);\n\n    bitArr[finish + 1] = (1 << i) - 1;\n\n    for (let j = finish; j >= start; j -= 1) {\n      let currentLocation = j - 1;\n      let charMatch = patternAlphabet[text.charAt(currentLocation)];\n\n      if (computeMatches) {\n        // Speed up: quick bool to int conversion (i.e, `charMatch ? 1 : 0`)\n        matchMask[currentLocation] = +!!charMatch;\n      }\n\n      // First pass: exact match\n      bitArr[j] = ((bitArr[j + 1] << 1) | 1) & charMatch;\n\n      // Subsequent passes: fuzzy match\n      if (i) {\n        bitArr[j] |=\n          ((lastBitArr[j + 1] | lastBitArr[j]) << 1) | 1 | lastBitArr[j + 1];\n      }\n\n      if (bitArr[j] & mask) {\n        finalScore = computeScore$1(pattern, {\n          errors: i,\n          currentLocation,\n          expectedLocation,\n          distance,\n          ignoreLocation\n        });\n\n        // This match will almost certainly be better than any existing match.\n        // But check anyway.\n        if (finalScore <= currentThreshold) {\n          // Indeed it is\n          currentThreshold = finalScore;\n          bestLocation = currentLocation;\n\n          // Already passed `loc`, downhill from here on in.\n          if (bestLocation <= expectedLocation) {\n            break\n          }\n\n          // When passing `bestLocation`, don't exceed our current distance from `expectedLocation`.\n          start = Math.max(1, 2 * expectedLocation - bestLocation);\n        }\n      }\n    }\n\n    // No hope for a (better) match at greater error levels.\n    const score = computeScore$1(pattern, {\n      errors: i + 1,\n      currentLocation: expectedLocation,\n      expectedLocation,\n      distance,\n      ignoreLocation\n    });\n\n    if (score > currentThreshold) {\n      break\n    }\n\n    lastBitArr = bitArr;\n  }\n\n  const result = {\n    isMatch: bestLocation >= 0,\n    // Count exact matches (those with a score of 0) to be \"almost\" exact\n    score: Math.max(0.001, finalScore)\n  };\n\n  if (computeMatches) {\n    const indices = convertMaskToIndices(matchMask, minMatchCharLength);\n    if (!indices.length) {\n      result.isMatch = false;\n    } else if (includeMatches) {\n      result.indices = indices;\n    }\n  }\n\n  return result\n}\n\nfunction createPatternAlphabet(pattern) {\n  let mask = {};\n\n  for (let i = 0, len = pattern.length; i < len; i += 1) {\n    const char = pattern.charAt(i);\n    mask[char] = (mask[char] || 0) | (1 << (len - i - 1));\n  }\n\n  return mask\n}\n\nclass BitapSearch {\n  constructor(\n    pattern,\n    {\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance,\n      includeMatches = Config.includeMatches,\n      findAllMatches = Config.findAllMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      isCaseSensitive = Config.isCaseSensitive,\n      ignoreLocation = Config.ignoreLocation\n    } = {}\n  ) {\n    this.options = {\n      location,\n      threshold,\n      distance,\n      includeMatches,\n      findAllMatches,\n      minMatchCharLength,\n      isCaseSensitive,\n      ignoreLocation\n    };\n\n    this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();\n\n    this.chunks = [];\n\n    if (!this.pattern.length) {\n      return\n    }\n\n    const addChunk = (pattern, startIndex) => {\n      this.chunks.push({\n        pattern,\n        alphabet: createPatternAlphabet(pattern),\n        startIndex\n      });\n    };\n\n    const len = this.pattern.length;\n\n    if (len > MAX_BITS) {\n      let i = 0;\n      const remainder = len % MAX_BITS;\n      const end = len - remainder;\n\n      while (i < end) {\n        addChunk(this.pattern.substr(i, MAX_BITS), i);\n        i += MAX_BITS;\n      }\n\n      if (remainder) {\n        const startIndex = len - MAX_BITS;\n        addChunk(this.pattern.substr(startIndex), startIndex);\n      }\n    } else {\n      addChunk(this.pattern, 0);\n    }\n  }\n\n  searchIn(text) {\n    const { isCaseSensitive, includeMatches } = this.options;\n\n    if (!isCaseSensitive) {\n      text = text.toLowerCase();\n    }\n\n    // Exact match\n    if (this.pattern === text) {\n      let result = {\n        isMatch: true,\n        score: 0\n      };\n\n      if (includeMatches) {\n        result.indices = [[0, text.length - 1]];\n      }\n\n      return result\n    }\n\n    // Otherwise, use Bitap algorithm\n    const {\n      location,\n      distance,\n      threshold,\n      findAllMatches,\n      minMatchCharLength,\n      ignoreLocation\n    } = this.options;\n\n    let allIndices = [];\n    let totalScore = 0;\n    let hasMatches = false;\n\n    this.chunks.forEach(({ pattern, alphabet, startIndex }) => {\n      const { isMatch, score, indices } = search(text, pattern, alphabet, {\n        location: location + startIndex,\n        distance,\n        threshold,\n        findAllMatches,\n        minMatchCharLength,\n        includeMatches,\n        ignoreLocation\n      });\n\n      if (isMatch) {\n        hasMatches = true;\n      }\n\n      totalScore += score;\n\n      if (isMatch && indices) {\n        allIndices = [...allIndices, ...indices];\n      }\n    });\n\n    let result = {\n      isMatch: hasMatches,\n      score: hasMatches ? totalScore / this.chunks.length : 1\n    };\n\n    if (hasMatches && includeMatches) {\n      result.indices = allIndices;\n    }\n\n    return result\n  }\n}\n\nclass BaseMatch {\n  constructor(pattern) {\n    this.pattern = pattern;\n  }\n  static isMultiMatch(pattern) {\n    return getMatch(pattern, this.multiRegex)\n  }\n  static isSingleMatch(pattern) {\n    return getMatch(pattern, this.singleRegex)\n  }\n  search(/*text*/) {}\n}\n\nfunction getMatch(pattern, exp) {\n  const matches = pattern.match(exp);\n  return matches ? matches[1] : null\n}\n\n// Token: 'file\n\nclass ExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'exact'\n  }\n  static get multiRegex() {\n    return /^=\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^=(.*)$/\n  }\n  search(text) {\n    const isMatch = text === this.pattern;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, this.pattern.length - 1]\n    }\n  }\n}\n\n// Token: !fire\n\nclass InverseExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-exact'\n  }\n  static get multiRegex() {\n    return /^!\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^!(.*)$/\n  }\n  search(text) {\n    const index = text.indexOf(this.pattern);\n    const isMatch = index === -1;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\n// Token: ^file\n\nclass PrefixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'prefix-exact'\n  }\n  static get multiRegex() {\n    return /^\\^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^\\^(.*)$/\n  }\n  search(text) {\n    const isMatch = text.startsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, this.pattern.length - 1]\n    }\n  }\n}\n\n// Token: !^fire\n\nclass InversePrefixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-prefix-exact'\n  }\n  static get multiRegex() {\n    return /^!\\^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^!\\^(.*)$/\n  }\n  search(text) {\n    const isMatch = !text.startsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\n// Token: .file$\n\nclass SuffixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'suffix-exact'\n  }\n  static get multiRegex() {\n    return /^\"(.*)\"\\$$/\n  }\n  static get singleRegex() {\n    return /^(.*)\\$$/\n  }\n  search(text) {\n    const isMatch = text.endsWith(this.pattern);\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [text.length - this.pattern.length, text.length - 1]\n    }\n  }\n}\n\n// Token: !.file$\n\nclass InverseSuffixExactMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'inverse-suffix-exact'\n  }\n  static get multiRegex() {\n    return /^!\"(.*)\"\\$$/\n  }\n  static get singleRegex() {\n    return /^!(.*)\\$$/\n  }\n  search(text) {\n    const isMatch = !text.endsWith(this.pattern);\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices: [0, text.length - 1]\n    }\n  }\n}\n\nclass FuzzyMatch extends BaseMatch {\n  constructor(\n    pattern,\n    {\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance,\n      includeMatches = Config.includeMatches,\n      findAllMatches = Config.findAllMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      isCaseSensitive = Config.isCaseSensitive,\n      ignoreLocation = Config.ignoreLocation\n    } = {}\n  ) {\n    super(pattern);\n    this._bitapSearch = new BitapSearch(pattern, {\n      location,\n      threshold,\n      distance,\n      includeMatches,\n      findAllMatches,\n      minMatchCharLength,\n      isCaseSensitive,\n      ignoreLocation\n    });\n  }\n  static get type() {\n    return 'fuzzy'\n  }\n  static get multiRegex() {\n    return /^\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^(.*)$/\n  }\n  search(text) {\n    return this._bitapSearch.searchIn(text)\n  }\n}\n\n// Token: 'file\n\nclass IncludeMatch extends BaseMatch {\n  constructor(pattern) {\n    super(pattern);\n  }\n  static get type() {\n    return 'include'\n  }\n  static get multiRegex() {\n    return /^'\"(.*)\"$/\n  }\n  static get singleRegex() {\n    return /^'(.*)$/\n  }\n  search(text) {\n    let location = 0;\n    let index;\n\n    const indices = [];\n    const patternLen = this.pattern.length;\n\n    // Get all exact matches\n    while ((index = text.indexOf(this.pattern, location)) > -1) {\n      location = index + patternLen;\n      indices.push([index, location - 1]);\n    }\n\n    const isMatch = !!indices.length;\n\n    return {\n      isMatch,\n      score: isMatch ? 0 : 1,\n      indices\n    }\n  }\n}\n\n// ❗Order is important. DO NOT CHANGE.\nconst searchers = [\n  ExactMatch,\n  IncludeMatch,\n  PrefixExactMatch,\n  InversePrefixExactMatch,\n  InverseSuffixExactMatch,\n  SuffixExactMatch,\n  InverseExactMatch,\n  FuzzyMatch\n];\n\nconst searchersLen = searchers.length;\n\n// Regex to split by spaces, but keep anything in quotes together\nconst SPACE_RE = / +(?=(?:[^\\\"]*\\\"[^\\\"]*\\\")*[^\\\"]*$)/;\nconst OR_TOKEN = '|';\n\n// Return a 2D array representation of the query, for simpler parsing.\n// Example:\n// \"^core go$ | rb$ | py$ xy$\" => [[\"^core\", \"go$\"], [\"rb$\"], [\"py$\", \"xy$\"]]\nfunction parseQuery(pattern, options = {}) {\n  return pattern.split(OR_TOKEN).map((item) => {\n    let query = item\n      .trim()\n      .split(SPACE_RE)\n      .filter((item) => item && !!item.trim());\n\n    let results = [];\n    for (let i = 0, len = query.length; i < len; i += 1) {\n      const queryItem = query[i];\n\n      // 1. Handle multiple query match (i.e, once that are quoted, like `\"hello world\"`)\n      let found = false;\n      let idx = -1;\n      while (!found && ++idx < searchersLen) {\n        const searcher = searchers[idx];\n        let token = searcher.isMultiMatch(queryItem);\n        if (token) {\n          results.push(new searcher(token, options));\n          found = true;\n        }\n      }\n\n      if (found) {\n        continue\n      }\n\n      // 2. Handle single query matches (i.e, once that are *not* quoted)\n      idx = -1;\n      while (++idx < searchersLen) {\n        const searcher = searchers[idx];\n        let token = searcher.isSingleMatch(queryItem);\n        if (token) {\n          results.push(new searcher(token, options));\n          break\n        }\n      }\n    }\n\n    return results\n  })\n}\n\n// These extended matchers can return an array of matches, as opposed\n// to a singl match\nconst MultiMatchSet = new Set([FuzzyMatch.type, IncludeMatch.type]);\n\n/**\n * Command-like searching\n * ======================\n *\n * Given multiple search terms delimited by spaces.e.g. `^jscript .python$ ruby !java`,\n * search in a given text.\n *\n * Search syntax:\n *\n * | Token       | Match type                 | Description                            |\n * | ----------- | -------------------------- | -------------------------------------- |\n * | `jscript`   | fuzzy-match                | Items that fuzzy match `jscript`       |\n * | `=scheme`   | exact-match                | Items that are `scheme`                |\n * | `'python`   | include-match              | Items that include `python`            |\n * | `!ruby`     | inverse-exact-match        | Items that do not include `ruby`       |\n * | `^java`     | prefix-exact-match         | Items that start with `java`           |\n * | `!^earlang` | inverse-prefix-exact-match | Items that do not start with `earlang` |\n * | `.js$`      | suffix-exact-match         | Items that end with `.js`              |\n * | `!.go$`     | inverse-suffix-exact-match | Items that do not end with `.go`       |\n *\n * A single pipe character acts as an OR operator. For example, the following\n * query matches entries that start with `core` and end with either`go`, `rb`,\n * or`py`.\n *\n * ```\n * ^core go$ | rb$ | py$\n * ```\n */\nclass ExtendedSearch {\n  constructor(\n    pattern,\n    {\n      isCaseSensitive = Config.isCaseSensitive,\n      includeMatches = Config.includeMatches,\n      minMatchCharLength = Config.minMatchCharLength,\n      ignoreLocation = Config.ignoreLocation,\n      findAllMatches = Config.findAllMatches,\n      location = Config.location,\n      threshold = Config.threshold,\n      distance = Config.distance\n    } = {}\n  ) {\n    this.query = null;\n    this.options = {\n      isCaseSensitive,\n      includeMatches,\n      minMatchCharLength,\n      findAllMatches,\n      ignoreLocation,\n      location,\n      threshold,\n      distance\n    };\n\n    this.pattern = isCaseSensitive ? pattern : pattern.toLowerCase();\n    this.query = parseQuery(this.pattern, this.options);\n  }\n\n  static condition(_, options) {\n    return options.useExtendedSearch\n  }\n\n  searchIn(text) {\n    const query = this.query;\n\n    if (!query) {\n      return {\n        isMatch: false,\n        score: 1\n      }\n    }\n\n    const { includeMatches, isCaseSensitive } = this.options;\n\n    text = isCaseSensitive ? text : text.toLowerCase();\n\n    let numMatches = 0;\n    let allIndices = [];\n    let totalScore = 0;\n\n    // ORs\n    for (let i = 0, qLen = query.length; i < qLen; i += 1) {\n      const searchers = query[i];\n\n      // Reset indices\n      allIndices.length = 0;\n      numMatches = 0;\n\n      // ANDs\n      for (let j = 0, pLen = searchers.length; j < pLen; j += 1) {\n        const searcher = searchers[j];\n        const { isMatch, indices, score } = searcher.search(text);\n\n        if (isMatch) {\n          numMatches += 1;\n          totalScore += score;\n          if (includeMatches) {\n            const type = searcher.constructor.type;\n            if (MultiMatchSet.has(type)) {\n              allIndices = [...allIndices, ...indices];\n            } else {\n              allIndices.push(indices);\n            }\n          }\n        } else {\n          totalScore = 0;\n          numMatches = 0;\n          allIndices.length = 0;\n          break\n        }\n      }\n\n      // OR condition, so if TRUE, return\n      if (numMatches) {\n        let result = {\n          isMatch: true,\n          score: totalScore / numMatches\n        };\n\n        if (includeMatches) {\n          result.indices = allIndices;\n        }\n\n        return result\n      }\n    }\n\n    // Nothing was matched\n    return {\n      isMatch: false,\n      score: 1\n    }\n  }\n}\n\nconst registeredSearchers = [];\n\nfunction register(...args) {\n  registeredSearchers.push(...args);\n}\n\nfunction createSearcher(pattern, options) {\n  for (let i = 0, len = registeredSearchers.length; i < len; i += 1) {\n    let searcherClass = registeredSearchers[i];\n    if (searcherClass.condition(pattern, options)) {\n      return new searcherClass(pattern, options)\n    }\n  }\n\n  return new BitapSearch(pattern, options)\n}\n\nconst LogicalOperator = {\n  AND: '$and',\n  OR: '$or'\n};\n\nconst KeyType = {\n  PATH: '$path',\n  PATTERN: '$val'\n};\n\nconst isExpression = (query) =>\n  !!(query[LogicalOperator.AND] || query[LogicalOperator.OR]);\n\nconst isPath = (query) => !!query[KeyType.PATH];\n\nconst isLeaf = (query) =>\n  !isArray(query) && isObject(query) && !isExpression(query);\n\nconst convertToExplicit = (query) => ({\n  [LogicalOperator.AND]: Object.keys(query).map((key) => ({\n    [key]: query[key]\n  }))\n});\n\n// When `auto` is `true`, the parse function will infer and initialize and add\n// the appropriate `Searcher` instance\nfunction parse(query, options, { auto = true } = {}) {\n  const next = (query) => {\n    let keys = Object.keys(query);\n\n    const isQueryPath = isPath(query);\n\n    if (!isQueryPath && keys.length > 1 && !isExpression(query)) {\n      return next(convertToExplicit(query))\n    }\n\n    if (isLeaf(query)) {\n      const key = isQueryPath ? query[KeyType.PATH] : keys[0];\n\n      const pattern = isQueryPath ? query[KeyType.PATTERN] : query[key];\n\n      if (!isString(pattern)) {\n        throw new Error(LOGICAL_SEARCH_INVALID_QUERY_FOR_KEY(key))\n      }\n\n      const obj = {\n        keyId: createKeyId(key),\n        pattern\n      };\n\n      if (auto) {\n        obj.searcher = createSearcher(pattern, options);\n      }\n\n      return obj\n    }\n\n    let node = {\n      children: [],\n      operator: keys[0]\n    };\n\n    keys.forEach((key) => {\n      const value = query[key];\n\n      if (isArray(value)) {\n        value.forEach((item) => {\n          node.children.push(next(item));\n        });\n      }\n    });\n\n    return node\n  };\n\n  if (!isExpression(query)) {\n    query = convertToExplicit(query);\n  }\n\n  return next(query)\n}\n\n// Practical scoring function\nfunction computeScore(\n  results,\n  { ignoreFieldNorm = Config.ignoreFieldNorm }\n) {\n  results.forEach((result) => {\n    let totalScore = 1;\n\n    result.matches.forEach(({ key, norm, score }) => {\n      const weight = key ? key.weight : null;\n\n      totalScore *= Math.pow(\n        score === 0 && weight ? Number.EPSILON : score,\n        (weight || 1) * (ignoreFieldNorm ? 1 : norm)\n      );\n    });\n\n    result.score = totalScore;\n  });\n}\n\nfunction transformMatches(result, data) {\n  const matches = result.matches;\n  data.matches = [];\n\n  if (!isDefined(matches)) {\n    return\n  }\n\n  matches.forEach((match) => {\n    if (!isDefined(match.indices) || !match.indices.length) {\n      return\n    }\n\n    const { indices, value } = match;\n\n    let obj = {\n      indices,\n      value\n    };\n\n    if (match.key) {\n      obj.key = match.key.src;\n    }\n\n    if (match.idx > -1) {\n      obj.refIndex = match.idx;\n    }\n\n    data.matches.push(obj);\n  });\n}\n\nfunction transformScore(result, data) {\n  data.score = result.score;\n}\n\nfunction format(\n  results,\n  docs,\n  {\n    includeMatches = Config.includeMatches,\n    includeScore = Config.includeScore\n  } = {}\n) {\n  const transformers = [];\n\n  if (includeMatches) transformers.push(transformMatches);\n  if (includeScore) transformers.push(transformScore);\n\n  return results.map((result) => {\n    const { idx } = result;\n\n    const data = {\n      item: docs[idx],\n      refIndex: idx\n    };\n\n    if (transformers.length) {\n      transformers.forEach((transformer) => {\n        transformer(result, data);\n      });\n    }\n\n    return data\n  })\n}\n\nclass Fuse {\n  constructor(docs, options = {}, index) {\n    this.options = { ...Config, ...options };\n\n    if (\n      this.options.useExtendedSearch &&\n      !true\n    ) {\n      throw new Error(EXTENDED_SEARCH_UNAVAILABLE)\n    }\n\n    this._keyStore = new KeyStore(this.options.keys);\n\n    this.setCollection(docs, index);\n  }\n\n  setCollection(docs, index) {\n    this._docs = docs;\n\n    if (index && !(index instanceof FuseIndex)) {\n      throw new Error(INCORRECT_INDEX_TYPE)\n    }\n\n    this._myIndex =\n      index ||\n      createIndex(this.options.keys, this._docs, {\n        getFn: this.options.getFn,\n        fieldNormWeight: this.options.fieldNormWeight\n      });\n  }\n\n  add(doc) {\n    if (!isDefined(doc)) {\n      return\n    }\n\n    this._docs.push(doc);\n    this._myIndex.add(doc);\n  }\n\n  remove(predicate = (/* doc, idx */) => false) {\n    const results = [];\n\n    for (let i = 0, len = this._docs.length; i < len; i += 1) {\n      const doc = this._docs[i];\n      if (predicate(doc, i)) {\n        this.removeAt(i);\n        i -= 1;\n        len -= 1;\n\n        results.push(doc);\n      }\n    }\n\n    return results\n  }\n\n  removeAt(idx) {\n    this._docs.splice(idx, 1);\n    this._myIndex.removeAt(idx);\n  }\n\n  getIndex() {\n    return this._myIndex\n  }\n\n  search(query, { limit = -1 } = {}) {\n    const {\n      includeMatches,\n      includeScore,\n      shouldSort,\n      sortFn,\n      ignoreFieldNorm\n    } = this.options;\n\n    let results = isString(query)\n      ? isString(this._docs[0])\n        ? this._searchStringList(query)\n        : this._searchObjectList(query)\n      : this._searchLogical(query);\n\n    computeScore(results, { ignoreFieldNorm });\n\n    if (shouldSort) {\n      results.sort(sortFn);\n    }\n\n    if (isNumber(limit) && limit > -1) {\n      results = results.slice(0, limit);\n    }\n\n    return format(results, this._docs, {\n      includeMatches,\n      includeScore\n    })\n  }\n\n  _searchStringList(query) {\n    const searcher = createSearcher(query, this.options);\n    const { records } = this._myIndex;\n    const results = [];\n\n    // Iterate over every string in the index\n    records.forEach(({ v: text, i: idx, n: norm }) => {\n      if (!isDefined(text)) {\n        return\n      }\n\n      const { isMatch, score, indices } = searcher.searchIn(text);\n\n      if (isMatch) {\n        results.push({\n          item: text,\n          idx,\n          matches: [{ score, value: text, norm, indices }]\n        });\n      }\n    });\n\n    return results\n  }\n\n  _searchLogical(query) {\n\n    const expression = parse(query, this.options);\n\n    const evaluate = (node, item, idx) => {\n      if (!node.children) {\n        const { keyId, searcher } = node;\n\n        const matches = this._findMatches({\n          key: this._keyStore.get(keyId),\n          value: this._myIndex.getValueForItemAtKeyId(item, keyId),\n          searcher\n        });\n\n        if (matches && matches.length) {\n          return [\n            {\n              idx,\n              item,\n              matches\n            }\n          ]\n        }\n\n        return []\n      }\n\n      const res = [];\n      for (let i = 0, len = node.children.length; i < len; i += 1) {\n        const child = node.children[i];\n        const result = evaluate(child, item, idx);\n        if (result.length) {\n          res.push(...result);\n        } else if (node.operator === LogicalOperator.AND) {\n          return []\n        }\n      }\n      return res\n    };\n\n    const records = this._myIndex.records;\n    const resultMap = {};\n    const results = [];\n\n    records.forEach(({ $: item, i: idx }) => {\n      if (isDefined(item)) {\n        let expResults = evaluate(expression, item, idx);\n\n        if (expResults.length) {\n          // Dedupe when adding\n          if (!resultMap[idx]) {\n            resultMap[idx] = { idx, item, matches: [] };\n            results.push(resultMap[idx]);\n          }\n          expResults.forEach(({ matches }) => {\n            resultMap[idx].matches.push(...matches);\n          });\n        }\n      }\n    });\n\n    return results\n  }\n\n  _searchObjectList(query) {\n    const searcher = createSearcher(query, this.options);\n    const { keys, records } = this._myIndex;\n    const results = [];\n\n    // List is Array<Object>\n    records.forEach(({ $: item, i: idx }) => {\n      if (!isDefined(item)) {\n        return\n      }\n\n      let matches = [];\n\n      // Iterate over every key (i.e, path), and fetch the value at that key\n      keys.forEach((key, keyIndex) => {\n        matches.push(\n          ...this._findMatches({\n            key,\n            value: item[keyIndex],\n            searcher\n          })\n        );\n      });\n\n      if (matches.length) {\n        results.push({\n          idx,\n          item,\n          matches\n        });\n      }\n    });\n\n    return results\n  }\n  _findMatches({ key, value, searcher }) {\n    if (!isDefined(value)) {\n      return []\n    }\n\n    let matches = [];\n\n    if (isArray(value)) {\n      value.forEach(({ v: text, i: idx, n: norm }) => {\n        if (!isDefined(text)) {\n          return\n        }\n\n        const { isMatch, score, indices } = searcher.searchIn(text);\n\n        if (isMatch) {\n          matches.push({\n            score,\n            key,\n            value: text,\n            idx,\n            norm,\n            indices\n          });\n        }\n      });\n    } else {\n      const { v: text, n: norm } = value;\n\n      const { isMatch, score, indices } = searcher.searchIn(text);\n\n      if (isMatch) {\n        matches.push({ score, key, value: text, norm, indices });\n      }\n    }\n\n    return matches\n  }\n}\n\nFuse.version = '6.6.2';\nFuse.createIndex = createIndex;\nFuse.parseIndex = parseIndex;\nFuse.config = Config;\n\n{\n  Fuse.parseQuery = parse;\n}\n\n{\n  register(ExtendedSearch);\n}\n\nexport { Fuse as default };\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarProvider = exports.KBarContext = void 0;\nvar useStore_1 = require(\"./useStore\");\nvar React = __importStar(require(\"react\"));\nvar InternalEvents_1 = require(\"./InternalEvents\");\nexports.KBarContext = React.createContext({});\nvar KBarProvider = function (props) {\n    var contextValue = (0, useStore_1.useStore)(props);\n    return (React.createElement(exports.KBarContext.Provider, { value: contextValue },\n        React.createElement(InternalEvents_1.InternalEvents, null),\n        props.children));\n};\nexports.KBarProvider = KBarProvider;\n", "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  Root: () => Slot,\n  Slot: () => Slot,\n  Slottable: () => Slottable,\n  createSlot: () => createSlot,\n  createSlottable: () => createSlottable\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// src/slot.tsx\nvar React = __toESM(require(\"react\"));\nvar import_react_compose_refs = require(\"@radix-ui/react-compose-refs\");\nvar import_jsx_runtime = require(\"react/jsx-runtime\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: React.isValidElement(newElement) ? React.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = React.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== React.Fragment) {\n        props2.ref = forwardedRef ? (0, import_react_compose_refs.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props2);\n    }\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return React.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n//# sourceMappingURL=index.js.map\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.history = exports.HistoryItemImpl = void 0;\nvar utils_1 = require(\"../utils\");\nvar HistoryItemImpl = /** @class */ (function () {\n    function HistoryItemImpl(item) {\n        this.perform = item.perform;\n        this.negate = item.negate;\n    }\n    HistoryItemImpl.create = function (item) {\n        return new HistoryItemImpl(item);\n    };\n    return HistoryItemImpl;\n}());\nexports.HistoryItemImpl = HistoryItemImpl;\nvar HistoryImpl = /** @class */ (function () {\n    function HistoryImpl() {\n        this.undoStack = [];\n        this.redoStack = [];\n        if (!HistoryImpl.instance) {\n            HistoryImpl.instance = this;\n            this.init();\n        }\n        return HistoryImpl.instance;\n    }\n    HistoryImpl.prototype.init = function () {\n        var _this = this;\n        if (typeof window === \"undefined\")\n            return;\n        window.addEventListener(\"keydown\", function (event) {\n            var _a;\n            if ((!_this.redoStack.length && !_this.undoStack.length) ||\n                (0, utils_1.shouldRejectKeystrokes)()) {\n                return;\n            }\n            var key = (_a = event.key) === null || _a === void 0 ? void 0 : _a.toLowerCase();\n            if (event.metaKey && key === \"z\" && event.shiftKey) {\n                _this.redo();\n            }\n            else if (event.metaKey && key === \"z\") {\n                _this.undo();\n            }\n        });\n    };\n    HistoryImpl.prototype.add = function (item) {\n        var historyItem = HistoryItemImpl.create(item);\n        this.undoStack.push(historyItem);\n        return historyItem;\n    };\n    HistoryImpl.prototype.remove = function (item) {\n        var undoIndex = this.undoStack.findIndex(function (i) { return i === item; });\n        if (undoIndex !== -1) {\n            this.undoStack.splice(undoIndex, 1);\n            return;\n        }\n        var redoIndex = this.redoStack.findIndex(function (i) { return i === item; });\n        if (redoIndex !== -1) {\n            this.redoStack.splice(redoIndex, 1);\n        }\n    };\n    HistoryImpl.prototype.undo = function (item) {\n        // if not undoing a specific item, just undo the latest\n        if (!item) {\n            var item_1 = this.undoStack.pop();\n            if (!item_1)\n                return;\n            item_1 === null || item_1 === void 0 ? void 0 : item_1.negate();\n            this.redoStack.push(item_1);\n            return item_1;\n        }\n        // else undo the specific item\n        var index = this.undoStack.findIndex(function (i) { return i === item; });\n        if (index === -1)\n            return;\n        this.undoStack.splice(index, 1);\n        item.negate();\n        this.redoStack.push(item);\n        return item;\n    };\n    HistoryImpl.prototype.redo = function (item) {\n        if (!item) {\n            var item_2 = this.redoStack.pop();\n            if (!item_2)\n                return;\n            item_2 === null || item_2 === void 0 ? void 0 : item_2.perform();\n            this.undoStack.push(item_2);\n            return item_2;\n        }\n        var index = this.redoStack.findIndex(function (i) { return i === item; });\n        if (index === -1)\n            return;\n        this.redoStack.splice(index, 1);\n        item.perform();\n        this.undoStack.push(item);\n        return item;\n    };\n    HistoryImpl.prototype.reset = function () {\n        this.undoStack.splice(0);\n        this.redoStack.splice(0);\n    };\n    return HistoryImpl;\n}());\nvar history = new HistoryImpl();\nexports.history = history;\nObject.freeze(history);\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Command = void 0;\nvar Command = /** @class */ (function () {\n    function Command(command, options) {\n        var _this = this;\n        if (options === void 0) { options = {}; }\n        this.perform = function () {\n            var negate = command.perform();\n            // no need for history if non negatable\n            if (typeof negate !== \"function\")\n                return;\n            // return if no history enabled\n            var history = options.history;\n            if (!history)\n                return;\n            // since we are performing the same action, we'll clean up the\n            // previous call to the action and create a new history record\n            if (_this.historyItem) {\n                history.remove(_this.historyItem);\n            }\n            _this.historyItem = history.add({\n                perform: command.perform,\n                negate: negate,\n            });\n            _this.history = {\n                undo: function () { return history.undo(_this.historyItem); },\n                redo: function () { return history.redo(_this.historyItem); },\n            };\n        };\n    }\n    return Command;\n}());\nexports.Command = Command;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarAnimator = void 0;\nvar React = __importStar(require(\"react\"));\nvar types_1 = require(\"./types\");\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nvar appearanceAnimationKeyframes = [\n    {\n        opacity: 0,\n        transform: \"scale(.99)\",\n    },\n    { opacity: 1, transform: \"scale(1.01)\" },\n    { opacity: 1, transform: \"scale(1)\" },\n];\nvar bumpAnimationKeyframes = [\n    {\n        transform: \"scale(1)\",\n    },\n    {\n        transform: \"scale(.98)\",\n    },\n    {\n        transform: \"scale(1)\",\n    },\n];\nvar KBarAnimator = function (_a) {\n    var _b, _c;\n    var children = _a.children, style = _a.style, className = _a.className, disableCloseOnOuterClick = _a.disableCloseOnOuterClick;\n    var _d = (0, useKBar_1.useKBar)(function (state) { return ({\n        visualState: state.visualState,\n        currentRootActionId: state.currentRootActionId,\n    }); }), visualState = _d.visualState, currentRootActionId = _d.currentRootActionId, query = _d.query, options = _d.options;\n    var outerRef = React.useRef(null);\n    var innerRef = React.useRef(null);\n    var enterMs = ((_b = options === null || options === void 0 ? void 0 : options.animations) === null || _b === void 0 ? void 0 : _b.enterMs) || 0;\n    var exitMs = ((_c = options === null || options === void 0 ? void 0 : options.animations) === null || _c === void 0 ? void 0 : _c.exitMs) || 0;\n    // Show/hide animation\n    React.useEffect(function () {\n        if (visualState === types_1.VisualState.showing) {\n            return;\n        }\n        var duration = visualState === types_1.VisualState.animatingIn ? enterMs : exitMs;\n        var element = outerRef.current;\n        element === null || element === void 0 ? void 0 : element.animate(appearanceAnimationKeyframes, {\n            duration: duration,\n            easing: \n            // TODO: expose easing in options\n            visualState === types_1.VisualState.animatingOut ? \"ease-in\" : \"ease-out\",\n            direction: visualState === types_1.VisualState.animatingOut ? \"reverse\" : \"normal\",\n            fill: \"forwards\",\n        });\n    }, [options, visualState, enterMs, exitMs]);\n    // Height animation\n    var previousHeight = React.useRef();\n    React.useEffect(function () {\n        // Only animate if we're actually showing\n        if (visualState === types_1.VisualState.showing) {\n            var outer_1 = outerRef.current;\n            var inner_1 = innerRef.current;\n            if (!outer_1 || !inner_1) {\n                return;\n            }\n            var ro_1 = new ResizeObserver(function (entries) {\n                for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                    var entry = entries_1[_i];\n                    var cr = entry.contentRect;\n                    if (!previousHeight.current) {\n                        previousHeight.current = cr.height;\n                    }\n                    outer_1.animate([\n                        {\n                            height: previousHeight.current + \"px\",\n                        },\n                        {\n                            height: cr.height + \"px\",\n                        },\n                    ], {\n                        duration: enterMs / 2,\n                        // TODO: expose configs here\n                        easing: \"ease-out\",\n                        fill: \"forwards\",\n                    });\n                    previousHeight.current = cr.height;\n                }\n            });\n            ro_1.observe(inner_1);\n            return function () {\n                ro_1.unobserve(inner_1);\n            };\n        }\n    }, [visualState, options, enterMs, exitMs]);\n    // Bump animation between nested actions\n    var firstRender = React.useRef(true);\n    React.useEffect(function () {\n        if (firstRender.current) {\n            firstRender.current = false;\n            return;\n        }\n        var element = outerRef.current;\n        if (element) {\n            element.animate(bumpAnimationKeyframes, {\n                duration: enterMs,\n                easing: \"ease-out\",\n            });\n        }\n    }, [currentRootActionId, enterMs]);\n    (0, utils_1.useOuterClick)(outerRef, function () {\n        var _a, _b;\n        if (disableCloseOnOuterClick) {\n            return;\n        }\n        query.setVisualState(types_1.VisualState.animatingOut);\n        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onClose) === null || _b === void 0 ? void 0 : _b.call(_a);\n    });\n    return (React.createElement(\"div\", { ref: outerRef, style: __assign(__assign(__assign({}, appearanceAnimationKeyframes[0]), style), { pointerEvents: \"auto\" }), className: className },\n        React.createElement(\"div\", { ref: innerRef }, children)));\n};\nexports.KBarAnimator = KBarAnimator;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Priority = exports.isModKey = exports.shouldRejectKeystrokes = exports.useThrottledValue = exports.getScrollbarWidth = exports.useIsomorphicLayout = exports.noop = exports.createAction = exports.randomId = exports.usePointerMovedSinceMount = exports.useOuterClick = exports.swallowEvent = void 0;\nvar React = __importStar(require(\"react\"));\nfunction swallowEvent(event) {\n    event.stopPropagation();\n    event.preventDefault();\n}\nexports.swallowEvent = swallowEvent;\nfunction useOuterClick(dom, cb) {\n    var cbRef = React.useRef(cb);\n    cbRef.current = cb;\n    React.useEffect(function () {\n        function handler(event) {\n            var _a, _b;\n            if (((_a = dom.current) === null || _a === void 0 ? void 0 : _a.contains(event.target)) ||\n                // Add support for ReactShadowRoot\n                // @ts-expect-error wrong types, the `host` property exists https://stackoverflow.com/a/25340456\n                event.target === ((_b = dom.current) === null || _b === void 0 ? void 0 : _b.getRootNode().host)) {\n                return;\n            }\n            event.preventDefault();\n            event.stopPropagation();\n            cbRef.current();\n        }\n        window.addEventListener(\"pointerdown\", handler, true);\n        return function () { return window.removeEventListener(\"pointerdown\", handler, true); };\n    }, [dom]);\n}\nexports.useOuterClick = useOuterClick;\nfunction usePointerMovedSinceMount() {\n    var _a = React.useState(false), moved = _a[0], setMoved = _a[1];\n    React.useEffect(function () {\n        function handler() {\n            setMoved(true);\n        }\n        if (!moved) {\n            window.addEventListener(\"pointermove\", handler);\n            return function () { return window.removeEventListener(\"pointermove\", handler); };\n        }\n    }, [moved]);\n    return moved;\n}\nexports.usePointerMovedSinceMount = usePointerMovedSinceMount;\nfunction randomId() {\n    return Math.random().toString(36).substring(2, 9);\n}\nexports.randomId = randomId;\nfunction createAction(params) {\n    return __assign({ id: randomId() }, params);\n}\nexports.createAction = createAction;\nfunction noop() { }\nexports.noop = noop;\nexports.useIsomorphicLayout = typeof window === \"undefined\" ? noop : React.useLayoutEffect;\n// https://stackoverflow.com/questions/13382516/getting-scroll-bar-width-using-javascript\nfunction getScrollbarWidth() {\n    var outer = document.createElement(\"div\");\n    outer.style.visibility = \"hidden\";\n    outer.style.overflow = \"scroll\";\n    document.body.appendChild(outer);\n    var inner = document.createElement(\"div\");\n    outer.appendChild(inner);\n    var scrollbarWidth = outer.offsetWidth - inner.offsetWidth;\n    outer.parentNode.removeChild(outer);\n    return scrollbarWidth;\n}\nexports.getScrollbarWidth = getScrollbarWidth;\nfunction useThrottledValue(value, ms) {\n    if (ms === void 0) { ms = 100; }\n    var _a = React.useState(value), throttledValue = _a[0], setThrottledValue = _a[1];\n    var lastRan = React.useRef(Date.now());\n    React.useEffect(function () {\n        if (ms === 0)\n            return;\n        var timeout = setTimeout(function () {\n            setThrottledValue(value);\n            lastRan.current = Date.now();\n        }, lastRan.current - (Date.now() - ms));\n        return function () {\n            clearTimeout(timeout);\n        };\n    }, [ms, value]);\n    return ms === 0 ? value : throttledValue;\n}\nexports.useThrottledValue = useThrottledValue;\nfunction shouldRejectKeystrokes(_a) {\n    var _b, _c, _d;\n    var _e = _a === void 0 ? { ignoreWhenFocused: [] } : _a, ignoreWhenFocused = _e.ignoreWhenFocused;\n    var inputs = __spreadArray([\"input\", \"textarea\"], ignoreWhenFocused, true).map(function (el) {\n        return el.toLowerCase();\n    });\n    var activeElement = document.activeElement;\n    var ignoreStrokes = activeElement &&\n        (inputs.indexOf(activeElement.tagName.toLowerCase()) !== -1 ||\n            ((_b = activeElement.attributes.getNamedItem(\"role\")) === null || _b === void 0 ? void 0 : _b.value) === \"textbox\" ||\n            ((_c = activeElement.attributes.getNamedItem(\"contenteditable\")) === null || _c === void 0 ? void 0 : _c.value) ===\n                \"true\" ||\n            ((_d = activeElement.attributes.getNamedItem(\"contenteditable\")) === null || _d === void 0 ? void 0 : _d.value) ===\n                \"plaintext-only\");\n    return ignoreStrokes;\n}\nexports.shouldRejectKeystrokes = shouldRejectKeystrokes;\nvar SSR = typeof window === \"undefined\";\nvar isMac = !SSR && window.navigator.platform === \"MacIntel\";\nfunction isModKey(event) {\n    return isMac ? event.metaKey : event.ctrlKey;\n}\nexports.isModKey = isModKey;\nexports.Priority = {\n    HIGH: 1,\n    NORMAL: 0,\n    LOW: -1,\n};\n", "var HAS_WEAKSET_SUPPORT = typeof WeakSet === 'function';\r\nvar keys = Object.keys;\r\n/**\r\n * are the values passed strictly equal or both NaN\r\n *\r\n * @param a the value to compare against\r\n * @param b the value to test\r\n * @returns are the values equal by the SameValueZero principle\r\n */\r\nfunction sameValueZeroEqual(a, b) {\r\n    return a === b || (a !== a && b !== b);\r\n}\r\n/**\r\n * is the value a plain object\r\n *\r\n * @param value the value to test\r\n * @returns is the value a plain object\r\n */\r\nfunction isPlainObject(value) {\r\n    return value.constructor === Object || value.constructor == null;\r\n}\r\n/**\r\n * is the value promise-like (meaning it is thenable)\r\n *\r\n * @param value the value to test\r\n * @returns is the value promise-like\r\n */\r\nfunction isPromiseLike(value) {\r\n    return !!value && typeof value.then === 'function';\r\n}\r\n/**\r\n * is the value passed a react element\r\n *\r\n * @param value the value to test\r\n * @returns is the value a react element\r\n */\r\nfunction isReactElement(value) {\r\n    return !!(value && value.$$typeof);\r\n}\r\n/**\r\n * in cases where WeakSet is not supported, creates a new custom\r\n * object that mimics the necessary API aspects for cache purposes\r\n *\r\n * @returns the new cache object\r\n */\r\nfunction getNewCacheFallback() {\r\n    var values = [];\r\n    return {\r\n        add: function (value) {\r\n            values.push(value);\r\n        },\r\n        has: function (value) {\r\n            return values.indexOf(value) !== -1;\r\n        },\r\n    };\r\n}\r\n/**\r\n * get a new cache object to prevent circular references\r\n *\r\n * @returns the new cache object\r\n */\r\nvar getNewCache = (function (canUseWeakMap) {\r\n    if (canUseWeakMap) {\r\n        return function _getNewCache() {\r\n            return new WeakSet();\r\n        };\r\n    }\r\n    return getNewCacheFallback;\r\n})(HAS_WEAKSET_SUPPORT);\r\n/**\r\n * create a custom isEqual handler specific to circular objects\r\n *\r\n * @param [isEqual] the isEqual comparator to use instead of isDeepEqual\r\n * @returns the method to create the `isEqual` function\r\n */\r\nfunction createCircularEqualCreator(isEqual) {\r\n    return function createCircularEqual(comparator) {\r\n        var _comparator = isEqual || comparator;\r\n        return function circularEqual(a, b, cache) {\r\n            if (cache === void 0) { cache = getNewCache(); }\r\n            var isCacheableA = !!a && typeof a === 'object';\r\n            var isCacheableB = !!b && typeof b === 'object';\r\n            if (isCacheableA || isCacheableB) {\r\n                var hasA = isCacheableA && cache.has(a);\r\n                var hasB = isCacheableB && cache.has(b);\r\n                if (hasA || hasB) {\r\n                    return hasA && hasB;\r\n                }\r\n                if (isCacheableA) {\r\n                    cache.add(a);\r\n                }\r\n                if (isCacheableB) {\r\n                    cache.add(b);\r\n                }\r\n            }\r\n            return _comparator(a, b, cache);\r\n        };\r\n    };\r\n}\r\n/**\r\n * are the arrays equal in value\r\n *\r\n * @param a the array to test\r\n * @param b the array to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta object to pass through\r\n * @returns are the arrays equal\r\n */\r\nfunction areArraysEqual(a, b, isEqual, meta) {\r\n    var index = a.length;\r\n    if (b.length !== index) {\r\n        return false;\r\n    }\r\n    while (index-- > 0) {\r\n        if (!isEqual(a[index], b[index], meta)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * are the maps equal in value\r\n *\r\n * @param a the map to test\r\n * @param b the map to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta map to pass through\r\n * @returns are the maps equal\r\n */\r\nfunction areMapsEqual(a, b, isEqual, meta) {\r\n    var isValueEqual = a.size === b.size;\r\n    if (isValueEqual && a.size) {\r\n        var matchedIndices_1 = {};\r\n        a.forEach(function (aValue, aKey) {\r\n            if (isValueEqual) {\r\n                var hasMatch_1 = false;\r\n                var matchIndex_1 = 0;\r\n                b.forEach(function (bValue, bKey) {\r\n                    if (!hasMatch_1 && !matchedIndices_1[matchIndex_1]) {\r\n                        hasMatch_1 =\r\n                            isEqual(aKey, bKey, meta) && isEqual(aValue, bValue, meta);\r\n                        if (hasMatch_1) {\r\n                            matchedIndices_1[matchIndex_1] = true;\r\n                        }\r\n                    }\r\n                    matchIndex_1++;\r\n                });\r\n                isValueEqual = hasMatch_1;\r\n            }\r\n        });\r\n    }\r\n    return isValueEqual;\r\n}\r\nvar OWNER = '_owner';\r\nvar hasOwnProperty = Function.prototype.bind.call(Function.prototype.call, Object.prototype.hasOwnProperty);\r\n/**\r\n * are the objects equal in value\r\n *\r\n * @param a the object to test\r\n * @param b the object to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta object to pass through\r\n * @returns are the objects equal\r\n */\r\nfunction areObjectsEqual(a, b, isEqual, meta) {\r\n    var keysA = keys(a);\r\n    var index = keysA.length;\r\n    if (keys(b).length !== index) {\r\n        return false;\r\n    }\r\n    if (index) {\r\n        var key = void 0;\r\n        while (index-- > 0) {\r\n            key = keysA[index];\r\n            if (key === OWNER) {\r\n                var reactElementA = isReactElement(a);\r\n                var reactElementB = isReactElement(b);\r\n                if ((reactElementA || reactElementB) &&\r\n                    reactElementA !== reactElementB) {\r\n                    return false;\r\n                }\r\n            }\r\n            if (!hasOwnProperty(b, key) || !isEqual(a[key], b[key], meta)) {\r\n                return false;\r\n            }\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * are the regExps equal in value\r\n *\r\n * @param a the regExp to test\r\n * @param b the regExp to test agains\r\n * @returns are the regExps equal\r\n */\r\nfunction areRegExpsEqual(a, b) {\r\n    return (a.source === b.source &&\r\n        a.global === b.global &&\r\n        a.ignoreCase === b.ignoreCase &&\r\n        a.multiline === b.multiline &&\r\n        a.unicode === b.unicode &&\r\n        a.sticky === b.sticky &&\r\n        a.lastIndex === b.lastIndex);\r\n}\r\n/**\r\n * are the sets equal in value\r\n *\r\n * @param a the set to test\r\n * @param b the set to test against\r\n * @param isEqual the comparator to determine equality\r\n * @param meta the meta set to pass through\r\n * @returns are the sets equal\r\n */\r\nfunction areSetsEqual(a, b, isEqual, meta) {\r\n    var isValueEqual = a.size === b.size;\r\n    if (isValueEqual && a.size) {\r\n        var matchedIndices_2 = {};\r\n        a.forEach(function (aValue) {\r\n            if (isValueEqual) {\r\n                var hasMatch_2 = false;\r\n                var matchIndex_2 = 0;\r\n                b.forEach(function (bValue) {\r\n                    if (!hasMatch_2 && !matchedIndices_2[matchIndex_2]) {\r\n                        hasMatch_2 = isEqual(aValue, bValue, meta);\r\n                        if (hasMatch_2) {\r\n                            matchedIndices_2[matchIndex_2] = true;\r\n                        }\r\n                    }\r\n                    matchIndex_2++;\r\n                });\r\n                isValueEqual = hasMatch_2;\r\n            }\r\n        });\r\n    }\r\n    return isValueEqual;\r\n}\n\nvar HAS_MAP_SUPPORT = typeof Map === 'function';\r\nvar HAS_SET_SUPPORT = typeof Set === 'function';\r\nfunction createComparator(createIsEqual) {\r\n    var isEqual = \r\n    /* eslint-disable no-use-before-define */\r\n    typeof createIsEqual === 'function'\r\n        ? createIsEqual(comparator)\r\n        : comparator;\r\n    /* eslint-enable */\r\n    /**\r\n     * compare the value of the two objects and return true if they are equivalent in values\r\n     *\r\n     * @param a the value to test against\r\n     * @param b the value to test\r\n     * @param [meta] an optional meta object that is passed through to all equality test calls\r\n     * @returns are a and b equivalent in value\r\n     */\r\n    function comparator(a, b, meta) {\r\n        if (a === b) {\r\n            return true;\r\n        }\r\n        if (a && b && typeof a === 'object' && typeof b === 'object') {\r\n            if (isPlainObject(a) && isPlainObject(b)) {\r\n                return areObjectsEqual(a, b, isEqual, meta);\r\n            }\r\n            var aShape = Array.isArray(a);\r\n            var bShape = Array.isArray(b);\r\n            if (aShape || bShape) {\r\n                return aShape === bShape && areArraysEqual(a, b, isEqual, meta);\r\n            }\r\n            aShape = a instanceof Date;\r\n            bShape = b instanceof Date;\r\n            if (aShape || bShape) {\r\n                return (aShape === bShape && sameValueZeroEqual(a.getTime(), b.getTime()));\r\n            }\r\n            aShape = a instanceof RegExp;\r\n            bShape = b instanceof RegExp;\r\n            if (aShape || bShape) {\r\n                return aShape === bShape && areRegExpsEqual(a, b);\r\n            }\r\n            if (isPromiseLike(a) || isPromiseLike(b)) {\r\n                return a === b;\r\n            }\r\n            if (HAS_MAP_SUPPORT) {\r\n                aShape = a instanceof Map;\r\n                bShape = b instanceof Map;\r\n                if (aShape || bShape) {\r\n                    return aShape === bShape && areMapsEqual(a, b, isEqual, meta);\r\n                }\r\n            }\r\n            if (HAS_SET_SUPPORT) {\r\n                aShape = a instanceof Set;\r\n                bShape = b instanceof Set;\r\n                if (aShape || bShape) {\r\n                    return aShape === bShape && areSetsEqual(a, b, isEqual, meta);\r\n                }\r\n            }\r\n            return areObjectsEqual(a, b, isEqual, meta);\r\n        }\r\n        return a !== a && b !== b;\r\n    }\r\n    return comparator;\r\n}\n\nvar deepEqual = createComparator();\r\nvar shallowEqual = createComparator(function () { return sameValueZeroEqual; });\r\nvar circularDeepEqual = createComparator(createCircularEqualCreator());\r\nvar circularShallowEqual = createComparator(createCircularEqualCreator(sameValueZeroEqual));\n\nexport { circularDeepEqual, circularShallowEqual, createComparator as createCustomEqual, deepEqual, sameValueZeroEqual, shallowEqual };\n//# sourceMappingURL=fast-equals.esm.js.map\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.KBarResults = void 0;\nvar React = __importStar(require(\"react\"));\nvar react_virtual_1 = require(\"react-virtual\");\nvar KBarSearch_1 = require(\"./KBarSearch\");\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nvar START_INDEX = 0;\nvar KBarResults = function (props) {\n    var activeRef = React.useRef(null);\n    var parentRef = React.useRef(null);\n    // store a ref to all items so we do not have to pass\n    // them as a dependency when setting up event listeners.\n    var itemsRef = React.useRef(props.items);\n    itemsRef.current = props.items;\n    var rowVirtualizer = (0, react_virtual_1.useVirtual)({\n        size: itemsRef.current.length,\n        parentRef: parentRef,\n    });\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        currentRootActionId: state.currentRootActionId,\n        activeIndex: state.activeIndex,\n    }); }), query = _a.query, search = _a.search, currentRootActionId = _a.currentRootActionId, activeIndex = _a.activeIndex, options = _a.options;\n    React.useEffect(function () {\n        var handler = function (event) {\n            var _a;\n            if (event.isComposing) {\n                return;\n            }\n            if (event.key === \"ArrowUp\" || (event.ctrlKey && event.key === \"p\")) {\n                event.preventDefault();\n                event.stopPropagation();\n                query.setActiveIndex(function (index) {\n                    var nextIndex = index > START_INDEX ? index - 1 : index;\n                    // avoid setting active index on a group\n                    if (typeof itemsRef.current[nextIndex] === \"string\") {\n                        if (nextIndex === 0)\n                            return index;\n                        nextIndex -= 1;\n                    }\n                    return nextIndex;\n                });\n            }\n            else if (event.key === \"ArrowDown\" ||\n                (event.ctrlKey && event.key === \"n\")) {\n                event.preventDefault();\n                event.stopPropagation();\n                query.setActiveIndex(function (index) {\n                    var nextIndex = index < itemsRef.current.length - 1 ? index + 1 : index;\n                    // avoid setting active index on a group\n                    if (typeof itemsRef.current[nextIndex] === \"string\") {\n                        if (nextIndex === itemsRef.current.length - 1)\n                            return index;\n                        nextIndex += 1;\n                    }\n                    return nextIndex;\n                });\n            }\n            else if (event.key === \"Enter\") {\n                event.preventDefault();\n                event.stopPropagation();\n                // storing the active dom element in a ref prevents us from\n                // having to calculate the current action to perform based\n                // on the `activeIndex`, which we would have needed to add\n                // as part of the dependencies array.\n                (_a = activeRef.current) === null || _a === void 0 ? void 0 : _a.click();\n            }\n        };\n        window.addEventListener(\"keydown\", handler, { capture: true });\n        return function () {\n            return window.removeEventListener(\"keydown\", handler, { capture: true });\n        };\n    }, [query]);\n    // destructuring here to prevent linter warning to pass\n    // entire rowVirtualizer in the dependencies array.\n    var scrollToIndex = rowVirtualizer.scrollToIndex;\n    React.useEffect(function () {\n        scrollToIndex(activeIndex, {\n            // ensure that if the first item in the list is a group\n            // name and we are focused on the second item, to not\n            // scroll past that group, hiding it.\n            align: activeIndex <= 1 ? \"end\" : \"auto\",\n        });\n    }, [activeIndex, scrollToIndex]);\n    // reset active index only when search or root action changes\n    React.useEffect(function () {\n        query.setActiveIndex(\n        // avoid setting active index on a group\n        typeof itemsRef.current[START_INDEX] === \"string\"\n            ? START_INDEX + 1\n            : START_INDEX);\n    }, [search, currentRootActionId, query]);\n    // adjust active index when items change (ie when actions load async)\n    React.useEffect(function () {\n        var currentIndex = activeIndex;\n        var maxIndex = itemsRef.current.length - 1;\n        if (currentIndex > maxIndex && maxIndex >= 0) {\n            var newIndex = maxIndex;\n            if (typeof itemsRef.current[newIndex] === \"string\" && newIndex > 0) {\n                newIndex -= 1;\n            }\n            query.setActiveIndex(newIndex);\n        }\n        else if (currentIndex <= maxIndex &&\n            typeof itemsRef.current[currentIndex] === \"string\") {\n            var newIndex = currentIndex + 1;\n            if (newIndex > maxIndex ||\n                typeof itemsRef.current[newIndex] === \"string\") {\n                newIndex = currentIndex - 1;\n            }\n            if (newIndex >= 0 &&\n                newIndex <= maxIndex &&\n                typeof itemsRef.current[newIndex] !== \"string\") {\n                query.setActiveIndex(newIndex);\n            }\n        }\n    }, [props.items, activeIndex, query]);\n    var execute = React.useCallback(function (item) {\n        var _a, _b;\n        if (typeof item === \"string\")\n            return;\n        if (item.command) {\n            item.command.perform(item);\n            query.toggle();\n        }\n        else {\n            query.setSearch(\"\");\n            query.setCurrentRootAction(item.id);\n        }\n        (_b = (_a = options.callbacks) === null || _a === void 0 ? void 0 : _a.onSelectAction) === null || _b === void 0 ? void 0 : _b.call(_a, item);\n    }, [query, options]);\n    var pointerMoved = (0, utils_1.usePointerMovedSinceMount)();\n    return (React.createElement(\"div\", { ref: parentRef, style: {\n            maxHeight: props.maxHeight || 400,\n            position: \"relative\",\n            overflow: \"auto\",\n        } },\n        React.createElement(\"div\", { role: \"listbox\", id: KBarSearch_1.KBAR_LISTBOX, style: {\n                height: rowVirtualizer.totalSize + \"px\",\n                width: \"100%\",\n            } }, rowVirtualizer.virtualItems.map(function (virtualRow) {\n            var item = itemsRef.current[virtualRow.index];\n            var handlers = typeof item !== \"string\" && {\n                onPointerMove: function () {\n                    return pointerMoved &&\n                        activeIndex !== virtualRow.index &&\n                        query.setActiveIndex(virtualRow.index);\n                },\n                onPointerDown: function () { return query.setActiveIndex(virtualRow.index); },\n                onClick: function () { return execute(item); },\n            };\n            var active = virtualRow.index === activeIndex;\n            return (React.createElement(\"div\", __assign({ ref: active ? activeRef : null, id: (0, KBarSearch_1.getListboxItemId)(virtualRow.index), role: \"option\", \"aria-selected\": active, key: virtualRow.index, style: {\n                    position: \"absolute\",\n                    top: 0,\n                    left: 0,\n                    width: \"100%\",\n                    transform: \"translateY(\" + virtualRow.start + \"px)\",\n                } }, handlers), React.cloneElement(props.onRender({\n                item: item,\n                active: active,\n            }), {\n                ref: virtualRow.measureRef,\n            })));\n        }))));\n};\nexports.KBarResults = KBarResults;\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useDeepMatches = exports.useMatches = exports.NO_GROUP = void 0;\nvar React = __importStar(require(\"react\"));\nvar useKBar_1 = require(\"./useKBar\");\nvar utils_1 = require(\"./utils\");\nvar fuse_js_1 = __importDefault(require(\"fuse.js\"));\nexports.NO_GROUP = {\n    name: \"none\",\n    priority: utils_1.Priority.NORMAL,\n};\nvar fuseOptions = {\n    keys: [\n        {\n            name: \"name\",\n            weight: 0.5,\n        },\n        {\n            name: \"keywords\",\n            getFn: function (item) { var _a; return ((_a = item.keywords) !== null && _a !== void 0 ? _a : \"\").split(\",\"); },\n            weight: 0.5,\n        },\n        \"subtitle\",\n    ],\n    ignoreLocation: true,\n    includeScore: true,\n    includeMatches: true,\n    threshold: 0.2,\n    minMatchCharLength: 1,\n};\nfunction order(a, b) {\n    /**\n     * Larger the priority = higher up the list\n     */\n    return b.priority - a.priority;\n}\n/**\n * returns deep matches only when a search query is present\n */\nfunction useMatches() {\n    var _a = (0, useKBar_1.useKBar)(function (state) { return ({\n        search: state.searchQuery,\n        actions: state.actions,\n        rootActionId: state.currentRootActionId,\n    }); }), search = _a.search, actions = _a.actions, rootActionId = _a.rootActionId;\n    var rootResults = React.useMemo(function () {\n        return Object.keys(actions)\n            .reduce(function (acc, actionId) {\n            var action = actions[actionId];\n            if (!action.parent && !rootActionId) {\n                acc.push(action);\n            }\n            if (action.id === rootActionId) {\n                for (var i = 0; i < action.children.length; i++) {\n                    acc.push(action.children[i]);\n                }\n            }\n            return acc;\n        }, [])\n            .sort(order);\n    }, [actions, rootActionId]);\n    var getDeepResults = React.useCallback(function (actions) {\n        var actionsClone = [];\n        for (var i = 0; i < actions.length; i++) {\n            actionsClone.push(actions[i]);\n        }\n        return (function collectChildren(actions, all) {\n            if (all === void 0) { all = actionsClone; }\n            for (var i = 0; i < actions.length; i++) {\n                if (actions[i].children.length > 0) {\n                    var childsChildren = actions[i].children;\n                    for (var i_1 = 0; i_1 < childsChildren.length; i_1++) {\n                        all.push(childsChildren[i_1]);\n                    }\n                    collectChildren(actions[i].children, all);\n                }\n            }\n            return all;\n        })(actions);\n    }, []);\n    var emptySearch = !search;\n    var filtered = React.useMemo(function () {\n        if (emptySearch)\n            return rootResults;\n        return getDeepResults(rootResults);\n    }, [getDeepResults, rootResults, emptySearch]);\n    var fuse = React.useMemo(function () { return new fuse_js_1.default(filtered, fuseOptions); }, [filtered]);\n    var matches = useInternalMatches(filtered, search, fuse);\n    var results = React.useMemo(function () {\n        var _a, _b;\n        /**\n         * Store a reference to a section and it's list of actions.\n         * Alongside these actions, we'll keep a temporary record of the\n         * final priority calculated by taking the commandScore + the\n         * explicitly set `action.priority` value.\n         */\n        var map = {};\n        /**\n         * Store another reference to a list of sections alongside\n         * the section's final priority, calculated the same as above.\n         */\n        var list = [];\n        /**\n         * We'll take the list above and sort by its priority. Then we'll\n         * collect all actions from the map above for this specific name and\n         * sort by its priority as well.\n         */\n        var ordered = [];\n        for (var i = 0; i < matches.length; i++) {\n            var match = matches[i];\n            var action = match.action;\n            var score = match.score || utils_1.Priority.NORMAL;\n            var section = {\n                name: typeof action.section === \"string\"\n                    ? action.section\n                    : ((_a = action.section) === null || _a === void 0 ? void 0 : _a.name) || exports.NO_GROUP.name,\n                priority: typeof action.section === \"string\"\n                    ? score\n                    : ((_b = action.section) === null || _b === void 0 ? void 0 : _b.priority) || 0 + score,\n            };\n            if (!map[section.name]) {\n                map[section.name] = [];\n                list.push(section);\n            }\n            map[section.name].push({\n                priority: action.priority + score,\n                action: action,\n            });\n        }\n        ordered = list.sort(order).map(function (group) { return ({\n            name: group.name,\n            actions: map[group.name].sort(order).map(function (item) { return item.action; }),\n        }); });\n        /**\n         * Our final result is simply flattening the ordered list into\n         * our familiar (ActionImpl | string)[] shape.\n         */\n        var results = [];\n        for (var i = 0; i < ordered.length; i++) {\n            var group = ordered[i];\n            if (group.name !== exports.NO_GROUP.name)\n                results.push(group.name);\n            for (var i_2 = 0; i_2 < group.actions.length; i_2++) {\n                results.push(group.actions[i_2]);\n            }\n        }\n        return results;\n    }, [matches]);\n    // ensure that users have an accurate `currentRootActionId`\n    // that syncs with the throttled return value.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    var memoRootActionId = React.useMemo(function () { return rootActionId; }, [results]);\n    return React.useMemo(function () { return ({\n        results: results,\n        rootActionId: memoRootActionId,\n    }); }, [memoRootActionId, results]);\n}\nexports.useMatches = useMatches;\nfunction useInternalMatches(filtered, search, fuse) {\n    var value = React.useMemo(function () { return ({\n        filtered: filtered,\n        search: search,\n    }); }, [filtered, search]);\n    var _a = (0, utils_1.useThrottledValue)(value), throttledFiltered = _a.filtered, throttledSearch = _a.search;\n    return React.useMemo(function () {\n        if (throttledSearch.trim() === \"\") {\n            return throttledFiltered.map(function (action) { return ({ score: 0, action: action }); });\n        }\n        var matches = [];\n        // Use Fuse's `search` method to perform the search efficiently\n        var searchResults = fuse.search(throttledSearch);\n        // Format the search results to match the existing structure\n        matches = searchResults.map(function (_a) {\n            var action = _a.item, score = _a.score;\n            return ({\n                score: 1 / ((score !== null && score !== void 0 ? score : 0) + 1),\n                action: action,\n            });\n        });\n        return matches;\n    }, [throttledFiltered, throttledSearch, fuse]);\n}\n/**\n * @deprecated use useMatches\n */\nexports.useDeepMatches = useMatches;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useKBar = void 0;\nvar React = __importStar(require(\"react\"));\nvar KBarContextProvider_1 = require(\"./KBarContextProvider\");\nfunction useKBar(collector) {\n    var _a = React.useContext(KBarContextProvider_1.KBarContext), query = _a.query, getState = _a.getState, subscribe = _a.subscribe, options = _a.options;\n    var collected = React.useRef(collector === null || collector === void 0 ? void 0 : collector(getState()));\n    var collectorRef = React.useRef(collector);\n    var onCollect = React.useCallback(function (collected) { return (__assign(__assign({}, collected), { query: query, options: options })); }, [query, options]);\n    var _b = React.useState(onCollect(collected.current)), render = _b[0], setRender = _b[1];\n    React.useEffect(function () {\n        var unsubscribe;\n        if (collectorRef.current) {\n            unsubscribe = subscribe(function (current) { return collectorRef.current(current); }, function (collected) { return setRender(onCollect(collected)); });\n        }\n        return function () {\n            if (unsubscribe) {\n                unsubscribe();\n            }\n        };\n    }, [onCollect, subscribe]);\n    return render;\n}\nexports.useKBar = useKBar;\n", "'use client';\nexport {\n  Portal,\n  //\n  Root,\n} from './portal';\nexport type { PortalProps } from './portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// packages/react/compose-refs/src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  composeRefs: () => composeRefs,\n  useComposedRefs: () => useComposedRefs\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// packages/react/compose-refs/src/compose-refs.tsx\nvar React = __toESM(require(\"react\"));\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return React.useCallback(composeRefs(...refs), refs);\n}\n//# sourceMappingURL=index.js.map\n", "\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Priority = exports.createAction = void 0;\nvar utils_1 = require(\"./utils\");\nObject.defineProperty(exports, \"createAction\", { enumerable: true, get: function () { return utils_1.createAction; } });\nObject.defineProperty(exports, \"Priority\", { enumerable: true, get: function () { return utils_1.Priority; } });\n__exportStar(require(\"./useMatches\"), exports);\n__exportStar(require(\"./KBarPortal\"), exports);\n__exportStar(require(\"./KBarPositioner\"), exports);\n__exportStar(require(\"./KBarSearch\"), exports);\n__exportStar(require(\"./KBarResults\"), exports);\n__exportStar(require(\"./useKBar\"), exports);\n__exportStar(require(\"./useRegisterActions\"), exports);\n__exportStar(require(\"./KBarContextProvider\"), exports);\n__exportStar(require(\"./KBarAnimator\"), exports);\n__exportStar(require(\"./types\"), exports);\n__exportStar(require(\"./action\"), exports);\n"], "names": ["ReactDOM"], "sourceRoot": ""}