{"version": 3, "file": "../app/dashboard/profile/[[...profile]]/page.js", "mappings": "ubAAA,6GCAA,+JCEA,SAASA,EAAK,WACZC,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,OAAOH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBP,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,CACjBR,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,aAAaH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,mBAAmBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBV,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBX,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,swBCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,yBCNjN,mECAA,0GCAA,8CCAA,uCAAmN,yBCAnN,gDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,gDCAA,uCAAmN,yBCAnN,sDCAA,wDCAA,oDCAA,uECAA,oDCAA,iECAA,iJCMe,SAASM,IACtB,GAAM,MACJC,CAAI,SACJC,CAAO,SACPC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,UACX,EACS,OADI,CACJ,EAACd,MAAAA,UAAI,eAETW,EAGE,IAHI,CAGJ,MAACX,MAAAA,CAAIF,UAAU,8BAA8BK,wBAAsB,kBAAkBC,0BAAwB,kCAChH,UAACP,EAAAA,EAAIA,CAAAA,CAACkB,sBAAoB,OAAOX,0BAAwB,iCACvD,UAACC,EAAAA,EAAUA,CAAAA,CAACU,sBAAoB,aAAaX,0BAAwB,iCACnE,WAACJ,MAAAA,CAAIF,UAAU,kFACb,WAACkB,EAAAA,MAAMA,CAAAA,CAAClB,UAAU,qBAAqBiB,sBAAoB,SAASX,0BAAwB,kCAC1F,UAACa,EAAAA,WAAWA,CAAAA,CAACC,IAAK,CAAC,iCAAiC,EAAEP,EAAKQ,IAAI,CAAC,kBAAkB,CAAC,CAAEJ,sBAAoB,cAAcX,0BAAwB,0BAC/I,UAACgB,EAAAA,cAAcA,CAAAA,CAACL,sBAAoB,iBAAiBX,0BAAwB,iCAAyBO,EAAKQ,IAAI,CAACE,MAAM,CAAC,QAEzH,WAACrB,MAAAA,CAAIF,UAAU,sBACb,UAACQ,EAAAA,EAASA,CAAAA,CAACR,UAAU,WAAWiB,sBAAoB,YAAYX,0BAAwB,iCAAyBO,EAAKQ,IAAI,GAC1H,UAACG,IAAAA,CAAExB,UAAU,iCAAyBa,EAAKY,KAAK,aAMxD,WAAC1B,EAAAA,EAAIA,CAAAA,CAACkB,sBAAoB,OAAOX,0BAAwB,kCACvD,UAACC,EAAAA,EAAUA,CAAAA,CAACU,sBAAoB,aAAaX,0BAAwB,iCACnE,UAACE,EAAAA,EAASA,CAAAA,CAACS,sBAAoB,YAAYX,0BAAwB,iCAAwB,sBAE7F,WAACI,EAAAA,EAAWA,CAAAA,CAACV,UAAU,YAAYiB,sBAAoB,cAAcX,0BAAwB,kCAC3F,WAACJ,MAAAA,CAAIF,UAAU,kDACb,WAACE,MAAAA,CAAIF,UAAU,sBACb,UAACwB,IAAAA,CAAExB,UAAU,qDAA4C,cAGzD,UAACwB,IAAAA,UAAGX,EAAKQ,IAAI,MAEf,WAACnB,MAAAA,CAAIF,UAAU,sBACb,UAACwB,IAAAA,CAAExB,UAAU,qDAA4C,kBAGzD,UAACwB,IAAAA,UAAGX,EAAKY,KAAK,MAEhB,WAACvB,MAAAA,CAAIF,UAAU,sBACb,UAACwB,IAAAA,CAAExB,UAAU,qDAA4C,SACzD,UAACwB,IAAAA,CAAExB,UAAU,sBAAca,EAAKa,IAAI,CAACC,OAAO,CAAC,IAAK,UAEnDd,EAAKe,aAAa,EAAI,WAAC1B,MAAAA,CAAIF,UAAU,sBAClC,UAACwB,IAAAA,CAAExB,UAAU,qDAA4C,mBAGzD,UAACwB,IAAAA,UAAGX,EAAKe,aAAa,SAG5B,UAAC1B,MAAAA,CAAIF,UAAU,iCACb,UAAC6B,EAAAA,CAAMA,CAAAA,CAACC,QAASf,EAASgB,QAAQ,cAAcd,sBAAoB,SAASX,0BAAwB,iCAAwB,wBAhD9H,UAACJ,MAAAA,UAAI,wCAuDhB,0BCvEA,oVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,iBACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAAmL,CAuBvM,kJAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EAEA,CAAO,CACP,CACA,QAzCA,IAAsB,uCAA4J,CAyClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EAEA,CAAO,CACP,CACA,QA1DA,IAAsB,sCAAiJ,CA0DvK,gHACA,gBA1DA,IAAsB,uCAAuJ,CA0D7K,sHACA,aA1DA,IAAsB,uCAAoJ,CA0D1K,mHACA,WA1DA,IAAsB,4CAAgF,CA0DtG,+CACA,cA1DA,IAAsB,4CAAmF,CA0DzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,qJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,8CACA,6CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,oCElFG,sBAAsB,gMDbb8B,EAAqB,CAChCC,KAAAA,CAAO,wBACPC,WAAAA,CAAa,6BACf,EACe,eAAeC,EAAgB,UAC5CC,CAAQ,CAGT,CAJ6BD,CAM5B,IAAME,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkCI,KAAAA,GAAU,OAChE,MAAOC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAK1B,qBAAAA,CAAoB,OAAOZ,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAsC,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBN,WAAAA,CAAaA,EAAatB,SAAbsB,YAAatB,CAAoB,kBAAkBX,yBAAAA,CAAwB,uBACvGoC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACI,EAAAA,OAAAA,CAAAA,CAAW7B,qBAAAA,CAAoB,aAAaX,yBAAAA,CAAwB,eACrEsC,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAa9B,qBAAAA,CAAoB,eAAeX,yBAAAA,CAAwB,uBACvEoC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACM,EAAAA,OAAAA,CAAAA,CAAO/B,qBAAAA,CAAoB,SAASX,yBAAAA,CAAwB,eAE7DoC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,MAAAA,CAAAA,CAAKjD,SAAAA,CAAU,kDACboC,QAAAA,CAAAA,WAMb,CCvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZc,EAO8B,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CACrC,IAAI,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,EAOF,KAhBkB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,uHDmBI,sBAAsB,sJElBblB,EAAW,CACtBC,KADWD,CACJ,qBACT,EACe,eAAemB,IAAAA,GAAAA,GACrBT,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAAC9B,EAAAA,OAAAA,CAAAA,CAAgBK,qBAAAA,CAAoB,kBAAkBZ,uBAAAA,CAAsB,OAAOC,yBAAAA,CAAwB,YACrH,CFEA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EAAyB,IAAI,KAAK,CAPZ4C,EAO8B,CAClD,CARiD,IAQ5C,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,mCAAmC,CACnD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAKC,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IGhF9B,qDCAA,4DCAA,uDCAA,2DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,wBCNjN,wDCAA,sDCAA,iDCAA,2DCAA,2DCAA,iDCAA,wDCAA,6DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/?5a61", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/?2a72", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/./src/features/profile/components/profile-view-page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/?e100", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/dashboard/profile/[[...profile]]/page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\profile\\\\components\\\\profile-view-page.tsx\");\n", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\profile\\\\components\\\\profile-view-page.tsx\");\n", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "'use client';\n\nimport { useAuth } from '@/hooks/use-auth';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nexport default function ProfileViewPage() {\n  const {\n    user,\n    loading,\n    signOut\n  } = useAuth();\n  if (loading) {\n    return <div>Loading...</div>;\n  }\n  if (!user) {\n    return <div>Please sign in to view your profile.</div>;\n  }\n  return <div className='space-y-6 p-4 sm:p-6 lg:p-8' data-sentry-component=\"ProfileViewPage\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n          <div className='flex flex-col items-center gap-4 text-center sm:flex-row sm:text-left'>\r\n            <Avatar className='h-24 w-24 text-3xl' data-sentry-element=\"Avatar\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n              <AvatarImage src={`https://ui-avatars.com/api/?name=${user.name}&background=random`} data-sentry-element=\"AvatarImage\" data-sentry-source-file=\"profile-view-page.tsx\" />\r\n              <AvatarFallback data-sentry-element=\"AvatarFallback\" data-sentry-source-file=\"profile-view-page.tsx\">{user.name.charAt(0)}</AvatarFallback>\r\n            </Avatar>\r\n            <div className='space-y-1'>\r\n              <CardTitle className='text-2xl' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"profile-view-page.tsx\">{user.name}</CardTitle>\r\n              <p className='text-muted-foreground'>{user.email}</p>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n      </Card>\r\n\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"profile-view-page.tsx\">Account Details</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className='space-y-4' data-sentry-element=\"CardContent\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>\r\n            <div className='space-y-1'>\r\n              <p className='text-muted-foreground text-sm font-medium'>\r\n                Full Name\r\n              </p>\r\n              <p>{user.name}</p>\r\n            </div>\r\n            <div className='space-y-1'>\r\n              <p className='text-muted-foreground text-sm font-medium'>\r\n                Email address\r\n              </p>\r\n              <p>{user.email}</p>\r\n            </div>\r\n            <div className='space-y-1'>\r\n              <p className='text-muted-foreground text-sm font-medium'>Role</p>\r\n              <p className='capitalize'>{user.role.replace('_', ' ')}</p>\r\n            </div>\r\n            {user.institutionId && <div className='space-y-1'>\r\n                <p className='text-muted-foreground text-sm font-medium'>\r\n                  Institution ID\r\n                </p>\r\n                <p>{user.institutionId}</p>\r\n              </div>}\r\n          </div>\r\n          <div className='flex justify-end pt-4'>\r\n            <Button onClick={signOut} variant='destructive' data-sentry-element=\"Button\" data-sentry-source-file=\"profile-view-page.tsx\">\r\n              Sign Out\r\n            </Button>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "module.exports = require(\"node:util\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\[[...profile]]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'profile',\n        {\n        children: [\n        '[[...profile]]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\[[...profile]]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\profile\\\\[[...profile]]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/profile/[[...profile]]/page\",\n        pathname: \"/dashboard/profile/[[...profile]]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import ProfileViewPage from '@/features/profile/components/profile-view-page';\nexport const metadata = {\n  title: 'Dashboard : Profile'\n};\nexport default async function Page() {\n  return <ProfileViewPage data-sentry-element=\"ProfileViewPage\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\" />;\n}", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["Card", "className", "props", "div", "data-slot", "cn", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "ProfileViewPage", "user", "loading", "signOut", "useAuth", "data-sentry-element", "Avatar", "AvatarImage", "src", "name", "AvatarFallback", "char<PERSON>t", "p", "email", "role", "replace", "institutionId", "<PERSON><PERSON>", "onClick", "variant", "metadata", "title", "description", "DashboardLayout", "children", "cookieStore", "cookies", "defaultOpen", "get", "value", "_jsx", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "serverComponentModule.default", "Page"], "sourceRoot": ""}