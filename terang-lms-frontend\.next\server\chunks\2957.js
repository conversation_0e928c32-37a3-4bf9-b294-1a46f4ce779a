try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="e2867075-7536-4db6-9490-06ce819d149e",e._sentryDebugIdIdentifier="sentry-dbid-e2867075-7536-4db6-9490-06ce819d149e")}catch(e){}exports.id=2957,exports.ids=[2957],exports.modules={18621:(e,t,d)=>{"use strict";d.d(t,{db:()=>l,ll:()=>i});var s=d(138),n=d(41617),r=d(32230);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let i=(0,s.lw)(process.env.DATABASE_URL),l=(0,n.fd)(i,{schema:r})},32230:(e,t,d)=>{"use strict";d.r(t),d.d(t,{billingCycleEnum:()=>y,chapters:()=>x,chaptersRelations:()=>W,classEnrollments:()=>S,classEnrollmentsRelations:()=>ed,classes:()=>w,classesRelations:()=>j,courseAcademics:()=>z,courseAcademicsRelations:()=>k,courseAdmissions:()=>K,courseAdmissionsRelations:()=>U,courseCareers:()=>P,courseCareersRelations:()=>O,courseEnrollments:()=>D,courseEnrollmentsRelations:()=>et,courseStudentExperience:()=>R,courseStudentExperienceRelations:()=>B,courseTuitionAndFinancing:()=>b,courseTuitionAndFinancingRelations:()=>F,courseTypeEnum:()=>E,courses:()=>g,coursesRelations:()=>H,enrollmentTypeEnum:()=>I,institutionTypeEnum:()=>m,institutions:()=>q,institutionsRelations:()=>G,modules:()=>X,modulesRelations:()=>V,paymentStatusEnum:()=>v,questionTypeEnum:()=>h,questions:()=>L,questionsRelations:()=>Z,quizAttempts:()=>C,quizAttemptsRelations:()=>$,quizzes:()=>T,quizzesRelations:()=>Y,studentEnrollments:()=>J,studentEnrollmentsRelations:()=>ee,studentProgress:()=>M,studentProgressRelations:()=>es,subscriptionPlanEnum:()=>N,userRoleEnum:()=>_,users:()=>A,usersRelations:()=>Q});var s=d(70690),n=d(80509),r=d(46128),i=d(96588),l=d(61047),o=d(95064),u=d(5710),a=d(55487),c=d(58878),p=d(39993),f=d(26718);let _=(0,n.rL)("user_role",["student","teacher","super_admin"]),m=(0,n.rL)("institution_type",["sd-negeri","sd-swasta","smp-negeri","smp-swasta","sma-negeri","sma-swasta","university-negeri","university-swasta","institution-training","institution-course","institution-other"]),N=(0,n.rL)("subscription_plan",["basic","pro","enterprise"]),y=(0,n.rL)("billing_cycle",["monthly","yearly"]),v=(0,n.rL)("payment_status",["paid","unpaid"]),E=(0,n.rL)("course_type",["self_paced","verified"]),I=(0,n.rL)("enrollment_type",["code","invitation","both","purchase"]),h=(0,n.rL)("question_type",["multiple_choice","true_false","essay"]),A=(0,r.cJ)("users",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),email:(0,l.yf)("email",{length:255}).notNull().unique(),password:(0,l.yf)("password",{length:255}).notNull(),role:_("role").notNull().default("student"),institutionId:(0,o.nd)("institution_id").references(()=>q.id),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),q=(0,r.cJ)("institutions",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),type:m("type").notNull(),subscriptionPlan:N("subscription_plan").notNull().default("basic"),billingCycle:y("billing_cycle").notNull().default("monthly"),paymentStatus:v("payment_status").notNull().default("unpaid"),paymentDueDate:(0,u.vE)("payment_due_date"),studentCount:(0,o.nd)("student_count").default(0),teacherCount:(0,o.nd)("teacher_count").default(0),coverPicture:(0,a.Qq)("cover_picture"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),w=(0,r.cJ)("classes",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),description:(0,a.Qq)("description"),institutionId:(0,o.nd)("institution_id").references(()=>q.id).notNull(),teacherId:(0,o.nd)("teacher_id").references(()=>A.id).notNull(),coverPicture:(0,a.Qq)("cover_picture"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),g=(0,r.cJ)("courses",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),description:(0,a.Qq)("description"),instructor:(0,l.yf)("instructor",{length:255}),type:E("type").notNull().default("self_paced"),enrollmentType:I("enrollment_type").notNull().default("code"),isPurchasable:(0,c.zM)("is_purchasable").default(!1),price:(0,p._)("price",{precision:10,scale:2}),currency:(0,l.yf)("currency",{length:10}).default("IDR"),previewMode:(0,c.zM)("preview_mode").default(!1),startDate:(0,u.vE)("start_date"),endDate:(0,u.vE)("end_date"),teacherId:(0,o.nd)("teacher_id").references(()=>A.id).notNull(),institutionId:(0,o.nd)("institution_id").references(()=>q.id).notNull(),courseCode:(0,l.yf)("course_code",{length:50}).unique(),coverPicture:(0,a.Qq)("cover_picture"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),K=(0,r.cJ)("course_admissions",{id:(0,i.vX)("id").primaryKey(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),requirements:(0,f.Pq)("requirements"),applicationDeadline:(0,l.yf)("application_deadline",{length:255}),prerequisites:(0,f.Pq)("prerequisites"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),z=(0,r.cJ)("course_academics",{id:(0,i.vX)("id").primaryKey(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),credits:(0,o.nd)("credits"),workload:(0,l.yf)("workload",{length:255}),assessment:(0,f.Pq)("assessment"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),b=(0,r.cJ)("course_tuition_financing",{id:(0,i.vX)("id").primaryKey(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),totalCost:(0,p._)("total_cost",{precision:10,scale:2}),paymentOptions:(0,f.Pq)("payment_options"),scholarships:(0,f.Pq)("scholarships"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),P=(0,r.cJ)("course_careers",{id:(0,i.vX)("id").primaryKey(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),outcomes:(0,f.Pq)("outcomes"),industries:(0,f.Pq)("industries"),averageSalary:(0,l.yf)("average_salary",{length:255}),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),R=(0,r.cJ)("course_student_experience",{id:(0,i.vX)("id").primaryKey(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),testimonials:(0,f.Pq)("testimonials"),facilities:(0,f.Pq)("facilities"),support:(0,f.Pq)("support"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),D=(0,r.cJ)("course_enrollments",{id:(0,i.vX)("id").primaryKey(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),classId:(0,o.nd)("class_id").references(()=>w.id).notNull(),enrolledAt:(0,u.vE)("enrolled_at").defaultNow().notNull()}),J=(0,r.cJ)("student_enrollments",{id:(0,i.vX)("id").primaryKey(),studentId:(0,o.nd)("student_id").references(()=>A.id).notNull(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),enrolledAt:(0,u.vE)("enrolled_at").defaultNow().notNull(),completedAt:(0,u.vE)("completed_at"),finalScore:(0,p._)("final_score",{precision:5,scale:2}),certificateGenerated:(0,c.zM)("certificate_generated").default(!1)}),X=(0,r.cJ)("modules",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),description:(0,a.Qq)("description"),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),orderIndex:(0,o.nd)("order_index").notNull(),startDate:(0,u.vE)("start_date"),endDate:(0,u.vE)("end_date"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),x=(0,r.cJ)("chapters",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),content:(0,f.Pq)("content"),moduleId:(0,o.nd)("module_id").references(()=>X.id).notNull(),orderIndex:(0,o.nd)("order_index").notNull(),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),T=(0,r.cJ)("quizzes",{id:(0,i.vX)("id").primaryKey(),name:(0,l.yf)("name",{length:255}).notNull(),description:(0,a.Qq)("description"),chapterId:(0,o.nd)("chapter_id").references(()=>x.id),moduleId:(0,o.nd)("module_id").references(()=>X.id),courseId:(0,o.nd)("course_id").references(()=>g.id),quizType:(0,l.yf)("quiz_type",{length:50}).notNull().default("chapter"),minimumScore:(0,p._)("minimum_score",{precision:5,scale:2}).notNull().default("70"),timeLimit:(0,o.nd)("time_limit"),startDate:(0,u.vE)("start_date"),endDate:(0,u.vE)("end_date"),isActive:(0,c.zM)("is_active").default(!0),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),L=(0,r.cJ)("questions",{id:(0,i.vX)("id").primaryKey(),quizId:(0,o.nd)("quiz_id").references(()=>T.id).notNull(),type:h("type").notNull(),question:(0,f.Pq)("question").notNull(),options:(0,f.Pq)("options"),essayAnswer:(0,a.Qq)("essay_answer"),explanation:(0,f.Pq)("explanation"),points:(0,p._)("points",{precision:5,scale:2}).notNull().default("1"),orderIndex:(0,o.nd)("order_index").notNull(),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),C=(0,r.cJ)("quiz_attempts",{id:(0,i.vX)("id").primaryKey(),studentId:(0,o.nd)("student_id").references(()=>A.id).notNull(),quizId:(0,o.nd)("quiz_id").references(()=>T.id).notNull(),score:(0,p._)("score",{precision:5,scale:2}),totalPoints:(0,p._)("total_points",{precision:5,scale:2}),passed:(0,c.zM)("passed").default(!1),startedAt:(0,u.vE)("started_at").defaultNow().notNull(),completedAt:(0,u.vE)("completed_at"),answers:(0,f.Pq)("answers"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),S=(0,r.cJ)("class_enrollments",{id:(0,i.vX)("id").primaryKey(),studentId:(0,o.nd)("student_id").references(()=>A.id).notNull(),classId:(0,o.nd)("class_id").references(()=>w.id).notNull(),enrolledAt:(0,u.vE)("enrolled_at").defaultNow().notNull(),status:(0,l.yf)("status",{length:20}).default("active").notNull(),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),M=(0,r.cJ)("student_progress",{id:(0,i.vX)("id").primaryKey(),studentId:(0,o.nd)("student_id").references(()=>A.id).notNull(),courseId:(0,o.nd)("course_id").references(()=>g.id).notNull(),moduleId:(0,o.nd)("module_id").references(()=>X.id),chapterId:(0,o.nd)("chapter_id").references(()=>x.id),completed:(0,c.zM)("completed").default(!1),completedAt:(0,u.vE)("completed_at"),createdAt:(0,u.vE)("created_at").defaultNow().notNull(),updatedAt:(0,u.vE)("updated_at").defaultNow().notNull()}),Q=(0,s.K1)(A,({one:e,many:t})=>({institution:e(q,{fields:[A.institutionId],references:[q.id]}),teachingClasses:t(w),teachingCourses:t(g),studentEnrollments:t(J),classEnrollments:t(S),quizAttempts:t(C),progress:t(M)})),U=(0,s.K1)(K,({one:e})=>({course:e(g,{fields:[K.courseId],references:[g.id]})})),k=(0,s.K1)(z,({one:e})=>({course:e(g,{fields:[z.courseId],references:[g.id]})})),F=(0,s.K1)(b,({one:e})=>({course:e(g,{fields:[b.courseId],references:[g.id]})})),O=(0,s.K1)(P,({one:e})=>({course:e(g,{fields:[P.courseId],references:[g.id]})})),B=(0,s.K1)(R,({one:e})=>({course:e(g,{fields:[R.courseId],references:[g.id]})})),G=(0,s.K1)(q,({many:e})=>({users:e(A),classes:e(w),courses:e(g)})),j=(0,s.K1)(w,({one:e,many:t})=>({institution:e(q,{fields:[w.institutionId],references:[q.id]}),teacher:e(A,{fields:[w.teacherId],references:[A.id]}),courseEnrollments:t(D),classEnrollments:t(S)})),H=(0,s.K1)(g,({one:e,many:t})=>({teacher:e(A,{fields:[g.teacherId],references:[A.id]}),institution:e(q,{fields:[g.institutionId],references:[q.id]}),modules:t(X),courseEnrollments:t(D),studentEnrollments:t(J),quizzes:t(T),progress:t(M),admissions:t(K),academics:t(z),tuitionAndFinancing:t(b),careers:t(P),studentExperience:t(R)})),V=(0,s.K1)(X,({one:e,many:t})=>({course:e(g,{fields:[X.courseId],references:[g.id]}),chapters:t(x),quizzes:t(T),progress:t(M)})),W=(0,s.K1)(x,({one:e,many:t})=>({module:e(X,{fields:[x.moduleId],references:[X.id]}),quizzes:t(T),progress:t(M)})),Y=(0,s.K1)(T,({one:e,many:t})=>({chapter:e(x,{fields:[T.chapterId],references:[x.id]}),module:e(X,{fields:[T.moduleId],references:[X.id]}),course:e(g,{fields:[T.courseId],references:[g.id]}),questions:t(L),attempts:t(C)})),Z=(0,s.K1)(L,({one:e})=>({quiz:e(T,{fields:[L.quizId],references:[T.id]})})),$=(0,s.K1)(C,({one:e})=>({student:e(A,{fields:[C.studentId],references:[A.id]}),quiz:e(T,{fields:[C.quizId],references:[T.id]})})),ee=(0,s.K1)(J,({one:e})=>({student:e(A,{fields:[J.studentId],references:[A.id]}),course:e(g,{fields:[J.courseId],references:[g.id]})})),et=(0,s.K1)(D,({one:e})=>({course:e(g,{fields:[D.courseId],references:[g.id]}),class:e(w,{fields:[D.classId],references:[w.id]})})),ed=(0,s.K1)(S,({one:e})=>({student:e(A,{fields:[S.studentId],references:[A.id]}),class:e(w,{fields:[S.classId],references:[w.id]})})),es=(0,s.K1)(M,({one:e})=>({student:e(A,{fields:[M.studentId],references:[A.id]}),course:e(g,{fields:[M.courseId],references:[g.id]}),module:e(X,{fields:[M.moduleId],references:[X.id]}),chapter:e(x,{fields:[M.chapterId],references:[x.id]})}))},44725:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=44725,e.exports=t},78335:()=>{},96487:()=>{}};
//# sourceMappingURL=2957.js.map