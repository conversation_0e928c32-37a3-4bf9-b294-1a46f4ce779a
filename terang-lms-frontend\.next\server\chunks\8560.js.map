{"version": 3, "file": "8560.js", "mappings": "ujCCmBI,sBAAsB,gKDhBbA,EAAqB,CAChCC,KAAAA,CAAO,eACPC,WAAAA,CAAa,0CACf,ECEM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,SDjBbC,CCiBA,KAA4B,IDhBlDC,CAAQ,CAGT,EACC,EALsBD,IAKfE,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,kBAAAA,CAAAA,CAAmBC,qBAAAA,CAAoB,qBAAqBC,uBAAAA,CAAsB,mBAAmBC,yBAAAA,CAAwB,aACjI,SAAAJ,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACK,EAAAA,OAAAA,CAAAA,CAAKH,qBAAAA,CAAoB,OAAOE,yBAAAA,CAAwB,aAAcL,QAAAA,CAAAA,KAE7E,ECSsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAExE,CAAE,KAAM,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,gBAAgB,CAChC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,CAEjB,OAAO,EACf,CAAO,CAFc,CAEZ,KAAK,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,2FC9E9B,IAAMO,EAAaC,EAAAA,UAAgB,CAAC,CAAC,QACnCC,CAAM,QACNC,CAAM,qBACNC,CAAmB,CAKpB,CAAEC,KACD,IAAMC,EAAYL,EAAAA,OAAa,CAAC,KAC9B,GAAI,CAACG,EAAqB,OAAOF,EAAOI,SAAS,CACjD,IAAMC,EAAQL,EAAOI,SAAS,CAACE,SAAS,CAACC,GAAYA,EAASC,EAAE,GAAKN,GACrE,OAAOF,EAAOI,SAAS,CAACK,KAAK,CAACJ,EAAQ,EACxC,EAAG,CAACL,EAAOI,SAAS,CAAEF,EAAoB,EAC1C,MAAO,WAACQ,MAAAA,CAAIP,IAAKA,EAAKQ,UAAW,CAAC,wEAAwE,CAAC,WACpGV,GAAU,UAACS,MAAAA,CAAIF,GAAG,mBAAmBG,UAAU,oEAChD,WAACD,MAAAA,CAAIC,UAAU,kDACZX,EAAOY,IAAI,EAAIZ,EAAOY,IAAI,CAC3B,WAACF,MAAAA,CAAIC,UAAU,0BACb,WAACD,MAAAA,WACEN,EAAUS,MAAM,CAAG,GAAKT,EAAUU,GAAG,CAACP,GAAY,WAACR,EAAAA,QAAc,YAC5D,UAACgB,OAAAA,CAAKJ,UAAU,sCACbJ,EAASS,IAAI,GAEhB,UAACD,OAAAA,CAAKJ,UAAU,gBAAO,QAJ2CJ,EAASC,EAAE,GAMnF,UAACO,OAAAA,UAAMf,EAAOgB,IAAI,MAEnBhB,EAAOiB,QAAQ,EAAI,UAACF,OAAAA,CAAKJ,UAAU,yCAC/BX,EAAOiB,QAAQ,SAIvBjB,EAAOkB,QAAQ,EAAEL,OAAS,UAACH,MAAAA,CAAIC,UAAU,kDACrCX,EAAOkB,QAAQ,CAACJ,GAAG,CAAC,CAACK,EAAIC,IAAM,UAACC,MAAAA,CAAiBV,UAAU,iGACvDQ,GADqCA,EAAKC,MAGxC,OAEnB,GCvCe,SAASE,IACtB,GAAM,SACJC,CAAO,cACPC,CAAY,CACb,CAAGC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,GACd,MAAO,UAACC,EAAAA,WAAWA,CAAAA,CAACC,MAAOJ,EAASK,SAAU,CAAC,MAC7CC,CAAI,QACJ5B,CAAM,CACP,GAAqB,UAAhB,OAAO4B,EAAoB,UAACnB,MAAAA,CAAIC,UAAU,0EACrCkB,IACM,UAAC/B,EAAUA,CAACE,OAAQ6B,CAAT/B,CAAeG,OAAQA,EAAQC,oBAAqBsB,GAAgB,KAAQ9B,sBAAoB,cAAcC,wBAAsB,gBAAgBC,0BAAwB,qBAC1M,CD6BAE,EAAWgC,WAAW,CAAG,gCEbzB,MA3B0B,KACxB,GAAM,CACJC,OAAK,CACLC,IAwBWC,MAxBH,CACT,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,EAuBkBD,CAF9BE,CAE+B,EAF/BA,EAAAA,kBAAAA,CAAkBA,CAACC,CAjBE,CACnB5B,GAAI,cACJQ,KAAM,eACNE,SAAU,CAAC,IAAK,IAAI,CACpBmB,QAAS,QACTC,QARkB,CAQTC,IAPTP,EAAmB,YAAU,OAAS,QACxC,CAOA,EAAG,CACDxB,GAAI,gBACJQ,KAAM,kBACNqB,QAAS,QACTC,QAAS,IAAMN,EAAS,QAC1B,EAAG,CACDxB,GAAI,eACJQ,KAAM,iBACNqB,QAAS,QACTC,QAAS,IAAMN,EAAS,OAC1B,EAAE,CAC8B,CAACD,EAAM,CACzC,ECpBe,SAASlC,EAAK,UAC3BN,CAAQ,CAGT,EACC,IAAMiD,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAGlBC,EAAUC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAEtB,IAAMC,EAAa,IACjBJ,EAAOK,IAAI,CAACC,EACd,EACA,OAAOC,EAAAA,EAAQA,CAACC,OAAO,CAACC,IAEtB,IAAMC,EAA6B,MAAhBD,EAAQH,GAAG,CAAW,CACvCtC,GAAI,GAAGyC,EAAQ7D,KAAK,CAAC+D,WAAW,GAAG,MAAM,CAAC,CAC1CnC,KAAMiC,EAAQ7D,KAAK,CACnB8B,SAAU+B,EAAQ/B,QAAQ,CAC1BkC,SAAUH,EAAQ7D,KAAK,CAAC+D,WAAW,GACnCd,QAAS,aACTpB,SAAU,CAAC,MAAM,EAAEgC,EAAQ7D,KAAK,EAAE,CAClCkD,QAAS,IAAMM,EAAWK,EAAQH,GAAG,CACvC,EAAI,KAGEO,EAAeJ,EAAQtB,KAAK,EAAEb,IAAIwC,GAAc,EACpD9C,GAAI,GAAG8C,CAD6C,CACnClE,KAAK,CAAC+D,WAAW,GAAG,MAAM,CAAC,CAC5CnC,KAAMsC,EAAUlE,KAAK,CACrB8B,SAAUoC,EAAUpC,QAAQ,CAC5BkC,SAAUE,EAAUlE,KAAK,CAAC+D,WAAW,GACrCd,QAASY,EAAQ7D,KAAK,CACtB6B,SAAU,CAAC,MAAM,EAAEqC,EAAUlE,KAAK,EAAE,CACpCkD,QAAS,IAAMM,EAAWU,EAAUR,GAAG,EACzC,IAAO,EAAE,CAGT,OAAOI,EAAa,CAACA,KAAeG,EAAa,CAAGA,CACtD,EACF,EAAG,CAACb,EAAO,EACX,MAAO,UAACe,EAAAA,YAAYA,CAAAA,CAACb,QAASA,EAAShD,sBAAoB,eAAeC,wBAAsB,OAAOC,0BAAwB,qBAC3H,UAAC4D,EAAAA,CAAc9D,sBAAoB,gBAAgBE,0BAAwB,qBAAaL,KAE9F,CACA,IAAMiE,EAAgB,CAAC,UACrBjE,CAAQ,CAGT,IACC0C,IACO,eADUA,MACV,YACH,UAACwB,EAAAA,UAAUA,CAAAA,CAAC/D,sBAAoB,aAAaE,0BAAwB,qBACnE,UAAC8D,EAAAA,cAAcA,CAAAA,CAAC/C,UAAU,+DAA+DjB,sBAAoB,iBAAiBE,0BAAwB,qBACpJ,WAAC+D,EAAAA,YAAYA,CAAAA,CAAChD,UAAU,iIAAiIjB,sBAAoB,eAAeE,0BAAwB,sBAClN,UAACc,MAAAA,CAAIC,UAAU,4DACb,UAACiD,EAAAA,UAAUA,CAAAA,CAACjD,UAAU,oHAAoHjB,sBAAoB,aAAaE,0BAAwB,gBAErM,UAACc,MAAAA,CAAIC,UAAU,yBACb,UAACW,EAAaA,CAAC5B,UAAD4B,YAAqB,gBAAgB1B,0BAAwB,uBAKlFL,w/BCjEwC,MAAQ,cAAC,6BAA6B,+FAA+F,IAAyB,iBCN7M,sCAA0L,CAE1L,uCAA0M,gDCI3J,MAAQ,cAAC,yBAAyB,6CAA6C,WAAW,0MAA0M,WAAW,4KAA4K,WAAW,8KAA8K,WAAW,4VAA4V,IAAyB,4GCoBnkC,IAAMsE,EAAoBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,MAAoCC,GAC9DC,EAAgB,KAC3B,IAAMC,EAAUC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACL,GAC3B,GAAI,CAACI,EACH,MAAM,CADM,KACI,2DAElB,OAAOA,CACT,EAIaxE,EAAwD,CAAC,UACpEF,CAAQ,CACT,IACC,GAAM,CAAC4E,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASG,EAAAA,EAAkBA,EACjE,CAACC,EAAiBC,EAAmB,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7DM,EAAc,sBACdC,EAAuB,+BAI7BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACkB,MACxB,GAAI,CAEF,IAAMC,EAAiBC,aAAaC,OAAO,CAACJ,GAC5C,GAAIE,EAAgB,CAClB,IAAMG,EAAuCC,KAAKC,KAAK,CAACL,EAIpDM,CAHQC,KAAKD,GAAG,GAGVH,EAAaK,cAAc,EAAE,EAClBL,EAAaR,eAAe,EAC/CL,EAAca,EAAaR,eAAe,CAAC5D,MAAM,CAAG,GAChDoE,EAAaR,eAAe,CAAC5D,MAAM,CAAG,GAAG,EAC7BoE,EAAaR,eAAe,CAAC,EAAE,GAAG,aAIrCc,UAAU,CAACX,GAE1B,GANkF,GAOpF,CAGA,IAAMY,EAAST,aAAaC,OAAO,CAACL,GACpC,GAAIa,EAAQ,CACV,IAAMC,EAAiCP,KAAKC,KAAK,CAACK,GAIlD,GAHYH,KAAKD,GAAG,GAGVK,EAAeH,cAAc,CAAE,CACvClB,EAAcqB,EAAetB,UAAU,EACvCI,EAAckB,EAAenB,UAAU,EACvCI,EAAmB,CAACe,EAAenB,UAAU,CAAC,EAG9C,IAAMW,EAAuC,CAC3CR,gBAAiB,CAACgB,EAAenB,UAAU,CAAC,CAC5CoB,oBAAqBD,EAAeC,mBAAmB,CACvDJ,eAAgBG,EAAeH,cAAc,EAE/CP,aAAaY,OAAO,CAACf,EAAsBM,KAAKU,SAAS,CAACX,IAC1DF,aAAaQ,UAAU,CAACZ,EAC1B,MAEEI,CAFK,KADiC,OAGzBQ,UAAU,CAACZ,EAHkC,CAMhE,CAAE,MAAOkB,EAAO,CACdC,QAAQD,KAAK,CAAC,kCAAmCA,GACjDd,aAAaQ,UAAU,CAACZ,GACxBI,aAAaQ,UAAU,CAACX,EAC1B,EACF,GAEF,EAAG,EAAE,EAGL,IAAMmB,EAAwB,IAC5B,IAAMX,EAAMC,KAAKD,GAAG,GACpB,GAAI,CAEFV,EAAmBsB,IAYjB,IAVIC,EAUEhB,EAAuC,CAC3CR,eAAAA,CARAwB,CAQiBA,CAZOD,EAAKE,IAAI,CAACC,GAAKA,EAAE3F,EAAE,GAAK4F,EAAO5F,EAAE,EAIxCwF,EAAKlF,GAAG,CAACqF,GAAKA,EAAE3F,EAAE,GAAK4F,EAAO5F,EAAE,CAAG4F,EAASD,GAG5C,IAAIH,EAAMI,EAAO,CAMlCV,oBAAqBN,EACrBE,eAAgBF,KAClB,CADwBiB,CAGxB,OADAtB,aAAaY,OAAO,CAACf,EAAsBM,KAAKU,SAAS,CAACX,IACnDgB,CACT,GAGAK,WAAW,KACTvB,aAAaQ,UAAU,CAACX,GACxBR,GAAc,GACdM,EAAmB,EAAE,EACrBH,EAAcC,EAAAA,EAAkBA,CAClC,EA5FoB,CA4FjB6B,GACL,CA7F2B,KAAK,CA6FvBR,EAAO,CACdC,EA9FoC,MA8F5BD,KAAK,CAAC,iBA9FmD,oBA8FbA,EACtD,CACF,EAsDA,MAAO,UAAChC,EAAkB0C,QAAQ,EAACC,MAVrB,CAU4BA,WATxCrC,aACAG,EACAmC,eA9CqB,KACrBrC,GAAc,GACd,IAAMsC,EAAgB,CACpB,GAAGlC,EAAAA,EAAkB,CACrBmC,OAAQ,aACV,EACApC,EAAcmC,GACdX,EAAsBW,EACxB,EAuCEE,2BAtCiC,IACjCxC,GAAc,GACd,IAAMsC,EAAgB,CACpB,GAAGN,CAAM,CACTO,OAAQ,cACRE,cAAe,CACjB,EACAtC,EAAcmC,GACdX,EAAsBW,EACxB,EA8BEI,qBA7B2B,IAEvBxC,EAAW9D,EAAE,GAAKkG,EAAclG,EAAE,EAAE,EACxBkG,GAIhBhC,EAAmBsB,GAAQA,EAAKlF,GAAG,CAACsF,GAAUA,EAAO5F,EAAE,GAAKkG,EAAclG,EAAE,CAAGkG,EAAgBN,IAG3FjC,GACF4B,EAAsBW,EAE1B,KAHkB,aAoBhBjC,EACAsC,mBAfyB,GAClBtC,EAAgByB,IAAI,CAACE,GAAUA,EAAO5F,EAAE,GAAKwG,GAepDC,cAXoB,GACbxC,EAAgByC,IAAI,CAACd,GAAUA,EAAO5F,EAAE,GAAKwG,EAWtD,EACiDtH,sBAAoB,6BAA6BC,wBAAsB,qBAAqBC,0BAAwB,kCAChKL,GAEP,EAAE,iBCtMF,sCAA0L,CAE1L,uCAA0M,0DCYnM,IAAM4H,EAAc,GAAiC,CAC1D,CACE/H,MAAO,oBACP0D,IAAK,WACLlC,KAAM,aACNwG,SAAUC,EAASC,UAAU,CAAC,YAC9BpG,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,CAAC,CAEZ,CACEvC,MAAO,aACP0D,IAAK,cACLlC,GALoD,EAK9C,gBACNwG,SAAUC,EAASC,UAAU,CAAC,eAC9BpG,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,CAAC,CAEb,CAAC,EAGiCwF,EAAY,IAAI", "sources": ["webpack://terang-lms-ui/src/app/(course-view)/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/./src/components/kbar/result-item.tsx", "webpack://terang-lms-ui/./src/components/kbar/render-result.tsx", "webpack://terang-lms-ui/./src/components/kbar/use-theme-switching.tsx", "webpack://terang-lms-ui/./src/components/kbar/index.tsx", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/arrow_left_01_icon.js", "webpack://terang-lms-ui/?9bf4", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/award_01_icon.js", "webpack://terang-lms-ui/./src/contexts/enrollment-context.tsx", "webpack://terang-lms-ui/?444e", "webpack://terang-lms-ui/./src/constants/data.ts"], "sourcesContent": ["import KBar from '@/components/kbar';\nimport { EnrollmentProvider } from '@/contexts/enrollment-context';\nimport type { Metadata } from 'next';\nexport const metadata: Metadata = {\n  title: 'Course - LMS',\n  description: 'Learning Management System - Course View'\n};\nexport default function CourseViewLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  return <EnrollmentProvider data-sentry-element=\"EnrollmentProvider\" data-sentry-component=\"CourseViewLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <KBar data-sentry-element=\"KBar\" data-sentry-source-file=\"layout.tsx\">{children}</KBar>\r\n    </EnrollmentProvider>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/(course-view)',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/(course-view)',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/(course-view)',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/(course-view)',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "import type { ActionId, ActionImpl } from 'kbar';\nimport * as React from 'react';\nconst ResultItem = React.forwardRef(({\n  action,\n  active,\n  currentRootActionId\n}: {\n  action: ActionImpl;\n  active: boolean;\n  currentRootActionId: ActionId;\n}, ref: React.Ref<HTMLDivElement>) => {\n  const ancestors = React.useMemo(() => {\n    if (!currentRootActionId) return action.ancestors;\n    const index = action.ancestors.findIndex(ancestor => ancestor.id === currentRootActionId);\n    return action.ancestors.slice(index + 1);\n  }, [action.ancestors, currentRootActionId]);\n  return <div ref={ref} className={`relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`}>\r\n        {active && <div id='kbar-result-item' className='border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4'></div>}\r\n        <div className='relative z-10 flex items-center gap-2'>\r\n          {action.icon && action.icon}\r\n          <div className='flex flex-col'>\r\n            <div>\r\n              {ancestors.length > 0 && ancestors.map(ancestor => <React.Fragment key={ancestor.id}>\r\n                    <span className='text-muted-foreground mr-2'>\r\n                      {ancestor.name}\r\n                    </span>\r\n                    <span className='mr-2'>&rsaquo;</span>\r\n                  </React.Fragment>)}\r\n              <span>{action.name}</span>\r\n            </div>\r\n            {action.subtitle && <span className='text-muted-foreground text-sm'>\r\n                {action.subtitle}\r\n              </span>}\r\n          </div>\r\n        </div>\r\n        {action.shortcut?.length ? <div className='relative z-10 grid grid-flow-col gap-1'>\r\n            {action.shortcut.map((sc, i) => <kbd key={sc + i} className='bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium'>\r\n                {sc}\r\n              </kbd>)}\r\n          </div> : null}\r\n      </div>;\n});\nResultItem.displayName = 'KBarResultItem';\nexport default ResultItem;", "import { KBarResults, useMatches } from 'kbar';\nimport ResultItem from './result-item';\nexport default function RenderResults() {\n  const {\n    results,\n    rootActionId\n  } = useMatches();\n  return <KBarResults items={results} onRender={({\n    item,\n    active\n  }) => typeof item === 'string' ? <div className='text-primary-foreground px-4 py-2 text-sm uppercase opacity-50'>\r\n            {item}\r\n          </div> : <ResultItem action={item} active={active} currentRootActionId={rootActionId ?? ''} />} data-sentry-element=\"KBarResults\" data-sentry-component=\"RenderResults\" data-sentry-source-file=\"render-result.tsx\" />;\n}", "import { useRegisterActions } from 'kbar';\nimport { useTheme } from 'next-themes';\nconst useThemeSwitching = () => {\n  const {\n    theme,\n    setTheme\n  } = useTheme();\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n  const themeAction = [{\n    id: 'toggleTheme',\n    name: 'Toggle Theme',\n    shortcut: ['t', 't'],\n    section: 'Theme',\n    perform: toggleTheme\n  }, {\n    id: 'setLightTheme',\n    name: 'Set Light Theme',\n    section: 'Theme',\n    perform: () => setTheme('light')\n  }, {\n    id: 'setDarkTheme',\n    name: 'Set Dark Theme',\n    section: 'Theme',\n    perform: () => setTheme('dark')\n  }];\n  useRegisterActions(themeAction, [theme]);\n};\nexport default useThemeSwitching;", "'use client';\n\nimport { navItems } from '@/constants/data';\nimport { KBarAnimator, KBarPortal, KBarPositioner, KBarProvider, KBarSearch } from 'kbar';\nimport { useRouter } from 'next/navigation';\nimport { useMemo } from 'react';\nimport RenderResults from './render-result';\nimport useThemeSwitching from './use-theme-switching';\nexport default function KBar({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const router = useRouter();\n\n  // These action are for the navigation\n  const actions = useMemo(() => {\n    // Define navigateTo inside the useMemo callback to avoid dependency array issues\n    const navigateTo = (url: string) => {\n      router.push(url);\n    };\n    return navItems.flatMap(navItem => {\n      // Only include base action if the navItem has a real URL and is not just a container\n      const baseAction = navItem.url !== '#' ? {\n        id: `${navItem.title.toLowerCase()}Action`,\n        name: navItem.title,\n        shortcut: navItem.shortcut,\n        keywords: navItem.title.toLowerCase(),\n        section: 'Navigation',\n        subtitle: `Go to ${navItem.title}`,\n        perform: () => navigateTo(navItem.url)\n      } : null;\n\n      // Map child items into actions\n      const childActions = navItem.items?.map(childItem => ({\n        id: `${childItem.title.toLowerCase()}Action`,\n        name: childItem.title,\n        shortcut: childItem.shortcut,\n        keywords: childItem.title.toLowerCase(),\n        section: navItem.title,\n        subtitle: `Go to ${childItem.title}`,\n        perform: () => navigateTo(childItem.url)\n      })) ?? [];\n\n      // Return only valid actions (ignoring null base actions for containers)\n      return baseAction ? [baseAction, ...childActions] : childActions;\n    });\n  }, [router]);\n  return <KBarProvider actions={actions} data-sentry-element=\"KBarProvider\" data-sentry-component=\"KBar\" data-sentry-source-file=\"index.tsx\">\r\n      <KBarComponent data-sentry-element=\"KBarComponent\" data-sentry-source-file=\"index.tsx\">{children}</KBarComponent>\r\n    </KBarProvider>;\n}\nconst KBarComponent = ({\n  children\n}: {\n  children: React.ReactNode;\n}) => {\n  useThemeSwitching();\n  return <>\r\n      <KBarPortal data-sentry-element=\"KBarPortal\" data-sentry-source-file=\"index.tsx\">\r\n        <KBarPositioner className='bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm' data-sentry-element=\"KBarPositioner\" data-sentry-source-file=\"index.tsx\">\r\n          <KBarAnimator className='bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg' data-sentry-element=\"KBarAnimator\" data-sentry-source-file=\"index.tsx\">\r\n            <div className='bg-card border-border sticky top-0 z-10 border-b'>\r\n              <KBarSearch className='bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden' data-sentry-element=\"KBarSearch\" data-sentry-source-file=\"index.tsx\" />\r\n            </div>\r\n            <div className='max-h-[400px]'>\r\n              <RenderResults data-sentry-element=\"RenderResults\" data-sentry-source-file=\"index.tsx\" />\r\n            </div>\r\n          </KBarAnimator>\r\n        </KBarPositioner>\r\n      </KBarPortal>\r\n      {children}\r\n    </>;\n};", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"ArrowLeft01Icon\",[[\"path\",{d:\"M15 6C15 6 9.00001 10.4189 9 12C8.99999 13.5812 15 18 15 18\",stroke:\"currentColor\",key:\"k0\"}]]);export{o as default};\n", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"EnrollmentProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\contexts\\\\enrollment-context.tsx\");\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const C=r(\"Award01Icon\",[[\"path\",{d:\"M12 12V18\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M12 18C10.3264 18 8.86971 19.012 8.11766 20.505C7.75846 21.218 8.27389 22 8.95877 22H15.0412C15.7261 22 16.2415 21.218 15.8823 20.505C15.1303 19.012 13.6736 18 12 18Z\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M5 5H3.98471C2.99819 5 2.50493 5 2.20017 5.37053C1.89541 5.74106 1.98478 6.15597 2.16352 6.9858C2.50494 8.57086 3.24548 9.9634 4.2489 11\",stroke:\"currentColor\",key:\"k2\"}],[\"path\",{d:\"M19 5H20.0153C21.0018 5 21.4951 5 21.7998 5.37053C22.1046 5.74106 22.0152 6.15597 21.8365 6.9858C21.4951 8.57086 20.7545 9.9634 19.7511 11\",stroke:\"currentColor\",key:\"k3\"}],[\"path\",{d:\"M12 12C15.866 12 19 8.8831 19 5.03821C19 4.93739 18.9978 4.83707 18.9936 4.73729C18.9509 3.73806 18.9295 3.23845 18.2523 2.61922C17.5751 2 16.8247 2 15.324 2H8.67596C7.17526 2 6.42492 2 5.74772 2.61922C5.07051 3.23844 5.04915 3.73806 5.00642 4.73729C5.00215 4.83707 5 4.93739 5 5.03821C5 8.8831 8.13401 12 12 12Z\",stroke:\"currentColor\",key:\"k4\"}]]);export{C as default};\n", "'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { architectureCourse } from '@/constants/shared-course-data';\nimport { Course } from '@/types/lms';\ninterface EnrollmentData {\n  isEnrolled: boolean;\n  courseData: Course;\n  enrollmentTimestamp: number;\n  expirationTime: number; // 10 minutes in milliseconds\n}\ninterface MultipleEnrollmentData {\n  enrolledCourses: Course[];\n  enrollmentTimestamp: number;\n  expirationTime: number; // 10 minutes in milliseconds\n}\ninterface EnrollmentContextType {\n  isEnrolled: boolean;\n  courseData: Course;\n  enrollInCourse: () => void;\n  enrollInCourseWithPurchase: (course: Course) => void;\n  updateCourseProgress: (updatedCourse: Course) => void;\n  enrolledCourses: Course[];\n  isEnrolledInCourse: (courseId: string) => boolean;\n  getCourseById: (courseId: string) => Course | undefined;\n}\nconst EnrollmentContext = createContext<EnrollmentContextType | undefined>(undefined);\nexport const useEnrollment = () => {\n  const context = useContext(EnrollmentContext);\n  if (!context) {\n    throw new Error('useEnrollment must be used within an EnrollmentProvider');\n  }\n  return context;\n};\ninterface EnrollmentProviderProps {\n  children: ReactNode;\n}\nexport const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({\n  children\n}) => {\n  const [isEnrolled, setIsEnrolled] = useState(false);\n  const [courseData, setCourseData] = useState<Course>(architectureCourse);\n  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);\n  const STORAGE_KEY = 'lms-enrollment-data';\n  const MULTIPLE_STORAGE_KEY = 'lms-multiple-enrollment-data';\n  const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds\n\n  // Load persisted data on component mount\n  useEffect(() => {\n    const loadPersistedData = () => {\n      try {\n        // Try to load multiple enrollment data first\n        const multipleStored = localStorage.getItem(MULTIPLE_STORAGE_KEY);\n        if (multipleStored) {\n          const multipleData: MultipleEnrollmentData = JSON.parse(multipleStored);\n          const now = Date.now();\n\n          // Check if enrollment has expired\n          if (now < multipleData.expirationTime) {\n            setEnrolledCourses(multipleData.enrolledCourses);\n            setIsEnrolled(multipleData.enrolledCourses.length > 0);\n            if (multipleData.enrolledCourses.length > 0) {\n              setCourseData(multipleData.enrolledCourses[0]); // Set first course as primary\n            }\n          } else {\n            // Clear expired data\n            localStorage.removeItem(MULTIPLE_STORAGE_KEY);\n          }\n          return;\n        }\n\n        // Fallback to old single enrollment data for backward compatibility\n        const stored = localStorage.getItem(STORAGE_KEY);\n        if (stored) {\n          const enrollmentData: EnrollmentData = JSON.parse(stored);\n          const now = Date.now();\n\n          // Check if enrollment has expired\n          if (now < enrollmentData.expirationTime) {\n            setIsEnrolled(enrollmentData.isEnrolled);\n            setCourseData(enrollmentData.courseData);\n            setEnrolledCourses([enrollmentData.courseData]);\n\n            // Migrate to new format\n            const multipleData: MultipleEnrollmentData = {\n              enrolledCourses: [enrollmentData.courseData],\n              enrollmentTimestamp: enrollmentData.enrollmentTimestamp,\n              expirationTime: enrollmentData.expirationTime\n            };\n            localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\n            localStorage.removeItem(STORAGE_KEY); // Remove old format\n          } else {\n            // Clear expired data\n            localStorage.removeItem(STORAGE_KEY);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to load enrollment data:', error);\n        localStorage.removeItem(STORAGE_KEY);\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\n      }\n    };\n    loadPersistedData();\n  }, []);\n\n  // Persist enrollment data to localStorage\n  const persistEnrollmentData = (course: Course) => {\n    const now = Date.now();\n    try {\n      // Update enrolled courses state\n      setEnrolledCourses(prev => {\n        const isAlreadyEnrolled = prev.some(c => c.id === course.id);\n        let updatedCourses;\n        if (isAlreadyEnrolled) {\n          // Update existing course\n          updatedCourses = prev.map(c => c.id === course.id ? course : c);\n        } else {\n          // Add new course\n          updatedCourses = [...prev, course];\n        }\n\n        // Save to localStorage with new format\n        const multipleData: MultipleEnrollmentData = {\n          enrolledCourses: updatedCourses,\n          enrollmentTimestamp: now,\n          expirationTime: now + EXPIRATION_TIME\n        };\n        localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\n        return updatedCourses;\n      });\n\n      // Set up automatic cleanup after expiration\n      setTimeout(() => {\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\n        setIsEnrolled(false);\n        setEnrolledCourses([]);\n        setCourseData(architectureCourse);\n      }, EXPIRATION_TIME);\n    } catch (error) {\n      console.error('Failed to persist enrollment data:', error);\n    }\n  };\n  const enrollInCourse = () => {\n    setIsEnrolled(true);\n    const updatedCourse = {\n      ...architectureCourse,\n      status: 'in-progress' as const\n    };\n    setCourseData(updatedCourse);\n    persistEnrollmentData(updatedCourse);\n  };\n  const enrollInCourseWithPurchase = (course: Course) => {\n    setIsEnrolled(true);\n    const updatedCourse = {\n      ...course,\n      status: 'in-progress' as const,\n      totalProgress: 0\n    };\n    setCourseData(updatedCourse);\n    persistEnrollmentData(updatedCourse);\n  };\n  const updateCourseProgress = (updatedCourse: Course) => {\n    // Update the primary courseData if it's the same course\n    if (courseData.id === updatedCourse.id) {\n      setCourseData(updatedCourse);\n    }\n\n    // Update the course in enrolledCourses array\n    setEnrolledCourses(prev => prev.map(course => course.id === updatedCourse.id ? updatedCourse : course));\n\n    // Update persisted data with new progress\n    if (isEnrolled) {\n      persistEnrollmentData(updatedCourse);\n    }\n  };\n\n  // Check if user is enrolled in a specific course\n  const isEnrolledInCourse = (courseId: string): boolean => {\n    return enrolledCourses.some(course => course.id === courseId);\n  };\n\n  // Get a specific course by ID\n  const getCourseById = (courseId: string): Course | undefined => {\n    return enrolledCourses.find(course => course.id === courseId);\n  };\n  const value = {\n    isEnrolled,\n    courseData,\n    enrollInCourse,\n    enrollInCourseWithPurchase,\n    updateCourseProgress,\n    enrolledCourses,\n    isEnrolledInCourse,\n    getCourseById\n  };\n  return <EnrollmentContext.Provider value={value} data-sentry-element=\"EnrollmentContext.Provider\" data-sentry-component=\"EnrollmentProvider\" data-sentry-source-file=\"enrollment-context.tsx\">\r\n      {children}\r\n    </EnrollmentContext.Provider>;\n};", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"EnrollmentProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\contexts\\\\enrollment-context.tsx\");\n", "import { NavItem } from '@/types';\r\n\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n//Info: The following data is used for the sidebar navigation and Cmd K bar.\r\nexport const getNavItems = (pathname: string): NavItem[] => [\r\n  {\r\n    title: 'Available Courses',\r\n    url: '/courses',\r\n    icon: 'searchList',\r\n    isActive: pathname.startsWith('/courses'),\r\n    shortcut: ['a', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  },\r\n  {\r\n    title: 'My Courses',\r\n    url: '/my-courses',\r\n    icon: 'graduationCap',\r\n    isActive: pathname.startsWith('/my-courses'),\r\n    shortcut: ['m', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  }\r\n];\r\n\r\n// Legacy export for backward compatibility\r\nexport const navItems: NavItem[] = getNavItems('');\r\n\r\nexport interface SaleUser {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  amount: string;\r\n  image: string;\r\n  initials: string;\r\n}\r\n\r\nexport const recentSalesData: SaleUser[] = [\r\n  {\r\n    id: 1,\r\n    name: '<PERSON>',\r\n    email: '<EMAIL>',\r\n    amount: '+$1,999.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/1.png',\r\n    initials: 'OM'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Jackson Lee',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/2.png',\r\n    initials: 'JL'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Isabella Nguyen',\r\n    email: '<EMAIL>',\r\n    amount: '+$299.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/3.png',\r\n    initials: 'IN'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'William Kim',\r\n    email: '<EMAIL>',\r\n    amount: '+$99.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/4.png',\r\n    initials: 'WK'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Sofia Davis',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/5.png',\r\n    initials: 'SD'\r\n  }\r\n];\r\n"], "names": ["metadata", "title", "description", "CourseViewLayout", "children", "_jsx", "EnrollmentProvider", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "KBar", "ResultItem", "React", "action", "active", "currentRootActionId", "ref", "ancestors", "index", "findIndex", "ancestor", "id", "slice", "div", "className", "icon", "length", "map", "span", "name", "subtitle", "shortcut", "sc", "i", "kbd", "RenderResults", "results", "rootActionId", "useMatches", "KBarResults", "items", "onRender", "item", "displayName", "theme", "setTheme", "useThemeSwitching", "useTheme", "useRegisterActions", "themeAction", "section", "perform", "toggleTheme", "router", "useRouter", "actions", "useMemo", "navigateTo", "push", "url", "navItems", "flatMap", "navItem", "baseAction", "toLowerCase", "keywords", "childActions", "childItem", "KBarProvider", "KBarComponent", "KBarPortal", "KBarPositioner", "KBarAnimator", "KBarSearch", "EnrollmentContext", "createContext", "undefined", "useEnrollment", "context", "useContext", "isEnrolled", "setIsEnrolled", "useState", "courseData", "setCourseData", "architectureCourse", "enrolledCourses", "setEnrolledCourses", "STORAGE_KEY", "MULTIPLE_STORAGE_KEY", "useEffect", "multipleStored", "localStorage", "getItem", "multipleData", "JSON", "parse", "now", "Date", "expirationTime", "removeItem", "stored", "enrollmentData", "enrollmentTimestamp", "setItem", "stringify", "error", "console", "persistEnrollmentData", "prev", "updatedCourses", "some", "c", "course", "EXPIRATION_TIME", "setTimeout", "Provider", "value", "enrollInCourse", "updatedCourse", "status", "enrollInCourseWithPurchase", "totalProgress", "updateCourseProgress", "isEnrolledInCourse", "courseId", "getCourseById", "find", "getNavItems", "isActive", "pathname", "startsWith"], "sourceRoot": ""}