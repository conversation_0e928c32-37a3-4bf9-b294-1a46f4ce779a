{"version": 3, "file": "../app/dashboard/admin/institutions/new/page.js", "mappings": "ubAAA,4GCAA,qDCAA,qGCAA,mECAA,0GCAA,mPCae,SAASA,IACtB,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,OACJC,CAAK,CACN,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACN,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrC,CAACC,EAAUC,EAAY,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACvCG,KAAM,GACNC,KAAM,GACNC,iBAAkB,QAClBC,aAAc,UACdC,aAAc,EACdC,aAAc,EACdC,cAAe,QACjB,GACMC,EAAe,MAAOC,IAC1BA,EAAEC,cAAc,GAChBb,GAAa,GACb,GAAI,CACF,IAAMc,EAAW,MAAMC,MAAM,oBAAqB,CAChDC,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMC,KAAKC,SAAS,CAAClB,EACvB,GACMmB,EAAO,MAAMP,EAASQ,IAAI,GAC5BD,EAAKE,OAAO,EAAE,EACV,CACJC,MAAO,UACPC,YAAa,kCACf,GACA9B,EAAO+B,IAAI,CAAC,kCAEZ7B,EAAM,CACJ2B,MAAO,QACPC,YAAaJ,EAAKM,KAAK,EAAI,+BAC3BC,QAAS,aACX,EAEJ,CAAE,MAAOD,EAAO,CACdE,QAAQF,KAAK,CAAC,8BAA+BA,GAC7C9B,EAAM,CACJ2B,MAAO,QACPC,YAAa,+BACbG,QAAS,aACX,EACF,QAAU,CACR5B,GAAa,EACf,CACF,EACM8B,EAAoB,CAACC,EAAeC,KACxC7B,EAAY8B,GAAS,EACnB,EADmB,CAChBA,CAAI,CACP,CAACF,EAAM,CAAEC,EACX,EACF,EACA,MAAO,WAACE,MAAAA,CAAIC,UAAU,YAAYC,wBAAsB,qBAAqBC,0BAAwB,qBACjG,WAACH,MAAAA,CAAIC,UAAU,wCACb,UAACG,IAAIA,CAACC,KAAK,gCAAgCC,OAAtCF,eAA0D,OAAOD,0BAAwB,oBAC5F,WAACI,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKF,sBAAoB,SAASH,0BAAwB,qBACvF,UAACM,EAAAA,CAASA,CAAAA,CAACR,UAAU,eAAeK,sBAAoB,YAAYH,0BAAwB,aAAa,YAI7G,WAACH,MAAAA,WACC,UAACU,KAAAA,CAAGT,UAAU,6CAAoC,wBAGlD,UAACU,IAAAA,CAAEV,UAAU,iCAAwB,+DAMzC,WAACW,EAAAA,EAAIA,CAAAA,CAACN,sBAAoB,OAAOH,0BAAwB,qBACvD,WAACU,EAAAA,EAAUA,CAAAA,CAACP,sBAAoB,aAAaH,0BAAwB,qBACnE,UAACW,EAAAA,EAASA,CAAAA,CAACR,sBAAoB,YAAYH,0BAAwB,oBAAW,wBAC9E,UAACY,EAAAA,EAAeA,CAAAA,CAACT,sBAAoB,kBAAkBH,0BAAwB,oBAAW,2DAI5F,UAACa,EAAAA,EAAWA,CAAAA,CAACV,sBAAoB,cAAcH,0BAAwB,oBACrE,WAACc,OAAAA,CAAKC,SAAUzC,EAAcwB,UAAU,sBACtC,WAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,OAAOd,sBAAoB,QAAQH,0BAAwB,oBAAW,qBACrF,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,GAAG,OAAOxB,MAAO9B,EAASE,IAAI,CAAEqD,SAAU7C,GAAKkB,EAAkB,OAAQlB,EAAE8C,MAAM,CAAC1B,KAAK,EAAG2B,YAAY,yBAAyBC,QAAQ,IAACpB,sBAAoB,QAAQH,0BAAwB,gBAGrM,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,OAAOd,sBAAoB,QAAQH,0BAAwB,oBAAW,qBACrF,WAACwB,EAAAA,EAAMA,CAAAA,CAAC7B,MAAO9B,EAASG,IAAI,CAAEyD,cAAe9B,GAASF,EAAkB,OAAQE,GAAQ4B,QAAQ,IAACpB,sBAAoB,SAASH,0BAAwB,qBACpJ,UAAC0B,EAAAA,EAAaA,CAAAA,CAACvB,sBAAoB,gBAAgBH,0BAAwB,oBACzE,UAAC2B,EAAAA,EAAWA,CAAAA,CAACL,YAAY,0BAA0BnB,sBAAoB,cAAcH,0BAAwB,eAE/G,UAAC4B,EAAAA,EAAaA,CAAAA,CAACzB,sBAAoB,gBAAgBH,0BAAwB,oBACxE6B,EAAAA,EAAgBA,CAACC,GAAG,CAAC9D,GAAQ,UAAC+D,EAAAA,EAAUA,CAAAA,CAAkBpC,MAAO3B,EAAK2B,KAAK,UACvE3B,EAAKgE,KAAK,EADgChE,EAAK2B,KAAK,WAO/D,WAACE,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,mBAAmBd,sBAAoB,QAAQH,0BAAwB,oBAAW,sBACjG,WAACwB,EAAAA,EAAMA,CAAAA,CAAC7B,MAAO9B,EAASI,gBAAgB,CAAEwD,cAAe9B,GAASF,EAAkB,mBAAoBE,GAAQQ,sBAAoB,SAASH,0BAAwB,qBACnK,UAAC0B,EAAAA,EAAaA,CAAAA,CAACvB,sBAAoB,gBAAgBH,0BAAwB,oBACzE,UAAC2B,EAAAA,EAAWA,CAAAA,CAACxB,sBAAoB,cAAcH,0BAAwB,eAEzE,UAAC4B,EAAAA,EAAaA,CAAAA,CAACzB,sBAAoB,gBAAgBH,0BAAwB,oBACxEiC,OAAOC,OAAO,CAACC,EAAAA,EAAiBA,EAAEL,GAAG,CAAC,CAAC,CAACM,EAAKC,EAAK,GAAK,WAACN,EAAAA,EAAUA,CAAAA,CAAWpC,MAAOyC,YAChFC,EAAKtE,IAAI,CAAC,QAAM,IAChBsE,EAAKC,eAAe,CAACC,OAAO,CAACC,cAAc,GAAG,mBAFsBJ,YAS/E,WAACvC,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAed,sBAAoB,QAAQH,0BAAwB,oBAAW,kBAC7F,WAACwB,EAAAA,EAAMA,CAAAA,CAAC7B,MAAO9B,EAASK,YAAY,CAAEuD,cAAe9B,GAASF,EAAkB,eAAgBE,GAAQQ,sBAAoB,SAASH,0BAAwB,qBAC3J,UAAC0B,EAAAA,EAAaA,CAAAA,CAACvB,sBAAoB,gBAAgBH,0BAAwB,oBACzE,UAAC2B,EAAAA,EAAWA,CAAAA,CAACxB,sBAAoB,cAAcH,0BAAwB,eAEzE,WAAC4B,EAAAA,EAAaA,CAAAA,CAACzB,sBAAoB,gBAAgBH,0BAAwB,qBACzE,UAAC+B,EAAAA,EAAUA,CAAAA,CAACpC,MAAM,UAAUQ,sBAAoB,aAAaH,0BAAwB,oBAAW,YAChG,UAAC+B,EAAAA,EAAUA,CAAAA,CAACpC,MAAM,SAASQ,sBAAoB,aAAaH,0BAAwB,oBAAW,mCAOrG,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAed,sBAAoB,QAAQH,0BAAwB,oBAAW,uBAC7F,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,GAAG,eAAenD,KAAK,SAAS2B,MAAO9B,EAASM,YAAY,CAAEiD,SAAU7C,GAAKkB,EAAkB,eAAgBgD,SAASlE,EAAE8C,MAAM,CAAC1B,KAAK,GAAK,GAAI2B,YAAY,IAAIoB,IAAI,IAAIvC,sBAAoB,QAAQH,0BAAwB,gBAGpO,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,eAAed,sBAAoB,QAAQH,0BAAwB,oBAAW,uBAC7F,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,GAAG,eAAenD,KAAK,SAAS2B,MAAO9B,EAASO,YAAY,CAAEgD,SAAU7C,GAAKkB,EAAkB,eAAgBgD,SAASlE,EAAE8C,MAAM,CAAC1B,KAAK,GAAK,GAAI2B,YAAY,IAAIoB,IAAI,IAAIvC,sBAAoB,QAAQH,0BAAwB,gBAGpO,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACkB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,gBAAgBd,sBAAoB,QAAQH,0BAAwB,oBAAW,mBAC9F,WAACwB,EAAAA,EAAMA,CAAAA,CAAC7B,MAAO9B,EAASQ,aAAa,CAAEoD,cAAe9B,GAASF,EAAkB,gBAAiBE,GAAQQ,sBAAoB,SAASH,0BAAwB,qBAC7J,UAAC0B,EAAAA,EAAaA,CAAAA,CAACvB,sBAAoB,gBAAgBH,0BAAwB,oBACzE,UAAC2B,EAAAA,EAAWA,CAAAA,CAACxB,sBAAoB,cAAcH,0BAAwB,eAEzE,WAAC4B,EAAAA,EAAaA,CAAAA,CAACzB,sBAAoB,gBAAgBH,0BAAwB,qBACzE,UAAC+B,EAAAA,EAAUA,CAAAA,CAACpC,MAAM,OAAOQ,sBAAoB,aAAaH,0BAAwB,oBAAW,SAC7F,UAAC+B,EAAAA,EAAUA,CAAAA,CAACpC,MAAM,SAASQ,sBAAoB,aAAaH,0BAAwB,oBAAW,uBAOtGnC,EAASI,gBAAgB,EAAI,WAACwC,EAAAA,EAAIA,CAAAA,WAC/B,UAACC,EAAAA,EAAUA,CAAAA,UACT,WAACC,EAAAA,EAASA,CAAAA,CAACb,UAAU,oBAClBqC,EAAAA,EAAiB,CAACtE,EAASI,gBAAgB,CAAmC,CAACF,IAAI,CAAE,IAAI,oBAI9F,UAAC8C,EAAAA,EAAWA,CAAAA,UACV,WAAChB,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,WACC,UAAC8C,KAAAA,CAAG7C,UAAU,8BAAqB,cACnC,UAAC8C,KAAAA,CAAG9C,UAAU,6BACXqC,EAAAA,EAAiB,CAACtE,EAASI,gBAAgB,CAAmC,CAAC4E,QAAQ,CAACC,KAAK,CAAC,EAAG,GAAGhB,GAAG,CAAC,CAACiB,EAASC,IAAU,WAACC,KAAAA,CAAenD,UAAU,8BACjJ,UAACoD,OAAAA,CAAKpD,UAAU,2CACfiD,IAF+HC,SAM1I,WAACnD,MAAAA,WACC,UAAC8C,KAAAA,CAAG7C,UAAU,8BAAqB,aACnC,WAACD,MAAAA,CAAIC,UAAU,8BACb,WAACU,IAAAA,WAAE,cACW,IACX2B,EAAAA,EAAiB,CAACtE,EAASI,gBAAgB,CAAmC,CAACqE,eAAe,CAACC,OAAO,CAACC,cAAc,GAAG,cAG3H,WAAChC,IAAAA,WAAE,aACU,IACV2B,EAAAA,EAAiB,CAACtE,EAASI,gBAAgB,CAAmC,CAACqE,eAAe,CAACa,MAAM,CAACX,cAAc,GAAG,cAG1H,WAAChC,IAAAA,CAAEV,UAAU,kCAAwB,iBACpB,IACdqC,EAAAA,EAAiB,CAACtE,EAASI,gBAAgB,CAAmC,CAACmF,WAAW,CAAE,IAAI,IAC/F,IACDjB,EAAAA,EAAiB,CAACtE,EAASI,gBAAgB,CAAmC,CAACoF,WAAW,kBAQzG,WAACxD,MAAAA,CAAIC,UAAU,uCACb,UAACG,IAAIA,CAACC,KAAK,gCAAgCC,OAAtCF,eAA0D,OAAOD,0BAAwB,oBAC5F,UAACI,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUvB,KAAK,SAASmC,sBAAoB,SAASH,0BAAwB,oBAAW,aAI1G,WAACI,EAAAA,CAAMA,CAAAA,CAACpC,KAAK,SAASsF,SAAU5F,EAAWyC,sBAAoB,SAASH,0BAAwB,qBAC9F,UAACuD,EAAAA,CAAIA,CAAAA,CAACzD,UAAU,eAAeK,sBAAoB,OAAOH,0BAAwB,aACjFtC,EAAY,cAAgB,qCAO7C,0BCvOA,yCCAA,uCAAoL,yBCApL,kDCAA,gDCAA,wGCAA,6VCgBA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,QACA,CACA,UACA,eACA,CACA,UACA,MACA,CACA,uBAAiC,EACjC,MA1BA,IAAoB,uCAAoL,CA0BxM,mJAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QA5CA,IAAsB,uCAAmK,CA4CzL,mIAGA,CACO,CACP,CACA,QAnDA,IAAsB,uCAA4J,CAmDlL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QApEA,IAAsB,sCAAiJ,CAoEvK,gHACA,gBApEA,IAAsB,uCAAuJ,CAoE7K,sHACA,aApEA,IAAsB,uCAAoJ,CAoE1K,mHACA,WApEA,IAAsB,4CAAgF,CAoEtG,+CACA,cApEA,IAAsB,4CAAmF,CAoEzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EAEA,CAAO,UACP,sJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,8CACA,6CAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC/GD,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,wDCAA,qDCAA,sECAA,mDCAA,mECAA,yDCAA,iECmBI,sBAAsB,guBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJE,KALa,EAKN,GAA8B,EAAE,QAInB,CAAG,CAJD,GAIK,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,mCAAmC,CACnD,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BE8F,EAoCnB,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,6GCAA,qDCAA,4DCAA,wDCAA,gECAA,wDCAA,sDCAA,iDCAA,2DCAA,oDCAA,uCAAoL,yBCApL,gDCAA,0DCAA,4DCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/./src/app/dashboard/admin/institutions/new/page.tsx", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/?c2a8", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/?2998", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/?57a0", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { ArrowLeft, Save } from 'lucide-react';\nimport Link from 'next/link';\nimport { institutionTypes, subscriptionPlans } from '@/config/subscriptions';\nimport { useToast } from '@/hooks/use-toast';\nexport default function NewInstitutionPage() {\n  const router = useRouter();\n  const {\n    toast\n  } = useToast();\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    name: '',\n    type: '',\n    subscriptionPlan: 'basic',\n    billingCycle: 'monthly',\n    studentCount: 0,\n    teacherCount: 0,\n    paymentStatus: 'unpaid'\n  });\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    try {\n      const response = await fetch('/api/institutions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(formData)\n      });\n      const data = await response.json();\n      if (data.success) {\n        toast({\n          title: 'Success',\n          description: 'Institution created successfully'\n        });\n        router.push('/dashboard/admin/institutions');\n      } else {\n        toast({\n          title: 'Error',\n          description: data.error || 'Failed to create institution',\n          variant: 'destructive'\n        });\n      }\n    } catch (error) {\n      console.error('Error creating institution:', error);\n      toast({\n        title: 'Error',\n        description: 'Failed to create institution',\n        variant: 'destructive'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleInputChange = (field: string, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  return <div className='space-y-6' data-sentry-component=\"NewInstitutionPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center space-x-4'>\r\n        <Link href='/dashboard/admin/institutions' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n          <Button variant='outline' size='sm' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n            <ArrowLeft className='mr-2 h-4 w-4' data-sentry-element=\"ArrowLeft\" data-sentry-source-file=\"page.tsx\" />\r\n            Back\r\n          </Button>\r\n        </Link>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>\r\n            Add New Institution\r\n          </h1>\r\n          <p className='text-muted-foreground'>\r\n            Create a new educational institution on the platform\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Institution Details</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\r\n            Enter the basic information for the new institution\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <form onSubmit={handleSubmit} className='space-y-6'>\r\n            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='name' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Institution Name</Label>\r\n                <Input id='name' value={formData.name} onChange={e => handleInputChange('name', e.target.value)} placeholder='Enter institution name' required data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='type' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Institution Type</Label>\r\n                <Select value={formData.type} onValueChange={value => handleInputChange('type', value)} required data-sentry-element=\"Select\" data-sentry-source-file=\"page.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectValue placeholder='Select institution type' data-sentry-element=\"SelectValue\" data-sentry-source-file=\"page.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"page.tsx\">\r\n                    {institutionTypes.map(type => <SelectItem key={type.value} value={type.value}>\r\n                        {type.label}\r\n                      </SelectItem>)}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='subscriptionPlan' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Subscription Plan</Label>\r\n                <Select value={formData.subscriptionPlan} onValueChange={value => handleInputChange('subscriptionPlan', value)} data-sentry-element=\"Select\" data-sentry-source-file=\"page.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"page.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"page.tsx\">\r\n                    {Object.entries(subscriptionPlans).map(([key, plan]) => <SelectItem key={key} value={key}>\r\n                        {plan.name} - Rp{' '}\r\n                        {plan.pricePerStudent.monthly.toLocaleString()}\r\n                        /student/month\r\n                      </SelectItem>)}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='billingCycle' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Billing Cycle</Label>\r\n                <Select value={formData.billingCycle} onValueChange={value => handleInputChange('billingCycle', value)} data-sentry-element=\"Select\" data-sentry-source-file=\"page.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"page.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectItem value='monthly' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">Monthly</SelectItem>\r\n                    <SelectItem value='yearly' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">\r\n                      Yearly (25% discount)\r\n                    </SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='studentCount' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Number of Students</Label>\r\n                <Input id='studentCount' type='number' value={formData.studentCount} onChange={e => handleInputChange('studentCount', parseInt(e.target.value) || 0)} placeholder='0' min='0' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='teacherCount' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Number of Teachers</Label>\r\n                <Input id='teacherCount' type='number' value={formData.teacherCount} onChange={e => handleInputChange('teacherCount', parseInt(e.target.value) || 0)} placeholder='0' min='0' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n              </div>\r\n\r\n              <div className='space-y-2'>\r\n                <Label htmlFor='paymentStatus' data-sentry-element=\"Label\" data-sentry-source-file=\"page.tsx\">Payment Status</Label>\r\n                <Select value={formData.paymentStatus} onValueChange={value => handleInputChange('paymentStatus', value)} data-sentry-element=\"Select\" data-sentry-source-file=\"page.tsx\">\r\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"page.tsx\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"page.tsx\">\r\n                    <SelectItem value='paid' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">Paid</SelectItem>\r\n                    <SelectItem value='unpaid' data-sentry-element=\"SelectItem\" data-sentry-source-file=\"page.tsx\">Unpaid</SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Subscription Plan Preview */}\r\n            {formData.subscriptionPlan && <Card>\r\n                <CardHeader>\r\n                  <CardTitle className='text-lg'>\r\n                    {subscriptionPlans[formData.subscriptionPlan as keyof typeof subscriptionPlans].name}{' '}\r\n                    Plan Preview\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>\r\n                    <div>\r\n                      <h4 className='mb-2 font-semibold'>Features:</h4>\r\n                      <ul className='space-y-1 text-sm'>\r\n                        {subscriptionPlans[formData.subscriptionPlan as keyof typeof subscriptionPlans].features.slice(0, 5).map((feature, index) => <li key={index} className='flex items-center'>\r\n                              <span className='mr-2 h-2 w-2 rounded-full bg-green-500'></span>\r\n                              {feature}\r\n                            </li>)}\r\n                      </ul>\r\n                    </div>\r\n                    <div>\r\n                      <h4 className='mb-2 font-semibold'>Pricing:</h4>\r\n                      <div className='space-y-1 text-sm'>\r\n                        <p>\r\n                          Monthly: Rp{' '}\r\n                          {subscriptionPlans[formData.subscriptionPlan as keyof typeof subscriptionPlans].pricePerStudent.monthly.toLocaleString()}\r\n                          /student\r\n                        </p>\r\n                        <p>\r\n                          Yearly: Rp{' '}\r\n                          {subscriptionPlans[formData.subscriptionPlan as keyof typeof subscriptionPlans].pricePerStudent.yearly.toLocaleString()}\r\n                          /student\r\n                        </p>\r\n                        <p className='text-muted-foreground'>\r\n                          Student Range:{' '}\r\n                          {subscriptionPlans[formData.subscriptionPlan as keyof typeof subscriptionPlans].minStudents}{' '}\r\n                          -{' '}\r\n                          {subscriptionPlans[formData.subscriptionPlan as keyof typeof subscriptionPlans].maxStudents}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>}\r\n\r\n            <div className='flex justify-end space-x-4'>\r\n              <Link href='/dashboard/admin/institutions' data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n                <Button variant='outline' type='button' data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                  Cancel\r\n                </Button>\r\n              </Link>\r\n              <Button type='submit' disabled={isLoading} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n                <Save className='mr-2 h-4 w-4' data-sentry-element=\"Save\" data-sentry-source-file=\"page.tsx\" />\r\n                {isLoading ? 'Creating...' : 'Create Institution'}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </CardContent>\r\n      </Card>\r\n    </div>;\n}", "module.exports = require(\"os\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\new\\\\page.tsx\");\n", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\new\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'admin',\n        {\n        children: [\n        'institutions',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\new\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/admin/institutions/new/page\",\n        pathname: \"/dashboard/admin/institutions/new\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/admin/institutions/new',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/admin/institutions/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/admin/institutions/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/admin/institutions/new',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\institutions\\\\new\\\\page.tsx\");\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["NewInstitutionPage", "router", "useRouter", "toast", "useToast", "isLoading", "setIsLoading", "useState", "formData", "setFormData", "name", "type", "subscriptionPlan", "billingCycle", "studentCount", "teacherCount", "paymentStatus", "handleSubmit", "e", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "success", "title", "description", "push", "error", "variant", "console", "handleInputChange", "field", "value", "prev", "div", "className", "data-sentry-component", "data-sentry-source-file", "Link", "href", "data-sentry-element", "<PERSON><PERSON>", "size", "ArrowLeft", "h1", "p", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "form", "onSubmit", "Label", "htmlFor", "Input", "id", "onChange", "target", "placeholder", "required", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "institutionTypes", "map", "SelectItem", "label", "Object", "entries", "subscriptionPlans", "key", "plan", "pricePerStudent", "monthly", "toLocaleString", "parseInt", "min", "h4", "ul", "features", "slice", "feature", "index", "li", "span", "yearly", "minStudents", "maxStudents", "disabled", "Save", "serverComponentModule.default"], "sourceRoot": ""}