try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d579e6ca-e2b1-40b3-ad44-fc43434ffe85",e._sentryDebugIdIdentifier="sentry-dbid-d579e6ca-e2b1-40b3-ad44-fc43434ffe85")}catch(e){}"use strict";exports.id=6429,exports.ids=[6429],exports.modules={184:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},729:(e,t,n)=>{n.d(t,{N_:()=>eW,Ay:()=>e_});var r=n(53744);let i="numeric",o="ascii",s="alpha",l="asciinumeric",a="alphanumeric",d="domain",h="emoji",c="whitespace";function p(e,t,n){for(let r in t[i]&&(t[l]=!0,t[a]=!0),t[o]&&(t[l]=!0,t[s]=!0),t[l]&&(t[a]=!0),t[s]&&(t[a]=!0),t[a]&&(t[d]=!0),t[h]&&(t[d]=!0),t){let t=(r in n||(n[r]=[]),n[r]);0>t.indexOf(e)&&t.push(e)}}function u(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}u.groups={},u.prototype={accepts(){return!!this.t},go(e){let t=this.j[e];if(t)return t;for(let t=0;t<this.jr.length;t++){let n=this.jr[t][0],r=this.jr[t][1];if(r&&n.test(e))return r}return this.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,r){for(let i=0;i<e.length;i++)this.tt(e[i],t,n,r)},tr(e,t,n,r){let i;return r=r||u.groups,t&&t.j?i=t:(i=new u(t),n&&r&&p(t,n,r)),this.jr.push([e,i]),i},ts(e,t,n,r){let i=this,o=e.length;if(!o)return i;for(let t=0;t<o-1;t++)i=i.tt(e[t]);return i.tt(e[o-1],t,n,r)},tt(e,t,n,r){if(r=r||u.groups,t&&t.j)return this.j[e]=t,t;let i,o=this.go(e);return o?(Object.assign((i=new u).j,o.j),i.jr.push.apply(i.jr,o.jr),i.jd=o.jd,i.t=o.t):i=new u,t&&(r&&(i.t&&"string"==typeof i.t?p(t,Object.assign(function(e,t){let n={};for(let r in t)t[r].indexOf(e)>=0&&(n[r]=!0);return n}(i.t,r),n),r):n&&p(t,n,r)),i.t=t),this.j[e]=i,i}};let f=(e,t,n,r,i)=>e.ta(t,n,r,i),m=(e,t,n,r,i)=>e.tr(t,n,r,i),g=(e,t,n,r,i)=>e.ts(t,n,r,i),y=(e,t,n,r,i)=>e.tt(t,n,r,i),b="WORD",v="UWORD",w="ASCIINUMERICAL",k="ALPHANUMERICAL",x="LOCALHOST",S="UTLD",M="SCHEME",C="SLASH_SCHEME",A="OPENBRACE",T="CLOSEBRACE",O="OPENBRACKET",E="CLOSEBRACKET",N="OPENPAREN",D="CLOSEPAREN",R="OPENANGLEBRACKET",L="CLOSEANGLEBRACKET",I="FULLWIDTHLEFTPAREN",P="FULLWIDTHRIGHTPAREN",z="LEFTCORNERBRACKET",$="RIGHTCORNERBRACKET",B="LEFTWHITECORNERBRACKET",F="RIGHTWHITECORNERBRACKET",j="FULLWIDTHLESSTHAN",V="FULLWIDTHGREATERTHAN",H="AMPERSAND",K="APOSTROPHE",J="ASTERISK",W="BACKSLASH",_="BACKTICK",U="CARET",q="COLON",G="COMMA",Y="DOLLAR",X="EQUALS",Z="EXCLAMATION",Q="HYPHEN",ee="PERCENT",et="PIPE",en="PLUS",er="POUND",ei="QUERY",eo="QUOTE",es="FULLWIDTHMIDDLEDOT",el="SEMI",ea="SLASH",ed="TILDE",eh="UNDERSCORE",ec="EMOJI";var ep=Object.freeze({__proto__:null,ALPHANUMERICAL:k,AMPERSAND:H,APOSTROPHE:K,ASCIINUMERICAL:w,ASTERISK:J,AT:"AT",BACKSLASH:W,BACKTICK:_,CARET:U,CLOSEANGLEBRACKET:L,CLOSEBRACE:T,CLOSEBRACKET:E,CLOSEPAREN:D,COLON:q,COMMA:G,DOLLAR:Y,DOT:"DOT",EMOJI:ec,EQUALS:X,EXCLAMATION:Z,FULLWIDTHGREATERTHAN:V,FULLWIDTHLEFTPAREN:I,FULLWIDTHLESSTHAN:j,FULLWIDTHMIDDLEDOT:es,FULLWIDTHRIGHTPAREN:P,HYPHEN:Q,LEFTCORNERBRACKET:z,LEFTWHITECORNERBRACKET:B,LOCALHOST:x,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:R,OPENBRACE:A,OPENBRACKET:O,OPENPAREN:N,PERCENT:ee,PIPE:et,PLUS:en,POUND:er,QUERY:ei,QUOTE:eo,RIGHTCORNERBRACKET:$,RIGHTWHITECORNERBRACKET:F,SCHEME:M,SEMI:el,SLASH:ea,SLASH_SCHEME:C,SYM:"SYM",TILDE:ed,TLD:"TLD",UNDERSCORE:eh,UTLD:S,UWORD:v,WORD:b,WS:"WS"});let eu=/[a-z]/,ef=/\p{L}/u,em=/\p{Emoji}/u,eg=/\d/,ey=/\s/,eb=null,ev=null;function ew(e,t){let n=function(e){let t=[],n=e.length,r=0;for(;r<n;){let i,o=e.charCodeAt(r),s=o<55296||o>56319||r+1===n||(i=e.charCodeAt(r+1))<56320||i>57343?e[r]:e.slice(r,r+2);t.push(s),r+=s.length}return t}(t.replace(/[A-Z]/g,e=>e.toLowerCase())),r=n.length,i=[],o=0,s=0;for(;s<r;){let l=e,a=null,d=0,h=null,c=-1,p=-1;for(;s<r&&(a=l.go(n[s]));)(l=a).accepts()?(c=0,p=0,h=l):c>=0&&(c+=n[s].length,p++),d+=n[s].length,o+=n[s].length,s++;o-=c,s-=p,d-=c,i.push({t:h.t,v:t.slice(o-d,o),s:o-d,e:o})}return i}function ek(e,t,n,r,i){let o,s=t.length;for(let n=0;n<s-1;n++){let s=t[n];e.j[s]?o=e.j[s]:((o=new u(r)).jr=i.slice(),e.j[s]=o),e=o}return(o=new u(n)).jr=i.slice(),e.j[t[s-1]]=o,o}function ex(e){let t=[],n=[],r=0;for(;r<e.length;){let i=0;for(;"0123456789".indexOf(e[r+i])>=0;)i++;if(i>0){t.push(n.join(""));for(let t=parseInt(e.substring(r,r+i),10);t>0;t--)n.pop();r+=i}else n.push(e[r]),r++}return t}let eS={defaultProtocol:"http",events:null,format:eC,formatHref:eC,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function eM(e,t=null){let n=Object.assign({},eS);e&&(n=Object.assign(n,e instanceof eM?e.o:e));let r=n.ignoreTags,i=[];for(let e=0;e<r.length;e++)i.push(r[e].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=i}function eC(e){return e}function eA(e,t){this.t="token",this.v=e,this.tk=t}function eT(e,t){class n extends eA{constructor(t,n){super(t,n),this.t=e}}for(let e in t)n.prototype[e]=t[e];return n.t=e,n}eM.prototype={o:eS,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){let r=null!=t,i=this.o[e];return i&&("object"==typeof i?"function"==typeof(i=n.t in i?i[n.t]:eS[e])&&r&&(i=i(t,n)):"function"==typeof i&&r&&(i=i(t,n.t,n))),i},getObj(e,t,n){let r=this.o[e];return"function"==typeof r&&null!=t&&(r=r(t,n.t,n)),r},render(e){let t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},eA.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){let t=this.toString(),n=e.get("truncate",t,this),r=e.get("format",t,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=eS.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){let t=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",t,this),r=e.get("tagName",t,this),i=this.toFormattedString(e),o={},s=e.get("className",t,this),l=e.get("target",t,this),a=e.get("rel",t,this),d=e.getObj("attributes",t,this),h=e.getObj("events",t,this);return o.href=n,s&&(o.class=s),l&&(o.target=l),a&&(o.rel=a),d&&Object.assign(o,d),{tagName:r,attributes:o,content:i,eventListeners:h}}};let eO=eT("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),eE=eT("text"),eN=eT("nl"),eD=eT("url",{isLink:!0,toHref(e=eS.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){let e=this.tk;return e.length>=2&&e[0].t!==x&&e[1].t===q}}),eR=e=>new u(e);function eL(e,t,n){let r=n[0].s,i=n[n.length-1].e;return new e(t.slice(r,i),n)}let eI="undefined"!=typeof console&&console&&console.warn||(()=>{}),eP={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function ez(e,t=!1){if(eP.initialized&&eI(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);eP.customSchemes.push([e,t])}function e$(e){return eP.initialized||function(){eP.scanner=function(e=[]){let t={};u.groups=t;let n=new u;null==eb&&(eb=ex("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0axi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==ev&&(ev=ex("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),y(n,"'",K),y(n,"{",A),y(n,"}",T),y(n,"[",O),y(n,"]",E),y(n,"(",N),y(n,")",D),y(n,"<",R),y(n,">",L),y(n,"（",I),y(n,"）",P),y(n,"「",z),y(n,"」",$),y(n,"『",B),y(n,"』",F),y(n,"＜",j),y(n,"＞",V),y(n,"&",H),y(n,"*",J),y(n,"@","AT"),y(n,"`",_),y(n,"^",U),y(n,":",q),y(n,",",G),y(n,"$",Y),y(n,".","DOT"),y(n,"=",X),y(n,"!",Z),y(n,"-",Q),y(n,"%",ee),y(n,"|",et),y(n,"+",en),y(n,"#",er),y(n,"?",ei),y(n,'"',eo),y(n,"/",ea),y(n,";",el),y(n,"~",ed),y(n,"_",eh),y(n,"\\",W),y(n,"・",es);let r=m(n,eg,"NUM",{[i]:!0});m(r,eg,r);let f=m(r,eu,w,{[l]:!0}),ew=m(r,ef,k,{[a]:!0}),eS=m(n,eu,b,{[o]:!0});m(eS,eg,f),m(eS,eu,eS),m(f,eg,f),m(f,eu,f);let eM=m(n,ef,v,{[s]:!0});m(eM,eu),m(eM,eg,ew),m(eM,ef,eM),m(ew,eg,ew),m(ew,eu),m(ew,ef,ew);let eC=y(n,"\n","NL",{[c]:!0}),eA=y(n,"\r","WS",{[c]:!0}),eT=m(n,ey,"WS",{[c]:!0});y(n,"￼",eT),y(eA,"\n",eC),y(eA,"￼",eT),m(eA,ey,eT),y(eT,"\r"),y(eT,"\n"),m(eT,ey,eT),y(eT,"￼",eT);let eO=m(n,em,ec,{[h]:!0});y(eO,"#"),m(eO,em,eO),y(eO,"️",eO);let eE=y(eO,"‍");y(eE,"#"),m(eE,em,eO);let eN=[[eu,eS],[eg,f]],eD=[[eu,null],[ef,eM],[eg,ew]];for(let e=0;e<eb.length;e++)ek(n,eb[e],"TLD",b,eN);for(let e=0;e<ev.length;e++)ek(n,ev[e],S,v,eD);p("TLD",{tld:!0,ascii:!0},t),p(S,{utld:!0,alpha:!0},t),ek(n,"file",M,b,eN),ek(n,"mailto",M,b,eN),ek(n,"http",C,b,eN),ek(n,"https",C,b,eN),ek(n,"ftp",C,b,eN),ek(n,"ftps",C,b,eN),p(M,{scheme:!0,ascii:!0},t),p(C,{slashscheme:!0,ascii:!0},t),e=e.sort((e,t)=>e[0]>t[0]?1:-1);for(let t=0;t<e.length;t++){let r=e[t][0],s=e[t][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?s[d]=!0:eu.test(r)?eg.test(r)?s[l]=!0:s[o]=!0:s[i]=!0,g(n,r,r,s)}return g(n,"localhost",x,{ascii:!0}),n.jd=new u("SYM"),{start:n,tokens:Object.assign({groups:t},ep)}}(eP.customSchemes);for(let e=0;e<eP.tokenQueue.length;e++)eP.tokenQueue[e][1]({scanner:eP.scanner});eP.parser=function({groups:e}){let t=e.domain.concat([H,J,"AT",W,_,U,Y,X,Q,"NUM",ee,et,en,er,ea,"SYM",ed,eh]),n=[K,q,G,"DOT",Z,ee,ei,eo,el,R,L,A,T,E,O,N,D,I,P,z,$,B,F,j,V],r=[H,K,J,W,_,U,Y,X,Q,A,T,ee,et,en,er,ei,ea,"SYM",ed,eh],i=eR(),o=y(i,ed);f(o,r,o),f(o,e.domain,o);let s=eR(),l=eR(),a=eR();f(i,e.domain,s),f(i,e.scheme,l),f(i,e.slashscheme,a),f(s,r,o),f(s,e.domain,s);let d=y(s,"AT");y(o,"AT",d),y(l,"AT",d),y(a,"AT",d);let h=y(o,"DOT");f(h,r,o),f(h,e.domain,o);let c=eR();f(d,e.domain,c),f(c,e.domain,c);let p=y(c,"DOT");f(p,e.domain,c);let u=eR(eO);f(p,e.tld,u),f(p,e.utld,u),y(d,x,u);let m=y(c,Q);y(m,Q,m),f(m,e.domain,c),f(u,e.domain,c),y(u,"DOT",p),y(u,Q,m),f(y(u,q),e.numeric,eO);let g=y(s,Q),b=y(s,"DOT");y(g,Q,g),f(g,e.domain,s),f(b,r,o),f(b,e.domain,s);let v=eR(eD);f(b,e.tld,v),f(b,e.utld,v),f(v,e.domain,s),f(v,r,o),y(v,"DOT",b),y(v,Q,g),y(v,"AT",d);let w=y(v,q),k=eR(eD);f(w,e.numeric,k);let S=eR(eD),M=eR();f(S,t,S),f(S,n,M),f(M,t,S),f(M,n,M),y(v,ea,S),y(k,ea,S);let C=y(l,q),es=y(a,q),ec=y(es,ea),eu=y(ec,ea);f(l,e.domain,s),y(l,"DOT",b),y(l,Q,g),f(a,e.domain,s),y(a,"DOT",b),y(a,Q,g),f(C,e.domain,S),y(C,ea,S),y(C,ei,S),f(eu,e.domain,S),f(eu,t,S),y(eu,ea,S);let ef=[[A,T],[O,E],[N,D],[R,L],[I,P],[z,$],[B,F],[j,V]];for(let e=0;e<ef.length;e++){let[r,i]=ef[e],o=y(S,r);y(M,r,o),y(o,i,S);let s=eR(eD);f(o,t,s);let l=eR();f(o,n),f(s,t,s),f(s,n,l),f(l,t,s),f(l,n,l),y(s,i,S),y(l,i,S)}return y(i,x,v),y(i,"NL",eN),{start:i,tokens:ep}}(eP.scanner.tokens);for(let e=0;e<eP.pluginQueue.length;e++)eP.pluginQueue[e][1]({scanner:eP.scanner,parser:eP.parser});eP.initialized=!0}(),function(e,t,n){let r=n.length,i=0,o=[],s=[];for(;i<r;){let l=e,a=null,d=null,h=0,c=null,p=-1;for(;i<r&&!(a=l.go(n[i].t));)s.push(n[i++]);for(;i<r&&(d=a||l.go(n[i].t));)a=null,(l=d).accepts()?(p=0,c=l):p>=0&&p++,i++,h++;if(p<0)(i-=h)<r&&(s.push(n[i]),i++);else{s.length>0&&(o.push(eL(eE,t,s)),s=[]),i-=p,h-=p;let e=c.t,r=n.slice(i-h,i);o.push(eL(e,t,r))}}return s.length>0&&o.push(eL(eE,t,s)),o}(eP.parser.start,e,ew(eP.scanner.start,e))}function eB(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}let r=new eM(n),i=e$(e),o=[];for(let e=0;e<i.length;e++){let n=i[e];n.isLink&&(!t||n.t===t)&&r.check(n)&&o.push(n.toFormattedObject(r))}return o}e$.scan=ew;var eF=n(42404),ej="[\0- \xa0 ᠎ -\u2029 　]",eV=new RegExp(ej),eH=RegExp(`${ej}$`),eK=RegExp(ej,"g");function eJ(e,t){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(e=>{let t="string"==typeof e?e:e.scheme;t&&n.push(t)}),!e||e.replace(eK,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}var eW=r.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if("string"==typeof e)return void ez(e);ez(e.scheme,e.optionalSlashes)})},onDestroy(){u.groups={},eP.scanner=null,eP.parser=null,eP.tokenQueue=[],eP.pluginQueue=[],eP.customSchemes=[],eP.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,enableClickSelection:!1,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!eJ(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{let t=e.getAttribute("href");return!!t&&!!this.options.isAllowedUri(t,{defaultValidate:e=>!!eJ(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!eJ(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,r.KV)(this.options.HTMLAttributes,e),0]:["a",(0,r.KV)(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eJ(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{let{href:n}=e||{};return(!n||!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eJ(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,r.Zc)({find:e=>{let t=[];if(e){let{protocols:n,defaultProtocol:r}=this.options,i=eB(e).filter(e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!eJ(e,n),protocols:n,defaultProtocol:r}));i.length&&i.forEach(e=>t.push({text:e.value,data:{href:e.href},index:e.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:null==(t=e.data)?void 0:t.href}}})]},addProseMirrorPlugins(){var e,t,n;let i=[],{protocols:o,defaultProtocol:s}=this.options;return this.options.autolink&&i.push((e={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!eJ(e,o),protocols:o,defaultProtocol:s}),shouldAutoLink:this.options.shouldAutoLink},new eF.k_({key:new eF.hs("autolink"),appendTransaction:(t,n,i)=>{let o=t.some(e=>e.docChanged)&&!n.doc.eq(i.doc),s=t.some(e=>e.getMeta("preventAutolink"));if(!o||s)return;let{tr:l}=i,a=(0,r.T7)(n.doc,[...t]);if((0,r.FF)(a).forEach(({newRange:t})=>{let n,o,s=(0,r.Nx)(i.doc,t,e=>e.isTextblock);if(s.length>1)n=s[0],o=i.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ");else if(s.length){let e=i.doc.textBetween(t.from,t.to," "," ");if(!eH.test(e))return;n=s[0],o=i.doc.textBetween(n.pos,t.to,void 0," ")}if(n&&o){let t=o.split(eV).filter(Boolean);if(t.length<=0)return!1;let s=t[t.length-1],a=n.pos+o.lastIndexOf(s);if(!s)return!1;let d=e$(s).map(t=>t.toObject(e.defaultProtocol));if(!(1===d.length?d[0].isLink:3===d.length&&!!d[1].isLink&&["()","[]"].includes(d[0].value+d[2].value)))return!1;d.filter(e=>e.isLink).map(e=>({...e,from:a+e.start+1,to:a+e.end+1})).filter(e=>!i.schema.marks.code||!i.doc.rangeHasMark(e.from,e.to,i.schema.marks.code)).filter(t=>e.validate(t.value)).filter(t=>e.shouldAutoLink(t.value)).forEach(t=>{(0,r.hO)(t.from,t.to,i.doc).some(t=>t.mark.type===e.type)||l.addMark(t.from,t.to,e.type.create({href:t.href}))})}}),l.steps.length)return l}}))),!0===this.options.openOnClick&&i.push((t={type:this.type,editor:this.editor,enableClickSelection:this.options.enableClickSelection},new eF.k_({key:new eF.hs("handleClickLink"),props:{handleClick:(e,n,i)=>{var o,s;if(0!==i.button||!e.editable)return!1;let l=null;if(i.target instanceof HTMLAnchorElement)l=i.target;else{let e=i.target,t=[];for(;"DIV"!==e.nodeName;)t.push(e),e=e.parentNode;l=t.find(e=>"A"===e.nodeName)}if(!l)return!1;let a=(0,r.gu)(e.state,t.type.name),d=null!=(o=null==l?void 0:l.href)?o:a.href,h=null!=(s=null==l?void 0:l.target)?s:a.target;return t.enableClickSelection&&t.editor.commands.extendMarkRange(t.type.name),!!l&&!!d&&(window.open(d,h),!0)}}}))),this.options.linkOnPaste&&i.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new eF.k_({key:new eF.hs("handlePasteLink"),props:{handlePaste:(e,t,r)=>{let{state:i}=e,{selection:o}=i,{empty:s}=o;if(s)return!1;let l="";r.content.forEach(e=>{l+=e.textContent});let a=eB(l,{defaultProtocol:n.defaultProtocol}).find(e=>e.isLink&&e.value===l);return!!l&&!!a&&n.editor.commands.setMark(n.type,{href:a.href})}}}))),i}}),e_=eW},4978:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},6410:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},8238:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},10664:(e,t,n)=>{n.d(t,{Lz:()=>tH,NZ:()=>tg,zF:()=>tv});var r=n(42404),i=n(42842),o=n(98539);let s=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},l=function(e){let t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t},a=null,d=function(e,t,n){let r=a||(a=document.createRange());return r.setEnd(e,null==n?e.nodeValue.length:n),r.setStart(e,t||0),r},h=function(){a=null},c=function(e,t,n,r){return n&&(u(e,t,n,r,-1)||u(e,t,n,r,1))},p=/^(img|br|input|textarea|hr)$/i;function u(e,t,n,r,i){for(var o;;){if(e==n&&t==r)return!0;if(t==(i<0?0:f(e))){let n=e.parentNode;if(!n||1!=n.nodeType||m(e)||p.test(e.nodeName)||"false"==e.contentEditable)return!1;t=s(e)+(i<0?0:1),e=n}else{if(1!=e.nodeType)return!1;let n=e.childNodes[t+(i<0?-1:0)];if(1==n.nodeType&&"false"==n.contentEditable)if(null==(o=n.pmViewDesc)||!o.ignoreForSelection)return!1;else t+=i;else e=n,t=i<0?f(e):0}}}function f(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function m(e){let t;for(let n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}let g=function(e){return e.focusNode&&c(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)};function y(e,t){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}let b="undefined"!=typeof navigator?navigator:null,v="undefined"!=typeof document?document:null,w=b&&b.userAgent||"",k=/Edge\/(\d+)/.exec(w),x=/MSIE \d/.exec(w),S=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(w),M=!!(x||S||k),C=x?document.documentMode:S?+S[1]:k?+k[1]:0,A=!M&&/gecko\/(\d+)/i.test(w);A&&(/Firefox\/(\d+)/.exec(w)||[0,0])[1];let T=!M&&/Chrome\/(\d+)/.exec(w),O=!!T,E=T?+T[1]:0,N=!M&&!!b&&/Apple Computer/.test(b.vendor),D=N&&(/Mobile\/\w+/.test(w)||!!b&&b.maxTouchPoints>2),R=D||!!b&&/Mac/.test(b.platform),L=!!b&&/Win/.test(b.platform),I=/Android \d/.test(w),P=!!v&&"webkitFontSmoothing"in v.documentElement.style,z=P?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function $(e,t){return"number"==typeof e?e:e[t]}function B(e,t,n){let r=e.someProp("scrollThreshold")||0,i=e.someProp("scrollMargin")||5,o=e.dom.ownerDocument;for(let s=n||e.dom;s;){if(1!=s.nodeType){s=l(s);continue}let e=s,n=e==o.body,a=n?function(e){let t=e.defaultView&&e.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}(o):function(e){let t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,r=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*r}}(e),d=0,h=0;if(t.top<a.top+$(r,"top")?h=-(a.top-t.top+$(i,"top")):t.bottom>a.bottom-$(r,"bottom")&&(h=t.bottom-t.top>a.bottom-a.top?t.top+$(i,"top")-a.top:t.bottom-a.bottom+$(i,"bottom")),t.left<a.left+$(r,"left")?d=-(a.left-t.left+$(i,"left")):t.right>a.right-$(r,"right")&&(d=t.right-a.right+$(i,"right")),d||h)if(n)o.defaultView.scrollBy(d,h);else{let n=e.scrollLeft,r=e.scrollTop;h&&(e.scrollTop+=h),d&&(e.scrollLeft+=d);let i=e.scrollLeft-n,o=e.scrollTop-r;t={left:t.left-i,top:t.top-o,right:t.right-i,bottom:t.bottom-o}}let c=n?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(c))break;s="absolute"==c?s.offsetParent:l(s)}}function F(e){let t=[],n=e.ownerDocument;for(let r=e;r&&(t.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),e!=n);r=l(r));return t}function j(e,t){for(let n=0;n<e.length;n++){let{dom:r,top:i,left:o}=e[n];r.scrollTop!=i+t&&(r.scrollTop=i+t),r.scrollLeft!=o&&(r.scrollLeft=o)}}let V=null;function H(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function K(e){return e.top<e.bottom||e.left<e.right}function J(e,t){let n=e.getClientRects();if(n.length){let e=n[t<0?0:n.length-1];if(K(e))return e}return Array.prototype.find.call(n,K)||e.getBoundingClientRect()}let W=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function _(e,t,n){let{node:r,offset:i,atom:o}=e.docView.domFromPos(t,n<0?-1:1),s=P||A;if(3==r.nodeType)if(s&&(W.test(r.nodeValue)||(n<0?!i:i==r.nodeValue.length))){let e=J(d(r,i,i),n);if(A&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let t=J(d(r,i-1,i-1),-1);if(t.top==e.top){let n=J(d(r,i,i+1),-1);if(n.top!=e.top)return U(n,n.left<t.left)}}return e}else{let e=i,t=i,o=n<0?1:-1;return n<0&&!i?(t++,o=-1):n>=0&&i==r.nodeValue.length?(e--,o=1):n<0?e--:t++,U(J(d(r,e,t),o),o<0)}if(!e.state.doc.resolve(t-(o||0)).parent.inlineContent){if(null==o&&i&&(n<0||i==f(r))){let e=r.childNodes[i-1];if(1==e.nodeType)return q(e.getBoundingClientRect(),!1)}if(null==o&&i<f(r)){let e=r.childNodes[i];if(1==e.nodeType)return q(e.getBoundingClientRect(),!0)}return q(r.getBoundingClientRect(),n>=0)}if(null==o&&i&&(n<0||i==f(r))){let e=r.childNodes[i-1],t=3==e.nodeType?d(e,f(e)-!s):1!=e.nodeType||"BR"==e.nodeName&&e.nextSibling?null:e;if(t)return U(J(t,1),!1)}if(null==o&&i<f(r)){let e=r.childNodes[i];for(;e.pmViewDesc&&e.pmViewDesc.ignoreForCoords;)e=e.nextSibling;let t=e?3==e.nodeType?d(e,0,+!s):1==e.nodeType?e:null:null;if(t)return U(J(t,-1),!0)}return U(J(3==r.nodeType?d(r):r,-n),n>=0)}function U(e,t){if(0==e.width)return e;let n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function q(e,t){if(0==e.height)return e;let n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function G(e,t,n){let r=e.state,i=e.root.activeElement;r!=t&&e.updateState(t),i!=e.dom&&e.focus();try{return n()}finally{r!=t&&e.updateState(r),i!=e.dom&&i&&i.focus()}}let Y=/[\u0590-\u08ac]/,X=null,Z=null,Q=!1;class ee{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){let r;if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode))if(n<0){let n,r;if(e==this.contentDOM)n=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.previousSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}else{let n,r;if(e==this.contentDOM)n=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.nextSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}if(e==this.dom&&this.contentDOM)r=t>s(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!1;break}if(t.previousSibling)break}if(null==r&&t==e.childNodes.length)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!0;break}if(t.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let i=this.getDesc(r),o;if(i&&(!t||i.node))if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==e.nodeType?e:e.parentNode):o==e))return i;else n=!1}}getDesc(e){let t=e.pmViewDesc;for(let e=t;e;e=e.parent)if(e==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let i=this.getDesc(r);if(i)return i.localPosFromDOM(e,t,n)}return -1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],i=n+r.size;if(n==e&&i!=n){for(;!r.border&&r.children.length;)for(let e=0;e<r.children.length;e++){let t=r.children[e];if(t.size){r=t;break}}return r}if(e<i)return r.descAt(e-n-r.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let t=0;n<this.children.length;n++){let i=this.children[n],o=t+i.size;if(o>e||i instanceof el){r=e-t;break}t=o}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let e;n&&!(e=this.children[n-1]).size&&e instanceof et&&e.side>=0;n--);if(t<=0){let e,r=!0;for(;(e=n?this.children[n-1]:null)&&e.dom.parentNode!=this.contentDOM;n--,r=!1);return e&&t&&r&&!e.border&&!e.domAtom?e.domFromPos(e.size,t):{node:this.contentDOM,offset:e?s(e.dom)+1:0}}{let e,r=!0;for(;(e=n<this.children.length?this.children[n]:null)&&e.dom.parentNode!=this.contentDOM;n++,r=!1);return e&&r&&!e.border&&!e.domAtom?e.domFromPos(0,t):{node:this.contentDOM,offset:e?s(e.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,i=-1;for(let o=n,l=0;;l++){let n=this.children[l],a=o+n.size;if(-1==r&&e<=a){let i=o+n.border;if(e>=i&&t<=a-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(e,t,i);e=o;for(let t=l;t>0;t--){let n=this.children[t-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=s(n.dom)+1;break}e-=n.size}-1==r&&(r=0)}if(r>-1&&(a>t||l==this.children.length-1)){t=a;for(let e=l+1;e<this.children.length;e++){let n=this.children[e];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){i=s(n.dom);break}t+=n.size}-1==i&&(i=this.contentDOM.childNodes.length);break}o=a}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let i=Math.min(e,t),o=Math.max(e,t);for(let s=0,l=0;s<this.children.length;s++){let a=this.children[s],d=l+a.size;if(i>l&&o<d)return a.setSelection(e-l-a.border,t-l-a.border,n,r);l=d}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),d=n.root.getSelection(),h=n.domSelectionRange(),p=!1;if((A||N)&&e==t){let{node:e,offset:t}=l;if(3==e.nodeType){if((p=!!(t&&"\n"==e.nodeValue[t-1]))&&t==e.nodeValue.length)for(let t=e,n;t;t=t.parentNode){if(n=t.nextSibling){"BR"==n.nodeName&&(l=a={node:n.parentNode,offset:s(n)+1});break}let e=t.pmViewDesc;if(e&&e.node&&e.node.isBlock)break}}else{let n=e.childNodes[t-1];p=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(A&&h.focusNode&&h.focusNode!=a.node&&1==h.focusNode.nodeType){let e=h.focusNode.childNodes[h.focusOffset];e&&"false"==e.contentEditable&&(r=!0)}if(!(r||p&&N)&&c(l.node,l.offset,h.anchorNode,h.anchorOffset)&&c(a.node,a.offset,h.focusNode,h.focusOffset))return;let u=!1;if((d.extend||e==t)&&!(p&&A)){d.collapse(l.node,l.offset);try{e!=t&&d.extend(a.node,a.offset),u=!0}catch(e){}}if(!u){if(e>t){let e=l;l=a,a=e}let n=document.createRange();n.setEnd(a.node,a.offset),n.setStart(l.node,l.offset),d.removeAllRanges(),d.addRange(n)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let i=this.children[r],o=n+i.size;if(n==o?e<=o&&t>=n:e<o&&t>n){let r=n+i.border,s=o-i.border;if(e>=r&&t<=s){this.dirty=e==n||t==o?2:1,e==r&&t==s&&(i.contentLost||i.dom.parentNode!=this.contentDOM)?i.dirty=3:i.markDirty(e-r,t-r);return}i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=o}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}}class et extends ee{constructor(e,t,n,r){let i,o=t.type.toDOM;if("function"==typeof o&&(o=o(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:r)),!t.type.spec.raw){if(1!=o.nodeType){let e=document.createElement("span");e.appendChild(o),o=e}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class en extends ee{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class er extends ee{constructor(e,t,n,r,i){super(e,[],n,r),this.mark=t,this.spec=i}static create(e,t,n,r){let o=r.nodeViews[t.type.name],s=o&&o(t,r,n);return s&&s.dom||(s=i.ZF.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new er(e,t,s.dom,s.contentDOM||s.dom,s)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let e=this.parent;for(;!e.node;)e=e.parent;e.dirty<this.dirty&&(e.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=er.create(this.parent,this.mark,!0,n),i=this.children,o=this.size;t<o&&(i=eb(i,t,o,n)),e>0&&(i=eb(i,0,e,n));for(let e=0;e<i.length;e++)i[e].parent=r;return r.children=i,r}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class ei extends ee{constructor(e,t,n,r,i,o,s,l,a){super(e,[],i,o),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(e,t,n,r,o,s){let l=o.nodeViews[t.type.name],a,d=l&&l(t,o,()=>a?a.parent?a.parent.posBeforeChild(a):void 0:s,n,r),h=d&&d.dom,c=d&&d.contentDOM;if(t.isText)if(h){if(3!=h.nodeType)throw RangeError("Text must be rendered as a DOM text node")}else h=document.createTextNode(t.text);else if(!h){let e=i.ZF.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs);({dom:h,contentDOM:c}=e)}c||t.isText||"BR"==h.nodeName||(h.hasAttribute("contenteditable")||(h.contentEditable="false"),t.type.spec.draggable&&(h.draggable=!0));let p=h;return(h=eu(h,n,t),d)?a=new ea(e,t,n,r,h,c||null,p,d,o,s+1):t.isText?new es(e,t,n,r,h,p,o):new ei(e,t,n,r,h,c||null,p,o,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>i.FK.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&ef(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return+!this.node.isLeaf}updateChildren(e,t){let n=this.node.inlineContent,r=t,o=e.composing?this.localCompositionInfo(e,t):null,s=o&&o.pos>-1?o:null,l=o&&o.pos<0,a=new eg(this,s&&s.node,e);(function(e,t,n,r){let i=t.locals(e),o=0;if(0==i.length){for(let n=0;n<e.childCount;n++){let s=e.child(n);r(s,i,t.forChild(o,s),n),o+=s.nodeSize}return}let s=0,l=[],a=null;for(let d=0;;){let h,c,p,u;for(;s<i.length&&i[s].to==o;){let e=i[s++];e.widget&&(h?(c||(c=[h])).push(e):h=e)}if(h)if(c){c.sort(ey);for(let e=0;e<c.length;e++)n(c[e],d,!!a)}else n(h,d,!!a);if(a)u=-1,p=a,a=null;else if(d<e.childCount)u=d,p=e.child(d++);else break;for(let e=0;e<l.length;e++)l[e].to<=o&&l.splice(e--,1);for(;s<i.length&&i[s].from<=o&&i[s].to>o;)l.push(i[s++]);let f=o+p.nodeSize;if(p.isText){let e=f;s<i.length&&i[s].from<e&&(e=i[s].from);for(let t=0;t<l.length;t++)l[t].to<e&&(e=l[t].to);e<f&&(a=p.cut(e-o),p=p.cut(0,e-o),f=e,u=-1)}else for(;s<i.length&&i[s].to<f;)s++;let m=p.isInline&&!p.isLeaf?l.filter(e=>!e.inline):l.slice();r(p,m,t.forChild(o,p),u),o=f}})(this.node,this.innerDeco,(t,o,s)=>{t.spec.marks?a.syncToMarks(t.spec.marks,n,e):t.type.side>=0&&!s&&a.syncToMarks(o==this.node.childCount?i.CU.none:this.node.child(o).marks,n,e),a.placeWidget(t,e,r)},(t,i,s,d)=>{let h;a.syncToMarks(t.marks,n,e),a.findNodeMatch(t,i,s,d)||l&&e.state.selection.from>r&&e.state.selection.to<r+t.nodeSize&&(h=a.findIndexWithChild(o.node))>-1&&a.updateNodeAt(t,i,s,h,e)||a.updateNextNode(t,i,s,e,d,r)||a.addNode(t,i,s,e,r),r+=t.nodeSize}),a.syncToMarks([],n,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||2==this.dirty)&&(s&&this.protectLocalComposition(e,s),function e(t,n,r){let i=t.firstChild,o=!1;for(let s=0;s<n.length;s++){let l=n[s],a=l.dom;if(a.parentNode==t){for(;a!=i;)i=em(i),o=!0;i=i.nextSibling}else o=!0,t.insertBefore(a,i);if(l instanceof er){let n=i?i.previousSibling:t.lastChild;e(l.contentDOM,l.children,r),i=n?n.nextSibling:t.firstChild}}for(;i;)i=em(i),o=!0;o&&r.trackWrites==t&&(r.trackWrites=null)}(this.contentDOM,this.children,e),D&&function(e){if("UL"==e.nodeName||"OL"==e.nodeName){let t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:i}=e.state.selection;if(!(e.state.selection instanceof r.U3)||n<t||i>t+this.node.content.size)return null;let o=e.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(!this.node.inlineContent)return{node:o,pos:-1,text:""};{let e=o.nodeValue,r=function(e,t,n,r){for(let i=0,o=0;i<e.childCount&&o<=r;){let s=e.child(i++),l=o;if(o+=s.nodeSize,!s.isText)continue;let a=s.text;for(;i<e.childCount;){let t=e.child(i++);if(o+=t.nodeSize,!t.isText)break;a+=t.text}if(o>=n){if(o>=r&&a.slice(r-t.length-l,r-l)==t)return r-t.length;let e=l<r?a.lastIndexOf(t,r-l-1):-1;if(e>=0&&e+t.length+l>=n)return l+e;if(n==r&&a.length>=r+t.length-l&&a.slice(r-l,r-l+t.length)==t)return r}}return -1}(this.node.content,e,n-t,i-t);return r<0?null:{node:o,pos:r,text:e}}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let o=new en(this,i,t,r);e.input.compositionNodes.push(o),this.children=eb(this.children,n,n+r.length,e,o)}update(e,t,n,r){return 3!=this.dirty&&!!e.sameMarkup(this.node)&&(this.updateInner(e,t,n,r),!0)}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(ef(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=ep(this.dom,this.nodeDOM,ec(this.outerDeco,this.node,t),ec(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function eo(e,t,n,r,i){eu(r,t,e);let o=new ei(void 0,e,t,n,r,r,r,i,0);return o.contentDOM&&o.updateChildren(i,0),o}class es extends ei{constructor(e,t,n,r,i,o,s){super(e,t,n,r,i,null,o,s,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return 3!=this.dirty&&(0==this.dirty||!!this.inParent())&&!!e.sameMarkup(this.node)&&(this.updateOuterDeco(t),(0!=this.dirty||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let r=this.node.cut(e,t),i=document.createTextNode(r.text);return new es(this.parent,r,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(0==e||t==this.nodeDOM.nodeValue.length)&&(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class el extends ee{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class ea extends ei{constructor(e,t,n,r,i,o,s,l,a,d){super(e,t,n,r,i,o,s,a,d),this.spec=l}update(e,t,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,r),i}return(!!this.contentDOM||!!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}let ed=function(e){e&&(this.nodeName=e)};ed.prototype=Object.create(null);let eh=[new ed];function ec(e,t,n){if(0==e.length)return eh;let r=n?eh[0]:new ed,i=[r];for(let o=0;o<e.length;o++){let s=e[o].type.attrs;if(s)for(let e in s.nodeName&&i.push(r=new ed(s.nodeName)),s){let o=s[e];null!=o&&(n&&1==i.length&&i.push(r=new ed(t.isInline?"span":"div")),"class"==e?r.class=(r.class?r.class+" ":"")+o:"style"==e?r.style=(r.style?r.style+";":"")+o:"nodeName"!=e&&(r[e]=o))}}return i}function ep(e,t,n,r){if(n==eh&&r==eh)return t;let i=t;for(let t=0;t<r.length;t++){let o=r[t],s=n[t];if(t){let t;s&&s.nodeName==o.nodeName&&i!=e&&(t=i.parentNode)&&t.nodeName.toLowerCase()==o.nodeName||((t=document.createElement(o.nodeName)).pmIsDeco=!0,t.appendChild(i),s=eh[0]),i=t}!function(e,t,n){for(let r in t)"class"==r||"style"==r||"nodeName"==r||r in n||e.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=t[r]&&e.setAttribute(r,n[r]);if(t.class!=n.class){let r=t.class?t.class.split(" ").filter(Boolean):[],i=n.class?n.class.split(" ").filter(Boolean):[];for(let t=0;t<r.length;t++)-1==i.indexOf(r[t])&&e.classList.remove(r[t]);for(let t=0;t<i.length;t++)-1==r.indexOf(i[t])&&e.classList.add(i[t]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style){let n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,r;for(;r=n.exec(t.style);)e.style.removeProperty(r[1])}n.style&&(e.style.cssText+=n.style)}}(i,s||eh[0],o)}return i}function eu(e,t,n){return ep(e,e,eh,ec(t,n,1!=e.nodeType))}function ef(e,t){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function em(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}class eg{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(e,t){let n=t,r=n.children.length,i=e.childCount,o=new Map,s=[];e:for(;i>0;){let l;for(;;)if(r){let e=n.children[r-1];if(e instanceof er)n=e,r=e.children.length;else{l=e,r--;break}}else if(n==t)break e;else r=n.parent.children.indexOf(n),n=n.parent;let a=l.node;if(a){if(a!=e.child(i-1))break;--i,o.set(l,i),s.push(l)}}return{index:i,matched:o,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,i=this.stack.length>>1,o=Math.min(i,e.length);for(;r<o&&(r==i-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&!1!==e[r].type.spec.spanning;)r++;for(;r<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let t=this.index;t<Math.min(this.index+3,this.top.children.length);t++){let n=this.top.children[t];if(n.matchesMark(e[i])&&!this.isLocked(n.dom)){r=t;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=er.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,r){let i=-1,o;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))i=this.top.children.indexOf(o,this.index);else for(let r=this.index,o=Math.min(this.top.children.length,r+5);r<o;r++){let o=this.top.children[r];if(o.matchesNode(e,t,n)&&!this.preMatch.matched.has(o)){i=r;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,t,n,r,i){let o=this.top.children[r];return 3==o.dirty&&o.dom==o.contentDOM&&(o.dirty=2),!!o.update(e,t,n,i)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return -1;if(t==this.top.contentDOM){let t=e.pmViewDesc;if(t){for(let e=this.index;e<this.top.children.length;e++)if(this.top.children[e]==t)return e}return -1}e=t}}updateNextNode(e,t,n,r,i,o){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof ei){let a=this.preMatch.matched.get(l);if(null!=a&&a!=i)return!1;let d=l.dom,h,c=this.isLocked(d)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&3!=l.dirty&&ef(t,l.outerDeco));if(!c&&l.update(e,t,n,r))return this.destroyBetween(this.index,s),l.dom!=d&&(this.changed=!0),this.index++,!0;if(!c&&(h=this.recreateWrapper(l,e,t,n,r,o)))return this.destroyBetween(this.index,s),this.top.children[this.index]=h,h.contentDOM&&(h.dirty=2,h.updateChildren(r,o+1),h.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,i,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!ef(n,e.outerDeco)||!r.eq(e.innerDeco))return null;let s=ei.create(this.top,t,n,r,i,o);if(s.contentDOM)for(let t of(s.children=e.children,e.children=[],s.children))t.parent=s;return e.destroy(),s}addNode(e,t,n,r,i){let o=ei.create(this.top,e,t,n,r,i);o.contentDOM&&o.updateChildren(r,i+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(r&&r.matchesWidget(e)&&(e==r.widget||!r.widget.type.toDOM.parentNode))this.index++;else{let r=new et(this.top,e,t,n);this.top.children.splice(this.index++,0,r),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof er;)e=(t=e).children[t.children.length-1];(!e||!(e instanceof es)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((N||O)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let r=new el(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function ey(e,t){return e.type.side-t.type.side}function eb(e,t,n,r,i){let o=[];for(let s=0,l=0;s<e.length;s++){let a=e[s],d=l,h=l+=a.size;d>=n||h<=t?o.push(a):(d<t&&o.push(a.slice(0,t-d,r)),i&&(o.push(i),i=void 0),h>n&&o.push(a.slice(n-d,a.size,r)))}return o}function ev(e,t=null){let n=e.domSelectionRange(),i=e.state.doc;if(!n.focusNode)return null;let o=e.docView.nearestDesc(n.focusNode),l=o&&0==o.size,a=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(a<0)return null;let d=i.resolve(a),h,c;if(g(n)){for(h=a;o&&!o.node;)o=o.parent;let e=o.node;if(o&&e.isAtom&&r.nh.isSelectable(e)&&o.parent&&!(e.isInline&&function(e,t,n){for(let r=0==t,i=t==f(e);r||i;){if(e==n)return!0;let t=s(e);if(!(e=e.parentNode))return!1;r=r&&0==t,i=i&&t==f(e)}}(n.focusNode,n.focusOffset,o.dom))){let e=o.posBefore;c=new r.nh(a==e?d:i.resolve(e))}}else{if(n instanceof e.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let t=a,r=a;for(let i=0;i<n.rangeCount;i++){let o=n.getRangeAt(i);t=Math.min(t,e.docView.posFromDOM(o.startContainer,o.startOffset,1)),r=Math.max(r,e.docView.posFromDOM(o.endContainer,o.endOffset,-1))}if(t<0)return null;[h,a]=r==e.state.selection.anchor?[r,t]:[t,r],d=i.resolve(a)}else h=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(h<0)return null}let p=i.resolve(h);if(!c){let n="pointer"==t||e.state.selection.head<d.pos&&!l?1:-1;c=eO(e,p,d,n)}return c}function ew(e){return e.editable?e.hasFocus():eN(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function ek(e,t=!1){let n=e.state.selection;if(eA(e,n),ew(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&O){let t=e.domSelectionRange(),n=e.domObserver.currentSelection;if(t.anchorNode&&n.anchorNode&&c(t.anchorNode,t.anchorOffset,n.anchorNode,n.anchorOffset)){e.input.mouseDown.delayedSelectionSync=!0,e.domObserver.setCurSelection();return}}if(e.domObserver.disconnectSelection(),e.cursorWrapper)!function(e){let t=e.domSelection();if(!t)return;let n=e.cursorWrapper.dom,r="IMG"==n.nodeName;r?t.collapse(n.parentNode,s(n)+1):t.collapse(n,0),!r&&!e.state.selection.visible&&M&&C<=11&&(n.disabled=!0,n.disabled=!1)}(e);else{var i;let o,s,l,a,{anchor:d,head:h}=n,c,p;ex&&!(n instanceof r.U3)&&(n.$from.parent.inlineContent||(c=eS(e,n.from)),n.empty||n.$from.parent.inlineContent||(p=eS(e,n.to))),e.docView.setSelection(d,h,e,t),ex&&(c&&eC(c),p&&eC(p)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&((o=(i=e).dom.ownerDocument).removeEventListener("selectionchange",i.input.hideSelectionGuard),l=(s=i.domSelectionRange()).anchorNode,a=s.anchorOffset,o.addEventListener("selectionchange",i.input.hideSelectionGuard=()=>{(s.anchorNode!=l||s.anchorOffset!=a)&&(o.removeEventListener("selectionchange",i.input.hideSelectionGuard),setTimeout(()=>{(!ew(i)||i.state.selection.visible)&&i.dom.classList.remove("ProseMirror-hideselection")},20))})))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}let ex=N||O&&E<63;function eS(e,t){let{node:n,offset:r}=e.docView.domFromPos(t,0),i=r<n.childNodes.length?n.childNodes[r]:null,o=r?n.childNodes[r-1]:null;if(N&&i&&"false"==i.contentEditable)return eM(i);if((!i||"false"==i.contentEditable)&&(!o||"false"==o.contentEditable)){if(i)return eM(i);else if(o)return eM(o)}}function eM(e){return e.contentEditable="true",N&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function eC(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function eA(e,t){if(t instanceof r.nh){let n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(eT(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else eT(e)}function eT(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function eO(e,t,n,i){return e.someProp("createSelectionBetween",r=>r(e,t,n))||r.U3.between(t,n,i)}function eE(e){return(!e.editable||!!e.hasFocus())&&eN(e)}function eN(e){let t=e.domSelectionRange();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(e){return!1}}function eD(e,t){let{$anchor:n,$head:i}=e.selection,o=t>0?n.max(i):n.min(i),s=o.parent.inlineContent?o.depth?e.doc.resolve(t>0?o.after():o.before()):null:o;return s&&r.LN.findFrom(s,t)}function eR(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function eL(e,t,n){let i=e.state.selection;if(i instanceof r.U3){if(n.indexOf("s")>-1){let{$head:n}=i,o=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let s=e.state.doc.resolve(n.pos+o.nodeSize*(t<0?-1:1));return eR(e,new r.U3(i.$anchor,s))}else if(!i.empty)return!1;else if(e.endOfTextblock(t>0?"forward":"backward")){let n=eD(e.state,t);return!!n&&n instanceof r.nh&&eR(e,n)}else if(!(R&&n.indexOf("m")>-1)){let n=i.$head,o=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter,s;if(!o||o.isText)return!1;let l=t<0?n.pos-o.nodeSize:n.pos;return!!(o.isAtom||(s=e.docView.descAt(l))&&!s.contentDOM)&&(r.nh.isSelectable(o)?eR(e,new r.nh(t<0?e.state.doc.resolve(n.pos-o.nodeSize):n)):!!P&&eR(e,new r.U3(e.state.doc.resolve(t<0?l:l+o.nodeSize))))}}else{if(i instanceof r.nh&&i.node.isInline)return eR(e,new r.U3(t>0?i.$to:i.$from));let n=eD(e.state,t);return!!n&&eR(e,n)}}function eI(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function eP(e,t){let n=e.pmViewDesc;return n&&0==n.size&&(t<0||e.nextSibling||"BR"!=e.nodeName)}function ez(e,t){return t<0?function(e){let t=e.domSelectionRange(),n=t.focusNode,r=t.focusOffset;if(!n)return;let i,o,l=!1;for(A&&1==n.nodeType&&r<eI(n)&&eP(n.childNodes[r],-1)&&(l=!0);;)if(r>0)if(1!=n.nodeType)break;else{let e=n.childNodes[r-1];if(eP(e,-1))i=n,o=--r;else if(3==e.nodeType)r=(n=e).nodeValue.length;else break}else if(e$(n))break;else{let t=n.previousSibling;for(;t&&eP(t,-1);)i=n.parentNode,o=s(t),t=t.previousSibling;if(t)r=eI(n=t);else{if((n=n.parentNode)==e.dom)break;r=0}}l?eB(e,n,r):i&&eB(e,i,o)}(e):function(e){let t,n,r=e.domSelectionRange(),i=r.focusNode,o=r.focusOffset;if(!i)return;let l=eI(i);for(;;)if(o<l){if(1!=i.nodeType)break;if(eP(i.childNodes[o],1))t=i,n=++o;else break}else if(e$(i))break;else{let r=i.nextSibling;for(;r&&eP(r,1);)t=r.parentNode,n=s(r)+1,r=r.nextSibling;if(r)o=0,l=eI(i=r);else{if((i=i.parentNode)==e.dom)break;o=l=0}}t&&eB(e,t,n)}(e)}function e$(e){let t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function eB(e,t,n){if(3!=t.nodeType){let e,r;(r=function(e,t){for(;e&&t==e.childNodes.length&&!m(e);)t=s(e)+1,e=e.parentNode;for(;e&&t<e.childNodes.length;){let n=e.childNodes[t];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=n,t=0}}(t,n))?(t=r,n=0):(e=function(e,t){for(;e&&!t&&!m(e);)t=s(e),e=e.parentNode;for(;e&&t;){let n=e.childNodes[t-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=(e=n).childNodes.length}}(t,n))&&(t=e,n=e.nodeValue.length)}let r=e.domSelection();if(!r)return;if(g(r)){let e=document.createRange();e.setEnd(t,n),e.setStart(t,n),r.removeAllRanges(),r.addRange(e)}else r.extend&&r.extend(t,n);e.domObserver.setCurSelection();let{state:i}=e;setTimeout(()=>{e.state==i&&ek(e)},50)}function eF(e,t){let n=e.state.doc.resolve(t);if(!(O||L)&&n.parent.inlineContent){let r=e.coordsAtPos(t);if(t>n.start()){let n=e.coordsAtPos(t-1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(t<n.end()){let n=e.coordsAtPos(t+1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(e.dom).direction?"rtl":"ltr"}function ej(e,t,n){let i=e.state.selection;if(i instanceof r.U3&&!i.empty||n.indexOf("s")>-1||R&&n.indexOf("m")>-1)return!1;let{$from:o,$to:s}=i;if(!o.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){let n=eD(e.state,t);if(n&&n instanceof r.nh)return eR(e,n)}if(!o.parent.inlineContent){let n=t<0?o:s,l=i instanceof r.i5?r.LN.near(n,t):r.LN.findFrom(n,t);return!!l&&eR(e,l)}return!1}function eV(e,t){if(!(e.state.selection instanceof r.U3))return!0;let{$head:n,$anchor:i,empty:o}=e.state.selection;if(!n.sameParent(i))return!0;if(!o)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;let s=!n.textOffset&&(t<0?n.nodeBefore:n.nodeAfter);if(s&&!s.isText){let r=e.state.tr;return t<0?r.delete(n.pos-s.nodeSize,n.pos):r.delete(n.pos,n.pos+s.nodeSize),e.dispatch(r),!0}return!1}function eH(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function eK(e,t){e.someProp("transformCopied",n=>{t=n(t,e)});let n=[],{content:r,openStart:o,openEnd:s}=t;for(;o>1&&s>1&&1==r.childCount&&1==r.firstChild.childCount;){o--,s--;let e=r.firstChild;n.push(e.type.name,e.attrs!=e.type.defaultAttrs?e.attrs:null),r=e.content}let l=e.someProp("clipboardSerializer")||i.ZF.fromSchema(e.state.schema),a=eX(),d=a.createElement("div");d.appendChild(l.serializeFragment(r,{document:a}));let h=d.firstChild,c,p=0;for(;h&&1==h.nodeType&&(c=eG[h.nodeName.toLowerCase()]);){for(let e=c.length-1;e>=0;e--){let t=a.createElement(c[e]);for(;d.firstChild;)t.appendChild(d.firstChild);d.appendChild(t),p++}h=d.firstChild}return h&&1==h.nodeType&&h.setAttribute("data-pm-slice",`${o} ${s}${p?` -${p}`:""} ${JSON.stringify(n)}`),{dom:d,text:e.someProp("clipboardTextSerializer",n=>n(t,e))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function eJ(e,t,n,r,o){let s,l,a=o.parent.type.spec.code;if(!n&&!t)return null;let d=!!t&&(r||a||!n);if(d){if(e.someProp("transformPastedText",n=>{t=n(t,a||r,e)}),a)return l=new i.Ji(i.FK.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0),e.someProp("transformPasted",t=>{l=t(l,e,!0)}),l;let n=e.someProp("clipboardTextParser",n=>n(t,o,r,e));if(n)l=n;else{let n=o.marks(),{schema:r}=e.state,l=i.ZF.fromSchema(r);s=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach(e=>{let t=s.appendChild(document.createElement("p"));e&&t.appendChild(l.serializeNode(r.text(e,n)))})}}else e.someProp("transformPastedHTML",t=>{n=t(n,e)}),s=function(e){var t;let n,r=/^(\s*<meta [^>]*>)*/.exec(e);r&&(e=e.slice(r[0].length));let i=eX().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(e),s;if((s=o&&eG[o[1].toLowerCase()])&&(e=s.map(e=>"<"+e+">").join("")+e+s.map(e=>"</"+e+">").reverse().join("")),i.innerHTML=(t=e,(n=window.trustedTypes)?(eZ||(eZ=n.defaultPolicy||n.createPolicy("ProseMirrorClipboard",{createHTML:e=>e})),eZ.createHTML(t)):t),s)for(let e=0;e<s.length;e++)i=i.querySelector(s[e])||i;return i}(n),P&&function(e){let t=e.querySelectorAll(O?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<t.length;n++){let r=t[n];1==r.childNodes.length&&"\xa0"==r.textContent&&r.parentNode&&r.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),r)}}(s);let h=s&&s.querySelector("[data-pm-slice]"),c=h&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(h.getAttribute("data-pm-slice")||"");if(c&&c[3])for(let e=+c[3];e>0;e--){let e=s.firstChild;for(;e&&1!=e.nodeType;)e=e.nextSibling;if(!e)break;s=e}if(l||(l=(e.someProp("clipboardParser")||e.someProp("domParser")||i.S4.fromSchema(e.state.schema)).parseSlice(s,{preserveWhitespace:!!(d||c),context:o,ruleFromNode:e=>"BR"!=e.nodeName||e.nextSibling||!e.parentNode||eW.test(e.parentNode.nodeName)?null:{ignore:!0}})),c)l=function(e,t){if(!e.size)return e;let n=e.content.firstChild.type.schema,r;try{r=JSON.parse(t)}catch(t){return e}let{content:o,openStart:s,openEnd:l}=e;for(let e=r.length-2;e>=0;e-=2){let t=n.nodes[r[e]];if(!t||t.hasRequiredAttrs())break;o=i.FK.from(t.create(r[e+1],o)),s++,l++}return new i.Ji(o,s,l)}(eq(l,+c[1],+c[2]),c[4]);else if((l=i.Ji.maxOpen(function(e,t){if(e.childCount<2)return e;for(let n=t.depth;n>=0;n--){let r=t.node(n).contentMatchAt(t.index(n)),o,s=[];if(e.forEach(e=>{if(!s)return;let t=r.findWrapping(e.type),n;if(!t)return s=null;if(n=s.length&&o.length&&function e(t,n,r,o,s){if(s<t.length&&s<n.length&&t[s]==n[s]){let l=e(t,n,r,o.lastChild,s+1);if(l)return o.copy(o.content.replaceChild(o.childCount-1,l));if(o.contentMatchAt(o.childCount).matchType(s==t.length-1?r.type:t[s+1]))return o.copy(o.content.append(i.FK.from(e_(r,t,s+1))))}}(t,o,e,s[s.length-1],0))s[s.length-1]=n;else{s.length&&(s[s.length-1]=function e(t,n){if(0==n)return t;let r=t.content.replaceChild(t.childCount-1,e(t.lastChild,n-1)),o=t.contentMatchAt(t.childCount).fillBefore(i.FK.empty,!0);return t.copy(r.append(o))}(s[s.length-1],o.length));let n=e_(e,t);s.push(n),r=r.matchType(n.type),o=t}}),s)return i.FK.from(s)}return e}(l.content,o),!0)).openStart||l.openEnd){let e=0,t=0;for(let t=l.content.firstChild;e<l.openStart&&!t.type.spec.isolating;e++,t=t.firstChild);for(let e=l.content.lastChild;t<l.openEnd&&!e.type.spec.isolating;t++,e=e.lastChild);l=eq(l,e,t)}return e.someProp("transformPasted",t=>{l=t(l,e,d)}),l}let eW=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function e_(e,t,n=0){for(let r=t.length-1;r>=n;r--)e=t[r].create(null,i.FK.from(e));return e}function eU(e,t,n,r,o,s){let l=t<0?e.firstChild:e.lastChild,a=l.content;return e.childCount>1&&(s=0),o<r-1&&(a=eU(a,t,n,r,o+1,s)),o>=n&&(a=t<0?l.contentMatchAt(0).fillBefore(a,s<=o).append(a):a.append(l.contentMatchAt(l.childCount).fillBefore(i.FK.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,l.copy(a))}function eq(e,t,n){return t<e.openStart&&(e=new i.Ji(eU(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new i.Ji(eU(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}let eG={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},eY=null;function eX(){return eY||(eY=document.implementation.createHTMLDocument("title"))}let eZ=null,eQ={},e0={},e1={touchstart:!0,touchmove:!0};class e2{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function e3(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function e4(e){e.someProp("handleDOMEvents",t=>{for(let n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=t=>e5(e,t))})}function e5(e,t){return e.someProp("handleDOMEvents",n=>{let r=n[t.type];return!!r&&(r(e,t)||t.defaultPrevented)})}function e6(e){return{left:e.clientX,top:e.clientY}}function e7(e,t,n,r,i){if(-1==r)return!1;let o=e.state.doc.resolve(r);for(let r=o.depth+1;r>0;r--)if(e.someProp(t,t=>r>o.depth?t(e,n,o.nodeAfter,o.before(r),i,!0):t(e,n,o.node(r),o.before(r),i,!1)))return!0;return!1}function e9(e,t,n){if(e.focused||e.focus(),e.state.selection.eq(t))return;let r=e.state.tr.setSelection(t);"pointer"==n&&r.setMeta("pointer",!0),e.dispatch(r)}e0.keydown=(e,t)=>{if((e.input.shiftKey=16==t.keyCode||t.shiftKey,!tt(e,t))&&(e.input.lastKeyCode=t.keyCode,e.input.lastKeyCodeTime=Date.now(),!I||!O||13!=t.keyCode))if(229!=t.keyCode&&e.domObserver.forceFlush(),!D||13!=t.keyCode||t.ctrlKey||t.altKey||t.metaKey)e.someProp("handleKeyDown",n=>n(e,t))||function(e,t){let n,r=t.keyCode,i=(n="",t.ctrlKey&&(n+="c"),t.metaKey&&(n+="m"),t.altKey&&(n+="a"),t.shiftKey&&(n+="s"),n);if(8==r||R&&72==r&&"c"==i)return eV(e,-1)||ez(e,-1);if(46==r&&!t.shiftKey||R&&68==r&&"c"==i)return eV(e,1)||ez(e,1);if(13==r||27==r)return!0;if(37==r||R&&66==r&&"c"==i){let t=37==r?"ltr"==eF(e,e.state.selection.from)?-1:1:-1;return eL(e,t,i)||ez(e,t)}if(39==r||R&&70==r&&"c"==i){let t=39==r?"ltr"==eF(e,e.state.selection.from)?1:-1:1;return eL(e,t,i)||ez(e,t)}else if(38==r||R&&80==r&&"c"==i)return ej(e,-1,i)||ez(e,-1);else if(40==r||R&&78==r&&"c"==i)return function(e){if(!N||e.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:n}=e.domSelectionRange();if(t&&1==t.nodeType&&0==n&&t.firstChild&&"false"==t.firstChild.contentEditable){let n=t.firstChild;eH(e,n,"true"),setTimeout(()=>eH(e,n,"false"),20)}return!1}(e)||ej(e,1,i)||ez(e,1);else if(i==(R?"m":"c")&&(66==r||73==r||89==r||90==r))return!0;return!1}(e,t)?t.preventDefault():e3(e,"key");else{let t=Date.now();e.input.lastIOSEnter=t,e.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{e.input.lastIOSEnter==t&&(e.someProp("handleKeyDown",t=>t(e,y(13,"Enter"))),e.input.lastIOSEnter=0)},200)}},e0.keyup=(e,t)=>{16==t.keyCode&&(e.input.shiftKey=!1)},e0.keypress=(e,t)=>{if(tt(e,t)||!t.charCode||t.ctrlKey&&!t.altKey||R&&t.metaKey)return;if(e.someProp("handleKeyPress",n=>n(e,t)))return void t.preventDefault();let n=e.state.selection;if(!(n instanceof r.U3)||!n.$from.sameParent(n.$to)){let r=String.fromCharCode(t.charCode),i=()=>e.state.tr.insertText(r).scrollIntoView();/[\r\n]/.test(r)||e.someProp("handleTextInput",t=>t(e,n.$from.pos,n.$to.pos,r,i))||e.dispatch(i()),t.preventDefault()}};let e8=R?"metaKey":"ctrlKey";eQ.mousedown=(e,t)=>{e.input.shiftKey=t.shiftKey;let n=to(e),i=Date.now(),o="singleClick";i-e.input.lastClick.time<500&&function(e,t){let n=t.x-e.clientX,r=t.y-e.clientY;return n*n+r*r<100}(t,e.input.lastClick)&&!t[e8]&&e.input.lastClick.button==t.button&&("singleClick"==e.input.lastClick.type?o="doubleClick":"doubleClick"==e.input.lastClick.type&&(o="tripleClick")),e.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:o,button:t.button};let s=e.posAtCoords(e6(t));s&&("singleClick"==o?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new te(e,s,t,!!n)):("doubleClick"==o?function(e,t,n,r){return e7(e,"handleDoubleClickOn",t,n,r)||e.someProp("handleDoubleClick",n=>n(e,t,r))}:function(e,t,n,i){return e7(e,"handleTripleClickOn",t,n,i)||e.someProp("handleTripleClick",n=>n(e,t,i))||function(e,t,n){if(0!=n.button)return!1;let i=e.state.doc;if(-1==t)return!!i.inlineContent&&(e9(e,r.U3.create(i,0,i.content.size),"pointer"),!0);let o=i.resolve(t);for(let t=o.depth+1;t>0;t--){let n=t>o.depth?o.nodeAfter:o.node(t),s=o.before(t);if(n.inlineContent)e9(e,r.U3.create(i,s+1,s+1+n.content.size),"pointer");else{if(!r.nh.isSelectable(n))continue;e9(e,r.nh.create(i,s),"pointer")}return!0}}(e,n,i)})(e,s.pos,s.inside,t)?t.preventDefault():e3(e,"pointer"))};class te{constructor(e,t,n,i){let o,s;if(this.view=e,this.pos=t,this.event=n,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[e8],this.allowDefault=n.shiftKey,t.inside>-1)o=e.state.doc.nodeAt(t.inside),s=t.inside;else{let n=e.state.doc.resolve(t.pos);o=n.parent,s=n.depth?n.before():0}let l=i?null:n.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&1==a.dom.nodeType?a.dom:null;let{selection:d}=e.state;(0==n.button&&o.type.spec.draggable&&!1!==o.type.spec.selectable||d instanceof r.nh&&d.from<=s&&d.to>s)&&(this.mightDrag={node:o,pos:s,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&A&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),e3(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>ek(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;if(this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(e6(e))),this.updateAllowDefault(e),this.allowDefault||!t)e3(this.view,"pointer");else{var n,i,o,s;(n=this.view,i=t.pos,o=t.inside,s=this.selectNode,e7(n,"handleClickOn",i,o,e)||n.someProp("handleClick",t=>t(n,i,e))||(s?function(e,t){if(-1==t)return!1;let n=e.state.selection,i,o;n instanceof r.nh&&(i=n.node);let s=e.state.doc.resolve(t);for(let e=s.depth+1;e>0;e--){let t=e>s.depth?s.nodeAfter:s.node(e);if(r.nh.isSelectable(t)){o=i&&n.$from.depth>0&&e>=n.$from.depth&&s.before(n.$from.depth+1)==n.$from.pos?s.before(n.$from.depth):s.before(e);break}}return null!=o&&(e9(e,r.nh.create(e.state.doc,o),"pointer"),!0)}(n,o):function(e,t){if(-1==t)return!1;let n=e.state.doc.resolve(t),i=n.nodeAfter;return!!(i&&i.isAtom&&r.nh.isSelectable(i))&&(e9(e,new r.nh(n),"pointer"),!0)}(n,o)))?e.preventDefault():0==e.button&&(this.flushed||N&&this.mightDrag&&!this.mightDrag.node.isAtom||O&&!this.view.state.selection.visible&&2>=Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to)))?(e9(this.view,r.LN.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):e3(this.view,"pointer")}}move(e){this.updateAllowDefault(e),e3(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function tt(e,t){return!!e.composing||!!(N&&500>Math.abs(t.timeStamp-e.input.compositionEndedAt))&&(e.input.compositionEndedAt=-2e8,!0)}eQ.touchstart=e=>{e.input.lastTouch=Date.now(),to(e),e3(e,"pointer")},eQ.touchmove=e=>{e.input.lastTouch=Date.now(),e3(e,"pointer")},eQ.contextmenu=e=>to(e);let tn=I?5e3:-1;function tr(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout(()=>to(e),t))}function ti(e){let t;for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=((t=document.createEvent("Event")).initEvent("event",!0,!0),t.timeStamp));e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function to(e,t=!1){if(!I||!(e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),ti(e),t||e.docView&&e.docView.dirty){let n=ev(e),r=e.state.selection;return n&&!n.eq(r)?e.dispatch(e.state.tr.setSelection(n)):(e.markCursor||t)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?e.dispatch(e.state.tr.deleteSelection()):e.updateState(e.state),!0}return!1}}e0.compositionstart=e0.compositionupdate=e=>{if(!e.composing){e.domObserver.flush();let{state:t}=e,n=t.selection.$to;if(t.selection instanceof r.U3&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some(e=>!1===e.type.spec.inclusive)))e.markCursor=e.state.storedMarks||n.marks(),to(e,!0),e.markCursor=null;else if(to(e,!t.selection.empty),A&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let t=e.domSelectionRange();for(let n=t.focusNode,r=t.focusOffset;n&&1==n.nodeType&&0!=r;){let t=r<0?n.lastChild:n.childNodes[r-1];if(!t)break;if(3==t.nodeType){let n=e.domSelection();n&&n.collapse(t,t.nodeValue.length);break}n=t,r=-1}}e.input.composing=!0}tr(e,tn)},e0.compositionend=(e,t)=>{e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,e.input.compositionPendingChanges=e.domObserver.pendingRecords().length?e.input.compositionID:0,e.input.compositionNode=null,e.input.compositionPendingChanges&&Promise.resolve().then(()=>e.domObserver.flush()),e.input.compositionID++,tr(e,20))};let ts=M&&C<15||D&&z<604;function tl(e,t,n,r,o){let s=eJ(e,t,n,r,e.state.selection.$from);if(e.someProp("handlePaste",t=>t(e,o,s||i.Ji.empty)))return!0;if(!s)return!1;let l=0==s.openStart&&0==s.openEnd&&1==s.content.childCount?s.content.firstChild:null,a=l?e.state.tr.replaceSelectionWith(l,r):e.state.tr.replaceSelection(s);return e.dispatch(a.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function ta(e){let t=e.getData("text/plain")||e.getData("Text");if(t)return t;let n=e.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}eQ.copy=e0.cut=(e,t)=>{let n=e.state.selection,r="cut"==t.type;if(n.empty)return;let i=ts?null:t.clipboardData,{dom:o,text:s}=eK(e,n.content());i?(t.preventDefault(),i.clearData(),i.setData("text/html",o.innerHTML),i.setData("text/plain",s)):function(e,t){if(!e.dom.parentNode)return;let n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(t),e.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n),e.focus()},50)}(e,o),r&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},e0.paste=(e,t)=>{if(e.composing&&!I)return;let n=ts?null:t.clipboardData,r=e.input.shiftKey&&45!=e.input.lastKeyCode;n&&tl(e,ta(n),n.getData("text/html"),r,t)?t.preventDefault():function(e,t){if(!e.dom.parentNode)return;let n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,r=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=e.input.shiftKey&&45!=e.input.lastKeyCode;setTimeout(()=>{e.focus(),r.parentNode&&r.parentNode.removeChild(r),n?tl(e,r.value,null,i,t):tl(e,r.textContent,r.innerHTML,i,t)},50)}(e,t)};class td{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}let th=R?"altKey":"ctrlKey";function tc(e,t){let n=e.someProp("dragCopies",e=>!e(t));return null!=n?n:!t[th]}for(let e in eQ.dragstart=(e,t)=>{let n,i=e.input.mouseDown;if(i&&i.done(),!t.dataTransfer)return;let o=e.state.selection,s=o.empty?null:e.posAtCoords(e6(t));if(s&&s.pos>=o.from&&s.pos<=(o instanceof r.nh?o.to-1:o.to));else if(i&&i.mightDrag)n=r.nh.create(e.state.doc,i.mightDrag.pos);else if(t.target&&1==t.target.nodeType){let i=e.docView.nearestDesc(t.target,!0);i&&i.node.type.spec.draggable&&i!=e.docView&&(n=r.nh.create(e.state.doc,i.posBefore))}let l=(n||e.state.selection).content(),{dom:a,text:d,slice:h}=eK(e,l);t.dataTransfer.files.length&&O&&!(E>120)||t.dataTransfer.clearData(),t.dataTransfer.setData(ts?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",ts||t.dataTransfer.setData("text/plain",d),e.dragging=new td(h,tc(e,t),n)},eQ.dragend=e=>{let t=e.dragging;window.setTimeout(()=>{e.dragging==t&&(e.dragging=null)},50)},e0.dragover=e0.dragenter=(e,t)=>t.preventDefault(),e0.drop=(e,t)=>{let n=e.dragging;if(e.dragging=null,!t.dataTransfer)return;let s=e.posAtCoords(e6(t));if(!s)return;let l=e.state.doc.resolve(s.pos),a=n&&n.slice;a?e.someProp("transformPasted",t=>{a=t(a,e,!1)}):a=eJ(e,ta(t.dataTransfer),ts?null:t.dataTransfer.getData("text/html"),!1,l);let d=!!(n&&tc(e,t));if(e.someProp("handleDrop",n=>n(e,t,a||i.Ji.empty,d)))return void t.preventDefault();if(!a)return;t.preventDefault();let h=a?(0,o.Um)(e.state.doc,l.pos,a):l.pos;null==h&&(h=l.pos);let c=e.state.tr;if(d){let{node:e}=n;e?e.replace(c):c.deleteSelection()}let p=c.mapping.map(h),u=0==a.openStart&&0==a.openEnd&&1==a.content.childCount,f=c.doc;if(u?c.replaceRangeWith(p,p,a.content.firstChild):c.replaceRange(p,p,a),c.doc.eq(f))return;let m=c.doc.resolve(p);if(u&&r.nh.isSelectable(a.content.firstChild)&&m.nodeAfter&&m.nodeAfter.sameMarkup(a.content.firstChild))c.setSelection(new r.nh(m));else{let t=c.mapping.map(h);c.mapping.maps[c.mapping.maps.length-1].forEach((e,n,r,i)=>t=i),c.setSelection(eO(e,m,c.doc.resolve(t)))}e.focus(),e.dispatch(c.setMeta("uiEvent","drop"))},eQ.focus=e=>{e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout(()=>{e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelectionRange())&&ek(e)},20))},eQ.blur=(e,t)=>{e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),t.relatedTarget&&e.dom.contains(t.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},eQ.beforeinput=(e,t)=>{if(O&&I&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();let{domChangeCount:t}=e.input;setTimeout(()=>{if(e.input.domChangeCount!=t||(e.dom.blur(),e.focus(),e.someProp("handleKeyDown",t=>t(e,y(8,"Backspace")))))return;let{$cursor:n}=e.state.selection;n&&n.pos>0&&e.dispatch(e.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}},e0)eQ[e]=e0[e];function tp(e,t){if(e==t)return!0;for(let n in e)if(e[n]!==t[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}class tu{constructor(e,t){this.toDOM=e,this.spec=t||tb,this.side=this.spec.side||0}map(e,t,n,r){let{pos:i,deleted:o}=e.mapResult(t.from+r,this.side<0?-1:1);return o?null:new tg(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof tu&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&tp(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class tf{constructor(e,t){this.attrs=e,this.spec=t||tb}map(e,t,n,r){let i=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,o=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return i>=o?null:new tg(i,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof tf&&tp(this.attrs,e.attrs)&&tp(this.spec,e.spec)}static is(e){return e.type instanceof tf}destroy(){}}class tm{constructor(e,t){this.attrs=e,this.spec=t||tb}map(e,t,n,r){let i=e.mapResult(t.from+r,1);if(i.deleted)return null;let o=e.mapResult(t.to+r,-1);return o.deleted||o.pos<=i.pos?null:new tg(i.pos-n,o.pos-n,this)}valid(e,t){let{index:n,offset:r}=e.content.findIndex(t.from),i;return r==t.from&&!(i=e.child(n)).isText&&r+i.nodeSize==t.to}eq(e){return this==e||e instanceof tm&&tp(this.attrs,e.attrs)&&tp(this.spec,e.spec)}destroy(){}}class tg{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new tg(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new tg(e,e,new tu(t,n))}static inline(e,t,n,r){return new tg(e,t,new tf(n,r))}static node(e,t,n,r){return new tg(e,t,new tm(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof tf}get widget(){return this.type instanceof tu}}let ty=[],tb={};class tv{constructor(e,t){this.local=e.length?e:ty,this.children=t.length?t:ty}static create(e,t){return t.length?tC(t,e,0,tb):tw}find(e,t,n){let r=[];return this.findInner(null==e?0:e,null==t?1e9:t,r,0,n),r}findInner(e,t,n,r,i){for(let o=0;o<this.local.length;o++){let s=this.local[o];s.from<=t&&s.to>=e&&(!i||i(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let s=this.children[o]+1;this.children[o+2].findInner(e-s,t-s,n,r+s,i)}}map(e,t,n){return this==tw||0==e.maps.length?this:this.mapInner(e,t,0,0,n||tb)}mapInner(e,t,n,r,i){let o;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(e,n,r);l&&l.type.valid(t,l)?(o||(o=[])).push(l):i.onRemove&&i.onRemove(this.local[s].spec)}return this.children.length?function(e,t,n,r,i,o,s){let l=e.slice();for(let e=0,t=o;e<n.maps.length;e++){let r=0;n.maps[e].forEach((e,n,i,o)=>{let s=o-i-(n-e);for(let i=0;i<l.length;i+=3){let o=l[i+1];if(o<0||e>o+t-r)continue;let a=l[i]+t-r;n>=a?l[i+1]=e<=a?-2:-1:e>=t&&s&&(l[i]+=s,l[i+1]+=s)}r+=s}),t=n.maps[e].map(t,-1)}let a=!1;for(let t=0;t<l.length;t+=3)if(l[t+1]<0){if(-2==l[t+1]){a=!0,l[t+1]=-1;continue}let d=n.map(e[t]+o),h=d-i;if(h<0||h>=r.content.size){a=!0;continue}let c=n.map(e[t+1]+o,-1)-i,{index:p,offset:u}=r.content.findIndex(h),f=r.maybeChild(p);if(f&&u==h&&u+f.nodeSize==c){let r=l[t+2].mapInner(n,f,d+1,e[t]+o+1,s);r!=tw?(l[t]=h,l[t+1]=c,l[t+2]=r):(l[t+1]=-2,a=!0)}else a=!0}if(a){let a=tC(function(e,t,n,r,i,o,s){for(let l=0;l<e.length;l+=3)-1==e[l+1]&&function e(t,o){for(let e=0;e<t.local.length;e++){let l=t.local[e].map(r,i,o);l?n.push(l):s.onRemove&&s.onRemove(t.local[e].spec)}for(let n=0;n<t.children.length;n+=3)e(t.children[n+2],t.children[n]+o+1)}(e[l+2],t[l]+o+1);return n}(l,e,t,n,i,o,s),r,0,s);t=a.local;for(let e=0;e<l.length;e+=3)l[e+1]<0&&(l.splice(e,3),e-=3);for(let e=0,t=0;e<a.children.length;e+=3){let n=a.children[e];for(;t<l.length&&l[t]<n;)t+=3;l.splice(t,0,a.children[e],a.children[e+1],a.children[e+2])}}return new tv(t.sort(tA),l)}(this.children,o||[],e,t,n,r,i):o?new tv(o.sort(tA),ty):tw}add(e,t){return t.length?this==tw?tv.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,i=0;e.forEach((e,o)=>{let s=o+n,l;if(l=tS(t,e,s)){for(r||(r=this.children.slice());i<r.length&&r[i]<o;)i+=3;r[i]==o?r[i+2]=r[i+2].addInner(e,l,s+1):r.splice(i,0,o,o+e.nodeSize,tC(l,e,s+1,tb)),i+=3}});let o=tx(i?tM(t):t,-n);for(let t=0;t<o.length;t++)o[t].type.valid(e,o[t])||o.splice(t--,1);return new tv(o.length?this.local.concat(o).sort(tA):this.local,r||this.children)}remove(e){return 0==e.length||this==tw?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let i,o=n[r]+t,s=n[r+1]+t;for(let t=0,n;t<e.length;t++)(n=e[t])&&n.from>o&&n.to<s&&(e[t]=null,(i||(i=[])).push(n));if(!i)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(i,o+1);l!=tw?n[r+2]=l:(n.splice(r,3),r-=3)}if(r.length){for(let n=0,i;n<e.length;n++)if(i=e[n])for(let e=0;e<r.length;e++)r[e].eq(i,t)&&(r==this.local&&(r=this.local.slice()),r.splice(e--,1))}return n==this.children&&r==this.local?this:r.length||n.length?new tv(r,n):tw}forChild(e,t){let n,r;if(this==tw)return this;if(t.isLeaf)return tv.empty;for(let t=0;t<this.children.length;t+=3)if(this.children[t]>=e){this.children[t]==e&&(n=this.children[t+2]);break}let i=e+1,o=i+t.content.size;for(let e=0;e<this.local.length;e++){let t=this.local[e];if(t.from<o&&t.to>i&&t.type instanceof tf){let e=Math.max(i,t.from)-i,n=Math.min(o,t.to)-i;e<n&&(r||(r=[])).push(t.copy(e,n))}}if(r){let e=new tv(r.sort(tA),ty);return n?new tk([e,n]):e}return n||tw}eq(e){if(this==e)return!0;if(!(e instanceof tv)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return tT(this.localsInner(e))}localsInner(e){if(this==tw)return ty;if(e.inlineContent||!this.local.some(tf.is))return this.local;let t=[];for(let e=0;e<this.local.length;e++)this.local[e].type instanceof tf||t.push(this.local[e]);return t}forEachSet(e){e(this)}}tv.empty=new tv([],[]),tv.removeOverlap=tT;let tw=tv.empty;class tk{constructor(e){this.members=e}map(e,t){let n=this.members.map(n=>n.map(e,t,tb));return tk.from(n)}forChild(e,t){if(t.isLeaf)return tv.empty;let n=[];for(let r=0;r<this.members.length;r++){let i=this.members[r].forChild(e,t);i!=tw&&(i instanceof tk?n=n.concat(i.members):n.push(i))}return tk.from(n)}eq(e){if(!(e instanceof tk)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let i=this.members[r].localsInner(e);if(i.length)if(t){n&&(t=t.slice(),n=!1);for(let e=0;e<i.length;e++)t.push(i[e])}else t=i}return t?tT(n?t:t.sort(tA)):ty}static from(e){switch(e.length){case 0:return tw;case 1:return e[0];default:return new tk(e.every(e=>e instanceof tv)?e:e.reduce((e,t)=>e.concat(t instanceof tv?t:t.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function tx(e,t){if(!t||!e.length)return e;let n=[];for(let r=0;r<e.length;r++){let i=e[r];n.push(new tg(i.from+t,i.to+t,i.type))}return n}function tS(e,t,n){if(t.isLeaf)return null;let r=n+t.nodeSize,i=null;for(let t=0,o;t<e.length;t++)(o=e[t])&&o.from>n&&o.to<r&&((i||(i=[])).push(o),e[t]=null);return i}function tM(e){let t=[];for(let n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function tC(e,t,n,r){let i=[],o=!1;t.forEach((t,s)=>{let l=tS(e,t,s+n);if(l){o=!0;let e=tC(l,t,n+s+1,r);e!=tw&&i.push(s,s+t.nodeSize,e)}});let s=tx(o?tM(e):e,-n).sort(tA);for(let e=0;e<s.length;e++)s[e].type.valid(t,s[e])||(r.onRemove&&r.onRemove(s[e].spec),s.splice(e--,1));return s.length||i.length?new tv(s,i):tw}function tA(e,t){return e.from-t.from||e.to-t.to}function tT(e){let t=e;for(let n=0;n<t.length-1;n++){let r=t[n];if(r.from!=r.to)for(let i=n+1;i<t.length;i++){let o=t[i];if(o.from==r.from){o.to!=r.to&&(t==e&&(t=e.slice()),t[i]=o.copy(o.from,r.to),tO(t,i+1,o.copy(r.to,o.to)));continue}o.from<r.to&&(t==e&&(t=e.slice()),t[n]=r.copy(r.from,o.from),tO(t,i,r.copy(o.from,r.to)));break}}return t}function tO(e,t,n){for(;t<e.length&&tA(n,e[t])>0;)t++;e.splice(t,0,n)}function tE(e){let t=[];return e.someProp("decorations",n=>{let r=n(e.state);r&&r!=tw&&t.push(r)}),e.cursorWrapper&&t.push(tv.create(e.state.doc,[e.cursorWrapper.deco])),tk.from(t)}let tN={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},tD=M&&C<=11;class tR{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class tL{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new tR,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(e=>{for(let t=0;t<e.length;t++)this.queue.push(e[t]);M&&C<=11&&e.some(e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length)?this.flushSoon():this.flush()}),tD&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,tN)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(eE(this.view)){if(this.suppressingSelectionUpdates)return ek(this.view);if(M&&C<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&c(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,n;for(let n=e.focusNode;n;n=l(n))t.add(n);for(let r=e.anchorNode;r;r=l(r))if(t.has(r)){n=r;break}let r=n&&this.view.docView.nearestDesc(n);if(r&&r.ignoreMutation({type:"selection",target:3==n.nodeType?n.parentNode:n}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){var e;let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let n=this.pendingRecords();n.length&&(this.queue=[]);let i=t.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(i)&&eE(t)&&!this.ignoreSelectionChange(i),s=-1,l=-1,a=!1,d=[];if(t.editable)for(let e=0;e<n.length;e++){let t=this.registerMutation(n[e],d);t&&(s=s<0?t.from:Math.min(t.from,s),l=l<0?t.to:Math.max(t.to,l),t.typeOver&&(a=!0))}if(A&&d.length){let e=d.filter(e=>"BR"==e.nodeName);if(2==e.length){let[t,n]=e;t.parentNode&&t.parentNode.parentNode==n.parentNode?n.remove():t.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of e){let e=r.parentNode;e&&"LI"==e.nodeName&&(!n||function(e,t){for(let n=t.parentNode;n&&n!=e.dom;n=n.parentNode){let t=e.docView.nearestDesc(n,!0);if(t&&t.node.isBlock)return n}return null}(t,n)!=e)&&r.remove()}}}let h=null;s<0&&o&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&g(i)&&(h=ev(t))&&h.eq(r.LN.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,ek(t),this.currentSelection.set(i),t.scrollToSelection()):(s>-1||o)&&(s>-1&&(t.docView.markDirty(s,l),e=t,!tI.has(e)&&(tI.set(e,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace))&&(e.requiresGeckoHackNode=A,tP||(console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),tP=!0))),this.handleDOMChange(s,l,a,d),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(i)||ek(t),this.currentSelection.set(i))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let n=0;n<e.addedNodes.length;n++){let r=e.addedNodes[n];t.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,i=e.nextSibling;if(M&&C<=11&&e.addedNodes.length)for(let t=0;t<e.addedNodes.length;t++){let{previousSibling:n,nextSibling:o}=e.addedNodes[t];(!n||0>Array.prototype.indexOf.call(e.addedNodes,n))&&(r=n),(!o||0>Array.prototype.indexOf.call(e.addedNodes,o))&&(i=o)}let o=r&&r.parentNode==e.target?s(r)+1:0,l=n.localPosFromDOM(e.target,o,-1),a=i&&i.parentNode==e.target?s(i):e.target.childNodes.length;return{from:l,to:n.localPosFromDOM(e.target,a,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let tI=new WeakMap,tP=!1;function tz(e,t){let n=t.startContainer,r=t.startOffset,i=t.endContainer,o=t.endOffset,s=e.domAtPos(e.state.selection.anchor);return c(s.node,s.offset,i,o)&&([n,r,i,o]=[i,o,n,r]),{anchorNode:n,anchorOffset:r,focusNode:i,focusOffset:o}}function t$(e){let t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(N&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}else if(e.parentNode.lastChild==e||N&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}let tB=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|img|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function tF(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:eO(e,t.resolve(n.anchor),t.resolve(n.head))}function tj(e,t,n){let r=e.depth,i=t?e.end():e.pos;for(;r>0&&(t||e.indexAfter(r)==e.node(r).childCount);)r--,i++,t=!1;if(n){let t=e.node(r).maybeChild(e.indexAfter(r));for(;t&&!t.isLeaf;)t=t.firstChild,i++}return i}function tV(e){if(2!=e.length)return!1;let t=e.charCodeAt(0),n=e.charCodeAt(1);return t>=56320&&t<=57343&&n>=55296&&n<=56319}class tH{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new e2,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(tU),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=tW(this),tJ(this),this.nodeViews=t_(this),this.docView=eo(this.state.doc,tK(this),tE(this),this.dom,this),this.domObserver=new tL(this,(e,t,n,o)=>(function(e,t,n,o,s){let l,a,d,h,c=e.input.compositionPendingChanges||(e.composing?e.input.compositionID:0);if(e.input.compositionPendingChanges=0,t<0){let t=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,n=ev(e,t);if(n&&!e.state.selection.eq(n)){if(O&&I&&13===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime&&e.someProp("handleKeyDown",t=>t(e,y(13,"Enter"))))return;let r=e.state.tr.setSelection(n);"pointer"==t?r.setMeta("pointer",!0):"key"==t&&r.scrollIntoView(),c&&r.setMeta("composition",c),e.dispatch(r)}return}let p=e.state.doc.resolve(t),u=p.sharedDepth(n);t=p.before(u+1),n=e.state.doc.resolve(n).after(u+1);let f=e.state.selection,m=function(e,t,n){let r,{node:o,fromOffset:s,toOffset:l,from:a,to:d}=e.docView.parseRange(t,n),h=e.domSelectionRange(),c=h.anchorNode;if(c&&e.dom.contains(1==c.nodeType?c:c.parentNode)&&(r=[{node:c,offset:h.anchorOffset}],g(h)||r.push({node:h.focusNode,offset:h.focusOffset})),O&&8===e.input.lastKeyCode)for(let e=l;e>s;e--){let t=o.childNodes[e-1],n=t.pmViewDesc;if("BR"==t.nodeName&&!n){l=e;break}if(!n||n.size)break}let p=e.state.doc,u=e.someProp("domParser")||i.S4.fromSchema(e.state.schema),f=p.resolve(a),m=null,y=u.parse(o,{topNode:f.parent,topMatch:f.parent.contentMatchAt(f.index()),topOpen:!0,from:s,to:l,preserveWhitespace:"pre"!=f.parent.type.whitespace||"full",findPositions:r,ruleFromNode:t$,context:f});if(r&&null!=r[0].pos){let e=r[0].pos,t=r[1]&&r[1].pos;null==t&&(t=e),m={anchor:e+a,head:t+a}}return{doc:y,sel:m,from:a,to:d}}(e,t,n),b=e.state.doc,v=b.slice(m.from,m.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(l=e.state.selection.to,a="end"):(l=e.state.selection.from,a="start"),e.input.lastKeyCode=null;let w=function(e,t,n,r,i){let o=e.findDiffStart(t,n);if(null==o)return null;let{a:s,b:l}=e.findDiffEnd(t,n+e.size,n+t.size);if("end"==i){let e=Math.max(0,o-Math.min(s,l));r-=s+e-o}if(s<o&&e.size<t.size){let e=r<=o&&r>=s?o-r:0;(o-=e)&&o<t.size&&tV(t.textBetween(o-1,o+1))&&(o+=e?1:-1),l=o+(l-s),s=o}else if(l<o){let t=r<=o&&r>=l?o-r:0;(o-=t)&&o<e.size&&tV(e.textBetween(o-1,o+1))&&(o+=t?1:-1),s=o+(s-l),l=o}return{start:o,endA:s,endB:l}}(v.content,m.doc.content,m.from,l,a);if(w&&e.input.domChangeCount++,(D&&e.input.lastIOSEnter>Date.now()-225||I)&&s.some(e=>1==e.nodeType&&!tB.test(e.nodeName))&&(!w||w.endA>=w.endB)&&e.someProp("handleKeyDown",t=>t(e,y(13,"Enter")))){e.input.lastIOSEnter=0;return}if(!w)if(o&&f instanceof r.U3&&!f.empty&&f.$head.sameParent(f.$anchor)&&!e.composing&&!(m.sel&&m.sel.anchor!=m.sel.head))w={start:f.from,endA:f.to,endB:f.to};else{if(m.sel){let t=tF(e,e.state.doc,m.sel);if(t&&!t.eq(e.state.selection)){let n=e.state.tr.setSelection(t);c&&n.setMeta("composition",c),e.dispatch(n)}}return}e.state.selection.from<e.state.selection.to&&w.start==w.endB&&e.state.selection instanceof r.U3&&(w.start>e.state.selection.from&&w.start<=e.state.selection.from+2&&e.state.selection.from>=m.from?w.start=e.state.selection.from:w.endA<e.state.selection.to&&w.endA>=e.state.selection.to-2&&e.state.selection.to<=m.to&&(w.endB+=e.state.selection.to-w.endA,w.endA=e.state.selection.to)),M&&C<=11&&w.endB==w.start+1&&w.endA==w.start&&w.start>m.from&&" \xa0"==m.doc.textBetween(w.start-m.from-1,w.start-m.from+1)&&(w.start--,w.endA--,w.endB--);let k=m.doc.resolveNoCache(w.start-m.from),x=m.doc.resolveNoCache(w.endB-m.from),S=b.resolve(w.start),A=k.sameParent(x)&&k.parent.inlineContent&&S.end()>=w.endA;if((D&&e.input.lastIOSEnter>Date.now()-225&&(!A||s.some(e=>"DIV"==e.nodeName||"P"==e.nodeName))||!A&&k.pos<m.doc.content.size&&(!k.sameParent(x)||!k.parent.inlineContent)&&!/\S/.test(m.doc.textBetween(k.pos,x.pos,"",""))&&(d=r.LN.findFrom(m.doc.resolve(k.pos+1),1,!0))&&d.head>k.pos)&&e.someProp("handleKeyDown",t=>t(e,y(13,"Enter")))){e.input.lastIOSEnter=0;return}if(e.state.selection.anchor>w.start&&function(e,t,n,r,i){if(n-t<=i.pos-r.pos||tj(r,!0,!1)<i.pos)return!1;let o=e.resolve(t);if(!r.parent.isTextblock){let e=o.nodeAfter;return null!=e&&n==t+e.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=e.resolve(tj(o,!0,!0));return!(!s.parent.isTextblock||s.pos>n||tj(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(b,w.start,w.endA,k,x)&&e.someProp("handleKeyDown",t=>t(e,y(8,"Backspace")))){I&&O&&e.domObserver.suppressSelectionUpdates();return}O&&w.endB==w.start&&(e.input.lastChromeDelete=Date.now()),I&&!A&&k.start()!=x.start()&&0==x.parentOffset&&k.depth==x.depth&&m.sel&&m.sel.anchor==m.sel.head&&m.sel.head==w.endA&&(w.endB-=2,x=m.doc.resolveNoCache(w.endB-m.from),setTimeout(()=>{e.someProp("handleKeyDown",function(t){return t(e,y(13,"Enter"))})},20));let T=w.start,E=w.endA,N=t=>{let n=t||e.state.tr.replace(T,E,m.doc.slice(w.start-m.from,w.endB-m.from));if(m.sel){let t=tF(e,n.doc,m.sel);t&&!(O&&e.composing&&t.empty&&(w.start!=w.endB||e.input.lastChromeDelete<Date.now()-100)&&(t.head==T||t.head==n.mapping.map(E)-1)||M&&t.empty&&t.head==T)&&n.setSelection(t)}return c&&n.setMeta("composition",c),n.scrollIntoView()};if(A){if(k.pos==x.pos){M&&C<=11&&0==k.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout(()=>ek(e),20));let t=N(e.state.tr.delete(T,E)),n=b.resolve(w.start).marksAcross(b.resolve(w.endA));n&&t.ensureMarks(n),e.dispatch(t)}else if(w.endA==w.endB&&(h=function(e,t){let n=e.firstChild.marks,r=t.firstChild.marks,o=n,s=r,l,a,d;for(let e=0;e<r.length;e++)o=r[e].removeFromSet(o);for(let e=0;e<n.length;e++)s=n[e].removeFromSet(s);if(1==o.length&&0==s.length)a=o[0],l="add",d=e=>e.mark(a.addToSet(e.marks));else{if(0!=o.length||1!=s.length)return null;a=s[0],l="remove",d=e=>e.mark(a.removeFromSet(e.marks))}let h=[];for(let e=0;e<t.childCount;e++)h.push(d(t.child(e)));if(i.FK.from(h).eq(e))return{mark:a,type:l}}(k.parent.content.cut(k.parentOffset,x.parentOffset),S.parent.content.cut(S.parentOffset,w.endA-S.start())))){let t=N(e.state.tr);"add"==h.type?t.addMark(T,E,h.mark):t.removeMark(T,E,h.mark),e.dispatch(t)}else if(k.parent.child(k.index()).isText&&k.index()==x.index()-!x.textOffset){let t=k.parent.textBetween(k.parentOffset,x.parentOffset),n=()=>N(e.state.tr.insertText(t,T,E));e.someProp("handleTextInput",r=>r(e,T,E,t,n))||e.dispatch(n())}}else e.dispatch(N())})(this,e,t,n,o)),this.domObserver.start(),function(e){for(let t in eQ){let n=eQ[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=t=>{!function(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}(e,t)||e5(e,t)||!e.editable&&t.type in e0||n(e,t)},e1[t]?{passive:!0}:void 0)}N&&e.dom.addEventListener("input",()=>null),e4(e)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;for(let t in this._props={},e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&e4(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(tU),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let e in this._props)t[e]=this._props[e];for(let n in t.state=this.state,e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n,r,i;let o=this.state,l=!1,a=!1;e.storedMarks&&this.composing&&(ti(this),a=!0),this.state=e;let d=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(d||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let e=t_(this);(function(e,t){let n=0,r=0;for(let r in e){if(e[r]!=t[r])return!0;n++}for(let e in t)r++;return n!=r})(e,this.nodeViews)&&(this.nodeViews=e,l=!0)}(d||t.handleDOMEvents!=this._props.handleDOMEvents)&&e4(this),this.editable=tW(this),tJ(this);let h=tE(this),p=tK(this),u=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",g=l||!this.docView.matchesNode(e.doc,p,h);(g||!e.selection.eq(o.selection))&&(a=!0);let y="preserve"==u&&a&&null==this.dom.style.overflowAnchor&&function(e){let t,n,r=e.dom.getBoundingClientRect(),i=Math.max(0,r.top);for(let o=(r.left+r.right)/2,s=i+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=e.root.elementFromPoint(o,s);if(!r||r==e.dom||!e.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=i-20){t=r,n=l.top;break}}return{refDOM:t,refTop:n,stack:F(e.dom)}}(this);if(a){let t,n,a;this.domObserver.stop();let d=g&&(M||O)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&(r=o.selection,i=e.selection,a=Math.min(r.$anchor.sharedDepth(r.head),i.$anchor.sharedDepth(i.head)),r.$anchor.start(a)!=i.$anchor.start(a));if(g){let t=O?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(e){let t=e.domSelectionRange();if(!t.focusNode)return null;let n=function(e,t){for(;;){if(3==e.nodeType&&t)return e;if(1==e.nodeType&&t>0){if("false"==e.contentEditable)return null;t=f(e=e.childNodes[t-1])}else{if(!e.parentNode||m(e))return null;t=s(e),e=e.parentNode}}}(t.focusNode,t.focusOffset),r=function(e,t){for(;;){if(3==e.nodeType&&t<e.nodeValue.length)return e;if(1==e.nodeType&&t<e.childNodes.length){if("false"==e.contentEditable)return null;e=e.childNodes[t],t=0}else{if(!e.parentNode||m(e))return null;t=s(e)+1,e=e.parentNode}}}(t.focusNode,t.focusOffset);if(n&&r&&n!=r){let t=r.pmViewDesc,i=e.domObserver.lastChangedTextNode;if(n==i||r==i)return i;if(!t||!t.isText(r.nodeValue))return r;if(e.input.compositionNode==r){let e=n.pmViewDesc;if(!(!e||!e.isText(n.nodeValue)))return r}}return n||r}(this)),(l||!this.docView.update(e.doc,p,h,this))&&(this.docView.updateOuterDeco(p),this.docView.destroy(),this.docView=eo(e.doc,p,h,this.dom,this)),t&&!this.trackWrites&&(d=!0)}d||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&(t=this.docView.domFromPos(this.state.selection.anchor,0),n=this.domSelectionRange(),c(t.node,t.offset,n.anchorNode,n.anchorOffset)))?ek(this,d):(eA(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),(null==(n=this.dragging)?void 0:n.node)&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),"reset"==u?this.dom.scrollTop=0:"to selection"==u?this.scrollToSelection():y&&function({refDOM:e,refTop:t,stack:n}){let r=e?e.getBoundingClientRect().top:0;j(n,0==r?0:r-t)}(y)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode))if(this.someProp("handleScrollToSelection",e=>e(this)));else if(this.state.selection instanceof r.nh){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&B(this,t.getBoundingClientRect(),e)}else B(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let t=this.directPlugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let t=this.state.plugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,i=-1;if(this.state.doc.nodeAt(n.from)==n.node)i=n.from;else{let e=n.from+(this.state.doc.content.size-t.doc.content.size);(e>0&&this.state.doc.nodeAt(e))==n.node&&(i=e)}this.dragging=new td(e.slice,e.move,i<0?void 0:r.nh.create(this.state.doc,i))}someProp(e,t){let n=this._props&&this._props[e],r;if(null!=n&&(r=t?t(n):n))return r;for(let n=0;n<this.directPlugins.length;n++){let i=this.directPlugins[n].props[e];if(null!=i&&(r=t?t(i):i))return r}let i=this.state.plugins;if(i)for(let n=0;n<i.length;n++){let o=i[n].props[e];if(null!=o&&(r=t?t(o):o))return r}}hasFocus(){if(M){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(V)return e.focus(V);let t=F(e);e.focus(null==V?{get preventScroll(){return V={preventScroll:!0},!0}}:void 0),V||(V=!1,j(t,0))}(this.dom),ek(this),this.domObserver.start()}get root(){let e=this._root;if(null==e){for(let e=this.dom.parentNode;e;e=e.parentNode)if(9==e.nodeType||11==e.nodeType&&e.host)return e.getSelection||(Object.getPrototypeOf(e).getSelection=()=>e.ownerDocument.getSelection()),this._root=e}return e||document}updateRoot(){this._root=null}posAtCoords(e){return function(e,t){var n;let r,i,o=e.dom.ownerDocument,s,a=0,h=function(e,t,n){if(e.caretPositionFromPoint)try{let r=e.caretPositionFromPoint(t,n);if(r)return{node:r.offsetNode,offset:Math.min(f(r.offsetNode),r.offset)}}catch(e){}if(e.caretRangeFromPoint){let r=e.caretRangeFromPoint(t,n);if(r)return{node:r.startContainer,offset:Math.min(f(r.startContainer),r.startOffset)}}}(o,t.left,t.top);h&&({node:s,offset:a}=h);let c=(e.root.elementFromPoint?e.root:o).elementFromPoint(t.left,t.top);if(!c||!e.dom.contains(1!=c.nodeType?c.parentNode:c)){let n=e.dom.getBoundingClientRect();if(!H(t,n)||!(c=function e(t,n,r){let i=t.childNodes.length;if(i&&r.top<r.bottom)for(let o=Math.max(0,Math.min(i-1,Math.floor(i*(n.top-r.top)/(r.bottom-r.top))-2)),s=o;;){let r=t.childNodes[s];if(1==r.nodeType){let t=r.getClientRects();for(let i=0;i<t.length;i++){let o=t[i];if(H(n,o))return e(r,n,o)}}if((s=(s+1)%i)==o)break}return t}(e.dom,t,n)))return null}if(N)for(let e=c;s&&e;e=l(e))e.draggable&&(s=void 0);if(c=(r=(n=c).parentNode)&&/^li$/i.test(r.nodeName)&&t.left<n.getBoundingClientRect().left?r:n,s){let n;if(A&&1==s.nodeType&&(a=Math.min(a,s.childNodes.length))<s.childNodes.length){let e=s.childNodes[a],n;"IMG"==e.nodeName&&(n=e.getBoundingClientRect()).right<=t.left&&n.bottom>t.top&&a++}P&&a&&1==s.nodeType&&1==(n=s.childNodes[a-1]).nodeType&&"false"==n.contentEditable&&n.getBoundingClientRect().top>=t.top&&a--,s==e.dom&&a==s.childNodes.length-1&&1==s.lastChild.nodeType&&t.top>s.lastChild.getBoundingClientRect().bottom?i=e.state.doc.content.size:(0==a||1!=s.nodeType||"BR"!=s.childNodes[a-1].nodeName)&&(i=function(e,t,n,r){let i=-1;for(let n=t,o=!1;n!=e.dom;){let t=e.docView.nearestDesc(n,!0),s;if(!t)return null;if(1==t.dom.nodeType&&(t.node.isBlock&&t.parent||!t.contentDOM)&&((s=t.dom.getBoundingClientRect()).width||s.height)&&(t.node.isBlock&&t.parent&&!/^T(R|BODY|HEAD|FOOT)$/.test(t.dom.nodeName)&&(!o&&s.left>r.left||s.top>r.top?i=t.posBefore:(!o&&s.right<r.left||s.bottom<r.top)&&(i=t.posAfter),o=!0),!t.contentDOM&&i<0&&!t.node.isText))return(t.node.isBlock?r.top<(s.top+s.bottom)/2:r.left<(s.left+s.right)/2)?t.posBefore:t.posAfter;n=t.dom.parentNode}return i>-1?i:e.docView.posFromDOM(t,n,-1)}(e,s,a,t))}null==i&&(i=function(e,t,n){let{node:r,offset:i}=function e(t,n){let r,i,o,s=2e8,l,a=0,h=n.top,c=n.top;for(let e=t.firstChild,p=0;e;e=e.nextSibling,p++){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=d(e).getClientRects()}for(let d=0;d<t.length;d++){let u=t[d];if(u.top<=h&&u.bottom>=c){h=Math.max(u.bottom,h),c=Math.min(u.top,c);let t=u.left>n.left?u.left-n.left:u.right<n.left?n.left-u.right:0;if(t<s){o=e,s=t,l=t&&3==o.nodeType?{left:u.right<n.left?u.right:u.left,top:n.top}:n,1==e.nodeType&&t&&(a=p+ +(n.left>=(u.left+u.right)/2));continue}}else u.top>n.top&&!r&&u.left<=n.left&&u.right>=n.left&&(r=e,i={left:Math.max(u.left,Math.min(u.right,n.left)),top:u.top});!o&&(n.left>=u.right&&n.top>=u.top||n.left>=u.left&&n.top>=u.bottom)&&(a=p+1)}}return(!o&&r&&(o=r,l=i,s=0),o&&3==o.nodeType)?function(e,t){let n=e.nodeValue.length,r=document.createRange();for(let i=0;i<n;i++){r.setEnd(e,i+1),r.setStart(e,i);let n=J(r,1);if(n.top!=n.bottom&&H(t,n))return{node:e,offset:i+ +(t.left>=(n.left+n.right)/2)}}return{node:e,offset:0}}(o,l):!o||s&&1==o.nodeType?{node:t,offset:a}:e(o,l)}(t,n),o=-1;if(1==r.nodeType&&!r.firstChild){let e=r.getBoundingClientRect();o=e.left!=e.right&&n.left>(e.left+e.right)/2?1:-1}return e.docView.posFromDOM(r,i,o)}(e,c,t));let p=e.docView.nearestDesc(c,!0);return{pos:i,inside:p?p.posAtStart-p.border:-1}}(this,e)}coordsAtPos(e,t=1){return _(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(null==r)throw RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){return function(e,t,n){let r,i;return X==t&&Z==n?Q:(X=t,Z=n,Q="up"==n||"down"==n?(r=t.selection,i="up"==n?r.$from:r.$to,G(e,t,()=>{let{node:t}=e.docView.domFromPos(i.pos,"up"==n?-1:1);for(;;){let n=e.docView.nearestDesc(t,!0);if(!n)break;if(n.node.isBlock){t=n.contentDOM||n.dom;break}t=n.dom.parentNode}let r=_(e,i.pos,1);for(let e=t.firstChild;e;e=e.nextSibling){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=d(e,0,e.nodeValue.length).getClientRects()}for(let e=0;e<t.length;e++){let i=t[e];if(i.bottom>i.top+1&&("up"==n?r.top-i.top>(i.bottom-r.top)*2:i.bottom-r.bottom>(r.bottom-i.top)*2))return!1}}return!0})):function(e,t,n){let{$head:r}=t.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,o=i==r.parent.content.size,s=e.domSelection();return s?Y.test(r.parent.textContent)&&s.modify?G(e,t,()=>{let{focusNode:t,focusOffset:i,anchorNode:o,anchorOffset:l}=e.domSelectionRange(),a=s.caretBidiLevel;s.modify("move",n,"character");let d=r.depth?e.docView.domAfterPos(r.before()):e.dom,{focusNode:h,focusOffset:c}=e.domSelectionRange(),p=h&&!d.contains(1==h.nodeType?h:h.parentNode)||t==h&&i==c;try{s.collapse(o,l),t&&(t!=o||i!=l)&&s.extend&&s.extend(t,i)}catch(e){}return null!=a&&(s.caretBidiLevel=a),p}):"left"==n||"backward"==n?!i:o:r.pos==r.start()||r.pos==r.end()}(e,t,n))}(this,t||this.state,e)}pasteHTML(e,t){return tl(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return tl(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return eK(this,e)}destroy(){if(this.docView){for(let e in this.domObserver.stop(),this.input.eventHandlers)this.dom.removeEventListener(e,this.input.eventHandlers[e]);clearTimeout(this.input.composingTimeout),clearTimeout(this.input.lastIOSEnterFallbackTimeout),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],tE(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,h()}}get isDestroyed(){return null==this.docView}dispatchEvent(e){!e5(this,e)&&eQ[e.type]&&(this.editable||!(e.type in e0))&&eQ[e.type](this,e)}domSelectionRange(){let e=this.domSelection();return e?N&&11===this.root.nodeType&&function(e){let t=e.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}(this.dom.ownerDocument)==this.dom&&function(e,t){let n;if(t.getComposedRanges){let n=t.getComposedRanges(e.root)[0];if(n)return tz(e,n)}function r(e){e.preventDefault(),e.stopImmediatePropagation(),n=e.getTargetRanges()[0]}return e.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),e.dom.removeEventListener("beforeinput",r,!0),n?tz(e,n):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function tK(e){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),e.someProp("attributes",n=>{if("function"==typeof n&&(n=n(e.state)),n)for(let e in n)"class"==e?t.class+=" "+n[e]:"style"==e?t.style=(t.style?t.style+";":"")+n[e]:t[e]||"contenteditable"==e||"nodeName"==e||(t[e]=String(n[e]))}),t.translate||(t.translate="no"),[tg.node(0,e.state.doc.content.size,t)]}function tJ(e){if(e.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:tg.widget(e.state.selection.from,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function tW(e){return!e.someProp("editable",t=>!1===t(e.state))}function t_(e){let t=Object.create(null);function n(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function tU(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw RangeError("Plugins passed directly to the view must not have a state component")}tH.prototype.dispatch=function(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}},11477:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Hourglass",[["path",{d:"M5 22h14",key:"ehvnwv"}],["path",{d:"M5 2h14",key:"pdyrp9"}],["path",{d:"M17 22v-4.172a2 2 0 0 0-.586-1.414L12 12l-4.414 4.414A2 2 0 0 0 7 17.828V22",key:"1d314k"}],["path",{d:"M7 2v4.172a2 2 0 0 0 .586 1.414L12 12l4.414-4.414A2 2 0 0 0 17 6.172V2",key:"1vvvr6"}]])},11671:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},12258:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},12793:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},14687:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("LifeBuoy",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.93 4.93 4.24 4.24",key:"1ymg45"}],["path",{d:"m14.83 9.17 4.24-4.24",key:"1cb5xl"}],["path",{d:"m14.83 14.83 4.24 4.24",key:"q42g0n"}],["path",{d:"m9.17 14.83-4.24 4.24",key:"bqpfvv"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}]])},14908:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},15019:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},18876:(e,t,n)=>{n.d(t,{Ay:()=>o});var r=n(53744),i=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,o=r.bP.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null},width:{default:null},height:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",(0,r.KV)(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}},addInputRules(){return[(0,r.jT)({find:i,type:this.type,getAttributes:e=>{let[,,t,n,r]=e;return{src:n,alt:t,title:r}}})]}})},22646:(e,t,n)=>{n.d(t,{C1:()=>S,bL:()=>k});var r=n(93491),i=n(42014),o=n(10158),s=n(18682),l=n(76322),a=n(78476),d=n(96432),h=n(55462),c=n(90604),p=n(91754),u="Checkbox",[f,m]=(0,o.A)(u),[g,y]=f(u);function b(e){let{__scopeCheckbox:t,checked:n,children:i,defaultChecked:o,disabled:s,form:a,name:d,onCheckedChange:h,required:c,value:f="on",internal_do_not_use_render:m}=e,[y,b]=(0,l.i)({prop:n,defaultProp:o??!1,onChange:h,caller:u}),[v,w]=r.useState(null),[k,x]=r.useState(null),S=r.useRef(!1),M=!v||!!a||!!v.closest("form"),C={checked:y,disabled:s,setChecked:b,control:v,setControl:w,name:d,form:a,value:f,hasConsumerStoppedPropagationRef:S,required:c,defaultChecked:!A(o)&&o,isFormControl:M,bubbleInput:k,setBubbleInput:x};return(0,p.jsx)(g,{scope:t,...C,children:"function"==typeof m?m(C):i})}var v="CheckboxTrigger",w=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...o},l)=>{let{control:a,value:d,disabled:h,checked:u,required:f,setControl:m,setChecked:g,hasConsumerStoppedPropagationRef:b,isFormControl:w,bubbleInput:k}=y(v,e),x=(0,i.s)(l,m),S=r.useRef(u);return r.useEffect(()=>{let e=a?.form;if(e){let t=()=>g(S.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[a,g]),(0,p.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":A(u)?"mixed":u,"aria-required":f,"data-state":T(u),"data-disabled":h?"":void 0,disabled:h,value:d,...o,ref:x,onKeyDown:(0,s.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.m)(n,e=>{g(e=>!!A(e)||!e),k&&w&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})})});w.displayName=v;var k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:i,defaultChecked:o,required:s,disabled:l,value:a,onCheckedChange:d,form:h,...c}=e;return(0,p.jsx)(b,{__scopeCheckbox:n,checked:i,defaultChecked:o,disabled:l,required:s,onCheckedChange:d,name:r,form:h,value:a,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(w,{...c,ref:t,__scopeCheckbox:n}),e&&(0,p.jsx)(C,{__scopeCheckbox:n})]})})});k.displayName=u;var x="CheckboxIndicator",S=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...i}=e,o=y(x,n);return(0,p.jsx)(h.C,{present:r||A(o.checked)||!0===o.checked,children:(0,p.jsx)(c.sG.span,{"data-state":T(o.checked),"data-disabled":o.disabled?"":void 0,...i,ref:t,style:{pointerEvents:"none",...e.style}})})});S.displayName=x;var M="CheckboxBubbleInput",C=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{let{control:o,hasConsumerStoppedPropagationRef:s,checked:l,defaultChecked:h,required:u,disabled:f,name:m,value:g,form:b,bubbleInput:v,setBubbleInput:w}=y(M,e),k=(0,i.s)(n,w),x=(0,a.Z)(l),S=(0,d.X)(o);r.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!s.current;if(x!==l&&e){let n=new Event("click",{bubbles:t});v.indeterminate=A(l),e.call(v,!A(l)&&l),v.dispatchEvent(n)}},[v,x,l,s]);let C=r.useRef(!A(l)&&l);return(0,p.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??C.current,required:u,disabled:f,name:m,value:g,form:b,...t,tabIndex:-1,ref:k,style:{...t.style,...S,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function T(e){return A(e)?"indeterminate":e?"checked":"unchecked"}C.displayName=M},23298:(e,t,n)=>{n.d(t,{A:()=>r});var r=n(98998).nA},23913:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},24727:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},25124:(e,t,n)=>{n.d(t,{A:()=>r});var r=n(98998).Hj},28235:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},28280:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]])},29428:(e,t,n)=>{e.exports=n(68708)},29602:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},31667:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},33772:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Text",[["path",{d:"M17 6.1H3",key:"wptmhv"}],["path",{d:"M21 12.1H3",key:"1j38uz"}],["path",{d:"M15.1 18H3",key:"1nb16a"}]])},36445:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]])},36470:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Heading3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},37607:(e,t,n)=>{n.d(t,{bL:()=>x,zi:()=>S});var r=n(93491),i=n(18682),o=n(42014),s=n(10158),l=n(76322),a=n(78476),d=n(96432),h=n(90604),c=n(91754),p="Switch",[u,f]=(0,s.A)(p),[m,g]=u(p),y=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:s,checked:a,defaultChecked:d,required:u,disabled:f,value:g="on",onCheckedChange:y,form:b,...v}=e,[x,S]=r.useState(null),M=(0,o.s)(t,e=>S(e)),C=r.useRef(!1),A=!x||b||!!x.closest("form"),[T,O]=(0,l.i)({prop:a,defaultProp:d??!1,onChange:y,caller:p});return(0,c.jsxs)(m,{scope:n,checked:T,disabled:f,children:[(0,c.jsx)(h.sG.button,{type:"button",role:"switch","aria-checked":T,"aria-required":u,"data-state":k(T),"data-disabled":f?"":void 0,disabled:f,value:g,...v,ref:M,onClick:(0,i.m)(e.onClick,e=>{O(e=>!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,c.jsx)(w,{control:x,bubbles:!C.current,name:s,value:g,checked:T,required:u,disabled:f,form:b,style:{transform:"translateX(-100%)"}})]})});y.displayName=p;var b="SwitchThumb",v=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,i=g(b,n);return(0,c.jsx)(h.sG.span,{"data-state":k(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:t})});v.displayName=b;var w=r.forwardRef(({__scopeSwitch:e,control:t,checked:n,bubbles:i=!0,...s},l)=>{let h=r.useRef(null),p=(0,o.s)(h,l),u=(0,a.Z)(n),f=(0,d.X)(t);return r.useEffect(()=>{let e=h.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==n&&t){let r=new Event("click",{bubbles:i});t.call(e,n),e.dispatchEvent(r)}},[u,n,i]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...s,tabIndex:-1,ref:p,style:{...s.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function k(e){return e?"checked":"unchecked"}w.displayName="SwitchBubbleInput";var x=y,S=v},40171:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},42243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},42352:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},42404:(e,t,n)=>{n.d(t,{$t:()=>S,LN:()=>s,U3:()=>h,hs:()=>T,i5:()=>f,k_:()=>M,nh:()=>p,yn:()=>l});var r=n(42842),i=n(98539);let o=Object.create(null);class s{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new l(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=r.Ji.empty){let n=t.content.lastChild,i=null;for(let e=0;e<t.openEnd;e++)i=n,n=n.lastChild;let o=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:d}=s[l],h=e.mapping.slice(o);e.replaceRange(h.map(a.pos),h.map(d.pos),l?r.Ji.empty:t),0==l&&y(e,o,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=e.mapping.slice(n),a=l.map(o.pos),d=l.map(s.pos);i?e.deleteRange(a,d):(e.replaceRangeWith(a,d,t),y(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new h(e):g(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let r=e.depth-1;r>=0;r--){let i=t<0?g(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):g(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(i)return i}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new f(e.node(0))}static atStart(e){return g(e,e,0,0,1)||new f(e)}static atEnd(e){return g(e,e,e.content.size,e.childCount,-1)||new f(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=o[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in o)throw RangeError("Duplicate use of selection JSON ID "+e);return o[e]=t,t.prototype.jsonID=e,t}getBookmark(){return h.between(this.$anchor,this.$head).getBookmark()}}s.prototype.visible=!0;class l{constructor(e,t){this.$from=e,this.$to=t}}let a=!1;function d(e){a||e.parent.inlineContent||(a=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class h extends s{constructor(e,t=e){d(e),d(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return s.near(n);let r=e.resolve(t.map(this.anchor));return new h(r.parent.inlineContent?r:n,n)}replace(e,t=r.Ji.empty){if(super.replace(e,t),t==r.Ji.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof h&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new c(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new h(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let e=s.findFrom(t,n,!0)||s.findFrom(t,-n,!0);if(!e)return s.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r?e=t:(e=(s.findFrom(e,-n,!0)||s.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0&&(e=t)),new h(e,t)}}s.jsonID("text",h);class c{constructor(e,t){this.anchor=e,this.head=t}map(e){return new c(e.map(this.anchor),e.map(this.head))}resolve(e){return h.between(e.resolve(this.anchor),e.resolve(this.head))}}class p extends s{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),i=e.resolve(r);return n?s.near(i):new p(i)}content(){return new r.Ji(r.FK.from(this.node),0,0)}eq(e){return e instanceof p&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new u(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new p(e.resolve(t.anchor))}static create(e,t){return new p(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}p.prototype.visible=!1,s.jsonID("node",p);class u{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new c(n,n):new u(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&p.isSelectable(n)?new p(t):s.near(t)}}class f extends s{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=r.Ji.empty){if(t==r.Ji.empty){e.delete(0,e.doc.content.size);let t=s.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new f(e)}map(e){return new f(e)}eq(e){return e instanceof f}getBookmark(){return m}}s.jsonID("all",f);let m={map(){return this},resolve:e=>new f(e)};function g(e,t,n,r,i,o=!1){if(t.inlineContent)return h.create(e,n);for(let s=r-(i>0?0:1);i>0?s<t.childCount:s>=0;s+=i){let r=t.child(s);if(r.isAtom){if(!o&&p.isSelectable(r))return p.create(e,n-(i<0?r.nodeSize:0))}else{let t=g(e,r,n+i,i<0?r.childCount:0,i,o);if(t)return t}n+=r.nodeSize*i}return null}function y(e,t,n){let r,o=e.steps.length-1;if(o<t)return;let l=e.steps[o];(l instanceof i.Ln||l instanceof i.Wg)&&(e.mapping.maps[o].forEach((e,t,n,i)=>{null==r&&(r=i)}),e.setSelection(s.near(e.doc.resolve(r),n)))}class b extends i.dL{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return r.CU.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.CU.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let e=this.doc.resolve(t);i=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,i)),this.selection.empty||this.setSelection(s.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function v(e,t){return t&&e?e.bind(t):e}class w{constructor(e,t,n){this.name=e,this.init=v(t.init,n),this.apply=v(t.apply,n)}}let k=[new w("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new w("selection",{init:(e,t)=>e.selection||s.atStart(t.doc),apply:e=>e.selection}),new w("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new w("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class x{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=k.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new w(e.key,e.spec.state,e))})}}class S{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let l=r?r[o].n:0,a=r?r[o].state:this,d=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(d&&n.filterTransaction(d,o)){if(d.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<o?{state:n,n:t.length}:{state:this,n:0})}t.push(d),n=n.applyInner(d),i=!0}r&&(r[o]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new S(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new b(this)}static create(e){let t=new x(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new S(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new x(this.schema,e.plugins),n=t.fields,r=new S(t);for(let t=0;t<n.length;t++){let i=n[t].name;r[i]=this.hasOwnProperty(i)?this[i]:n[t].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],i=r.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let i=new x(e.schema,e.plugins),o=new S(i);return i.fields.forEach(i=>{if("doc"==i.name)o.doc=r.bP.fromJSON(e.schema,t.doc);else if("selection"==i.name)o.selection=s.fromJSON(o.doc,t.selection);else if("storedMarks"==i.name)t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let r in n){let s=n[r],l=s.spec.state;if(s.key==i.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,r)){o[i.name]=l.fromJSON.call(s,e,t[r],o);return}}o[i.name]=i.init(e,o)}}),o}}class M{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,r){for(let i in t){let o=t[i];o instanceof Function?o=o.bind(n):"handleDOMEvents"==i&&(o=e(o,n,{})),r[i]=o}return r}(e.props,this,this.props),this.key=e.key?e.key.key:A("plugin")}getState(e){return e[this.key]}}let C=Object.create(null);function A(e){return e in C?e+"$"+ ++C[e]:(C[e]=0,e+"$")}class T{constructor(e="key"){this.key=A(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}},42842:(e,t,n)=>{function r(e){this.content=e}n.d(t,{S4:()=>j,ZF:()=>G,FK:()=>i,CU:()=>a,sX:()=>$,bP:()=>S,u$:()=>k,vI:()=>d,Sj:()=>B,Ji:()=>h}),r.prototype={constructor:r,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var i=n&&n!=e?this.remove(n):this,o=i.find(e),s=i.content.slice();return -1==o?s.push(n||e,t):(s[o+1]=t,n&&(s[o]=n)),new r(s)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new r(n)},addToStart:function(e,t){return new r([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new r(n)},addBefore:function(e,t,n){var i=this.remove(t),o=i.content.slice(),s=i.find(e);return o.splice(-1==s?o.length:s,0,t,n),new r(o)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=r.from(e)).size?new r(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=r.from(e)).size?new r(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=r.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},r.from=function(e){if(e instanceof r)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new r(t)};class i{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,r=0,i){for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;if(a>e&&!1!==n(l,r+s,i||null,o)&&l.content.size){let i=s+1;l.nodesBetween(Math.max(0,e-i),Math.min(l.content.size,t-i),n,r+i)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let i="",o=!0;return this.nodesBetween(e,t,(s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(o?o=!1:i+=n),i+=a},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),o=1);o<e.content.length;o++)r.push(e.content[o]);return new i(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let i=0,o=0;o<t;i++){let s=this.content[i],l=o+s.nodeSize;l>e&&((o<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-o),Math.min(s.text.length,t-o)):s.cut(Math.max(0,e-o-1),Math.min(s.content.size,t-o-1))),n.push(s),r+=s.nodeSize),o=l}return new i(n,r)}cutByIndex(e,t){return e==t?i.empty:0==e&&t==this.content.length?this:new i(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),o=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new i(r,o)}addToStart(e){return new i([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new i(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return function e(t,n,r){for(let i=0;;i++){if(i==t.childCount||i==n.childCount)return t.childCount==n.childCount?null:r;let o=t.child(i),s=n.child(i);if(o==s){r+=o.nodeSize;continue}if(!o.sameMarkup(s))return r;if(o.isText&&o.text!=s.text){for(let e=0;o.text[e]==s.text[e];e++)r++;return r}if(o.content.size||s.content.size){let t=e(o.content,s.content,r+1);if(null!=t)return t}r+=o.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,r,i){for(let o=t.childCount,s=n.childCount;;){if(0==o||0==s)return o==s?null:{a:r,b:i};let l=t.child(--o),a=n.child(--s),d=l.nodeSize;if(l==a){r-=d,i-=d;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let e=0,t=Math.min(l.text.length,a.text.length);for(;e<t&&l.text[l.text.length-e-1]==a.text[a.text.length-e-1];)e++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let t=e(l.content,a.content,r-1,i-1);if(t)return t}r-=d,i-=d}}(this,e,t,n)}findIndex(e){if(0==e)return s(0,e);if(e==this.size)return s(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let t=0,n=0;;t++){let r=n+this.child(t).nodeSize;if(r>=e){if(r==e)return s(t+1,r);return s(t,n)}n=r}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return i.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new i(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return i.empty;let t,n=0;for(let r=0;r<e.length;r++){let i=e[r];n+=i.nodeSize,r&&i.isText&&e[r-1].sameMarkup(i)?(t||(t=e.slice(0,r)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new i(t||e,n)}static from(e){if(!e)return i.empty;if(e instanceof i)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new i([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}i.empty=new i([],0);let o={index:0,offset:0};function s(e,t){return o.index=e,o.offset=t,o}function l(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!l(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!l(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class a{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&l(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return a.none;if(e instanceof a)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}a.none=[];class d extends Error{}class h{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,r,i){let{index:o,offset:s}=t.findIndex(n),l=t.maybeChild(o);if(s==n||l.isText)return i&&!i.canReplace(o,o,r)?null:t.cut(0,n).append(r).append(t.cut(n));let a=e(l.content,n-s-1,r,l);return a&&t.replaceChild(o,l.copy(a))}(this.content,e+this.openStart,t);return n&&new h(n,this.openStart,this.openEnd)}removeBetween(e,t){return new h(function e(t,n,r){let{index:i,offset:o}=t.findIndex(n),s=t.maybeChild(i),{index:l,offset:a}=t.findIndex(r);if(o==n||s.isText){if(a!=r&&!t.child(l).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return t.replaceChild(i,s.copy(e(s.content,n-o-1,r-o-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return h.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new h(i.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)r++;return new h(e,n,r)}}function c(e,t){if(!t.type.compatibleContent(e.type))throw new d("Cannot join "+t.type.name+" onto "+e.type.name)}function p(e,t,n){let r=e.node(n);return c(r,t.node(n)),r}function u(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function f(e,t,n,r){let i=(t||e).node(n),o=0,s=t?t.index(n):i.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(u(e.nodeAfter,r),o++));for(let e=o;e<s;e++)u(i.child(e),r);t&&t.depth==n&&t.textOffset&&u(t.nodeBefore,r)}function m(e,t){return e.type.checkContent(t),e.copy(t)}function g(e,t,n){let r=[];return f(null,e,n,r),e.depth>n&&u(m(p(e,t,n+1),g(e,t,n+1)),r),f(t,null,n,r),new i(r)}h.empty=new h(i.empty,0,0);class y{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)r+=n.child(t).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return a.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let i=n.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||r&&i[o].isInSet(r.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new k(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],r=0,i=t;for(let t=e;;){let{index:e,offset:o}=t.content.findIndex(i),s=i-o;if(n.push(t,e,r+o),!s||(t=t.child(e)).isText)break;i=s-1,r+=o+1}return new y(t,n,i)}static resolveCached(e,t){let n=w.get(e);if(n)for(let e=0;e<n.elts.length;e++){let r=n.elts[e];if(r.pos==t)return r}else w.set(e,n=new b);let r=n.elts[n.i]=y.resolve(e,t);return n.i=(n.i+1)%v,r}}class b{constructor(){this.elts=[],this.i=0}}let v=12,w=new WeakMap;class k{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let x=Object.create(null);class S{constructor(e,t,n,r=a.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||i.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&l(this.attrs,t||e.defaultAttrs||x)&&a.sameSet(this.marks,n||a.none)}copy(e=null){return e==this.content?this:new S(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new S(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return h.empty;let r=this.resolve(e),i=this.resolve(t),o=n?0:r.sharedDepth(t),s=r.start(o);return new h(r.node(o).content.cut(r.pos-s,i.pos-s),r.depth-o,i.depth-o)}replace(e,t,n){var r=this.resolve(e),o=this.resolve(t);if(n.openStart>r.depth)throw new d("Inserted content deeper than insertion position");if(r.depth-n.openStart!=o.depth-n.openEnd)throw new d("Inconsistent open depths");return function e(t,n,r,o){let s=t.index(o),l=t.node(o);if(s==n.index(o)&&o<t.depth-r.openStart){let i=e(t,n,r,o+1);return l.copy(l.content.replaceChild(s,i))}if(!r.content.size)return m(l,g(t,n,o));if(r.openStart||r.openEnd||t.depth!=o||n.depth!=o){let{start:e,end:s}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)r=t.node(e).copy(i.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(r,t);return m(l,function e(t,n,r,o,s){let l=t.depth>s&&p(t,n,s+1),a=o.depth>s&&p(r,o,s+1),d=[];return f(null,t,s,d),l&&a&&n.index(s)==r.index(s)?(c(l,a),u(m(l,e(t,n,r,o,s+1)),d)):(l&&u(m(l,g(t,n,s+1)),d),f(n,r,s,d),a&&u(m(a,g(r,o,s+1)),d)),f(o,null,s,d),new i(d)}(t,e,s,n,o))}{let e=t.parent,i=e.content;return m(e,i.cut(0,t.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(r,o,n,0)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return y.resolveCached(this,e)}resolveNoCache(e){return y.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),C(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=i.empty,r=0,o=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,r,o),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let e=r;e<o;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(e).matchType(n),o=i&&i.matchFragment(this.content,t);return!!o&&o.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=a.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!a.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=i.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,r,n);return o.type.checkAttrs(o.attrs),o}}S.prototype.text=void 0;class M extends S{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):C(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new M(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new M(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function C(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class A{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let r,i=new T(e,t);if(null==i.next)return A.empty;let o=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let i=[];for(let e in n){let r=n[e];r.isInGroup(t)&&i.push(r)}return 0==i.length&&e.err("No node type or group '"+t+"' found"),i})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=O(e),r=n;return e.eat(",")&&(r="}"!=e.next?O(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let s=(n=function(e){let t=[[]];return i(function e(t,o){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,o)),[]);if("seq"==t.type)for(let r=0;;r++){let s=e(t.exprs[r],o);if(r==t.exprs.length-1)return s;i(s,o=n())}else if("star"==t.type){let s=n();return r(o,s),i(e(t.expr,s),s),[r(s)]}else if("plus"==t.type){let s=n();return i(e(t.expr,o),s),i(e(t.expr,s),s),[r(s)]}else if("opt"==t.type)return[r(o)].concat(e(t.expr,o));else if("range"==t.type){let s=o;for(let r=0;r<t.min;r++){let r=n();i(e(t.expr,s),r),s=r}if(-1==t.max)i(e(t.expr,s),s);else for(let o=t.min;o<t.max;o++){let o=n();r(s,o),i(e(t.expr,s),o),s=o}return[r(s)]}else if("name"==t.type)return[r(o,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let i={term:r,to:n};return t[e].push(i),i}function i(e,t){e.forEach(e=>e.to=t)}}(o),r=Object.create(null),function e(t){let i=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let r;if(e){for(let t=0;t<i.length;t++)i[t][0]==e&&(r=i[t][1]);N(n,t).forEach(t=>{r||i.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)})}})});let o=r[t.join(",")]=new A(t.indexOf(n.length-1)>-1);for(let t=0;t<i.length;t++){let n=i[t][1].sort(E);o.next.push({type:i[t][0],next:r[n.join(",")]||e(n)})}return o}(N(n,0)));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],i=!e.validEnd,o=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];o.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(s)&&r.push(s)}i&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function o(s,l){let a=s.matchFragment(e,n);if(a&&(!t||a.validEnd))return i.from(l.map(e=>e.createAndFill()));for(let e=0;e<s.next.length;e++){let{type:t,next:n}=s.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let e=o(n,l.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<i.next.length;e++){let{type:o,next:s}=i.next[e];o.isLeaf||o.hasRequiredAttrs()||o.name in t||r.type&&!s.validEnd||(n.push({match:o.contentMatch,type:o,via:r}),t[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return!function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)r+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return r}).join("\n")}}A.empty=new A(!0);class T{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function O(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function E(e,t){return t-e}function N(e,t){let n=[];return function t(r){let i=e[r];if(1==i.length&&!i[0].term)return t(i[0].to);n.push(r);for(let e=0;e<i.length;e++){let{term:r,to:o}=i[e];r||-1!=n.indexOf(o)||t(o)}}(t),n.sort(E)}function D(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function R(e,t){let n=Object.create(null);for(let r in e){let i=t&&t[r];if(void 0===i){let t=e[r];if(t.hasDefault)i=t.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function L(e,t,n,r){for(let r in t)if(!(r in e))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in e){let r=e[n];r.validate&&r.validate(t[n])}}function I(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new z(e,r,t[r]);return n}class P{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=I(e,n.attrs),this.defaultAttrs=D(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==A.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:R(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new S(this,this.computeAttrs(e),i.from(t),a.setFrom(n))}createChecked(e=null,t,n){return t=i.from(t),this.checkContent(t),new S(this,this.computeAttrs(e),t,a.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=i.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),o=r&&r.fillBefore(i.empty,!0);return o?new S(this,e,t.append(o),a.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){L(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:a.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,r)=>n[e]=new P(e,t,r));let r=t.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class z{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${i}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class ${constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=I(e,r.attrs),this.excluded=null;let i=D(this.attrs);this.instance=i?new a(this,i):null}create(e=null){return!e&&this.instance?this.instance:new a(this,R(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((e,i)=>n[e]=new $(e,r++,t,i)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){L(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class B{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=r.from(e.nodes),t.marks=r.from(e.marks||{}),this.nodes=P.compile(this.spec.nodes,this),this.marks=$.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],r=t.spec.content||"",i=t.spec.marks;if(t.contentMatch=n[r]||(n[r]=A.parse(r,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==i?null:i?F(this,i.split(" ")):""!=i&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:F(this,n.split(" "))}this.nodeFromJSON=e=>S.fromJSON(this,e),this.markFromJSON=e=>a.fromJSON(this,e),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof P){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new M(n,n.defaultAttrs,e,a.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function F(e,t){let n=[];for(let r=0;r<t.length;r++){let i=t[r],o=e.marks[i],s=o;if(o)n.push(o);else for(let t in e.marks){let r=e.marks[t];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=r)}if(!s)throw SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class j{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new _(this,t,!1);return n.addAll(e,a.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new _(this,t,!0);return n.addAll(e,a.none,t.from,t.to),h.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){var r,i;let n=this.tags[o];if(r=e,i=n.tag,(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,i)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],o=r.style;if(0==o.indexOf(e)&&(!r.context||n.matchesContext(r.context))&&(!(o.length>e.length)||61==o.charCodeAt(e.length)&&o.slice(e.length+1)==t)){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let t in e.marks){let r=e.marks[t].spec.parseDOM;r&&r.forEach(e=>{n(e=U(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let r=e.nodes[t].spec.parseDOM;r&&r.forEach(e=>{n(e=U(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new j(e,j.schemaRules(e)))}}let V={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},H={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},K={ol:!0,ul:!0};function J(e,t,n){return null!=t?!!t|2*("full"===t):e&&"pre"==e.whitespace?3:-5&n}class W{constructor(e,t,n,r,i,o){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=o,this.content=[],this.activeMarks=a.none,this.match=i||(4&o?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(i.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=i.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(i.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!V.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class _{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=t.topNode,i,o=J(null,t.preserveWhitespace,0)|4*!!n;i=r?new W(r.type,r.attrs,a.none,!0,t.topMatch||r.type.contentMatch,o):n?new W(null,null,a.none,!0,null,o):new W(e.schema.topNodeType,null,a.none,!0,null,o),this.nodes=[i],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],i=e.previousSibling;(!t||i&&"BR"==i.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,i=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),s;K.hasOwnProperty(o)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&K.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(s=this.parser.matchTag(e,this,n));t:if(l?l.ignore:H.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,r=this.needsBlock;if(V.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break t}let s=l&&l.skip?t:this.readStyles(e,t);s&&this.addAll(e,s),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?s:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let r=this.parser.matchedStyles[e],i=n.getPropertyValue(r);if(i)for(let e;;){let n=this.parser.matchStyle(r,i,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,r){let i,o;if(t.node)if((o=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(o.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(o,t.attrs||null,n,t.preserveWhitespace);e&&(i=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n,!1));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}i&&this.sync(s)&&this.open--}addAll(e,t,n,r){let i=n||0;for(let o=n?e.childNodes[n]:e.firstChild,s=null==r?null:e.childNodes[r];o!=s;o=o.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(o,t);this.findAtPoint(e,i)}findPlace(e,t,n){let r,i;for(let t=this.open,o=0;t>=0;t--){let s=this.nodes[t],l=s.findWrapping(e);if(l&&(!r||r.length>l.length+o)&&(r=l,i=s,!l.length))break;if(s.solid){if(n)break;o+=2}}if(!r)return null;this.sync(i);for(let e=0;e<r.length;e++)t=this.enterInner(r[e],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=a.none;for(let i of r.concat(e.marks))(t.type?t.type.allowsMarkType(i.type):q(i.type,e.type))&&(n=i.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,r){let i=this.findPlace(e.create(t),n,!1);return i&&(i=this.enterInner(e,t,n,!0,r)),i}enterInner(e,t,n,r=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let s=J(e,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let l=a.none;return n=n.filter(t=>(o.type?!o.type.allowsMarkType(t.type):!q(t.type,e))||(l=t.addToSet(l),!1)),this.nodes.push(new W(e,t,l,r,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;else this.localPreserveWS&&(this.nodes[t].options|=1);return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+ +!r,o=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=i;s--)if(o(e-1,s))return!0;return!1}{let e=s>0||0==s&&r?this.nodes[s].type:n&&s>=i?n.node(s-i).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function U(e){let t={};for(let n in e)t[n]=e[n];return t}function q(e,t){let n=t.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(e))continue;let o=[],s=e=>{o.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:i}=e.edge(n);if(r==t||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class G{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=X(t).createDocumentFragment());let r=n,i=[];return e.forEach(e=>{if(i.length||e.marks.length){let n=0,o=0;for(;n<i.length&&o<e.marks.length;){let t=e.marks[o];if(!this.marks[t.type.name]){o++;continue}if(!t.eq(i[n][0])||!1===t.type.spec.spanning)break;n++,o++}for(;n<i.length;)r=i.pop()[1];for(;o<e.marks.length;){let n=e.marks[o++],s=this.serializeMark(n,e.isInline,t);s&&(i.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=Q(X(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&Q(X(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return Q(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new G(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Y(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return Y(e.marks)}}function Y(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function X(e){return e.document||window.document}let Z=new WeakMap;function Q(e,t,n,r){let i,o,s;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let l=t[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(o=Z.get(r))&&Z.set(r,(s=null,!function e(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])s||(s=[]),s.push(t);else for(let n=0;n<t.length;n++)e(t[n]);else for(let n in t)e(t[n])}(r),o=s)),a=o)&&a.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let d=l.indexOf(" ");d>0&&(n=l.slice(0,d),l=l.slice(d+1));let h=n?e.createElementNS(n,l):e.createElement(l),c=t[1],p=1;if(c&&"object"==typeof c&&null==c.nodeType&&!Array.isArray(c)){for(let e in p=2,c)if(null!=c[e]){let t=e.indexOf(" ");t>0?h.setAttributeNS(e.slice(0,t),e.slice(t+1),c[e]):"style"==e&&h.style?h.style.cssText=c[e]:h.setAttribute(e,c[e])}}for(let o=p;o<t.length;o++){let s=t[o];if(0===s){if(o<t.length-1||o>p)throw RangeError("Content hole must be the only child of its parent node");return{dom:h,contentDOM:h}}{let{dom:t,contentDOM:o}=Q(e,s,n,r);if(h.appendChild(t),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:h,contentDOM:i}}},44620:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("MonitorPlay",[["path",{d:"M10 7.75a.75.75 0 0 1 1.142-.638l3.664 2.249a.75.75 0 0 1 0 1.278l-3.664 2.25a.75.75 0 0 1-1.142-.64z",key:"1pctta"}],["path",{d:"M12 17v4",key:"1riwvh"}],["path",{d:"M8 21h8",key:"1ev6f3"}],["rect",{x:"2",y:"3",width:"20",height:"14",rx:"2",key:"x3v2xh"}]])},46934:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Shuffle",[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]])},49295:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},50307:(e,t,n)=>{n.d(t,{A:()=>eD});var r=n(53744),i=(e,t)=>{if("slot"===e)return 0;if(e instanceof Function)return e(t);let{children:n,...r}=null!=t?t:{};if("svg"===e)throw Error("SVG elements are not supported in the JSX syntax, use the array syntax instead");return[e,r,n]},o=/^\s*>\s$/,s=r.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return i("blockquote",{...(0,r.KV)(this.options.HTMLAttributes,e),children:i("slot",{})})},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.tG)({find:o,type:this.type})]}}),l=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,a=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,d=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,c=r.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return i("strong",{...(0,r.KV)(this.options.HTMLAttributes,e),children:i("slot",{})})},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.OX)({find:l,type:this.type}),(0,r.OX)({find:d,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:a,type:this.type}),(0,r.Zc)({find:h,type:this.type})]}}),p=n(42404),u=/^```([a-z]+)?[\s\n]$/,f=/^~~~([a-z]+)?[\s\n]$/,m=r.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,enableTabIndentation:!1,tabSize:4,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options,r=[...(null==(t=e.firstElementChild)?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",(0,r.KV)(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Tab:({editor:e})=>{if(!this.options.enableTabIndentation)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;if(r.parent.type!==this.type)return!1;let o=" ".repeat(this.options.tabSize);return i?e.commands.insertContent(o):e.commands.command(({tr:e})=>{let{from:r,to:i}=n,s=t.doc.textBetween(r,i,"\n","\n").split("\n").map(e=>o+e).join("\n");return e.replaceWith(r,i,t.schema.text(s)),!0})},"Shift-Tab":({editor:e})=>{if(!this.options.enableTabIndentation)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;return r.parent.type===this.type&&(i?e.commands.command(({tr:e})=>{var n;let{pos:i}=r,o=r.start(),s=r.end(),l=t.doc.textBetween(o,s,"\n","\n").split("\n"),a=0,d=0,h=i-o;for(let e=0;e<l.length;e+=1){if(d+l[e].length>=h){a=e;break}d+=l[e].length+1}let c=Math.min(((null==(n=l[a].match(/^ */))?void 0:n[0])||"").length,this.options.tabSize);if(0===c)return!0;let u=o;for(let e=0;e<a;e+=1)u+=l[e].length+1;return e.delete(u,u+c),i-u<=c&&e.setSelection(p.U3.create(e.doc,u)),!0}):e.commands.command(({tr:e})=>{let{from:r,to:i}=n,o=t.doc.textBetween(r,i,"\n","\n").split("\n").map(e=>{var t;let n=Math.min(((null==(t=e.match(/^ */))?void 0:t[0])||"").length,this.options.tabSize);return e.slice(n)}).join("\n");return e.replaceWith(r,i,t.schema.text(o)),!0}))},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!!o&&!!s&&e.chain().command(({tr:e})=>(e.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:r}=t,{$from:i,empty:o}=n;if(!o||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let s=i.after();return void 0!==s&&(r.nodeAt(s)?e.commands.command(({tr:e})=>(e.setSelection(p.LN.near(r.resolve(s))),!0)):e.commands.exitCode())}}},addInputRules(){return[(0,r.JJ)({find:u,type:this.type,getAttributes:e=>({language:e[1]})}),(0,r.JJ)({find:f,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new p.k_({key:new p.hs("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=null==i?void 0:i.mode;if(!n||!o)return!1;let{tr:s,schema:l}=e.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(p.U3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}}),g=r.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",(0,r.KV)(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:i}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=r.extensionManager,l=i||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&l&&o){let t=l.filter(e=>s.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),y=r.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,...{[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}}),{})},addInputRules(){return this.options.levels.map(e=>(0,r.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}}),b=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,v=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,w=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,k=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,x=r.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.OX)({find:b,type:this.type}),(0,r.OX)({find:w,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:v,type:this.type}),(0,r.Zc)({find:k,type:this.type})]}}),S=n(729),M=Object.defineProperty,C="textStyle",A=/^\s*([-+*])\s$/,T=r.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(C)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=(0,r.tG)({find:A,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.tG)({find:A,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(C),editor:this.editor})),[e]}}),O=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",(0,r.KV)(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}});((e,t)=>{for(var n in t)M(e,n,{get:t[n],enumerable:!0})})({},{findListItemPos:()=>E,getNextListDepth:()=>N,handleBackspace:()=>I,handleDelete:()=>$,hasListBefore:()=>D,hasListItemAfter:()=>B,hasListItemBefore:()=>R,listItemHasSubList:()=>L,nextListIsDeeper:()=>P,nextListIsHigher:()=>z});var E=(e,t)=>{let{$from:n}=t.selection,i=(0,r.Pg)(e,t.schema),o=null,s=n.depth,l=n.pos,a=null;for(;s>0&&null===a;)n.node(s).type===i?a=s:(s-=1,l-=1);return null===a?null:{$pos:t.doc.resolve(l),depth:a}},N=(e,t)=>{let n=E(e,t);if(!n)return!1;let[,i]=(0,r.fl)(t,e,n.$pos.pos+4);return i},D=(e,t,n)=>{let{$anchor:r}=e.selection,i=Math.max(0,r.pos-2),o=e.doc.resolve(i).node();return!!o&&!!n.includes(o.type.name)},R=(e,t)=>{var n;let{$anchor:r}=t.selection,i=t.doc.resolve(r.pos-2);return 0!==i.index()&&(null==(n=i.nodeBefore)?void 0:n.type.name)===e},L=(e,t,n)=>{if(!n)return!1;let i=(0,r.Pg)(e,t.schema),o=!1;return n.descendants(e=>{e.type===i&&(o=!0)}),o},I=(e,t,n)=>{if(e.commands.undoInputRule())return!0;if(e.state.selection.from!==e.state.selection.to)return!1;if(!(0,r.rU)(e.state,t)&&D(e.state,t,n)){let{$anchor:n}=e.state.selection,r=e.state.doc.resolve(n.before()-1),i=[];r.node().descendants((e,n)=>{e.type.name===t&&i.push({node:e,pos:n})});let o=i.at(-1);if(!o)return!1;let s=e.state.doc.resolve(r.start()+o.pos+1);return e.chain().cut({from:n.start()-1,to:n.end()+1},s.end()).joinForward().run()}if(!(0,r.rU)(e.state,t)||!(0,r.J_)(e.state))return!1;let i=E(t,e.state);if(!i)return!1;let o=e.state.doc.resolve(i.$pos.pos-2).node(i.depth),s=L(t,e.state,o);return R(t,e.state)&&!s?e.commands.joinItemBackward():e.chain().liftListItem(t).run()},P=(e,t)=>{let n=N(e,t),r=E(e,t);return!!r&&!!n&&n>r.depth},z=(e,t)=>{let n=N(e,t),r=E(e,t);return!!r&&!!n&&n<r.depth},$=(e,t)=>{if(!(0,r.rU)(e.state,t)||!(0,r.QN)(e.state,t))return!1;let{selection:n}=e.state,{$from:i,$to:o}=n;return!(!n.empty&&i.sameParent(o))&&(P(t,e.state)?e.chain().focus(e.state.selection.from+4).lift(t).joinBackward().run():z(t,e.state)?e.chain().joinForward().joinBackward().run():e.commands.joinItemForward())},B=(e,t)=>{var n;let{$anchor:r}=t.selection,i=t.doc.resolve(r.pos-r.parentOffset-2);return i.index()!==i.parent.childCount-1&&(null==(n=i.nodeAfter)?void 0:n.type.name)===e},F=r.YY.create({name:"listKeymap",addOptions:()=>({listTypes:[{itemName:"listItem",wrapperNames:["bulletList","orderedList"]},{itemName:"taskItem",wrapperNames:["taskList"]}]}),addKeyboardShortcuts(){return{Delete:({editor:e})=>{let t=!1;return this.options.listTypes.forEach(({itemName:n})=>{void 0!==e.state.schema.nodes[n]&&$(e,n)&&(t=!0)}),t},"Mod-Delete":({editor:e})=>{let t=!1;return this.options.listTypes.forEach(({itemName:n})=>{void 0!==e.state.schema.nodes[n]&&$(e,n)&&(t=!0)}),t},Backspace:({editor:e})=>{let t=!1;return this.options.listTypes.forEach(({itemName:n,wrapperNames:r})=>{void 0!==e.state.schema.nodes[n]&&I(e,n,r)&&(t=!0)}),t},"Mod-Backspace":({editor:e})=>{let t=!1;return this.options.listTypes.forEach(({itemName:n,wrapperNames:r})=>{void 0!==e.state.schema.nodes[n]&&I(e,n,r)&&(t=!0)}),t}}}}),j="textStyle",V=/^(\d+)\.\s$/,H=r.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",(0,r.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(j)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=(0,r.tG)({find:V,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.tG)({find:V,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(j)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),K=/^\s*(\[([( |x])?\])\s$/,J=r.bP.create({name:"taskItem",addOptions:()=>({nested:!1,HTMLAttributes:{},taskListTypeName:"taskList",a11y:void 0}),content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes:()=>({checked:{default:!1,keepOnSplit:!1,parseHTML:e=>{let t=e.getAttribute("data-checked");return""===t||"true"===t},renderHTML:e=>({"data-checked":e.checked})}}),parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:e,HTMLAttributes:t}){return["li",(0,r.KV)(this.options.HTMLAttributes,t,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:e.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){let e={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...e,Tab:()=>this.editor.commands.sinkListItem(this.name)}:e},addNodeView(){return({node:e,HTMLAttributes:t,getPos:n,editor:r})=>{let i=document.createElement("li"),o=document.createElement("label"),s=document.createElement("span"),l=document.createElement("input"),a=document.createElement("div"),d=e=>{var t,n;l.ariaLabel=(null==(n=null==(t=this.options.a11y)?void 0:t.checkboxLabel)?void 0:n.call(t,e,l.checked))||`Task item checkbox for ${e.textContent||"empty task item"}`};return d(e),o.contentEditable="false",l.type="checkbox",l.addEventListener("mousedown",e=>e.preventDefault()),l.addEventListener("change",t=>{if(!r.isEditable&&!this.options.onReadOnlyChecked){l.checked=!l.checked;return}let{checked:i}=t.target;r.isEditable&&"function"==typeof n&&r.chain().focus(void 0,{scrollIntoView:!1}).command(({tr:e})=>{let t=n();if("number"!=typeof t)return!1;let r=e.doc.nodeAt(t);return e.setNodeMarkup(t,void 0,{...null==r?void 0:r.attrs,checked:i}),!0}).run(),r.isEditable||!this.options.onReadOnlyChecked||this.options.onReadOnlyChecked(e,i)||(l.checked=!l.checked)}),Object.entries(this.options.HTMLAttributes).forEach(([e,t])=>{i.setAttribute(e,t)}),i.dataset.checked=e.attrs.checked,l.checked=e.attrs.checked,o.append(l,s),i.append(o,a),Object.entries(t).forEach(([e,t])=>{i.setAttribute(e,t)}),{dom:i,contentDOM:a,update:e=>e.type===this.type&&(i.dataset.checked=e.attrs.checked,l.checked=e.attrs.checked,d(e),!0)}}},addInputRules(){return[(0,r.tG)({find:K,type:this.type,getAttributes:e=>({checked:"x"===e[e.length-1]})})]}}),W=r.bP.create({name:"taskList",addOptions:()=>({itemTypeName:"taskItem",HTMLAttributes:{}}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:e}){return["ul",(0,r.KV)(this.options.HTMLAttributes,e,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:e})=>e.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}});r.YY.create({name:"listKit",addExtensions(){let e=[];return!1!==this.options.bulletList&&e.push(T.configure(this.options.bulletList)),!1!==this.options.listItem&&e.push(O.configure(this.options.listItem)),!1!==this.options.listKeymap&&e.push(F.configure(this.options.listKeymap)),!1!==this.options.orderedList&&e.push(H.configure(this.options.orderedList)),!1!==this.options.taskItem&&e.push(J.configure(this.options.taskItem)),!1!==this.options.taskList&&e.push(W.configure(this.options.taskList)),e}});var _=r.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),U=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,q=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,G=r.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.OX)({find:U,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:q,type:this.type})]}}),Y=r.CU.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("underline")&&{}}],renderHTML({HTMLAttributes:e}){return["u",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setUnderline:()=>({commands:e})=>e.setMark(this.name),toggleUnderline:()=>({commands:e})=>e.toggleMark(this.name),unsetUnderline:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),X=n(98539);class Z{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=t.width)?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i,o=this.editorView.dom,s=o.getBoundingClientRect(),l=s.width/o.offsetWidth,a=s.height/o.offsetHeight;if(r){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let r=n.getBoundingClientRect(),o=e?r.bottom:r.top;e&&t&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let s=this.width/2*a;i={left:r.left,right:r.right,top:o-s,bottom:o+s}}}}if(!i){let e=this.editorView.coordsAtPos(this.cursorPos),t=this.width/2*l;i={left:e.left-t,right:e.left+t,top:e.top,bottom:e.bottom}}let d=this.editorView.dom.offsetParent;if(!this.element&&(this.element=d.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),d&&(d!=document.body||"static"!=getComputedStyle(d).position)){let n=d.getBoundingClientRect(),r=n.width/d.offsetWidth,i=n.height/d.offsetHeight;e=n.left-d.scrollLeft*r,t=n.top-d.scrollTop*i}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=(i.left-e)/l+"px",this.element.style.top=(i.top-t)/a+"px",this.element.style.width=(i.right-i.left)/l+"px",this.element.style.height=(i.bottom-i.top)/a+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,t,e):r;if(t&&!i){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=(0,X.Um)(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}var Q=n(10664),ee=n(66030),et=n(42842);class en extends p.LN{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return en.valid(n)?new en(n):p.LN.near(n)}content(){return et.Ji.empty}eq(e){return e instanceof en&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new en(e.resolve(t.pos))}getBookmark(){return new er(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){n:for(;;){if(!n&&en.valid(e))return e;let r=e.pos,i=null;for(let n=e.depth;;n--){let o=e.node(n);if(t>0?e.indexAfter(n)<o.childCount:e.index(n)>0){i=o.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let s=e.doc.resolve(r);if(en.valid(s))return s}for(;;){let o=t>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!p.nh.isSelectable(i)){e=e.doc.resolve(r+i.nodeSize*t),n=!1;continue n}break}i=o,r+=t;let s=e.doc.resolve(r);if(en.valid(s))return s}return null}}}en.prototype.visible=!1,en.findFrom=en.findGapCursorFrom,p.LN.jsonID("gapcursor",en);class er{constructor(e){this.pos=e}map(e){return new er(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return en.valid(t)?new en(t):p.LN.near(t)}}let ei=(0,ee.K)({ArrowLeft:eo("horiz",-1),ArrowRight:eo("horiz",1),ArrowUp:eo("vert",-1),ArrowDown:eo("vert",1)});function eo(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,i){let o=e.selection,s=t>0?o.$to:o.$from,l=o.empty;if(o instanceof p.U3){if(!i.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=en.findGapCursorFrom(s,t,l);return!!a&&(r&&r(e.tr.setSelection(new en(a))),!0)}}function es(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!en.valid(r))return!1;let i=e.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&p.nh.isSelectable(e.state.doc.nodeAt(i.inside)))&&(e.dispatch(e.state.tr.setSelection(new en(r))),!0)}function el(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof en))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let i=et.FK.empty;for(let e=r.length-1;e>=0;e--)i=et.FK.from(r[e].createAndFill(null,i));let o=e.state.tr.replace(n.pos,n.pos,new et.Ji(i,0,0));return o.setSelection(p.U3.near(o.doc.resolve(n.pos+1))),e.dispatch(o),!1}function ea(e){if(!(e.selection instanceof en))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",Q.zF.create(e.doc,[Q.NZ.widget(e.selection.head,t,{key:"gapcursor"})])}var ed=function(){};ed.prototype.append=function(e){return e.length?(e=ed.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},ed.prototype.prepend=function(e){return e.length?ed.from(e).append(this):this},ed.prototype.appendInner=function(e){return new ec(this,e)},ed.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?ed.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},ed.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},ed.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},ed.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(t,n){return r.push(e(t,n))},t,n),r},ed.from=function(e){return e instanceof ed?e:e&&e.length?new eh(e):ed.empty};var eh=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var i=t;i<n;i++)if(!1===e(this.values[i],r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var i=t-1;i>=n;i--)if(!1===e(this.values[i],r+i))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(ed);ed.empty=new eh([]);var ec=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var i=this.left.length;if(t<i&&!1===this.left.forEachInner(e,t,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(e,Math.max(t-i,0),Math.min(this.length,n)-i,r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){var i=this.left.length;if(t>i&&!1===this.right.forEachInvertedInner(e,t-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(e,Math.min(t,i),n,r))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(ed);class ep{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,r,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}t&&(r=(n=this.remapping(s,this.items.length)).maps.length);let l=e.tr,a=[],d=[];return this.items.forEach((e,t)=>{if(!e.step){n||(r=(n=this.remapping(s,t+1)).maps.length),r--,d.push(e);return}if(n){d.push(new eu(e.map));let t=e.step.map(n.slice(r)),i;t&&l.maybeStep(t).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new eu(i,void 0,void 0,a.length+d.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(e.step);if(e.selection)return i=n?e.selection.map(n.slice(r)):e.selection,o=new ep(this.items.slice(0,s).append(d.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:l,selection:i}}addTransform(e,t,n,r){var i,o;let s,l=[],a=this.eventCount,d=this.items,h=!r&&d.length?d.get(d.length-1):null;for(let n=0;n<e.steps.length;n++){let i=e.steps[n].invert(e.docs[n]),o=new eu(e.mapping.maps[n],i,t),s;(s=h&&h.merge(o))&&(o=s,n?l.pop():d=d.slice(0,d.length-1)),l.push(o),t&&(a++,t=void 0),r||(h=o)}let c=a-n.depth;return c>em&&(i=d,o=c,i.forEach((e,t)=>{if(e.selection&&0==o--)return s=t,!1}),d=i.slice(s),a-=c),new ep(d.append(l),a)}remapping(e,t){let n=new X.X9;return this.items.forEach((t,r)=>{let i=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,i)},e,t),n}addMaps(e){return 0==this.eventCount?this:new ep(this.items.append(e.map(e=>new eu(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),i=e.mapping,o=e.steps.length,s=this.eventCount;this.items.forEach(e=>{e.selection&&s--},r);let l=t;this.items.forEach(t=>{let r=i.getMirror(--l);if(null==r)return;o=Math.min(o,r);let a=i.maps[r];if(t.step){let o=e.steps[r].invert(e.docs[r]),d=t.selection&&t.selection.map(i.slice(l+1,r));d&&s++,n.push(new eu(a,o,d))}else n.push(new eu(a))},r);let a=[];for(let e=t;e<o;e++)a.push(new eu(i.maps[e]));let d=new ep(this.items.slice(0,r).append(a).append(n),s);return d.emptyItemCount()>500&&(d=d.compress(this.items.length-n.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],i=0;return this.items.forEach((o,s)=>{if(s>=e)r.push(o),o.selection&&i++;else if(o.step){let e=o.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=o.selection&&o.selection.map(t.slice(n));l&&i++;let a=new eu(s.invert(),e,l),d,h=r.length-1;(d=r.length&&r[h].merge(a))?r[h]=d:r.push(a)}}else o.map&&n--},this.items.length,0),new ep(ed.from(r.reverse()),i)}}ep.empty=new ep(ed.empty,0);class eu{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new eu(t.getMap().invert(),t,this.selection)}}}class ef{constructor(e,t,n,r,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let em=20;function eg(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,r,i)=>t.push(r,i));return t}function ey(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let i=t.map(e[r],1),o=t.map(e[r+1],-1);i<=o&&n.push(i,o)}return n}let eb=!1,ev=null;function ew(e){let t=e.plugins;if(ev!=t){eb=!1,ev=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){eb=!0;break}}return eb}let ek=new p.hs("history"),ex=new p.hs("closeHistory");function eS(e,t){return(n,r)=>{let i=ek.getState(n);if(!i||0==(e?i.undone:i.done).eventCount)return!1;if(r){let o=function(e,t,n){let r=ew(t),i=ek.get(t).spec.config,o=(n?e.undone:e.done).popEvent(t,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(n?e.done:e.undone).addTransform(o.transform,t.selection.getBookmark(),i,r),a=new ef(n?l:o.remaining,n?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(ek,{redo:n,historyState:a})}(i,n,e);o&&r(t?o.scrollIntoView():o)}return!0}}let eM=eS(!1,!0),eC=eS(!0,!0);eS(!1,!1),eS(!0,!1),r.YY.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:e=>e.length,wordCounter:e=>e.split(" ").filter(e=>""!==e).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc;if("textSize"===((null==e?void 0:e.mode)||this.options.mode)){let e=t.textBetween(0,t.content.size,void 0," ");return this.options.textCounter(e)}return t.nodeSize},this.storage.words=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc,n=t.textBetween(0,t.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let e=!1;return[new p.k_({key:new p.hs("characterCount"),appendTransaction:(t,n,r)=>{if(e)return;let i=this.options.limit;if(null==i||0===i){e=!0;return}let o=this.storage.characters({node:r.doc});if(o>i){console.warn(`[CharacterCount] Initial content exceeded limit of ${i} characters. Content was automatically trimmed.`);let t=r.tr.deleteRange(0,o-i);return e=!0,t}e=!0},filterTransaction:(e,t)=>{let n=this.options.limit;if(!e.docChanged||0===n||null==n)return!0;let r=this.storage.characters({node:t.doc}),i=this.storage.characters({node:e.doc});if(i<=n||r>n&&i>n&&i<=r)return!0;if(r>n&&i>n&&i>r||!e.getMeta("paste"))return!1;let o=e.selection.$head.pos;return e.deleteRange(o-(i-n),o),!(this.storage.characters({node:e.doc})>n)}})]}});var eA=r.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new p.k_({view:t=>new Z(t,e)})}(this.options)]}});r.YY.create({name:"focus",addOptions:()=>({className:"has-focus",mode:"all"}),addProseMirrorPlugins(){return[new p.k_({key:new p.hs("focus"),props:{decorations:({doc:e,selection:t})=>{let{isEditable:n,isFocused:r}=this.editor,{anchor:i}=t,o=[];if(!n||!r)return Q.zF.create(e,[]);let s=0;"deepest"===this.options.mode&&e.descendants((e,t)=>{if(!e.isText){if(!(i>=t&&i<=t+e.nodeSize-1))return!1;s+=1}});let l=0;return e.descendants((e,t)=>!e.isText&&!!(i>=t&&i<=t+e.nodeSize-1)&&((l+=1,"deepest"===this.options.mode&&s-l>0||"shallowest"===this.options.mode&&l>1)?"deepest"===this.options.mode:void o.push(Q.NZ.node(t,t+e.nodeSize,{class:this.options.className})))),Q.zF.create(e,o)}}})]}});var eT=r.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new p.k_({props:{decorations:ea,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&en.valid(n)?new en(n):null,handleClick:es,handleKeyDown:ei,handleDOMEvents:{beforeinput:el}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!=(t=(0,r.gk)((0,r.iI)(e,"allowGapCursor",n)))?t:null}}});function eO({types:e,node:t}){return t&&Array.isArray(e)&&e.includes(t.type)||(null==t?void 0:t.type)===e}r.YY.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new p.k_({key:new p.hs("placeholder"),props:{decorations:({doc:e,selection:t})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:i}=t,o=[];if(!n)return null;let s=this.editor.isEmpty;return e.descendants((e,t)=>{let n=i>=t&&i<=t+e.nodeSize,l=!e.isLeaf&&(0,r.Op)(e);if((n||!this.options.showOnlyCurrent)&&l){let r=[this.options.emptyNodeClass];s&&r.push(this.options.emptyEditorClass);let i=Q.NZ.node(t,t+e.nodeSize,{class:r.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:e,pos:t,hasAnchor:n}):this.options.placeholder});o.push(i)}return this.options.includeChildren}),Q.zF.create(e,o)}}})]}}),r.YY.create({name:"selection",addOptions:()=>({className:"selection"}),addProseMirrorPlugins(){let{editor:e,options:t}=this;return[new p.k_({key:new p.hs("selection"),props:{decorations:n=>n.selection.empty||e.isFocused||!e.isEditable||(0,r.BQ)(n.selection)||e.view.dragging?null:Q.zF.create(n.doc,[Q.NZ.inline(n.selection.from,n.selection.to,{class:t.className})])}})]}});var eE=r.YY.create({name:"trailingNode",addOptions:()=>({node:"paragraph",notAfter:[]}),addProseMirrorPlugins(){let e=new p.hs(this.name),t=Object.entries(this.editor.schema.nodes).map(([,e])=>e).filter(e=>(this.options.notAfter||[]).concat(this.options.node).includes(e.name));return[new p.k_({key:e,appendTransaction:(t,n,r)=>{let{doc:i,tr:o,schema:s}=r,l=e.getState(r),a=i.content.size,d=s.nodes[this.options.node];if(l)return o.insert(a,d.create())},state:{init:(e,n)=>!eO({node:n.tr.doc.lastChild,types:t}),apply:(e,n)=>e.docChanged?!eO({node:e.doc.lastChild,types:t}):n}})]}}),eN=r.YY.create({name:"undoRedo",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>eM(e,t),redo:()=>({state:e,dispatch:t})=>eC(e,t)}),addProseMirrorPlugins(){return[function(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new p.k_({key:ek,state:{init:()=>new ef(ep.empty,ep.empty,null,0,-1),apply:(t,n,r)=>(function(e,t,n,r){let i=n.getMeta(ek),o;if(i)return i.historyState;n.getMeta(ex)&&(e=new ef(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(ek))if(s.getMeta(ek).redo)return new ef(e.done.addTransform(n,void 0,r,ew(t)),e.undone,eg(n.mapping.maps),e.prevTime,e.prevComposition);else return new ef(e.done,e.undone.addTransform(n,void 0,r,ew(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))if(o=n.getMeta("rebased"))return new ef(e.done.rebased(n,o),e.undone.rebased(n,o),ey(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);else return new ef(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),ey(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let i=n.getMeta("composition"),o=0==e.prevTime||!s&&e.prevComposition!=i&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,r)=>{for(let i=0;i<t.length;i+=2)e<=t[i+1]&&r>=t[i]&&(n=!0)}),n}(n,e.prevRanges)),l=s?ey(e.prevRanges,n.mapping):eg(n.mapping.maps);return new ef(e.done.addTransform(n,o?t.selection.getBookmark():void 0,r,ew(t)),ep.empty,l,n.time,null==i?e.prevComposition:i)}})(n,r,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?eM:"historyRedo"==n?eC:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}});!function(){var e=Error("Cannot find module '@tiptap/extension-code'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@tiptap/extension-document'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@tiptap/extension-horizontal-rule'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@tiptap/extension-text'");throw e.code="MODULE_NOT_FOUND",e}();var eD=r.YY.create({name:"starterKit",addExtensions(){var e,t,n,r;let i=[];return!1!==this.options.bold&&i.push(c.configure(this.options.bold)),!1!==this.options.blockquote&&i.push(s.configure(this.options.blockquote)),!1!==this.options.bulletList&&i.push(T.configure(this.options.bulletList)),!1!==this.options.code&&i.push(Object(function(){var e=Error("Cannot find module '@tiptap/extension-code'");throw e.code="MODULE_NOT_FOUND",e}()).configure(this.options.code)),!1!==this.options.codeBlock&&i.push(m.configure(this.options.codeBlock)),!1!==this.options.document&&i.push(Object(function(){var e=Error("Cannot find module '@tiptap/extension-document'");throw e.code="MODULE_NOT_FOUND",e}()).configure(this.options.document)),!1!==this.options.dropcursor&&i.push(eA.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&i.push(eT.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&i.push(g.configure(this.options.hardBreak)),!1!==this.options.heading&&i.push(y.configure(this.options.heading)),!1!==this.options.undoRedo&&i.push(eN.configure(this.options.undoRedo)),!1!==this.options.horizontalRule&&i.push(Object(function(){var e=Error("Cannot find module '@tiptap/extension-horizontal-rule'");throw e.code="MODULE_NOT_FOUND",e}()).configure(this.options.horizontalRule)),!1!==this.options.italic&&i.push(x.configure(this.options.italic)),!1!==this.options.listItem&&i.push(O.configure(this.options.listItem)),!1!==this.options.listKeymap&&i.push(F.configure(null==(e=this.options)?void 0:e.listKeymap)),!1!==this.options.link&&i.push(S.N_.configure(null==(t=this.options)?void 0:t.link)),!1!==this.options.orderedList&&i.push(H.configure(this.options.orderedList)),!1!==this.options.paragraph&&i.push(_.configure(this.options.paragraph)),!1!==this.options.strike&&i.push(G.configure(this.options.strike)),!1!==this.options.text&&i.push(Object(function(){var e=Error("Cannot find module '@tiptap/extension-text'");throw e.code="MODULE_NOT_FOUND",e}()).configure(this.options.text)),!1!==this.options.underline&&i.push(Y.configure(null==(n=this.options)?void 0:n.underline)),!1!==this.options.trailingNode&&i.push(eE.configure(null==(r=this.options)?void 0:r.trailingNode)),i}})},53744:(e,t,n)=>{n.d(t,{KE:()=>nd,YY:()=>e3,CU:()=>eX,Db:()=>nm,bP:()=>ng,Yv:()=>ny,gk:()=>el,T7:()=>G,Nx:()=>ee,eL:()=>et,gu:()=>eS,FF:()=>eM,iI:()=>er,hO:()=>eN,fl:()=>eD,Pg:()=>ek,zU:()=>ec,QN:()=>e$,J_:()=>eB,rU:()=>ez,Op:()=>eV,BQ:()=>eH,OX:()=>nh,Zc:()=>nb,KV:()=>eh,jT:()=>nc,JJ:()=>np,tG:()=>nu});var r,i=n(42404),o=n(10664),s=n(66030),l=n(98539),a=n(42842);let d=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function h(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let c=(e,t,n)=>{let r=h(e,n);if(!r)return!1;let o=y(r);if(!o){let n=r.blockRange(),i=n&&(0,l.jP)(n);return null!=i&&(t&&t(e.tr.lift(n,i).scrollIntoView()),!0)}let s=o.nodeBefore;if(D(e,o,t,-1))return!0;if(0==r.parent.content.size&&(m(s,"end")||i.nh.isSelectable(s)))for(let n=r.depth;;n--){let d=(0,l.$L)(e.doc,r.before(n),r.after(n),a.Ji.empty);if(d&&d.slice.size<d.to-d.from){if(t){let n=e.tr.step(d);n.setSelection(m(s,"end")?i.LN.findFrom(n.doc.resolve(n.mapping.map(o.pos,-1)),-1):i.nh.create(n.doc,o.pos-s.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!!s.isAtom&&o.depth==r.depth-1&&(t&&t(e.tr.delete(o.pos-s.nodeSize,o.pos).scrollIntoView()),!0)},p=(e,t,n)=>{let r=h(e,n);if(!r)return!1;let i=y(r);return!!i&&f(e,i,t)},u=(e,t,n)=>{let r=b(e,n);if(!r)return!1;let i=k(r);return!!i&&f(e,i,t)};function f(e,t,n){let r=t.nodeBefore,o=t.pos-1;for(;!r.isTextblock;o--){if(r.type.spec.isolating)return!1;let e=r.lastChild;if(!e)return!1;r=e}let s=t.nodeAfter,d=t.pos+1;for(;!s.isTextblock;d++){if(s.type.spec.isolating)return!1;let e=s.firstChild;if(!e)return!1;s=e}let h=(0,l.$L)(e.doc,o,d,a.Ji.empty);if(!h||h.from!=o||h instanceof l.Ln&&h.slice.size>=d-o)return!1;if(n){let t=e.tr.step(h);t.setSelection(i.U3.create(t.doc,o)),n(t.scrollIntoView())}return!0}function m(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let g=(e,t,n)=>{let{$head:r,empty:o}=e.selection,s=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):r.parentOffset>0)return!1;s=y(r)}let l=s&&s.nodeBefore;return!!l&&!!i.nh.isSelectable(l)&&(t&&t(e.tr.setSelection(i.nh.create(e.doc,s.pos-l.nodeSize)).scrollIntoView()),!0)};function y(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function b(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let v=(e,t,n)=>{let r=b(e,n);if(!r)return!1;let o=k(r);if(!o)return!1;let s=o.nodeAfter;if(D(e,o,t,1))return!0;if(0==r.parent.content.size&&(m(s,"start")||i.nh.isSelectable(s))){let n=(0,l.$L)(e.doc,r.before(),r.after(),a.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(m(s,"start")?i.LN.findFrom(r.doc.resolve(r.mapping.map(o.pos)),1):i.nh.create(r.doc,r.mapping.map(o.pos))),t(r.scrollIntoView())}return!0}}return!!s.isAtom&&o.depth==r.depth-1&&(t&&t(e.tr.delete(o.pos,o.pos+s.nodeSize).scrollIntoView()),!0)},w=(e,t,n)=>{let{$head:r,empty:o}=e.selection,s=r;if(!o)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):r.parentOffset<r.parent.content.size)return!1;s=k(r)}let l=s&&s.nodeAfter;return!!l&&!!i.nh.isSelectable(l)&&(t&&t(e.tr.setSelection(i.nh.create(e.doc,s.pos)).scrollIntoView()),!0)};function k(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let x=(e,t)=>{let n=e.selection,r=n instanceof i.nh,o;if(r){if(n.node.isTextblock||!(0,l.n9)(e.doc,n.from))return!1;o=n.from}else if(null==(o=(0,l.N0)(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(o);r&&n.setSelection(i.nh.create(n.doc,o-e.doc.resolve(o).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},S=(e,t)=>{let n=e.selection,r;if(n instanceof i.nh){if(n.node.isTextblock||!(0,l.n9)(e.doc,n.to))return!1;r=n.to}else if(null==(r=(0,l.N0)(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(r).scrollIntoView()),!0},M=(e,t)=>{let{$from:n,$to:r}=e.selection,i=n.blockRange(r),o=i&&(0,l.jP)(i);return null!=o&&(t&&t(e.tr.lift(i,o).scrollIntoView()),!0)},C=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!!n.parent.type.spec.code&&!!n.sameParent(r)&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function A(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let T=(e,t)=>{let{$head:n,$anchor:r}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let o=n.node(-1),s=n.indexAfter(-1),l=A(o.contentMatchAt(s));if(!l||!o.canReplaceWith(s,s,l))return!1;if(t){let r=n.after(),o=e.tr.replaceWith(r,r,l.createAndFill());o.setSelection(i.LN.near(o.doc.resolve(r),1)),t(o.scrollIntoView())}return!0},O=(e,t)=>{let n=e.selection,{$from:r,$to:o}=n;if(n instanceof i.i5||r.parent.inlineContent||o.parent.inlineContent)return!1;let s=A(o.parent.contentMatchAt(o.indexAfter()));if(!s||!s.isTextblock)return!1;if(t){let n=(!r.parentOffset&&o.index()<o.parent.childCount?r:o).pos,l=e.tr.insert(n,s.createAndFill());l.setSelection(i.U3.create(l.doc,n+1)),t(l.scrollIntoView())}return!0},E=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if((0,l.zy)(e.doc,r))return t&&t(e.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),i=r&&(0,l.jP)(r);return null!=i&&(t&&t(e.tr.lift(r,i).scrollIntoView()),!0)},N=(e,t)=>{let{$from:n,to:r}=e.selection,o,s=n.sharedDepth(r);return 0!=s&&(o=n.before(s),t&&t(e.tr.setSelection(i.nh.create(e.doc,o))),!0)};function D(e,t,n,r){let o,s,d,h=t.nodeBefore,c=t.nodeAfter,p,u,f=h.type.spec.isolating||c.type.spec.isolating;if(!f&&(o=t.nodeBefore,s=t.nodeAfter,d=t.index(),o&&s&&o.type.compatibleContent(s.type)&&(!o.content.size&&t.parent.canReplace(d-1,d)?(n&&n(e.tr.delete(t.pos-o.nodeSize,t.pos).scrollIntoView()),!0):!!t.parent.canReplace(d,d+1)&&!!(s.isTextblock||(0,l.n9)(e.doc,t.pos))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let g=!f&&t.parent.canReplace(t.index(),t.index()+1);if(g&&(p=(u=h.contentMatchAt(h.childCount)).findWrapping(c.type))&&u.matchType(p[0]||c.type).validEnd){if(n){let r=t.pos+c.nodeSize,i=a.FK.empty;for(let e=p.length-1;e>=0;e--)i=a.FK.from(p[e].create(null,i));i=a.FK.from(h.copy(i));let o=e.tr.step(new l.Wg(t.pos-1,r,t.pos,r,new a.Ji(i,1,0),p.length,!0)),s=o.doc.resolve(r+2*p.length);s.nodeAfter&&s.nodeAfter.type==h.type&&(0,l.n9)(o.doc,s.pos)&&o.join(s.pos),n(o.scrollIntoView())}return!0}let y=c.type.spec.isolating||r>0&&f?null:i.LN.findFrom(t,1),b=y&&y.$from.blockRange(y.$to),v=b&&(0,l.jP)(b);if(null!=v&&v>=t.depth)return n&&n(e.tr.lift(b,v).scrollIntoView()),!0;if(g&&m(c,"start",!0)&&m(h,"end")){let r=h,i=[];for(;i.push(r),!r.isTextblock;)r=r.lastChild;let o=c,s=1;for(;!o.isTextblock;o=o.firstChild)s++;if(r.canReplace(r.childCount,r.childCount,o.content)){if(n){let r=a.FK.empty;for(let e=i.length-1;e>=0;e--)r=a.FK.from(i[e].copy(r));n(e.tr.step(new l.Wg(t.pos-i.length,t.pos+c.nodeSize,t.pos+s,t.pos+c.nodeSize-s,new a.Ji(r,i.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function R(e){return function(t,n){let r=t.selection,o=e<0?r.$from:r.$to,s=o.depth;for(;o.node(s).isInline;){if(!s)return!1;s--}return!!o.node(s).isTextblock&&(n&&n(t.tr.setSelection(i.U3.create(t.doc,e<0?o.start(s):o.end(s)))),!0)}}let L=R(-1),I=R(1);function P(e,t=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(o,s,(r,o)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(e,t)))if(r.type==e)i=!0;else{let t=n.doc.resolve(o),r=t.index();i=t.parent.canReplaceWith(r,r+1,e)}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];i.setBlockType(o,s,e,t)}r(i.scrollIntoView())}return!0}}function z(...e){return function(t,n,r){for(let i=0;i<e.length;i++)if(e[i](t,n,r))return!0;return!1}}let $=z(d,c,g),B=z(d,v,w),F={Enter:z(C,O,E,(e,t)=>{let{$from:n,$to:r}=e.selection;if(e.selection instanceof i.nh&&e.selection.node.isBlock)return!!n.parentOffset&&!!(0,l.zy)(e.doc,n.pos)&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let o=[],s,a,d=!1,h=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;d=n.end(e)==n.pos+(n.depth-e),h=n.start(e)==n.pos-(n.depth-e),a=A(n.node(e-1).contentMatchAt(n.indexAfter(e-1)));o.unshift(t||(d&&a?{type:a}:null)),s=e;break}if(1==e)return!1;o.unshift(null)}let c=e.tr;(e.selection instanceof i.U3||e.selection instanceof i.i5)&&c.deleteSelection();let p=c.mapping.map(n.pos),u=(0,l.zy)(c.doc,p,o.length,o);if(u||(o[0]=a?{type:a}:null,u=(0,l.zy)(c.doc,p,o.length,o)),!u)return!1;if(c.split(p,o.length,o),!d&&h&&n.node(s).type!=a){let e=c.mapping.map(n.before(s)),t=c.doc.resolve(e);a&&n.node(s-1).canReplaceWith(t.index(),t.index()+1,a)&&c.setNodeMarkup(c.mapping.map(n.before(s)),a)}return t&&t(c.scrollIntoView()),!0}),"Mod-Enter":T,Backspace:$,"Mod-Backspace":$,"Shift-Backspace":$,Delete:B,"Mod-Delete":B,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new i.i5(e.doc))),!0)},j={"Ctrl-h":F.Backspace,"Alt-Backspace":F["Mod-Backspace"],"Ctrl-d":F.Delete,"Ctrl-Alt-Backspace":F["Mod-Delete"],"Alt-Delete":F["Mod-Delete"],"Alt-d":F["Mod-Delete"],"Ctrl-a":L,"Ctrl-e":I};for(let e in F)j[e]=F[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();let V=["ol",0],H=["ul",0],K=["li",0];var J=Object.defineProperty,W=(e,t)=>{for(var n in t)J(e,n,{get:t[n],enumerable:!0})};function _(e){let{state:t,transaction:n}=e,{selection:r}=n,{doc:i}=n,{storedMarks:o}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return o},get selection(){return r},get doc(){return i},get tr(){return r=n.selection,i=n.doc,o=n.storedMarks,n}}}var U=class{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:e,editor:t,state:n}=this,{view:r}=t,{tr:i}=n,o=this.buildProps(i);return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,(...e)=>{let n=t(...e)(o);return i.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(i),n}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s=[],l=!!e,a=e||i.tr,d={...Object.fromEntries(Object.entries(n).map(([e,n])=>[e,(...e)=>{let r=this.buildProps(a,t),i=n(...e)(r);return s.push(i),d}])),run:()=>(l||!t||a.getMeta("preventDispatch")||this.hasCustomState||o.dispatch(a),s.every(e=>!0===e))};return d}createCan(e){let{rawCommands:t,state:n}=this,r=e||n.tr,i=this.buildProps(r,!1);return{...Object.fromEntries(Object.entries(t).map(([e,t])=>[e,(...e)=>t(...e)({...i,dispatch:void 0})])),chain:()=>this.createChain(r,!1)}}buildProps(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s={tr:e,editor:r,view:o,state:_({state:i,transaction:e}),dispatch:t?()=>void 0:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([e,t])=>[e,(...e)=>t(...e)(s)]))}};return s}},q=class{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){let n=this.callbacks[e];return n&&n.forEach(e=>e.apply(this,t)),this}off(e,t){let n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(e=>e!==t):delete this.callbacks[e]),this}once(e,t){let n=(...r)=>{this.off(e,n),t.apply(this,r)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}};function G(e,t){let n=new l.dL(e);return t.forEach(e=>{e.steps.forEach(e=>{n.step(e)})}),n}var Y=e=>{let t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){let r=t[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?e.removeChild(r):1===r.nodeType&&Y(r)}return e};function X(e){if("undefined"==typeof window)throw Error("[tiptap error]: there is no window object available, so this function cannot be used");let t=`<body>${e}</body>`;return Y(new window.DOMParser().parseFromString(t,"text/html").body)}function Z(e,t,n){if(e instanceof a.bP||e instanceof a.FK)return e;n={slice:!0,parseOptions:{},...n};let r="object"==typeof e&&null!==e,i="string"==typeof e;if(r)try{if(Array.isArray(e)&&e.length>0)return a.FK.fromArray(e.map(e=>t.nodeFromJSON(e)));let r=t.nodeFromJSON(e);return n.errorOnInvalidContent&&r.check(),r}catch(r){if(n.errorOnInvalidContent)throw Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",r),Z("",t,n)}if(i){if(n.errorOnInvalidContent){let r=!1,i="",o=new a.Sj({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(r=!0,i="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?a.S4.fromSchema(o).parseSlice(X(e),n.parseOptions):a.S4.fromSchema(o).parse(X(e),n.parseOptions),n.errorOnInvalidContent&&r)throw Error("[tiptap error]: Invalid HTML content",{cause:Error(`Invalid element found: ${i}`)})}let r=a.S4.fromSchema(t);return n.slice?r.parseSlice(X(e),n.parseOptions).content:r.parse(X(e),n.parseOptions)}return Z("",t,n)}function Q(e,t,n={},r={}){return Z(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function ee(e,t,n){let r=[];return e.nodesBetween(t.from,t.to,(e,t)=>{n(e)&&r.push({node:e,pos:t})}),r}function et(e,t){for(let n=e.depth;n>0;n-=1){let r=e.node(n);if(t(r))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:r}}}function en(e){return t=>et(t.$from,e)}function er(e,t,n){return void 0===e.config[t]&&e.parent?er(e.parent,t,n):"function"==typeof e.config[t]?e.config[t].bind({...n,parent:e.parent?er(e.parent,t,n):null}):e.config[t]}function ei(e){return e.map(e=>{let t={name:e.name,options:e.options,storage:e.storage},n=er(e,"addExtensions",t);return n?[e,...ei(n())]:e}).flat(10)}function eo(e,t){let n=a.ZF.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}function es(e){return"function"==typeof e}function el(e,t,...n){return es(e)?t?e.bind(t)(...n):e(...n):e}function ea(e){let t=e.filter(e=>"extension"===e.type);return{baseExtensions:t,nodeExtensions:e.filter(e=>"node"===e.type),markExtensions:e.filter(e=>"mark"===e.type)}}function ed(e){let t=[],{nodeExtensions:n,markExtensions:r}=ea(e),i=[...n,...r],o={default:null,validate:void 0,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage,extensions:i},r=er(e,"addGlobalAttributes",n);r&&r().forEach(e=>{e.types.forEach(n=>{Object.entries(e.attributes).forEach(([e,r])=>{t.push({type:n,name:e,attribute:{...o,...r}})})})})}),i.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage},r=er(e,"addAttributes",n);r&&Object.entries(r()).forEach(([n,r])=>{let i={...o,...r};"function"==typeof(null==i?void 0:i.default)&&(i.default=i.default()),(null==i?void 0:i.isRequired)&&(null==i?void 0:i.default)===void 0&&delete i.default,t.push({type:e.name,name:n,attribute:i})})}),t}function eh(...e){return e.filter(e=>!!e).reduce((e,t)=>{let n={...e};return Object.entries(t).forEach(([e,t])=>{if(!n[e]){n[e]=t;return}if("class"===e){let r=t?String(t).split(" "):[],i=n[e]?n[e].split(" "):[],o=r.filter(e=>!i.includes(e));n[e]=[...i,...o].join(" ")}else if("style"===e){let r=t?t.split(";").map(e=>e.trim()).filter(Boolean):[],i=n[e]?n[e].split(";").map(e=>e.trim()).filter(Boolean):[],o=new Map;i.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),r.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),n[e]=Array.from(o.entries()).map(([e,t])=>`${e}: ${t}`).join("; ")}else n[e]=t}),n},{})}function ec(e,t){return t.filter(t=>t.type===e.type.name).filter(e=>e.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]}).reduce((e,t)=>eh(e,t),{})}function ep(e,t){return"style"in e?e:{...e,getAttrs:n=>{let r=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===r)return!1;let i=t.reduce((e,t)=>{var r;let i=t.attribute.parseHTML?t.attribute.parseHTML(n):"string"!=typeof(r=n.getAttribute(t.name))?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):"true"===r||"false"!==r&&r;return null==i?e:{...e,[t.name]:i}},{});return{...r,...i}}}}function eu(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>!("attrs"===e&&function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t))}function ef(e,t){var n;let r=ed(e),{nodeExtensions:i,markExtensions:o}=ea(e),s=null==(n=i.find(e=>er(e,"topNode")))?void 0:n.name,l=Object.fromEntries(i.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=eu({...e.reduce((e,t)=>{let r=er(t,"extendNodeSchema",o);return{...e,...r?r(n):{}}},{}),content:el(er(n,"content",o)),marks:el(er(n,"marks",o)),group:el(er(n,"group",o)),inline:el(er(n,"inline",o)),atom:el(er(n,"atom",o)),selectable:el(er(n,"selectable",o)),draggable:el(er(n,"draggable",o)),code:el(er(n,"code",o)),whitespace:el(er(n,"whitespace",o)),linebreakReplacement:el(er(n,"linebreakReplacement",o)),defining:el(er(n,"defining",o)),isolating:el(er(n,"isolating",o)),attrs:Object.fromEntries(i.map(e=>{var t,n;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default,validate:null==(n=null==e?void 0:e.attribute)?void 0:n.validate}]}))}),l=el(er(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>ep(e,i)));let a=er(n,"renderHTML",o);a&&(s.toDOM=e=>a({node:e,HTMLAttributes:ec(e,i)}));let d=er(n,"renderText",o);return d&&(s.toText=d),[n.name,s]})),d=Object.fromEntries(o.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=eu({...e.reduce((e,t)=>{let r=er(t,"extendMarkSchema",o);return{...e,...r?r(n):{}}},{}),inclusive:el(er(n,"inclusive",o)),excludes:el(er(n,"excludes",o)),group:el(er(n,"group",o)),spanning:el(er(n,"spanning",o)),code:el(er(n,"code",o)),attrs:Object.fromEntries(i.map(e=>{var t,n;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default,validate:null==(n=null==e?void 0:e.attribute)?void 0:n.validate}]}))}),l=el(er(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>ep(e,i)));let a=er(n,"renderHTML",o);return a&&(s.toDOM=e=>a({mark:e,HTMLAttributes:ec(e,i)})),[n.name,s]}));return new a.Sj({topNode:s,nodes:l,marks:d})}function em(e){return e.sort((e,t)=>{let n=er(e,"priority")||100,r=er(t,"priority")||100;return n>r?-1:+(n<r)})}function eg(e){var t;let n=em(ei(e)),r=Array.from(new Set((t=n.map(e=>e.name)).filter((e,n)=>t.indexOf(e)!==n)));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(e=>`'${e}'`).join(", ")}]. This can lead to issues.`),n}function ey(e,t,n){let{from:r,to:i}=t,{blockSeparator:o="\n\n",textSerializers:s={}}=n||{},l="";return e.nodesBetween(r,i,(e,n,a,d)=>{var h;e.isBlock&&n>r&&(l+=o);let c=null==s?void 0:s[e.type.name];if(c)return a&&(l+=c({node:e,pos:n,parent:a,index:d,range:t})),!1;e.isText&&(l+=null==(h=null==e?void 0:e.text)?void 0:h.slice(Math.max(r,n)-n,i-n))}),l}function eb(e){return Object.fromEntries(Object.entries(e.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}function ev(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function ew(e,t){let n=ev(t,e.schema),{from:r,to:i,empty:o}=e.selection,s=[];o?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(r,i,e=>{s.push(...e.marks)});let l=s.find(e=>e.type.name===n.name);return l?{...l.attrs}:{}}function ek(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function ex(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function eS(e,t){let n=ex("string"==typeof t?t:t.name,e.schema);if("node"===n){let n=ek(t,e.schema),{from:r,to:i}=e.selection,o=[];e.doc.nodesBetween(r,i,e=>{o.push(e)});let s=o.reverse().find(e=>e.type.name===n.name);return s?{...s.attrs}:{}}return"mark"===n?ew(e,t):{}}function eM(e){let{mapping:t,steps:n}=e,r=[];t.maps.forEach((e,i)=>{let o=[];if(e.ranges.length)e.forEach((e,t)=>{o.push({from:e,to:t})});else{let{from:e,to:t}=n[i];if(void 0===e||void 0===t)return;o.push({from:e,to:t})}o.forEach(({from:e,to:n})=>{let o=t.slice(i).map(e,-1),s=t.slice(i).map(n),l=t.invert().map(o,-1),a=t.invert().map(s);r.push({oldRange:{from:l,to:a},newRange:{from:o,to:s}})})});let i=function(e,t=JSON.stringify){let n={};return e.filter(e=>{let r=t(e);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)})}(r);return 1===i.length?i:i.filter((e,t)=>!i.filter((e,n)=>n!==t).some(t=>e.oldRange.from>=t.oldRange.from&&e.oldRange.to<=t.oldRange.to&&e.newRange.from>=t.newRange.from&&e.newRange.to<=t.newRange.to))}function eC(e){return"[object RegExp]"===Object.prototype.toString.call(e)}function eA(e,t,n={strict:!0}){let r=Object.keys(t);return!r.length||r.every(r=>n.strict?t[r]===e[r]:eC(t[r])?t[r].test(e[r]):t[r]===e[r])}function eT(e,t,n={}){return e.find(e=>e.type===t&&eA(Object.fromEntries(Object.keys(n).map(t=>[t,e.attrs[t]])),n))}function eO(e,t,n={}){return!!eT(e,t,n)}function eE(e,t,n){var r;if(!e||!t)return;let i=e.parent.childAfter(e.parentOffset);if(i.node&&i.node.marks.some(e=>e.type===t)||(i=e.parent.childBefore(e.parentOffset)),!i.node||!i.node.marks.some(e=>e.type===t)||(n=n||(null==(r=i.node.marks[0])?void 0:r.attrs),!eT([...i.node.marks],t,n)))return;let o=i.index,s=e.start()+i.offset,l=o+1,a=s+i.node.nodeSize;for(;o>0&&eO([...e.parent.child(o-1).marks],t,n);)o-=1,s-=e.parent.child(o).nodeSize;for(;l<e.parent.childCount&&eO([...e.parent.child(l).marks],t,n);)a+=e.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function eN(e,t,n){let r=[];return e===t?n.resolve(e).marks().forEach(t=>{let i=eE(n.resolve(e),t.type);i&&r.push({mark:t,...i})}):n.nodesBetween(e,t,(e,t)=>{e&&(null==e?void 0:e.nodeSize)!==void 0&&r.push(...e.marks.map(n=>({from:t,to:t+e.nodeSize,mark:n})))}),r}var eD=(e,t,n,r=20)=>{let i=e.doc.resolve(n),o=r,s=null;for(;o>0&&null===s;){let e=i.node(o);(null==e?void 0:e.type.name)===t?s=e:o-=1}return[s,o]};function eR(e,t){return t.nodes[e]||t.marks[e]||null}function eL(e,t,n){return Object.fromEntries(Object.entries(n).filter(([n])=>{let r=e.find(e=>e.type===t&&e.name===n);return!!r&&r.attribute.keepOnSplit}))}var eI=(e,t=500)=>{let n="",r=e.parentOffset;return e.parent.nodesBetween(Math.max(0,r-t),r,(e,t,i,o)=>{var s,l;let a=(null==(l=(s=e.type.spec).toText)?void 0:l.call(s,{node:e,pos:t,parent:i,index:o}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?a:a.slice(0,Math.max(0,r-t))}),n};function eP(e,t,n={}){let{empty:r,ranges:i}=e.selection,o=t?ev(t,e.schema):null;if(r)return!!(e.storedMarks||e.selection.$from.marks()).filter(e=>!o||o.name===e.type.name).find(e=>eA(e.attrs,n,{strict:!1}));let s=0,l=[];if(i.forEach(({$from:t,$to:n})=>{let r=t.pos,i=n.pos;e.doc.nodesBetween(r,i,(e,t)=>{if(!e.isText&&!e.marks.length)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);s+=o-n,l.push(...e.marks.map(e=>({mark:e,from:n,to:o})))})}),0===s)return!1;let a=l.filter(e=>!o||o.name===e.mark.type.name).filter(e=>eA(e.mark.attrs,n,{strict:!1})).reduce((e,t)=>e+t.to-t.from,0),d=l.filter(e=>!o||e.mark.type!==o&&e.mark.type.excludes(o)).reduce((e,t)=>e+t.to-t.from,0);return(a>0?a+d:a)>=s}function ez(e,t,n={}){let{from:r,to:i,empty:o}=e.selection,s=t?ek(t,e.schema):null,l=[];e.doc.nodesBetween(r,i,(e,t)=>{if(e.isText)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);l.push({node:e,from:n,to:o})});let a=i-r,d=l.filter(e=>!s||s.name===e.node.type.name).filter(e=>eA(e.node.attrs,n,{strict:!1}));return o?!!d.length:d.reduce((e,t)=>e+t.to-t.from,0)>=a}var e$=(e,t)=>{let{$from:n,$to:r,$anchor:i}=e.selection;if(t){let n=en(e=>e.type.name===t)(e.selection);if(!n)return!1;let r=e.doc.resolve(n.pos+1);return i.pos+1===r.end()}return!(r.parentOffset<r.parent.nodeSize-2)&&n.pos===r.pos},eB=e=>{let{$from:t,$to:n}=e.selection;return!(t.parentOffset>0)&&t.pos===n.pos};function eF(e,t){return Array.isArray(t)?t.some(t=>("string"==typeof t?t:t.name)===e.name):t}function ej(e,t){let{nodeExtensions:n}=ea(t),r=n.find(t=>t.name===e);if(!r)return!1;let i={name:r.name,options:r.options,storage:r.storage},o=el(er(r,"group",i));return"string"==typeof o&&o.split(" ").includes("list")}function eV(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!=(r=e.text)?r:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let r=!0;return e.content.forEach(e=>{!1!==r&&(eV(e,{ignoreWhitespace:n,checkChildren:t})||(r=!1))}),r}return!1}function eH(e){return e instanceof i.nh}function eK(e){return e instanceof i.U3}function eJ(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function eW(e,t=null){if(!t)return null;let n=i.LN.atStart(e),r=i.LN.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return r;let o=n.from,s=r.to;return"all"===t?i.U3.create(e,eJ(0,o,s),eJ(e.content.size,o,s)):i.U3.create(e,eJ(t,o,s),eJ(t,o,s))}var e_=class{constructor(e){this.find=e.find,this.handler=e.handler}},eU=(e,t)=>{if(eC(t))return t.exec(e);let n=t(e);if(!n)return null;let r=[n.text];return r.index=n.index,r.input=e,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function eq(e){var t;let{editor:n,from:r,to:i,text:o,rules:s,plugin:l}=e,{view:a}=n;if(a.composing)return!1;let d=a.state.doc.resolve(r);if(d.parent.type.spec.code||(null==(t=d.nodeBefore||d.nodeAfter)?void 0:t.marks.find(e=>e.type.spec.code)))return!1;let h=!1,c=eI(d)+o;return s.forEach(e=>{if(h)return;let t=eU(c,e.find);if(!t)return;let s=a.state.tr,d=_({state:a.state,transaction:s}),p={from:r-(t[0].length-o.length),to:i},{commands:u,chain:f,can:m}=new U({editor:n,state:d});null!==e.handler({state:d,range:p,match:t,commands:u,chain:f,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:r,to:i,text:o}),a.dispatch(s),h=!0)}),h}function eG(e){return"Object"===Object.prototype.toString.call(e).slice(8,-1)&&e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype}var eY=class{constructor(e={}){this.type="extendable",this.parent=null,this.child=null,this.name="",this.config={name:this.name},this.config={...this.config,...e},this.name=this.config.name}get options(){return{...el(er(this,"addOptions",{name:this.name}))||{}}}get storage(){return{...el(er(this,"addStorage",{name:this.name,options:this.options}))||{}}}configure(e={}){let t=this.extend({...this.config,addOptions:()=>(function e(t,n){let r={...t};return eG(t)&&eG(n)&&Object.keys(n).forEach(i=>{eG(n[i])&&eG(t[i])?r[i]=e(t[i],n[i]):r[i]=n[i]}),r})(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new this.constructor({...this.config,...e});return t.parent=this,this.child=t,t.name="name"in e?e.name:t.parent.name,t}},eX=class e extends eY{constructor(){super(...arguments),this.type="mark"}static create(t={}){return new e("function"==typeof t?t():t)}static handleExit({editor:e,mark:t}){let{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){let i=r.marks();if(!i.find(e=>(null==e?void 0:e.type.name)===t.name))return!1;let o=i.find(e=>(null==e?void 0:e.type.name)===t.name);return o&&n.removeStoredMark(o),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}configure(e){return super.configure(e)}extend(e){let t="function"==typeof e?e():e;return super.extend(t)}},eZ=class{constructor(e){this.find=e.find,this.handler=e.handler}},eQ=(e,t,n)=>{if(eC(t))return[...e.matchAll(t)];let r=t(e,n);return r?r.map(t=>{let n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n}):[]},e0=null,e1=e=>{var t;let n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null==(t=n.clipboardData)||t.setData("text/html",e),n},e2=class{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=eg(e),this.schema=ef(this.extensions,t),this.setupExtensions()}get commands(){return this.extensions.reduce((e,t)=>{let n={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:this.editor,type:eR(t.name,this.schema)},r=er(t,"addCommands",n);return r?{...e,...r()}:e},{})}get plugins(){let{editor:e}=this;return em([...this.extensions].reverse()).map(t=>{let n={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:e,type:eR(t.name,this.schema)},r=[],o=er(t,"addKeyboardShortcuts",n),l={};if("mark"===t.type&&er(t,"exitable",n)&&(l.ArrowRight=()=>eX.handleExit({editor:e,mark:t})),o){let t=Object.fromEntries(Object.entries(o()).map(([t,n])=>[t,()=>n({editor:e})]));l={...l,...t}}let d=(0,s.w)(l);r.push(d);let h=er(t,"addInputRules",n);if(eF(t,e.options.enableInputRules)&&h){let t=h();if(t&&t.length){let n=function(e){let{editor:t,rules:n}=e,r=new i.k_({state:{init:()=>null,apply(e,i,o){let s=e.getMeta(r);if(s)return s;let l=e.getMeta("applyInputRules");return l&&setTimeout(()=>{let{text:e}=l;"string"==typeof e||(e=eo(a.FK.from(e),o.schema));let{from:i}=l,s=i+e.length;eq({editor:t,from:i,to:s,text:e,rules:n,plugin:r})}),e.selectionSet||e.docChanged?null:i}},props:{handleTextInput:(e,i,o,s)=>eq({editor:t,from:i,to:o,text:s,rules:n,plugin:r}),handleDOMEvents:{compositionend:e=>(setTimeout(()=>{let{$cursor:i}=e.state.selection;i&&eq({editor:t,from:i.pos,to:i.pos,text:"",rules:n,plugin:r})}),!1)},handleKeyDown(e,i){if("Enter"!==i.key)return!1;let{$cursor:o}=e.state.selection;return!!o&&eq({editor:t,from:o.pos,to:o.pos,text:"\n",rules:n,plugin:r})}},isInputRules:!0});return r}({editor:e,rules:t}),o=Array.isArray(n)?n:[n];r.push(...o)}}let c=er(t,"addPasteRules",n);if(eF(t,e.options.enablePasteRules)&&c){let t=c();if(t&&t.length){let n=function(e){let t,{editor:n,rules:r}=e,o=null,s=!1,l=!1,d="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}let h=({state:e,from:r,to:i,rule:o,pasteEvt:s})=>{let l=e.tr;if(function(e){let{editor:t,state:n,from:r,to:i,rule:o,pasteEvent:s,dropEvent:l}=e,{commands:a,chain:d,can:h}=new U({editor:t,state:n}),c=[];return n.doc.nodesBetween(r,i,(e,t)=>{var p,u,f,m,g;if((null==(u=null==(p=e.type)?void 0:p.spec)?void 0:u.code)||!(e.isText||e.isTextblock||e.isInline))return;let y=null!=(g=null!=(m=null==(f=e.content)?void 0:f.size)?m:e.nodeSize)?g:0,b=Math.max(r,t),v=Math.min(i,t+y);b>=v||eQ(e.isText?e.text||"":e.textBetween(b-t,v-t,void 0,"￼"),o.find,s).forEach(e=>{if(void 0===e.index)return;let t=b+e.index+1,r=t+e[0].length,i={from:n.tr.mapping.map(t),to:n.tr.mapping.map(r)},p=o.handler({state:n,range:i,match:e,commands:a,chain:d,can:h,pasteEvent:s,dropEvent:l});c.push(p)})}),c.every(e=>null!==e)}({editor:n,state:_({state:e,transaction:l}),from:Math.max(r-1,0),to:i.b-1,rule:o,pasteEvent:s,dropEvent:t})&&l.steps.length){try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}return d="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,l}};return r.map(e=>new i.k_({view(e){let t=t=>{var r;(o=(null==(r=e.dom.parentElement)?void 0:r.contains(t.target))?e.dom.parentElement:null)&&(e0=n)},r=()=>{e0&&(e0=null)};return window.addEventListener("dragstart",t),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",t),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(e,n)=>{if(l=o===e.dom.parentElement,t=n,!l){let e=e0;(null==e?void 0:e.isEditable)&&setTimeout(()=>{let t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})},10)}return!1},paste:(e,t)=>{var n;let r=null==(n=t.clipboardData)?void 0:n.getData("text/html");return d=t,s=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,r)=>{let i=t[0],o="paste"===i.getMeta("uiEvent")&&!s,c="drop"===i.getMeta("uiEvent")&&!l,p=i.getMeta("applyPasteRules"),u=!!p;if(!o&&!c&&!u)return;if(u){let{text:t}=p;"string"==typeof t||(t=eo(a.FK.from(t),r.schema));let{from:n}=p,i=n+t.length;return h({rule:e,state:r,from:n,to:{b:i},pasteEvt:e1(t)})}let f=n.doc.content.findDiffStart(r.doc.content),m=n.doc.content.findDiffEnd(r.doc.content);if("number"==typeof f&&m&&f!==m.b)return h({rule:e,state:r,from:f,to:m,pasteEvt:d})}}))}({editor:e,rules:t});r.push(...n)}}let p=er(t,"addProseMirrorPlugins",n);if(p){let e=p();r.push(...e)}return r}).flat()}get attributes(){return ed(this.extensions)}get nodeViews(){let{editor:e}=this,{nodeExtensions:t}=ea(this.extensions);return Object.fromEntries(t.filter(e=>!!er(e,"addNodeView")).map(t=>{let n=this.attributes.filter(e=>e.type===t.name),r={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:e,type:ek(t.name,this.schema)},i=er(t,"addNodeView",r);return i?[t.name,(r,o,s,l,a)=>{let d=ec(r,n);return i()({node:r,view:o,getPos:s,decorations:l,innerDecorations:a,editor:e,extension:t,HTMLAttributes:d})}]:[]}))}get markViews(){let{editor:e}=this,{markExtensions:t}=ea(this.extensions);return Object.fromEntries(t.filter(e=>!!er(e,"addMarkView")).map(t=>{let n=this.attributes.filter(e=>e.type===t.name),r={name:t.name,options:t.options,storage:this.editor.extensionStorage[t.name],editor:e,type:ev(t.name,this.schema)},i=er(t,"addMarkView",r);return i?[t.name,(r,o,s)=>{let l=ec(r,n);return i()({mark:r,view:o,inline:s,editor:e,extension:t,HTMLAttributes:l,updateAttributes:t=>{nf(r,e,t)}})}]:[]}))}setupExtensions(){let e=this.extensions;this.editor.extensionStorage=Object.fromEntries(e.map(e=>[e.name,e.storage])),e.forEach(e=>{var t;let n={name:e.name,options:e.options,storage:this.editor.extensionStorage[e.name],editor:this.editor,type:eR(e.name,this.schema)};"mark"===e.type&&(null==(t=el(er(e,"keepOnSplit",n)))||t)&&this.splittableMarks.push(e.name);let r=er(e,"onBeforeCreate",n),i=er(e,"onCreate",n),o=er(e,"onUpdate",n),s=er(e,"onSelectionUpdate",n),l=er(e,"onTransaction",n),a=er(e,"onFocus",n),d=er(e,"onBlur",n),h=er(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),i&&this.editor.on("create",i),o&&this.editor.on("update",o),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),d&&this.editor.on("blur",d),h&&this.editor.on("destroy",h)})}};e2.resolve=eg,e2.sort=em,e2.flatten=ei,W({},{ClipboardTextSerializer:()=>e4,Commands:()=>t9,Delete:()=>t8,Drop:()=>ne,Editable:()=>nt,FocusEvents:()=>nr,Keymap:()=>ni,Paste:()=>no,Tabindex:()=>ns,focusEventsPluginKey:()=>nn});var e3=class e extends eY{constructor(){super(...arguments),this.type="extension"}static create(t={}){return new e("function"==typeof t?t():t)}configure(e){return super.configure(e)}extend(e){let t="function"==typeof e?e():e;return super.extend(t)}},e4=e3.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new i.k_({key:new i.hs("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:e}=this,{state:t,schema:n}=e,{doc:r,selection:i}=t,{ranges:o}=i,s=Math.min(...o.map(e=>e.$from.pos)),l=Math.max(...o.map(e=>e.$to.pos)),a=eb(n);return ey(r,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),e5={};W(e5,{blur:()=>e6,clearContent:()=>e7,clearNodes:()=>e9,command:()=>e8,createParagraphNear:()=>te,cut:()=>tt,deleteCurrentNode:()=>tn,deleteNode:()=>tr,deleteRange:()=>ti,deleteSelection:()=>to,enter:()=>ts,exitCode:()=>tl,extendMarkRange:()=>ta,first:()=>td,focus:()=>tp,forEach:()=>tu,insertContent:()=>tf,insertContentAt:()=>tg,joinBackward:()=>tv,joinDown:()=>tb,joinForward:()=>tw,joinItemBackward:()=>tk,joinItemForward:()=>tx,joinTextblockBackward:()=>tS,joinTextblockForward:()=>tM,joinUp:()=>ty,keyboardShortcut:()=>tA,lift:()=>tT,liftEmptyBlock:()=>tO,liftListItem:()=>tE,newlineInCode:()=>tN,resetAttributes:()=>tR,scrollIntoView:()=>tL,selectAll:()=>tI,selectNodeBackward:()=>tP,selectNodeForward:()=>tz,selectParentNode:()=>t$,selectTextblockEnd:()=>tB,selectTextblockStart:()=>tF,setContent:()=>tj,setMark:()=>tV,setMeta:()=>tH,setNode:()=>tK,setNodeSelection:()=>tJ,setTextSelection:()=>tW,sinkListItem:()=>t_,splitBlock:()=>tq,splitListItem:()=>tG,toggleList:()=>tZ,toggleMark:()=>tQ,toggleNode:()=>t0,toggleWrap:()=>t1,undoInputRule:()=>t2,unsetAllMarks:()=>t3,unsetMark:()=>t4,updateAttributes:()=>t5,wrapIn:()=>t6,wrapInList:()=>t7});var e6=()=>({editor:e,view:t})=>(requestAnimationFrame(()=>{var n;e.isDestroyed||(t.dom.blur(),null==(n=null==window?void 0:window.getSelection())||n.removeAllRanges())}),!0),e7=(e=!0)=>({commands:t})=>t.setContent("",{emitUpdate:e}),e9=()=>({state:e,tr:t,dispatch:n})=>{let{selection:r}=t,{ranges:i}=r;return!n||(i.forEach(({$from:n,$to:r})=>{e.doc.nodesBetween(n.pos,r.pos,(e,n)=>{if(e.type.isText)return;let{doc:r,mapping:i}=t,o=r.resolve(i.map(n)),s=r.resolve(i.map(n+e.nodeSize)),a=o.blockRange(s);if(!a)return;let d=(0,l.jP)(a);if(e.type.isTextblock){let{defaultType:e}=o.parent.contentMatchAt(o.index());t.setNodeMarkup(a.start,e)}(d||0===d)&&t.lift(a,d)})}),!0)},e8=e=>t=>e(t),te=()=>({state:e,dispatch:t})=>O(e,t),tt=(e,t)=>({editor:n,tr:r})=>{let{state:o}=n,s=o.doc.slice(e.from,e.to);r.deleteRange(e.from,e.to);let l=r.mapping.map(t);return r.insert(l,s.content),r.setSelection(new i.U3(r.doc.resolve(Math.max(l-1,0)))),!0},tn=()=>({tr:e,dispatch:t})=>{let{selection:n}=e,r=n.$anchor.node();if(r.content.size>0)return!1;let i=e.selection.$anchor;for(let n=i.depth;n>0;n-=1)if(i.node(n).type===r.type){if(t){let t=i.before(n),r=i.after(n);e.delete(t,r).scrollIntoView()}return!0}return!1},tr=e=>({tr:t,state:n,dispatch:r})=>{let i=ek(e,n.schema),o=t.selection.$anchor;for(let e=o.depth;e>0;e-=1)if(o.node(e).type===i){if(r){let n=o.before(e),r=o.after(e);t.delete(n,r).scrollIntoView()}return!0}return!1},ti=e=>({tr:t,dispatch:n})=>{let{from:r,to:i}=e;return n&&t.delete(r,i),!0},to=()=>({state:e,dispatch:t})=>d(e,t),ts=()=>({commands:e})=>e.keyboardShortcut("Enter"),tl=()=>({state:e,dispatch:t})=>T(e,t),ta=(e,t={})=>({tr:n,state:r,dispatch:o})=>{let s=ev(e,r.schema),{doc:l,selection:a}=n,{$from:d,from:h,to:c}=a;if(o){let e=eE(d,s,t);if(e&&e.from<=h&&e.to>=c){let t=i.U3.create(l,e.from,e.to);n.setSelection(t)}}return!0},td=e=>t=>{let n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1};function th(){return"Android"===navigator.platform||/android/i.test(navigator.userAgent)}function tc(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}var tp=(e=null,t={})=>({editor:n,view:r,tr:i,dispatch:o})=>{t={scrollIntoView:!0,...t};let s=()=>{(tc()||th())&&r.dom.focus(),requestAnimationFrame(()=>{!n.isDestroyed&&(r.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())})};if(r.hasFocus()&&null===e||!1===e)return!0;if(o&&null===e&&!eK(n.state.selection))return s(),!0;let l=eW(i.doc,e)||n.state.selection,a=n.state.selection.eq(l);return o&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),s()),!0},tu=(e,t)=>n=>e.every((e,r)=>t(e,{...n,index:r})),tf=(e,t)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),tm=e=>!("type"in e),tg=(e,t,n)=>({tr:r,dispatch:o,editor:s})=>{var d;if(o){let o,h;n={parseOptions:s.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};let{selection:c}=s.state,p=e=>{s.emit("contentError",{editor:s,error:e,disableCollaboration:()=>{"collaboration"in s.storage&&"object"==typeof s.storage.collaboration&&s.storage.collaboration&&(s.storage.collaboration.isDisabled=!0)}})},u={preserveWhitespace:"full",...n.parseOptions};if(!n.errorOnInvalidContent&&!s.options.enableContentCheck&&s.options.emitContentError)try{Z(t,s.schema,{parseOptions:u,errorOnInvalidContent:!0})}catch(e){p(e)}try{o=Z(t,s.schema,{parseOptions:u,errorOnInvalidContent:null!=(d=n.errorOnInvalidContent)?d:s.options.enableContentCheck})}catch(e){return p(e),!1}let{from:f,to:m}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},g=!0,y=!0;if((tm(o)?o:[o]).forEach(e=>{e.check(),g=!!g&&e.isText&&0===e.marks.length,y=!!y&&e.isBlock}),f===m&&y){let{parent:e}=r.doc.resolve(f);!e.isTextblock||e.type.spec.code||e.childCount||(f-=1,m+=1)}if(g){if(Array.isArray(t))h=t.map(e=>e.text||"").join("");else if(t instanceof a.FK){let e="";t.forEach(t=>{t.text&&(e+=t.text)}),h=e}else h="object"==typeof t&&t&&t.text?t.text:t;r.insertText(h,f,m)}else{h=o;let e=0===c.$from.parentOffset,t=c.$from.node().isText||c.$from.node().isTextblock,n=c.$from.node().content.size>0;e&&t&&n&&(f=Math.max(0,f-1)),r.replaceWith(f,m,h)}n.updateSelection&&function(e,t,n){let r=e.steps.length-1;if(r<t)return;let o=e.steps[r];if(!(o instanceof l.Ln||o instanceof l.Wg))return;let s=e.mapping.maps[r],a=0;s.forEach((e,t,n,r)=>{0===a&&(a=r)}),e.setSelection(i.LN.near(e.doc.resolve(a),-1))}(r,r.steps.length-1,0),n.applyInputRules&&r.setMeta("applyInputRules",{from:f,text:h}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:f,text:h})}return!0},ty=()=>({state:e,dispatch:t})=>x(e,t),tb=()=>({state:e,dispatch:t})=>S(e,t),tv=()=>({state:e,dispatch:t})=>c(e,t),tw=()=>({state:e,dispatch:t})=>v(e,t),tk=()=>({state:e,dispatch:t,tr:n})=>{try{let r=(0,l.N0)(e.doc,e.selection.$from.pos,-1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},tx=()=>({state:e,dispatch:t,tr:n})=>{try{let r=(0,l.N0)(e.doc,e.selection.$from.pos,1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},tS=()=>({state:e,dispatch:t})=>p(e,t),tM=()=>({state:e,dispatch:t})=>u(e,t);function tC(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}var tA=e=>({editor:t,view:n,tr:r,dispatch:i})=>{let o=(function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"===s&&(s=" ");for(let e=0;e<o.length-1;e+=1){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))tc()||tC()?i=!0:n=!0;else throw Error(`Unrecognized modifier name: ${s}`)}return t&&(s=`Alt-${s}`),n&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),r&&(s=`Shift-${s}`),s})(e).split(/-(?!$)/),s=o.find(e=>!["Alt","Ctrl","Meta","Shift"].includes(e)),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:o.includes("Alt"),ctrlKey:o.includes("Ctrl"),metaKey:o.includes("Meta"),shiftKey:o.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction(()=>{n.someProp("handleKeyDown",e=>e(n,l))});return null==a||a.steps.forEach(e=>{let t=e.map(r.mapping);t&&i&&r.maybeStep(t)}),!0},tT=(e,t={})=>({state:n,dispatch:r})=>{let i=ek(e,n.schema);return!!ez(n,i,t)&&M(n,r)},tO=()=>({state:e,dispatch:t})=>E(e,t),tE=e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);return!!o&&(!n||(r.node(o.depth-1).type==e?function(e,t,n,r){let i=e.tr,o=r.end,s=r.$to.end(r.depth);o<s&&(i.step(new l.Wg(o-1,s,o,s,new a.Ji(a.FK.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new a.u$(i.doc.resolve(r.$from.pos),i.doc.resolve(s),r.depth));let d=(0,l.jP)(r);if(null==d)return!1;i.lift(r,d);let h=i.doc.resolve(i.mapping.map(o,-1)-1);return(0,l.n9)(i.doc,h.pos)&&h.nodeBefore.type==h.nodeAfter.type&&i.join(h.pos),t(i.scrollIntoView()),!0}(t,n,e,o):function(e,t,n){let r=e.tr,i=n.parent;for(let e=n.end,t=n.endIndex-1,o=n.startIndex;t>o;t--)e-=i.child(t).nodeSize,r.delete(e-1,e+1);let o=r.doc.resolve(n.start),s=o.nodeAfter;if(r.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let d=0==n.startIndex,h=n.endIndex==i.childCount,c=o.node(-1),p=o.index(-1);if(!c.canReplace(p+ +!d,p+1,s.content.append(h?a.FK.empty:a.FK.from(i))))return!1;let u=o.pos,f=u+s.nodeSize;return r.step(new l.Wg(u-!!d,f+ +!!h,u+1,f-1,new a.Ji((d?a.FK.empty:a.FK.from(i.copy(a.FK.empty))).append(h?a.FK.empty:a.FK.from(i.copy(a.FK.empty))),+!d,+!h),+!d)),t(r.scrollIntoView()),!0}(t,n,o)))}})(ek(e,t.schema))(t,n),tN=()=>({state:e,dispatch:t})=>C(e,t);function tD(e,t){let n="string"==typeof t?[t]:t;return Object.keys(e).reduce((t,r)=>(n.includes(r)||(t[r]=e[r]),t),{})}var tR=(e,t)=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=ex("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=ek(e,r.schema)),"mark"===l&&(s=ev(e,r.schema)),i&&n.selection.ranges.forEach(e=>{r.doc.nodesBetween(e.$from.pos,e.$to.pos,(e,r)=>{o&&o===e.type&&n.setNodeMarkup(r,void 0,tD(e.attrs,t)),s&&e.marks.length&&e.marks.forEach(i=>{s===i.type&&n.addMark(r,r+e.nodeSize,s.create(tD(i.attrs,t)))})})}),!0)},tL=()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),tI=()=>({tr:e,dispatch:t})=>{if(t){let t=new i.i5(e.doc);e.setSelection(t)}return!0},tP=()=>({state:e,dispatch:t})=>g(e,t),tz=()=>({state:e,dispatch:t})=>w(e,t),t$=()=>({state:e,dispatch:t})=>N(e,t),tB=()=>({state:e,dispatch:t})=>I(e,t),tF=()=>({state:e,dispatch:t})=>L(e,t),tj=(e,{errorOnInvalidContent:t,emitUpdate:n=!0,parseOptions:r={}}={})=>({editor:i,tr:o,dispatch:s,commands:l})=>{let{doc:a}=o;if("full"!==r.preserveWhitespace){let l=Q(e,i.schema,r,{errorOnInvalidContent:null!=t?t:i.options.enableContentCheck});return s&&o.replaceWith(0,a.content.size,l).setMeta("preventUpdate",!n),!0}return s&&o.setMeta("preventUpdate",!n),l.insertContentAt({from:0,to:a.content.size},e,{parseOptions:r,errorOnInvalidContent:null!=t?t:i.options.enableContentCheck})},tV=(e,t={})=>({tr:n,state:r,dispatch:i})=>{let{selection:o}=n,{empty:s,ranges:l}=o,a=ev(e,r.schema);if(i)if(s){let e=ew(r,a);n.addStoredMark(a.create({...e,...t}))}else l.forEach(e=>{let i=e.$from.pos,o=e.$to.pos;r.doc.nodesBetween(i,o,(e,r)=>{let s=Math.max(r,i),l=Math.min(r+e.nodeSize,o);e.marks.find(e=>e.type===a)?e.marks.forEach(e=>{a===e.type&&n.addMark(s,l,a.create({...e.attrs,...t}))}):n.addMark(s,l,a.create(t))})});return function(e,t,n){var r;let{selection:i}=t,o=null;if(eK(i)&&(o=i.$cursor),o){let t=null!=(r=e.storedMarks)?r:o.marks();return o.parent.type.allowsMarkType(n)&&(!!n.isInSet(t)||!t.some(e=>e.type.excludes(n)))}let{ranges:s}=i;return s.some(({$from:t,$to:r})=>{let i=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,r.pos,(e,t,r)=>{if(i)return!1;if(e.isInline){let t=!r||r.type.allowsMarkType(n),o=!!n.isInSet(e.marks)||!e.marks.some(e=>e.type.excludes(n));i=t&&o}return!i}),i})}(r,n,a)},tH=(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),tK=(e,t={})=>({state:n,dispatch:r,chain:i})=>{let o,s=ek(e,n.schema);return(n.selection.$anchor.sameParent(n.selection.$head)&&(o=n.selection.$anchor.parent.attrs),s.isTextblock)?i().command(({commands:e})=>!!P(s,{...o,...t})(n)||e.clearNodes()).command(({state:e})=>P(s,{...o,...t})(e,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},tJ=e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,r=eJ(e,0,n.content.size),o=i.nh.create(n,r);t.setSelection(o)}return!0},tW=e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,{from:r,to:o}="number"==typeof e?{from:e,to:e}:e,s=i.U3.atStart(n).from,l=i.U3.atEnd(n).to,a=eJ(r,s,l),d=eJ(o,s,l),h=i.U3.create(n,a,d);t.setSelection(h)}return!0},t_=e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);if(!o)return!1;let s=o.startIndex;if(0==s)return!1;let d=o.parent,h=d.child(s-1);if(h.type!=e)return!1;if(n){let r=h.lastChild&&h.lastChild.type==d.type,i=a.FK.from(r?e.create():null),s=new a.Ji(a.FK.from(e.create(null,a.FK.from(d.type.create(null,i)))),r?3:1,0),c=o.start,p=o.end;n(t.tr.step(new l.Wg(c-(r?3:1),p,c,p,s,1,!0)).scrollIntoView())}return!0}})(ek(e,t.schema))(t,n);function tU(e,t){let n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){let r=n.filter(e=>null==t?void 0:t.includes(e.type.name));e.tr.ensureMarks(r)}}var tq=({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:r,editor:o})=>{let{selection:s,doc:a}=t,{$from:d,$to:h}=s,c=eL(o.extensionManager.attributes,d.node().type.name,d.node().attrs);if(s instanceof i.nh&&s.node.isBlock)return!!d.parentOffset&&!!(0,l.zy)(a,d.pos)&&(r&&(e&&tU(n,o.extensionManager.splittableMarks),t.split(d.pos).scrollIntoView()),!0);if(!d.parent.isBlock)return!1;let p=h.parentOffset===h.parent.content.size,u=0===d.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(d.node(-1).contentMatchAt(d.indexAfter(-1))),f=p&&u?[{type:u,attrs:c}]:void 0,m=(0,l.zy)(t.doc,t.mapping.map(d.pos),1,f);if(!f&&!m&&(0,l.zy)(t.doc,t.mapping.map(d.pos),1,u?[{type:u}]:void 0)&&(m=!0,f=u?[{type:u,attrs:c}]:void 0),r){if(m&&(s instanceof i.U3&&t.deleteSelection(),t.split(t.mapping.map(d.pos),1,f),u&&!p&&!d.parentOffset&&d.parent.type!==u)){let e=t.mapping.map(d.before()),n=t.doc.resolve(e);d.node(-1).canReplaceWith(n.index(),n.index()+1,u)&&t.setNodeMarkup(t.mapping.map(d.before()),u)}e&&tU(n,o.extensionManager.splittableMarks),t.scrollIntoView()}return m},tG=(e,t={})=>({tr:n,state:r,dispatch:o,editor:s})=>{var d;let h=ek(e,r.schema),{$from:c,$to:p}=r.selection,u=r.selection.node;if(u&&u.isBlock||c.depth<2||!c.sameParent(p))return!1;let f=c.node(-1);if(f.type!==h)return!1;let m=s.extensionManager.attributes;if(0===c.parent.content.size&&c.node(-1).childCount===c.indexAfter(-1)){if(2===c.depth||c.node(-3).type!==h||c.index(-2)!==c.node(-2).childCount-1)return!1;if(o){let e=a.FK.empty,r=c.index(-1)?1:c.index(-2)?2:3;for(let t=c.depth-r;t>=c.depth-3;t-=1)e=a.FK.from(c.node(t).copy(e));let o=c.indexAfter(-1)<c.node(-2).childCount?1:c.indexAfter(-2)<c.node(-3).childCount?2:3,s={...eL(m,c.node().type.name,c.node().attrs),...t},l=(null==(d=h.contentMatch.defaultType)?void 0:d.createAndFill(s))||void 0;e=e.append(a.FK.from(h.createAndFill(null,l)||void 0));let p=c.before(c.depth-(r-1));n.replace(p,c.after(-o),new a.Ji(e,4-r,0));let u=-1;n.doc.nodesBetween(p,n.doc.content.size,(e,t)=>{if(u>-1)return!1;e.isTextblock&&0===e.content.size&&(u=t+1)}),u>-1&&n.setSelection(i.U3.near(n.doc.resolve(u))),n.scrollIntoView()}return!0}let g=p.pos===c.end()?f.contentMatchAt(0).defaultType:null,y={...eL(m,f.type.name,f.attrs),...t},b={...eL(m,c.node().type.name,c.node().attrs),...t};n.delete(c.pos,p.pos);let v=g?[{type:h,attrs:y},{type:g,attrs:b}]:[{type:h,attrs:y}];if(!(0,l.zy)(n.doc,c.pos,2))return!1;if(o){let{selection:e,storedMarks:t}=r,{splittableMarks:i}=s.extensionManager,l=t||e.$to.parentOffset&&e.$from.marks();if(n.split(c.pos,2,v).scrollIntoView(),!l||!o)return!0;let a=l.filter(e=>i.includes(e.type.name));n.ensureMarks(a)}return!0},tY=(e,t)=>{let n=en(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&(0,l.n9)(e.doc,n.pos))||(e.join(n.pos),!0)},tX=(e,t)=>{let n=en(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&(0,l.n9)(e.doc,r))||(e.join(r),!0)},tZ=(e,t,n,r={})=>({editor:i,tr:o,state:s,dispatch:l,chain:a,commands:d,can:h})=>{let{extensions:c,splittableMarks:p}=i.extensionManager,u=ek(e,s.schema),f=ek(t,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:b}=m,v=y.blockRange(b),w=g||m.$to.parentOffset&&m.$from.marks();if(!v)return!1;let k=en(e=>ej(e.type.name,c))(m);if(v.depth>=1&&k&&v.depth-k.depth<=1){if(k.node.type===u)return d.liftListItem(f);if(ej(k.node.type.name,c)&&u.validContent(k.node.content)&&l)return a().command(()=>(o.setNodeMarkup(k.pos,u),!0)).command(()=>tY(o,u)).command(()=>tX(o,u)).run()}return n&&w&&l?a().command(()=>{let e=h().wrapInList(u,r),t=w.filter(e=>p.includes(e.type.name));return o.ensureMarks(t),!!e||d.clearNodes()}).wrapInList(u,r).command(()=>tY(o,u)).command(()=>tX(o,u)).run():a().command(()=>!!h().wrapInList(u,r)||d.clearNodes()).wrapInList(u,r).command(()=>tY(o,u)).command(()=>tX(o,u)).run()},tQ=(e,t={},n={})=>({state:r,commands:i})=>{let{extendEmptyMarkRange:o=!1}=n,s=ev(e,r.schema);return eP(r,s,t)?i.unsetMark(s,{extendEmptyMarkRange:o}):i.setMark(s,t)},t0=(e,t,n={})=>({state:r,commands:i})=>{let o,s=ek(e,r.schema),l=ek(t,r.schema),a=ez(r,s,n);return(r.selection.$anchor.sameParent(r.selection.$head)&&(o=r.selection.$anchor.parent.attrs),a)?i.setNode(l,o):i.setNode(s,{...o,...n})},t1=(e,t={})=>({state:n,commands:r})=>{let i=ek(e,n.schema);return ez(n,i,t)?r.lift(i):r.wrapIn(i,t)},t2=()=>({state:e,dispatch:t})=>{let n=e.plugins;for(let r=0;r<n.length;r+=1){let i,o=n[r];if(o.spec.isInputRules&&(i=o.getState(e))){if(t){let t=e.tr,n=i.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(i.text){let n=t.doc.resolve(i.from).marks();t.replaceWith(i.from,i.to,e.schema.text(i.text,n))}else t.delete(i.from,i.to)}return!0}}return!1},t3=()=>({tr:e,dispatch:t})=>{let{selection:n}=e,{empty:r,ranges:i}=n;return!!r||(t&&i.forEach(t=>{e.removeMark(t.$from.pos,t.$to.pos)}),!0)},t4=(e,t={})=>({tr:n,state:r,dispatch:i})=>{var o;let{extendEmptyMarkRange:s=!1}=t,{selection:l}=n,a=ev(e,r.schema),{$from:d,empty:h,ranges:c}=l;if(!i)return!0;if(h&&s){let{from:e,to:t}=l,r=null==(o=d.marks().find(e=>e.type===a))?void 0:o.attrs,i=eE(d,a,r);i&&(e=i.from,t=i.to),n.removeMark(e,t,a)}else c.forEach(e=>{n.removeMark(e.$from.pos,e.$to.pos,a)});return n.removeStoredMark(a),!0},t5=(e,t={})=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=ex("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=ek(e,r.schema)),"mark"===l&&(s=ev(e,r.schema)),i&&n.selection.ranges.forEach(e=>{let i,l,a,d,h=e.$from.pos,c=e.$to.pos;n.selection.empty?r.doc.nodesBetween(h,c,(e,t)=>{o&&o===e.type&&(a=Math.max(t,h),d=Math.min(t+e.nodeSize,c),i=t,l=e)}):r.doc.nodesBetween(h,c,(e,r)=>{r<h&&o&&o===e.type&&(a=Math.max(r,h),d=Math.min(r+e.nodeSize,c),i=r,l=e),r>=h&&r<=c&&(o&&o===e.type&&n.setNodeMarkup(r,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach(i=>{if(s===i.type){let o=Math.max(r,h),l=Math.min(r+e.nodeSize,c);n.addMark(o,l,s.create({...i.attrs,...t}))}}))}),l&&(void 0!==i&&n.setNodeMarkup(i,void 0,{...l.attrs,...t}),s&&l.marks.length&&l.marks.forEach(e=>{s===e.type&&n.addMark(a,d,s.create({...e.attrs,...t}))}))}),!0)},t6=(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o),a=s&&(0,l.oM)(s,e,t);return!!a&&(r&&r(n.tr.wrap(s,a).scrollIntoView()),!0)}})(ek(e,n.schema),t)(n,r),t7=(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o);if(!s)return!1;let d=r?n.tr:null;return!!function(e,t,n,r=null){let i=!1,o=t,s=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=s.resolve(t.start-2);o=new a.u$(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new a.u$(t.$from,s.resolve(t.$to.end(t.depth)),t.depth)),i=!0}let d=(0,l.oM)(o,n,r,t);return!!d&&(e&&function(e,t,n,r,i){let o=a.FK.empty;for(let e=n.length-1;e>=0;e--)o=a.FK.from(n[e].type.create(n[e].attrs,o));e.step(new l.Wg(t.start-2*!!r,t.end,t.start,t.end,new a.Ji(o,0,0),n.length,!0));let s=0;for(let e=0;e<n.length;e++)n[e].type==i&&(s=e+1);let d=n.length-s,h=t.start+n.length-2*!!r,c=t.parent;for(let n=t.startIndex,r=t.endIndex,i=!0;n<r;n++,i=!1)!i&&(0,l.zy)(e.doc,h,d)&&(e.split(h,d),h+=2*d),h+=c.child(n).nodeSize}(e,t,d,i,n),!0)}(d,s,e,t)&&(r&&r(d.scrollIntoView()),!0)}})(ek(e,n.schema),t)(n,r),t9=e3.create({name:"commands",addCommands:()=>({...e5})}),t8=e3.create({name:"delete",onUpdate({transaction:e,appendedTransactions:t}){var n,r,i;let o=()=>{var n,r,i,o;if(null!=(o=null==(i=null==(r=null==(n=this.editor.options.coreExtensionOptions)?void 0:n.delete)?void 0:r.filterTransaction)?void 0:i.call(r,e))?o:e.getMeta("y-sync$"))return;let s=G(e.before,[e,...t]);eM(s).forEach(t=>{s.mapping.mapResult(t.oldRange.from).deletedAfter&&s.mapping.mapResult(t.oldRange.to).deletedBefore&&s.before.nodesBetween(t.oldRange.from,t.oldRange.to,(n,r)=>{let i=r+n.nodeSize-2,o=t.oldRange.from<=r&&i<=t.oldRange.to;this.editor.emit("delete",{type:"node",node:n,from:r,to:i,newFrom:s.mapping.map(r),newTo:s.mapping.map(i),deletedRange:t.oldRange,newRange:t.newRange,partial:!o,editor:this.editor,transaction:e,combinedTransform:s})})});let a=s.mapping;s.steps.forEach((t,n)=>{var r,i;if(t instanceof l.Ys){let o=a.slice(n).map(t.from,-1),l=a.slice(n).map(t.to),d=a.invert().map(o,-1),h=a.invert().map(l),c=null==(r=s.doc.nodeAt(o-1))?void 0:r.marks.some(e=>e.eq(t.mark)),p=null==(i=s.doc.nodeAt(l))?void 0:i.marks.some(e=>e.eq(t.mark));this.editor.emit("delete",{type:"mark",mark:t.mark,from:t.from,to:t.to,deletedRange:{from:d,to:h},newRange:{from:o,to:l},partial:!!(p||c),editor:this.editor,transaction:e,combinedTransform:s})}})};null==(i=null==(r=null==(n=this.editor.options.coreExtensionOptions)?void 0:n.delete)?void 0:r.async)||i?setTimeout(o,0):o()}}),ne=e3.create({name:"drop",addProseMirrorPlugins(){return[new i.k_({key:new i.hs("tiptapDrop"),props:{handleDrop:(e,t,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:r})}}})]}}),nt=e3.create({name:"editable",addProseMirrorPlugins(){return[new i.k_({key:new i.hs("editable"),props:{editable:()=>this.editor.options.editable}})]}}),nn=new i.hs("focusEvents"),nr=e3.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:e}=this;return[new i.k_({key:nn,props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;let r=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1},blur:(t,n)=>{e.isFocused=!1;let r=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1}}}})]}}),ni=e3.create({name:"keymap",addKeyboardShortcuts(){let e=()=>this.editor.commands.first(({commands:e})=>[()=>e.undoInputRule(),()=>e.command(({tr:t})=>{let{selection:n,doc:r}=t,{empty:o,$anchor:s}=n,{pos:l,parent:a}=s,d=s.parent.isTextblock&&l>0?t.doc.resolve(l-1):s,h=d.parent.type.spec.isolating,c=s.pos-s.parentOffset,p=h&&1===d.parent.childCount?c===s.pos:i.LN.atStart(r).from===l;return!!o&&!!a.type.isTextblock&&!a.textContent.length&&!!p&&(!p||"paragraph"!==s.parent.type.name)&&e.clearNodes()}),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()]),t=()=>this.editor.commands.first(({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},r={...n},o={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return tc()||tC()?o:r},addProseMirrorPlugins(){return[new i.k_({key:new i.hs("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some(e=>e.getMeta("composition")))return;let r=e.some(e=>e.docChanged)&&!t.doc.eq(n.doc),o=e.some(e=>e.getMeta("preventClearDocument"));if(!r||o)return;let{empty:s,from:l,to:a}=t.selection,d=i.LN.atStart(t.doc).from,h=i.LN.atEnd(t.doc).to;if(s||l!==d||a!==h||!eV(n.doc))return;let c=n.tr,p=_({state:n,transaction:c}),{commands:u}=new U({editor:this.editor,state:p});if(u.clearNodes(),c.steps.length)return c}})]}}),no=e3.create({name:"paste",addProseMirrorPlugins(){return[new i.k_({key:new i.hs("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),ns=e3.create({name:"tabindex",addProseMirrorPlugins(){return[new i.k_({key:new i.hs("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}}),nl=class e{constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!=(e=this.actualDepth)?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can\u2019t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+ +!this.node.isText}get parent(){if(0===this.depth)return null;let t=this.resolvedPos.start(this.resolvedPos.depth-1);return new e(this.resolvedPos.doc.resolve(t),this.editor)}get before(){let t=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.from-3)),new e(t,this.editor)}get after(){let t=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.to+3)),new e(t,this.editor)}get children(){let t=[];return this.node.content.forEach((n,r)=>{let i=n.isBlock&&!n.isTextblock,o=n.isAtom&&!n.isText,s=this.pos+r+ +!o;if(s<0||s>this.resolvedPos.doc.nodeSize-2)return;let l=this.resolvedPos.doc.resolve(s);if(!i&&l.depth<=this.depth)return;let a=new e(l,this.editor,i,i?n:null);i&&(a.actualDepth=this.depth+1),t.push(new e(l,this.editor,i,i?n:null))}),t}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e)if(Object.keys(t).length>0){let e=r.node.attrs,n=Object.keys(t);for(let r=0;r<n.length;r+=1){let i=n[r];if(e[i]!==t[i])break}}else n=r;r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;let i=Object.keys(t);return this.children.forEach(o=>{(!n||!(r.length>0))&&(o.node.type.name===e&&i.every(e=>t[e]===o.node.attrs[e])&&r.push(o),n&&r.length>0||(r=r.concat(o.querySelectorAll(e,t,n))))}),r}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}},na=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}`,nd=class extends q{constructor(e={}){super(),this.css=null,this.editorView=null,this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.instanceId=Math.random().toString(36).slice(2,9),this.options={element:"undefined"!=typeof document?document.createElement("div"):null,content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onMount:()=>null,onUnmount:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null,onDelete:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("mount",this.options.onMount),this.on("unmount",this.options.onUnmount),this.on("contentError",this.options.onContentError),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n)),this.on("paste",({event:e,slice:t})=>this.options.onPaste(e,t)),this.on("delete",this.options.onDelete);let t=this.createDoc(),n=eW(t,this.options.autofocus);this.editorState=i.$t.create({doc:t,schema:this.schema,selection:n||void 0}),this.options.element&&this.mount(this.options.element)}mount(e){if("undefined"==typeof document)throw Error("[tiptap error]: The editor cannot be mounted because there is no 'document' defined in this environment.");this.createView(e),this.emit("mount",{editor:this}),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}unmount(){if(this.editorView){let e=this.editorView.dom;(null==e?void 0:e.editor)&&delete e.editor,this.editorView.destroy()}if(this.editorView=null,this.isInitialized=!1,this.css)try{"function"==typeof this.css.remove?this.css.remove():this.css.parentNode&&this.css.parentNode.removeChild(this.css)}catch(e){console.warn("Failed to remove CSS element:",e)}this.css=null,this.emit("unmount",{editor:this})}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&"undefined"!=typeof document&&(this.css=function(e,t,n){let r=document.querySelector("style[data-tiptap-style]");if(null!==r)return r;let i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(na,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.editorView&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr,appendedTransactions:[]})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get view(){return this.editorView?this.editorView:new Proxy({state:this.editorState,updateState:e=>{this.editorState=e},dispatch:e=>{this.editorState=this.state.apply(e)},composing:!1,dragging:null,editable:!0,isDestroyed:!1},{get:(e,t)=>{if("state"===t)return this.editorState;if(t in e)return Reflect.get(e,t);throw Error(`[tiptap error]: The editor view is not available. Cannot access view['${t}']. The editor may not be mounted yet.`)}})}get state(){return this.editorView&&(this.editorState=this.view.state),this.editorState}registerPlugin(e,t){let n=es(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(e){if(this.isDestroyed)return;let t=this.state.plugins,n=t;if([].concat(e).forEach(e=>{let t="string"==typeof e?`${e}$`:e.key;n=n.filter(e=>!e.key.startsWith(t))}),t.length===n.length)return;let r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var e,t;let n=[...this.options.enableCoreExtensions?[nt,e4.configure({blockSeparator:null==(t=null==(e=this.options.coreExtensionOptions)?void 0:e.clipboardTextSerializer)?void 0:t.blockSeparator}),t9,nr,ni,ns,ne,no,t8].filter(e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name]):[],...this.options.extensions].filter(e=>["extension","node","mark"].includes(null==e?void 0:e.type));this.extensionManager=new e2(n,this)}createCommandManager(){this.commandManager=new U({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createDoc(){let e;try{e=Q(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(t){if(!(t instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(t.message))throw t;this.emit("contentError",{editor:this,error:t,disableCollaboration:()=>{"collaboration"in this.storage&&"object"==typeof this.storage.collaboration&&this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(e=>"collaboration"!==e.name),this.createExtensionManager()}}),e=Q(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}return e}createView(e){var t;this.editorView=new o.Lz(e,{...this.options.editorProps,attributes:{role:"textbox",...null==(t=this.options.editorProps)?void 0:t.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:this.editorState});let n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews(),this.prependClass(),this.injectCSS(),this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({markViews:this.extensionManager.markViews,nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;let t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(e=>{var t;return null==(t=this.capturedTransaction)?void 0:t.step(e)});return}let{state:t,transactions:n}=this.state.applyTransaction(e),r=!this.state.selection.eq(t.selection),i=n.includes(e),o=this.state;if(this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),!i)return;this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e,appendedTransactions:n.slice(1)}),r&&this.emit("selectionUpdate",{editor:this,transaction:e});let s=n.findLast(e=>e.getMeta("focus")||e.getMeta("blur")),l=null==s?void 0:s.getMeta("focus"),a=null==s?void 0:s.getMeta("blur");l&&this.emit("focus",{editor:this,event:l.event,transaction:s}),a&&this.emit("blur",{editor:this,event:a.event,transaction:s}),e.getMeta("preventUpdate")||!n.some(e=>e.docChanged)||o.doc.eq(t.doc)||this.emit("update",{editor:this,transaction:e,appendedTransactions:n.slice(1)})}getAttributes(e){return eS(this.state,e)}isActive(e,t){let n="string"==typeof e?e:null,r="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return ez(e,null,n)||eP(e,null,n);let r=ex(t,e.schema);return"node"===r?ez(e,t,n):"mark"===r&&eP(e,t,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return eo(this.state.doc.content,this.schema)}getText(e){let{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){let n={from:0,to:e.content.size};return ey(e,n,t)}(this.state.doc,{blockSeparator:t,textSerializers:{...eb(this.schema),...n}})}get isEmpty(){return eV(this.state.doc)}destroy(){this.emit("destroy"),this.unmount(),this.removeAllListeners()}get isDestroyed(){var e,t;return null==(t=null==(e=this.editorView)?void 0:e.isDestroyed)||t}$node(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelectorAll(e,t))||null}$pos(e){return new nl(this.state.doc.resolve(e),this)}get $doc(){return this.$pos(0)}};function nh(e){return new e_({find:e.find,handler:({state:t,range:n,match:r})=>{let i=el(e.getAttributes,void 0,r);if(!1===i||null===i)return null;let{tr:o}=t,s=r[r.length-1],l=r[0];if(s){let r=l.search(/\S/),a=n.from+l.indexOf(s),d=a+s.length;if(eN(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>a).length)return null;d<n.to&&o.delete(d,n.to),a>n.from&&o.delete(n.from+r,a);let h=n.from+r+s.length;o.addMark(n.from+r,h,e.type.create(i||{})),o.removeStoredMark(e.type)}}})}function nc(e){return new e_({find:e.find,handler:({state:t,range:n,match:r})=>{let i=el(e.getAttributes,void 0,r)||{},{tr:o}=t,s=n.from,l=n.to,a=e.type.create(i);if(r[1]){let e=s+r[0].lastIndexOf(r[1]);e>l?e=l:l=e+r[1].length;let t=r[0][r[0].length-1];o.insertText(t,s+r[0].length-1),o.replaceWith(e,l,a)}else if(r[0]){let t=e.type.isInline?s:s-1;o.insert(t,e.type.create(i)).delete(o.mapping.map(s),o.mapping.map(l))}o.scrollIntoView()}})}function np(e){return new e_({find:e.find,handler:({state:t,range:n,match:r})=>{let i=t.doc.resolve(n.from),o=el(e.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,o)}})}function nu(e){return new e_({find:e.find,handler:({state:t,range:n,match:r,chain:i})=>{let o=el(e.getAttributes,void 0,r)||{},s=t.tr.delete(n.from,n.to),a=s.doc.resolve(n.from).blockRange(),d=a&&(0,l.oM)(a,e.type,o);if(!d)return null;if(s.wrap(a,d),e.keepMarks&&e.editor){let{selection:n,storedMarks:r}=t,{splittableMarks:i}=e.editor.extensionManager,o=r||n.$to.parentOffset&&n.$from.marks();if(o){let e=o.filter(e=>i.includes(e.type.name));s.ensureMarks(e)}}if(e.keepAttributes){let t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";i().updateAttributes(t,o).run()}let h=s.doc.resolve(n.from-1).nodeBefore;h&&h.type===e.type&&(0,l.n9)(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(r,h))&&s.join(n.from-1)}})}function nf(e,t,n={}){let{state:r}=t,{doc:i,tr:o}=r;i.descendants((t,r)=>{let i=o.mapping.map(r),s=o.mapping.map(r)+t.nodeSize,l=null;if(t.marks.forEach(t=>{if(t!==e)return!1;l=t}),!l)return;let a=!1;if(Object.keys(n).forEach(e=>{n[e]!==l.attrs[e]&&(a=!0)}),a){let t=e.type.create({...e.attrs,...n});o.removeMark(i,s,e.type),o.addMark(i,s,t)}}),o.docChanged&&t.view.dispatch(o)}var nm=class{constructor(e,t,n){this.component=e,this.editor=t.editor,this.options={...n},this.mark=t.mark,this.HTMLAttributes=t.HTMLAttributes}get dom(){return this.editor.view.dom}get contentDOM(){return null}updateAttributes(e,t){nf(t||this.mark,this.editor,e)}ignoreMutation(e){return!this.dom||!this.contentDOM||("function"==typeof this.options.ignoreMutation?this.options.ignoreMutation({mutation:e}):!("selection"===e.type||this.dom.contains(e.target)&&"childList"===e.type&&(tc()||th())&&this.editor.isFocused&&[...Array.from(e.addedNodes),...Array.from(e.removedNodes)].every(e=>e.isContentEditable))&&(this.contentDOM===e.target&&"attributes"===e.type||!this.contentDOM.contains(e.target)))}},ng=class e extends eY{constructor(){super(...arguments),this.type="node"}static create(t={}){return new e("function"==typeof t?t():t)}configure(e){return super.configure(e)}extend(e){let t="function"==typeof e?e():e;return super.extend(t)}},ny=class{constructor(e,t,n){this.isDragging=!1,this.component=e,this.editor=t.editor,this.options={stopEvent:null,ignoreMutation:null,...n},this.extension=t.extension,this.node=t.node,this.decorations=t.decorations,this.innerDecorations=t.innerDecorations,this.view=t.view,this.HTMLAttributes=t.HTMLAttributes,this.getPos=t.getPos,this.mount()}mount(){}get dom(){return this.editor.view.dom}get contentDOM(){return null}onDragStart(e){var t,n,r,o,s,l,a;let{view:d}=this.editor,h=e.target,c=3===h.nodeType?null==(t=h.parentElement)?void 0:t.closest("[data-drag-handle]"):h.closest("[data-drag-handle]");if(!this.dom||(null==(n=this.contentDOM)?void 0:n.contains(h))||!c)return;let p=0,u=0;if(this.dom!==c){let t=this.dom.getBoundingClientRect(),n=c.getBoundingClientRect(),i=null!=(o=e.offsetX)?o:null==(r=e.nativeEvent)?void 0:r.offsetX,a=null!=(l=e.offsetY)?l:null==(s=e.nativeEvent)?void 0:s.offsetY;p=n.x-t.x+i,u=n.y-t.y+a}let f=this.dom.cloneNode(!0);try{let e=this.dom.getBoundingClientRect();f.style.width=`${Math.round(e.width)}px`,f.style.height=`${Math.round(e.height)}px`,f.style.boxSizing="border-box",f.style.pointerEvents="none"}catch{}let m=null;try{(m=document.createElement("div")).style.position="absolute",m.style.top="-9999px",m.style.left="-9999px",m.style.pointerEvents="none",m.appendChild(f),document.body.appendChild(m),null==(a=e.dataTransfer)||a.setDragImage(f,p,u)}finally{m&&setTimeout(()=>{try{null==m||m.remove()}catch{}},0)}let g=this.getPos();if("number"!=typeof g)return;let y=i.nh.create(d.state.doc,g),b=d.state.tr.setSelection(y);d.dispatch(b)}stopEvent(e){var t;if(!this.dom)return!1;if("function"==typeof this.options.stopEvent)return this.options.stopEvent({event:e});let n=e.target;if(!(this.dom.contains(n)&&!(null==(t=this.contentDOM)?void 0:t.contains(n))))return!1;let r=e.type.startsWith("drag"),o="drop"===e.type;if((["INPUT","BUTTON","SELECT","TEXTAREA"].includes(n.tagName)||n.isContentEditable)&&!o&&!r)return!0;let{isEditable:s}=this.editor,{isDragging:l}=this,a=!!this.node.type.spec.draggable,d=i.nh.isSelectable(this.node),h="copy"===e.type,c="paste"===e.type,p="cut"===e.type,u="mousedown"===e.type;if(!a&&d&&r&&e.target===this.dom&&e.preventDefault(),a&&r&&!l&&e.target===this.dom)return e.preventDefault(),!1;if(a&&s&&!l&&u){let e=n.closest("[data-drag-handle]");e&&(this.dom===e||this.dom.contains(e))&&(this.isDragging=!0,document.addEventListener("dragend",()=>{this.isDragging=!1},{once:!0}),document.addEventListener("drop",()=>{this.isDragging=!1},{once:!0}),document.addEventListener("mouseup",()=>{this.isDragging=!1},{once:!0}))}return!l&&!o&&!h&&!c&&!p&&(!u||!d)}ignoreMutation(e){return!this.dom||!this.contentDOM||("function"==typeof this.options.ignoreMutation?this.options.ignoreMutation({mutation:e}):!!this.node.isLeaf||!!this.node.isAtom||!("selection"===e.type||this.dom.contains(e.target)&&"childList"===e.type&&(tc()||th())&&this.editor.isFocused&&[...Array.from(e.addedNodes),...Array.from(e.removedNodes)].every(e=>e.isContentEditable))&&(this.contentDOM===e.target&&"attributes"===e.type||!this.contentDOM.contains(e.target)))}updateAttributes(e){this.editor.commands.command(({tr:t})=>{let n=this.getPos();return"number"==typeof n&&(t.setNodeMarkup(n,void 0,{...this.node.attrs,...e}),!0)})}deleteNode(){let e=this.getPos();if("number"!=typeof e)return;let t=e+this.node.nodeSize;this.editor.commands.deleteRange({from:e,to:t})}};function nb(e){return new eZ({find:e.find,handler:({state:t,range:n,match:r,pasteEvent:i})=>{let o=el(e.getAttributes,void 0,r,i);if(!1===o||null===o)return null;let{tr:s}=t,l=r[r.length-1],a=r[0],d=n.to;if(l){let r=a.search(/\S/),i=n.from+a.indexOf(l),h=i+l.length;if(eN(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>i).length)return null;h<n.to&&s.delete(h,n.to),i>n.from&&s.delete(n.from+r,i),d=n.from+r+l.length,s.addMark(n.from+r,d,e.type.create(o||{})),s.removeStoredMark(e.type)}}})}},54264:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},55863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},56249:e=>{e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;for(i of t.entries())if(!e(i[1],n.get(i[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(t[i]!==n[i])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,s=o[i];if(("_owner"!==s||!t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}},57850:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},63208:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},65090:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("HardHat",[["path",{d:"M10 10V5a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v5",key:"1p9q5i"}],["path",{d:"M14 6a6 6 0 0 1 6 6v3",key:"1hnv84"}],["path",{d:"M4 15v-3a6 6 0 0 1 6-6",key:"9ciidu"}],["rect",{x:"2",y:"15",width:"20",height:"4",rx:"1",key:"g3x8cw"}]])},66030:(e,t,n)=>{n.d(t,{K:()=>f,w:()=>u});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},i={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),s="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),l=0;l<10;l++)r[48+l]=r[96+l]=String(l);for(var l=1;l<=24;l++)r[l+111]="F"+l;for(var l=65;l<=90;l++)r[l]=String.fromCharCode(l+32),i[l]=String.fromCharCode(l);for(var a in r)i.hasOwnProperty(a)||(i[a]=r[a]);var d=n(42404);let h="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),c="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function p(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function u(e){return new d.k_({props:{handleKeyDown:f(e)}})}function f(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let e=0;e<o.length-1;e++){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))h?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+s)}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),i&&(s="Meta-"+s),r&&(s="Shift-"+s),s}(n)]=e[n];return t}(e);return function(e,n){var l;let a=("Esc"==(l=!(o&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||s&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?i:r)[n.keyCode]||n.key||"Unidentified")&&(l="Escape"),"Del"==l&&(l="Delete"),"Left"==l&&(l="ArrowLeft"),"Up"==l&&(l="ArrowUp"),"Right"==l&&(l="ArrowRight"),"Down"==l&&(l="ArrowDown"),l),d,h=t[p(a,n)];if(h&&h(e.state,e.dispatch,e))return!0;if(1==a.length&&" "!=a){if(n.shiftKey){let r=t[p(a,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(c&&n.ctrlKey&&n.altKey)&&(d=r[n.keyCode])&&d!=a){let r=t[p(d,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}},66207:(e,t,n)=>{n.d(t,{b:()=>l});var r=n(93491),i=n(90604),o=n(91754),s=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=s},68708:(e,t,n)=>{var r=n(93491),i=n(39230),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=i.useSyncExternalStore,l=r.useRef,a=r.useEffect,d=r.useMemo,h=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var c=l(null);if(null===c.current){var p={hasValue:!1,value:null};c.current=p}else p=c.current;var u=s(e,(c=d(function(){function e(e){if(!a){if(a=!0,s=e,e=r(e),void 0!==i&&p.hasValue){var t=p.value;if(i(t,e))return l=t}return l=e}if(t=l,o(s,e))return t;var n=r(e);return void 0!==i&&i(t,n)?(s=e,t):(s=e,l=n)}var s,l,a=!1,d=void 0===n?null:n;return[function(){return e(t())},null===d?void 0:function(){return e(d())}]},[t,n,r,i]))[0],c[1]);return a(function(){p.hasValue=!0,p.value=u},[u]),h(u),u}},68750:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},71473:(e,t,n)=>{n.d(t,{h:()=>r});var r=n(98998).A0},73562:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},82158:(e,t,n)=>{n.d(t,{$Z:()=>f,hG:()=>w});var r=n(93491),i=n(52410),o=n(39230),s=n(91754),l=n(53744),a=n(56249),d=n(29428),h=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},c=({contentComponent:e})=>{let t=(0,o.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getServerSnapshot);return(0,s.jsx)(s.Fragment,{children:Object.values(t)})},p=class extends r.Component{constructor(e){var t;super(e),this.editorContentRef=r.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(t=e.editor)?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:i.createPortal(r.reactElement,r.element,n)},e.forEach(e=>e())},removeRenderer(n){let r={...t};delete r[n],t=r,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){var e;let t=this.props.editor;if(!t||(this.initialized=!1,t.isDestroyed||t.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),t.contentComponent=null,!(null==(e=t.options.element)?void 0:e.firstChild)))return;let n=document.createElement("div");n.append(...t.options.element.childNodes),t.setOptions({element:n})}render(){let{editor:e,innerRef:t,...n}=this.props;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{ref:h(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&(0,s.jsx)(c,{contentComponent:e.contentComponent})]})}},u=(0,r.forwardRef)((e,t)=>{let n=r.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[e.editor]);return r.createElement(p,{key:n,innerRef:t,...e})}),f=r.memo(u),m="undefined"!=typeof window?r.useLayoutEffect:r.useEffect,g=class{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}},y="undefined"==typeof window,b=y||!!("undefined"!=typeof window&&window.next),v=class e{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){if(void 0===this.options.current.immediatelyRender)return y||b?null:this.createEditor();return this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null}createEditor(){let e={...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBeforeCreate)?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBlur)?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onCreate)?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDestroy)?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null==(n=(t=this.options.current).onFocus)?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onSelectionUpdate)?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null==(n=(t=this.options.current).onTransaction)?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onUpdate)?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null==(n=(t=this.options.current).onContentError)?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDrop)?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null==(n=(t=this.options.current).onPaste)?void 0:n.call(t,...e)},onDelete:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDelete)?void 0:n.call(t,...e)}};return new l.KE(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var r;return e===(null==(r=t.extensions)?void 0:r[n])}):e[n]===t[n]))}onRender(t){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===t.length?e.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(t),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}};function w(e={},t=[]){let n=(0,r.useRef)(e);n.current=e;let[i]=(0,r.useState)(()=>new v(n)),s=(0,o.useSyncExternalStore)(i.subscribe,i.getEditor,i.getServerSnapshot);return(0,r.useDebugValue)(s),(0,r.useEffect)(i.onRender(t)),!function(e){var t;let[n]=(0,r.useState)(()=>new g(e.editor)),i=(0,d.useSyncExternalStoreWithSelector)(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!=(t=e.equalityFn)?t:a);m(()=>n.watch(e.editor),[e.editor,n]),(0,r.useDebugValue)(i)}({editor:s,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction||void 0===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),s}var k=((0,r.createContext)({editor:null}).Consumer,(0,r.createContext)({onDragStart:()=>{},nodeViewContentChildren:void 0,nodeViewContentRef:()=>{}})),x=()=>(0,r.useContext)(k);function S(e){return!!("function"==typeof e&&e.prototype&&e.prototype.isReactComponent)}function M(e){return!!("object"==typeof e&&e.$$typeof&&("Symbol(react.forward_ref)"===e.$$typeof.toString()||"react.forward_ref"===e.$$typeof.description))}r.forwardRef((e,t)=>{let{onDragStart:n}=x(),r=e.as||"div";return(0,s.jsx)(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})});var C=class{constructor(e,{editor:t,props:n={},as:r="div",className:o=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=e,this.editor=t,this.props=n,this.element=document.createElement(r),this.element.classList.add("react-renderer"),o&&this.element.classList.add(...o.split(" ")),this.editor.isInitialized?(0,i.flushSync)(()=>{this.render()}):queueMicrotask(()=>{this.render()})}render(){var e;let t=this.component,n=this.props,i=this.editor,o=function(){try{if(r.version)return parseInt(r.version.split(".")[0],10)>=19}catch{}return!1}(),l=function(e){if(S(e)||M(e))return!0;if("object"==typeof e&&e.$$typeof&&("Symbol(react.memo)"===e.$$typeof.toString()||"react.memo"===e.$$typeof.description)){let t=e.type;if(t)return S(t)||M(t)}return!1}(t),a={...n};a.ref&&!(o||l)&&delete a.ref,!a.ref&&(o||l)&&(a.ref=e=>{this.ref=e}),this.reactElement=(0,s.jsx)(t,{...a}),null==(e=null==i?void 0:i.contentComponent)||e.setRenderer(this.id,this)}updateProps(e={}){this.props={...this.props,...e},this.render()}destroy(){var e;let t=this.editor;null==(e=null==t?void 0:t.contentComponent)||e.removeRenderer(this.id);try{this.element&&this.element.parentNode&&this.element.parentNode.removeChild(this.element)}catch{}}updateAttributes(e){Object.keys(e).forEach(t=>{this.element.setAttribute(t,e[t])})}},A=r.createContext({markViewContentRef:()=>{}});l.Db,l.Yv},82546:(e,t,n)=>{n.d(t,{UC:()=>P,VY:()=>F,ZD:()=>$,ZL:()=>L,bL:()=>D,hE:()=>B,hJ:()=>I,l9:()=>R,rc:()=>z});var r=n(93491),i=n(10158),o=n(42014),s=n(18227),l=n(18682),a=n(16435),d=n(91754),h="AlertDialog",[c,p]=(0,i.A)(h,[s.Hs]),u=(0,s.Hs)(),f=e=>{let{__scopeAlertDialog:t,...n}=e,r=u(t);return(0,d.jsx)(s.bL,{...r,...n,modal:!0})};f.displayName=h;var m=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=u(n);return(0,d.jsx)(s.l9,{...i,...r,ref:t})});m.displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...n}=e,r=u(t);return(0,d.jsx)(s.ZL,{...r,...n})};g.displayName="AlertDialogPortal";var y=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=u(n);return(0,d.jsx)(s.hJ,{...i,...r,ref:t})});y.displayName="AlertDialogOverlay";var b="AlertDialogContent",[v,w]=c(b),k=(0,a.Dc)("AlertDialogContent"),x=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:i,...a}=e,h=u(n),c=r.useRef(null),p=(0,o.s)(t,c),f=r.useRef(null);return(0,d.jsx)(s.G$,{contentName:b,titleName:S,docsSlug:"alert-dialog",children:(0,d.jsx)(v,{scope:n,cancelRef:f,children:(0,d.jsxs)(s.UC,{role:"alertdialog",...h,...a,ref:p,onOpenAutoFocus:(0,l.m)(a.onOpenAutoFocus,e=>{e.preventDefault(),f.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(k,{children:i}),(0,d.jsx)(N,{contentRef:c})]})})})});x.displayName=b;var S="AlertDialogTitle",M=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=u(n);return(0,d.jsx)(s.hE,{...i,...r,ref:t})});M.displayName=S;var C="AlertDialogDescription",A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=u(n);return(0,d.jsx)(s.VY,{...i,...r,ref:t})});A.displayName=C;var T=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,i=u(n);return(0,d.jsx)(s.bm,{...i,...r,ref:t})});T.displayName="AlertDialogAction";var O="AlertDialogCancel",E=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:i}=w(O,n),l=u(n),a=(0,o.s)(t,i);return(0,d.jsx)(s.bm,{...l,...r,ref:a})});E.displayName=O;var N=({contentRef:e})=>{let t=`\`${b}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${b}\` by passing a \`${C}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${b}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},D=f,R=m,L=g,I=y,P=x,z=T,$=E,B=M,F=A},85108:(e,t,n)=>{n.d(t,{id:()=>h});let r={lessThanXSeconds:{one:"kurang dari 1 detik",other:"kurang dari {{count}} detik"},xSeconds:{one:"1 detik",other:"{{count}} detik"},halfAMinute:"setengah menit",lessThanXMinutes:{one:"kurang dari 1 menit",other:"kurang dari {{count}} menit"},xMinutes:{one:"1 menit",other:"{{count}} menit"},aboutXHours:{one:"sekitar 1 jam",other:"sekitar {{count}} jam"},xHours:{one:"1 jam",other:"{{count}} jam"},xDays:{one:"1 hari",other:"{{count}} hari"},aboutXWeeks:{one:"sekitar 1 minggu",other:"sekitar {{count}} minggu"},xWeeks:{one:"1 minggu",other:"{{count}} minggu"},aboutXMonths:{one:"sekitar 1 bulan",other:"sekitar {{count}} bulan"},xMonths:{one:"1 bulan",other:"{{count}} bulan"},aboutXYears:{one:"sekitar 1 tahun",other:"sekitar {{count}} tahun"},xYears:{one:"1 tahun",other:"{{count}} tahun"},overXYears:{one:"lebih dari 1 tahun",other:"lebih dari {{count}} tahun"},almostXYears:{one:"hampir 1 tahun",other:"hampir {{count}} tahun"}};var i=n(79508);let o={date:(0,i.k)({formats:{full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"d/M/yyyy"},defaultWidth:"full"}),time:(0,i.k)({formats:{full:"HH.mm.ss",long:"HH.mm.ss",medium:"HH.mm",short:"HH.mm"},defaultWidth:"full"}),dateTime:(0,i.k)({formats:{full:"{{date}} 'pukul' {{time}}",long:"{{date}} 'pukul' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},s={lastWeek:"eeee 'lalu pukul' p",yesterday:"'Kemarin pukul' p",today:"'Hari ini pukul' p",tomorrow:"'Besok pukul' p",nextWeek:"eeee 'pukul' p",other:"P"};var l=n(99158);let a={ordinalNumber:(e,t)=>"ke-"+Number(e),era:(0,l.o)({values:{narrow:["SM","M"],abbreviated:["SM","M"],wide:["Sebelum Masehi","Masehi"]},defaultWidth:"wide"}),quarter:(0,l.o)({values:{narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["Kuartal ke-1","Kuartal ke-2","Kuartal ke-3","Kuartal ke-4"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,l.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","Mei","Jun","Jul","Agt","Sep","Okt","Nov","Des"],wide:["Januari","Februari","Maret","April","Mei","Juni","Juli","Agustus","September","Oktober","November","Desember"]},defaultWidth:"wide"}),day:(0,l.o)({values:{narrow:["M","S","S","R","K","J","S"],short:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],abbreviated:["Min","Sen","Sel","Rab","Kam","Jum","Sab"],wide:["Minggu","Senin","Selasa","Rabu","Kamis","Jumat","Sabtu"]},defaultWidth:"wide"}),dayPeriod:(0,l.o)({values:{narrow:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},wide:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"}},defaultWidth:"wide",formattingValues:{narrow:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},abbreviated:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"},wide:{am:"AM",pm:"PM",midnight:"tengah malam",noon:"tengah hari",morning:"pagi",afternoon:"siang",evening:"sore",night:"malam"}},defaultFormattingWidth:"wide"})};var d=n(3024);let h={code:"id",formatDistance:(e,t,n)=>{let i,o=r[e];if(i="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),n?.addSuffix)if(n.comparison&&n.comparison>0)return"dalam waktu "+i;else return i+" yang lalu";return i},formatLong:o,formatRelative:(e,t,n,r)=>s[e],localize:a,match:{ordinalNumber:(0,n(41200).K)({matchPattern:/^ke-(\d+)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,d.A)({matchPatterns:{narrow:/^(sm|m)/i,abbreviated:/^(s\.?\s?m\.?|s\.?\s?e\.?\s?u\.?|m\.?|e\.?\s?u\.?)/i,wide:/^(sebelum masehi|sebelum era umum|masehi|era umum)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^s/i,/^(m|e)/i]},defaultParseWidth:"any"}),quarter:(0,d.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^K-?\s[1234]/i,wide:/^Kuartal ke-?\s?[1234]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,d.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|mei|jun|jul|agt|sep|okt|nov|des)/i,wide:/^(januari|februari|maret|april|mei|juni|juli|agustus|september|oktober|november|desember)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^ap/i,/^me/i,/^jun/i,/^jul/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,d.A)({matchPatterns:{narrow:/^[srkjm]/i,short:/^(min|sen|sel|rab|kam|jum|sab)/i,abbreviated:/^(min|sen|sel|rab|kam|jum|sab)/i,wide:/^(minggu|senin|selasa|rabu|kamis|jumat|sabtu)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^m/i,/^s/i,/^s/i,/^r/i,/^k/i,/^j/i,/^s/i],any:[/^m/i,/^sen/i,/^sel/i,/^r/i,/^k/i,/^j/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,d.A)({matchPatterns:{narrow:/^(a|p|tengah m|tengah h|(di(\swaktu)?) (pagi|siang|sore|malam))/i,any:/^([ap]\.?\s?m\.?|tengah malam|tengah hari|(di(\swaktu)?) (pagi|siang|sore|malam))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^pm/i,midnight:/^tengah m/i,noon:/^tengah h/i,morning:/pagi/i,afternoon:/siang/i,evening:/sore/i,night:/malam/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}}},87656:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},95747:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},98539:(e,t,n)=>{n.d(t,{$L:()=>E,Ln:()=>m,N0:()=>T,Um:()=>O,Wg:()=>g,X9:()=>s,Ys:()=>p,dL:()=>j,jP:()=>v,n9:()=>C,oM:()=>w,zy:()=>M});var r=n(42842);class i{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class o{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&o.empty)return o.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let d=this.ranges[l+o],h=this.ranges[l+s],c=a+d;if(e<=c){let o=d?e==a?-1:e==c?1:t:t,s=a+r+(o<0?0:h);if(n)return s;let p=e==(t<0?a:c)?null:l/3+(e-a)*65536,u=e==a?2:e==c?1:4;return(t<0?e!=a:e!=c)&&(u|=8),new i(s,u,p)}r+=h-d}return n?e+r:new i(e+r,0,null)}touches(e,t){let n=0,r=65535&t,i=this.inverted?2:1,o=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let s=this.ranges[t]-(this.inverted?n:0);if(s>e)break;let l=this.ranges[t+i];if(e<=s+l&&t==3*r)return!0;n+=this.ranges[t+o]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let o=this.ranges[r],s=o-(this.inverted?i:0),l=o+(this.inverted?0:i),a=this.ranges[r+t],d=this.ranges[r+n];e(s,s+a,l,l+d),i+=d-a}}invert(){return new o(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?o.empty:new o(e<0?[0,-e,0]:[0,0,e])}}o.empty=new o([]);class s{constructor(e,t,n=0,r=e?e.length:0){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new s(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new s;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this._maps[n].mapResult(e,t);if(null!=i.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this._maps[t].recover(i.recover);continue}}r|=i.delInfo,e=i.pos}return n?e:new i(e,r,null)}}let l=Object.create(null);class a{getMap(){return o.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=l[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in l)throw RangeError("Duplicate use of step JSON ID "+e);return l[e]=t,t.prototype.jsonID=e,t}}class d{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new d(e,null)}static fail(e){return new d(null,e)}static fromReplace(e,t,n,i){try{return d.ok(e.replace(t,n,i))}catch(e){if(e instanceof r.vI)return d.fail(e.message);throw e}}}function h(e,t,n){let i=[];for(let r=0;r<e.childCount;r++){let o=e.child(r);o.content.size&&(o=o.copy(h(o.content,t,o))),o.isInline&&(o=t(o,n,r)),i.push(o)}return r.FK.fromArray(i)}class c extends a{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),i=n.node(n.sharedDepth(this.to)),o=new r.Ji(h(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,i),t.openStart,t.openEnd);return d.fromReplace(e,this.from,this.to,o)}invert(){return new p(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new c(t.pos,n.pos,this.mark)}merge(e){return e instanceof c&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new c(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new c(t.from,t.to,e.markFromJSON(t.mark))}}a.jsonID("addMark",c);class p extends a{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new r.Ji(h(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return d.fromReplace(e,this.from,this.to,n)}invert(){return new c(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new p(t.pos,n.pos,this.mark)}merge(e){return e instanceof p&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new p(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new p(t.from,t.to,e.markFromJSON(t.mark))}}a.jsonID("removeMark",p);class u extends a{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return d.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return d.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new u(this.pos,t.marks[n]);return new u(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new u(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new u(t.pos,e.markFromJSON(t.mark))}}a.jsonID("addNodeMark",u);class f extends a{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return d.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return d.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new u(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new f(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(t.pos,e.markFromJSON(t.mark))}}a.jsonID("removeNodeMark",f);class m extends a{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&y(e,this.from,this.to)?d.fail("Structure replace would overwrite content"):d.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new o([this.from,this.to-this.from,this.slice.size])}invert(e){return new m(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new m(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof m)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart)if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;else{let t=this.slice.size+e.slice.size==0?r.Ji.empty:new r.Ji(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new m(e.from,this.to,t,this.structure)}{let t=this.slice.size+e.slice.size==0?r.Ji.empty:new r.Ji(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new m(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(t.from,t.to,r.Ji.fromJSON(e,t.slice),!!t.structure)}}a.jsonID("replace",m);class g extends a{constructor(e,t,n,r,i,o,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=o,this.structure=s}apply(e){if(this.structure&&(y(e,this.from,this.gapFrom)||y(e,this.gapTo,this.to)))return d.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return d.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?d.fromReplace(e,this.from,this.to,n):d.fail("Content does not fit in gap")}getMap(){return new o([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||i>n.pos?null:new g(t.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(t.from,t.to,t.gapFrom,t.gapTo,r.Ji.fromJSON(e,t.slice),t.insert,!!t.structure)}}function y(e,t,n){let r=e.resolve(t),i=n-t,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let e=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,i--}}return!1}function b(e,t,n,i=n.contentMatch,o=!0){let s=e.doc.nodeAt(t),l=[],a=t+1;for(let t=0;t<s.childCount;t++){let d=s.child(t),h=a+d.nodeSize,c=i.matchType(d.type);if(c){i=c;for(let t=0;t<d.marks.length;t++)n.allowsMarkType(d.marks[t].type)||e.step(new p(a,h,d.marks[t]));if(o&&d.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,i;for(;e=t.exec(d.text);)i||(i=new r.Ji(r.FK.from(n.schema.text(" ",n.allowedMarks(d.marks))),0,0)),l.push(new m(a+e.index,a+e.index+e[0].length,i))}}else l.push(new m(a,h,r.Ji.empty));a=h}if(!i.validEnd){let t=i.fillBefore(r.FK.empty,!0);e.replace(a,a,new r.Ji(t,0,0))}for(let t=l.length-1;t>=0;t--)e.step(l[t])}function v(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),i=e.$from.index(n),o=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(i,o,t))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(o==r.childCount||r.canReplace(0,o))))break}return null}function w(e,t,n=null,r=e){let i=function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.contentMatchAt(r).findWrapping(t);if(!o)return null;let s=o.length?o[0]:t;return n.canReplaceWith(r,i,s)?o:null}(e,t),o=i&&function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.child(r),s=t.contentMatch.findWrapping(o.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let e=r;l&&e<i;e++)l=l.matchType(n.child(e).type);return l&&l.validEnd?s:null}(r,t);return o?i.map(k).concat({type:t,attrs:n}).concat(o.map(k)):null}function k(e){return{type:e,attrs:null}}function x(e,t,n,r){t.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let i=e.mapping.slice(r).map(n+1+o+s.index);e.replaceWith(i,i+1,t.type.schema.linebreakReplacement.create())}}})}function S(e,t,n,r){t.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=e.mapping.slice(r).map(n+1+o);e.replaceWith(i,i+1,t.type.schema.text("\n"))}})}function M(e,t,n=1,r){let i=e.resolve(t),o=i.depth-n,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let e=i.depth-1,t=n-2;e>o;e--,t--){let n=i.node(e),o=i.index(e);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(o,n.childCount),l=r&&r[t+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[t]||n;if(!n.canReplace(o+1,n.childCount)||!a.type.validContent(s))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function C(e,t){let n=e.resolve(t),r=n.index();return A(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function A(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let i=0;i<t.childCount;i++){let o=t.child(i),s=o.type==r?e.type.schema.nodes.text:o.type;if(!(n=n.matchType(s))||!e.type.allowsMarks(o.marks))return!1}return n.validEnd}(e,t))}function T(e,t,n=-1){let r=e.resolve(t);for(let e=r.depth;;e--){let i,o,s=r.index(e);if(e==r.depth?(i=r.nodeBefore,o=r.nodeAfter):n>0?(i=r.node(e+1),s++,o=r.node(e).maybeChild(s)):(i=r.node(e).maybeChild(s-1),o=r.node(e+1)),i&&!i.isTextblock&&A(i,o)&&r.node(e).canReplace(s,s+1))return t;if(0==e)break;t=n<0?r.before(e):r.after(e)}}function O(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let i=n.content;for(let e=0;e<n.openStart;e++)i=i.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=r.depth;t>=0;t--){let n=t==r.depth?0:r.pos<=(r.start(t+1)+r.end(t+1))/2?-1:1,o=r.index(t)+ +(n>0),s=r.node(t),l=!1;if(1==e)l=s.canReplace(o,o,i);else{let e=s.contentMatchAt(o).findWrapping(i.firstChild.type);l=e&&s.canReplaceWith(o,o,e[0])}if(l)return 0==n?r.pos:n<0?r.before(t+1):r.after(t+1)}return null}function E(e,t,n=t,i=r.Ji.empty){if(t==n&&!i.size)return null;let o=e.resolve(t),s=e.resolve(n);return N(o,s,i)?new m(t,n,i):new D(o,s,i).fit()}function N(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}a.jsonID("replaceAround",g);class D{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=r.FK.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=r.FK.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(e<0?this.$to:n.doc.resolve(e));if(!i)return null;let o=this.placed,s=n.depth,l=i.depth;for(;s&&l&&1==o.childCount;)o=o.firstChild.content,s--,l--;let a=new r.Ji(o,s,l);return e>-1?new g(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new m(n.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,i=null,o=(n?(i=I(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let e=this.depth;e>=0;e--){let{type:s,match:l}=this.frontier[e],a,d=null;if(1==t&&(o?l.matchType(o.type)||(d=l.fillBefore(r.FK.from(o),!1)):i&&s.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:e,parent:i,inject:d};if(2==t&&o&&(a=l.findWrapping(o.type)))return{sliceDepth:n,frontierDepth:e,parent:i,wrap:a};if(i&&l.matchType(i.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=I(e,t);return!!i.childCount&&!i.firstChild.isLeaf&&(this.unplaced=new r.Ji(e,t+1,Math.max(n,i.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=I(e,t);if(i.childCount<=1&&t>0){let o=e.size-t<=t+i.size;this.unplaced=new r.Ji(R(e,t-1,1),t-1,o?t-1:n)}else this.unplaced=new r.Ji(R(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:i,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let e=0;e<o.length;e++)this.openFrontierNode(o[e]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-e,d=0,h=[],{match:c,type:p}=this.frontier[t];if(i){for(let e=0;e<i.childCount;e++)h.push(i.child(e));c=c.matchFragment(i)}let u=l.size+e-(s.content.size-s.openEnd);for(;d<l.childCount;){let e=l.child(d),t=c.matchType(e.type);if(!t)break;(++d>1||0==a||e.content.size)&&(c=t,h.push(function e(t,n,i){if(n<=0)return t;let o=t.content;return n>1&&(o=o.replaceChild(0,e(o.firstChild,n-1,1==o.childCount?i-1:0))),n>0&&(o=t.type.contentMatch.fillBefore(o).append(o),i<=0&&(o=o.append(t.type.contentMatch.matchFragment(o).fillBefore(r.FK.empty,!0)))),t.copy(o)}(e.mark(p.allowedMarks(e.marks)),1==d?a:0,d==l.childCount?u:-1)))}let f=d==l.childCount;f||(u=-1),this.placed=L(this.placed,t,r.FK.from(h)),this.frontier[t].match=c,f&&u<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=l;e<u;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=f?0==e?r.Ji.empty:new r.Ji(R(s.content,e-1,1),e-1,u<0?s.openEnd:e-1):new r.Ji(R(s.content,e,d),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!P(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){r:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=P(e,t,r,n,i);if(o){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],i=P(e,n,r,t,!0);if(!i||i.childCount)continue r}return{depth:t,fit:o,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=L(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=L(this.placed,this.depth,r.FK.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(r.FK.empty,!0);e.childCount&&(this.placed=L(this.placed,this.frontier.length,e))}}function R(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(R(e.firstChild.content,t-1,n)))}function L(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(L(e.lastChild.content,t-1,n)))}function I(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function P(e,t,n,r,i){let o=e.node(t),s=i?e.indexAfter(t):e.index(t);if(s==o.childCount&&!n.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,o.content,s)?l:null}function z(e,t){let n=[],r=Math.min(e.depth,t.depth);for(let i=r;i>=0;i--){let r=e.start(i);if(r<e.pos-(e.depth-i)||t.end(i)>t.pos+(t.depth-i)||e.node(i).type.spec.isolating||t.node(i).type.spec.isolating)break;(r==t.start(i)||i==e.depth&&i==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&i&&t.start(i-1)==r-1)&&n.push(i)}return n}class $ extends a{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return d.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let i=t.type.create(n,null,t.marks);return d.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(i),0,+!t.isLeaf))}getMap(){return o.empty}invert(e){return new $(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new $(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new $(t.pos,t.attr,t.value)}}a.jsonID("attr",$);class B extends a{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return d.ok(n)}getMap(){return o.empty}invert(e){return new B(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new B(t.attr,t.value)}}a.jsonID("docAttr",B);let F=class extends Error{};(F=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),F.prototype.constructor=F,F.prototype.name="TransformError";class j{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new s}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new F(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=r.Ji.empty){let i=E(this.doc,e,t,n);return i&&this.step(i),this}replaceWith(e,t,n){return this.replace(e,t,new r.Ji(r.FK.from(n),0,0))}delete(e,t){return this.replace(e,t,r.Ji.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return!function(e,t,n,i){if(!i.size)return e.deleteRange(t,n);let o=e.doc.resolve(t),s=e.doc.resolve(n);if(N(o,s,i))return e.step(new m(t,n,i));let l=z(o,e.doc.resolve(n));0==l[l.length-1]&&l.pop();let a=-(o.depth+1);l.unshift(a);for(let e=o.depth,t=o.pos-1;e>0;e--,t--){let n=o.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;l.indexOf(e)>-1?a=e:o.before(e)==t&&l.splice(1,0,-e)}let d=l.indexOf(a),h=[],c=i.openStart;for(let e=i.content,t=0;;t++){let n=e.firstChild;if(h.push(n),t==i.openStart)break;e=n.content}for(let e=c-1;e>=0;e--){var p;let t=h[e],n=(p=t.type).spec.defining||p.spec.definingForContent;if(n&&!t.sameMarkup(o.node(Math.abs(a)-1)))c=e;else if(n||!t.type.isTextblock)break}for(let t=i.openStart;t>=0;t--){let a=(t+c+1)%(i.openStart+1),p=h[a];if(p)for(let t=0;t<l.length;t++){let h=l[(t+d)%l.length],c=!0;h<0&&(c=!1,h=-h);let u=o.node(h-1),f=o.index(h-1);if(u.canReplaceWith(f,f,p.type,p.marks))return e.replace(o.before(h),c?s.after(h):n,new r.Ji(function e(t,n,i,o,s){if(n<i){let r=t.firstChild;t=t.replaceChild(0,r.copy(e(r.content,n+1,i,o,r)))}if(n>o){let e=s.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.FK.empty,!0))}return t}(i.content,0,i.openStart,a),a,i.openEnd))}}let u=e.steps.length;for(let r=l.length-1;r>=0&&(e.replace(t,n,i),!(e.steps.length>u));r--){let e=l[r];e<0||(t=o.before(e),n=s.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){var i=e,o=t;if(!n.isInline&&i==o&&this.doc.resolve(i).parent.content.size){let e=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let e=r.depth-1;e>=0;e--){let t=r.index(e);if(r.node(e).canReplaceWith(t,t,n))return r.before(e+1);if(t>0)return null}if(r.parentOffset==r.parent.content.size)for(let e=r.depth-1;e>=0;e--){let t=r.indexAfter(e);if(r.node(e).canReplaceWith(t,t,n))return r.after(e+1);if(t<r.node(e).childCount)break}return null}(this.doc,i,n.type);null!=e&&(i=o=e)}return this.replaceRange(i,o,new r.Ji(r.FK.from(n),0,0)),this}deleteRange(e,t){return!function(e,t,n){let r=e.doc.resolve(t),i=e.doc.resolve(n),o=z(r,i);for(let t=0;t<o.length;t++){let n=o[t],s=t==o.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return e.delete(r.start(n),i.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return e.delete(r.before(n),i.after(n))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(t-r.start(o)==r.depth-o&&n>r.end(o)&&i.end(o)-n!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return e.delete(r.before(o),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return!function(e,t,n){let{$from:i,$to:o,depth:s}=t,l=i.before(s+1),a=o.after(s+1),d=l,h=a,c=r.FK.empty,p=0;for(let e=s,t=!1;e>n;e--)t||i.index(e)>0?(t=!0,c=r.FK.from(i.node(e).copy(c)),p++):d--;let u=r.FK.empty,f=0;for(let e=s,t=!1;e>n;e--)t||o.after(e+1)<o.end(e)?(t=!0,u=r.FK.from(o.node(e).copy(u)),f++):h++;e.step(new g(d,h,l,a,new r.Ji(c.append(u),p,f),c.size-p,!0))}(this,e,t),this}join(e,t=1){return!function(e,t,n){let i=null,{linebreakReplacement:o}=e.doc.type.schema,s=e.doc.resolve(t-n),l=s.node().type;if(o&&l.inlineContent){let e="pre"==l.whitespace,t=!!l.contentMatch.matchType(o);e&&!t?i=!1:!e&&t&&(i=!0)}let a=e.steps.length;if(!1===i){let r=e.doc.resolve(t+n);S(e,r.node(),r.before(),a)}l.inlineContent&&b(e,t+n-1,l,s.node().contentMatchAt(s.index()),null==i);let d=e.mapping.slice(a),h=d.map(t-n);if(e.step(new m(h,d.map(t+n,-1),r.Ji.empty,!0)),!0===i){let t=e.doc.resolve(h);x(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return!function(e,t,n){let i=r.FK.empty;for(let e=n.length-1;e>=0;e--){if(i.size){let t=n[e].type.contentMatch.matchFragment(i);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=r.FK.from(n[e].type.create(n[e].attrs,i))}let o=t.start,s=t.end;e.step(new g(o,s,o,s,new r.Ji(i,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,i=null){var o=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let s=o.steps.length;return o.doc.nodesBetween(e,t,(e,t)=>{var l,a,d;let h,c,p="function"==typeof i?i(e):i;if(e.isTextblock&&!e.hasMarkup(n,p)&&(l=o.doc,a=o.mapping.slice(s).map(t),d=n,c=(h=l.resolve(a)).index(),h.parent.canReplaceWith(c,c+1,d))){let i=null;if(n.schema.linebreakReplacement){let e="pre"==n.whitespace,t=!!n.contentMatch.matchType(n.schema.linebreakReplacement);e&&!t?i=!1:!e&&t&&(i=!0)}!1===i&&S(o,e,t,s),b(o,o.mapping.slice(s).map(t,1),n,void 0,null===i);let l=o.mapping.slice(s),a=l.map(t,1),d=l.map(t+e.nodeSize,1);return o.step(new g(a,d,a+1,d-1,new r.Ji(r.FK.from(n.create(p,null,e.marks)),0,0),1,!0)),!0===i&&x(o,e,t,s),!1}}),this}setNodeMarkup(e,t,n=null,i){return!function(e,t,n,i,o){let s=e.doc.nodeAt(t);if(!s)throw RangeError("No node at given position");n||(n=s.type);let l=n.create(i,null,o||s.marks);if(s.isLeaf)return e.replaceWith(t,t+s.nodeSize,l);if(!n.validContent(s.content))throw RangeError("Invalid content for node type "+n.name);e.step(new g(t,t+s.nodeSize,t+1,t+s.nodeSize-1,new r.Ji(r.FK.from(l),0,0),1,!0))}(this,e,t,n,i),this}setNodeAttribute(e,t,n){return this.step(new $(e,t,n)),this}setDocAttribute(e,t){return this.step(new B(e,t)),this}addNodeMark(e,t){return this.step(new u(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(t instanceof r.CU)t.isInSet(n.marks)&&this.step(new f(e,t));else{let r=n.marks,i,o=[];for(;i=t.isInSet(r);)o.push(new f(e,i)),r=i.removeFromSet(r);for(let e=o.length-1;e>=0;e--)this.step(o[e])}return this}split(e,t=1,n){return!function(e,t,n=1,i){let o=e.doc.resolve(t),s=r.FK.empty,l=r.FK.empty;for(let e=o.depth,t=o.depth-n,a=n-1;e>t;e--,a--){s=r.FK.from(o.node(e).copy(s));let t=i&&i[a];l=r.FK.from(t?t.type.create(t.attrs,l):o.node(e).copy(l))}e.step(new m(t,t,new r.Ji(s.append(l),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var r;let i,o,s,l;return r=this,s=[],l=[],r.doc.nodesBetween(e,t,(r,a,d)=>{if(!r.isInline)return;let h=r.marks;if(!n.isInSet(h)&&d.type.allowsMarkType(n.type)){let d=Math.max(a,e),u=Math.min(a+r.nodeSize,t),f=n.addToSet(h);for(let e=0;e<h.length;e++)h[e].isInSet(f)||(i&&i.to==d&&i.mark.eq(h[e])?i.to=u:s.push(i=new p(d,u,h[e])));o&&o.to==d?o.to=u:l.push(o=new c(d,u,n))}}),s.forEach(e=>r.step(e)),l.forEach(e=>r.step(e)),this}removeMark(e,t,n){var i;let o,s;return i=this,o=[],s=0,i.doc.nodesBetween(e,t,(i,l)=>{if(!i.isInline)return;s++;let a=null;if(n instanceof r.sX){let e=i.marks,t;for(;t=n.isInSet(e);)(a||(a=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(i.marks)&&(a=[n]):a=i.marks;if(a&&a.length){let n=Math.min(l+i.nodeSize,t);for(let t=0;t<a.length;t++){let r=a[t],i;for(let e=0;e<o.length;e++){let t=o[e];t.step==s-1&&r.eq(o[e].style)&&(i=t)}i?(i.to=n,i.step=s):o.push({style:r,from:Math.max(l,e),to:n,step:s})}}}),o.forEach(e=>i.step(new p(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return b(this,e,t,n),this}}},98998:(e,t,n)=>{n.d(t,{XI:()=>eb,nA:()=>eh,A0:()=>ec,Hj:()=>ep});var r,i,o=n(53744),s=n(42404),l=n(42842),a=n(10664),d=n(66030),h=n(98539);if("undefined"!=typeof WeakMap){let e=new WeakMap;r=t=>e.get(t),i=(t,n)=>(e.set(t,n),n)}else{let e=[],t=0;r=t=>{for(let n=0;n<e.length;n+=2)if(e[n]==t)return e[n+1]},i=(n,r)=>(10==t&&(t=0),e[t++]=n,e[t++]=r)}var c=class{constructor(e,t,n,r){this.width=e,this.height=t,this.map=n,this.problems=r}findCell(e){for(let t=0;t<this.map.length;t++){let n=this.map[t];if(n!=e)continue;let r=t%this.width,i=t/this.width|0,o=r+1,s=i+1;for(let e=1;o<this.width&&this.map[t+e]==n;e++)o++;for(let e=1;s<this.height&&this.map[t+this.width*e]==n;e++)s++;return{left:r,top:i,right:o,bottom:s}}throw RangeError(`No cell with offset ${e} found`)}colCount(e){for(let t=0;t<this.map.length;t++)if(this.map[t]==e)return t%this.width;throw RangeError(`No cell with offset ${e} found`)}nextCell(e,t,n){let{left:r,right:i,top:o,bottom:s}=this.findCell(e);return"horiz"==t?(n<0?0==r:i==this.width)?null:this.map[o*this.width+(n<0?r-1:i)]:(n<0?0==o:s==this.height)?null:this.map[r+this.width*(n<0?o-1:s)]}rectBetween(e,t){let{left:n,right:r,top:i,bottom:o}=this.findCell(e),{left:s,right:l,top:a,bottom:d}=this.findCell(t);return{left:Math.min(n,s),top:Math.min(i,a),right:Math.max(r,l),bottom:Math.max(o,d)}}cellsInRect(e){let t=[],n={};for(let r=e.top;r<e.bottom;r++)for(let i=e.left;i<e.right;i++){let o=r*this.width+i,s=this.map[o];!n[s]&&(n[s]=!0,i==e.left&&i&&this.map[o-1]==s||r==e.top&&r&&this.map[o-this.width]==s||t.push(s))}return t}positionAt(e,t,n){for(let r=0,i=0;;r++){let o=i+n.child(r).nodeSize;if(r==e){let n=t+e*this.width,r=(e+1)*this.width;for(;n<r&&this.map[n]<i;)n++;return n==r?o-1:this.map[n]}i=o}}static get(e){return r(e)||i(e,function(e){if("table"!=e.type.spec.tableRole)throw RangeError("Not a table node: "+e.type.name);let t=function(e){let t=-1,n=!1;for(let r=0;r<e.childCount;r++){let i=e.child(r),o=0;if(n)for(let t=0;t<r;t++){let n=e.child(t);for(let e=0;e<n.childCount;e++){let i=n.child(e);t+i.attrs.rowspan>r&&(o+=i.attrs.colspan)}}for(let e=0;e<i.childCount;e++){let t=i.child(e);o+=t.attrs.colspan,t.attrs.rowspan>1&&(n=!0)}-1==t?t=o:t!=o&&(t=Math.max(t,o))}return t}(e),n=e.childCount,r=[],i=0,o=null,s=[];for(let e=0,i=t*n;e<i;e++)r[e]=0;for(let l=0,a=0;l<n;l++){let d=e.child(l);a++;for(let e=0;;e++){for(;i<r.length&&0!=r[i];)i++;if(e==d.childCount)break;let h=d.child(e),{colspan:c,rowspan:p,colwidth:u}=h.attrs;for(let e=0;e<p;e++){if(e+l>=n){(o||(o=[])).push({type:"overlong_rowspan",pos:a,n:p-e});break}let d=i+e*t;for(let e=0;e<c;e++){0==r[d+e]?r[d+e]=a:(o||(o=[])).push({type:"collision",row:l,pos:a,n:c-e});let n=u&&u[e];if(n){let r=(d+e)%t*2,i=s[r];null==i||i!=n&&1==s[r+1]?(s[r]=n,s[r+1]=1):i==n&&s[r+1]++}}}i+=c,a+=h.nodeSize}let h=(l+1)*t,c=0;for(;i<h;)0==r[i++]&&c++;c&&(o||(o=[])).push({type:"missing",row:l,n:c}),a++}(0===t||0===n)&&(o||(o=[])).push({type:"zero_sized"});let l=new c(t,n,r,o),a=!1;for(let e=0;!a&&e<s.length;e+=2)null!=s[e]&&s[e+1]<n&&(a=!0);return a&&function(e,t,n){e.problems||(e.problems=[]);let r={};for(let i=0;i<e.map.length;i++){let o=e.map[i];if(r[o])continue;r[o]=!0;let s=n.nodeAt(o);if(!s)throw RangeError(`No cell with offset ${o} found`);let l=null,a=s.attrs;for(let n=0;n<a.colspan;n++){let r=t[2*((i+n)%e.width)];null==r||a.colwidth&&a.colwidth[n]==r||((l||(l=function(e){if(e.colwidth)return e.colwidth.slice();let t=[];for(let n=0;n<e.colspan;n++)t.push(0);return t}(a)))[n]=r)}l&&e.problems.unshift({type:"colwidth mismatch",pos:o,colwidth:l})}}(l,s,e),l}(e))}};function p(e){let t=e.cached.tableNodeTypes;if(!t)for(let n in t=e.cached.tableNodeTypes={},e.nodes){let r=e.nodes[n],i=r.spec.tableRole;i&&(t[i]=r)}return t}var u=new s.hs("selectingCells");function f(e){for(let t=e.depth-1;t>0;t--)if("row"==e.node(t).type.spec.tableRole)return e.node(0).resolve(e.before(t+1));return null}function m(e){let t=e.selection.$head;for(let e=t.depth;e>0;e--)if("row"==t.node(e).type.spec.tableRole)return!0;return!1}function g(e){let t=e.selection;if("$anchorCell"in t&&t.$anchorCell)return t.$anchorCell.pos>t.$headCell.pos?t.$anchorCell:t.$headCell;if("node"in t&&t.node&&"cell"==t.node.type.spec.tableRole)return t.$anchor;let n=f(t.$head)||y(t.$head);if(n)return n;throw RangeError(`No cell found around position ${t.head}`)}function y(e){for(let t=e.nodeAfter,n=e.pos;t;t=t.firstChild,n++){let r=t.type.spec.tableRole;if("cell"==r||"header_cell"==r)return e.doc.resolve(n)}for(let t=e.nodeBefore,n=e.pos;t;t=t.lastChild,n--){let r=t.type.spec.tableRole;if("cell"==r||"header_cell"==r)return e.doc.resolve(n-t.nodeSize)}}function b(e){return"row"==e.parent.type.spec.tableRole&&!!e.nodeAfter}function v(e,t){return e.depth==t.depth&&e.pos>=t.start(-1)&&e.pos<=t.end(-1)}function w(e,t,n){let r=e.node(-1),i=c.get(r),o=e.start(-1),s=i.nextCell(e.pos-o,t,n);return null==s?null:e.node(0).resolve(o+s)}function k(e,t,n=1){let r={...e,colspan:e.colspan-n};return r.colwidth&&(r.colwidth=r.colwidth.slice(),r.colwidth.splice(t,n),r.colwidth.some(e=>e>0)||(r.colwidth=null)),r}function x(e,t,n=1){let r={...e,colspan:e.colspan+n};if(r.colwidth){r.colwidth=r.colwidth.slice();for(let e=0;e<n;e++)r.colwidth.splice(t,0,0)}return r}var S=class e extends s.LN{constructor(e,t=e){let n=e.node(-1),r=c.get(n),i=e.start(-1),o=r.rectBetween(e.pos-i,t.pos-i),l=e.node(0),a=r.cellsInRect(o).filter(e=>e!=t.pos-i);a.unshift(t.pos-i);let d=a.map(e=>{let t=n.nodeAt(e);if(!t)throw RangeError(`No cell with offset ${e} found`);let r=i+e+1;return new s.yn(l.resolve(r),l.resolve(r+t.content.size))});super(d[0].$from,d[0].$to,d),this.$anchorCell=e,this.$headCell=t}map(t,n){let r=t.resolve(n.map(this.$anchorCell.pos)),i=t.resolve(n.map(this.$headCell.pos));if(b(r)&&b(i)&&v(r,i)){let t=this.$anchorCell.node(-1)!=r.node(-1);return t&&this.isRowSelection()?e.rowSelection(r,i):t&&this.isColSelection()?e.colSelection(r,i):new e(r,i)}return s.U3.between(r,i)}content(){let e=this.$anchorCell.node(-1),t=c.get(e),n=this.$anchorCell.start(-1),r=t.rectBetween(this.$anchorCell.pos-n,this.$headCell.pos-n),i={},o=[];for(let n=r.top;n<r.bottom;n++){let s=[];for(let o=n*t.width+r.left,l=r.left;l<r.right;l++,o++){let n=t.map[o];if(i[n])continue;i[n]=!0;let l=t.findCell(n),a=e.nodeAt(n);if(!a)throw RangeError(`No cell with offset ${n} found`);let d=r.left-l.left,h=l.right-r.right;if(d>0||h>0){let e=a.attrs;if(d>0&&(e=k(e,0,d)),h>0&&(e=k(e,e.colspan-h,h)),l.left<r.left){if(!(a=a.type.createAndFill(e)))throw RangeError(`Could not create cell with attrs ${JSON.stringify(e)}`)}else a=a.type.create(e,a.content)}if(l.top<r.top||l.bottom>r.bottom){let e={...a.attrs,rowspan:Math.min(l.bottom,r.bottom)-Math.max(l.top,r.top)};a=l.top<r.top?a.type.createAndFill(e):a.type.create(e,a.content)}s.push(a)}o.push(e.child(n).copy(l.FK.from(s)))}let s=this.isColSelection()&&this.isRowSelection()?e:o;return new l.Ji(l.FK.from(s),1,1)}replace(e,t=l.Ji.empty){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],a=e.mapping.slice(n);e.replace(a.map(o.pos),a.map(s.pos),i?l.Ji.empty:t)}let i=s.LN.findFrom(e.doc.resolve(e.mapping.slice(n).map(this.to)),-1);i&&e.setSelection(i)}replaceWith(e,t){this.replace(e,new l.Ji(l.FK.from(t),0,0))}forEachCell(e){let t=this.$anchorCell.node(-1),n=c.get(t),r=this.$anchorCell.start(-1),i=n.cellsInRect(n.rectBetween(this.$anchorCell.pos-r,this.$headCell.pos-r));for(let n=0;n<i.length;n++)e(t.nodeAt(i[n]),r+i[n])}isColSelection(){let e=this.$anchorCell.index(-1),t=this.$headCell.index(-1);return!(Math.min(e,t)>0)&&Math.max(e+this.$anchorCell.nodeAfter.attrs.rowspan,t+this.$headCell.nodeAfter.attrs.rowspan)==this.$headCell.node(-1).childCount}static colSelection(t,n=t){let r=t.node(-1),i=c.get(r),o=t.start(-1),s=i.findCell(t.pos-o),l=i.findCell(n.pos-o),a=t.node(0);return s.top<=l.top?(s.top>0&&(t=a.resolve(o+i.map[s.left])),l.bottom<i.height&&(n=a.resolve(o+i.map[i.width*(i.height-1)+l.right-1]))):(l.top>0&&(n=a.resolve(o+i.map[l.left])),s.bottom<i.height&&(t=a.resolve(o+i.map[i.width*(i.height-1)+s.right-1]))),new e(t,n)}isRowSelection(){let e=this.$anchorCell.node(-1),t=c.get(e),n=this.$anchorCell.start(-1),r=t.colCount(this.$anchorCell.pos-n),i=t.colCount(this.$headCell.pos-n);return!(Math.min(r,i)>0)&&Math.max(r+this.$anchorCell.nodeAfter.attrs.colspan,i+this.$headCell.nodeAfter.attrs.colspan)==t.width}eq(t){return t instanceof e&&t.$anchorCell.pos==this.$anchorCell.pos&&t.$headCell.pos==this.$headCell.pos}static rowSelection(t,n=t){let r=t.node(-1),i=c.get(r),o=t.start(-1),s=i.findCell(t.pos-o),l=i.findCell(n.pos-o),a=t.node(0);return s.left<=l.left?(s.left>0&&(t=a.resolve(o+i.map[s.top*i.width])),l.right<i.width&&(n=a.resolve(o+i.map[i.width*(l.top+1)-1]))):(l.left>0&&(n=a.resolve(o+i.map[l.top*i.width])),s.right<i.width&&(t=a.resolve(o+i.map[i.width*(s.top+1)-1]))),new e(t,n)}toJSON(){return{type:"cell",anchor:this.$anchorCell.pos,head:this.$headCell.pos}}static fromJSON(t,n){return new e(t.resolve(n.anchor),t.resolve(n.head))}static create(t,n,r=n){return new e(t.resolve(n),t.resolve(r))}getBookmark(){return new M(this.$anchorCell.pos,this.$headCell.pos)}};S.prototype.visible=!1,s.LN.jsonID("cell",S);var M=class e{constructor(e,t){this.anchor=e,this.head=t}map(t){return new e(t.map(this.anchor),t.map(this.head))}resolve(e){let t=e.resolve(this.anchor),n=e.resolve(this.head);return"row"==t.parent.type.spec.tableRole&&"row"==n.parent.type.spec.tableRole&&t.index()<t.parent.childCount&&n.index()<n.parent.childCount&&v(t,n)?new S(t,n):s.LN.near(n,1)}};function C(e){if(!(e.selection instanceof S))return null;let t=[];return e.selection.forEachCell((e,n)=>{t.push(a.NZ.node(n,n+e.nodeSize,{class:"selectedCell"}))}),a.zF.create(e.doc,t)}var A=new s.hs("fix-tables");function T(e,t){let n,r=(t,r)=>{"table"==t.type.spec.tableRole&&(n=function(e,t,n,r){let i,o,s=c.get(t);if(!s.problems)return r;r||(r=e.tr);let l=[];for(let e=0;e<s.height;e++)l.push(0);for(let e=0;e<s.problems.length;e++){let i=s.problems[e];if("collision"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;let o=e.attrs;for(let e=0;e<o.rowspan;e++)l[i.row+e]+=i.n;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,k(o,o.colspan-i.n,i.n))}else if("missing"==i.type)l[i.row]+=i.n;else if("overlong_rowspan"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...e.attrs,rowspan:e.attrs.rowspan-i.n})}else if("colwidth mismatch"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...e.attrs,colwidth:i.colwidth})}else if("zero_sized"==i.type){let e=r.mapping.map(n);r.delete(e,e+t.nodeSize)}}for(let e=0;e<l.length;e++)l[e]&&(null==i&&(i=e),o=e);for(let a=0,d=n+1;a<s.height;a++){let n=t.child(a),s=d+n.nodeSize,h=l[a];if(h>0){let t="cell";n.firstChild&&(t=n.firstChild.type.spec.tableRole);let l=[];for(let n=0;n<h;n++){let n=p(e.schema)[t].createAndFill();n&&l.push(n)}let c=(0==a||i==a-1)&&o==a?d+1:s-1;r.insert(r.mapping.map(c),l)}d=s}return r.setMeta(A,{fixTables:!0})}(e,t,r,n))};return t?t.doc!=e.doc&&function e(t,n,r,i){let o=t.childCount,s=n.childCount;e:for(let l=0,a=0;l<s;l++){let s=n.child(l);for(let e=a,n=Math.min(o,l+3);e<n;e++)if(t.child(e)==s){a=e+1,r+=s.nodeSize;continue e}i(s,r),a<o&&t.child(a).sameMarkup(s)?e(t.child(a),s,r+1,i):s.nodesBetween(0,s.content.size,i,r+1),r+=s.nodeSize}}(t.doc,e.doc,0,r):e.doc.descendants(r),n}function O(e){var t=e=>"table"===e.type.spec.tableRole,n=e;for(let e=n.depth;e>=0;e-=1){let r=n.node(e);if(t(r))return{node:r,pos:0===e?0:n.before(e),start:n.start(e),depth:e}}return null}function E(e,t){let n=O(t.$from);if(!n)return;let r=c.get(n.node);if(!(e<0)&&!(e>r.width-1))return r.cellsInRect({left:e,right:e+1,top:0,bottom:r.height}).map(e=>{let t=n.node.nodeAt(e),r=e+n.start;return{pos:r,start:r+1,node:t,depth:n.depth+2}})}function N(e,t){let n=O(t.$from);if(!n)return;let r=c.get(n.node);if(!(e<0)&&!(e>r.height-1))return r.cellsInRect({left:0,right:r.width,top:e,bottom:e+1}).map(e=>{let t=n.node.nodeAt(e),r=e+n.start;return{pos:r,start:r+1,node:t,depth:n.depth+2}})}function D(e){let t=e.selection,n=g(e),r=n.node(-1),i=n.start(-1),o=c.get(r);return{...t instanceof S?o.rectBetween(t.$anchorCell.pos-i,t.$headCell.pos-i):o.findCell(n.pos-i),tableStart:i,map:o,table:r}}function R(e,{map:t,tableStart:n,table:r},i){let o=i>0?-1:0;(function(e,t,n){let r=p(t.type.schema).header_cell;for(let i=0;i<e.height;i++)if(t.nodeAt(e.map[n+i*e.width]).type!=r)return!1;return!0})(t,r,i+o)&&(o=0==i||i==t.width?null:0);for(let s=0;s<t.height;s++){let l=s*t.width+i;if(i>0&&i<t.width&&t.map[l-1]==t.map[l]){let o=t.map[l],a=r.nodeAt(o);e.setNodeMarkup(e.mapping.map(n+o),null,x(a.attrs,i-t.colCount(o))),s+=a.attrs.rowspan-1}else{let a=null==o?p(r.type.schema).cell:r.nodeAt(t.map[l+o]).type,d=t.positionAt(s,i,r);e.insert(e.mapping.map(n+d),a.createAndFill())}}return e}function L(e,{map:t,tableStart:n,table:r},i){var o;let s=n;for(let e=0;e<i;e++)s+=r.child(e).nodeSize;let l=[],a=i>0?-1:0;(function(e,t,n){var r;let i=p(t.type.schema).header_cell;for(let o=0;o<e.width;o++)if((null==(r=t.nodeAt(e.map[o+n*e.width]))?void 0:r.type)!=i)return!1;return!0})(t,r,i+a)&&(a=0==i||i==t.height?null:0);for(let s=0,d=t.width*i;s<t.width;s++,d++)if(i>0&&i<t.height&&t.map[d]==t.map[d-t.width]){let i=t.map[d],o=r.nodeAt(i).attrs;e.setNodeMarkup(n+i,null,{...o,rowspan:o.rowspan+1}),s+=o.colspan-1}else{let e=null==a?p(r.type.schema).cell:null==(o=r.nodeAt(t.map[d+a*t.width]))?void 0:o.type,n=null==e?void 0:e.createAndFill();n&&l.push(n)}return e.insert(s,p(r.type.schema).row.create(null,l)),e}function I(e){let t=e.content;return 1==t.childCount&&t.child(0).isTextblock&&0==t.child(0).childCount}function P(e,t){let n=e.selection;if(!(n instanceof S)||n.$anchorCell.pos==n.$headCell.pos)return!1;let r=D(e),{map:i}=r;if(function({width:e,height:t,map:n},r){let i=r.top*e+r.left,o=i,s=(r.bottom-1)*e+r.left,l=i+(r.right-r.left-1);for(let t=r.top;t<r.bottom;t++){if(r.left>0&&n[o]==n[o-1]||r.right<e&&n[l]==n[l+1])return!0;o+=e,l+=e}for(let o=r.left;o<r.right;o++){if(r.top>0&&n[i]==n[i-e]||r.bottom<t&&n[s]==n[s+e])return!0;i++,s++}return!1}(i,r))return!1;if(t){let n,o,s=e.tr,a={},d=l.FK.empty;for(let e=r.top;e<r.bottom;e++)for(let t=r.left;t<r.right;t++){let l=i.map[e*i.width+t],h=r.table.nodeAt(l);if(!a[l]&&h)if(a[l]=!0,null==n)n=l,o=h;else{I(h)||(d=d.append(h.content));let e=s.mapping.map(l+r.tableStart);s.delete(e,e+h.nodeSize)}}if(null==n||null==o)return!0;if(s.setNodeMarkup(n+r.tableStart,null,{...x(o.attrs,o.attrs.colspan,r.right-r.left-o.attrs.colspan),rowspan:r.bottom-r.top}),d.size){let e=n+1+o.content.size,t=I(o)?n+1:e;s.replaceWith(t+r.tableStart,e+r.tableStart,d)}s.setSelection(new S(s.doc.resolve(n+r.tableStart))),t(s)}return!0}function z(e,t){var n;let r=p(e.schema);return(n=({node:e})=>r[e.type.spec.tableRole],(e,t)=>{var r;let i,o,s=e.selection;if(s instanceof S){if(s.$anchorCell.pos!=s.$headCell.pos)return!1;i=s.$anchorCell.nodeAfter,o=s.$anchorCell.pos}else{if(!(i=function(e){for(let t=e.depth;t>0;t--){let n=e.node(t).type.spec.tableRole;if("cell"===n||"header_cell"===n)return e.node(t)}return null}(s.$from)))return!1;o=null==(r=f(s.$from))?void 0:r.pos}if(null==i||null==o||1==i.attrs.colspan&&1==i.attrs.rowspan)return!1;if(t){let r,l=i.attrs,a=[],d=l.colwidth;l.rowspan>1&&(l={...l,rowspan:1}),l.colspan>1&&(l={...l,colspan:1});let h=D(e),c=e.tr;for(let e=0;e<h.right-h.left;e++)a.push(d?{...l,colwidth:d&&d[e]?[d[e]]:null}:l);for(let e=h.top;e<h.bottom;e++){let t=h.map.positionAt(e,h.left,h.table);e==h.top&&(t+=i.nodeSize);for(let o=h.left,s=0;o<h.right;o++,s++)(o!=h.left||e!=h.top)&&c.insert(r=c.mapping.map(t+h.tableStart,1),n({node:i,row:e,col:o}).createAndFill(a[s]))}c.setNodeMarkup(o,n({node:i,row:h.top,col:h.left}),a[0]),s instanceof S&&c.setSelection(new S(c.doc.resolve(s.$anchorCell.pos),r?c.doc.resolve(r):void 0)),t(c)}return!0})(e,t)}function $(e,t,n){let r=t.map.cellsInRect({left:0,top:0,right:"row"==e?t.map.width:1,bottom:"column"==e?t.map.height:1});for(let e=0;e<r.length;e++){let i=t.table.nodeAt(r[e]);if(i&&i.type!==n.header_cell)return!1}return!0}function B(e,t){if((t=t||{useDeprecatedLogic:!1}).useDeprecatedLogic)return function(t,n){if(!m(t))return!1;if(n){let r=p(t.schema),i=D(t),o=t.tr,s=i.map.cellsInRect("column"==e?{left:i.left,top:0,right:i.right,bottom:i.map.height}:"row"==e?{left:0,top:i.top,right:i.map.width,bottom:i.bottom}:i),l=s.map(e=>i.table.nodeAt(e));for(let e=0;e<s.length;e++)l[e].type==r.header_cell&&o.setNodeMarkup(i.tableStart+s[e],r.cell,l[e].attrs);if(0==o.steps.length)for(let e=0;e<s.length;e++)o.setNodeMarkup(i.tableStart+s[e],r.header_cell,l[e].attrs);n(o)}return!0};return function(t,n){if(!m(t))return!1;if(n){let r=p(t.schema),i=D(t),o=t.tr,s=$("row",i,r),l=$("column",i,r),a=+!!("column"===e?s:"row"===e&&l),d="column"==e?{left:0,top:a,right:1,bottom:i.map.height}:"row"==e?{left:a,top:0,right:i.map.width,bottom:1}:i,h="column"==e?l?r.cell:r.header_cell:"row"==e?s?r.cell:r.header_cell:r.cell;i.map.cellsInRect(d).forEach(e=>{let t=e+i.tableStart,n=o.doc.nodeAt(t);n&&o.setNodeMarkup(t,h,n.attrs)}),n(o)}return!0}}B("row",{useDeprecatedLogic:!0}),B("column",{useDeprecatedLogic:!0});var F=B("cell",{useDeprecatedLogic:!0});function j(e){return function(t,n){if(!m(t))return!1;let r=function(e,t){if(t<0){let t=e.nodeBefore;if(t)return e.pos-t.nodeSize;for(let t=e.index(-1)-1,n=e.before();t>=0;t--){let r=e.node(-1).child(t),i=r.lastChild;if(i)return n-1-i.nodeSize;n-=r.nodeSize}}else{if(e.index()<e.parent.childCount-1)return e.pos+e.nodeAfter.nodeSize;let t=e.node(-1);for(let n=e.indexAfter(-1),r=e.after();n<t.childCount;n++){let e=t.child(n);if(e.childCount)return r+1;r+=e.nodeSize}}return null}(g(t),e);if(null==r)return!1;if(n){let e=t.doc.resolve(r);n(t.tr.setSelection(s.U3.between(e,e.node(0).resolve(e.pos+e.nodeAfter.nodeSize))).scrollIntoView())}return!0}}function V(e,t){let n=e.selection;if(!(n instanceof S))return!1;if(t){let r=e.tr,i=p(e.schema).cell.createAndFill().content;n.forEachCell((e,t)=>{e.content.eq(i)||r.replace(r.mapping.map(t+1),r.mapping.map(t+e.nodeSize-1),new l.Ji(i,0,0))}),r.docChanged&&t(r)}return!0}function H(e,t){let n=e.createAndFill();return new h.dL(n).replace(0,n.content.size,t).doc}function K(e,t,n,r,i,o,s,l){if(0==s||s==t.height)return!1;let a=!1;for(let d=i;d<o;d++){let i=s*t.width+d,o=t.map[i];if(t.map[i-t.width]==o){a=!0;let i=n.nodeAt(o),{top:h,left:c}=t.findCell(o);e.setNodeMarkup(e.mapping.slice(l).map(o+r),null,{...i.attrs,rowspan:s-h}),e.insert(e.mapping.slice(l).map(t.positionAt(s,c,n)),i.type.createAndFill({...i.attrs,rowspan:h+i.attrs.rowspan-s})),d+=i.attrs.colspan-1}}return a}function J(e,t,n,r,i,o,s,l){if(0==s||s==t.width)return!1;let a=!1;for(let d=i;d<o;d++){let i=d*t.width+s,o=t.map[i];if(t.map[i-1]==o){a=!0;let i=n.nodeAt(o),h=t.colCount(o),c=e.mapping.slice(l).map(o+r);e.setNodeMarkup(c,null,k(i.attrs,s-h,i.attrs.colspan-(s-h))),e.insert(c+i.nodeSize,i.type.createAndFill(k(i.attrs,0,s-h))),d+=i.attrs.rowspan-1}}return a}function W(e,t,n,r,i){let o=n?e.doc.nodeAt(n-1):e.doc;if(!o)throw Error("No table found");let s=c.get(o),{top:a,left:d}=r,h=d+i.width,u=a+i.height,f=e.tr,m=0;function g(){if(!(o=n?f.doc.nodeAt(n-1):f.doc))throw Error("No table found");s=c.get(o),m=f.mapping.maps.length}(function(e,t,n,r,i,o,s){let a,d,h=p(e.doc.type.schema);if(i>t.width)for(let o=0,s=0;o<t.height;o++){let l,c=n.child(o);s+=c.nodeSize;let p=[];l=null==c.lastChild||c.lastChild.type==h.cell?a||(a=h.cell.createAndFill()):d||(d=h.header_cell.createAndFill());for(let e=t.width;e<i;e++)p.push(l);e.insert(e.mapping.slice(0).map(s-1+r),p)}if(o>t.height){let c=[];for(let e=0,r=(t.height-1)*t.width;e<Math.max(t.width,i);e++){let i=!(e>=t.width)&&n.nodeAt(t.map[r+e]).type==h.header_cell;c.push(i?d||(d=h.header_cell.createAndFill()):a||(a=h.cell.createAndFill()))}let p=h.row.create(null,l.FK.from(c)),u=[];for(let e=t.height;e<o;e++)u.push(p);e.insert(e.mapping.slice(s).map(r+n.nodeSize-2),u)}return!!(a||d)})(f,s,o,n,h,u,0)&&g(),K(f,s,o,n,d,h,a,m)&&g(),K(f,s,o,n,d,h,u,m)&&g(),J(f,s,o,n,a,u,d,m)&&g(),J(f,s,o,n,a,u,h,m)&&g();for(let e=a;e<u;e++){let t=s.positionAt(e,d,o),r=s.positionAt(e,h,o);f.replace(f.mapping.slice(m).map(t+n),f.mapping.slice(m).map(r+n),new l.Ji(i.rows[e-a],0,0))}g(),f.setSelection(new S(f.doc.resolve(n+s.positionAt(a,d,o)),f.doc.resolve(n+s.positionAt(u-1,h-1,o)))),t(f)}var _=(0,d.K)({ArrowLeft:q("horiz",-1),ArrowRight:q("horiz",1),ArrowUp:q("vert",-1),ArrowDown:q("vert",1),"Shift-ArrowLeft":G("horiz",-1),"Shift-ArrowRight":G("horiz",1),"Shift-ArrowUp":G("vert",-1),"Shift-ArrowDown":G("vert",1),Backspace:V,"Mod-Backspace":V,Delete:V,"Mod-Delete":V});function U(e,t,n){return!n.eq(e.selection)&&(t&&t(e.tr.setSelection(n).scrollIntoView()),!0)}function q(e,t){return(n,r,i)=>{if(!i)return!1;let o=n.selection;if(o instanceof S)return U(n,r,s.LN.near(o.$headCell,t));if("horiz"!=e&&!o.empty)return!1;let l=Q(i,e,t);if(null==l)return!1;if("horiz"==e)return U(n,r,s.LN.near(n.doc.resolve(o.head+t),t));{let i,o=n.doc.resolve(l),a=w(o,e,t);return i=a?s.LN.near(a,1):t<0?s.LN.near(n.doc.resolve(o.before(-1)),-1):s.LN.near(n.doc.resolve(o.after(-1)),1),U(n,r,i)}}}function G(e,t){return(n,r,i)=>{let o;if(!i)return!1;let s=n.selection;if(s instanceof S)o=s;else{let r=Q(i,e,t);if(null==r)return!1;o=new S(n.doc.resolve(r))}let l=w(o.$headCell,e,t);return!!l&&U(n,r,new S(o.$anchorCell,l))}}function Y(e,t){let n=f(e.state.doc.resolve(t));return!!n&&(e.dispatch(e.state.tr.setSelection(new S(n))),!0)}function X(e,t,n){if(!m(e.state))return!1;let r=function(e){if(!e.size)return null;let{content:t,openStart:n,openEnd:r}=e;for(;1==t.childCount&&(n>0&&r>0||"table"==t.child(0).type.spec.tableRole);)n--,r--,t=t.child(0).content;let i=t.child(0),o=i.type.spec.tableRole,s=i.type.schema,a=[];if("row"==o)for(let e=0;e<t.childCount;e++){let i=t.child(e).content,o=e?0:Math.max(0,n-1),d=e<t.childCount-1?0:Math.max(0,r-1);(o||d)&&(i=H(p(s).row,new l.Ji(i,o,d)).content),a.push(i)}else{if("cell"!=o&&"header_cell"!=o)return null;a.push(n||r?H(p(s).row,new l.Ji(t,n,r)).content:t)}return function(e,t){let n=[];for(let e=0;e<t.length;e++){let r=t[e];for(let t=r.childCount-1;t>=0;t--){let{rowspan:i,colspan:o}=r.child(t).attrs;for(let t=e;t<e+i;t++)n[t]=(n[t]||0)+o}}let r=0;for(let e=0;e<n.length;e++)r=Math.max(r,n[e]);for(let i=0;i<n.length;i++)if(i>=t.length&&t.push(l.FK.empty),n[i]<r){let o=p(e).cell.createAndFill(),s=[];for(let e=n[i];e<r;e++)s.push(o);t[i]=t[i].append(l.FK.from(s))}return{height:t.length,width:r,rows:t}}(s,a)}(n),i=e.state.selection;if(i instanceof S){r||(r={width:1,height:1,rows:[l.FK.from(H(p(e.state.schema).cell,n))]});let t=i.$anchorCell.node(-1),o=i.$anchorCell.start(-1),s=c.get(t).rectBetween(i.$anchorCell.pos-o,i.$headCell.pos-o);return r=function({width:e,height:t,rows:n},r,i){if(e!=r){let t=[],i=[];for(let e=0;e<n.length;e++){let o=n[e],s=[];for(let n=t[e]||0,i=0;n<r;i++){let l=o.child(i%o.childCount);n+l.attrs.colspan>r&&(l=l.type.createChecked(k(l.attrs,l.attrs.colspan,n+l.attrs.colspan-r),l.content)),s.push(l),n+=l.attrs.colspan;for(let n=1;n<l.attrs.rowspan;n++)t[e+n]=(t[e+n]||0)+l.attrs.colspan}i.push(l.FK.from(s))}n=i,e=r}if(t!=i){let e=[];for(let r=0,o=0;r<i;r++,o++){let s=[],a=n[o%t];for(let e=0;e<a.childCount;e++){let t=a.child(e);r+t.attrs.rowspan>i&&(t=t.type.create({...t.attrs,rowspan:Math.max(1,i-t.attrs.rowspan)},t.content)),s.push(t)}e.push(l.FK.from(s))}n=e,t=i}return{width:e,height:t,rows:n}}(r,s.right-s.left,s.bottom-s.top),W(e.state,e.dispatch,o,s,r),!0}if(!r)return!1;{let t=g(e.state),n=t.start(-1);return W(e.state,e.dispatch,n,c.get(t.node(-1)).findCell(t.pos-n),r),!0}}function Z(e,t){var n;let r;if(t.ctrlKey||t.metaKey)return;let i=ee(e,t.target);if(t.shiftKey&&e.state.selection instanceof S)o(e.state.selection.$anchorCell,t),t.preventDefault();else if(t.shiftKey&&i&&null!=(r=f(e.state.selection.$anchor))&&(null==(n=et(e,t))?void 0:n.pos)!=r.pos)o(r,t),t.preventDefault();else if(!i)return;function o(t,n){let r=et(e,n),i=null==u.getState(e.state);if(!r||!v(t,r))if(!i)return;else r=t;let o=new S(t,r);if(i||!e.state.selection.eq(o)){let n=e.state.tr.setSelection(o);i&&n.setMeta(u,t.pos),e.dispatch(n)}}function s(){e.root.removeEventListener("mouseup",s),e.root.removeEventListener("dragstart",s),e.root.removeEventListener("mousemove",l),null!=u.getState(e.state)&&e.dispatch(e.state.tr.setMeta(u,-1))}function l(n){let r,l=u.getState(e.state);if(null!=l)r=e.state.doc.resolve(l);else if(ee(e,n.target)!=i&&!(r=et(e,t)))return s();r&&o(r,n)}e.root.addEventListener("mouseup",s),e.root.addEventListener("dragstart",s),e.root.addEventListener("mousemove",l)}function Q(e,t,n){if(!(e.state.selection instanceof s.U3))return null;let{$head:r}=e.state.selection;for(let i=r.depth-1;i>=0;i--){let o=r.node(i);if((n<0?r.index(i):r.indexAfter(i))!=(n<0?0:o.childCount))break;if("cell"==o.type.spec.tableRole||"header_cell"==o.type.spec.tableRole){let o=r.before(i),s="vert"==t?n>0?"down":"up":n>0?"right":"left";return e.endOfTextblock(s)?o:null}}return null}function ee(e,t){for(;t&&t!=e.dom;t=t.parentNode)if("TD"==t.nodeName||"TH"==t.nodeName)return t;return null}function et(e,t){let n=e.posAtCoords({left:t.clientX,top:t.clientY});return n&&n?f(e.state.doc.resolve(n.pos)):null}var en=class{constructor(e,t){this.node=e,this.defaultCellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.table.style.setProperty("--default-cell-min-width",`${t}px`),this.colgroup=this.table.appendChild(document.createElement("colgroup")),er(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type==this.node.type&&(this.node=e,er(e,this.colgroup,this.table,this.defaultCellMinWidth),!0)}ignoreMutation(e){return"attributes"==e.type&&(e.target==this.table||this.colgroup.contains(e.target))}};function er(e,t,n,r,i,o){var s;let l=0,a=!0,d=t.firstChild,h=e.firstChild;if(h){for(let e=0,n=0;e<h.childCount;e++){let{colspan:s,colwidth:c}=h.child(e).attrs;for(let e=0;e<s;e++,n++){let s=i==n?o:c&&c[e],h=s?s+"px":"";if(l+=s||r,s||(a=!1),d)d.style.width!=h&&(d.style.width=h),d=d.nextSibling;else{let e=document.createElement("col");e.style.width=h,t.appendChild(e)}}}for(;d;){let e=d.nextSibling;null==(s=d.parentNode)||s.removeChild(d),d=e}a?(n.style.width=l+"px",n.style.minWidth=""):(n.style.width="",n.style.minWidth=l+"px")}}var ei=new s.hs("tableColumnResizing"),eo=class e{constructor(e,t){this.activeHandle=e,this.dragging=t}apply(t){let n=t.getMeta(ei);if(n&&null!=n.setHandle)return new e(n.setHandle,!1);if(n&&void 0!==n.setDragging)return new e(this.activeHandle,n.setDragging);if(this.activeHandle>-1&&t.docChanged){let n=t.mapping.map(this.activeHandle,-1);return b(t.doc.resolve(n))||(n=-1),new e(n,this.dragging)}return this}};function es(e,t,n,r){let i=e.posAtCoords({left:t.clientX+("right"==n?-r:r),top:t.clientY});if(!i)return -1;let{pos:o}=i,s=f(e.state.doc.resolve(o));if(!s)return -1;if("right"==n)return s.pos;let l=c.get(s.node(-1)),a=s.start(-1),d=l.map.indexOf(s.pos-a);return d%l.width==0?-1:a+l.map[d-1]}function el(e,t,n){let r=t.clientX-e.startX;return Math.max(n,e.startWidth+r)}function ea(e,t){e.dispatch(e.state.tr.setMeta(ei,{setHandle:t}))}function ed(e,t,n,r){let i=e.state.doc.resolve(t),o=i.node(-1),s=i.start(-1),l=c.get(o).colCount(i.pos-s)+i.nodeAfter.attrs.colspan-1,a=e.domAtPos(i.start(-1)).node;for(;a&&"TABLE"!=a.nodeName;)a=a.parentNode;a&&er(o,a.firstChild,a,r,l,n)}var eh=o.bP.create({name:"tableCell",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"cell",isolating:!0,parseHTML:()=>[{tag:"td"}],renderHTML({HTMLAttributes:e}){return["td",(0,o.KV)(this.options.HTMLAttributes,e),0]}}),ec=o.bP.create({name:"tableHeader",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"header_cell",isolating:!0,parseHTML:()=>[{tag:"th"}],renderHTML({HTMLAttributes:e}){return["th",(0,o.KV)(this.options.HTMLAttributes,e),0]}}),ep=o.bP.create({name:"tableRow",addOptions:()=>({HTMLAttributes:{}}),content:"(tableCell | tableHeader)*",tableRole:"row",parseHTML:()=>[{tag:"tr"}],renderHTML({HTMLAttributes:e}){return["tr",(0,o.KV)(this.options.HTMLAttributes,e),0]}});function eu(e,t){return t?["width",`${Math.max(t,e)}px`]:["min-width",`${e}px`]}function ef(e,t,n,r,i,o){var s;let l=0,a=!0,d=t.firstChild,h=e.firstChild;if(null!==h)for(let e=0,n=0;e<h.childCount;e+=1){let{colspan:s,colwidth:c}=h.child(e).attrs;for(let e=0;e<s;e+=1,n+=1){let s=i===n?o:c&&c[e],h=s?`${s}px`:"";if(l+=s||r,s||(a=!1),d){if(d.style.width!==h){let[e,t]=eu(r,s);d.style.setProperty(e,t)}d=d.nextSibling}else{let e=document.createElement("col"),[n,i]=eu(r,s);e.style.setProperty(n,i),t.appendChild(e)}}}for(;d;){let e=d.nextSibling;null==(s=d.parentNode)||s.removeChild(d),d=e}a?(n.style.width=`${l}px`,n.style.minWidth=""):(n.style.width="",n.style.minWidth=`${l}px`)}var em=class{constructor(e,t){this.node=e,this.cellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.colgroup=this.table.appendChild(document.createElement("colgroup")),ef(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type===this.node.type&&(this.node=e,ef(e,this.colgroup,this.table,this.cellMinWidth),!0)}ignoreMutation(e){return"attributes"===e.type&&(e.target===this.table||this.colgroup.contains(e.target))}};function eg(e,t){return t?e.createChecked(null,t):e.createAndFill()}var ey=({editor:e})=>{let{selection:t}=e.state;if(!(t instanceof S))return!1;let n=0,r=(0,o.eL)(t.ranges[0].$from,e=>"table"===e.type.name);return null==r||r.node.descendants(e=>{if("table"===e.type.name)return!1;["tableCell","tableHeader"].includes(e.type.name)&&(n+=1)}),n===t.ranges.length&&(e.commands.deleteTable(),!0)},eb=o.bP.create({name:"table",addOptions:()=>({HTMLAttributes:{},resizable:!1,handleWidth:5,cellMinWidth:25,View:em,lastColumnResizable:!0,allowTableNodeSelection:!1}),content:"tableRow+",tableRole:"table",isolating:!0,group:"block",parseHTML:()=>[{tag:"table"}],renderHTML({node:e,HTMLAttributes:t}){let{colgroup:n,tableWidth:r,tableMinWidth:i}=function(e,t,n,r){let i=0,o=!0,s=[],l=e.firstChild;if(!l)return{};for(let e=0,n=0;e<l.childCount;e+=1){let{colspan:r,colwidth:a}=l.child(e).attrs;for(let e=0;e<r;e+=1,n+=1){let r=void 0===n?void 0:a&&a[e];i+=r||t,r||(o=!1);let[l,d]=eu(t,r);s.push(["col",{style:`${l}: ${d}`}])}}return{colgroup:["colgroup",{},...s],tableWidth:o?`${i}px`:"",tableMinWidth:o?"":`${i}px`}}(e,this.options.cellMinWidth);return["table",(0,o.KV)(this.options.HTMLAttributes,t,{style:r?`width: ${r}`:`min-width: ${i}`}),n,["tbody",0]]},addCommands:()=>({insertTable:({rows:e=3,cols:t=3,withHeaderRow:n=!0}={})=>({tr:r,dispatch:i,editor:o})=>{let l=function(e,t,n,r,i){let o=function(e){if(e.cached.tableNodeTypes)return e.cached.tableNodeTypes;let t={};return Object.keys(e.nodes).forEach(n=>{let r=e.nodes[n];r.spec.tableRole&&(t[r.spec.tableRole]=r)}),e.cached.tableNodeTypes=t,t}(e),s=[],l=[];for(let e=0;e<n;e+=1){let e=eg(o.cell,void 0);if(e&&l.push(e),r){let e=eg(o.header_cell,void 0);e&&s.push(e)}}let a=[];for(let e=0;e<t;e+=1)a.push(o.row.createChecked(null,r&&0===e?s:l));return o.table.createChecked(null,a)}(o.schema,e,t,n);if(i){let e=r.selection.from+1;r.replaceSelectionWith(l).scrollIntoView().setSelection(s.U3.near(r.doc.resolve(e)))}return!0},addColumnBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=D(e);t(R(e.tr,n,n.left))}return!0})(e,t),addColumnAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=D(e);t(R(e.tr,n,n.right))}return!0})(e,t),deleteColumn:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=D(e),r=e.tr;if(0==n.left&&n.right==n.map.width)return!1;for(let e=n.right-1;!function(e,{map:t,table:n,tableStart:r},i){let o=e.mapping.maps.length;for(let s=0;s<t.height;){let l=s*t.width+i,a=t.map[l],d=n.nodeAt(a),h=d.attrs;if(i>0&&t.map[l-1]==a||i<t.width-1&&t.map[l+1]==a)e.setNodeMarkup(e.mapping.slice(o).map(r+a),null,k(h,i-t.colCount(a)));else{let t=e.mapping.slice(o).map(r+a);e.delete(t,t+d.nodeSize)}s+=h.rowspan}}(r,n,e),e!=n.left;e--){let e=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=c.get(e)}t(r)}return!0})(e,t),addRowBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=D(e);t(L(e.tr,n,n.top))}return!0})(e,t),addRowAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=D(e);t(L(e.tr,n,n.bottom))}return!0})(e,t),deleteRow:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=D(e),r=e.tr;if(0==n.top&&n.bottom==n.map.height)return!1;for(let e=n.bottom-1;!function(e,{map:t,table:n,tableStart:r},i){let o=0;for(let e=0;e<i;e++)o+=n.child(e).nodeSize;let s=o+n.child(i).nodeSize,l=e.mapping.maps.length;e.delete(o+r,s+r);let a=new Set;for(let o=0,s=i*t.width;o<t.width;o++,s++){let d=t.map[s];if(!a.has(d)){if(a.add(d),i>0&&d==t.map[s-t.width]){let t=n.nodeAt(d).attrs;e.setNodeMarkup(e.mapping.slice(l).map(d+r),null,{...t,rowspan:t.rowspan-1}),o+=t.colspan-1}else if(i<t.height&&d==t.map[s+t.width]){let s=n.nodeAt(d),a=s.attrs,h=s.type.create({...a,rowspan:s.attrs.rowspan-1},s.content),c=t.positionAt(i+1,o,n);e.insert(e.mapping.slice(l).map(r+c),h),o+=a.colspan-1}}}}(r,n,e),e!=n.top;e--){let e=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=c.get(n.table)}t(r)}return!0})(e,t),deleteTable:()=>({state:e,dispatch:t})=>(function(e,t){let n=e.selection.$anchor;for(let r=n.depth;r>0;r--)if("table"==n.node(r).type.spec.tableRole)return t&&t(e.tr.delete(n.before(r),n.after(r)).scrollIntoView()),!0;return!1})(e,t),mergeCells:()=>({state:e,dispatch:t})=>P(e,t),splitCell:()=>({state:e,dispatch:t})=>z(e,t),toggleHeaderColumn:()=>({state:e,dispatch:t})=>B("column")(e,t),toggleHeaderRow:()=>({state:e,dispatch:t})=>B("row")(e,t),toggleHeaderCell:()=>({state:e,dispatch:t})=>F(e,t),mergeOrSplit:()=>({state:e,dispatch:t})=>!!P(e,t)||z(e,t),setCellAttribute:(e,t)=>({state:n,dispatch:r})=>(function(e,t){return function(n,r){if(!m(n))return!1;let i=g(n);if(i.nodeAfter.attrs[e]===t)return!1;if(r){let o=n.tr;n.selection instanceof S?n.selection.forEachCell((n,r)=>{n.attrs[e]!==t&&o.setNodeMarkup(r,null,{...n.attrs,[e]:t})}):o.setNodeMarkup(i.pos,null,{...i.nodeAfter.attrs,[e]:t}),r(o)}return!0}})(e,t)(n,r),goToNextCell:()=>({state:e,dispatch:t})=>j(1)(e,t),goToPreviousCell:()=>({state:e,dispatch:t})=>j(-1)(e,t),fixTables:()=>({state:e,dispatch:t})=>(t&&T(e),!0),setCellSelection:e=>({tr:t,dispatch:n})=>{if(n){let n=S.create(t.doc,e.anchorCell,e.headCell);t.setSelection(n)}return!0}}),addKeyboardShortcuts(){return{Tab:()=>!!this.editor.commands.goToNextCell()||!!this.editor.can().addRowAfter()&&this.editor.chain().addRowAfter().goToNextCell().run(),"Shift-Tab":()=>this.editor.commands.goToPreviousCell(),Backspace:ey,"Mod-Backspace":ey,Delete:ey,"Mod-Delete":ey}},addProseMirrorPlugins(){return[...this.options.resizable&&this.editor.isEditable?[function({handleWidth:e=5,cellMinWidth:t=25,defaultCellMinWidth:n=100,View:r=en,lastColumnResizable:i=!0}={}){let o=new s.k_({key:ei,state:{init(e,t){var i,s;let l=null==(s=null==(i=o.spec)?void 0:i.props)?void 0:s.nodeViews,a=p(t.schema).table.name;return r&&l&&(l[a]=(e,t)=>new r(e,n,t)),new eo(-1,!1)},apply:(e,t)=>t.apply(e)},props:{attributes:e=>{let t=ei.getState(e);return t&&t.activeHandle>-1?{class:"resize-cursor"}:{}},handleDOMEvents:{mousemove:(t,n)=>{!function(e,t,n,r){if(!e.editable)return;let i=ei.getState(e.state);if(i&&!i.dragging){let o=function(e){for(;e&&"TD"!=e.nodeName&&"TH"!=e.nodeName;)e=e.classList&&e.classList.contains("ProseMirror")?null:e.parentNode;return e}(t.target),s=-1;if(o){let{left:r,right:i}=o.getBoundingClientRect();t.clientX-r<=n?s=es(e,t,"left",n):i-t.clientX<=n&&(s=es(e,t,"right",n))}if(s!=i.activeHandle){if(!r&&-1!==s){let t=e.state.doc.resolve(s),n=t.node(-1),r=c.get(n),i=t.start(-1);if(r.colCount(t.pos-i)+t.nodeAfter.attrs.colspan-1==r.width-1)return}ea(e,s)}}}(t,n,e,i)},mouseleave:e=>{!function(e){if(!e.editable)return;let t=ei.getState(e.state);t&&t.activeHandle>-1&&!t.dragging&&ea(e,-1)}(e)},mousedown:(e,r)=>{!function(e,t,n,r){var i;if(!e.editable)return;let o=null!=(i=e.dom.ownerDocument.defaultView)?i:window,s=ei.getState(e.state);if(!s||-1==s.activeHandle||s.dragging)return;let l=e.state.doc.nodeAt(s.activeHandle),a=function(e,t,{colspan:n,colwidth:r}){let i=r&&r[r.length-1];if(i)return i;let o=e.domAtPos(t),s=o.node.childNodes[o.offset].offsetWidth,l=n;if(r)for(let e=0;e<n;e++)r[e]&&(s-=r[e],l--);return s/l}(e,s.activeHandle,l.attrs);function d(t){o.removeEventListener("mouseup",d),o.removeEventListener("mousemove",h);let r=ei.getState(e.state);(null==r?void 0:r.dragging)&&(function(e,t,n){let r=e.state.doc.resolve(t),i=r.node(-1),o=c.get(i),s=r.start(-1),l=o.colCount(r.pos-s)+r.nodeAfter.attrs.colspan-1,a=e.state.tr;for(let e=0;e<o.height;e++){let t=e*o.width+l;if(e&&o.map[t]==o.map[t-o.width])continue;let r=o.map[t],d=i.nodeAt(r).attrs,h=1==d.colspan?0:l-o.colCount(r);if(d.colwidth&&d.colwidth[h]==n)continue;let c=d.colwidth?d.colwidth.slice():Array(d.colspan).fill(0);c[h]=n,a.setNodeMarkup(s+r,null,{...d,colwidth:c})}a.docChanged&&e.dispatch(a)}(e,r.activeHandle,el(r.dragging,t,n)),e.dispatch(e.state.tr.setMeta(ei,{setDragging:null})))}function h(t){if(!t.which)return d(t);let i=ei.getState(e.state);if(i&&i.dragging){let o=el(i.dragging,t,n);ed(e,i.activeHandle,o,r)}}e.dispatch(e.state.tr.setMeta(ei,{setDragging:{startX:t.clientX,startWidth:a}})),ed(e,s.activeHandle,a,r),o.addEventListener("mouseup",d),o.addEventListener("mousemove",h),t.preventDefault()}(e,r,t,n)}},decorations:e=>{let t=ei.getState(e);if(t&&t.activeHandle>-1)return function(e,t){var n;let r=[],i=e.doc.resolve(t),o=i.node(-1);if(!o)return a.zF.empty;let s=c.get(o),l=i.start(-1),d=s.colCount(i.pos-l)+i.nodeAfter.attrs.colspan-1;for(let t=0;t<s.height;t++){let i=d+t*s.width;if((d==s.width-1||s.map[i]!=s.map[i+1])&&(0==t||s.map[i]!=s.map[i-s.width])){let t=s.map[i],d=l+t+o.nodeAt(t).nodeSize-1,h=document.createElement("div");h.className="column-resize-handle",(null==(n=ei.getState(e))?void 0:n.dragging)&&r.push(a.NZ.node(l+t,l+t+o.nodeAt(t).nodeSize,{class:"column-resize-dragging"})),r.push(a.NZ.widget(d,h))}}return a.zF.create(e.doc,r)}(e,t.activeHandle)},nodeViews:{}}});return o}({handleWidth:this.options.handleWidth,cellMinWidth:this.options.cellMinWidth,defaultCellMinWidth:this.options.cellMinWidth,View:this.options.View,lastColumnResizable:this.options.lastColumnResizable})]:[],function({allowTableNodeSelection:e=!1}={}){return new s.k_({key:u,state:{init:()=>null,apply(e,t){let n=e.getMeta(u);if(null!=n)return -1==n?null:n;if(null==t||!e.docChanged)return t;let{deleted:r,pos:i}=e.mapping.mapResult(t);return r?null:i}},props:{decorations:C,handleDOMEvents:{mousedown:Z},createSelectionBetween:e=>null!=u.getState(e.state)?e.state.selection:null,handleTripleClick:Y,handleKeyDown:_,handlePaste:X},appendTransaction:(t,n,r)=>(function(e,t,n){let r,i,o=(t||e).selection,l=(t||e).doc;if(o instanceof s.nh&&(i=o.node.type.spec.tableRole)){if("cell"==i||"header_cell"==i)r=S.create(l,o.from);else if("row"==i){let e=l.resolve(o.from+1);r=S.rowSelection(e,e)}else if(!n){let e=c.get(o.node),t=o.from+1,n=t+e.map[e.width*e.height-1];r=S.create(l,t+1,n)}}else o instanceof s.U3&&function({$from:e,$to:t}){if(e.pos==t.pos||e.pos<t.pos-6)return!1;let n=e.pos,r=t.pos,i=e.depth;for(;i>=0&&!(e.after(i+1)<e.end(i));i--,n++);for(let e=t.depth;e>=0&&!(t.before(e+1)>t.start(e));e--,r--);return n==r&&/row|table/.test(e.node(i).type.spec.tableRole)}(o)?r=s.U3.create(l,o.from):o instanceof s.U3&&function({$from:e,$to:t}){let n,r;for(let t=e.depth;t>0;t--){let r=e.node(t);if("cell"===r.type.spec.tableRole||"header_cell"===r.type.spec.tableRole){n=r;break}}for(let e=t.depth;e>0;e--){let n=t.node(e);if("cell"===n.type.spec.tableRole||"header_cell"===n.type.spec.tableRole){r=n;break}}return n!==r&&0===t.parentOffset}(o)&&(r=s.U3.create(l,o.$from.start(),o.$from.end()));return r&&(t||(t=e.tr)).setSelection(r),t})(r,T(r,n),e)})}({allowTableNodeSelection:this.options.allowTableNodeSelection})]},extendNodeSchema(e){let t={name:e.name,options:e.options,storage:e.storage};return{tableRole:(0,o.gk)((0,o.iI)(e,"tableRole",t))}}});o.YY.create({name:"tableKit",addExtensions(){let e=[];return!1!==this.options.table&&e.push(eb.configure(this.options.table)),!1!==this.options.tableCell&&e.push(eh.configure(this.options.tableCell)),!1!==this.options.tableHeader&&e.push(ec.configure(this.options.tableHeader)),!1!==this.options.tableRow&&e.push(ep.configure(this.options.tableRow)),e}})},99462:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(55732).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])}};
//# sourceMappingURL=6429.js.map