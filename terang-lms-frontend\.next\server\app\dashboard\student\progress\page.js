try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="83f576f3-93bd-4eeb-8457-967134a27f99",e._sentryDebugIdIdentifier="sentry-dbid-83f576f3-93bd-4eeb-8457-967134a27f99")}catch(e){}(()=>{var e={};e.id=5297,e.ids=[5297],e.modules={116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(91754);function s({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}r(93491),r(76328)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5827:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var a=r(91754),s=r(93491),n=r(66536),o=r(82233);let i=s.forwardRef(({className:e,value:t,...r},s)=>(0,a.jsx)(n.bL,{ref:s,className:(0,o.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,a.jsx)(n.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));i.displayName=n.bL.displayName},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var a=r(91754);r(93491);var s=r(82233);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},9770:(e,t,r)=>{Promise.resolve().then(r.bind(r,80819))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11101:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=r(95500),s=r(56947),n=r(26052),o=r(13636),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);r.d(t,i);let l={children:["",{children:["dashboard",{children:["student",{children:["progress",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80819)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\progress\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,97890)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\progress\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/student/progress/page",pathname:"/dashboard/student/progress",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},36991:(e,t,r)=>{"use strict";r.d(t,{B8:()=>D,UC:()=>E,bL:()=>P,l9:()=>M});var a=r(93491),s=r(18682),n=r(10158),o=r(92023),i=r(55462),l=r(90604),d=r(78283),c=r(76322),u=r(62962),p=r(91754),m="Tabs",[f,y]=(0,n.A)(m,[o.RG]),x=(0,o.RG)(),[h,g]=f(m),v=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:s,defaultValue:n,orientation:o="horizontal",dir:i,activationMode:f="automatic",...y}=e,x=(0,d.jH)(i),[g,v]=(0,c.i)({prop:a,onChange:s,defaultProp:n??"",caller:m});return(0,p.jsx)(h,{scope:r,baseId:(0,u.B)(),value:g,onValueChange:v,orientation:o,dir:x,activationMode:f,children:(0,p.jsx)(l.sG.div,{dir:x,"data-orientation":o,...y,ref:t})})});v.displayName=m;var b="TabsList",j=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...s}=e,n=g(b,r),i=x(r);return(0,p.jsx)(o.bL,{asChild:!0,...i,orientation:n.orientation,dir:n.dir,loop:a,children:(0,p.jsx)(l.sG.div,{role:"tablist","aria-orientation":n.orientation,...s,ref:t})})});j.displayName=b;var A="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...i}=e,d=g(A,r),c=x(r),u=S(d.baseId,a),m=k(d.baseId,a),f=a===d.value;return(0,p.jsx)(o.q7,{asChild:!0,...c,focusable:!n,active:f,children:(0,p.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":m,"data-state":f?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...i,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,s.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;f||n||!e||d.onValueChange(a)})})})});w.displayName=A;var C="TabsContent",N=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,forceMount:n,children:o,...d}=e,c=g(C,r),u=S(c.baseId,s),m=k(c.baseId,s),f=s===c.value,y=a.useRef(f);return a.useEffect(()=>{let e=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(i.C,{present:n||f,children:({present:r})=>(0,p.jsx)(l.sG.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:y.current?"0s":void 0},children:r&&o})})});function S(e,t){return`${e}-trigger-${t}`}function k(e,t){return`${e}-content-${t}`}N.displayName=C;var P=v,D=j,M=w,E=N},37067:e=>{"use strict";e.exports=require("node:http")},38006:(e,t,r)=>{Promise.resolve().then(r.bind(r,116))},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},51897:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54090:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>i});var a=r(91754),s=r(93491),n=r(36991),o=r(82233);let i=n.bL,l=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(n.B8,{ref:r,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=n.B8.displayName;let d=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(n.l9,{ref:r,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",e),...t}));d.displayName=n.l9.displayName;let c=s.forwardRef(({className:e,...t},r)=>(0,a.jsx)(n.UC,{ref:r,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=n.UC.displayName},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>v,generateImageMetadata:()=>h,generateMetadata:()=>x,generateViewport:()=>g,metadata:()=>p});var s=r(63033),n=r(18188),o=r(5434),i=r(45188),l=r(67999),d=r(4590),c=r(23064),u=r(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(o.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(d.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(i.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(l.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let f={...s},y="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;a=new Proxy(m,{apply:(e,t,r)=>{let a,s,n;try{let e=y?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}});let x=void 0,h=void 0,g=void 0,v=a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66536:(e,t,r)=>{"use strict";r.d(t,{C1:()=>j,bL:()=>b});var a=r(93491),s=r(10158),n=r(90604),o=r(91754),i="Progress",[l,d]=(0,s.A)(i),[c,u]=l(i),p=a.forwardRef((e,t)=>{var r,a;let{__scopeProgress:s,value:i=null,max:l,getValueLabel:d=y,...u}=e;(l||0===l)&&!g(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=g(l)?l:100;null===i||v(i,p)||console.error((a=`${i}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=v(i,p)?i:null,f=h(m)?d(m,p):void 0;return(0,o.jsx)(c,{scope:s,value:m,max:p,children:(0,o.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":h(m)?m:void 0,"aria-valuetext":f,role:"progressbar","data-state":x(m,p),"data-value":m??void 0,"data-max":p,...u,ref:t})})});p.displayName=i;var m="ProgressIndicator",f=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,s=u(m,r);return(0,o.jsx)(n.sG.div,{"data-state":x(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...a,ref:t})});function y(e,t){return`${Math.round(e/t*100)}%`}function x(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function h(e){return"number"==typeof e}function g(e){return h(e)&&!isNaN(e)&&e>0}function v(e,t){return h(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=m;var b=p,j=f},72818:(e,t,r)=>{Promise.resolve().then(r.bind(r,93803))},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80601:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(91754);r(93491);var s=r(16435),n=r(25758),o=r(82233);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?s.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},80819:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var s=r(63033),n=r(1472),o=r(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\progress\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\progress\\page.tsx","default");let l={...s},d="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;a="function"==typeof i?new Proxy(i,{apply:(e,t,r)=>{let a,s,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student/progress",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}}):i;let c=void 0,u=void 0,p=void 0,m=a},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},86857:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},93803:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>en});var a=r(91754),s=r(9260),n=r(80601),o=r(5827),i=r(54090),l=r(22715),d=r(5346),c=r(67957),u=r(37578),p=r(32005),m=r(63623),f=r(69078),y=r(31981),x=r(93491),h=r.n(x),g=r(35878),v=r(56584),b=r.n(v),j=r(97645),A=r.n(j),w=r(65470),C=r.n(w),N=r(72995),S=r(73042),k=r(61731),P=r(11019),D=r(28604),M=r(19232),E=r(39398),T=r(74367),O=r(54744),q=r(89064),R=["type","layout","connectNulls","ref"],_=["key"];function W(e){return(W="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function L(e,t){if(null==e)return{};var r,a,s=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)r=n[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach(function(t){H(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function B(e){return function(e){if(Array.isArray(e))return F(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return F(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return F(e,t)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function U(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,V(a.key),a)}}function K(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(K=function(){return!!e})()}function Z(e){return(Z=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function Q(e,t){return(Q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function H(e,t,r){return(t=V(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function V(e){var t=function(e,t){if("object"!=W(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=W(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==W(t)?t:t+""}var $=function(e){var t,r;function a(){var e,t,r;if(!(this instanceof a))throw TypeError("Cannot call a class as a function");for(var s=arguments.length,n=Array(s),o=0;o<s;o++)n[o]=arguments[o];return t=a,r=[].concat(n),t=Z(t),H(e=function(e,t){if(t&&("object"===W(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,K()?Reflect.construct(t,r||[],Z(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),H(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),H(e,"getStrokeDasharray",function(t,r,s){var n=s.reduce(function(e,t){return e+t});if(!n)return e.generateSimpleStrokeDasharray(r,t);for(var o=Math.floor(t/n),i=t%n,l=r-t,d=[],c=0,u=0;c<s.length;u+=s[c],++c)if(u+s[c]>i){d=[].concat(B(s.slice(0,c)),[i-u]);break}var p=d.length%2==0?[0,l]:[l];return[].concat(B(a.repeat(s,o)),B(d),p).map(function(e){return"".concat(e,"px")}).join(", ")}),H(e,"id",(0,E.NF)("recharts-line-")),H(e,"pathRef",function(t){e.mainCurve=t}),H(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),H(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return a.prototype=Object.create(e&&e.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),e&&Q(a,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,a=r.points,s=r.xAxis,n=r.yAxis,o=r.layout,i=r.children,l=(0,T.aS)(i,M.u);if(!l)return null;var d=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,q.kr)(e.payload,t)}};return h().createElement(P.W,{clipPath:e?"url(#clipPath-".concat(t,")"):null},l.map(function(e){return h().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:a,xAxis:s,yAxis:n,layout:o,dataPointFormatter:d})}))}},{key:"renderDots",value:function(e,t,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var s=this.props,n=s.dot,o=s.points,i=s.dataKey,l=(0,T.J9)(this.props,!1),d=(0,T.J9)(n,!0),c=o.map(function(e,t){var r=I(I(I({key:"dot-".concat(t),r:3},l),d),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:i,payload:e.payload,points:o});return a.renderDotItem(n,r)}),u={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return h().createElement(P.W,z({className:"recharts-line-dots",key:"dots"},u),c)}},{key:"renderCurveStatically",value:function(e,t,r,a){var s=this.props,n=s.type,o=s.layout,i=s.connectNulls,l=(s.ref,L(s,R)),d=I(I(I({},(0,T.J9)(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},a),{},{type:n,layout:o,connectNulls:i});return h().createElement(S.I,z({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,a=this.props,s=a.points,n=a.strokeDasharray,o=a.isAnimationActive,i=a.animationBegin,l=a.animationDuration,d=a.animationEasing,c=a.animationId,u=a.animateNewValues,p=a.width,m=a.height,f=this.state,y=f.prevPoints,x=f.totalLength;return h().createElement(g.Ay,{begin:i,duration:l,isActive:o,easing:d,from:{t:0},to:{t:1},key:"line-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(a){var o,i=a.t;if(y){var l=y.length/s.length,d=s.map(function(e,t){var r=Math.floor(t*l);if(y[r]){var a=y[r],s=(0,E.Dj)(a.x,e.x),n=(0,E.Dj)(a.y,e.y);return I(I({},e),{},{x:s(i),y:n(i)})}if(u){var o=(0,E.Dj)(2*p,e.x),d=(0,E.Dj)(m/2,e.y);return I(I({},e),{},{x:o(i),y:d(i)})}return I(I({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(d,e,t)}var c=(0,E.Dj)(0,x)(i);if(n){var f="".concat(n).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});o=r.getStrokeDasharray(c,x,f)}else o=r.generateSimpleStrokeDasharray(x,c);return r.renderCurveStatically(s,e,t,{strokeDasharray:o})})}},{key:"renderCurve",value:function(e,t){var r=this.props,a=r.points,s=r.isAnimationActive,n=this.state,o=n.prevPoints,i=n.totalLength;return s&&a&&a.length&&(!o&&i>0||!C()(o,a))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(a,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,a=t.dot,s=t.points,n=t.className,o=t.xAxis,i=t.yAxis,l=t.top,d=t.left,c=t.width,u=t.height,p=t.isAnimationActive,m=t.id;if(r||!s||!s.length)return null;var f=this.state.isAnimationFinished,y=1===s.length,x=(0,N.A)("recharts-line",n),g=o&&o.allowDataOverflow,v=i&&i.allowDataOverflow,b=g||v,j=A()(m)?this.id:m,w=null!=(e=(0,T.J9)(a,!1))?e:{r:3,strokeWidth:2},C=w.r,S=w.strokeWidth,k=((0,T.sT)(a)?a:{}).clipDot,M=void 0===k||k,E=2*(void 0===C?3:C)+(void 0===S?2:S);return h().createElement(P.W,{className:x},g||v?h().createElement("defs",null,h().createElement("clipPath",{id:"clipPath-".concat(j)},h().createElement("rect",{x:g?d:d-c/2,y:v?l:l-u/2,width:g?c:2*c,height:v?u:2*u})),!M&&h().createElement("clipPath",{id:"clipPath-dots-".concat(j)},h().createElement("rect",{x:d-E/2,y:l-E/2,width:c+E,height:u+E}))):null,!y&&this.renderCurve(b,j),this.renderErrorBar(b,j),(y||a)&&this.renderDots(b,M,j),(!p||f)&&D.Z.renderCallByParent(this.props,s))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(B(e),[0]):e,a=[],s=0;s<t;++s)a=[].concat(B(a),B(r));return a}},{key:"renderDotItem",value:function(e,t){var r;if(h().isValidElement(e))r=h().cloneElement(e,t);else if(b()(e))r=e(t);else{var a=t.key,s=L(t,_),n=(0,N.A)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=h().createElement(k.c,z({key:a},s,{className:n}))}return r}}],t&&U(a.prototype,t),r&&U(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(x.PureComponent);H($,"displayName","Line"),H($,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!O.m.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),H($,"getComposedData",function(e){var t=e.props,r=e.xAxis,a=e.yAxis,s=e.xAxisTicks,n=e.yAxisTicks,o=e.dataKey,i=e.bandSize,l=e.displayedData,d=e.offset,c=t.layout;return I({points:l.map(function(e,t){var l=(0,q.kr)(e,o);return"horizontal"===c?{x:(0,q.nb)({axis:r,ticks:s,bandSize:i,entry:e,index:t}),y:A()(l)?null:a.scale(l),value:l,payload:e}:{x:A()(l)?null:r.scale(l),y:(0,q.nb)({axis:a,ticks:n,bandSize:i,entry:e,index:t}),value:l,payload:e}}),layout:c},d)});var X=r(29772),J=(0,y.gu)({chartName:"LineChart",GraphicalChild:$,axisComponents:[{axisType:"xAxis",AxisComp:u.W},{axisType:"yAxis",AxisComp:p.h}],formatAxisMap:X.pr}),Y=r(41867),ee=r(51897),et=r(96249),er=r(69622),ea=r(77406),es=r(86857);function en(){let e=[{week:"Week 1",hours:4,score:85},{week:"Week 2",hours:6,score:88},{week:"Week 3",hours:3,score:82},{week:"Week 4",hours:5,score:90},{week:"Week 5",hours:4,score:87},{week:"Week 6",hours:2,score:78}];return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentProgressPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Progress"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track your learning journey and achievements"})]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-6",children:[(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Courses"}),(0,a.jsx)(Y.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"BookOpen","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:3}),(0,a.jsxs)("p",{className:"text-muted-foreground text-xs",children:[1," completed"]})]})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Study Hours"}),(0,a.jsx)(ee.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Clock","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:24}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Total hours"})]})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Average Score"}),(0,a.jsx)(et.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Target","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[82,"%"]}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Across all quizzes"})]})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Certificates"}),(0,a.jsx)(er.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"Award","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:1}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Earned"})]})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Streak"}),(0,a.jsx)(ea.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"TrendingUp","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:7}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Days active"})]})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Completion"}),(0,a.jsx)(es.A,{className:"text-muted-foreground h-4 w-4","data-sentry-element":"CheckCircle","data-sentry-source-file":"page.tsx"})]}),(0,a.jsxs)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[Math.round(33.33333333333333),"%"]}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:"Overall progress"})]})]})]}),(0,a.jsxs)(i.tU,{defaultValue:"courses",className:"space-y-6","data-sentry-element":"Tabs","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(i.j7,{"data-sentry-element":"TabsList","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(i.Xi,{value:"courses","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"Course Progress"}),(0,a.jsx)(i.Xi,{value:"analytics","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:"Analytics"})]}),(0,a.jsx)(i.av,{value:"courses","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)("div",{className:"space-y-6",children:[{id:1,name:"Introduction to Algebra",progress:85,modules:[{name:"Module 1",progress:100,score:92},{name:"Module 2",progress:100,score:88},{name:"Module 3",progress:100,score:95},{name:"Module 4",progress:100,score:85},{name:"Module 5",progress:100,score:90},{name:"Module 6",progress:100,score:87},{name:"Module 7",progress:100,score:93},{name:"Module 8",progress:40,score:null}],quizzes:[{name:"Module 1 Quiz",score:92,maxScore:100,passed:!0},{name:"Module 2 Quiz",score:88,maxScore:100,passed:!0},{name:"Module 3 Quiz",score:95,maxScore:100,passed:!0},{name:"Module 4 Quiz",score:85,maxScore:100,passed:!0},{name:"Module 5 Quiz",score:90,maxScore:100,passed:!0},{name:"Module 6 Quiz",score:87,maxScore:100,passed:!0},{name:"Module 7 Quiz",score:93,maxScore:100,passed:!0}]},{id:2,name:"Physics Fundamentals",progress:45,modules:[{name:"Module 1",progress:100,score:78},{name:"Module 2",progress:100,score:82},{name:"Module 3",progress:100,score:75},{name:"Module 4",progress:100,score:88},{name:"Module 5",progress:100,score:80},{name:"Module 6",progress:60,score:null},{name:"Module 7",progress:0,score:null},{name:"Module 8",progress:0,score:null}],quizzes:[{name:"Module 1 Quiz",score:78,maxScore:100,passed:!0},{name:"Module 2 Quiz",score:82,maxScore:100,passed:!0},{name:"Module 3 Quiz",score:75,maxScore:100,passed:!0},{name:"Module 4 Quiz",score:88,maxScore:100,passed:!0},{name:"Module 5 Quiz",score:80,maxScore:100,passed:!0}]},{id:3,name:"Chemistry Basics",progress:100,modules:[{name:"Module 1",progress:100,score:95},{name:"Module 2",progress:100,score:92},{name:"Module 3",progress:100,score:98},{name:"Module 4",progress:100,score:94},{name:"Module 5",progress:100,score:96},{name:"Module 6",progress:100,score:93}],quizzes:[{name:"Module 1 Quiz",score:95,maxScore:100,passed:!0},{name:"Module 2 Quiz",score:92,maxScore:100,passed:!0},{name:"Module 3 Quiz",score:98,maxScore:100,passed:!0},{name:"Module 4 Quiz",score:94,maxScore:100,passed:!0},{name:"Module 5 Quiz",score:96,maxScore:100,passed:!0},{name:"Module 6 Quiz",score:93,maxScore:100,passed:!0},{name:"Final Exam",score:95,maxScore:100,passed:!0}]}].map(e=>(0,a.jsxs)(s.Zp,{children:[(0,a.jsx)(s.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(s.ZB,{children:e.name}),(0,a.jsxs)(s.BT,{children:[e.modules.filter(e=>100===e.progress).length," ","of ",e.modules.length," modules completed"]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[e.progress,"%"]}),(0,a.jsx)(n.E,{variant:100===e.progress?"default":"secondary",children:100===e.progress?"Completed":"In Progress"})]})]})}),(0,a.jsx)(s.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(o.k,{value:e.progress,className:"h-3"}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-3 font-semibold",children:"Module Progress"}),(0,a.jsx)("div",{className:"space-y-2",children:e.modules.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.k,{value:e.progress,className:"h-2 w-16"}),(0,a.jsxs)("span",{className:"w-12 text-right",children:[e.progress,"%"]}),e.score&&(0,a.jsxs)(n.E,{variant:"outline",className:"text-xs",children:[e.score,"%"]})]})]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-3 font-semibold",children:"Quiz Results"}),(0,a.jsx)("div",{className:"space-y-2",children:e.quizzes.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{children:[e.score,"/",e.maxScore]}),(0,a.jsx)(n.E,{variant:e.passed?"default":"destructive",children:e.passed?"Passed":"Failed"})]})]},t))})]})]})]})})]},e.id))})}),(0,a.jsx)(i.av,{value:"analytics","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Weekly Study Hours"}),(0,a.jsx)(s.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your study time over the past 6 weeks"})]}),(0,a.jsx)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(l.u,{width:"100%",height:300,"data-sentry-element":"ResponsiveContainer","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(d.E,{data:e,"data-sentry-element":"BarChart","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(c.d,{strokeDasharray:"3 3","data-sentry-element":"CartesianGrid","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(u.W,{dataKey:"week","data-sentry-element":"XAxis","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(p.h,{"data-sentry-element":"YAxis","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(m.m,{"data-sentry-element":"Tooltip","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(f.y,{dataKey:"hours",fill:"#3b82f6","data-sentry-element":"Bar","data-sentry-source-file":"page.tsx"})]})})})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Score Trend"}),(0,a.jsx)(s.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Your average quiz scores over time"})]}),(0,a.jsx)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(l.u,{width:"100%",height:300,"data-sentry-element":"ResponsiveContainer","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(J,{data:e,"data-sentry-element":"LineChart","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(c.d,{strokeDasharray:"3 3","data-sentry-element":"CartesianGrid","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(u.W,{dataKey:"week","data-sentry-element":"XAxis","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(p.h,{domain:[70,100],"data-sentry-element":"YAxis","data-sentry-source-file":"page.tsx"}),(0,a.jsx)(m.m,{"data-sentry-element":"Tooltip","data-sentry-source-file":"page.tsx"}),(0,a.jsx)($,{type:"monotone",dataKey:"score",stroke:"#10b981",strokeWidth:2,dot:{fill:"#10b981"},"data-sentry-element":"Line","data-sentry-source-file":"page.tsx"})]})})})]})]})})]})]})}},94735:e=>{"use strict";e.exports=require("events")},96249:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},97890:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var s=r(63033),n=r(1472),o=r(7688),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx","default");let l={...s},d="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;a="function"==typeof i?new Proxy(i,{apply:(e,t,r)=>{let a,s,n;try{let e=d?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:n}).apply(t,r)}}):i;let c=void 0,u=void 0,p=void 0,m=a},98254:(e,t,r)=>{Promise.resolve().then(r.bind(r,97890))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,800,8134,8634],()=>r(11101));module.exports=a})();
//# sourceMappingURL=page.js.map