{"version": 3, "file": "9678.js", "mappings": "igBAKA,IAAMA,EAAWC,EAAAA,UAAgB,CAAiH,CAAC,WACjJC,CAAS,OACTC,CAAK,CACL,GAAGC,EACJ,CAAEC,IAAQ,UAACC,EAAAA,EAAsB,EAACD,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEL,GAAa,GAAGE,CAAK,UAC7I,UAACE,EAAAA,EAA2B,EAACJ,UAAU,iDAAiDM,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,KAAON,CAAAA,GAAS,EAAG,EAAE,CACjD,OAEFH,EAASU,WAAW,CAAGJ,EAAAA,EAAsB,CAACI,WAAW,gGCVzD,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACblB,CAAS,CACTY,SAAO,SACPO,GAAU,CAAK,CACf,GAAGjB,EAGJ,EACC,IAAMkB,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,QAAQtB,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACI,EAAc,SACzDG,CACF,GAAIZ,GAAa,GAAGE,CAAK,CAAEqB,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,iFCZO,IAAMC,EAAgC,CAAC,QAC5CC,CAAM,OACNC,CAAK,YACLC,CAAU,gBACVC,CAAc,gBACdC,CAAc,MACdC,EAAO,OAAO,UACdC,EAAW,EAAK,aAChBC,GAAc,CAAK,eACnBC,CAAa,WACbC,CAAS,CACV,IACC,IAAMC,EAAsB,UAATL,EAAmBF,IAAmBF,EAAQU,MAAMC,OAAO,CAACT,IAAmBA,EAAeU,QAAQ,CAACZ,GACpHa,EAA2B,UAATT,EAAmBG,IAAkBP,EAAQU,MAAMC,OAAO,CAACJ,IAAkBA,EAAcK,QAAQ,CAACZ,GA8B5H,MAAO,WAACc,QAAAA,CAAM1C,UAAW,CAAC;;QAEpB,EAAE2C,CAnBDT,EAEIG,EAAa,SAFJ,IAmBM,gBAjB6B,yDAIjDI,EAEK,eAFY,gBAGVJ,EAEF,UAFc,iBAKd;QAML,EAAEJ,EAAW,gCAAkC,GAAG;QAClD,EAAEC,GAAeO,EAAkB,wBAA0B,GAAG;QAChE,EAAEP,GAAeG,GAAc,CAACI,EAAkB,sBAAwB,GAAG;MAC/E,CAAC,CAAEjB,wBAAsB,SAASC,0BAAwB,uBAC1D,UAACmB,QAAAA,CAAMZ,KAAMA,EAAMa,KAAMhB,EAAY5B,MAAO2B,EAAOkB,QAAST,EAAYU,SApCvD,CAoCiEC,IAnCpF,IAAIf,EACJ,GAAa,KADC,IACQ,CAAlBD,EACFD,EAAeF,EAAYD,OACtB,CAEL,IAAMqB,EAAiBX,MAAMC,OAAO,CAACT,GAAkBA,EAAiB,EAAE,CAE1EC,EAAeF,EADIQ,EAAaY,EAAeC,MAAM,CAACC,GAAOA,IAAQvB,GAAS,IAAIqB,EAAgBrB,EAAM,CAE1G,CACF,EA0BoGK,SAAUA,EAAUjC,UAAW,CAAC;;UAE5H,EAAW,UAATgC,EAAmB,gBAAkB,wBAAwB;UAC/D,EAAEC,EAAW,qBAAuB,iBAAiB;QACvD,CAAC,GACH,WAACmB,OAAAA,CAAKpD,UAAW,CAAC,+BAA+B,EAAEiC,EAAW,gBAAkB,iBAAiB,WAC/F,WAACmB,OAAAA,CAAKpD,UAAU,6BACbqD,OAAOC,YAAY,CAAC,GAAK1B,GAAO,OAEhB,UAAlB,OAAOD,EAAsBA,EAASA,EAAO4B,OAAO,CAACC,GAAG,CAAC,CAACC,EAAOC,IAAe,WAAC3D,IAAAA,QAAc,YAC1E,SAAf0D,EAAMzB,IAAI,EAAe,UAACoB,OAAAA,UAAMK,EAAMxD,KAAK,GAC5B,UAAfwD,EAAMzB,IAAI,EAAgByB,EAAMxD,KAAK,EAAI,UAAC0D,MAAAA,CAAIC,IAAKH,EAAMxD,KAAK,CAAE4D,IAAK,CAAC,aAAa,EAAEH,EAAAA,CAAY,CAAE1D,UAAU,6CAC9F,UAAfyD,EAAMzB,IAAI,EAAgB,WAACoB,OAAAA,WAAK,WAASK,EAAMxD,KAAK,CAAC,OACtC,QAAfwD,EAAMzB,IAAI,EAAc,WAACoB,OAAAA,WAAK,SAAOK,EAAMxD,KAAK,CAAC,OAClC,mBAAfwD,EAAMzB,IAAI,EAAyB,WAACoB,OAAAA,WAAK,eAAaK,EAAMxD,KAAK,CAAC,SAL6ByD,OAUvGxB,GAAe,WAAC4B,MAAAA,CAAI9D,UAAU,mCAC1ByC,GAAmB,UAACW,OAAAA,CAAKpD,UAAU,kFAAyE,YAG5GqC,GAAc,CAACI,GAAmB,UAACW,OAAAA,CAAKpD,UAAU,8EAAqE,iBAKlI,EAAE,yBChFK,IAAM+D,EAAoC,CAAC,UAChDC,CAAQ,gBACRC,CAAc,gBACdC,CAAc,gBACdpC,CAAc,gBACdC,CAAc,aACdG,GAAc,CAAK,WACnBE,CAAS,UACTH,GAAW,CAAK,CACjB,IAoBC,IAAMkC,EAAwB,IACxB,GACFpC,EAAeiC,EAASI,EAAE,CAAEnE,EAEhC,EACA,MAAO,UAACoE,EAAAA,EAAIA,CAAAA,CAACrE,UAAW,CAAC;;MAErB,EAAEkC,EAAcE,EAAY,+BAAiC,2BAA6B,kBAAkB;IAC9G,CAAC,CAAEb,sBAAoB,OAAOC,wBAAsB,WAAWC,0BAAwB,wBACrF,WAAC6C,EAAAA,EAAWA,CAAAA,CAACtE,UAAU,MAAMuB,sBAAoB,cAAcE,0BAAwB,yBACrF,WAACqC,MAAAA,CAAI9D,UAAU,mDACb,WAAC8D,MAAAA,CAAI9D,UAAU,wCACb,WAACkB,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUZ,UAAU,2CAA2CuB,sBAAoB,QAAQE,0BAAwB,yBAAe,QACzIwC,EAAe,SAAOC,KAE9B,UAAChD,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,YAAYZ,UAAU,UAAUuB,sBAAoB,QAAQE,0BAAwB,wBAlChF,CAACO,IAC5B,OAAQA,GACN,IAAK,kBACL,IAAK,kBACH,MAAO,eACT,KAAK,aACL,IAAK,aACH,MAAO,aACT,KAAK,QACH,MAAO,MACT,SACE,OAAOA,CACX,EACF,EAsBkCgC,EAAShC,IAAI,OAGtCE,GAAe,UAAChB,EAAAA,CAAKA,CAAAA,CAACN,QAASwB,EAAY,UAAY,cAAepC,UAAWoC,EAAY,kCAAoC,YAC7HA,EAAY,QAAU,aAI7B,UAAC0B,MAAAA,CAAI9D,UAAU,gBACb,UAACuE,IAAAA,CAAEvE,UAAU,qEACmB,UAA7B,OAAOgE,EAASA,QAAQ,CAAgBA,EAASA,QAAQ,CAAG1B,MAAMC,OAAO,CAACyB,EAASA,QAAQ,EAAIA,EAASA,QAAQ,CAACR,GAAG,CAAC,CAACC,EAAO7B,IAAU,WAAC7B,IAAAA,QAAc,YACjI,SAAf0D,EAAMzB,IAAI,EAAe,UAACoB,OAAAA,UAAMK,EAAMxD,KAAK,GAC5B,UAAfwD,EAAMzB,IAAI,EAAgByB,EAAMxD,KAAK,EAAI,UAAC0D,MAAAA,CAAIC,IAAKH,EAAMxD,KAAK,CAAE4D,IAAK,CAAC,eAAe,EAAEjC,EAAAA,CAAO,CAAE5B,UAAU,gDAF4C4B,IAGpI,UAACwB,OAAAA,UAAMC,OAAOW,EAASA,QAAQ,QAK1DA,CAAkB,oBAAlBA,EAAShC,IAAI,EAA4C,oBAAlBgC,EAAShC,IAAI,CAAqB,EAAMgC,EAASQ,OAAO,EAAI,UAACV,MAAAA,CAAI9D,UAAU,qBAC/GgE,EAASQ,OAAO,CAAChB,GAAG,CAAC,CAAC7B,EAAQC,IAAU,UAACF,EAAMA,CAAaC,GAAbD,IAAqBC,EAAQC,MAAOA,EAAOC,WAAYmC,EAASI,EAAE,CAAEtC,eAAgBA,EAAgBC,eAAgBA,EAAgBC,KAAK,QAAQC,SAAUA,EAAUC,YAAaA,EAAaC,cAAe6B,EAAS7B,aAAa,CAAEC,UAAWA,GAA3OR,MAIxDoC,CAAkB,eAAlBA,EAAShC,IAAI,EAAuC,eAAlBgC,EAAShC,IAAI,CAAgB,EAAM,UAAC8B,MAAAA,CAAI9D,UAAU,qBACjF,CAAC,OAAQ,QAAQ,CAACwD,GAAG,CAAC,CAACvD,EAAO2B,KACjC,IAAMS,EAAaP,IAAmB7B,EAChCwC,EAAkBuB,EAAS7B,aAAa,GAAKlC,EAanD,MAAO,WAACyC,QAAAA,CAAkB1C,UAAW,CAAC;;oBAE5B,EAbJ,CAACkC,EACIG,EAAa,SADJ,IAaU,gBAZyB,yDAEjDI,EACK,eADY,gBAEVJ,EACF,UADc,iBAGd;oBAMD,EAAEJ,EAAW,gCAAkC,GAAG;oBAClD,EAAEC,GAAeO,EAAkB,wBAA0B,GAAG;oBAChE,EAAEP,GAAeG,GAAc,CAACI,EAAkB,sBAAwB,GAAG;kBAC/E,CAAC,WACD,WAACqB,MAAAA,CAAI9D,UAAU,wCACb,UAAC4C,QAAAA,CAAMZ,KAAK,QAAQa,KAAMmB,EAASI,EAAE,CAAEnE,MAAOA,EAAO6C,QAAST,EAAYU,SAAU,IAAMoB,EAAsBlE,GAAQgC,SAAUA,EAAUjC,UAAU,0BACtJ,WAACoD,OAAAA,CAAKpD,UAAW,CAAC,YAAY,EAAEiC,EAAW,gBAAkB,iBAAiB,WAC3EoB,OAAOC,YAAY,CAAC,GAAK1B,GAAO,KAAa,SAAV3B,EAAmB,QAAU,cAKpEiC,GAAe,WAAC4B,MAAAA,CAAI9D,UAAU,8BAC1ByC,GAAmB,UAACW,OAAAA,CAAKpD,UAAU,kFAAyE,YAG5GqC,GAAc,CAACI,GAAmB,UAACW,OAAAA,CAAKpD,UAAU,8EAAqE,iBAnBjHC,EAwBrB,KAImB,UAAlB+D,EAAShC,IAAI,EAAgB,UAACyC,WAAAA,CAASzE,UAAW,CAAC;;cAE9C,EAAEiC,EAAW,gCAAkC,GAAG;YACpD,CAAC,CAAEyC,KAAM,EAAGC,YAAY,gCAAgC1E,MAAO6B,GAA4B,GAAIiB,SA3F/E,CA2FyF6B,GA1F7G,GACF7C,EAAeiC,EAASI,EAAE,CAAES,EAAEC,MAAM,CAAC7E,KAAK,CAE9C,EAuFsIgC,SAAUA,IAGzIC,GAAe8B,EAASe,WAAW,EAAI,WAACjB,MAAAA,CAAI9D,UAAU,sEACnD,UAACgF,KAAAA,CAAGhF,UAAU,4CAAmC,gBACjD,UAACuE,IAAAA,CAAEvE,UAAU,iDACsB,UAAhC,OAAOgE,EAASe,WAAW,CAAgBf,EAASe,WAAW,CAAGzC,MAAMC,OAAO,CAACyB,EAASe,WAAW,EAAIf,EAASe,WAAW,CAACvB,GAAG,CAAC,CAACC,EAAO7B,IAAU,WAAC7B,IAAAA,QAAc,YAC7I,SAAf0D,EAAMzB,IAAI,EAAe,UAACoB,OAAAA,UAAMK,EAAMxD,KAAK,GAC5B,UAAfwD,EAAMzB,IAAI,EAAgByB,EAAMxD,KAAK,EAAI,UAAC0D,MAAAA,CAAIC,IAAKH,EAAMxD,KAAK,CAAE4D,IAAK,CAAC,kBAAkB,EAAEjC,EAAAA,CAAO,CAAE5B,UAAU,8CAC9F,UAAfyD,EAAMzB,IAAI,EAAgB,WAACoB,OAAAA,WAAK,WAASK,EAAMxD,KAAK,CAAC,OACtC,QAAfwD,EAAMzB,IAAI,EAAc,WAACoB,OAAAA,WAAK,SAAOK,EAAMxD,KAAK,CAAC,OAClC,mBAAfwD,EAAMzB,IAAI,EAAyB,WAACoB,OAAAA,WAAK,eAAaK,EAAMxD,KAAK,CAAC,SALgG2B,IAMhJ,UAACwB,OAAAA,UAAMC,OAAOW,EAASe,WAAW,aAKzE,EAAE,eC9HK,IAAME,EAA4C,CAAC,WACxDC,CAAS,iBACTC,CAAe,mBACfC,CAAiB,CACjBC,kBAAgB,kBAChBC,EAAmB,IAAIC,GAAK,cAC5BC,CAAY,WACZC,GAAY,CAAI,UAChBC,CAAQ,WACRC,GAAY,CAAK,cACjBC,GAAe,CAAK,YACpBC,GAAa,CAAK,SAClBC,EAAU,CAAC,CAAC,CACb,IACC,IAAMC,EAAoB,IACxB,GAAIF,EAAY,CAEd,IAAMzD,EAAY0D,CAAO,CADRZ,CAAS,CAACtD,EAAM,CACEwC,EAAE,CAAC,QAClCxC,IAAUuD,EACL/C,EAAY,aADU,KACU,oBAElCA,EAAY,UAAY,WACjC,QACA,IAAc+C,EAAwB,UAClCC,EAAkBY,GADS,CACLpE,GAAe,KAAP,MAC3B,YACT,EACMqE,EAAiB,IACrB,OAAQC,GACN,IAAK,UACH,MAAO,wCACT,KAAK,kBACH,MAAO,gEACT,KAAK,oBACH,MAAO,0DACT,KAAK,UAIL,IAAK,WAHH,MAAO,iEACT,KAAK,YACH,MAAO,yDAGT,KAAK,aACH,MAAO,4DACT,SACE,MAAO,0CACX,CACF,EACMC,EAAgBf,EAAkBgB,IAAI,CACtCC,EAAkBnB,EAAUoB,MAAM,CAAGH,EAGrCI,EAAeV,EAAaX,EAAUhC,MAAM,CAACsD,GAAKV,CAAO,CAACU,EAAEpC,EAAE,CAAC,EAAEkC,MAAM,CAAG,EAC1EG,EAAiBZ,EAAaX,EAAUhC,MAAM,CAACsD,GAAK,CAACV,CAAO,CAACU,EAAEpC,EAAE,CAAC,EAAEkC,MAAM,CAAG,EACnF,MAAO,WAACjC,EAAAA,EAAIA,CAAAA,CAACrE,UAAU,qBAAqBuB,sBAAoB,OAAOC,wBAAsB,eAAeC,0BAAwB,8BAChI,WAACiF,EAAAA,EAAUA,CAAAA,CAAC1G,UAAU,OAAOuB,sBAAoB,aAAaE,0BAAwB,8BACpF,UAACkF,EAAAA,EAASA,CAAAA,CAAC3G,UAAU,UAAUuB,sBAAoB,YAAYE,0BAAwB,6BAAqBoE,EAAa,cAAgB,eACxIA,EAAa,WAAC/B,MAAAA,CAAI9D,UAAU,2CACzB,WAAC8D,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,yDACf,WAACoD,OAAAA,WAAK,UAAQmD,QAEhB,WAACzC,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,qDACf,WAACoD,OAAAA,WAAK,UAAQqD,WAET,WAAC3C,MAAAA,CAAI9D,UAAU,2CACtB,WAAC8D,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,yDACf,WAACoD,OAAAA,WAAK,aAAW+C,QAEnB,WAACrC,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,sDACf,WAACoD,OAAAA,WAAK,UAAQiD,WAGnBZ,GAAaH,EAAiBc,IAAI,CAAG,GAAK,WAACtC,MAAAA,CAAI9D,UAAU,gDACtD,UAAC8D,MAAAA,CAAI9D,UAAU,2EACb,UAAC8D,MAAAA,CAAI9D,UAAU,kEAEjB,WAACoD,OAAAA,WAAK,aAAWkC,EAAiBc,IAAI,UAI5C,WAAC9B,EAAAA,EAAWA,CAAAA,CAACtE,UAAU,YAAYuB,sBAAoB,cAAcE,0BAAwB,8BAE3F,UAACqC,MAAAA,CAAI9D,UAAU,kCACZkF,EAAU1B,GAAG,CAAC,CAACoD,EAAGhF,KACnB,IAAMsE,EAASH,EAAkBnE,GAC3BiF,EAAYvB,EAAiBU,GAAG,CAACpE,GACvC,MAAO,WAACkF,EAAAA,CAAMA,CAAAA,CAAalG,QAAQ,UAAUwF,KAAK,KAAKpG,UAAW,CAAC;;kBAE3D,EAAEiG,EAAeC,QAAQ;kBACzB,EAAEW,EAAY,yBAA2B,GAAG;gBAC9C,CAAC,CAAEE,QAAS,IAAM1B,EAAiBzD,aAClCA,EAAQ,EACRiF,GAAa,UAAC/C,MAAAA,CAAI9D,UAAU,wFANf4B,EAQtB,KAIA,WAACkC,MAAAA,CAAI9D,UAAU,0DACZ6F,EAAa,iCACV,WAAC/B,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,iCACf,UAACoD,OAAAA,UAAK,6BAER,WAACU,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,+BACf,UAACoD,OAAAA,UAAK,6BAER,WAACU,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,yDACf,UAACoD,OAAAA,UAAK,qBAER,WAACU,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,qDACf,UAACoD,OAAAA,UAAK,wBAEJ,iCACJ,WAACU,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,gCACf,UAACoD,OAAAA,UAAK,qBAER,WAACU,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,yDACf,UAACoD,OAAAA,UAAK,qBAER,WAACU,MAAAA,CAAI9D,UAAU,wCACb,UAAC8D,MAAAA,CAAI9D,UAAU,sDACf,UAACoD,OAAAA,UAAK,wBAGXqC,GAAa,WAAC3B,MAAAA,CAAI9D,UAAU,wCACzB,UAAC8D,MAAAA,CAAI9D,UAAU,2EACb,UAAC8D,MAAAA,CAAI9D,UAAU,0EAEjB,UAACoD,OAAAA,UAAK,gCAKXqC,GAAaD,GAAgB,UAAC1B,MAAAA,CAAI9D,UAAU,yBACzC,UAAC8G,EAAAA,CAAMA,CAAAA,CAAClG,QAAQ,UAAUwF,KAAK,KAAKpG,UAAU,iBAAiB+G,QAAS,IAAMvB,EAAaL,YACxFG,EAAiBU,GAAG,CAACb,GAAmB,cAAgB,kBAK9DO,GAAY,UAAC5B,MAAAA,CAAI9D,UAAU,yBACxB,UAAC8G,EAAAA,CAAMA,CAAAA,CAACC,QAASrB,EAAUzD,SAAU,CAAC0D,GAAaC,EAAc5F,UAAU,kDAAkDoG,KAAK,cAC/HR,EAAe,iBAAmB,wBAKjD,EAAE", "sources": ["webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/./src/components/ui/badge.tsx", "webpack://terang-lms-ui/./src/components/lms/final-exam/option.tsx", "webpack://terang-lms-ui/./src/components/lms/final-exam/question.tsx", "webpack://terang-lms-ui/./src/components/lms/final-exam/question-bank.tsx", "webpack://terang-lms-ui/./src/components/lms/final-exam/index.ts"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "import React from 'react';\nimport { ContentBlock } from '@/components/dynamic-content-editor';\ninterface OptionProps {\n  option: string | {\n    content: ContentBlock[];\n    isCorrect: boolean;\n  };\n  index: number;\n  questionId: string;\n  selectedAnswer: number | string | number[] | undefined;\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\n  type: 'radio' | 'checkbox';\n  disabled?: boolean;\n  showResults?: boolean;\n  correctAnswer?: number | string | number[];\n  isCorrect?: boolean;\n}\nexport const Option: React.FC<OptionProps> = ({\n  option,\n  index,\n  questionId,\n  selectedAnswer,\n  onAnswerChange,\n  type = 'radio',\n  disabled = false,\n  showResults = false,\n  correctAnswer,\n  isCorrect\n}) => {\n  const isSelected = type === 'radio' ? selectedAnswer === index : Array.isArray(selectedAnswer) && selectedAnswer.includes(index);\n  const isCorrectOption = type === 'radio' ? correctAnswer === index : Array.isArray(correctAnswer) && correctAnswer.includes(index);\n  const handleChange = () => {\n    if (disabled) return;\n    if (type === 'radio') {\n      onAnswerChange(questionId, index);\n    } else {\n      // Handle checkbox logic for multiple selection\n      const currentAnswers = Array.isArray(selectedAnswer) ? selectedAnswer : [];\n      const newAnswers = isSelected ? currentAnswers.filter(ans => ans !== index) : [...currentAnswers, index];\n      onAnswerChange(questionId, newAnswers);\n    }\n  };\n  const getOptionStyles = () => {\n    if (!showResults) {\n      // Normal mode - just show selected state\n      return isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\n    }\n\n    // Results mode - show correct/incorrect highlighting\n    if (isCorrectOption) {\n      // This is the correct answer - always highlight green\n      return 'border-green-500 bg-green-50';\n    } else if (isSelected) {\n      // User selected this wrong answer - highlight red\n      return 'border-red-500 bg-red-50';\n    } else {\n      // Not selected, not correct - neutral\n      return 'border-gray-200 bg-gray-50';\n    }\n  };\n  return <label className={`\n        flex cursor-pointer items-start space-x-3 p-3 rounded-lg border-2 transition-all\n        ${getOptionStyles()}\n        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\n        ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\n        ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\n      `} data-sentry-component=\"Option\" data-sentry-source-file=\"option.tsx\">\r\n      <input type={type} name={questionId} value={index} checked={isSelected} onChange={handleChange} disabled={disabled} className={`\n          mt-0.5 h-4 w-4 shrink-0\n          ${type === 'radio' ? 'text-blue-600' : 'text-blue-600 rounded'}\n          ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}\n        `} />\r\n      <span className={`text-sm leading-relaxed flex-1 ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n        <span className=\"font-medium mr-2\">\r\n          {String.fromCharCode(65 + index)}.\r\n        </span>\r\n        {typeof option === 'string' ? option : option.content.map((block, blockIndex) => <React.Fragment key={blockIndex}>\r\n              {block.type === 'text' && <span>{block.value}</span>}\r\n              {block.type === 'image' && block.value && <img src={block.value} alt={`Option image ${blockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />}\r\n              {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n              {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n              {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n            </React.Fragment>)}\r\n      </span>\r\n      \r\n      {/* Show correct/incorrect indicators in results mode */}\r\n      {showResults && <div className=\"flex items-center ml-2\">\r\n          {isCorrectOption && <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n              ✓ Benar\r\n            </span>}\r\n          {isSelected && !isCorrectOption && <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n              ✗ Salah\r\n            </span>}\r\n        </div>}\r\n    </label>;\n};", "import React from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Option } from './option';\nimport { Question as QuestionType } from '@/types/lms';\ninterface QuestionProps {\n  question: QuestionType;\n  questionNumber: number;\n  totalQuestions: number;\n  selectedAnswer: number | string | number[] | undefined;\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\n  showResults?: boolean;\n  isCorrect?: boolean;\n  disabled?: boolean;\n}\nexport const Question: React.FC<QuestionProps> = ({\n  question,\n  questionNumber,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerChange,\n  showResults = false,\n  isCorrect,\n  disabled = false\n}) => {\n  const getQuestionTypeLabel = (type: string) => {\n    switch (type) {\n      case 'multiple-choice':\n      case 'multiple_choice':\n        return 'Pilihan Ganda';\n      case 'true-false':\n      case 'true_false':\n        return 'Benar/Salah';\n      case 'essay':\n        return 'Esai';\n      default:\n        return type;\n    }\n  };\n  const handleEssayChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    if (!disabled) {\n      onAnswerChange(question.id, e.target.value);\n    }\n  };\n  const handleTrueFalseChange = (value: string) => {\n    if (!disabled) {\n      onAnswerChange(question.id, value);\n    }\n  };\n  return <Card className={`\n      border-2 transition-all\n      ${showResults ? isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50' : 'border-gray-200'}\n    `} data-sentry-element=\"Card\" data-sentry-component=\"Question\" data-sentry-source-file=\"question.tsx\">\r\n      <CardContent className=\"p-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"question.tsx\">\r\n        <div className=\"mb-4 flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200\" data-sentry-element=\"Badge\" data-sentry-source-file=\"question.tsx\">\r\n              Soal {questionNumber} dari {totalQuestions}\r\n            </Badge>\r\n            <Badge variant=\"secondary\" className=\"text-xs\" data-sentry-element=\"Badge\" data-sentry-source-file=\"question.tsx\">\r\n              {getQuestionTypeLabel(question.type)}\r\n            </Badge>\r\n          </div>\r\n          {showResults && <Badge variant={isCorrect ? 'default' : 'destructive'} className={isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}>\r\n              {isCorrect ? 'Benar' : 'Salah'}\r\n            </Badge>}\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-lg leading-relaxed text-gray-900 whitespace-pre-wrap\">\r\n            {typeof question.question === 'string' ? question.question : Array.isArray(question.question) ? question.question.map((block, index) => <React.Fragment key={index}>\r\n                  {block.type === 'text' && <span>{block.value}</span>}\r\n                  {block.type === 'image' && block.value && <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />}\r\n                </React.Fragment>) : <span>{String(question.question)}</span>}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Multiple Choice Options */}\r\n        {(question.type === 'multiple-choice' || question.type === 'multiple_choice') && question.options && <div className=\"space-y-3\">\r\n            {question.options.map((option, index) => <Option key={index} option={option} index={index} questionId={question.id} selectedAnswer={selectedAnswer} onAnswerChange={onAnswerChange} type=\"radio\" disabled={disabled} showResults={showResults} correctAnswer={question.correctAnswer} isCorrect={isCorrect} />)}\r\n          </div>}\r\n\r\n        {/* True/False Options */}\r\n        {(question.type === 'true-false' || question.type === 'true_false') && <div className=\"space-y-3\">\r\n            {['true', 'false'].map((value, index) => {\n          const isSelected = selectedAnswer === value;\n          const isCorrectOption = question.correctAnswer === value;\n          const getOptionStyles = () => {\n            if (!showResults) {\n              return isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\n            }\n            if (isCorrectOption) {\n              return 'border-green-500 bg-green-50';\n            } else if (isSelected) {\n              return 'border-red-500 bg-red-50';\n            } else {\n              return 'border-gray-200 bg-gray-50';\n            }\n          };\n          return <label key={value} className={`\n                    flex cursor-pointer items-center justify-between p-3 rounded-lg border-2 transition-all\n                    ${getOptionStyles()}\n                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\n                    ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\n                    ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\n                  `}>\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <input type=\"radio\" name={question.id} value={value} checked={isSelected} onChange={() => handleTrueFalseChange(value)} disabled={disabled} className=\"h-4 w-4 text-blue-600\" />\r\n                    <span className={`font-medium ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n                      {String.fromCharCode(65 + index)}. {value === 'true' ? 'Benar' : 'Salah'}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {/* Show correct/incorrect indicators in results mode */}\r\n                  {showResults && <div className=\"flex items-center\">\r\n                      {isCorrectOption && <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n                          ✓ Benar\r\n                        </span>}\r\n                      {isSelected && !isCorrectOption && <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n                          ✗ Salah\r\n                        </span>}\r\n                    </div>}\r\n                </label>;\n        })}\r\n          </div>}\r\n\r\n        {/* Essay Question */}\r\n        {question.type === 'essay' && <textarea className={`\n              w-full resize-none rounded-lg border-2 p-4 focus:border-transparent focus:ring-2 focus:ring-blue-500\n              ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}\n            `} rows={8} placeholder=\"Ketik jawaban Anda di sini...\" value={selectedAnswer as string || ''} onChange={handleEssayChange} disabled={disabled} />}\r\n\r\n        {/* Show explanation in results */}\r\n        {showResults && question.explanation && <div className=\"mt-4 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\">\r\n            <h4 className=\"font-semibold text-gray-900 mb-2\">Penjelasan:</h4>\r\n            <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n              {typeof question.explanation === 'string' ? question.explanation : Array.isArray(question.explanation) ? question.explanation.map((block, index) => <React.Fragment key={index}>\r\n                    {block.type === 'text' && <span>{block.value}</span>}\r\n                    {block.type === 'image' && block.value && <img src={block.value} alt={`Explanation image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />}\r\n                    {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                  </React.Fragment>) : <span>{String(question.explanation)}</span>}\r\n            </p>\r\n          </div>}\r\n      </CardContent>\r\n    </Card>;\n};", "import React from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Question } from '@/types/lms';\ninterface QuestionBankProps {\n  questions: Question[];\n  currentQuestion: number;\n  answeredQuestions: Set<number>;\n  onQuestionSelect: (questionIndex: number) => void;\n  flaggedQuestions?: Set<number>;\n  onToggleFlag?: (questionIndex: number) => void;\n  showFlags?: boolean;\n  onSubmit?: () => void;\n  canSubmit?: boolean;\n  isSubmitting?: boolean;\n  reviewMode?: boolean;\n  results?: {\n    [key: string]: boolean;\n  };\n}\nexport const QuestionBank: React.FC<QuestionBankProps> = ({\n  questions,\n  currentQuestion,\n  answeredQuestions,\n  onQuestionSelect,\n  flaggedQuestions = new Set(),\n  onToggleFlag,\n  showFlags = true,\n  onSubmit,\n  canSubmit = false,\n  isSubmitting = false,\n  reviewMode = false,\n  results = {}\n}) => {\n  const getQuestionStatus = (index: number) => {\n    if (reviewMode) {\n      const question = questions[index];\n      const isCorrect = results[question.id];\n      if (index === currentQuestion) {\n        return isCorrect ? 'current-correct' : 'current-incorrect';\n      }\n      return isCorrect ? 'correct' : 'incorrect';\n    }\n    if (index === currentQuestion) return 'current';\n    if (answeredQuestions.has(index)) return 'answered';\n    return 'unanswered';\n  };\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'current':\n        return 'bg-blue-600 text-white border-blue-600';\n      case 'current-correct':\n        return 'bg-green-600 text-white border-green-600 ring-2 ring-green-200';\n      case 'current-incorrect':\n        return 'bg-red-600 text-white border-red-600 ring-2 ring-red-200';\n      case 'correct':\n        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';\n      case 'incorrect':\n        return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200';\n      case 'answered':\n        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';\n      case 'unanswered':\n        return 'bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100';\n      default:\n        return 'bg-gray-50 text-gray-600 border-gray-300';\n    }\n  };\n  const answeredCount = answeredQuestions.size;\n  const unansweredCount = questions.length - answeredCount;\n\n  // Count correct/incorrect in review mode\n  const correctCount = reviewMode ? questions.filter(q => results[q.id]).length : 0;\n  const incorrectCount = reviewMode ? questions.filter(q => !results[q.id]).length : 0;\n  return <Card className=\"h-fit sticky top-4\" data-sentry-element=\"Card\" data-sentry-component=\"QuestionBank\" data-sentry-source-file=\"question-bank.tsx\">\r\n      <CardHeader className=\"pb-4\" data-sentry-element=\"CardHeader\" data-sentry-source-file=\"question-bank.tsx\">\r\n        <CardTitle className=\"text-lg\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"question-bank.tsx\">{reviewMode ? 'Review Soal' : 'Nomor Soal'}</CardTitle>\r\n        {reviewMode ? <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-green-100 border border-green-300 rounded\"></div>\r\n              <span>Benar: {correctCount}</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-red-100 border border-red-300 rounded\"></div>\r\n              <span>Salah: {incorrectCount}</span>\r\n            </div>\r\n          </div> : <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-green-100 border border-green-300 rounded\"></div>\r\n              <span>Terjawab: {answeredCount}</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-gray-50 border border-gray-300 rounded\"></div>\r\n              <span>Belum: {unansweredCount}</span>\r\n            </div>\r\n          </div>}\r\n        {showFlags && flaggedQuestions.size > 0 && <div className=\"flex items-center space-x-2 text-sm\">\r\n            <div className=\"w-4 h-4 bg-yellow-100 border border-yellow-400 rounded relative\">\r\n              <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full\"></div>\r\n            </div>\r\n            <span>Ditandai: {flaggedQuestions.size}</span>\r\n          </div>}\r\n      </CardHeader>\r\n      \r\n      <CardContent className=\"space-y-4\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"question-bank.tsx\">\r\n        {/* Question Grid */}\r\n        <div className=\"grid grid-cols-4 gap-2\">\r\n          {questions.map((_, index) => {\n          const status = getQuestionStatus(index);\n          const isFlagged = flaggedQuestions.has(index);\n          return <Button key={index} variant=\"outline\" size=\"sm\" className={`\n                  relative h-12 w-12 p-0 font-medium transition-all\n                  ${getStatusColor(status)}\n                  ${isFlagged ? 'ring-2 ring-yellow-400' : ''}\n                `} onClick={() => onQuestionSelect(index)}>\r\n                {index + 1}\r\n                {isFlagged && <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white\"></div>}\r\n              </Button>;\n        })}\r\n        </div>\r\n\r\n        {/* Legend */}\r\n        <div className=\"pt-4 border-t space-y-2 text-xs text-gray-600\">\r\n          {reviewMode ? <>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-600 rounded\"></div>\r\n                <span>Soal saat ini (Benar)</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-red-600 rounded\"></div>\r\n                <span>Soal saat ini (Salah)</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-100 border border-green-300 rounded\"></div>\r\n                <span>Jawaban benar</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-red-100 border border-red-300 rounded\"></div>\r\n                <span>Jawaban salah</span>\r\n              </div>\r\n            </> : <>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-blue-600 rounded\"></div>\r\n                <span>Soal saat ini</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-100 border border-green-300 rounded\"></div>\r\n                <span>Sudah dijawab</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-gray-50 border border-gray-300 rounded\"></div>\r\n                <span>Belum dijawab</span>\r\n              </div>\r\n            </>}\r\n          {showFlags && <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-3 h-3 bg-yellow-100 border border-yellow-400 rounded relative\">\r\n                <div className=\"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-yellow-500 rounded-full\"></div>\r\n              </div>\r\n              <span>Ditandai untuk review</span>\r\n            </div>}\r\n        </div>\r\n\r\n        {/* Quick Actions */}\r\n        {showFlags && onToggleFlag && <div className=\"pt-4 border-t\">\r\n            <Button variant=\"outline\" size=\"sm\" className=\"w-full text-xs\" onClick={() => onToggleFlag(currentQuestion)}>\r\n              {flaggedQuestions.has(currentQuestion) ? 'Hapus Tanda' : 'Tandai Soal'}\r\n            </Button>\r\n          </div>}\r\n\r\n        {/* Submit Button */}\r\n        {onSubmit && <div className=\"pt-4 border-t\">\r\n            <Button onClick={onSubmit} disabled={!canSubmit || isSubmitting} className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\" size=\"lg\">\r\n              {isSubmitting ? 'Menyerahkan...' : 'Submit Ujian'}\r\n            </Button>\r\n          </div>}\r\n      </CardContent>\r\n    </Card>;\n};", "export { Option } from './option';\r\nexport { Question } from './question';\r\nexport { QuestionBank } from './question-bank';"], "names": ["Progress", "React", "className", "value", "props", "ref", "ProgressPrimitive", "cn", "style", "transform", "displayName", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "data-slot", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "Option", "option", "index", "questionId", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "type", "disabled", "showResults", "<PERSON><PERSON><PERSON><PERSON>", "isCorrect", "isSelected", "Array", "isArray", "includes", "isCorrectOption", "label", "getOptionStyles", "input", "name", "checked", "onChange", "handleChange", "currentAnswers", "filter", "ans", "span", "String", "fromCharCode", "content", "map", "block", "blockIndex", "img", "src", "alt", "div", "Question", "question", "questionNumber", "totalQuestions", "handleTrueFalseChange", "id", "Card", "<PERSON><PERSON><PERSON><PERSON>", "p", "options", "textarea", "rows", "placeholder", "handleEssayChange", "e", "target", "explanation", "h4", "QuestionBank", "questions", "currentQuestion", "answeredQuestions", "onQuestionSelect", "flaggedQuestions", "Set", "onToggleFlag", "showFlags", "onSubmit", "canSubmit", "isSubmitting", "reviewMode", "results", "getQuestionStatus", "has", "getStatusColor", "status", "answeredCount", "size", "unansweredCount", "length", "correctCount", "q", "incorrectCount", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "_", "isFlagged", "<PERSON><PERSON>", "onClick"], "sourceRoot": ""}