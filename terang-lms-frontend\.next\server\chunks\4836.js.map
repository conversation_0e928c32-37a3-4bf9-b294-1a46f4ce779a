{"version": 3, "file": "4836.js", "mappings": "ukBAmOgBA,6BAA6B,mBAA7BA,GA1MAC,0BAA0B,mBAA1BA,aAjBT,WAE8B,WACO,WACV,WACG,WACqB,WAC5B,OAUvB,SAASA,EACdC,CAAmB,CACnBC,CAA2B,CAC3BC,CAA2C,CAC3CC,CAAQ,CACRC,CAAgB,EAEhB,IAGIC,EAHAC,EAAcL,EAAMM,IAAI,CACxBC,EAAeP,EAAMQ,KAAK,CACxBC,EAAOC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBR,GAG/B,GAA0B,UAAtB,OAAOD,EACT,OAAO,EAGT,IAAK,IAAMU,KAAwBV,EAAY,CAE7C,GAAI,CAACW,SAwFAA,EAA8BC,CAAkC,EACvE,GAAI,CAACA,EAAU,OAAO,EAEtB,IAAMC,EAAiBD,CAAQ,CAAC,EAAE,CAGlC,GAFgBA,CAAQ,CAAC,EAAE,CAGzB,KADW,EACJ,EAGT,IAAK,IAAME,KAAOD,EAChB,GAAIF,EAA8BE,CAAc,CAACC,EAAI,EACnD,CADsD,CADxB,KAEvB,EAIX,OAAO,CACT,EAzGuCJ,EAAqBE,QAAQ,EAC9D,CADiE,QAInE,IAAIG,EAAYL,EAAqBL,IAAI,CAIzCU,EAAYnB,EACVmB,EACAC,OAAOC,WAAW,CAAChB,EAAIiB,YAAY,GAGrC,GAAM,UAAEN,CAAQ,CAAEO,cAAY,eAAEC,CAAa,CAAE,CAAGV,EAE5CW,EAAoC,CAAC,MAAOD,EAAc,CAKhEL,EAAYnB,EACVmB,EACAC,OAAOC,WAAW,CAAChB,EAAIiB,YAAY,GAGrC,IAAII,EAAUC,CAAAA,EAAAA,EAAAA,2BAAAA,EACZF,EACAjB,EACAW,EACAP,GAGIgB,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,IAIjB,GAAIN,GAAgBP,EAAU,CAE5B,IAAMc,EAAMd,CAAQ,CAAC,EAAE,CAEvBY,EAASG,OAAO,CADAf,CAAQ,CACLe,EADQ,CAE3BH,EAASE,GAAG,CAAGA,EAkErB,SAASE,EACP9B,CAAmB,CACnB0B,CAAmB,CACnBK,CAAwB,CACxBC,CAA8B,CAC9BC,CAA2C,EAG3C,GADsBf,CAClBgB,UADyBC,IAAI,CAACH,CAAW,CAAC,EAAE,EAAEI,MAAM,CAKxD,IAAK,IAAMpB,KAAOgB,CAAW,CAAC,EAAE,CAAE,CAChC,IASIK,EATEC,EAAqBN,CAAW,CAAC,EAAE,CAAChB,EAAI,CACxCuB,EAA0BD,CAAkB,CAAC,EAAE,CAC/CE,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBF,GAEhCG,EACkB,OAAtBT,QAA4DU,IAA9BV,CAAiB,CAAC,EAAE,CAACjB,EAAI,CACnDiB,CAAiB,CAAC,EAAE,CAACjB,EAAI,CACzB,KAGN,GAAyB,OAArB0B,EAA2B,CAE7B,IAAMd,EAAMc,CAAgB,CAAC,EAAE,CACzBb,EAAUa,CAAgB,CAAC,EAAE,CACnCL,EAAe,CACbO,SAAU,KAEVhB,IAAKW,EAAwBM,QAAQ,CAACC,EAAAA,gBAAgB,EAAI,KAAOlB,EACjEmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,YACpBrB,cACA7B,CACF,CACF,MAGEqC,CAHK,CAGU,CACbO,SAAU,KACVhB,IAAK,KACLmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,IACpBrB,QAAS,KACT7B,YAAa,CAAC,CAChB,EAGF,IAAMmD,EAAyBzB,EAASX,cAAc,CAACqC,GAAG,CAACpC,GACvDmC,EACFA,EAAuBE,GAAG,CAACb,EAAUH,GAErCX,EAASX,SAHiB,KAGH,CAACsC,GAAG,CAACrC,EAAK,IAAIkC,IAAI,CAAC,CAACV,EAAUH,EAAa,CAAC,GAGrEP,EACE9B,EACAqC,EACAN,EACAO,EACAI,EAEJ,CACF,EAlIQ1C,EACA0B,EACAlB,EACAS,EACAH,EAEJ,MAEEY,CAFK,CAEIE,GAAG,CAAGpB,EAAaoB,GAAG,CAC/BF,EAASqB,WAAW,CAAGvC,EAAauC,WAAW,CAC/CrB,EAASG,OAAO,CAAGrB,EAAaqB,OAAO,CACvCH,EAASX,cAAc,CAAG,IAAImC,IAAI1C,EAAaO,cAAc,EAG7DuC,CAAAA,EAAAA,EAAAA,yCAAAA,EACEtD,EACA0B,EACAlB,EACAI,GAMAY,IACFlB,EAAckB,EACdhB,CAFW,CAEIkB,EACfrB,GAAU,EAEd,OAEA,CAAI,CAACA,IAILD,EAAQmD,GAJM,QAIK,CAAGjD,EACtBF,EAAQK,KAAK,CAAGD,EAChBJ,EAAQoD,YAAY,CAAG9C,EACvBN,EAAQqD,YAAY,CAAGtD,EAAIuD,IAAI,CAExBC,CAAAA,EAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,GAC9B,CAkGO,SAASN,EACd8D,CAAoC,CACpCxC,CAA2D,EAE3D,GAAM,CAACyC,EAAS9C,EAAgB,GAAG+C,EAAK,CAAGF,EAG3C,GAAIC,EAAQhB,QAAQ,CAACC,EAAAA,gBAAgB,EAEnC,CAFsC,KAE/B,CADYiB,CAAAA,EAAAA,EAAAA,4BAAAA,EAA6BF,EAASzC,GACrCL,KAAmB+C,EAAK,CAI9C,IAAME,EAA8D,CAAC,EAErE,IAAK,GAAM,CAAChD,EAAKiD,EAAc,GAAI/C,OAAOgD,OAAO,CAACnD,GAChDiD,CAAqB,CAAChD,EAAI,CAAGlB,EAC3BmE,EACA7C,GAIJ,CAPmE,KAO5D,CAACyC,EAASG,KAA0BF,EAAK,gUChPlCK,qCAAAA,aAT8B,OASvC,SAASA,EACdC,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,EAA4B,MACvCC,EAAWD,CAAAA,EAAAA,EAAAA,MAAAA,EAA4B,MAS7C,MAAOE,CAAAA,EAAAA,EAAAA,WAAAA,EACL,IACE,GAAgB,OAAZC,EAAkB,CACpB,IAAMC,EAAaL,EAASI,OAAO,CAC/BC,IACFL,EAASI,MADK,CACE,CAAG,KACnBC,KAEF,IAAMC,EAAaJ,EAASE,OAAO,CAC/BE,IACFJ,EAASE,MADK,CACE,CAAG,KACnBE,IAEJ,MACMR,CADC,GAEHE,EAASI,OAAO,CAAGG,EAAST,EAAMM,EAAAA,EAEhCL,IACFG,EADQ,OACQ,CAAGK,EAASR,EAAMK,EAAAA,CAGxC,EACA,CAACN,EAAMC,EAAK,CAEhB,CAEA,SAASQ,EACPT,CAAgC,CAChCM,CAAiB,EAEjB,GAAI,mBAAON,EAST,OADAA,EAAKM,OAAO,CAAGA,EACR,KACLN,EAAKM,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAMI,EAAUV,EAAKM,SACrB,YAAI,OAAOI,EACFA,EAEA,IAAMV,EAAK,KAEtB,CAMF,MANS,yTCqMOW,qCAAAA,OAjQS,eACO,WACG,OAkF7BC,EAAiC,CACrC,eACA,OACA,OACA,kBACArC,EACD,CA4BD,SAASsC,EACPC,CAAoC,EAEpC,YAA0CvC,IAAlCuC,EAAsBC,OAAO,CAwBvC,SAASC,EAAOC,CAAU,SACxB,KAAiB,IAANA,EACFA,EAEL,KAH0B,KAGH,OAAhBA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAEjB,UAAb,OAAOH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,CAqGO,SAAST,EACd,CAyBa,CACbY,CAKC,MAkBmBC,IAjDpB,IA0CIC,EAqEAC,EACAC,EAhHJ,KACEb,CAAG,OACHc,CAAK,aACLC,GAAc,CAAK,UACnBC,GAAW,CAAK,SAChBrE,CAAO,WACPsE,CAAS,SACTC,CAAO,OACPC,CAAK,QACLC,CAAM,MACNC,GAAO,CAAK,CACZC,OAAK,aACLC,CAAW,QACXC,CAAM,mBACNC,CAAiB,aACjBC,EAAc,OAAO,aACrBC,CAAW,eACXC,CAAa,UACbC,EAAW,OAAO,QAClBC,CAAM,WACNC,CAAS,CACTC,gBAAc,cACdC,CAAY,UACZC,CAAQ,CACR,GAAGtD,EACQ,CAzBb,EAyCM,CAAEuD,SAAO,aAAEC,CAAW,CAAEC,cAAY,eAAEC,CAAa,CAAE,CAAG7B,EAE1DC,EAAIyB,GAAWI,EAAAA,kBAAkB,CACrC,GAAI,aAAc7B,EAChBC,CADmB,CACVD,MACJ,CACL,IAAM8B,EAAW,IAAI9B,EAAE+B,WAAW,IAAK/B,EAAEgC,UAAU,CAAC,CAACC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClEJ,EAAc/B,EAAE+B,WAAW,CAACE,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CC,EAAAA,OAAYpC,EAAAA,EAAEoC,SAAAA,EAAS,OAAXpC,EAAaiC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClDlC,EAAS,CAAE,GAAGD,CAAC,UAAE8B,cAAUC,YAAaK,CAAU,CACpD,CAEA,GAAI,KAAyB,IAAlBR,EACT,MAAM,CADkC,MAClC,cAEL,CAFK,MACJ,yIADI,+DAEN,GAEF,IAAIS,EAAgCnE,EAAKmE,MAAM,EAAIT,CAGnD,QAAO1D,EAAKmE,MAAM,CAClB,OAAQnE,EAAaoE,MAAM,CAI3B,IAAMC,EAAkB,uBAAwBF,EAEhD,GAAIE,GACF,GAAsB,UAAU,CADb,EACRF,MAAM,CACf,MAAM,qBAGL,CAHK,yBACe/C,EAAlB,IAAsB,gCACpB,kEAFC,+DAGN,EACF,KACK,CAIL,IAAMkD,EAAoBH,EAC1BA,EAAS,IACP,GAAM,CAAEpC,OAAQwC,CAAC,CAAE,GAAGC,EAAM,CAAGC,EAC/B,OAAOH,EAAkBE,EAC3B,CACF,CAEA,GAAItB,EAAQ,CACK,QAAQ,CAAnBA,IACFT,GAAO,GAUT,IAAMiC,EARoE,CACxEC,UAAW,CAAEC,SAAU,OAAQpC,OAAQ,MAAO,EAC9CqC,WAAY,CAAEtC,MAAO,OAAQC,OAAQ,MAAO,CAC9C,CAKiC,CAACU,EAAO,CACrCwB,GACFhC,GAAQ,CAAE,GAAGA,CAAK,CAAE,CADL,EACQgC,CAAW,CAAC,EAErC,IAAMI,EARoD,CACxDD,WAAY,QACZpC,KAAM,OACR,CAKiC,CAACS,EAAO,CACrC4B,GAAe,CAAC5C,IAClBA,EAAQ4C,CADiB,CAG7B,CAEA,IAAIC,EAAY,GACZC,EAAW1D,EAAOiB,GAClB0C,EAAY3D,EAAOkB,GAGvB,GA/OE,CAAC,CAACpB,CA+OeA,CAjP6B,GAG/B,UAAf,EACCD,KADMC,IACND,EAAgBC,QACf8D,CARoCrG,IAQlBuC,EARUA,GAAG,CAQbA,CAAmB,CA6OvC,IAAM+D,EAAkBhE,EAAgBC,GAAOA,EAAIC,OAAO,CAAGD,EAE7D,GAAI,CAAC+D,EAAgB/D,GAAG,CACtB,CADwB,KAClB,qBAIL,CAJK,MACH,8IAA6IgE,KAAKC,SAAS,CAC1JF,IAFE,+DAIN,GAEF,GAAI,CAACA,EAAgB3C,MAAM,EAAI,CAAC2C,EAAgB5C,KAAK,CACnD,CADqD,KAC/C,qBAIL,CAJK,MACH,2JAA0J6C,KAAKC,SAAS,CACvKF,IAFE,8DAIN,GAQF,GALAnD,EAAYmD,EAAgBnD,SAAS,CACrCC,EAAakD,EAAgBlD,UAAU,CACvCc,EAAcA,GAAeoC,EAAgBpC,WAAW,CACxDgC,EAAYI,EAAgB/D,GAAG,CAE3B,CAACqB,EACH,GAAI,CADK,EACSwC,GAGX,GAAID,GAHM,CAGOC,CAHK,CAGM,CACjC,IAAMK,EAAQN,EAAWG,EAAgB5C,KAAK,CAC9C0C,EAAYM,KAAKC,KAAK,CAACL,EAAgB3C,MAAM,CAAG8C,EAClD,MAAO,GAAI,CAACN,GAAYC,EAAW,CACjC,IAAMK,EAAQL,EAAYE,EAAgB3C,MAAM,CAChDwC,EAAWO,KAAKC,KAAK,CAACL,EAAgB5C,KAAK,CAAG+C,GAChD,MAREN,EAAWG,EAAgB5C,KAAK,CAChC0C,EAAYE,EAAgB3C,MASlC,CAGA,IAAIiD,EACF,CAACrD,IAAyB,QAAZrE,CAAAA,GAAsB,SAAOA,CAAY,EAAU,CAC/D,CAACqD,CAJLA,EAAqB,UAAf,OAAOA,EAAmBA,EAAM2D,CAAAA,GAI1B3D,EAAIsE,UAAU,CAAC,UAAYtE,EAAIsE,UAAU,CAAC,WAAU,CAE9DvD,GAAc,EACdsD,GAAS,GAEP1D,EAAOI,WAAW,EAAE,CACtBA,GAAc,GAGdkC,GACA,CAACtC,EAAO4D,mBAAmB,EAC3BvE,EAAIwE,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,CAGA1D,EAAc,IAGhB,IAAM2D,EAAaxE,EAAOgB,GAyMpByD,EAAW3I,OAAO4I,MAAM,CAC5BvD,EACI,CACEwD,SAAU,WACVzD,OAAQ,OACRD,MAAO,OACP2D,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,YACRlD,iBACAC,CACF,EACA,CAAC,EACLI,EAAc,CAAC,EAAI,CAAE8C,MAAO,aAAc,EAC1C5D,GAGI6D,EACJ,GAAiC,UAAhBzD,EAWb,KAVgB,SAAhBA,EACG,yCAAwC0D,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,UACvDxB,YACAC,EACAjD,YACAC,aACAc,YAAaA,GAAe,GAC5BI,UAAW4C,EAAS5C,SAAS,GAC5B,KACF,QAAOL,EAAY,KAAI,EAGR5B,EAA+BnC,QAAQ,CAC7DgH,EAAS5C,QAJ4C,CAInC,EAGO,SAAvB4C,EAAS5C,SAAS,CAChB,YAAY,QAFd4C,EAAS5C,SAAS,CAKlBsD,EAAqCF,EACrC,gBACEG,EACAC,CANuD,kBAMnCZ,EAAS3C,cAAc,EAAI,UAC/CwD,iBAAkB,4BAClBL,CACF,EACA,CAAC,EAeCM,EA3dR,SAASC,CAQS,EARQ,WACxB/E,CAAM,KACNX,CAAG,aACHe,CAAW,CACXI,OAAK,CACLD,SAAO,OACPJ,CAAK,CACLiC,QAAM,CACU,CARQ,EASxB,GAAIhC,EACF,MAAO,CAAEf,IADM,EACDgD,YAAQvF,EAAWqD,WAAOrD,CAAU,EAGpD,GAAM,QAAEkI,CAAM,CAAEC,MAAI,CAAE,CAAGC,SAxElBA,CAC+B,CACtC1E,CAAyB,CACzBL,CAAyB,EAFzB,gBAAE2B,CAAW,UAAED,CAAQ,CAAe,CAAtC,EAIA,GAAI1B,EAAO,CAET,IAAMgF,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAACnF,IAC5CiF,EAAAA,EADqDC,EACpC,CAACxF,GAD0C,MACjCwF,CAAK,CAAC,EAAE,GAErC,GAAID,EAAa7I,MAAM,CAAE,CACvB,IAAMgJ,EAA4C,IAA5B/B,KAAKgC,GAAG,IAAIJ,GAClC,MAAO,CACLJ,OAAQnD,EAAS4D,MAAM,CAAC,GAAOC,GAAK5D,CAAW,CAAC,EAAE,CAAGyD,GACrDN,KAAM,GACR,CACF,CACA,MAAO,CAAED,OAAQnD,EAAUoD,KAAM,GAAI,CACvC,OACA,UAAI,OAAOzE,EACF,CAAEwE,OAAQlD,EAAamD,KAAM,GAAI,EAkBnC,CAAED,OAfM,IACV,IAAIW,IACL,CAQCnF,EAAe,EAARA,EAA0B,CAACoF,GAAG,CACpC,GAAO/D,EAASgE,CADa,GACT,CAAC,GAAOC,GAAKC,IAAMlE,CAAQ,CAACA,EAAStF,MAAM,CAAG,EAAE,GAGzE,CACgB0I,KAAM,GAAI,CAC7B,EA+BqCjF,EAAQQ,EAAOL,GAC5C6F,EA7CmE,EA6CrDzJ,MAAM,CAAG,EAE7B,MAAO,CACL4D,MAAO,GAAmB,MAAT8E,EAAyB9E,EAAV,QAChCkC,OAAQ2C,EACLY,GAAG,CACF,CAACG,EAAGE,IACC7D,EAAO,CAAEpC,aAAQX,UAAKkB,EAASC,MAAOuF,CAAE,GAAG,IAC5Cd,CAAAA,CAAAA,KAAAA,EAAec,EAAIE,EAAAA,CAAAA,CAAI,CACtBhB,GAENiB,IAAI,CAAC,MAQR7G,IAAK+C,EAAO,CAAEpC,SAAQX,cAAKkB,EAASC,MAAOwE,CAAM,CAACgB,EAAK,EACzD,CACF,EAwbyC,QACrChG,MACAX,cACAe,EACAI,MAAOyC,EACP1C,QAASwD,QACT5D,SACAiC,CACF,GA4BA,MAAO,CAAE+D,MAde,CACtB,GAAGlI,CAAI,CACPjC,QAAS0H,EAAS,OAAS1H,gBAC3BiF,EACAT,MAAOyC,EACPxC,OAAQyC,WACRhC,YACAZ,EACAK,MAAO,CAAE,GAAGqD,CAAQ,CAAE,GAAGU,CAAgB,EACzCvE,MAAO2E,EAAc3E,KAAK,CAC1BkC,OAAQyC,EAAczC,MAAM,CAC5BhD,IAAKuB,GAAekE,EAAczF,GACpC,EAEgB+G,KADH,aAAEhG,WAAaC,cAAUU,OAAaL,CAAK,CACnC,CACvB,mGC/Wa2F,qCAAAA,kDA/VN,gBACc,gBACJ,YACW,UAYO,WACA,SACV,eACK,eAGJ,YACG,MAGvBC,EAAYC,CAAAA,YAAAA,CAAAA,IAAAA,IAAAA,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA,KAAAA,CAAAA,WAAAA,CAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,IAAAA,IAAAA,CAAAA,KAAAA,eAAAA,OAAAA,UAAAA,oBAAAA,CAAAA,EAAAA,YAAAA,CAAAA,CAAAA,CAA6B,CAyB/C,SAASC,EACPC,CAA2B,CAC3B1F,CAA6B,CAC7B2F,CAAqD,CACrDC,CAA2E,CAC3EC,CAAqC,CACrCxG,CAAoB,CACpByG,CAA8B,EAE9B,IAAMxH,EAAMoH,QAAAA,KAAAA,EAAAA,EAAKpH,GAAG,CACfoH,GAAOA,CAAG,CAAC,kBAAkB,GAAKpH,IAGvCoH,CAH4C,CAGxC,kBAAkB,CAAGpH,EAEzByG,CADU,WAAYW,EAAMA,EAAIK,MAAM,GAAKC,QAAQC,OAAO,IACxDC,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC,KACrB,GAAI,EAAKC,aAAa,EAAKV,EAAD,WAAgB,EAAE,GAQxB,SAAS,CAAzB1F,GACF6F,GAAgB,GAEdF,MAAAA,EAAAA,KAAAA,EAAAA,EAAW7H,OAAO,CAAE,CAItB,IAAMuI,EAAQ,IAAIC,MAAM,QACxBhM,OAAOiM,cAAc,CAACF,EAAO,SAAU,CAAEG,UAAU,EAAOC,MAAOf,CAAI,GACrE,IAAIgB,GAAY,EACZC,GAAU,EACdhB,EAAU7H,OAAO,CAAC,CAChB,GAAGuI,CAAK,CACRO,YAAaP,EACbQ,cAAenB,EACfoB,OAAQpB,EACRqB,mBAAoB,IAAML,EAC1BM,qBAAsB,IAAML,EAC5BM,QAAS,KAAO,EAChBC,eAAgB,KACdR,GAAY,EACZL,EAAMa,cAAc,EACtB,EACAC,gBAAiB,KACfR,GAAU,EACVN,EAAMc,eAAe,EACvB,CACF,EACF,EACIvB,MAAAA,EAAAA,KAAAA,EAAAA,EAAsB9H,OAAAA,EAAS,CACjC8H,EAAqB9H,OAAO,CAAC4H,GAkDjC,GACF,CAEA,SAAS0B,EACPlH,CAAsB,SAEVmH,EAAAA,EAARC,CAAW,CAIN,EAJS,aAIPpH,CAAc,EAIlB,CAAEqH,cAAerH,CAAc,CACxC,CA7IIsH,WAAmBC,qBAAqB,EAAG,EA+I/C,IAAMC,EAAeC,CAAAA,EAAAA,EAAAA,QAAfD,EAAeC,EACnB,GAwBEC,IAzBEF,IAEF,KACEpJ,CAAG,CACHgD,QAAM,OACNlC,CAAK,QACLM,CAAM,OACND,CAAK,UACLU,CAAQ,WACRZ,CAAS,OACTK,CAAK,eACLM,CAAa,aACbF,CAAW,SACX/E,CAAO,aACPoE,CAAW,MACXM,CAAI,WACJgG,CAAS,sBACTC,CAAoB,iBACpBC,CAAe,gBACfgC,CAAc,YACd/B,CAAU,QACVhG,CAAM,SACNgI,CAAO,CACP,GAAG5K,EACJ,GAGK6K,EAASlK,CAAAA,EAAAA,EAAAA,WAAAA,EACb,IACO6H,IAGDoC,CAHM,GAQRpC,EAAIpH,GALO,CAKDoH,EAAIpH,GAAAA,EAYZoH,EAAIsC,QAAQ,EAAE,EAEdtC,EACA1F,EACA2F,EACAC,EACAC,EACAxG,EACAyG,GAGN,EACA,CACExH,EACA0B,EACA2F,EACAC,EACAC,EACAiC,EACAzI,EACAyG,EACD,EAGGmC,EAAM1K,CAAAA,EAAAA,EAAAA,YAAAA,EAAaqK,EAAcG,GAEvC,MACE,UAACrC,MAAAA,CACE,GAAGxI,CAAI,CACP,GAAGkK,EAAgBlH,EAAc,CAIlCjF,QAASA,EACTwE,MAAOA,EACPC,OAAQA,EACRS,SAAUA,EACV+H,YAAWvI,EAAO,OAAS,IAC3BJ,UAAWA,EACXK,MAAOA,EAOPR,MAAOA,EACPkC,OAAQA,EACRhD,IAAKA,EACL2J,IAAKA,EACLnI,OAAQ,IAEN2F,EADYY,EAAMQ,UAEhBnB,GAF6B,CAG7B1F,EACA2F,EACAC,EACAC,EACAxG,EACAyG,EAEJ,EACAgC,QAAS,IAEPD,GAAe,GACK,SAAS,CAAzB7H,GAEF6F,EAAgB,IAEdiC,GACFA,EAAQzB,EAEZ,EAHe,CAMrB,GAGF,SAAS8B,EAAa,CAMrB,EANqB,gBACpBC,CAAW,eACXrE,CAAa,CAId,CANqB,EAOdrC,EAAO,CACX2G,GAAI,QACJC,YAAavE,EAAczC,MAAM,CACjCN,WAAY+C,EAAc3E,KAAK,CAC/BmJ,YAAaxE,EAAcwE,WAAW,CACtCC,eAAgBzE,EAAcyE,cAAc,CAC5C,GAAGpB,EAAgBrD,EAAc7D,aAAa,CAAC,SAGjD,GAAmBuI,EAAAA,OAAQ,CAACC,OAAO,EAAE,EAEnCD,OAAQ,CAACC,OAAO,CACd3E,EAAczF,GAAG,CACjB,GAGK,MAIP,UAACqK,EAAAA,OAAI,WACH,UAACC,OAAAA,CAOCC,IAAI,UAMJ/O,KAAMiK,EAAczC,MAAM,MAAGvF,EAAYgI,EAAczF,GAAG,CACzD,GAAGoD,CAAI,EAZN,UACAqC,EAAczF,GAAG,CACjByF,EAAczC,MAAM,CACpByC,EAAc3E,KAAK,GAa7B,CAOO,IAAMkG,EAAQqC,CAAAA,EAAAA,EAAAA,CAAAA,SAAAA,EAARrC,CACVF,EAAOwC,KACN,IAAMkB,EAAcC,CAAAA,EAAAA,EAAAA,UAAAA,EAAWC,EAAAA,aAAa,EAItCC,EAAgBF,CAAAA,EAAAA,EAAAA,UAAAA,EAAWG,EAAAA,kBAAkB,EAC7CjK,EAASkK,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,SAIHnK,EAHlB,IAAMA,EAAIuG,GAAa0D,GAAiBpI,EAAAA,kBAAkB,CACpDC,EAAW,IAAI9B,EAAE+B,WAAW,IAAK/B,EAAEgC,UAAU,CAAC,CAACC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClEJ,EAAc/B,EAAE+B,WAAW,CAACE,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CC,EAAuB,MAAXpC,CAAAA,EAAAA,EAAEoC,SAAAA,EAAS,OAAXpC,EAAaiC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD,MAAO,CAAE,GAAGnC,CAAC,UAAE8B,cAAUC,YAAaK,CAAU,CAClD,EAAG,CAAC6H,EAAc,EAEZ,CAAEnJ,QAAM,mBAAEC,CAAiB,CAAE,CAAGqF,EAChCO,EAAYhI,GAAAA,EAAAA,MAAAA,EAAOmC,GAEzBsJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACRzD,EAAU7H,OAAO,CAAGgC,CACtB,EAAG,CAACA,EAAO,EAEX,IAAM8F,EAAuBjI,CAAAA,EAAAA,EAAAA,MAAAA,EAAOoC,GAEpCqJ,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACRxD,EAAqB9H,OAAO,CAAGiC,CACjC,EAAG,CAACA,EAAkB,EAEtB,GAAM,CAACY,EAAckF,EAAgB,CAAGwD,CAAAA,EAAAA,EAAAA,QAAAA,GAAS,GAC3C,CAAC3I,EAAamH,EAAe,CAAGwB,CAAAA,EAAAA,EAAAA,QAAAA,GAAS,GAEzC,CAAEjE,MAAOrB,CAAa,CAAEsB,KAAMiE,CAAO,CAAE,CAAGnL,CAAAA,EAAAA,EAAAA,WAAAA,EAAYiH,EAAO,CACjExE,cAAAA,EAAAA,OAAa,CACbH,QAASxB,eACT0B,cACAD,CACF,GAEA,MACE,iCAEI,UAACgH,EAAAA,CACE,GAAG3D,CAAa,CACjB1E,YAAaiK,EAAQjK,WAAW,CAChCW,YAAasJ,EAAQtJ,WAAW,CAChCL,KAAM2J,EAAQ3J,IAAI,CAClBgG,UAAWA,EACXC,qBAAsBA,EACtBC,gBAAiBA,EACjBgC,eAAgBA,EAChB/B,WAAYV,EAAMhG,KAAK,CACvB6I,IAAKL,IAGR0B,EAAQhK,QAAQ,CACf,UAAC6I,EAAAA,CACCC,YApDY,CAoDCA,EACbrE,cAAeA,IAEf,OAGV,2OCvaF,kDAAoF,2GCKpEwF,qCAAAA,aANU,OAMnB,SAASA,EAAcC,CAAY,CAAEC,CAAe,EACzD,GAAI,CAACD,EAAK5G,UAAU,CAAC,MAAQ,CAAC6G,EAC5B,MADoC,CAC7BD,EAGT,GAAM,UAAEE,CAAQ,OAAEC,CAAK,MAAE7M,CAAI,CAAE,CAAG8M,CAAAA,EAAAA,EAAAA,SAAAA,EAAUJ,GAC5C,MAAQ,GAAEC,EAASC,EAAWC,EAAQ7M,CACxC,iHCEgB+M,qCAAAA,aAfkB,WACU,WACA,WAOV,WACF,WACF,WAEO,OAE9B,SAASA,EACdxQ,CAA2B,CAC3ByQ,CAAyB,EAEzB,GAAM,CACJC,eAAgB,YAAEzQ,CAAU,CAAEsD,aAAcoN,CAAoB,CAAE,aAClE5Q,CAAW,CACZ,CAAG0Q,EAEEtQ,EAAmB,CAAC,EAK1B,GAHAA,EAAQyQ,0BAA0B,EAAG,EAGjC,UAAgC,OAAzB3Q,EACT,MAAO4Q,CAAAA,EAAAA,EAAAA,iBAAAA,EACL7Q,EACAG,EACAF,EACAD,EAAM8Q,OAAO,CAACC,WAAW,EAI7B,IAAI1Q,EAAcL,EAAMM,IAAI,CACxBC,EAAeP,EAAMQ,KAAK,CAE9B,IAAK,IAAMG,KAAwBV,EAAY,CAC7C,GAAM,CAAE+Q,YAAaC,CAAiB,CAAE3Q,KAAMU,CAAS,CAAE,CACvDL,EAEIY,EAAUC,GAAAA,EAAAA,2BAAAA,EACd,CACC,MAAOyP,EAAkB,CAC1B5Q,EACAW,EACAhB,EAAMuD,YAAY,EAQpB,GAAgB,MAAM,CAAlBhC,EACF,OAAOvB,EAGT,GAAIkR,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4B7Q,EAAakB,GAC3C,MAAOsP,CAAAA,EAAAA,EAAAA,iBAAAA,EACL7Q,EACAG,EACAH,EAAMuD,YAAY,CAClBvD,EAAM8Q,OAAO,CAACC,WAAW,EAI7B,IAAMI,EAA2BR,EAC7BjQ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBiQ,QAClBjO,CAEAyO,KACFhR,EAAQoD,YAAY,CAAG4N,CAAAA,EAGzB,GAJ8B,CAIxB3Q,EAAmBkB,CAAAA,EAAAA,EAAAA,oBAAAA,IACzB0P,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBrR,EAAaQ,EAAcC,EAAOG,GAElDR,EAAQmD,WAAW,CAAG/B,EACtBpB,EAAQK,KAAK,CAAGA,EAEhBD,EAAeC,EACfH,EAAckB,CAChB,CAEA,MAAOmC,GAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,EAC9B,uOChEO,SAASkR,EACdC,CAAU,EAEV,IAAMC,EAAW9L,SAAS6L,EAAGE,KAAK,CAAC,EAAG,GAAI,IAEpCC,EAAWF,GAAY,EAAK,GAE5BG,EAAWC,MAAM,GAEvB,IAAK,IAAIC,EAAQ,EAAGA,EAAQ,EAAGA,IAAS,CAEtC,IAAMC,EAAOJ,GADO,EAAIG,EACe,EACvCF,CAAQ,CADgBI,EACT,CAAGD,KACpB,CAEA,MAAO,CACLE,KAAMC,IAZST,GAAY,EAAK,GAYV,YAAc,gBACpCG,SAAUA,EAQVO,YAAaC,IAnBa,EAAXX,CAAW,CAoB5B,CACF,CAMO,SAASY,EACdC,CAAe,CACfC,CAAyB,EAEzB,IAAMC,EAAe,MAAUF,EAAKjQ,MAAM,EAE1C,IAAK,IAAIyP,EAAQ,EAAGA,EAAQQ,EAAKjQ,MAAM,CAAEyP,KAEpCA,EAAQ,CAFqC,EAEhCS,EAAKX,QAAQ,CAACE,EAAM,EAGjCA,EAFD,CAEU,GAAKS,EAAKJ,WAAAA,EACpB,EACAK,CAAY,CAACV,EAAM,CAAGQ,CAAI,CAACR,EAAAA,EAI/B,OAAOU,CACT,gCAT8E,wGA1C9DjB,gCAAgC,mBAAhCA,GAiCAc,cAAc,mBAAdA,sDCrDZ,EAAC,CAAE,gBAAF,mBAAE,iHAAyK,ICApF,UAAgB,MAAQ,gBAAC,GAAG,kFAAkF,MAAM,OAAS,SAAS,CAAC,0DAA0D,MAAO,mBAAC,yBAAyB,mBAAC,IAAI,cAAc,kCAAkC,EAAE,wBAAwB,EAAE,SAA8B,0NEuT7a,OAyZC,mBAzZuBI,GA+ZXC,aAAa,mBAAbA,uCA1tB2D,YAE9C,WACO,WACJ,WACA,UACC,WACF,SACH,eASlB,WACoB,WACY,OAsRvC,SAASC,EAAkBC,CAAkC,QAC3D,UAAI,OAAOA,EACFA,EAGFC,CAAAA,EAAAA,EAAAA,SAAAA,EAAUD,EACnB,CAYe,SAASH,EACtBxG,CAGC,EAED,IAEI6G,EA+LAC,EAyLAtD,EA1XE,CAACuD,EAAYC,EAAwB,CAAGC,CAAAA,EAAAA,EAAAA,aAAAA,EAAcC,EAAAA,gBAAgB,EAItEC,EAAkB5O,CAAAA,EAAAA,EAAAA,MAAAA,EAA4B,MAE9C,CACJ7D,KAAM0S,CAAQ,CACdnE,GAAIoE,CAAM,CACVR,SAAUS,CAAY,CACtBC,SAAUC,EAAe,IAAI,UAC7BC,CAAQ,SACRC,CAAO,SACPC,CAAO,QACPC,CAAM,SACNC,CAAO,CACPC,aAAcC,CAAgB,CAC9BC,aAAcC,CAAgB,gBAC9BC,GAAiB,CAAK,YACtBC,CAAU,CACVtF,IAAKL,CAAY,yBACjB4F,CAAuB,CACvB,GAAGC,EACJ,CAAGrI,EAEJ6G,EAAWS,EAGTY,GACC,kBAAOrB,GAAyB,iBAAOA,CAAa,EAAO,EAC5D,EACW,SAAXA,CAAY/K,IAAAA,MAAZ+K,IAAeA,KAGjB,IAAMyB,EAASC,EAAAA,OAAK,CAAC5E,UAAU,CAAC6E,EAAAA,gBAAgB,EAE1CC,GAAmC,IAAjBjB,EAQlBkB,EACJlB,SAAwBmB,EAAAA,YAAY,CAACC,IAAI,CAAGD,EAAAA,YAAY,CAACE,IAAI,CA2IzD,MAAEnU,CAAI,IAAEuO,CAAE,CAAE,CAAGsF,EAAAA,OAAK,CAACxE,OAAO,CAAC,KACjC,IAAM+E,EAAepC,EAAkBU,GACvC,MAAO,CACL1S,KAAMoU,EACN7F,GAAIoE,EAASX,EAAkBW,GAAUyB,CAC3C,CACF,EAAG,CAAC1B,EAAUC,EAAO,EAIjBa,IA4BApB,EAAQyB,EAAAA,OAAK,CAACQ,QAAQ,CAACC,IAAI,CAACnC,EAAAA,EAYhC,IAAMoC,EAAgBf,EAClBpB,GAA0B,UAAjB,OAAOA,GAAsBA,EAAMjE,GAAG,CAC/CL,EAME0G,EAA+BX,EAAAA,OAAK,CAAC9P,WAAW,CACpD,IACiB,MAAM,CAAjB6P,IACFnB,EAAgBzO,OAAO,CAAGyQ,CAAAA,EAAAA,EAAAA,iBAAAA,EACxBC,EACA1U,EACA4T,EACAI,EACAD,EACAzB,EAAAA,EAIG,KACDG,EAAgBzO,OAAO,EAAE,CAC3B2Q,CAAAA,EAAAA,EAAAA,+BAAAA,EAAgClC,EAAgBzO,OAAO,EACvDyO,EAAgBzO,OAAO,CAAG,MAE5B4Q,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4BF,EAC9B,GAEF,CAACX,EAAiB/T,EAAM4T,EAAQI,EAAiB1B,EAAwB,EAKrEuC,EAMF,CACF1G,IATgB1K,CASXqR,EATWrR,EAAAA,YAAAA,EAAa+Q,EAA8BD,GAU3DpB,QAAQ4B,CAAC,EASH,GAAsC,YAAnB,OAAO5B,GAC5BA,EAAQ4B,GAIRvB,GACApB,EAAM9G,KAAK,EACoB,YAC/B,OADO8G,EAAM9G,KAAK,CAAC6H,OAAO,EAE1Bf,EAAM9G,KAAK,CAAC6H,OAAO,CAAC4B,GAGjBnB,IAIDmB,EAAEC,EAJO,cAIS,EAnY5B,SAASC,CACY,CACnBjV,CAAY,CACZuO,CAAU,CACVkE,CAAqD,CACrDO,CAAiB,CACjBE,CAAgB,CAChBO,CAAmC,EAEnC,GAAM,UAAEyB,CAAQ,CAAE,CAAGH,EAAEhI,aAAa,CAKpC,KACGoI,MAHsBD,EAASE,WAAW,IAzB/C,SAASC,CAAuC,EAE9C,IAAMrI,EADcT,EAAMQ,aAAa,CACZuI,YAAY,CAAC,UACxC,OACGtI,GAAqB,UAAXA,GACXT,EAAMgJ,OAAO,EACbhJ,EAAMiJ,OAAO,EACbjJ,EAAMkJ,QAAQ,EACdlJ,EAAMmJ,MAAM,EACXnJ,EAAMO,WAAW,EAAgC,IAA5BP,EAAMO,UADiB,CACN,CAAC6I,KAE5C,EAiByCZ,IACrCA,EAAEhI,aAAa,CAAC6I,YAAY,CAAC,cAC7B,GAKE,CAACC,CAAAA,EAAAA,EAAAA,UAAAA,EAAW7V,GAAO,CACjBgT,IAGF+B,EAAE3H,GAHS,WAGK,GAChB0I,SAAS9C,OAAO,CAAChT,IAInB,MACF,CAEA+U,EAAE3H,cAAc,GAyBhByG,EAAAA,OAAK,CAACkC,eAAe,CAACC,KAtBpB,GAAIvC,EAAY,CACd,IAAIxG,GAAqB,EAQzB,GANAwG,EAAW,CACTrG,eAAgB,KACdH,GAAqB,CACvB,CACF,GAEIA,EACF,MAEJ,CAEAgJ,CAAAA,EAAAA,EAAAA,MAL0B,gBAK1BA,EACE1H,GAAMvO,EACNgT,EAAU,UAAY,OACtBE,MAAAA,GAAAA,EACAT,EAAgBzO,KADN,EACa,CAE3B,GAGF,EA2UkB+Q,EAAG/U,EAAMuO,EAAIkE,EAAiBO,EAASE,EAAQO,GAC7D,EACAL,aAAa2B,CAAC,EACPvB,GAAkB,YAAwC,OAAjCH,GAC5BA,EAAiB0B,GAIjBvB,GACApB,EAAM9G,KAAK,EACyB,YACpC,OADO8G,EAAM9G,KAAK,CAAC8H,YAAY,EAE/BhB,EAAM9G,KAAK,CAAC8H,YAAY,CAAC2B,GAGtBnB,GAIAG,GAKLmC,CAAAA,CATa,CASbA,EAAAA,WALwBxK,OAKxBwK,CALgCC,CAM9BpB,EANiC,CAACqB,KAAa,GAAL,IAM3B,CAF4C,CAG3DC,IAH+B3C,EAJ+B,EAUlEJ,aAEI,CAFU5H,MAAsC,EAEvC4H,CADTrR,CAASA,CAEH,GAA+C,YAA5B,OAAOsR,GAC5BA,EAAiBwB,GAIjBvB,GACApB,EAAM9G,KAAK,EACyB,YAApC,OAAO8G,EAAM9G,KAAK,CAACgI,YAAY,EAE/BlB,EAAM9G,KAAK,CAACgI,YAAY,CAACyB,GAGtBnB,GAIAG,GAKLmC,CAAAA,CATa,CASbA,EAAAA,SALsB,SAKtBA,EACEnB,EAAEhI,aAAa,EACfsJ,IAH+B3C,EAKnC,CACN,EAmCA,MA9BI4C,CAAAA,EAAAA,EAAAA,aAAAA,EAAc/H,GAChBsG,EADqB,IACN,CAAGtG,EAElB,IACAwE,IACCX,OAAsB,CAAE,IAAd,EAAc,SAAUA,EAAM9G,KAAI,GAC7C,CACAuJ,EAAW7U,IAAI,CAAGuW,CAAAA,EAAAA,EAAAA,WAAAA,EAAYhI,EAAAA,EAc9BO,EATE0E,EASKK,EAAAA,CAAP/E,MAAY,CAAC0H,IATK,QASO,CAACpE,EAAOyC,GAG/B,UAACzN,IAAAA,CAAG,GAAGuM,CAAS,CAAG,GAAGkB,CAAU,UAC7B1C,IAML,UAACsE,EAAkBC,QAAQ,EAAC/J,MAAO0F,WAChCvD,GAGP,GAhsB0B,OAksB1B,IAAM2H,EAAoBE,CAAAA,EAAAA,EAAAA,aAApBF,EAEJjE,EAAAA,OAFIiE,SAEY,EAEL1E,EAAgB,IACpB9C,CAAAA,EAAAA,EAAAA,UAAAA,EAAWwH,2OC7tBpB,gBACA,8CACA,kEAGA,QACA,mJCKgBG,qCAAAA,SAAAA,EACdtX,CAAmB,CACnB0B,CAAmB,CACnBK,CAAoC,CACpCC,CAA8B,CAC9BC,CAA2C,CAC3Ce,CAAqB,CACrBuU,CAA6C,EAG7C,GAD6D,CACzDrV,GADkBhB,OAAOiB,IAAI,CAACH,CAAW,CAAC,EAAE,EAAEI,MAAM,CACrC,CACjBV,EAASsB,IAAI,CAAGA,EAChB,MACF,CAEA,IAAK,IAAMhC,KAAOgB,CAAW,CAAC,EAAE,CAAE,CAChC,IAgGIK,EAhGEC,EAAqBN,CAAW,CAAC,EAAE,CAAChB,EAAI,CACxCuB,EAA0BD,CAAkB,CAAC,EAAE,CAC/CE,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBF,GAYhCG,EACkB,OAAtBT,GAA4DU,SAA9BV,CAAiB,CAAC,EAAE,CAACjB,EAAI,CACnDiB,CAAiB,CAAC,EAAE,CAACjB,EAAI,CACzB,KACN,GAAIe,EAAe,CACjB,IAAMyV,EACJzV,EAAchB,cAAc,CAACqC,GAAG,CAACpC,GACnC,GAAIwW,EAAiC,CACnC,IAMInV,EANEoV,EACJF,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAezM,IAAAA,IAAS,QACxByM,EAAcG,MAAM,GAAKC,EAAAA,wBAAwB,CAACC,QAAQ,CAExDC,EAAyB,IAAI3U,IAAIsU,GAC/BM,EAAoBD,EAAuBzU,GAAG,CAACZ,GAMnDH,EAJuB,MAAM,CAA3BK,EAIa,CACbE,SAAU,KACVhB,IAJec,CAIVqV,CAJ2B,EAAE,CAUlChV,YAAa,KACbC,KAAM,KACNC,aAAc,KACdpB,QAZca,CAAgB,CAAC,EAAE,CAajC3B,eAAgB,IAAImC,IAAI4U,MAAAA,EAAAA,KAAAA,EAAAA,EAAmB/W,cAAc,EACzDf,aACF,EACSyX,GAAuBK,EAGjB,CACblV,SAAUkV,EAAkBlV,KAJqB,GAIb,CACpChB,IAAKkW,EAAkBlW,GAAG,CAI1BmB,YAAa+U,EAAkB/U,WAAW,CAC1CC,KAAM8U,EAAkB9U,IAAI,CAC5BC,aAAc6U,EAAkB7U,YAAY,CAC5ClC,eAAgB,IAAImC,IAAI4U,EAAkB/W,cAAc,EACxDc,QAASiW,EAAkBjW,OAAO,EAKrB,CACbe,SAAU,KACVhB,IAAK,KACLmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,IAAI4U,MAAAA,EAAAA,KAAAA,EAAAA,EAAmB/W,cAAc,EACzDc,QAAS,iBACT7B,CACF,EAIF6X,EAAuBxU,GAAG,CAACb,EAAUH,GAErCiV,EACEtX,EACAqC,EACAyV,EACAxV,EACAI,GAAsC,KACtCM,EACAuU,GAGF7V,EAASX,IALY2B,UAKE,CAACW,GAAG,CAACrC,EAAK6W,GACjC,QACF,CACF,CAGA,GAAyB,OAArBnV,EAA2B,CAE7B,IAAMqV,EAAWrV,CAAgB,CAAC,EAAE,CAC9Bb,EAAUa,CAAgB,CAAC,EAAE,CACnCL,EAAe,CACbO,SAAU,KACVhB,IAAKmW,EACLhV,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,YACpBrB,cACA7B,CACF,CACF,MAGEqC,CAHK,CAGU,CACbO,SAAU,KACVhB,IAAK,KACLmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,IACpBrB,QAAS,iBACT7B,CACF,EAGF,IAAMmD,EAAyBzB,EAASX,cAAc,CAACqC,GAAG,CAACpC,GACvDmC,EACFA,EAAuBE,GAAG,CAACb,EAAUH,GAErCX,EAASX,SAHiB,KAGH,CAACsC,GAAG,CAACrC,EAAK,IAAIkC,IAAI,CAAC,CAACV,EAAUH,EAAa,CAAC,GAGrEiV,EACEtX,EACAqC,OACAM,EACAL,EACAI,EACAM,EACAuU,EAEJ,CACF,aArKqC,WAI9B,yUCASS,qCAAAA,aATY,OASrB,SAASA,EAAexB,CAAgB,CAAErW,CAAQ,EACvD,GAAIqW,EAAShN,UAAU,CAAC,KAAM,CAC5B,IAAMyO,EAAU9X,EAAI+X,MAAM,CAAG/X,EAAImQ,QAAQ,CACzC,OAAO,IAAI6H,IACT,CAGCF,EAAQtO,QAAQ,CAAC,KAAOsO,EAAUA,EAAU,KAAE,EAEnD,CAEA,OAAO,IAAIE,IAAIlB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYT,GAAWrW,EAAIO,IAAI,CAChD,qBAPmG,2VC0HnF0X,2BAA2B,mBAA3BA,GAiBA9U,yCAAyC,mBAAzCA,aAvJ6B,WACC,WACT,WAEJ,OAMjC,SAAS+U,EACPrY,CAAmB,CACnB0B,CAAmB,CACnBK,CAAwB,CACxB7B,CAAgC,CAChCqX,CAA6C,CAC7Ce,CAAsB,EAEtB,GAAM,aACJrH,CAAW,CACXnQ,SAAUmB,CAAiB,CAC3B1B,KAAMU,CAAS,CACf+B,MAAI,CACL,CAAG9C,EACAmC,EAAeX,EACfoW,EAAoB/V,EAExB,IAAK,IAAI+J,EAAI,EAAGA,EAAImF,EAAY7O,MAAM,CAAE0J,GAAK,EAAG,CAC9C,IAAMyM,EAA2BtH,CAAW,CAACnF,EAAE,CACzCjI,EAAmBoN,CAAW,CAACnF,EAAI,EAAE,CAIrC0M,EAAc1M,IAAMmF,EAAY7O,MAAM,CAAG,EACzCI,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBoB,GAEhC4U,EACJX,EAAkB/W,cAAc,CAACqC,GAAG,CAACmV,GAEvC,GAAI,CAACE,EAGH,SAGF,IAAIC,EAAkBrW,EAAatB,MANL,QAMmB,CAACqC,GAAG,CAACmV,GACjDG,GAAmBA,IAAoBD,IAC1CC,EAAkB,IAAIxV,IAAIuV,GAC1BpW,EAAatB,MAFsD,QAExC,CAACsC,GAAG,CAACkV,EAAkBG,IAGpD,IAAMC,EAAyBF,EAAwBrV,GAAG,CAACZ,GACvDoW,EAAiBF,EAAgBtV,GAAG,CAACZ,GAEzC,GAAIgW,EAAa,CACf,GACEvW,GACC,EAAC2W,GACA,CAACA,EAAehW,QAAQ,EADzB,IAEoB+V,CAAAA,CAAAA,CACrB,CACA,IAAME,EAAkB5W,CAAiB,CAAC,EAAE,CACtCL,EAAMK,CAAiB,CAAC,EAAE,CAC1BJ,EAAUI,CAAiB,CAAC,EAAE,CAEpC2W,EAAiB,CACfhW,SAAU,KAGVhB,IACE0W,GAAiBO,IAAoB/V,EAAAA,gBAAgB,CAAGlB,EAAM,KAChEmB,YAAa,KACbC,KAAM,KACNC,aAAc,aACdpB,EACAd,eACEuX,GAAiBK,EACb,IAAIzV,IAAIyV,EAAuB5X,cAAc,EAC7C,IAAImC,gBACVlD,CACF,EAEI2Y,GAA0BL,GAC5BQ,GAAAA,EAAAA,OAD2C,qBAC3CA,EACEF,EACAD,EACA1X,GAGAqX,GACFhB,CAAAA,EAAAA,EAAAA,OADiB,sBACjBA,EACEtX,EACA4Y,EACAD,EACA1X,EACAgB,EACAe,EACAuU,GAIJmB,EAAgBrV,GAAG,CAACb,EAAUoW,EAChC,CACA,QACF,CAEKA,GAAmBD,IAMpBC,IAAmBD,IACrBC,EAAiB,CAPI,SAAyB,EAQnBhW,MAFkB,EAEV,CACjChB,IAAKgX,EAAehX,GAAG,CACvBmB,YAAa6V,EAAe7V,WAAW,CACvCC,KAAM4V,EAAe5V,IAAI,CACzBC,aAAc2V,EAAe3V,YAAY,CACzClC,eAAgB,IAAImC,IAAI0V,EAAe7X,cAAc,EACrDc,QAAS+W,EAAe/W,OAAO,EAEjC6W,EAAgBrV,GAAG,CAACb,EAAUoW,IAIhCvW,EAAeuW,EACfd,EAAoBa,EACtB,CACF,CAKO,SAASP,EACdpY,CAAmB,CACnB0B,CAAmB,CACnBK,CAAwB,CACxB7B,CAAgC,CAChCqX,CAAkC,EAElCc,EACErY,EACA0B,EACAK,EACA7B,EACAqX,GACA,EAEJ,CAEO,SAASjU,EACdtD,CAAmB,CACnB0B,CAAmB,CACnBK,CAAwB,CACxB7B,CAAgC,CAChCqX,CAAkC,EAElCc,EACErY,EACA0B,EACAK,EACA7B,EACAqX,EACA,GAEJ,8WC4PawB,WAAW,mBAAXA,GAoBAC,uBAAuB,mBAAvBA,GAPAC,iBAAiB,mBAAjBA,GAZAC,cAAc,mBAAdA,GACAC,iBAAiB,mBAAjBA,GATAC,EAAE,mBAAFA,GACAC,EAAE,mBAAFA,GAlXAC,UAAU,mBAAVA,GAsQGC,QAAQ,mBAARA,GA+BAC,cAAc,mBAAdA,GAXAC,iBAAiB,mBAAjBA,GAKAC,MAAM,mBAANA,GAPH1C,aAAa,mBAAbA,GAmBG2C,SAAS,mBAATA,GAkBMC,mBAAmB,mBAAnBA,GAdNC,wBAAwB,mBAAxBA,GA+GAC,cAAc,mBAAdA,KA9ZT,IAAMR,EAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAAO,CAsQ9D,SAASC,EACdQ,CAAK,EAEL,IACIC,EADAC,GAAO,EAGX,OAAQ,sCAAI5H,EAAAA,MAAAA,GAAAA,EAAAA,EAAAA,EAAAA,EAAAA,IAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAKV,OAJK4H,IACHA,EADS,CACF,EACPD,EAASD,KAAM1H,IAEV2H,CACT,CACF,CAIA,IAAME,EAAqB,6BACdlD,EAAgB,GAAiBkD,EAAmBzU,IAAI,CAACtF,GAE/D,SAASsZ,IACd,GAAM,UAAEU,CAAQ,UAAEC,CAAQ,MAAEC,CAAI,CAAE,CAAGC,OAAO9D,QAAQ,CACpD,OAAU2D,EAAS,KAAIC,GAAWC,EAAO,IAAMA,EAAbA,EAAoB,CACxD,CAEO,SAASX,IACd,GAAM,MAAEhZ,CAAI,CAAE,CAAG4Z,OAAO9D,QAAQ,CAC1B0B,EAASuB,IACf,OAAO/Y,EAAK6Z,SAAS,CAACrC,EAAO9V,MAAM,CACrC,CAEO,SAASoX,EAAkBgB,CAA2B,EAC3D,MAA4B,iBAAdA,EACVA,EACAA,EAAUC,WAAW,EAAID,EAAUE,IAAI,EAAI,SACjD,CAEO,SAASf,EAAUgB,CAAmB,EAC3C,OAAOA,EAAIC,QAAQ,EAAID,EAAIE,WAAW,CAGjC,SAAShB,EAAyB1Z,CAAW,EAClD,IAAM2a,EAAW3a,EAAIuJ,KAAK,CAAC,KAG3B,OAFmBoR,CAAQ,CAAC,EAAE,CAMzBpH,MAFD,CAEQ,CAAC,MAAO,KACfA,OAAO,CAAC,SAAU,MACpBoH,CAAAA,CAAS,EAAE,CAAI,IAAGA,EAASrJ,KAAK,CAAC,GAAG1F,IAAI,CAAC,KAAS,GAEvD,CAFwD,eAIlC6N,EAIpBmB,CAAgC,CAAEC,CAAM,EAUxC,IAAML,EAAMK,EAAIL,GAAG,EAAKK,EAAIA,GAAG,EAAIA,EAAIA,GAAG,CAACL,GAAG,CAE9C,GAAI,CAACI,EAAIE,eAAe,EAAE,MACpBD,EAAIA,GAAG,EAAIA,EAAIR,SAAS,CAEnB,CAFqB,UAGf,MAAMZ,EAAoBoB,EAAIR,SAAS,CAAEQ,EAAIA,GAAG,CAC7D,EAEK,CAAC,EAGV,IAAMhP,EAAQ,MAAM+O,EAAIE,eAAe,CAACD,GAExC,GAAIL,GAAOhB,EAAUgB,GACnB,GADyB,IAClB3O,EAGT,GAAI,CAACA,EAIH,KAJU,CAIJ,qBAAkB,CAAlB,MAHW,IAAGwN,EAClBuB,GACA,+DAA8D/O,EAAM,cAChE,+DAAiB,GAazB,OAAOA,CACT,CAEO,IAAMoN,EAA4B,aAAvB,OAAO8B,YACZ7B,EACXD,GACC,CAAC,OAAQ,UAAW,mBAAmB,CAAW+B,KAAK,CACtD,GAA2C,YAA/B,OAAOD,WAAW,CAACE,EAAO,CAGnC,OAAMrC,UAAoBsC,MAAO,CACjC,MAAMnC,UAAuBmC,MAAO,CACpC,MAAMlC,UAA0BkC,MAGrCC,YAAYC,CAAY,CAAE,CACxB,KAAK,GACL,IAAI,CAACC,IAAI,CAAG,SACZ,IAAI,CAACd,IAAI,CAAG,oBACZ,IAAI,CAACe,OAAO,CAAI,gCAA+BF,CACjD,CACF,CAEO,MAAMtC,UAA0BoC,MACrCC,YAAYC,CAAY,CAAEE,CAAe,CAAE,CACzC,KAAK,GACL,IAAI,CAACA,OAAO,CAAI,wCAAuCF,EAAK,IAAGE,CACjE,CACF,CAEO,MAAMzC,UAAgCqC,MAE3CC,aAAc,CACZ,KAAK,GACL,IAAI,CAACE,IAAI,CAAG,SACZ,IAAI,CAACC,OAAO,CAAI,mCAClB,CACF,CAWO,SAAS3B,EAAe4B,CAAY,EACzC,OAAOxS,KAAKC,SAAS,CAAC,CAAEsS,QAASC,EAAMD,OAAO,CAAEE,MAAOD,EAAMC,KAAK,EACpE,4GCjcgBhY,qCAAAA,aAXmB,OAOnC,SAASiY,EAAkBvO,CAAQ,EACjC,OAAO,KAAiB,IAAVA,CAChB,CAEO,SAAS1J,EACd1D,CAA2B,CAC3BG,CAAgB,MAGKA,EAwDbA,EAxDR,IAAMyb,EAAezb,MAAAA,GAAAA,EAAQyb,YAAAA,GAARzb,EAEjB0b,EAAU7b,EAAM6b,OAAO,CAE3B,GAAIF,EAAexb,EAAQmD,GAJkB,QAIP,EAAG,CAEvC,IAAMwY,EAAcC,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB/b,EAAMM,IAAI,CAAEH,EAAQmD,WAAW,EAClEwY,EAEFD,EAAUC,EACD,IAETD,EAAU7b,CALK,CAKCuD,CAFG,WAEHA,CAGpB,CAEA,MAAO,CAELA,aAAcoY,EAAexb,EAAQoD,YAAY,EAC7CpD,EAAQoD,YAAY,GAAKvD,EAAMuD,YAAY,CACzCvD,EAAMuD,YAAY,CAClBpD,EAAQoD,YAAY,CACtBvD,EAAMuD,YAAY,CACtBuN,QAAS,CACPC,YAAa4K,EAAexb,EAAQ4Q,WAAW,EAC3C5Q,EAAQ4Q,WAAW,CACnB/Q,EAAM8Q,OAAO,CAACC,WAAW,CAC7BiL,cAAeL,EAAexb,EAAQ6b,aAAa,EAC/C7b,EAAQ6b,aAAa,CACrBhc,EAAM8Q,OAAO,CAACkL,aAAa,CAC/BpL,2BAA4B+K,EAC1Bxb,EAAQyQ,0BAA0B,EAEhCzQ,EAAQyQ,0BAA0B,CAClC5Q,EAAM8Q,OAAO,CAACF,0BAA0B,EAG9CqL,kBAAmB,CACjBC,QAAON,MACHD,EAAexb,MAAAA,EAAAA,KAAAA,EAAAA,EAASgc,kBAAkB,GAExCnc,CADA,CACMic,iBAAiB,CAACC,KAAK,EAGnCE,CADI,cACYjc,EAAQic,cAAc,EAAI,GAC1C5Y,aAAcoY,EAEV,EACQpY,YAAY,EAA6B,KAAzBrD,EAAQqD,IADI,QACQ,CAE1C6Y,mBAAmBlc,EAAQqD,YAAY,CAACgO,KAAK,CAAC,IAC9CxR,EAAMic,iBAAiB,CAACzY,YAAY,CAEtC,KACJ8Y,aAAcV,EACVzb,OAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASgc,kBAAAA,EAAThc,EAA+BH,EAAMic,iBAAiB,CAACK,YAAY,CAEnE,EAAE,EAGR9b,MAAOL,EAAQK,KAAK,CAAGL,EAAQK,KAAK,CAAGR,EAAMQ,KAAK,CAClD+b,cAAepc,EAAQoc,aAAa,CAChCpc,EAAQoc,aAAa,CACrBvc,EAAMuc,aAAa,CAEvBjc,KAAMqb,EAAexb,EAAQmD,WAAW,EACpCnD,EAAQmD,WAAW,CACnBtD,EAAMM,IAAI,SACdub,CACF,CACF,0UC5EgBW,qCAAAA,aAVkB,OAU3B,SAASA,EACdxc,CAA2B,CAC3ByQ,CAAsB,CACtBzP,CAA4B,EAY5B,MAAO6P,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkB7Q,EAAO,CAAC,EAAGA,EAAMuD,YAAY,EAAE,EAC1D,yOCvBO,SAASkZ,EAAetM,CAAY,SAQPA,CAKpC,2FAbgBsM,qCAAAA,OAJY,sUCKZzF,qCAAAA,aALc,WACa,OAIpC,SAASA,EAAY7G,CAAY,CAAEuM,CAAkB,EAC1D,MAAOC,GAAAA,EAAAA,0BAAAA,EACLxQ,CAEI+D,EAAAA,EAAAA,CAFmDwM,GACnDvM,CAAIA,QACJD,EAAcC,EAN6C,IAMvCyM,gXCsIZlb,oBAAoB,mBAApBA,GA9EAmb,iBAAiB,mBAAjBA,GAwehB,OAwBC,mBAxBuBC,GAnfRC,aAAa,mBAAbA,uCA7CT,YAKA,WAEwB,WAEG,WAK3B,WACiD,eAKjD,WACe,WACM,WACO,WACF,WACD,WACG,UACJ,WACH,WACM,WAEG,WAK9B,WAC2D,WACpB,SACb,OAEjC,IAAMC,EAEF,CAAC,EAEE,SAASD,EAAc7c,CAAQ,EACpC,OAAOA,EAAI+X,MAAM,GAAKoC,OAAO9D,QAAQ,CAAC0B,MAAM,CAUvC,SAAS4E,EAAkBpc,CAAY,MAMxCP,EAJJ,GAAI+c,CAAAA,EAAAA,EAAAA,KAAAA,EAAM5C,OAAO6C,SAAS,CAACC,SAAS,EAClC,CADqC,MAC9B,KAIT,GAAI,CACFjd,EAAM,IAAIgY,IAAIlB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYvW,GAAO4Z,OAAO9D,QAAQ,CAAC9V,IAAI,CACvD,CAAE,MAAO2H,EAAG,CAGV,MAAM,qBAEL,CAFK,MACH,oBAAmB3H,EAAK,8CADrB,+DAEN,EACF,QAQA,EAAkBP,GACT,GADe,EAIjBA,CACT,CAEA,SAASkd,EAAe,CAIvB,EAJuB,IACtBC,gBAAc,CAGf,CAJuB,EA6CtB,MAxCAC,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmB,KAOjB,GAAM,MAAEhd,CAAI,SAAEwQ,CAAO,cAAEvN,CAAY,CAAE,CAAG8Z,EAClCE,EAAe,CACnB,GAAIzM,EAAQF,0BAA0B,CAAGyJ,OAAOmD,OAAO,CAACxd,KAAK,CAAG,CAAC,CAAC,CAIlEyd,MAAM,EACNC,gCAAiCpd,CACnC,EAEEwQ,EAAQC,WAAW,EAGnBrQ,CAAAA,CAFA,CAEAA,EAAAA,iBAAAA,EAAkB,IAAIwX,IAAImC,OAAO9D,QAAQ,CAAC9V,IAAI,KAAO8C,GAGrDuN,EAAQC,SAFR,EAEmB,EAAG,EACtBsJ,OAAOmD,OAAO,CAACG,KANgF,IAMvE,CAACJ,EAAc,GAAIha,IAE3C8W,OAAOmD,OAAO,CAACI,YAAY,CAACL,EAAc,GAAIha,EAElD,EAAG,CAAC8Z,EAAe,EAEnBtN,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAQV,EAAG,CAACsN,EAAexB,OAAO,CAAEwB,EAAe/c,IAAI,CAAC,EAEzC,IACT,CAEO,SAASoB,IACd,MAAO,CACLiB,SAAU,KACVhB,IAAK,KACLmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,IACpBrB,QAAS,KACT7B,YAAa,CAAC,CAChB,CACF,CAEA,SAAS8d,EAA+BC,CAAS,EACnC,MAARA,IAAcA,EAAO,EAAC,EAC1B,IAAMC,EAAe1D,OAAOmD,OAAO,CAACxd,KAAK,CACnCyd,EAAOM,MAAAA,EAAAA,KAAAA,EAAAA,EAAcN,IAAI,CAC3BA,IACFK,EAAKL,IAAI,CAAGA,CAAAA,EAEd,IAAMC,EACJK,MAAAA,EAAAA,KAAAA,EAAAA,EAAcL,+BAA+B,CAK/C,OAJIA,IACFI,EAAKJ,2BAD8B,IACC,CAAGA,CAAAA,EAGlCI,CACT,CAEA,SAASxO,EAAK,CAIb,EAJa,kBACZ0O,CAAa,CAGd,CAJa,EAQNjb,EAAyB,OAAlBib,EAAyBA,EAAcjb,IAAI,CAAG,KACrDC,EACJgb,SAAyBA,EAAchb,YAAY,CAAG,KAGlDib,EAAuC,OAAjBjb,EAAwBA,EAAeD,EAKnE,MAAOmb,CAAAA,EAAAA,EAAAA,gBAAAA,EAAiBnb,EAAMkb,EAChC,CAKA,SAASE,EAAO,CAQf,EARe,IA+QVpb,EA/QU,aACdqb,CAAW,aACXC,CAAW,aACXC,CAAW,CAKZ,CARe,EASRte,EAAQue,CAAAA,EAAAA,EAAAA,cAAAA,EAAeH,GACvB,cAAE7a,CAAY,CAAE,CAAGvD,EAEnB,cAAEmB,CAAY,UAAEkP,CAAQ,CAAE,CAAGP,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACzC,IAAM5P,EAAM,IAAIgY,IACd3U,EACgC,YAAhC,CAA6C8W,CAAoB,GAApC,CAGxB,CAELlZ,aAAcjB,EAAIiB,YAAY,CAC9BkP,SAAUmO,CAAAA,EAAAA,EAAAA,WAAAA,EAAYte,EAAImQ,QAAQ,EAC9BoM,CAAAA,EAAAA,EAAAA,cAAAA,EAAevc,EAAImQ,QAAQ,EAC3BnQ,EAAImQ,QAAQ,CAEpB,EAAG,CAAC9M,EAAa,EAqBjBwM,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAKR,SAAS0O,EAAezR,CAA0B,MAG7CqN,CADArN,GAAM0R,SAAS,EAChB,CAAqB,CAArB,MAACrE,EAAAA,OAAOmD,OAAO,CAACxd,KAAAA,EAAK,OAApBqa,EAAsBqD,+BAAAA,GACvB,CAOFV,EAAc2B,cAAc,MAAGjc,EAE/Bkc,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtB7M,KAAM8M,EAAAA,cAAc,CACpB3e,IAAK,IAAIgY,IAAImC,OAAO9D,QAAQ,CAAC9V,IAAI,EACjCH,KAAM+Z,OAAOmD,OAAO,CAACxd,KAAK,CAAC0d,+BAA+B,GAE9D,CAIA,OAFArD,OAAOyE,gBAAgB,CAAC,WAAYL,GAE7B,KACLpE,OAAO0E,mBAAmB,CAAC,WAAYN,EACzC,CACF,EAAG,EAAE,EAEL1O,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAGR,SAASiP,EACPhS,CAAyC,EAEzC,IAAMyO,EAAQ,WAAYzO,EAAQA,EAAMiS,MAAM,CAAGjS,EAAMyO,KAAK,CAC5D,GAAIyD,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBzD,GAAQ,CAC1BzO,EAAMa,cAAc,GACpB,IAAM3N,EAAMif,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB1D,EAIhC2D,CAHiBC,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyB5D,KAGzB6D,EAAAA,YAAY,CAACC,IAAI,CACpCC,CADsC,CACtCA,uBAAuB,CAACD,IAAI,CAACrf,EAAK,CAAC,GAEnCsf,EAAAA,uBAAuB,CAAC/L,OAAO,CAACvT,EAAK,CAAC,EAE1C,CACF,CAIA,OAHAma,OAAOyE,gBAAgB,CAAC,QAASE,GACjC3E,OAAOyE,gBAAgB,CAAC,qBAAsBE,GAEvC,KACL3E,OAAO0E,mBAAmB,CAAC,QAASC,GACpC3E,OAAO0E,mBAAmB,CAAC,qBAAsBC,EACnD,CACF,EAAG,EAAE,EAYL,GAAM,SAAElO,CAAO,CAAE,CAAG9Q,EACpB,GAAI8Q,EAAQkL,aAAa,CAAE,CAEzB,GAAIgB,EAAc2B,cAAc,GAAKpb,EAAc,CACjD,IAAMgT,EAAW8D,OAAO9D,QAAQ,CAC5BzF,EAAQC,WAAW,CACrBwF,CADuB,CACd1M,MAAM,CAACtG,GAEhBgT,EAAS9C,OAAO,CAAClQ,GAGnByZ,EAAc2B,cAAc,CAAGpb,CACjC,CAIAyK,CAAAA,EAAAA,EAAAA,GAAAA,EAAIyR,EAAAA,kBAAkB,CACxB,CAEA1P,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAM2P,EAAoBrF,OAAOmD,OAAO,CAACG,SAAS,CAACgC,IAAI,CAACtF,OAAOmD,OAAO,EAChEoC,EAAuBvF,OAAOmD,OAAO,CAACI,YAAY,CAAC+B,IAAI,CAC3DtF,OAAOmD,OAAO,EAIVqC,EAAiC,QAKnCxF,EAFF,IAAM5Z,EAAO4Z,OAAO9D,QAAQ,CAAC9V,IAAI,CAC3BH,EACgB,OAApB+Z,EAAAA,OAAOmD,OAAO,CAACxd,KAAAA,EAAK,OAApBqa,EAAsBqD,+BAA+B,CAEvDlH,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KACdoI,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtB7M,KAAM8M,EAAAA,cAAc,CACpB3e,IAAK,IAAIgY,IAAIhY,MAAAA,EAAAA,EAAOO,EAAMA,QAC1BH,CACF,EACF,EACF,EAOA+Z,OAAOmD,OAAO,CAACG,SAAS,CAAG,SAASA,CACzB,CACTmC,CAAe,CACf5f,CAAyB,SAGrB4d,MAAAA,EAAAA,KAAAA,EAAAA,EAAML,IAAAA,IAAQK,MAAAA,EAAAA,KAAAA,EAAAA,EAAMiC,EAAAA,GAAI,CAI5BjC,EAAOD,EAA+BC,GAElC5d,GACF2f,EADO,IALAH,EAAkB5B,EAAMgC,EAAS5f,EAU5C,EAOAma,OAAOmD,OAAO,CAACI,YAAY,CAAG,SAASA,CAC5B,CACTkC,CAAe,CACf5f,CAAyB,SAGrB4d,MAAAA,EAAAA,KAAAA,EAAAA,EAAML,IAAAA,IAAQK,MAAAA,EAAAA,KAAAA,EAAAA,EAAMiC,EAAAA,GAAI,CAG5BjC,EAAOD,EAA+BC,GAElC5d,GACF2f,EADO,IAJAD,EAAqB9B,EAAMgC,EAAS5f,EAQ/C,EAOA,IAAM8f,EAAa,IACjB,GAAKhT,CAAD,CAAOhN,KAAK,EAAE,GAMd,CAACgN,EAAMhN,KAAK,CAACyd,IAAI,CAAE,YACrBpD,OAAO9D,QAAQ,CAAC0J,MAAM,GAMxBzJ,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KACd0J,CAAAA,EAAAA,EAAAA,sBAAAA,EACE7F,OAAO9D,QAAQ,CAAC9V,IAAI,CACpBuM,EAAMhN,KAAK,CAAC0d,+BAA+B,CAE/C,GACF,EAIA,OADArD,OAAOyE,gBAAgB,CAAC,WAAYkB,GAC7B,KACL3F,OAAOmD,OAAO,CAACG,SAAS,CAAG+B,EAC3BrF,OAAOmD,OAAO,CAACI,YAAY,CAAGgC,EAC9BvF,OAAO0E,mBAAmB,CAAC,WAAYiB,EACzC,CACF,EAAG,EAAE,EAEL,GAAM,OAAExf,CAAK,CAAEF,MAAI,SAAEub,CAAO,mBAAEI,CAAiB,CAAE,CAAGjc,EAE9CmgB,EAAerQ,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IACpBsQ,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB5f,EAAOF,CAAI,CAAC,EAAE,EACpC,CAACE,EAAOF,EAAK,EAGV+f,EAAavQ,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,IAClBwQ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBhgB,GACxB,CAACA,EAAK,EAEHigB,EAAsBzQ,CAAAA,EAAAA,EAAAA,OAAO,EAAC,KAC3B,CACL0Q,WAAYlgB,EACZmgB,gBAAiBjgB,EACjBkgB,kBAAmB,KAGnBxgB,IAAKqD,EACP,EACC,CAACjD,EAAME,EAAO+C,EAAa,EAExBod,EAA4B7Q,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ,KACjC,MACLxP,oBACA2b,UACAJ,EACF,EACC,CAACvb,EAAM2b,EAAmBJ,EAAQ,EAGrC,GAAIsE,SAAuB,CAOzB,GAAM,CAACnC,EAAe4C,EAAQ,CAAGT,EACjCpd,EAAO,KAAPA,GAAO,EAACuM,EAAAA,CAAmB0O,GAA3Bjb,WAA0Cib,GAAxB4C,EACpB,MACE7d,CADK,CACE,KAGT,IAAI8d,EACF,QADEA,CACF,EAACC,EAAAA,MADCD,UACe,YACd9d,EACAvC,EAAMmB,GAAG,CACV,UAACof,EAAAA,kBAAkB,EAACzgB,KAAMA,OAwC9B,OAVEugB,EACE,QADFA,EACGG,EAAAA,OADHH,MACgB,EACZI,eAAgB3C,CAAW,CAAC,EAAE,CAC9B4C,YAAa5C,CAAW,CAAC,EAAE,UAE1BuC,IAML,iCACE,UAACzD,EAAAA,CAAeC,eAAgBrd,IAChC,UAACmhB,EAAAA,CAAAA,GACD,UAACC,EAAAA,iBAAiB,CAACjK,QAAQ,EAAC/J,MAAOiT,WACjC,UAACgB,EAAAA,eAAe,CAAClK,QAAQ,EAAC/J,MAAOiD,WAC/B,UAACiR,EAAAA,mBAAmB,CAACnK,QAAQ,EAAC/J,MAAOjM,WACnC,UAACogB,EAAAA,yBAAyB,CAACpK,QAAQ,EACjC/J,MAAOuT,WAOP,UAACpM,EAAAA,gBAAgB,CAAC4C,QAAQ,EAAC/J,MAAOoS,EAAAA,uBAAuB,UACvD,UAACgC,EAAAA,mBAAmB,CAACrK,QAAQ,EAAC/J,MAAOmT,WAClCM,gBASnB,CAEe,SAAS/D,EAAU,CAQjC,EARiC,gBAChCsB,CAAW,CACXqD,8BAA+B,CAACC,EAAsBC,EAAkB,aACxEtD,CAAW,CAKZ,CARiC,EAWhC,MAFAuD,GAAAA,EAAAA,oBAAAA,IAGE,UAACZ,EAAAA,aAAa,EAGZC,eAAgBY,EAAAA,OAAkB,UAElC,UAAC1D,EAAAA,CACCC,YAAaA,EACbC,YAAaA,EACbC,YAAa,CAACoD,EAAsBC,EAAkB,IAI9D,CAEA,IAAMG,EAAgB,IAAIvW,IACtBwW,EAAsB,IAAIxW,IAa9B,SAAS4V,IACP,GAAM,EAAGa,EAAY,CAAG1N,EAAAA,OAAK,CAACtE,QAAQ,CAAC,GACjCiS,EAAqBH,EAAcI,IAAI,OAC7CnS,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KACR,IAAMoS,EAAU,IAAMH,EAAY,GAAOrc,EAAI,GAK7C,OAJAoc,EAAoBK,GAAG,CAACD,GACpBF,IAAuBH,EAAcI,IAAI,EAAE,IAGxC,KACLH,EAAoBM,MAAM,CAACF,EAC7B,CACF,EAAG,CAACF,EAAoBD,EAAY,EAK7B,IAAIF,EAAc,CAACtW,GAAG,CAAC,CAAC/K,EAAMoL,IAAAA,CACnC,SAAC0D,CADkC1D,MAClC0D,CAECC,IAAI,aACJ/O,KAAO,GAAEA,EAET6hB,KAFgBC,MAEL,QAJN1W,GAUX,CAxCAsC,WAAWqU,eAAe,CAAG,SAAU/hB,CAAY,EACjD,IAAIgiB,EAAMX,EAAcI,IAAI,CAO5B,OANAJ,EAAcM,GAAG,CAAC3hB,GACdqhB,EAAcI,IAAI,GAAKO,GACzBV,EAD8B,OACH,CAAC,GAAQW,KAI/B/V,QAAQC,OAAO,EACxB,wOC1kBO,SAAS2D,EAAUJ,CAAY,EACpC,IAAMwS,EAAYxS,EAAKyS,OAAO,CAAC,KACzBC,EAAa1S,EAAKyS,OAAO,CAAC,KAC1BE,EAAWD,EAAa,CAAC,IAAMF,CAAAA,CAAY,GAAKE,EAAaF,CAAAA,CAAAA,CAAQ,OAE3E,GAAgBA,EAAY,CAAC,EACpB,CACLtS,SAAUF,EAAKmK,SAAS,CAAC,EAAGwI,EAAWD,EAAaF,GACpDrS,MAAOwS,EACH3S,EAAKmK,SAAS,CAACuI,EAAYF,EAAY,CAAC,EAAIA,OAAYjgB,GACxD,GACJe,KAAMkf,EAAY,CAAC,EAAIxS,EAAKqB,KAAK,CAACmR,GAAa,EACjD,EAGK,CAAEtS,SAAUF,EAAMG,MAAO,GAAI7M,KAAM,EAAG,CAC/C,sFAhBgB8M,qCAAAA,6GCDAiO,qCAAAA,aAJc,OAIvB,SAASA,EAAYrO,CAAY,EACtC,MAAO4S,GAAAA,EAAAA,aAAAA,EAAc5S,EAH4C,GAInE,CAD6ByM,+WCGpBoG,sBAAsB,mBAAtBA,EAAAA,sBAAsB,EAFlBC,6BAA6B,mBAA7BA,GAgBGC,UAAU,mBAAVA,GAJAjG,KAAK,mBAALA,aAlBuB,OAGjCkG,EACJ,8EAEWF,EAAgCD,EAAAA,sBAAsB,CAACI,MAAM,CAQ1E,SAASC,EAAmBlG,CAAiB,EAC3C,OAAO6F,EAAAA,sBAAsB,CAACxd,IAAI,CAAC2X,EACrC,CAEO,SAASF,EAAME,CAAiB,EACrC,OAAOmG,MAR+B,CAQpBnG,IAAckG,EAAmBlG,EACrD,CAEO,SAAS+F,EAAW/F,CAAiB,SAC1C,EAZkC3X,EAY9B8d,GAAWnG,GACN,MAELkG,EAAmBlG,CAHI,EAIlB,SAD0B,IAIrC,2HCvBgBtE,qCAAAA,aALqB,OAK9B,SAASA,EACdpX,CAAmB,CACnBK,CAAwB,CACxBC,CAA8B,EAG9B,IAAK,IAAMhB,KAAOgB,CAAW,CAAC,EAAE,CAAE,CAChC,IAAMO,EAA0BP,CAAW,CAAC,EAAE,CAAChB,EAAI,CAAC,EAAE,CAChDwB,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBF,GAChCiV,EACJzV,EAAchB,cAAc,CAACqC,GAAG,CAACpC,GACnC,GAAIwW,EAAiC,CACnC,IAAIK,EAAyB,IAAI3U,IAAIsU,GACrCK,EAAuByK,MAAM,CAAC9f,GAC9Bd,EAASX,cAAc,CAACsC,GAAG,CAACrC,EAAK6W,EACnC,CACF,CACF,wUCoLgB2L,qCAAAA,aAxMW,WACM,WAO1B,WAoBA,WACwB,WACG,WACA,WACU,WACA,WAEd,WACgB,WACT,WACa,WACZ,WACU,WAIzC,WAC0B,WACJ,WACkB,WAChB,WACH,WAIrB,SAC+B,OAxCtC,GAAM,iBAAEC,CAAe,CAAEC,6BAA2B,CAAEC,aAAW,CAAE,CAG7DC,EAFJ,KAA0B,EAsD5B,EAlDMA,CAA0C,OAFnCA,KAoDEC,EACb5jB,CAA2B,CAC3B6b,CAAwC,CACxC,CAA4C,EAA5C,IAqCIuD,EAaAyE,EAlDJ,UAAEC,CAAQ,YAAEC,CAAU,CAAsB,CAA5C,EAEMC,EAAsBP,IACtBpR,EAAOhB,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiCyS,GAKxCpS,EACU,cAAdW,EAAKN,IAAI,CAAmBI,CAAAA,EAAAA,EAAAA,cAAAA,EAAe4R,EAAY1R,GAAQ0R,EAE3DE,EAAO,MAAMP,EAAYhS,EAAU,CAAEsS,qBAAoB,GAEzDtJ,EAAM,MAAMwJ,MAAM,GAAI,CAC1B/I,OAAQ,OACRgJ,QAAS,CACPC,OAAQC,EAAAA,uBAAuB,CAC/B,CAACC,EAAAA,aAAa,CAAC,CAAER,EACjB,CAACS,EAAAA,6BAA6B,CAAC,CAAEC,mBAC/Bvb,KAAKC,SAAS,CAAClJ,EAAMM,IAAI,GAE3B,GAAI6L,CAIC,CAAC,CACN,GALkC,EAM9B,CALA,CAEC,EAIEsY,QAAQ,CAAC,CAAE5I,CACd,EACA,CAAC,CAAC,OAERoI,CACF,GAEMS,EAAiBhK,EAAIyJ,OAAO,CAAChhB,GAAG,CAAC,qBACjC,CAACoT,EAAUoO,EAAc,CAAGD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAgBjb,KAAK,CAAC,OAAQ,EAAE,CAElE,OAAQkb,GACN,IAAK,OACHvF,EAAeE,EAAAA,YAAY,CAACC,IAAI,CAChC,KACF,KAAK,UACHH,EAAeE,EAAAA,YAAY,CAAC7L,OAAO,CACnC,KACF,SACE2L,EAAe1c,MACnB,CAEA,IAAMkiB,EAAc,CAAC,CAAClK,EAAIyJ,OAAO,CAAChhB,GAAG,CAAC0hB,EAAAA,wBAAwB,EAE9D,GAAI,CACF,IAAMC,EAAoB7b,KAAK8b,KAAK,CAClCrK,EAAIyJ,OAAO,CAAChhB,GAAG,CAAC,yBAA2B,YAE7C0gB,EAAmB,CACjBmB,MAAOF,CAAiB,CAAC,EAAE,EAAI,EAAE,CACjCG,IAAK,CAAC,CAACH,CAAiB,CAAC,EAAE,CAC3BI,OAAQJ,CAAiB,CAAC,EAC5B,CACF,CAAE,MAAOtP,EAAG,CACVqO,EAAmB,CACjBmB,MAAO,EAAE,CACTC,KAAK,EACLC,OAAQ,EACV,CACF,CAEA,IAAMC,EAAmB5O,EACrBwB,CAAAA,EAAAA,EAAAA,cAAAA,EACExB,EACA,IAAI2B,IAAIlY,EAAMuD,YAAY,CAAE8W,OAAO9D,QAAQ,CAAC9V,IAAI,GAElDiC,OAEE0iB,EAAc1K,EAAIyJ,OAAO,CAAChhB,GAAG,CAAC,gBAEpC,GAAIiiB,MAAAA,EAAAA,KAAAA,EAAAA,EAAa7b,UAAU,CAAC8a,EAAAA,uBAAuB,EAAG,CACpD,IAAMgB,EAAiC,MAAM7B,EAC3C7W,QAAQC,OAAO,CAAC8N,GAChB,CAAE4K,WAAAA,EAAAA,UAAU,CAAEC,iBAAAA,EAAAA,gBAAgB,qBAAEvB,CAAoB,UAGlDzN,EAEK,CACLiP,OAHU,UAGQC,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBJ,EAASK,CAAC,mBAChDP,eACA/F,mBACAyE,cACAe,CACF,EAGK,CACLe,aAAcN,EAASxd,CAAC,CACxB2d,iBAAkBC,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBJ,EAASK,CAAC,mBAChDP,eACA/F,mBACAyE,EACAe,aACF,CACF,CAGA,GAAIlK,EAAIjD,MAAM,EAAI,IAQhB,CARqB,KAQf,qBAAgB,CAAhB,MAAUgE,eAJd2J,EACI,MAAM1K,EAAIkL,IAAI,GACd,wDAEA,+DAAe,GAGvB,MAAO,kBACLT,eACA/F,mBACAyE,cACAe,CACF,CACF,CAMO,SAASrB,EACdvjB,CAA2B,CAC3ByQ,CAA0B,EAE1B,GAAM,SAAE7D,CAAO,QAAEiZ,CAAM,CAAE,CAAGpV,EACtBtQ,EAA+B,CAAC,EAElCE,EAAcL,EAAMM,IAAI,CAE5BH,EAAQyQ,0BAA0B,CAAG,GAMrC,IAAMiL,EACJ7b,EAAM6b,OAAO,EAAIiK,CAAAA,EAAAA,EAAAA,iCAAAA,EAAkC9lB,EAAMM,IAAI,EACzDN,EAAM6b,OAAO,CACb,KAEA9b,EAAcgmB,KAAKC,GAAG,GAE5B,OAAOpC,EAAkB5jB,EAAO6b,EAASpL,GAAQ3D,IAAI,CACnD,cAQMmZ,EARC,cACLN,CAAY,CACZH,iBAAkBvlB,CAAU,kBAC5BklB,CAAgB,cAChB/F,CAAY,aACZwF,CAAW,kBACXf,CAAgB,CACjB,GAiBC,GAbIsB,IACE/F,IAAiBE,EAAAA,QADD,IACa,CAAC7L,OAAO,EAAE,EACnC3C,OAAO,CAACC,WAAW,EAAG,EAC5B5Q,EAAQ4Q,WAAW,EAAG,IAEtB/Q,EAAM8Q,OAAO,CAACC,WAAW,EAAG,EAC5B5Q,EAAQ4Q,WAAW,EAAG,GAIxB5Q,EAAQoD,YAAY,CADpB0iB,EACuBA,CADRvlB,EAAAA,EAAAA,iBAAAA,EAAkBykB,GAAkB,IAIjD,CAACllB,QAIH,CAHA2M,EAAQ+Y,CADO,EAIXR,GACKtU,CAAAA,EAAAA,EAAAA,UADa,OACbA,EACL7Q,EACAG,EACAglB,EAAiB1kB,IAAI,CACrBT,EAAM8Q,OAAO,CAACC,WAAW,EAGtB/Q,EAGT,GAA0B,UAAtB,OAAOC,EAIT,OAFA2M,EAAQ+Y,GAED9U,GAAAA,EAAAA,iBAAAA,EACL7Q,EACAG,EACAF,EACAD,EAAM8Q,OAAO,CAACC,WAAW,EAI7B,IAAMmV,EACJrC,EAAiBmB,KAAK,CAAC7iB,MAAM,CAAG,GAChC0hB,EAAiBoB,GAAG,EACpBpB,EAAiBqB,MAAM,CAEzB,IAAK,IAAMvkB,KAAwBV,EAAY,CAC7C,GAAM,CACJK,KAAMU,CAAS,CACfH,SAAUmB,CAAiB,MAC3Be,CAAI,cACJ3B,CAAY,CACb,CAAGT,EAEJ,GAAI,CAACS,EAKH,OAHA+kB,KAFiB,GAETC,GAAG,CAAC,8BACZxZ,EAAQ+Y,GAED3lB,EAIT,IAAMuB,EAAUC,GAAAA,EAAAA,2BAAAA,EAEd,CAAC,GAAG,CACJnB,EACAW,EACAilB,GAA8BjmB,EAAMuD,UAArB0iB,EAAiC,EAGlD,GAAgB,MAAM,CAAlB1kB,EAGF,OAFAqL,EAAQ+Y,GAEDnJ,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsBxc,EAAOyQ,EAAQzP,GAG9C,GAAIkQ,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4B7Q,EAAakB,GAG3C,OAHqD,EAC7CokB,GAED9U,CAAAA,EAAAA,EAAAA,iBAAAA,EACL7Q,EACAG,EACA8lB,GAAgBjmB,EAAMuD,YAAY,CAClCvD,EAAM8Q,OAAO,CAACC,WAAW,EAK7B,GAA0B,OAAtB/O,EAA4B,CAC9B,IAAML,EAAMK,CAAiB,CAAC,EAAE,CAC1BxB,EAAmBkB,CAAAA,EAAAA,EAAAA,oBAAAA,IACzBlB,EAAMmB,GAAG,CAAGA,EACZnB,EAAMsC,WAAW,CAAG,KACpBtC,EAAMoB,OAAO,CAAGI,CAAiB,CAAC,EAAE,CACpCqV,GAAAA,EAAAA,6BAAAA,EACEtX,EACAS,OACA,EAEAQ,EACAgB,EACAe,OACAL,GAGFvC,EAAQK,KAAK,CAAGA,EAIdL,EAAQoc,aAAa,CAAG,IAAItZ,IAE1BijB,GACF,MAAMG,CAAAA,EAAAA,EAAAA,KADe,0BACfA,EAAgC,aACpCtmB,QACAC,EACAsmB,YAAa/kB,EACbglB,aAAc/lB,EACdgmB,eAAgBvY,EAAQ4N,EACxBtY,aAAcpD,EAAQoD,YAAY,EAAIvD,EAAMuD,YAAY,EAG9D,CAEApD,EAAQmD,WAAW,CAAG/B,EACtBlB,EAAckB,CAChB,CAoDA,OAlDI4jB,GAAoBc,GAC2BC,IAW/CO,CAAAA,EAAAA,EAAAA,EAZkC,QACgC,oBAWlEA,EAA+B,CAC7BvmB,IAAKilB,EACLrH,KAAM,YACJ7d,EACAsD,aAAcb,OACdgkB,oBAAoB,EACpBC,aAAa,EACbC,WAAW,EAGXC,UAAW,CAAC,CACd,EACAvmB,KAAMN,EAAMM,IAAI,CAChBic,cAAevc,EAAMuc,aAAa,CAClCV,QAAS7b,EAAM6b,OAAO,CACtBhR,KAAM+Z,EAAclQ,EAAAA,YAAY,CAACE,IAAI,CAAGF,EAAAA,YAAY,CAACC,IAAI,GAE3DxU,EAAQoc,aAAa,CAAGvc,EAAMuc,aAAa,EAS7CsJ,EACEiB,CAAAA,EAAAA,EAAAA,gBAAAA,EACEtI,CAAAA,EAAAA,EAAAA,WAAAA,EAAYyH,GACRxJ,CAAAA,EAAAA,EAAAA,cAAAA,EAAewJ,GACfA,EACJ7G,GAAgBE,EAAAA,YAAY,CAACC,IAAI,IAIrC3S,EAAQ+Y,GAGHjiB,GAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,EAC9B,EACA,IAEE0lB,EAAOrQ,GAEAxV,GAGb,oUCpagBogB,qCAAAA,aAFqB,OAE9B,SAASA,EACd5f,CAAgB,CAChBM,CAAoC,EAEpC,OAGF,SAASimB,EACPvmB,CAAgB,CAChBM,CAAoC,CACpCkmB,CAAiB,EAGjB,GAD0D,CACtDC,UADsB/kB,EACV,EADc,CAACpB,GAAgBqB,MAAM,CAGnD,MAAO,CAAC3B,EAAOwmB,EAAU,CAK3B,GAAIlmB,EAAe8R,QAAQ,CAAE,CAC3B,GAAM,CAAChP,EAASsjB,EAAoB,CAAGpmB,EAAe8R,QAAQ,CACxD6F,EAAkBjY,EAAMM,cAAc,CAACqC,GAAG,CAAC,YACjD,GAAIsV,EAAiB,CACnB,IAAMlW,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBoB,GAChCujB,EAAY1O,EAAgBtV,GAAG,CAACZ,GACtC,GAAI4kB,EAAW,CACb,IAAMC,EAAOL,EACXI,EACAD,EACAF,EAAY,IAAMzkB,GAEpB,GAAI6kB,EAAM,OAAOA,CACnB,CACF,CACF,CAGA,IAAK,IAAMrmB,KAAOD,EAAgB,CAChC,GAAY,aAARC,EAAoB,SAExB,CAFiC,EAE3B,CAAC6C,EAASsjB,EAAoB,CAAGpmB,CAAc,CAACC,EAAI,CACpD0X,EAAkBjY,EAAMM,OAH2B,OAGb,CAACqC,GAAG,CAACpC,GACjD,GAAI,CAAC0X,EACH,SAGF,IAAMlW,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBoB,GAEhCujB,EAAY1O,EAAgBtV,GAAG,CAACZ,GACtC,GAAI,CAAC4kB,EACH,SAGF,IAAMC,EAAOL,EACXI,EACAD,EACAF,EAAY,IAAMzkB,GAEpB,GAAI6kB,EACF,IADQ,GACDA,CAEX,CAEA,OAAO,IACT,EA7D6B5mB,EAAOM,EAAgB,GACpD,yQCDa,MAAc,GACzB,EAAO,CAAP,MAAO,CAAQ,oBAAsB,QAAO,EAAE,WAAY,GA+B/C,EAAe,EAA2C,MACrE,EACG,CAFuB,KAEvB,CAAO,CAAC,EAAW,EAAO,IAEvB,CAFiC,CAEzB,GACyB,CACjC,IADC,CADgB,CACK,CAArB,GAAqB,IACtB,EAAM,OAAQ,EAAS,IAAM,GAGhC,EAHgC,EAGhC,CAAK,CAAG,IACR,CAAK,MClDV,CAAe,MACb,KAAO,8BACP,KAAO,IACP,MAAQ,IACR,OAAS,aACT,IAAM,QACN,MAAQ,gBACR,WAAa,GACb,aAAe,SACf,cAAgB,QAClB,ECcA,CAAM,KAAO,kBAET,OACE,EAAQ,oBACR,EAAO,eACP,EAAc,sBACd,YACA,EAAY,YACZ,WACA,EACA,GAAG,GAEL,GACG,CACI,qBACL,KACA,KACE,EACA,GAAG,EACH,KAAO,GACP,MAAQ,GACR,MAAQ,GACR,YAAa,EAAuB,EAA4B,CAA5B,GAAO,KAAW,EAAU,KAA5B,EAAmC,CAAI,EAAjB,EAAqB,UACpE,EAAa,SAAU,CAAV,EACxB,GAAG,GAEL,IACK,CAAS,KAAI,CAAC,CAAC,CAAK,EAAK,CAAM,uBAAc,EAAK,KAAK,CAAC,CACvD,KAAM,SAAQ,GAAY,EAAW,CAAC,EAAhB,CAAI,GCzChC,EDyCoD,CCzChC,EAAkB,KAC1C,GADiE,CAC3D,CAAY,EADK,EACL,eAAwC,WAAE,CAAW,EAAG,IAAS,KACjF,mBAAa,CAAC,EAAM,KAClB,WACA,EACA,UAAW,EAAa,YAAsB,GAAS,EAAI,GAAL,CAAT,EAC1C,EACJ,CAFqE,EAOjE,OAFG,cAAc,EAAG,GAAQ,EAE5B,CACT,GAHqC,qJC8EnBumB,mBAAmB,mBAAnBA,GAUAC,gBAAgB,mBAAhBA,GAxCLC,kBAAkB,mBAAlBA,GAkBAC,cAAc,mBAAdA,GApCAC,sBAAsB,mBAAtBA,GAhBAhR,QAAQ,mBAARA,GAPAnD,QAAQ,mBAARA,GAkDAoU,sBAAsB,mBAAtBA,GApCAC,qBAAqB,mBAArBA,GAkBAC,oBAAoB,mBAApBA,KAtCb,IAAMC,EAAkB,KACtB,MAAM,qBAEL,CAFK,MACJ,sEADI,+DAEN,EACF,EAEavU,EAKPuU,EAEOpR,EAKPoR,EAEOF,EAOPE,CApBJ1b,CAsBWsb,EAOPI,CAtBJ1b,CAwBWyb,CA/B4B,CAsCnCC,EArCA,CAEC,CA4CDA,EAEOH,CAvCN,CA8CDG,EAEOL,EAKPK,EAjDJ1b,IAwDgBkb,CA/ChBlb,CATuC,CAkBvCA,EASAA,CAtBK,CAIkC,EASA,CARnC,CAiBmC,CARnC,CAIC,CAKD,CAiBmC,GACnC,CAEC,GAQWkb,CAfX,UAeWA,sEAAAA,OAUAC,EAAAA,SAAAA,CAAAA,OAAAA,EAKf,SALeA,CAKf,aAIA,yBAKA,+BAdeA,oXC7GLQ,aAAa,mBAAbA,GAiIAtgB,kBAAkB,mBAAlBA,KAjIN,IAAMsgB,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYtgB,EAA0C,CACrDE,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/CwI,KAAM,eACNnI,OAAQ,UACR+f,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvB3e,qBAAqB,EACrB4e,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAe5lB,EACf6lB,eAAgB,EAAE,CAClBxgB,eAAWrF,EACXsD,YAAa,EACf,6GC9HgBwiB,qCAAAA,aApBoB,UACF,WACU,WACA,WAOV,WACJ,WAEgB,WACT,WACC,WACY,WACF,OAGzC,SAASA,EACdxoB,CAA2B,CAC3ByQ,CAAqB,EAErB,GAAM,QAAEwH,CAAM,CAAE,CAAGxH,EACbtQ,EAAmB,CAAC,EACpBM,EAAOT,EAAMuD,YAAY,CAE3BlD,EAAcL,EAAMM,IAExBH,GAAQyQ,0BAA0B,CAAG,GAErC,IAAMpQ,EAAmBkB,CAAAA,EAAAA,EAAAA,oBAAAA,IAInB8kB,EAAiBV,CAAAA,EAAAA,EAAAA,iCAAAA,EAAkC9lB,EAAMM,IAAI,EAInEE,EAAMmC,QAAQ,CAAG8lB,GAAAA,EAAAA,mBAAAA,EAAoB,IAAIvQ,IAAIzX,EAAMwX,GAAS,CAC1DtU,kBAAmB,CACjBtD,CAAW,CAAC,EAAE,CACdA,CAAW,CAAC,EAAE,CACdA,CAAW,CAAC,EAAE,CACd,UACD,CACDwb,QAAS2K,EAAiBxmB,EAAM6b,OAAO,CAAG,IAC5C,GAEA,IAAM9b,EAAcgmB,KAAKC,GAAG,GAC5B,OAAOxlB,EAAMmC,QAAQ,CAACmK,IAAI,CACxB,aAAO,YAAE7M,CAAU,CAAEsD,aAAcoN,CAAoB,CAAE,GAEvD,GAA0B,UAAtB,OAAO1Q,EACT,MAAO4Q,CAAAA,EAAAA,EAAAA,iBAAAA,EACL7Q,EACAG,EACAF,EACAD,EAAM8Q,OAAO,CAACC,WAAW,EAO7B,IAAK,IAAMpQ,KAFXH,EAAMmC,QAAQ,CAAG,KAEkB1C,GAAY,CAC7C,GAAM,CACJK,KAAMU,CAAS,CACfH,SAAUmB,CAAiB,MAC3Be,CAAI,cACJ3B,CAAY,CACb,CAAGT,EAEJ,GAAI,CAACS,EAGH,OADA+kB,KAFiB,GAETC,GAAG,CAAC,kBACLpmB,EAGT,IAAMuB,EAAUC,CAAAA,EAAAA,EAAAA,2BAAAA,EACd,CACC,GAAG,CACJnB,EACAW,EACAhB,EAAMuD,YAAY,EAGpB,GAAgB,MAAM,CAAlBhC,EACF,MAAOib,CAAAA,EAAAA,EAAAA,qBAAAA,EAAsBxc,EAAOyQ,EAAQzP,GAG9C,GAAIkQ,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4B7Q,EAAakB,GAC3C,MAAOsP,CAD8C,EAC9CA,EAAAA,iBAAAA,EACL7Q,EACAG,EACAM,EACAT,EAAM8Q,OAAO,CAACC,WAAW,EAI7B,IAAMI,EAA2BR,EAC7BjQ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBiQ,QAClBjO,EAOJ,GALIiO,IACFxQ,EAAQoD,YAAY,CAAG4N,CAAAA,EADC,OAKtBnP,EAA4B,CAC9B,IAAML,EAAMK,CAAiB,CAAC,EAAE,CAC1BJ,EAAUI,CAAiB,CAAC,EAAE,CACpCxB,EAAMmB,GAAG,CAAGA,EACZnB,EAAMsC,WAAW,CAAG,KACpBtC,EAAMoB,OAAO,CAAGA,EAChByV,CAAAA,EAAAA,EAAAA,6BAAAA,EACEtX,EACAS,OACA,EAEAQ,EACAgB,EACAe,OACAL,GAKAvC,EAAQoc,aAAa,CAAG,IAAItZ,GAEhC,CAEA,MAAMojB,CAAAA,EAAAA,EAAAA,+BAAAA,EAAgC,aACpCtmB,QACAC,EACAsmB,YAAa/kB,EACbglB,aAAc/lB,iBACdgmB,EACAjjB,aAAcpD,EAAQoD,YAAY,EAAIvD,EAAMuD,YAAY,GAG1DpD,EAAQK,KAAK,CAAGA,EAChBL,EAAQmD,WAAW,CAAG/B,EAEtBlB,EAAckB,CAChB,CAEA,MAAOmC,GAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,EAC9B,EACA,IAAMH,EAEV,GAtIsC,6OCf/B,SAASqK,EAAgB,CAc/B,EAd+B,aAC9BxB,CAAQ,WACRC,CAAS,WACTjD,CAAS,YACTC,CAAU,aACVc,CAAW,WACXI,CAAS,CAQV,CAd+B,EAgBxB0hB,EAAW7iB,EAAwB,GAAZA,EAAiBgD,EACxC8f,EAAY7iB,EAA0B,GAAbA,EAAkBgD,EAE3C8f,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,MAAQ,6CAA4CC,UAAQ,8FAA2FC,MAAI,oQAAiQA,MAAI,oEARpYD,GACxB,OACc,YAAd5hB,EACE,IAKuf8hB,OAJze,UAAd9hB,EACE,iBACA,QAEygB,sCAAqCJ,EAAY,iBACpkB,4FA9BgByD,qCAAAA,oHC0BA0W,qCAAAA,aA7B4B,WACf,OAGvBgI,EAAiB,uBAyBhB,SAAShI,EAAmB,CAAqC,EAArC,SAAEzgB,CAAI,CAA+B,CAArC,EAC3B,CAAC0oB,EAAYC,EAAc,CAAGjZ,CAAAA,EAAAA,EAAAA,QAAAA,EAA6B,MAEjED,CAAAA,EAAAA,EAAAA,SAAAA,EAAU,KAERkZ,EA3BJ,SAASC,GA2BSC,GAzBZC,EADJ,IAAMA,EAAoBC,SAASC,iBAAiB,CAACP,EAAe,CAAC,EAAE,CACvE,GAAIK,MAAAA,CAAAA,EAAAA,GAAAA,IAAAA,EAAAA,EAAmBG,UAAAA,EAAU,OAA7BH,EAA+BI,UAAU,CAAC,EAAE,CAC9C,CADgD,MACzCJ,EAAkBG,UAAU,CAACC,UAAU,CAAC,EAAE,EAEjD,IAAMC,EAAYJ,SAASK,aAAa,CAACX,GACzCU,EAAUljB,KAAK,CAACojB,OAAO,CAAG,oBAC1B,IAAMR,EAAYE,SAASK,aAAa,CAAC,OAWzC,OAVAP,EAAUS,QAAQ,CAAG,YACrBT,EAAU7X,EAAE,CAXK,EAWFuY,yBACfV,EAAUW,IAAI,CAAG,QACjBX,EAAU5iB,KAAK,CAACojB,OAAO,CACrB,+IAGaF,EAAUM,YAAY,CAAC,CAAEC,KAAM,MAAO,GAC9CC,WAAW,CAACd,GACnBE,SAASpF,IAAI,CAACgG,WAAW,CAACR,GACnBN,CACT,CACF,KAQW,KACL,IAAMM,EAAYJ,SAASa,oBAAoB,CAACnB,EAAe,CAAC,EAAE,EAC9DU,QAAAA,KAAAA,EAAAA,EAAWU,WAAAA,EAAa,CAC1Bd,SAASpF,IAAI,CAACmG,WAAW,CAACX,EAE9B,GACC,EAAE,EAEL,GAAM,CAACY,EAAmBC,EAAqB,CAAGta,CAAAA,EAAAA,EAAAA,QAAAA,EAAS,IACrDua,EAAgBjmB,CAAAA,EAAAA,EAAAA,MAAAA,OAA2B5B,GAwBjD,MAtBAqN,GAAAA,EAAAA,SAAAA,EAAU,KACR,IAAIya,EAAe,GACnB,GAAInB,SAASoB,KAAK,CAChBD,CADkB,CACHnB,SAASoB,KAAK,KACxB,CACL,IAAMC,EAAarB,SAASsB,aAAa,CAAC,MACtCD,IACFF,EAAeE,EAAWE,IADZ,KACqB,EAAIF,EAAWG,WAAW,EAAI,GAErE,MAK4BnoB,IAA1B6nB,EAAc9lB,OAAO,EACrB8lB,EAAc9lB,OAAO,GAAK+lB,GAE1BF,EAAqBE,GAEvBD,EAAc9lB,IAHZ,GAGmB,CAAG+lB,CAC1B,EAAG,CAAClqB,EAAK,EAEF0oB,EAAa8B,GAAAA,EAAAA,MAAb9B,MAAa8B,EAAaT,EAAmBrB,CAA7CA,EAA2D,IACpE,uUC5DgB+B,qCAAAA,SAAAA,EACd9Z,CAAiC,CACjCtN,CAAoC,EAEpC,GAAM,CAACC,EAAS9C,EAAe,CAAG6C,EAE5B,CAACqnB,EAAgB1S,EAAiB,CAAGrH,QAMtCga,CAAAA,EAAAA,CAAD,CAACA,YAAAA,EAAaD,EAAgBpnB,GAWlC,CAFoBqN,GAAkB9O,GATM,GASA,GAAI,GAMzC4oB,CAJU,CAKfG,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBja,GACzBnQ,CAAc,CAACwX,EAAiB,IAf5B3G,MAAMwZ,OAAO,CAACH,EAiBtB,aAnCyC,EAkBF,SAjBV,sXCsYhBI,oBAAoB,mBAApBA,GAGAC,mBAAmB,mBAAnBA,GAnIG5E,8BAA8B,mBAA9BA,GA9GA6E,6BAA6B,mBAA7BA,GA+NAC,kBAAkB,mBAAlBA,aA1XT,UAMA,WACuB,OAmB9B,SAASC,EACPtrB,CAAQ,CACRurB,CAA4B,CAC5Brb,CAAsB,EAKtB,IAAIsb,EAAkBxrB,EAAImQ,QAAQ,OAclC,CAPIob,IAIFC,GAAmBxrB,EAAIyrB,MAAAA,EAGrBvb,GACM,CARe,EAQbA,EADA,IACyCsb,EAG9CA,CAHcE,CAMvB,SAASC,EACP3rB,CAAQ,CACR2K,CAA8B,CAC9BgR,CAAuB,EAEvB,OAAO2P,EAA2BtrB,EAAK2K,IAAS6J,EAAAA,YAAY,CAACE,IAAI,CAAEiH,EACrE,CA8FO,SAASyP,EAA8B,CAW7C,EAX6C,IAC5CprB,KAAG,CACH2b,SAAO,CACPvb,MAAI,CACJic,eAAa,CACb1R,MAAI,eACJihB,GAAgB,CAAI,CAKrB,CAX6C,EAYtCC,EAxGR,SAASC,CACC,CACRnhB,CAA2C,CAC3CgR,CAAsB,CACtBU,CAA8C,CAC9CuP,CAAsB,EAKtB,IAAK,IAAMG,KARXphB,KAAAA,IAAAA,IAAAA,EAAqB6J,EAAAA,YAAY,CAACwX,SAAS,EAQhB,CAACrQ,EAAS,KAAK,EAAE,CAC1C,IAAMsQ,EAAqBX,EACzBtrB,GACA,EACA+rB,GAEIG,EAAwBZ,EAC5BtrB,GACA,EACA+rB,GAIII,EAAgBnsB,EAAIyrB,MAAM,CAC5BQ,EACAC,EAEEE,EAAgB/P,EAAcpZ,GAAG,CAACkpB,GACxC,GAAIC,GAAiBR,EAAe,CAMlC,GAHEQ,CAGEC,CAHYrsB,GAAG,CAACmQ,MAGL,EAHa,GAAKnQ,EAAImQ,QAAQ,EAC3Cic,EAAcpsB,GAAG,CAACyrB,MAAM,GAAKzrB,EAAIyrB,MAAM,CAGvC,MAAO,CACL,GAAGW,CAAa,CAChBE,SAAS,CACX,EAGF,OAAOF,CACT,CAMA,IAAMG,EAAqBlQ,EAAcpZ,GAAG,CAACipB,GAC7C,GACEjgB,CAAAA,EAEAjM,CAFoB,CAEhByrB,MAAM,EACV9gB,IAAS6J,EAAAA,YAAY,CAACE,IAAI,EAC1B6X,GAGA,CAACA,EAAmB1rB,GAAG,CAAC6B,QAAQ,CAACgpB,GAFjC,EAIA,MAAO,CAAE,GAAGa,CAAkB,CAAED,SAAS,CAAK,CAElD,CAOA,GAVI,CAWFrgB,GAAoB,EACXuI,YAAY,CAACE,IAAI,EAC1BkX,GAEA,IAAK,IAAMY,IADX,CACyBnQ,EAAcoQ,MAAM,EAlBqC,CAkBjC,GAE7CD,EAAWxsB,GAAG,CAACmQ,QAAQ,GAAKnQ,EAAImQ,QAAQ,EAGxC,CAACqc,CAFD,CAEY3rB,GAAG,CAAC6B,QAAQ,CApIM,KAsI9B,MAAO,CAAE,GAAG8pB,CAAU,CAAEF,SAAS,CAAK,CAE1C,CAIJ,EAmBItsB,CA1BI,CA2BJ2K,EACAgR,EACAU,EACAuP,UAGEC,GAEFA,EAAmBtU,MAAM,CAAGmV,EAA4Bb,GAKtDA,EAAmBlhB,CAPC,GAOG,GAAK6J,EAAAA,YAAY,CAGd,IAHmB,EAC7C7J,IAAS6J,EAAAA,YAAY,CAACE,IAAI,EAM1BmX,EAAmBjO,IAAI,CAAChR,IAAI,CAAC,IAQ3B,GAAI,CAAC+f,CANHlb,MAAMwZ,OAAO,CAAC2B,CAMK,CANY7sB,UAAU,GACzC6sB,EAAiB7sB,UAAU,CAAC8sB,IAAI,CAAE9sB,GAEzBA,EAAWmB,YAAY,EAA4B,OAAxBnB,EAAWY,QAAQ,CACvD,EAGA,OAAOmsB,EAAwB,MAC7B1sB,EACAJ,cACA2b,gBACAU,EAIA1R,KAAMA,MAAAA,EAAAA,EAAQ6J,EAAAA,YAAY,CAACwX,SAAS,EAG1C,GAKErhB,GAAQkhB,EAAmBlhB,IAAI,GAAK6J,EAAAA,YAAY,CAACwX,SAAS,EAAE,GAC3CrhB,IAAI,CAAGA,CAAAA,EAIrBkhB,GAIFiB,EAAwB,MAC7B1sB,MACAJ,UACA2b,EACAU,gBACA1R,KAAMA,GAAQ6J,EAAAA,YAAY,CAACwX,SAAS,EAExC,CAmCO,SAASzF,EAA+B,CAW9C,EAX8C,YAC7C5K,CAAO,MACPvb,CAAI,eACJic,CAAa,KACbrc,CAAG,MACH4d,CAAI,MACJjT,CAAI,CAKL,CAX8C,EAevCoiB,EAAmBnP,EAAK4I,kBAAkB,CAC5CmF,EAAuB3rB,EAAK2K,EAAMgR,GAClCgQ,EAAuB3rB,EAAK2K,GAE1ByM,EAAgB,CACpB4V,qBAAsB5sB,EACtBwd,KAAMnR,QAAQC,OAAO,CAACkR,GACtBjT,OACAsiB,aAAcpH,KAAKC,GAAG,GACtBoH,aAAcrH,KAAKC,GAAG,GACtBa,UAAW,CAAC,EACZ9lB,IAAKksB,EACLxV,OAAQC,EAAAA,wBAAwB,CAAC2V,KAAK,KACtCntB,CACF,EAIA,OAFAqc,EAAcnZ,GAAG,CAAC6pB,EAAkB3V,GAE7BA,CACT,CAKA,SAAS0V,EAAwB,CAShC,EATgC,QAC/B9sB,CAAG,MACH2K,CAAI,MACJvK,CAAI,SACJub,CAAO,eACPU,CAAa,CAId,CATgC,EAUzB0Q,EAAmBpB,EAAuB3rB,EAAK2K,GAI/CiT,EAAOwP,EAAAA,aAAa,CAACC,OAAO,CAAC,IACjC9E,GAAAA,EAAAA,mBAAAA,EAAoBvoB,EAAK,CACvByD,kBAAmBrD,UACnBub,EACA2R,aAAc3iB,CAChB,GAAGiC,IAAI,CAAC,IAIN,IAAI2gB,EAeJ,GAbIX,EAAiBpG,kBAAkB,EAAE,GA7F/C,SAASgH,CAQR,EARyC,QACxCxtB,CAAG,SACH2b,CAAO,eACPU,CAAa,kBACboR,CAAgB,CAIjB,CARyC,EASlC5B,EAAqBxP,EAAcpZ,GAAG,CAACwqB,GAC7C,GAAI,CAAC5B,EAEH,OAGF,IAAM0B,EAAc5B,EAClB3rB,EACA6rB,CAPuB,CAOJlhB,IAAI,CACvBgR,GAKF,OAHAU,EAAcnZ,GAAG,CAACqqB,EAAa,CAAE,GAAG1B,CAAkB,CAAEhrB,IAAK0sB,CAAY,GACzElR,EAAc8F,MAAM,CAACsL,GAEdF,CACT,EAuEuD,KAC7CvtB,EACAytB,iBAAkBV,UAClBpR,gBACAU,CACF,IAMEuQ,EAAiBnG,WAAW,CAAE,CAChC,IAAMoF,EAAqBxP,EAAcpZ,GAAG,CAE1CsqB,MADA,EACAA,EAAeR,GAEblB,IACFA,EAAmBlhB,IAAI,CAAG6J,EAAAA,OADJ,KACgB,CAACE,IAAI,CACR,CAAC,GAAG,CAAnCkY,EAAiBjG,SAAS,GAG5BkF,EAAmBlF,SAAS,CAAGiG,EAAiBjG,SAAAA,EAGtD,CAEA,OAAOiG,CACT,IAGIxV,EAAgB,CACpB4V,qBAAsB5sB,OACtBwd,OACAjT,EACAsiB,aAAcpH,KAAKC,GAAG,GACtBoH,aAAc,KACdvG,UAAW,CAAC,EACZ9lB,IAAKksB,EACLxV,OAAQC,EAAAA,wBAAwB,CAAC2V,KAAK,KACtCntB,CACF,EAIA,OAFAqc,EAAcnZ,GAAG,CAAC6pB,EAAkB3V,GAE7BA,CACT,CAEO,SAASiU,EACdhP,CAAoD,EAEpD,IAAK,GAAM,CAAC9b,EAAMmtB,EAAmB,GAAIrR,EAErCqQ,EAA4BgB,KAC5BlW,EAAAA,GAHoD,qBAG5B,CAACmW,OAAO,EAEhCtR,EAAc8F,MAAM,CAAC5hB,EAG3B,CAIO,IAAM2qB,EACkD,IAA7D/lB,OAAO8G,GAAkD,EAE9Ckf,EACiD,IAA5DhmB,OAAO8G,KAAiD,EAE1D,SAASygB,EAA4B,CAKhB,EALgB,SACnC/hB,CAAI,cACJsiB,CAAY,cACZC,CAAY,WACZvG,CAAS,CACU,CALgB,SAMnC,CAAmB,GAAG,CAAlBA,EASKd,KAAKC,GAAG,GAAKmH,EAAetG,EAC/BnP,EAAAA,wBAAwB,CAAC2V,KAAK,CAC9B3V,EAAAA,wBAAwB,CAACoW,KAAK,CAIhC/H,KAAKC,GAAG,GAAMoH,CAAAA,MAAAA,EAAAA,EAAgBD,CAAAA,CAAAA,CAAgB/B,EACzCgC,EACH1V,EAAAA,gBAFkE,QAE1C,CAACC,QAAQ,CACjCD,EAAAA,wBAAwB,CAAC2V,KAAK,CAMhCxiB,IAAS6J,EAAAA,YAAY,CAACC,IAAI,EAAE,KACrBqR,GAAG,GAAKmH,EAAe9B,EACvB3T,EAAAA,iBAD4C,OACpB,CAACoW,KAAK,CAKrCjjB,IAAS6J,EAAAA,YAAY,CAACE,IAAI,EAAE,KACrBoR,GAAG,GAAKmH,EAAe9B,EACvB3T,EAAAA,iBAD4C,OACpB,CAACC,QAAQ,CAIrCD,EAAAA,wBAAwB,CAACmW,OAAO,4TC5X5BE,qCAAAA,OAzDN,SAMyB,SACG,SACJ,SACA,SACC,SACE,SACE,WA6CvBA,EARb,QASE,CATOC,CACoB,CAC3BC,CAAuB,CAOM,CAL7B,EAKgCD,KALzBhuB,CACT,UAIkDkuB,CAAaA,qUC3DlDvR,qCAAAA,aAPuB,WACV,OAMbA,EAA6B,IACxC,GAAI,CAACxM,EAAK5G,UAAU,CAAC,KACnB,GAD2B4C,IACpBgE,EAGT,GAAM,EAJiE,QAI/DE,CAAQ,OAAEC,CAAK,MAAE7M,CAAI,CAAE,CAAG8M,CAAAA,EAAAA,EAAAA,SAAAA,EAAUJ,GAW5C,MAAQ,GAAEge,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoB9d,GAAYC,EAAQ7M,CACpD,iVCqDgBjC,qCAAT,SAASA,EACdyP,CAAoC,CACpCtN,CAAoC,CACpC3C,CAA4B,CAC5BmP,CAAY,EAEZ,IAqBIie,EArBE,CAACxqB,EAAS9C,EAAgBZ,EAAKmuB,EAASC,EAAa,CACzD3qB,EAGF,GAAiC,IAA7BsN,EAAkB9O,MAAM,CAAQ,CAClC,IAAM7B,EAA0BiuB,EAAW5qB,EAAmB3C,GAI9D,MAFAwtB,CAAAA,EAAAA,EAAAA,wCAAAA,EAAyCluB,EAAM6P,GAExC7P,CACT,CAEA,GAAM,CAAC0qB,EAAgB1S,EAAiB,CAAGrH,EAG3C,GAAI,CAACga,CAAAA,EAAAA,EAAAA,YAAAA,EAAaD,EAAgBpnB,GAChC,OAD0C,KAO5C,GAHiD,CAG7C6qB,GAHgBxd,EAAkB9O,MAAM,CAI1CisB,CADe,CACMG,EAAWztB,CAAc,CAACwX,EAAiB,CAAEtX,QASlE,GAAIotB,MAA6B,EAPjCA,EAAqB5sB,EACnB0pB,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBja,GACzBnQ,CAAc,CAACwX,EAAiB,CAChCtX,EACAmP,EAAAA,EAIA,OAAO,KAIX,IAAM7P,EAA0B,CAC9B2Q,CAAiB,CAAC,EAAE,CACpB,CACE,GAAGnQ,CAAc,CACjB,CAACwX,EAAiB,CAAE8V,CACtB,EACAluB,EACAmuB,EACD,CASD,OANIC,GACFhuB,EAAI,CAAC,EAAE,EAAG,GAGZkuB,CAJkB,EAIlBA,EAAAA,wCAAAA,EAAyCluB,EAAM6P,GAExC7P,CACT,aAtIoC,WACK,WACZ,WAC4B,OAKzD,SAASiuB,EACPG,CAA8B,CAC9BC,CAA4B,EAE5B,GAAM,CAACC,EAAgBC,EAAsB,CAAGH,EAC1C,CAACI,EAAcC,EAAoB,CAAGJ,EAI5C,GACEG,IAAiBE,EAAAA,mBAAmB,EACpCJ,IAAmBI,EAAAA,mBAAmB,CAEtC,CADA,MACON,EAGT,GAAIzD,CAAAA,EAAAA,EAAAA,YAAAA,EAAa2D,EAAgBE,GAAe,CAC9C,IAAMG,EAA0C,CAAC,EACjD,IAAK,IAAMluB,KAAO8tB,EAEd,KAAoC,IAA7BE,CAAmB,CAAChuB,EAAI,CAE/BkuB,CAAiB,CAACluB,EAAI,CAAGwtB,CAJY,CAKnCM,CAAqB,CAAC9tB,EAAI,CAC1BguB,CAAmB,CAAChuB,EAAI,EAG1BkuB,CAAiB,CAACluB,EAAI,CAAG8tB,CAAqB,CAAC9tB,EAAI,CAIvD,IAAK,IAAMA,KAAOguB,EACZE,CAAiB,CAACluB,EAAI,EAAE,CAI5BkuB,CAAiB,CAACluB,EAAI,CAAGguB,CAAmB,CAAChuB,EAAAA,EAG/C,IAAMT,EAA0B,CAACsuB,EAAgBK,EAAkB,CAenE,OAZIP,CAAW,CAAC,EAAE,EAAE,CAClBpuB,CAAI,CAAC,EAAE,CAAGouB,CAAW,CAAC,IAGpBA,CAAW,CAAC,EAAE,EAAE,CAClBpuB,CAAI,CAAC,EAAE,CAAGouB,CAAW,CAAC,IAGpBA,CAAW,CAAC,EAAE,EAAE,CAClBpuB,CAAI,CAAC,EAAE,CAAGouB,CAAW,CAAC,EAAE,EAGnBpuB,CACT,CAEA,OAAOquB,CACT,gXCmIgBO,wBAAwB,mBAAxBA,GA0DAxY,sBAAsB,mBAAtBA,GA+BAwJ,sBAAsB,mBAAtBA,GAnDAiP,wBAAwB,mBAAxBA,GAuEH3P,uBAAuB,mBAAvBA,aA1SN,WACiB,WACQ,WACL,SAC0B,eACb,WACZ,WACqB,WACjB,WAM+B,OA4B/D,SAAS4P,EACPhR,CAAiC,CACjCiR,CAA8B,EAEF,MAAM,CAA9BjR,EAAYkR,OAAO,GACrBlR,EAAYkR,OAAO,CAAGlR,EAAYkR,OAAO,CAACC,IAAI,CAClB,MAAM,CAA9BnR,EAAYkR,OAAO,CAErBE,EAAU,aACRpR,EACA3N,OAAQ2N,EAAYkR,OAAO,UAC3BD,CACF,GAGIjR,EAAYqR,YAAY,EAAE,CAC5BrR,EAAYqR,YAAY,EAAG,EAC3BrR,EAAYsR,QAAQ,CAClB,CACE3d,KAAM4d,EAAAA,cAAc,CACpB1X,OAAQoC,OAAO9D,QAAQ,CAAC0B,MAAM,EAEhCoX,IAKV,CAEA,eAAeG,EAAU,CAQxB,EARwB,gBACvBpR,CAAW,CACX3N,QAAM,UACN4e,CAAQ,CAKT,CARwB,EASjBO,EAAYxR,EAAYpe,KAAK,CAEnCoe,EAAYkR,OAAO,CAAG7e,EAEtB,IAAMof,EAAUpf,EAAOof,OAAO,CACxBlK,EAAevH,EAAY3N,MAAM,CAACmf,EAAWC,GAEnD,SAASC,EAAaC,CAAyB,EAEzCtf,EAAOuf,SAAS,EAAE,CAItB5R,EAAYpe,KAAK,CAAG+vB,EAEpBX,EAAoBhR,EAAaiR,GACjC5e,EAAO7D,OAAO,CAACmjB,GACjB,CAGIE,CAAAA,EAAAA,EAAAA,UAAU,EAACtK,GACbA,EAAa7Y,IAAI,CAACgjB,EAAc,GADJ,CAE1BV,EAAoBhR,EAAaiR,GACjC5e,EAAOoV,MAAM,CAACqK,EAChB,GAEAJ,EAAanK,EAEjB,CAiFO,SAASuJ,EACdiB,CAA4B,CAC5BC,CAAuD,EAEvD,IAAMhS,EAAoC,CACxCpe,MAAOmwB,EACPT,SAAU,CAACG,EAAyBR,IAClCgB,CAtFN,SAASA,CAC0B,CACjCR,CAAuB,CACvBR,CAA8B,EAE9B,IAAIiB,EAGA,CAAE1jB,QAASyiB,EAAUxJ,OAAQ,KAAO,CAAE,EAM1C,GAAIgK,EAAQ9d,IAAI,GAAK8M,EAAAA,cAAc,CAAE,CAEnC,IAAM0R,EAAkB,IAAI5jB,QAAwB,CAACC,EAASiZ,KAC5DyK,EAAY,SAAE1jB,SAASiZ,CAAO,CAChC,GAEArP,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KAGd6Y,EAASkB,EACX,EACF,CAEA,IAAMC,EAA6B,SACjCX,EACAN,KAAM,KACN3iB,QAAS0jB,EAAU1jB,OAAO,CAC1BiZ,OAAQyK,EAAUzK,MAAM,CAItBzH,MAA8B,IAAlBkR,OAAO,EAGrBlR,EAAYxS,IAAI,CAAG4kB,EAEnBhB,EAAU,aACRpR,EACA3N,OAAQ+f,EACRnB,UACF,IAEAQ,EAAQ9d,IAAI,GAAK0e,EAAAA,eAAe,EAChCZ,EAAQ9d,IAAI,GAAK8M,EAAAA,cAAc,EAC/B,EAGYyQ,OAAO,CAACU,SAAS,CAAG,GAIhCQ,EAAUjB,IAAI,CAAGnR,EAAYkR,OAAO,CAACC,IAAI,CAGrCnR,EAAYkR,OAAO,CAACO,OAAO,CAAC9d,IAAI,GAAK2e,EAAAA,oBAAoB,EAAE,CAC7DtS,EAAYqR,YAAY,EAAG,GAG7BD,EAAU,aACRpR,EACA3N,OAAQ+f,WACRnB,CACF,KAIyB,MAAM,CAA3BjR,EAAYxS,IAAI,GAClBwS,EAAYxS,IAAI,CAAC2jB,IAAI,CAAGiB,CAAAA,EAE1BpS,EAAYxS,IAAI,CAAG4kB,GAEvB,EAWqBpS,EAAayR,EAASR,GACvC5e,OAAQ,MAAOzQ,EAAuByQ,IACrBsd,CAAAA,EAAAA,EAAAA,OAAAA,EAAQ/tB,EAAOyQ,GAGhC6e,QAAS,KACT1jB,KAAM,KACN+kB,wBAC2B,OAAzBP,GACwD,YAAxD,OAAOA,EAAqBO,uBAAuB,CAE/CP,EAAqBO,uBAAuB,CAC5C,IACR,EAeA,OAAOvS,CACT,CAEO,SAAS+Q,IACd,OAAOyB,IACT,CAWA,SAASC,QAZsB,GAgBtB,IAhB6BD,CAmB/B,SAASla,EACdjW,CAAY,CACZqwB,CAA4C,CAC5ClV,CAAqB,CAtBiC5b,CAuBlB,EAIpC,EA3B2D,EA2BrDE,EAAM,IAAIgY,IAAIlB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYvW,GAAO8V,SAAS9V,IAAI,EAKpDswB,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4B7d,GAO5B0L,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtB7M,KAAM0e,EAAAA,eAAe,KACrBvwB,EACA8wB,cAAejU,CAAAA,EAAAA,EAAAA,aAAAA,EAAc7c,GAC7B+wB,eAAgB1a,SAASoV,MAAM,cAC/B/P,eACAkV,EACAhF,eAAe,CACjB,EACF,CAEO,SAAS5L,EACdzf,CAAY,CACZH,CAAmC,EAMnCse,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtB7M,KAAM8M,EAAAA,cAAc,CACpB3e,IAAK,IAAIgY,IAAIzX,QACbH,CACF,EACF,CAOO,IAAMkf,EAA6C,CACxD0R,KAAM,IAAM7W,OAAOmD,OAAO,CAAC0T,IAAI,GAC/BC,QAAS,IAAM9W,OAAOmD,OAAO,CAAC2T,OAAO,GACrC7d,SAaI,CAbMnH,EAaSilB,IAb8B,CAe3C,EAbF,EAaQhT,EAAciT,SArFnBA,EAEL,MAAM,qBAEL,CAFSjW,MACR,2EADI,+DAEN,EAGJ,IA+Eclb,EAAM2c,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBpc,GAC9B,GAAY,OAARP,EAAc,KAURkxB,EAHRE,CAAAA,EAAAA,EAAAA,eAAAA,EAAgBlT,EAAYpe,KAAK,CAAE,CACjC+R,KAAMwf,EAAAA,eAAe,KACrBrxB,EACA2K,KAAMumB,OAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASvmB,IAAAA,EAATumB,EAAiB1c,EAAAA,YAAY,CAACE,IAAI,EAE5C,CACF,EACJnB,QAAS,CAAChT,EAAc2wB,KACtB5a,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,SAC0B4a,EAAxC1a,EAAuBjW,EAAM,UAAW2wB,OAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASzd,MAAAA,GAATyd,EAAyB,KACnE,EACF,EACA7R,KAAM,CAAC9e,CAHwD,CAG1C2wB,KACnB5a,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,SACuB4a,EAArC1a,EAAuBjW,EAAM,OAAQ2wB,OAAAA,EAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASzd,MAAM,GAAfyd,EAAyB,KAChE,EACF,EACAI,OAH4D,CAGnD,KACPhb,CAAAA,EAAAA,EAAAA,eAAAA,EAAgB,KACdoI,CAAAA,EAAAA,EAAAA,uBAAAA,EAAwB,CACtB7M,KAAM4d,EAAAA,cAAc,CACpB1X,OAAQoC,OAAO9D,QAAQ,CAAC0B,MAAM,EAElC,EACF,EACAwZ,WAAY,KAER,MAAM,qBAEL,CAFK,MACJ,gFADI,+DAEN,EASJ,CACF,kQCxXA,QAEA,cACA,4BACA,0JCskCgBC,SAAS,mBAATA,GA1VAC,uBAAuB,mBAAvBA,GAvpBAC,kBAAkB,mBAAlBA,GA6jCAC,oCAAoC,mBAA7C,SAASA,EACdC,CAAuB,CACvB/vB,CAA8B,EAY9B,IAAMgwB,EAAsBhwB,CAAW,CAAC,EAAE,CACpCiwB,EAAoBF,EAAahxB,cAAc,CAC/CmuB,EAAoB,IAAIhsB,IAAI+uB,GAClC,IAAK,IAAI1Z,KAAoByZ,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAACzZ,EAAiB,CACjC4Z,EAAeD,CAAgB,CAAC,EAAE,CAClCE,EAAkB3vB,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB0vB,GACvCE,EAAqBJ,EAAkB7uB,GAAG,CAACmV,GACjD,QAA2B5V,IAAvB0vB,EAAkC,CACpC,IAAMC,EAAoBD,EAAmBjvB,GAAG,CAACgvB,GACjD,QAA0BzvB,IAAtB2vB,EAAiC,CACnC,IAAMC,EAAoBT,EACxBQ,EACAJ,GAEIM,EAAqB,IAAItvB,IAAImvB,GACnCG,EAAmBnvB,GAAG,CAAC+uB,EAAiBG,GACxCrD,EAAkB7rB,GAAG,CAACkV,EAAkBia,EAC1C,CACF,CACF,CAUA,IAAM5wB,EAAMmwB,EAAanwB,GAAG,CACtB6wB,EAAoBC,EAAc9wB,IAAQA,cAAI8V,MAAM,CAE1D,MAAO,CACL9U,SAAU,KACVhB,MACAoB,KAAM+uB,EAAa/uB,IAAI,CAEvBC,aAAcwvB,EAAoBV,EAAa9uB,YAAY,CAAG,CAAC,KAAM,KAAK,CAC1EF,YAAa0vB,EAAoBV,EAAahvB,WAAW,CAAG,KAC5DlB,QAASkwB,EAAalwB,OAAO,CAG7Bd,eAAgBmuB,EAEhBlvB,YAAa+xB,EAAa/xB,WAAW,CAEzC,aAvsCoC,WACP,WACQ,WAEO,WACP,OAiC/B2yB,EAAyC,CAC7CC,MAAO,KACPC,KAAM,KACNC,mBAAoB,KACpBjgB,SAAU,IACZ,EAiCO,SAASgf,EACd7xB,CAAmB,CACnB+xB,CAAuB,CACvBgB,CAAiC,CACjCC,CAAiC,CACjCC,CAAsC,CACtChwB,CAA6B,CAC7BiwB,CAA8B,CAC9BC,CAA6B,CAC7BC,CAAkD,EAGlD,OAeF,SAASC,EACPrzB,CAAmB,CACnB+xB,CAAuB,CACvBgB,CAAiC,CACjCC,CAAiC,CACjCM,CAA0B,CAC1BL,CAAsC,CACtChwB,CAA6B,CAC7BiwB,CAA8B,CAC9BC,CAA6B,CAC7BliB,CAA8B,CAC9BmiB,CAAkD,EAGlD,IAAMG,EAAyBR,CAAc,CAAC,EAAE,CAC1CS,EAAyBR,CAAc,CAAC,EAAE,CAC1CS,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAElEK,GAKC/E,CADuC,IAAtByE,CAAc,CAAC,EAAE,EAGpCM,GAFgB,CAEI,CAPA,EAWxB,IAAMrB,EAAoBF,EAAahxB,cAAc,CAa/C2yB,EAAyB,IAAIxwB,IAAI+uB,GAOnC0B,EAEA,CAAC,EACDC,EAAe,KAWfC,GAAsB,EAQtBC,EAEA,CAAC,EAEL,IAAK,IAAIvb,KAAoBib,EAAwB,CACnD,IAyBIO,EAzBEC,EACJR,CAAsB,CAACjb,EAAiB,CACpC0b,EACJV,CAAsB,CAAChb,EAAiB,CACpC8Z,EAAqBJ,EAAkB7uB,GAAG,CAACmV,GAC3C2b,EACJT,SACIA,CAAoB,CAAClb,EAAiB,CACtC,KAEA4b,EAAkBH,CAAmB,CAAC,EAAE,CACxCI,EAAsBnjB,EAAYojB,MAAM,CAAC,CAC7C9b,EACA4b,EACD,EACKG,EAAqB7xB,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB0xB,GAE1CI,OACoB5xB,IAAxBsxB,EAAoCA,CAAmB,CAAC,EAAE,CAAGtxB,OAEzD2vB,EACmB3vB,SAAvB0vB,EACIA,EAAmBjvB,GAAG,CAACkxB,QACvB3xB,EA+HN,GAAIoxB,QAhHAA,EAZAI,IAAoBlF,EAAAA,IAYRuF,eAZ2B,EAAE,IAQf7xB,IAAxBsxB,EAufD,CACLrB,IAxfyC,EAITqB,CAofzBQ,CACP5B,KAAM,KACNC,mBAAoB,KACpBjgB,SAAU,IACZ,EArfkB6hB,EACV10B,EACAi0B,EACAD,EACA1B,EACAgB,OACsB3wB,IAAtBuxB,EAAkCA,EAAoB,KACtDjxB,EACAiwB,EACAkB,EACAhB,GAIJD,GAO+C,GAC/C,CADAjyB,OAAOiB,IAAI,CAAC6xB,CAAmB,CAAC,EAAE,CANlC,CAMoC5xB,MAAM,CAoB9BsyB,EACV10B,EACAi0B,EACAD,EACA1B,EACAgB,EACAY,WAAkCA,EAAoB,EAhCrB,GAiCjCjxB,EACAiwB,EACAkB,EACAhB,QAGsBzwB,IAAxBsxB,QACoBtxB,IAApB4xB,GACArJ,GAAAA,EAAAA,YAAAA,EAAaiJ,EAAiBI,SAGN5xB,IAAtB2vB,KAFF,GAG0B3vB,IAAxBsxB,EAIYZ,EACVrzB,EACAsyB,CALF,CAME2B,EACAD,EACAV,EACAY,EACAjxB,EACAiwB,EACAC,EACAiB,EACAhB,GAKUsB,EACV10B,EACAi0B,EACAD,EACA1B,EACAgB,OACsB3wB,IAAtBuxB,EAAkCA,EAAoB,KACtDjxB,EACAiwB,EACAkB,EACAhB,IAmBkB,CAGtB,GAAwB,MAAM,CAA1BW,EAAUnB,KAAK,CAGjB,OAAOD,CAGY,MAAM,EAAvBiB,IACFA,EAAe,IAAI1wB,GAAAA,EAErB0wB,EAAavwB,GAAG,CAACkV,EAAkBwb,GACnC,IAAMxB,EAAoBwB,EAAUlB,IAAI,CACxC,GAA0B,OAAtBN,EAA4B,CAC9B,IAAMC,EAAsC,IAAItvB,IAAImvB,GACpDG,EAAmBnvB,GAAG,CAACixB,EAAoB/B,GAC3CmB,EAAuBrwB,GAAG,CAACkV,EAAkBia,EAC/C,CAKA,IAAMmC,EAAiBZ,EAAUnB,KAAK,CACtCe,CAA0B,CAACpb,EAAiB,CAAGoc,EAE/C,IAAMC,EAA0Bb,EAAUjB,kBAAkB,MACtB,EAAlC8B,GAEFf,GAAsB,EACtBC,CAA0B,CAACvb,EAAiB,CAAGqc,GAE/Cd,CAA0B,CAACvb,EAAiB,CAAGoc,CAEnD,MAEEhB,CAA0B,CAACpb,EAAiB,CAAGyb,EAC/CF,CAA0B,CAACvb,EAAiB,CAAGyb,CAEnD,CAEA,GAAqB,MAAM,GAEzB,OAAO,KAGT,IAAM3xB,EAA+B,CACnCO,SAAU,KACVhB,IAAKmwB,EAAanwB,GAAG,CAOrBmB,YAAagvB,EAAahvB,WAAW,CACrCC,KAAM+uB,EAAa/uB,IAAI,CACvBC,aAAc8uB,EAAa9uB,YAAY,CACvCpB,QAASkwB,EAAalwB,OAAO,CAG7Bd,eAAgB2yB,cAEhB1zB,CACF,EAEA,MAAO,CAEL4yB,MAAOiC,EACL7B,EACAW,GAEFd,KAAMxwB,EACNywB,mBAAoBe,EAChBgB,EACE7B,EACAc,GAEF,KACJjhB,SAAU+gB,CACZ,CACF,EAjUI5zB,EACA+xB,EACAgB,EACAC,GACA,EACAC,EACAhwB,EACAiwB,EACAC,EAV4C,EAAE,CAY9CC,EAEJ,CAuTA,SAASsB,EACP10B,CAAmB,CACnB+yB,CAAwC,CACxCC,CAAiC,CACjClb,CAAmC,CACnCwb,CAA0B,CAC1BL,CAAsC,CACtC6B,CAA4C,CAC5C5B,CAA8B,CAC9BjiB,CAA8B,CAC9BmiB,CAAkD,QAElD,CAAKE,SAqBkB3wB,IAAnBowB,GACA5hB,CAAAA,EAtBoB,EAsBpBA,2BAAAA,EAA4B4hB,EAAgBC,EAAAA,EAGrCL,CAFP,CAKGoC,SAYAA,EACP/0B,CAAmB,CACnBgC,CAA8B,CAC9B8V,CAAmC,CACnCmb,CAAsC,CACtC6B,CAA4C,CAC5C5B,CAA8B,CAC9BjiB,CAA8B,CAC9BmiB,CAAkD,EAQlD,IAQIxxB,EACAC,EACAmB,EACAgyB,EAXEhD,EAAsBhwB,CAAW,CAAC,EAAE,CACpCizB,EAA4D,IAA5C/zB,OAAOiB,IAAI,CAAC6vB,GAAqB5vB,MAAM,CAW7D,GACwBO,SAAtBmV,GAIAA,CAHA,CAGkB9X,WAAW,CAAGqrB,EAAAA,oBAAoB,CAAGrrB,EAIvD4B,EAAMkW,EAAkBlW,GAAG,CAC3BC,EAAUiW,CAJV,CAI4BjW,OAAO,CACnCmB,EAAO8U,EAAkB9U,IAAI,CAG7BgyB,CAZoE,CAY7Cld,EAAkB9X,WAAW,MAC/C,GAAqB,MAAM,CAAvBizB,EAsCT,OAAOiC,EACLl1B,EACAgC,EACA,KACA8yB,EACA5B,EACAjiB,EACAmiB,QAjCF,GARAxxB,EAAMqxB,CAAY,CAAC,EAAE,CACrBpxB,EAAUoxB,CAAY,CAAC,EAAE,CACzBjwB,EAAOiyB,EAAgBH,EAA8B,KAIrDE,EAAuBh1B,EACMizB,CAAY,CAAC,EAAE,EAKzCC,GAAyB+B,EAI1B,OAAOC,EACLl1B,EACAgC,EAPF,EASE8yB,EACA5B,EACAjiB,EACAmiB,GAyBN,IAvC2C,EAuCG,OAAjBH,EAAwBA,CAAY,CAAC,EAAE,CAAG,KACjEW,EAAe,IAAI1wB,IACnBiyB,OACkBxyB,IAAtBmV,EAAkCA,EAAkB/W,YAxCuB,EAwCT,CAAG,KACjEq0B,EAAoB,IAAIlyB,IAAIiyB,GAC9BrB,EAEA,CAAC,EACDD,GAAsB,EAC1B,GAAIoB,EAOF7B,EAAyB5T,IAAI,CAACvO,MAPb,EASjB,IAAK,IAAIsH,KAAoByZ,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAACzZ,EAAiB,CACjC2b,EACqB,OAAzBT,EACIA,CAAoB,CAAClb,EAAiB,CACtC,KACA8c,EAC0B,OAA9BF,EACIA,EAA0B/xB,GAAG,CAACmV,GAC9B5V,OACAwvB,EAAeD,CAAgB,CAAC,EAAE,CAClCoD,EAAmBrkB,EAAYojB,MAAM,CAAC,CAC1C9b,EACA4Z,EACD,EACKC,EAAkB3vB,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB0vB,GAOvC4B,EAAYgB,EAChB/0B,EACAkyB,OAN4BvvB,IAA5B0yB,EACIA,EAAwBjyB,GAAG,CAACgvB,GAC5BzvB,OAMJuxB,EACAY,EACA5B,EACAoC,EACAlC,GAEFQ,EAAavwB,GAAG,CAACkV,EAAkBwb,GACnC,IAAMa,EAA0Bb,EAAUjB,kBAAkB,MACtB,EAAlC8B,GAEFf,GAAsB,EACtBC,CAA0B,CAACvb,EAAiB,CAAGqc,GAE/Cd,CAA0B,CAACvb,EAAiB,CAAG2Z,EAEjD,IAAMK,EAAoBwB,EAAUlB,IAAI,CACxC,GAA0B,OAAtBN,EAA4B,CAC9B,IAAMC,EAAsC,IAAItvB,IAChDsvB,EAAmBnvB,GAAG,CAAC+uB,EAAiBG,GACxC6C,EAAkB/xB,GAAG,CAACkV,EAAkBia,EAC1C,CACF,CAGF,MAAO,CAKLI,MAAO5wB,EACP6wB,KAAM,CACJjwB,SAAU,SAGVhB,EACAmB,YAAa,UACbC,EACAC,aAAc,aACdpB,EACAd,eAAgBq0B,EAChBp1B,YAAag1B,CACf,EACAlC,mBAAoBe,EAChBgB,EAAgC7yB,EAAa8xB,GAC7C,KACJjhB,SAAU+gB,CACZ,CACF,EAtMI5zB,EACAgzB,EACAlb,EACAmb,EACA6B,EACA5B,EACAjiB,EACAmiB,EAEJ,CA+LA,SAASyB,EACPU,CAAkC,CAClCC,CAA8D,EAE9D,IAAMC,EAA2B,CAACF,CAAe,CAAC,EAAE,CAAEC,EAAY,CAalE,OATI,KAAKD,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,IAEzB,EAHsB,GAGjBA,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,IAEzB,EAHsB,GAGjBA,IACPE,CAAK,CAAC,EAAE,CAAGF,CAAe,CAAC,IAEtBE,CACT,CAJ4B,SAMnBP,EACPl1B,CAAmB,CACnBgC,CAA8B,CAC9BixB,CAAsC,CACtChwB,CAA6B,CAC7BiwB,CAA8B,CAC9BjiB,CAA8B,CAC9BmiB,CAAkD,EAMlD,IAAMN,EAAqB+B,EACzB7yB,EACAA,CAAW,CAAC,EAAE,EAsBhB,OAAO0zB,CApBW,CAAC,EAAE,CAAG,UAEF,CACpB9C,MAAO5wB,EAGP6wB,KAAM8C,SA8MDA,EACP31B,CAAmB,CACnBgC,CAA8B,CAC9BixB,CAAsC,CACtChwB,CAA6B,CAC7BiwB,CAA8B,CAC9BjiB,CAA8B,CAC9BmiB,CAAkD,EAElD,IAAMpB,EAAsBhwB,CAAW,CAAC,EAAE,CACpCyxB,EAAwC,OAAjBR,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAEjElyB,EAAiB,IAAImC,IAC3B,IAAK,IAAIqV,KAAoByZ,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAACzZ,EAAiB,CACjC2b,EACqB,OAAzBT,EACIA,CAAoB,CAAClb,EAAiB,CACtC,KAEA4Z,EAAeD,CAAgB,CAAC,EAAE,CAClCoD,EAAmBrkB,EAAYojB,MAAM,CAAC,CAC1C9b,EACA4Z,EACD,EACKC,EAAkB3vB,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB0vB,GAEvCI,EAAoBoD,EACxB31B,EACAkyB,OACsBvvB,IAAtBuxB,EAAkC,KAAOA,EACzCjxB,EACAiwB,EACAoC,EACAlC,GAGIZ,EAAsC,IAAItvB,IAChDsvB,EAAmBnvB,GAAG,CAAC+uB,EAAiBG,GACxCxxB,EAAesC,GAAG,CAACkV,EAAkBia,EACvC,CAIA,IAAMyC,EAAgBl0B,MAAeohB,IAAI,CAErC8S,GAOF7B,EAAyB5T,IAAI,CAACvO,GAGhC,EAVmB,EAUb2kB,EAAoC,OAAjB3C,EAAwBA,CAAY,CAAC,EAAE,CAAG,KAC7D4C,EAAwC,OAAjB5C,EAAwBA,CAAY,CAAC,EAAE,CAAG,KACvE,MAAO,CACLrwB,SAAU,KACV7B,eAAgBA,EAEhBgC,iBAAkCJ,IAArBizB,EAAiCA,EAAmB,KACjE3yB,aAAcgyB,EAAgBhyB,EAAe,CAAC,KAAM,KAAK,CAKzDpB,aAAkCc,IAAzBkzB,EAAqCA,EAAuB,KAIrEj0B,IAAKk0B,IACL9yB,KAAMiyB,EAAiBa,IAA0C,iBAEjE91B,CACF,CACF,EA3RMA,EACAgC,EACAixB,EACAhwB,EACAiwB,EACAjiB,EACAmiB,sBAIFN,EACAjgB,SAAU,IACZ,CAEF,CA4BO,SAAS+e,EACdmE,CAAuB,CACvBC,CAAmD,EAEnDA,EAAgBjpB,IAAI,CAClB,OAAC,YAAE7M,CAAU,CAA6B,GACxC,GAA0B,UAAtB,OAAOA,GAMX,IAAK,IAAMU,KAAwBV,EAAY,CAC7C,GAAM,aACJ+Q,CAAW,CACX1Q,KAAM01B,CAAiB,CACvBn1B,SAAUo1B,CAAW,CACrBlzB,KAAMmzB,CAAW,CAClB,CAAGv1B,EAECs1B,GAOLE,SAqBCA,CA5BiB,CA8BxBnlB,CAA8B,CAC9BglB,CAAoC,CACpCC,CAA8B,CAC9BC,CAAqB,EAYrB,IAAIJ,EAAOM,EACX,IAAK,IAAIvqB,EAAI,EAAGA,EAAImF,EAAY7O,MAAM,CAAE0J,GAAK,EAAG,CAC9C,IAAMyM,EAA2BtH,CAAW,CAACnF,EAAE,CACzCjI,EAAmBoN,CAAW,CAACnF,EAAI,EAAE,CACrC8nB,EAAemC,EAAKljB,QAAQ,CAClC,GAAI+gB,SAAuB,CACzB,IAAMG,EAAYH,EAAaxwB,GAAG,CAACmV,GACnC,QAAkB5V,IAAdoxB,EAAyB,CAC3B,IAAMuC,EAAcvC,EAAUnB,KAAK,CAAC,EAAE,CACtC,GAAI1H,CAAAA,EAAAA,EAAAA,YAAAA,EAAarnB,EAASyyB,GAAc,CAEtCP,EAAOhC,EACP,QACF,CACF,CACF,CAKA,MACF,EAEAwC,SAQOA,EACPR,CAAuB,CACvBE,CAAoC,CACpCC,CAA8B,CAC9BC,CAAqB,EAErB,GAAIJ,MAAkC,GAA7BjD,kBAAkB,CAEzB,OAKF,IAAMc,EAAemC,EAAKljB,QAAQ,CAC5B2jB,EAAWT,EAAKlD,IAAI,CAC1B,GAAIe,SAAuB,CAIR,MAAM,CAAnB4C,IACFC,SA+HGA,EACPrP,CAAoB,CACpBsP,CAA4B,CAC5BC,CAA8B,CAC9BT,CAA8B,CAC9BC,CAAqB,EAYrB,IAAMS,EAAoBF,CAAS,CAAC,EAAE,CAChCG,EAAsBF,CAAW,CAAC,EAAE,CACpCG,EAAeZ,CAAW,CAAC,EAAE,CAK7Bn1B,EAAiBqmB,EAAUrmB,cAAc,CAC/C,IAAK,IAAIwX,KAAoBqe,EAAmB,CAC9C,IAAMG,EACJH,CAAiB,CAACre,EAAiB,CAC/Bye,EACJH,CAAmB,CAACte,EAAiB,CACjC0e,EACJH,CAAY,CAACve,EAAiB,CAE1B2e,EAAkBn2B,EAAeqC,GAAG,CAACmV,GACrC4e,EAAmBJ,CAAc,CAAC,EAAE,CACpCK,EAAsB30B,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB00B,GAE3CE,OACgB10B,IAApBu0B,EACIA,EAAgB9zB,GAAG,CAACg0B,QACpBz0B,CAEiBA,UAAnB00B,CAA8B,QAET10B,IAArBq0B,GACA9L,CAAAA,EAAAA,EAAAA,YAAAA,EAAaiM,EAAkBH,CAAgB,CAAC,EAAE,GAClD,MACIC,EAEFR,EACEY,EACAN,EACAC,EACAC,EACAd,EAPcxzB,CAchB20B,EAAsBP,EAAgBM,EAAgB,MAa9D,CAIA,IAAMz1B,EAAMwlB,EAAUxlB,GAAG,CACnB21B,CAhC6C,CAgCxBrB,CAAW,CAAC,EAAE,CAhCgB,KAiCvC,EAAdt0B,EAGFwlB,EAAUxlB,GAAG,CAAG21B,EACP7E,EAAc9wB,IAIvBA,EAJ6B,OAIlB,CAAC21B,GASd,IAAMv0B,EAAOokB,EAAUpkB,IAAI,CACvB0vB,EAAc1vB,IAChBA,EAAK6J,CADkB,MACX,CAACspB,EAEjB,EAnOQK,EACAT,EAAKnD,KAAK,CACVqD,EACAC,EACAC,GAGFJ,EAAKjD,kBAAkB,CAAG,MAE5B,MACF,CAGA,IAAM0E,EAAiBvB,CAAiB,CAAC,EAAE,CACrCwB,EAAsBvB,CAAW,CAAC,EAAE,CAE1C,IAAK,IAAM3d,KAAoB0d,EAAmB,CAChD,IAAMyB,EACJF,CAAc,CAACjf,EAAiB,CAC5Bof,EACJF,CAAmB,CAAClf,EAAiB,CAEjCwb,EAAYH,EAAaxwB,GAAG,CAACmV,GACnC,QAAkB5V,IAAdoxB,EAAyB,CAC3B,IAAMuC,EAAcvC,EAAUnB,KAAK,CAAC,EAAE,CACtC,GACE1H,CAAAA,EAAAA,EAAAA,YAAAA,EAAawM,CAAsB,CAAC,EAAE,CAAEpB,IAExCqB,MADAA,EAIA,OAAOpB,EACLxC,EACA2D,EALmB/0B,EAOnBwzB,EAGN,CAKF,CAhB2B,EAvDzBJ,EACAE,CAwDI,CAvDJC,EACAC,EAEJ,EAlEUJ,EACA9kB,EACAglB,EACAC,EACAC,EAEJ,CAKAxE,EAAUoE,EAAM,MAClB,EACA,IAEEpE,EAAUoE,EAAMra,EAClB,EAEJ,CA4SO,SAASiW,EAAUoE,CAAuB,CAAEra,CAAU,EAC3D,IAAM0L,EAAY2O,EAAKlD,IAAI,CAC3B,GAAkB,MAAM,CAApBzL,EAEF,OAGF,IAAMwM,EAAemC,EAAKljB,QAAQ,CAClC,GAAqB,MAAM,CAAvB+gB,EAGF0D,EAAsBvB,EAAKnD,KAAK,CAAExL,EAAW1L,QAK7C,IAAK,IAAMqY,KAAaH,EAAahH,MAAM,GAAI,EACnCmH,EAAWrY,GAKzBqa,EAAKjD,kBAAkB,CAAG,IAC5B,CAEA,SAASwE,EACPt1B,CAA8B,CAC9BolB,CAAoB,CACpB1L,CAAU,EAMV,IAAMsW,EAAsBhwB,CAAW,CAAC,EAAE,CACpCjB,EAAiBqmB,EAAUrmB,cAAc,CAC/C,IAAK,IAAIwX,KAAoByZ,EAAqB,CAChD,IAAME,EACJF,CAAmB,CAACzZ,EAAiB,CACjC2e,EAAkBn2B,EAAeqC,GAAG,CAACmV,GAC3C,QAAwB5V,IAApBu0B,EAGF,KAHiC,IAKnC,IAAM/E,EAAeD,CAAgB,CAAC,EAAE,CAClCE,EAAkB3vB,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqB0vB,GACvCkF,EAAiBH,EAAgB9zB,GAAG,CAACgvB,QACpBzvB,IAAnB00B,GACFC,EAAsBpF,EADU,EACwBxW,EAK5D,CACA,IAAM9Z,EAAMwlB,EAAUxlB,GAAG,CACrB8wB,EAAc9wB,KACF,CADQ,KACF,CAAhB8Z,EAEF9Z,EAAIiL,OAAO,CAAC,MAGZjL,EAAIkkB,MAAM,CAACpK,IAQf,IAAM1Y,EAAOokB,EAAUpkB,IAAI,CACvB0vB,EAAc1vB,IAChBA,EAAK6J,CADkB,MACX,CAAC,KAEjB,CAkEA,IAAM+qB,EAAWC,SAkCjB,SAASnF,EAAcrlB,CAAU,EAC/B,OAAOA,GAASA,EAAM6X,GAAG,GAAK0S,CAChC,CAEA,SAAS9B,IAGP,IAFIjpB,EACAiZ,EACEgS,EAAa,IAAIlrB,QAAyB,CAAC+N,EAAKod,KACpDlrB,EAAU8N,EACVmL,EAASiS,CACX,GAmBA,OAlBAD,EAAWpgB,MAAM,CAAG,UACpBogB,EAAWjrB,OAAO,CAAG,IACO,WAAW,CAAjCirB,EAAWpgB,MAAM,GAEnBsgB,EAAatgB,MAAM,CAAG,YADqBogB,EAE9BzqB,KAAK,CAAGA,EACrBR,EAAQQ,GAEZ,EACAyqB,EAAWhS,MAAM,CAAG,IACQ,WAAW,CAAjCgS,EAAWpgB,MAAM,GAEnBugB,EAAYvgB,MAAM,CAAG,WADoBogB,EAE7B5Y,MAAM,CAAGxD,EACrBoK,EAAOpK,GAEX,EACAoc,EAAW5S,GAAG,CAAG0S,EACVE,CACT,gXCltCa5kB,gBAAgB,mBAAhBA,GAHAglB,mBAAmB,mBAAnBA,GAwHGC,iBAAiB,mBAAjBA,GA1CAhjB,iBAAiB,mBAAjBA,GA8FAijB,uBAAuB,mBAAvBA,GAsBAxhB,kBAAkB,mBAAlBA,GAoFAyhB,gBAAgB,mBAAhBA,GA7QArH,2BAA2B,mBAA3BA,GASA3b,+BAA+B,mBAA/BA,GAiIAC,2BAA2B,mBAA3BA,OAlNyB,0BAEZ,WACU,WASP,OAgD5BgjB,EAAmD,KAG1CJ,EAAsB,CAAE3I,SAAS,CAAK,EAGtCrc,EAAmB,CAAEqc,SAAS,CAAM,EAM1C,SAASyB,EAA4BxhB,CAAyB,EACnEiH,GAAAA,EAAAA,eAAAA,EAAgB,KACd6hB,MAAAA,CAAAA,EAAAA,EAA6BtlB,CAA7BslB,sBAAoD,CAACplB,GACrD1D,MAAAA,CAAAA,EAAAA,EAAMwD,CAANxD,sBAA6B,CAAC0oB,GAC9BI,EAA8B9oB,CAChC,EACF,CAGO,SAAS6F,EAAgC7F,CAAkB,EAC5D8oB,IAAgC9oB,IAClC8oB,EADwC,IACV,CAElC,CAIA,IAAMC,EAGe,YAAnB,OAAOC,QAAyB,IAAIA,QAAY,IAAIt1B,IAMhDu1B,EAAoD,IAAIjtB,IAGxDktB,EAC4B,YAAhC,OAAOC,qBACH,IAAIA,qBAAqBC,SA0HtBA,CAAyD,EAChE,IAAK,IAAMC,KAAS30B,EAAS,CAI3B,IAAM40B,EAAYD,EAAME,iBAAiB,CAAG,EAC5CX,EAAwBS,EAAMnrB,MAAM,CAAuBorB,EAC7D,CACF,EAlIgD,CACxCE,WAAY,OACd,GACA,KAEN,SAASC,EAAkB7jB,CAAgB,CAAE8jB,CAA8B,EAErEC,KAAqBx2B,IADA41B,EAAan1B,GAAG,CAACgS,CACN,GAIlCE,EAA4BF,GAG9BmjB,EAAal1B,GAAG,CAAC+R,EAAS8jB,GACT,MAAM,CAAnBR,GACFA,EAASU,OAAO,CAAChkB,EAErB,CAEA,SAASikB,EAAsB34B,CAAY,EACzC,GAAI,CACF,MAAOoc,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBpc,EAC3B,CAAE,QAAM,CAWN,MAHA44B,CADyB,YAAvB,OAAOC,YAA6BA,YAAcnT,QAAQ1K,KAAAA,EAEzD,oBAAmBhb,EAAK,8CAEpB,IACT,CACF,CAEO,SAASyU,EACdC,CAAoB,CACpB1U,CAAY,CACZ4T,CAAyB,CACzBxJ,CAA2C,CAC3C2J,CAAwB,CACxBzB,CAA+D,EAE/D,GAAIyB,EAAiB,CACnB,IAAM+kB,EAAcH,EAAsB34B,GAC1C,GAAoB,OAAhB84B,EAAsB,CACxB,IAAMN,EAAqC,QACzC5kB,OACAxJ,EACAguB,WAAW,EACXW,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAcJ,EAAY94B,IAAI,yBAC9BsS,CACF,EAIA,OADAimB,EAAkB7jB,EAAS8jB,GACpBA,CACT,CACF,CAaA,MAV8C,CAUvCA,OATL5kB,OACAxJ,EACAguB,WAAW,EACXW,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAc,6BACd5mB,CACF,CAEF,CAEO,SAASmlB,EACd/iB,CAAwB,CACxB1U,CAAY,CACZ4T,CAAyB,CACzBxJ,CAA2C,EAE3C,IAAM0uB,EAAcH,EAAsB34B,EACtB,MAAM,EAAtB84B,GAiBJP,EAAkB7jB,EAVa,OAUJ8jB,CATzB5kB,OACAxJ,EACAguB,WAAW,EACXW,qBAAqB,EACrBC,aAAc,KACdC,aAAc,CAAC,EACfC,aAAcJ,EAAY94B,IAAI,CAC9BsS,wBAAyB,IAC3B,EAEF,CAEO,SAASsC,EAA4BF,CAAgB,EAC1D,IAAM8jB,EAAWX,EAAan1B,GAAG,CAACgS,GAClC,QAAiBzS,IAAbu2B,EAAwB,CAC1BX,EAAajW,MAAM,CAAClN,GACpBqjB,EAAuBnW,MAAM,CAAC4W,GAC9B,IAAMQ,EAAeR,EAASQ,YAAY,MACf,EAAvBA,GACFlS,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBkS,EAEvB,CACiB,MAAM,CAAnBhB,GACFA,EAASmB,SAAS,CAACzkB,EAEvB,CAYO,SAASgjB,EAAwBhjB,CAAgB,CAAE0jB,CAAkB,EAQ1E,IAAMI,EAAWX,EAAan1B,GAAG,CAACgS,EACjBzS,UAAbu2B,CAAwB,GAI5BA,EAASJ,SAAS,CAAGA,EACjBA,EACFL,EAAuBpW,GAAG,CAAC6W,GADd,EAGU5W,MAAM,CAAC4W,GAEhCY,EAAuBZ,GACzB,CAEO,SAAStiB,EACdxB,CAAwC,CACxC2kB,CAA0C,EAE1C,IAAMb,EAAWX,EAAan1B,GAAG,CAACgS,EACjBzS,UAAbu2B,CAAwB,OAIXv2B,IAAbu2B,IACFA,EAASO,CADiB,kBACE,EAAG,EAQ/BK,EAAuBZ,GAE3B,CAEA,SAASY,EAAuBZ,CAA8B,EAC5D,IAAMc,EAAuBd,EAASQ,YAAY,CAElD,GAAI,CAACR,EAASJ,SAAS,CAAE,CAGM,MAAM,CAA/BkB,GACFxS,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBwS,GAMrB,MACF,CA8CF,CAEO,SAAS3B,EACdvc,CAAsB,CACtBvb,CAAuB,EASvB,IAAM05B,EAAsBvS,CAAAA,EAAAA,EAAAA,sBAAAA,IAC5B,IAAK,IAAMwR,KAAYT,EAAwB,CAC7C,IAAM1C,EAAOmD,EAASQ,YAAY,CAClC,GACW,OAAT3D,GACAmD,EAASS,YAAY,GAAKM,GAC1BlE,EAAK/0B,GAAG,CAAC8a,OAAO,GAAKA,GACrBia,EAAK5I,oBAAoB,GAAK5sB,EAI9B,IAHA,IAOW,MAAM,EAAfw1B,GACFvO,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBuO,GAErB,IAAMvzB,EAAWilB,CAAAA,EAAAA,EAAAA,cAAAA,EAAeyR,EAASU,YAAY,CAAE9d,GACjD5V,EAAWgzB,EAASO,mBAAmB,CACzClS,EAAAA,gBAAgB,CAAC2S,MAAM,CACvB3S,EAAAA,gBAAgB,CAAC4S,OAAO,CAC5BjB,EAASQ,YAAY,CAAGU,CAAAA,EAAAA,EAAAA,oBAAAA,EACtB53B,EACAjC,EACA24B,EAASpuB,IAAI,GAAK6J,EAAAA,YAAY,CAACE,IAAI,CACnC3O,GAEFgzB,EAASS,YAAY,CAAGjS,CAAAA,EAAAA,EAAAA,sBAAAA,GAC1B,CACF,kUCvXgB1E,qCAAAA,aATU,OASnB,SAASA,EAAc5S,CAAY,CAAEC,CAAc,EACxD,GAAI,UAA0B,OAAnBD,EACT,OAAO,EAGT,GAAM,UAAEE,CAAQ,CAAE,CAAGE,CAAAA,EAAAA,EAAAA,SAAAA,EAAUJ,GAC/B,OAAOE,IAAaD,GAAUC,EAAS9G,UAAU,CAAC6G,EAAS,IAC7D,0JCYgBuC,SAAS,mBAATA,GA6DAynB,oBAAoB,mBAApBA,GAfHC,aAAa,mBAAbA,wBAlDgB,QAEvBC,EAAmB,yBAElB,SAAS3nB,EAAU4nB,CAAiB,EACzC,GAAI,MAAEC,CAAI,UAAErgB,CAAQ,CAAE,CAAGogB,EACrBrgB,EAAWqgB,EAAOrgB,QAAQ,EAAI,GAC9B7J,EAAWkqB,EAAOlqB,QAAQ,EAAI,GAC9B5M,EAAO82B,EAAO92B,IAAI,EAAI,GACtB6M,EAAQiqB,EAAOjqB,KAAK,EAAI,GACxBmqB,GAAuB,EAE3BD,EAAOA,EAAOhW,mBAAmBgW,GAAM/mB,OAAO,CAAC,OAAQ,KAAO,IAAM,GAEhE8mB,EAAOE,IAAI,CACbA,CADe,CACRD,EAAOD,EAAOE,IAAI,CAChBtgB,IACTsgB,EAAOD,EAAQ,EAACrgB,EAASyI,CAAV,MAAiB,CAAC,KAAQ,IAAGzI,EAAS,IAAKA,CAAAA,CAAAA,CAAO,EACtDC,IAAI,EAAE,CACfqgB,GAAQ,IAAMF,EAAOngB,IAAI,GAIzB9J,GAA0B,UAAjB,OAAOA,IAClBA,EAAQoqB,OAAOC,EAAYC,sBAAsB,CAACtqB,GAAAA,EAGpD,IAAIqb,EAAS4O,EAAO5O,MAAM,EAAKrb,GAAU,IAAGA,GAAY,GAoBxD,OAlBI4J,GAAY,CAACA,EAASxQ,QAAQ,CAAC,OAAMwQ,GAAY,KAGnDqgB,EAAOM,OAAO,EACZ,EAAC3gB,GAAYogB,EAAiB90B,IAAI,CAAC0U,EAAAA,CAAAA,CAAQ,CAAe,KAATugB,EACnD,CACAA,EAAO,MAAQA,CAAAA,EAAQ,IAAC,GACQ,MAAhBpqB,CAAQ,CAAC,EAAE,GAAUA,EAAW,IAAMA,CAAAA,GAC7C,IACToqB,EADgB,EACT,EAGLh3B,GAAQA,OAAI,CAAC,EAAE,GAAUA,EAAO,IAAMA,CAAAA,EACtCkoB,GAAwB,MAAdA,CAAM,CAAC,EAAE,GAAUA,EAAS,IAAMA,CAAAA,EAKxC,GAAEzR,EAAWugB,GAHrBpqB,EAAWA,EAGiBA,OAHD,CAAC,GAGWsb,KAHFnH,mBAAAA,GACrCmH,EAASA,EAAOlY,OAAO,CAAC,IAAK,QAEmBhQ,CAClD,CAEO,IAAM42B,EAAgB,CAC3B,OACA,OACA,OACA,WACA,OACA,OACA,WACA,OACA,WACA,QACA,SACA,UACD,CAEM,SAASD,EAAqBl6B,CAAc,EAajD,OAAOyS,EAAUzS,EACnB,sGC5FS46B,qCAAAA,KAXT,IAAIA,EAAY,IAAgB,wHCEhB5pB,qCAAAA,SAAAA,EACd7Q,CAA8B,CAC9B06B,CAA2B,EAG3B,IAAMC,EAAqB36B,CAAW,CAAC,EAAE,CACnC46B,EAAkBF,CAAQ,CAAC,EAAE,CAKnC,GAAIppB,MAAMwZ,OAAO,CAAC6P,IAAuBrpB,MAAMwZ,OAAO,CAAC8P,IAGrD,GACED,CAAkB,CAAC,EAAE,GAAKC,CAAe,CAAC,EAAE,EAC5CD,CAAkB,CAAC,EAAE,GAAKC,CAAe,CAAC,EAAE,CAE5C,CADA,MACO,CACT,MACK,GAAID,IAAuBC,EAChC,OAAO,EAIT,GAAI56B,CAAW,CAAC,CALmC,CAKjC,CAEhB,CAFkB,KAEX,CAAC06B,CAAQ,CAAC,EAAE,CAGrB,GAAIA,CAAQ,CAAC,EAAE,CACb,CADe,MACR,EAKT,IAAMG,EAAmBj6B,OAAO0rB,MAAM,CAACtsB,CAAW,CAAC,EAAE,CAAC,CAAC,EAAE,CACnD86B,EAAgBl6B,OAAO0rB,MAAM,CAACoO,CAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,OACnD,CAAKG,GAAD,CAAsBC,GACnBjqB,EAA4BgqB,EAAkBC,EACvD,MAF2C,CAAhB,MAAuB,4UChClCC,qCAAAA,SAAAA,EACd35B,CAAmB,CACnBK,CAAwB,CACxBmP,CAAoC,EAEpC,IAAMsH,EAActH,EAAkB9O,MAAM,EAAI,EAE1C,CAACmW,EAAkB1U,EAAQ,CAAGqN,EAC9B1O,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBoB,GAEhC4U,EACJ1W,EAAchB,cAAc,CAACqC,GAAG,CAACmV,GAE/BG,EAAkBhX,EAASX,cAAc,CAACqC,GAAG,CAACmV,GAE7CG,GAAmBA,IAAoBD,IAC1CC,EAAkB,IAAIxV,IAAIuV,GAC1B/W,EAASX,MAF0D,QAE5C,CAACsC,GAAG,CAACkV,EAAkBG,IAGhD,IAAMC,EAAyBF,MAAAA,EAAAA,KAAAA,EAAAA,EAAyBrV,GAAG,CAACZ,GACxDoW,EAAiBF,EAAgBtV,GAAG,CAACZ,GAGzC,GAAIgW,EAAa,CAEb,GACCI,EAAehW,QAAQ,EACxBgW,GADA,CACmBD,GAEnBD,EAAgBrV,GAAG,CAACb,EAAU,CAC5BI,SAAU,GAFZ,EAGEhB,IAAK,KACLmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,IACpBrB,QAAS,KACT7B,YAAa,CAAC,CAChB,GAEF,MACF,CAEA,GAAI,CAAC4Y,GAAkB,CAACD,EAAwB,CAE1C,GACFD,EAAgBrV,GAAG,CAACb,EAAU,CAC5BI,IAFiB,KAEP,KACVhB,IAAK,KACLmB,YAAa,KACbC,KAAM,KACNC,aAAc,KACdlC,eAAgB,IAAImC,IACpBrB,QAAS,KACT7B,YAAa,CAAC,CAChB,GAEF,MACF,CAeA,OAbI4Y,IAAmBD,IACrBC,EAAiB,CACfhW,SAAUgW,EAAehW,MAFkB,EAEV,CACjChB,IAAKgX,EAAehX,GAAG,CACvBmB,YAAa6V,EAAe7V,WAAW,CACvCC,KAAM4V,EAAe5V,IAAI,CACzBC,aAAc2V,EAAe3V,YAAY,CACzClC,eAAgB,IAAImC,IAAI0V,EAAe7X,cAAc,EACrDc,QAAS+W,EAAe/W,OAAO,EAEjC6W,EAAgBrV,GAAG,CAACb,EAAUoW,IAGzByiB,EACLziB,EACAD,EACAwS,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBja,GAE7B,aArFyC,WACJ,uUCGxBoqB,qCAAAA,mCACX,yBACA,uBACA,gBAmDA,qBAtDK,OAAMA,EAcX9N,QAAW+N,CAA2B,CAAc,CAIlD,IAHIC,EACAC,EAEEC,EAAc,IAAI9uB,QAAQ,CAACC,EAASiZ,KACxC0V,EAAc3uB,EACd4uB,EAAa3V,CACf,GAEMiQ,EAAO,UACX,GAAI,CACF,QAAI,CAAC,QACL,IAAM/b,EAAS,MAAMuhB,IACrBC,EAAYxhB,EACd,CAAE,MAAO0B,EAAO,CACd+f,EAAW/f,EACb,QAAU,CACR,QAAI,CAAC,QACL,QAAI,CAAC,OACP,CACF,EAOA,OAHA,QAAI,CAAC,MAAO8D,IAAI,CAFM,CAAE+b,UAAWG,EAAa3F,MAAK,GAGrD,QAAI,CAAC,QAEE2F,CACT,CAEAC,KAAKJ,CAAuB,CAAE,CAC5B,IAAM1pB,EAAQ,QAAI,CAAC,MAAO+pB,SAAS,CAAC,GAAUvU,EAAKkU,SAAS,GAAKA,GAEjE,GAAI1pB,EAAQ,CAAC,EAAG,CACd,IAAMgqB,EAAa,QAAI,CAAC,MAAOC,MAAM,CAACjqB,EAAO,EAAE,CAAC,EAAE,CAClD,QAAI,CAAC,MAAOkqB,OAAO,CAACF,GACpB,QAAI,CAAC,OAAa,EACpB,CACF,CA5CAvgB,YAAY0gB,EAAiB,CAAC,CAAE,CA8ChC,wCArDA,yDACA,yDACA,yDAME,QAAI,CAAC,MAAkBA,EACvB,QAAI,CAAC,MAAgB,EACrB,QAAI,CAAC,MAAS,EAAE,CAmDpB,CARE,WAAaC,CAAc,EACzB,GADWA,KAAAA,IAAAA,IAAAA,GAAS,GAEjB,SAAI,CAAC,MAAgB,QAAI,CAAC,OAAmBA,CAAAA,CAAAA,CAAK,CACnD,QAAI,CAAC,MAAO75B,MAAM,CAAG,EACrB,KACA,CAAiB,QAAjB,UAAI,CAAC,MAAO85B,KAAK,KAAjB,EAAqBnG,IAAI,EAC3B,CACF,yUCjEW9S,qCAAAA,KAAN,IAAMA,EACX,6RCCF,SAASzb,EAAc,CAKM,MA8EzB3B,EAnFmB,WACrBA,CAAM,KACNX,CAAG,OACHmB,CAAK,SACLD,CAAO,CACoB,CALN,EAiFf+1B,EACJ/1B,IAAAA,OAAAA,EACAP,EAAOmC,SAAAA,EAAS,OAAhBnC,EAAkBu2B,MAAM,CAAC,CAACC,EAAMC,IAC9BjzB,KAAKkzB,GAAG,CAACD,MAAME,KAAkBD,GAAG,CAACF,EAtFzB,IAsF6CC,CAAbE,CAAmBH,EAAAA,CAAAA,KAEjEG,OAEQ32B,EAAOuK,IAAI,CAAC,QAAOqU,mBAAmBvf,GAAK,MAAKmB,EAAM,MAAK81B,GACnEj3B,CAAAA,CAAIsE,UAAU,CAAC,wBAEX,GAFsC4C,KAA8B,GACnE,CAAqC,2EAS9C,0CAFA5E,EAAci1B,kBAAkB,EAAG,MAEnC,EAAej1B,iBChGR,SAAS4mB,EAAoBwE,CAAa,EAC/C,OAAOA,EAAMlf,OAAO,CAAC,MAAO,KAAO,GACrC,gGAFgB0a,qCAAAA,uICCAsO,qCAAAA,SAAAA,EACdh7B,CAAmB,CACnBK,CAAwB,CACxBmP,CAAoC,EAEpC,IAAMsH,EAActH,EAAkB9O,MAAM,EAAI,EAC1C,CAACmW,EAAkB1U,EAAQ,CAAGqN,EAE9B1O,EAAWC,CAAAA,EAAAA,EAAAA,oBAAAA,EAAqBoB,GAEhC4U,EACJ1W,EAAchB,cAAc,CAACqC,GAAG,CAACmV,GAEnC,GAAI,CAACE,EAGH,OAGF,IAAIC,EAAkBhX,EAASX,QAND,MAMe,CAACqC,GAAG,CAACmV,GAOlD,GANKG,GAAmBA,IAAoBD,IAC1CC,EAAkB,IAAIxV,IAAIuV,GAC1B/W,EAASX,MAF0D,QAE5C,CAACsC,GAAG,CAACkV,EAAkBG,IAI5CF,EAAa,YACfE,EAAgB4J,MAAM,CAAC9f,GAIzB,IAAMmW,EAAyBF,EAAwBrV,GAAG,CAACZ,GACvDoW,EAAiBF,EAAgBtV,GAAG,CAACZ,GAEpCoW,GAAmBD,IAMpBC,IAAmBD,IACrBC,EAAiB,CAPI,SAAyB,EAQnBhW,MAFkB,EAEV,CACjChB,IAAKgX,EAAehX,GAAG,CACvBmB,YAAa6V,EAAe7V,WAAW,CACvCC,KAAM4V,EAAe5V,IAAI,CACzBC,aAAc2V,EAAe3V,YAAY,CACzClC,eAAgB,IAAImC,IAAI0V,EAAe7X,cAAc,CACvD,EACA2X,EAAgBrV,GAAG,CAACb,EAAUoW,IAGhC8jB,EACE9jB,EACAD,EACAwS,CAAAA,EAAAA,EAAAA,wBAAAA,EAAyBja,IAE7B,aA/DqC,WACI,4UC4H5ByrB,qCAAAA,OA/HuB,QACF,SACU,SACA,SAOV,SACJ,SACE,SAEK,SACC,SACY,WA+GrCA,EAPb,SAASC,CACoB,CAC3B1O,CAAyB,EAEzB,IA5FyDjuB,GA4FlDA,CACT,CAGsB,GAChB28B,wBACAC,CAAqBA,6MCjI3B,uDAAyF,yJCgCzF,OAAoB,mBAApB,GAjBgBC,aAAa,mBAAbA,wBAbY,UACN,cAGI,QASnB,SAASA,EAAcC,CAAoB,EAChD,GAAM,OAAE/wB,CAAK,CAAE,CAAGjH,CAAAA,EAAAA,EAAAA,WAAAA,EAAYg4B,EAAU,CACtCv1B,cAAAA,EAAAA,OAAa,CAEbH,QAAS+E,CAAAA,YAAAA,CAAAA,IAAAA,IAAAA,IAAAA,KAAAA,KAAAA,KAAAA,KAAAA,KAAAA,CAAAA,WAAAA,CAAAA,GAAAA,GAAAA,GAAAA,GAAAA,GAAAA,IAAAA,IAAAA,IAAAA,CAAAA,KAAAA,eAAAA,OAAAA,UAAAA,oBAAAA,CAAAA,EAAAA,YAAAA,CAAAA,CAAAA,CAA6B,GAKxC,IAAK,GAAM,CAACpL,EAAKqM,EAAM,GAAInM,OAAOgD,OAAO,CAAC8H,OAAQ,CAClCrJ,IAAV0K,GACF,IADuB,GAChBrB,CAAK,CAAChL,EAA0B,CAG3C,MAAO,OAAEgL,CAAM,CACjB,KAEA,EAAeE,EAAAA,KAAK,6GC3BJmF,qCAAAA,aAL8B,WACF,OAIrC,SAASA,EACdrR,CAAmB,CACnB+B,CAAwB,CACxBtB,CAAgB,CAChBP,CAAgC,CAChCqX,CAAkC,EAGlC,GAAM,CAAEhX,KAAMU,CAAS,UAAEH,CAAQ,MAAEkC,CAAI,cAAE3B,CAAY,CAAE,CAAGnB,EAG1D,GAAIY,MAAmB,GACrB,OAAO,EAGT,GAAIO,EAAc,CAChB,IAAMO,EAAMd,CAAQ,CAAC,EAAE,CAEvBL,EAAMoB,OAAO,CADGf,CAAQ,CACRe,EADW,CAE3BpB,EAAMmB,GAAG,CAAGA,EAMZnB,EAAMsC,WAAW,CAAG,KACpBuU,CAAAA,EAAAA,EAAAA,6BAAAA,EACEtX,EACAS,EACAsB,EACAd,EACAH,EACAkC,EACAuU,EAEJ,MAEE9W,CAFK,CAECmB,GAAG,CAAGG,EAAcH,GAAG,CAI7BnB,EAAMsC,WAAW,CAAGhB,EAAcgB,WAAW,CAC7CtC,EAAMM,cAAc,CAAG,IAAImC,IAAInB,EAAchB,cAAc,EAC3DN,EAAMoB,OAAO,CAAGE,EAAcF,OAAO,CAErCuW,CAAAA,EAAAA,EAAAA,2BAAAA,EACEpY,EACAS,EACAsB,EACA7B,EACAqX,GAIJ,OAAO,CACT,gXCnDagW,aAAa,mBAAbA,GAEAgE,eAAe,mBAAfA,aAPgB,WAItB,OACMhE,EAAgB,IAAI+N,EAAAA,YAAY,CAAC,GAEjC/J,EAcb,SAASyL,CACoB,CAC3BtsB,CAAsB,EAGtB8a,CAAAA,CAnB6Bpf,CAmB7Bof,EAAAA,GAnBoE,GAClEyR,CAAwCA,GACxCD,QAiBFxR,EAAmBvrB,EAAMuc,aAAa,EAEtC,GAAM,KAAErc,CAAG,CAAE,CAAGuQ,EAWhB,MATA6a,CAAAA,EAAAA,EAAAA,6BAAAA,EAA8B,KAC5BprB,EACA2b,QAAS7b,EAAM6b,OAAO,CACtBU,cAAevc,EAAMuc,aAAa,CAClC1R,KAAM4F,EAAO5F,IAAI,CACjBvK,KAAMN,EAAMM,IAAI,CAChBwrB,eAAe,CACjB,GAEO9rB,CACT,wOC3CO,SAASi9B,EACd97B,CAA6B,EAE7B,IAAMmP,EAAwB,CAAC,EAC/B,IAAK,GAAM,CAACvP,EAAKqM,EAAM,GAAIjM,EAAa8C,OAAO,GAAI,CACjD,IAAMi5B,EAAW5sB,CAAK,CAACvP,EAAI,MACH,IAAbm8B,EACT5sB,CAAK,CAACvP,EAAI,CAAGqM,EADsB,MAEpB+d,OAAO,CAAC+R,GACvBA,EAAS3d,IAAI,CAACnS,CADoB,EAGlCkD,CAAK,CAACvP,EAAI,CAAG,CAACm8B,EAAU9vB,EAAM,CAGlC,OAAOkD,CACT,CAEA,SAAS6sB,EAAuBC,CAAc,QAC5C,UAAI,OAAOA,EACFA,GAIW,UAAjB,EAA6B,KAAtBA,GAAuBC,MAAMD,EAAAA,GACpB,WAAjB,OAAOA,EAIA,GAFA1C,OAAO0C,EAIlB,CAEO,SAASxC,EAAuBtqB,CAAqB,EAC1D,IAAMnP,EAAe,IAAIm8B,gBACzB,IAAK,GAAM,CAACv8B,EAAKqM,EAAM,GAAInM,OAAOgD,OAAO,CAACqM,GACxC,GAAIqB,CAD4C,KACtCwZ,OAAO,CAAC/d,GAChB,IAAK,CADmB,GACbga,KAAQha,EACjBjM,EAAao8B,EADW,IACL,CAACx8B,EAAKo8B,EAAuB/V,SAGlDjmB,EAAaiC,GAAG,CAACrC,EAAKo8B,EAAuB/vB,IAGjD,OAAOjM,CACT,CAEO,SAAS0I,EACd4D,CAAuB,EACvB,2BAAG+vB,EAAH,6BAAGA,CAAAA,CAAH,iBAAsC,CAEtC,IAAK,IAAMr8B,KAAgBq8B,EAAkB,CAC3C,IAAK,IAAMz8B,KAAOI,EAAae,IAAI,GAAI,EAC9BmgB,MAAM,CAACthB,GAGhB,IAAK,GAAM,CAACA,EAAKqM,EAAM,GAAIjM,EAAa8C,OAAO,GAAI,EAC1Cs5B,MAAM,CAACx8B,EAAKqM,EAEvB,CAEA,OAAOK,CACT,wIAfgB5D,MAAM,mBAANA,GA9CAozB,sBAAsB,mBAAtBA,GAgCArC,sBAAsB,mBAAtBA,6JCsFA7e,kBAAkB,mBAAlBA,GA9EA0hB,gCAAgC,mBAAhCA,GA+FAnd,iBAAiB,mBAA1B,SAASA,EACdjgB,CAA8B,CAC9Bq9B,CAAmB,EAInB,IAAK,IAAM15B,KAJX05B,KAAAA,IAAAA,IAAAA,EAAiB,EAAC,EAIUz8B,OAAO0rB,MAAM,CAFlBtsB,CAAW,CAAC,EAAE,GAEsB,CACzD,IAAMuD,EAAUI,CAAa,CAAC,EAAE,CAC1B25B,EAAqBhsB,MAAMwZ,OAAO,CAACvnB,GACnCg6B,EAAeD,EAAqB/5B,CAAO,CAAC,EAAE,CAAGA,CACnD,EAACg6B,GAAgBA,EAAar0B,UAAU,CAAC1G,EAAAA,gBAAgB,GAAG,CAI9D86B,IAAsC,MAAf/5B,CAAO,CAAC,EAAE,EAA2B,MAArCA,CAAsBA,CAAO,CAAC,EAAE,EAAQ,CAGzD,CAACA,CAAO,CAAC,EAAE,CAAC,CAAGA,CAAO,CAAC,EAAE,CAAC6F,KAAK,CAAC,KAC7Bk0B,IACTD,CAAM,CAAC95B,CAAO,CAAC,EAAE,CAAC,CAAGA,CAAO,CAAC,IAG/B85B,EAASpd,EAAkBtc,EAAe05B,GAC5C,CAEA,OAAOA,CACT,aA/J2C,WAMpC,WACsB,OAEvBG,EAAqB,GACH,MAAfj6B,CAAO,CAAC,EAAE,CAAWA,EAAQ4N,KAAK,CAAC,GAAK5N,EAG3Ck6B,EAAoB,GACxB,UAAI,OAAOl6B,EAGT,YAA4B,CAAxBA,EAA+B,GAE5BA,EAGFA,CAAO,CAAC,EAAE,CAGnB,SAASm6B,EAAkBC,CAAkB,EAC3C,OACEA,EAAS7B,MAAM,CAAC,CAAC8B,EAAKr6B,IAEpB,MADAA,EAAUi6B,EAAmBj6B,EAAAA,GACPs6B,CAAAA,EAAAA,EAAAA,cAAAA,EAAet6B,GAC5Bq6B,EAGCA,EAAI,GAJiC,CAI9Br6B,EAChB,KAAO,GAEd,CAEO,SAAS65B,EACd95B,CAAoC,MAebA,EAbvB,IAAMC,EAAU+N,MAAMwZ,OAAO,CAACxnB,CAAiB,CAAC,EAAE,EAC9CA,CAAiB,CAAC,EAAE,CAAC,EAAE,CACvBA,CAAiB,CAAC,EAAE,CAExB,GACEC,IAAYorB,EAAAA,mBAAmB,EAC/BmP,EAAAA,0BAA0B,CAACpR,IAAI,CAAC,GAAOnpB,EAAQ2F,UAAU,CAAC60B,IAE1D,OAAO17B,GAELkB,EAAQ2F,UAAU,CAAC1G,EAAAA,gBAAgB,EAAG,MAAO,GAEjD,IAAMm7B,EAAW,CAACF,EAAkBl6B,GAAS,CACvC9C,EAAiB6C,MAAAA,GAAAA,CAAiB,CAAC,IAAlBA,EAAwB,CAAC,EAE1C06B,EAAev9B,EAAe8R,QAAQ,CACxC6qB,EAAiC38B,EAAe8R,QAAQ,EACxDlQ,OAEJ,QAAqBA,IAAjB27B,EACFL,EAASze,GADqB,CACjB,CAAC8e,QAEd,IAAK,GAAM,CAACt9B,EAAKqM,EAAM,GAAInM,OAAOgD,OAAO,CAACnD,GAAiB,CACzD,GAAY,aAARC,EAAoB,SAExB,IAAMu9B,EAAYb,EAAiCrwB,QAEjC1K,IAAd47B,GACFN,EAASze,EADkB,EACd,CAAC+e,EAElB,CAGF,OAAOP,EAAkBC,EAC3B,CAyCO,SAASjiB,EACdwiB,CAAwB,CACxBC,CAAwB,EAExB,IAAM1iB,EAAc2iB,SA3CbA,EACPF,CAAwB,CACxBC,CAAwB,EAExB,GAAM,CAACE,EAAUC,EAAgB,CAAGJ,EAC9B,CAACK,EAAUC,EAAgB,CAAGL,EAE9BM,EAAqBhB,EAAkBY,GACvCK,EAAqBjB,EAAkBc,GAE7C,GACET,EAAAA,0BAA0B,CAACpR,IAAI,CAC7B,GACE+R,EAAmBv1B,UAAU,CAAC60B,IAAMW,EAAmBx1B,UAAU,CAAC60B,IAGtE,CADA,KACO,GAGT,GAAI,CAACnT,CAAAA,EAAAA,EAAAA,YAAAA,EAAayT,EAAUE,GAAW,KAE9BnB,EAAP,OAAOA,MAAAA,GAAAA,EAAiCe,EAAAA,CAAAA,CAAjCf,EAA2C,EACpD,CAEA,IAAK,IAAMuB,KAAqBL,EAC9B,GAAIE,CAAe,CAACG,EAAkB,CAAE,CACtC,IAAMljB,CAFuC,CAEzB2iB,EAClBE,CAAe,CAACK,EAAkB,CAClCH,CAAe,CAACG,EAAkB,EAEpC,GAAoB,MAAM,CAAtBljB,EACF,OAAUgiB,EAAkBc,GAAU,IAAG9iB,CAE7C,CAGF,OAAO,IACT,EAM6CyiB,EAAOC,UAE9C1iB,SAAuC,KAAK,CAArBA,EAClBA,EAIFiiB,EAAkBjiB,EAAYrS,KAAK,CAAC,KAC7C,gXCXgB+kB,wCAAwC,mBAAjD,SAASA,EACdluB,CAAuB,CACvB6P,CAAY,EAEZ,GAAM,CAACvM,EAAS9C,GAAkBm+B,EAAc,CAAG3+B,EAOnD,IAAK,IAAMS,KALP6C,EAAQhB,QAAQ,CAACC,EAAAA,gBAAgB,GAAuB,WAAW,CAA7Bo8B,IACxC3+B,CAAI,CAAC,EAAE,CAAG6P,EACV7P,CAAI,CAAC,EAAE,CAAG,WAGMQ,EAChB0tB,EAAyC1tB,CAAc,CAACC,EAAI,CAAEoP,EAElE,GA5GsBkW,CAyGc,8BAzGiB,mBAA/BA,aAxBU,WACI,UACH,OAsB1B,eAAeA,EACpB+K,CAAwC,EAExC,IAAM8N,EAAkB,IAAI3zB,GAC5B,OAAM4zB,EAAoC,CACxC,GAAG/N,CAAO,CACVgO,SAAUhO,EAAQ9K,WAAW,iBAC7B4Y,CACF,EACF,CAEA,eAAeC,EAAoC,CAYlD,EAZkD,gBACjDp/B,CAAW,OACXC,CAAK,aACLsmB,CAAW,cACXC,CAAY,gBACZC,CAAc,iBACd0Y,CAAe,UACfE,EAAW9Y,CAAW,cACtB/iB,CAAY,CAIb,CAZkD,EAa3C,EAAGzC,EAAgBu+B,EAAaJ,EAAc,CAAG3Y,EACjDgZ,EAAgB,EAAE,CAExB,GACED,GACAA,IAAgB97B,GACE,YAAlB07B,CACA,EAEA,CAACC,EAAgBK,GAAG,CAACF,GACrB,CACAH,EAAgB9c,GAAG,CAACid,GAIpB,IAAMG,EAAe/W,CAAAA,EAAAA,CAJY,CAIZA,mBAAAA,EACnB,IAAIvQ,CALsD,GAKlDmnB,EAAa9oB,SAAS0B,MAAM,EACpC,CAGEtU,UAbwF,QAarE,CAACy7B,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAEA,CAAQ,CAAC,EAAE,CAAE,UAAU,CACrEvjB,QAAS2K,EAAiBxmB,EAAM6b,OAAO,CAAG,IAC5C,GACA/O,IAAI,CAAC,OAAC,YAAE7M,CAAU,CAAE,GACpB,GAA0B,UAAtB,OAAOA,EACT,IAAK,IAAMw/B,KAAkBx/B,EAI3BmR,CAAAA,EAAAA,EAAAA,IAJuC,WAIvCA,EACErR,EACAwmB,EACAA,EACAkZ,EAQR,GAEAH,EAAc/f,IAAI,CAACigB,EACrB,CAEA,IAAK,IAAMz+B,KAAOD,EAAgB,CAChC,IAAM4+B,EAAuBP,EAAoC,aAC/Dp/B,QACAC,EACAsmB,YAAaxlB,CAAc,CAACC,EAAI,cAChCwlB,iBACAC,EACA0Y,2BACAE,eACA77B,CACF,GAEA+7B,EAAc/f,IAAI,CAACmgB,EACrB,CAEA,MAAM/yB,QAAQgzB,GAAG,CAACL,EACpB,mUCxGgBM,qCAAAA,aATkB,WAMe,OAG1C,SAASA,EACd5/B,CAA2B,CAC3ByQ,CAAqB,MAmCVgtB,EAjCX,GAAM,KAAEv9B,CAAG,CAAEI,MAAI,CAAE,CAAGmQ,EAChBhQ,EAAOC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBR,GAOzB2/B,EAAgBv/B,GAAQN,EAAMM,IAAI,CAElCw/B,EAAW9/B,EAAMQ,KAAK,CAS5B,MAAO,CAEL+C,aAAc9C,EACdqQ,QAAS,CACPC,aAAa,EACbiL,eAAe,EAEfpL,4BAA4B,CAC9B,EACAqL,kBAAmBjc,EAAMic,iBAAiB,CAC1Czb,MAZEs/B,CAYKr+B,CACP8a,cAAevc,EAAMuc,aAAa,CAElCjc,KAAMu/B,EACNhkB,QAAS4hB,OAAAA,EAAAA,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiCoC,EAAAA,CAAAA,CAAjCpC,EAAmDv9B,EAAImQ,QAAQ,CAE5E,GAzCqD,sXC6BrCQ,iBAAiB,mBAAjBA,GAsIAkvB,eAAe,mBAAxB,SAASA,EACd//B,CAA2B,CAC3ByQ,CAAsB,EAEtB,GAAM,KAAEvQ,CAAG,eAAE8wB,CAAa,cAAEF,CAAY,cAAElV,CAAY,eAAEkQ,CAAa,CAAE,CACrErb,EACItQ,EAAmB,CAAC,EACpB,MAAEsD,CAAI,CAAE,CAAGvD,EACXO,EAAOC,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBR,GACzB6Q,EAA+B,SAAjB+f,EAOpB,GALAvF,CAAAA,EAAAA,EAAAA,kBAAAA,EAAmBvrB,EAAMuc,aAAa,EAEtCpc,EAAQyQ,0BAA0B,EAAG,EACrCzQ,EAAQ4Q,WAAW,CAAGA,EAElBigB,EACF,OAAOngB,EAAkB7Q,EAAOG,EADf,EAC4B6/B,QAAQ,GAAIjvB,GAK3D,GAAIsY,SAAS4W,cAAc,CAAC,wBAC1B,CADmD,MAC5CpvB,EAAkB7Q,EAAOG,EAASM,EAAMsQ,GAsBjD,IAAMmvB,EAAiB5U,CAAAA,EAAAA,EAAAA,6BAAAA,EAA8B,KACnDprB,EACA2b,QAAS7b,EAAM6b,OAAO,CACtBvb,KAAMN,EAAMM,IAAI,CAChBic,cAAevc,EAAMuc,aAAa,eAClCuP,CACF,GACM,CAAEoB,sBAAoB,MAAEpP,CAAI,CAAE,CAAGoiB,EAIvC,OAFA5S,EAAAA,aAAa,CAACoO,IAAI,CAAC5d,GAEZA,EAAKhR,IAAI,CACd,OAAC,YAAE7M,CAAU,CAAEsD,aAAcoN,CAAoB,WAAEiW,CAAS,CAAE,GACtD7mB,EAAcgmB,KAAKC,GAAG,GAExBma,GAAc,EAQlB,GANKD,EAAe9S,YAAY,EAAE,CAEhC8S,EAAe9S,YAAY,CAAGrtB,EAC9BogC,GAAc,GAGZD,EAAe1T,OAAO,CAAE,CAC1B,IAAMzS,EAASja,CAAAA,EAAAA,EAAAA,0BAAAA,EACbC,EACAC,EACAC,EACAC,EACAC,SAMF,CAAe,IAAX4Z,EACKgmB,CADa,CACG//B,EAAO,CAAE,GAAGyQ,CAAM,CAAEqb,eAAe,CAAM,GAG3D/R,CACT,CAGA,GAA0B,UAAtB,OAAO9Z,EACT,OAAO4Q,EAAkB7Q,EAAOG,EAASF,EAAY8Q,GAGvD,IAAMqvB,EAAsBzvB,EACxBjQ,CAAAA,EAAAA,EAAAA,iBAAAA,EAAkBiQ,GAClBlQ,EASJ,GANE,CAAC,EACDT,EAAMuD,YAAY,CAACkG,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,GACjC22B,EAAoB32B,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAUtC,OALAtJ,EAAQic,cAAc,EAAG,EACzBjc,EAAQoD,YAAY,CAAG68B,EACvBjgC,EAAQyb,YAAY,CAAGA,EACvBzb,EAAQqD,YAAY,CAAGC,EACvBtD,EAAQgc,kBAAkB,CAAG,EAAE,CACxBzY,CAAAA,EAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,GAG9B,IAAIE,EAAcL,EAAMM,IAAI,CACxBC,EAAeP,EAAMQ,KAAK,CAC1B2b,EAA0C,EAAE,CAChD,IAAK,IAAMxb,KAAwBV,EAAY,CAC7C,GAAM,CACJoB,cAAe4P,CAAiB,UAChCpQ,CAAQ,MACRkC,CAAI,eACJs9B,CAAa,cACbj/B,CAAY,CACb,CAAGT,EACAK,EAAYL,EAAqBL,IAAI,CAGnCgB,EAAoC,CAAC,MAAO2P,EAAkB,CAGhE1P,EAAUC,CAAAA,EAAAA,EAAAA,2BAAAA,EACZ,EAEAnB,EACAW,EACAP,GAeF,GAVgB,MAAM,CAAlBc,GAToB,CAUtBA,EAAUC,CAAAA,EAAAA,EAAAA,2BAAAA,EACR,EAEA0rB,EACAlsB,EACAP,EAAAA,EAIY,OAAZc,EAAkB,CACpB,EATwB,CAiBtBV,CAPA,EAQAO,GACAwlB,EACA,CACA,IAAMkP,EAAOlE,CAAAA,EAAAA,EAAAA,kBAAAA,EACX7xB,EACAQ,EACAF,EACAW,EACAH,EACAkC,EACAs9B,GACA,EAlBsD,GAsBxD,GAAa,OAATvK,EAAe,CACjB,GAAmB,MAAM,CAArBA,EAAKnD,KAAK,CAGZ,OAAO9hB,EAAkB7Q,EAAOG,EAASM,EAAMsQ,GAOjDxP,EAD8Cu0B,EAAKnD,KAAK,CAC9C2N,IAEJ7+B,EAAWq0B,EAAKlD,IAAI,MACH,EAAnBnxB,IAGFtB,EAAQK,KAAK,CAAGiB,CAAAA,EAElB,IAAMoxB,EAAqBiD,EAAKjD,kBAAkB,CAClD,GAA2B,OAAvBA,EAA6B,CAc/B,IAAM0N,EAAiB9X,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBvoB,EAAK,CAC9CyD,kBAAmBkvB,EACnBhX,QAAS7b,EAAM6b,OAAO,GAGxB8V,GAAAA,EAAAA,uBAAAA,EAAwBmE,EAAMyK,EAKhC,CAIF,MAKEh/B,CALK,CAKKP,CAEd,KAAO,CASL,GAAIkQ,CAAAA,EAAAA,EAAAA,2BAAAA,EAA4B7Q,EAAakB,GAC3C,OADqD,EAC5BvB,EAAOG,EAASM,EAAMsQ,GAGjD,IAAMvQ,EAAmBkB,CAAAA,EAAAA,EAAAA,oBAAAA,IACrBtB,GAAU,EAwDd,IAAK,IAAMogC,KArDTN,EAAezoB,MAAM,GAAKC,EAAAA,wBAAwB,CAACoW,KAAK,EACvDqS,EAAD,EAkBU/uB,CAAAA,EAAAA,EAAAA,IAjBV,WAiBUA,EACRrR,EACAQ,EACAC,EACAG,EACAu/B,IAfF9/B,EA7Vd,SACEqB,CAAmB,CACnBlB,CAAuB,CACvB0Q,CAAoC,CACpCjQ,CAA4B,EAE5B,IAAIy/B,GAAe,EAWnB,IAAK,IAAMnkB,KATX7a,EAASE,GAAG,CAAGpB,EAAaoB,GASD++B,CAR3Bj/B,EAASqB,WAAW,CAAGvC,EAAauC,EAQW,SARA,CAC/CrB,EAASG,OAAO,CAAGrB,EAAaqB,OAAO,CACvCH,EAASX,cAAc,CAAG,IAAImC,IAAI1C,EAAaO,cAAc,EAElC6/B,EAA0B3/B,GAAWwK,GAAG,CACjE,GAAa,IAAIyF,KAAsBrN,EAAQ,GAI/Cw3B,CAAAA,EAAAA,EAAAA,gCAAAA,EAAiC35B,EAAUlB,EAAc+b,GAEzDmkB,GAAe,EAGjB,OAAOA,CACT,EAsUgBjgC,EACAD,EACA0Q,EACAjQ,GAIFk/B,EAAe9S,YAAY,CAAGrtB,GAWXgrB,CAAAA,EAAAA,EAAAA,kBAAAA,EACnB,EAEA1qB,IAKAG,EAAMmB,GAAG,CAAGpB,EAAaoB,GAAG,CAC5BnB,EAAMsC,EARgB,SAQL,CAAGvC,EAAauC,WAAW,CAE5C25B,CAAAA,EAAAA,EAAAA,qCAAAA,EACEj8B,EACAD,EACA0Q,GAGF9Q,EAAQK,KAAK,CAAGA,GACPJ,IACTD,EAAQK,GADU,EACL,CAAGA,EAGhBD,EAAeC,GAGQmgC,EAA0B3/B,IAAY,CAC7D,IAAM4/B,EAAwB,IACzB3vB,KACAuvB,EACJ,EAGsB,CAACI,EAAsBz+B,MAAM,CAAG,EAAE,GACvD6sB,EAAAA,mBAAmB,EACnB,EACmBzP,IAAI,CAACqhB,EAE5B,CACF,CAEAvgC,EAAckB,CAChB,CACF,CAQA,OANApB,EAAQmD,WAAW,CAAGjD,EACtBF,EAAQoD,YAAY,CAAG68B,EACvBjgC,EAAQgc,kBAAkB,CAAGA,EAC7Bhc,EAAQqD,YAAY,CAAGC,EACvBtD,EAAQyb,YAAY,CAAGA,EAEhBlY,CAAAA,EAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,EAC9B,EACA,IAAMH,EAEV,aA7eoC,UACF,WACoB,WACV,WACT,WACS,WAOrC,WACuB,WACE,WACF,WACO,WACD,WACwB,WAIrD,WAC0C,WACN,MAOpC,SAAS6Q,EACd7Q,CAA2B,CAC3BG,CAAgB,CAChBD,CAAW,CACX6Q,CAAoB,EAOpB,OALA5Q,EAAQ6b,aAAa,EAAG,EACxB7b,EAAQoD,YAAY,CAAGrD,EACvBC,EAAQ4Q,WAAW,CAAGA,EACtB5Q,EAAQgc,kBAAkB,CAAGzZ,OAEtBgB,CAAAA,EAAAA,EAAAA,aAAAA,EAAc1D,EAAOG,EAC9B,CAEA,SAASwgC,EACPE,CAAoC,EAEpC,IAAM7C,EAAgC,EAAE,CAClC,CAACp6B,EAAS9C,EAAe,CAAG+/B,EAElC,GAA2C,GAAG,CAA1C5/B,OAAOiB,IAAI,CAACpB,GAAgBqB,MAAM,CACpC,MAAO,CAAC,CAACyB,EAAQ,CAAC,CAGpB,IAAK,GAAM,CAAC0U,EAAkBtU,EAAc,GAAI/C,OAAOgD,OAAO,CAC5DnD,GAEA,IAAK,IAAMggC,KADV,EACoD98B,GAEnC,IAAI,CAAhBJ,EACFo6B,EAASze,GAHwD,CAGpD,CAAC,CAACjH,KAAqBwoB,EAAa,EAEjD9C,EAASze,IAAI,CAAC,CAAC3b,EAAS0U,KAAqBwoB,EAAa,EAKhE,OAAO9C,CACT,GAxCO,qUC5BS1nB,qCAAAA,aANiC,WACrB,OAKrB,SAASA,EAAWpW,CAAW,EAEpC,GAAI,CAAC6W,CAAAA,EAAAA,EAAAA,aAAAA,EAAc7W,GAAM,OAAO,EAChC,GAAI,CAEF,IAAM6gC,EAAiBvnB,CAAAA,EAAAA,EAAAA,iBAAAA,IACjBwnB,EAAW,IAAI9oB,IAAIhY,EAAK6gC,GAC9B,OAAOC,EAAS/oB,MAAM,GAAK8oB,GAAkBviB,CAAAA,EAAAA,EAAAA,WAAAA,EAAYwiB,EAAS3wB,QAAQ,CAC5E,CAAE,MAAOjI,EAAG,CACV,OAAO,CACT,CACF", "sources": ["webpack://terang-lms-ui/../../../../src/client/components/router-reducer/aliased-prefetch-navigations.ts", "webpack://terang-lms-ui/../../src/client/use-merged-ref.ts", "webpack://terang-lms-ui/../../../src/shared/lib/get-img-props.ts", "webpack://terang-lms-ui/../../src/client/image-component.tsx", "webpack://terang-lms-ui/./node_modules/next/dist/server/route-modules/app-page/vendored/contexts/router-context.js", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/add-path-prefix.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts", "webpack://terang-lms-ui/../../../src/shared/lib/server-reference-info.ts", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/defaultAttributes.js", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/create-hugeicon-component.js", "webpack://terang-lms-ui/./node_modules/next/dist/api/image.js", "webpack://terang-lms-ui/../../../src/client/app-dir/link.tsx", "webpack://terang-lms-ui/./node_modules/next/node_modules/@swc/helpers/esm/_class_private_field_loose_base.js", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts", "webpack://terang-lms-ui/../../src/client/assign-location.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/fill-cache-with-new-subtree-data.ts", "webpack://terang-lms-ui/../../../src/shared/lib/utils.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/handle-mutable.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/handle-segment-mismatch.ts", "webpack://terang-lms-ui/../../src/client/remove-base-path.ts", "webpack://terang-lms-ui/../../src/client/add-base-path.ts", "webpack://terang-lms-ui/../../../src/client/components/app-router.tsx", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/parse-path.ts", "webpack://terang-lms-ui/../../src/client/has-base-path.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/is-bot.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/invalidate-cache-by-router-state.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts", "webpack://terang-lms-ui/../../../../../shared/src/utils.ts", "webpack://terang-lms-ui/../../src/defaultAttributes.ts", "webpack://terang-lms-ui/../../src/Icon.ts", "webpack://terang-lms-ui/../../src/createLucideIcon.ts", "webpack://terang-lms-ui/../../../src/client/components/segment-cache.ts", "webpack://terang-lms-ui/../../../src/shared/lib/image-config.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts", "webpack://terang-lms-ui/../../../src/shared/lib/image-blur-svg.ts", "webpack://terang-lms-ui/../../../src/client/components/app-router-announcer.tsx", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/should-hard-navigate.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/prefetch-cache-utils.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/router-reducer.ts", "webpack://terang-lms-ui/../../src/client/normalize-trailing-slash.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts", "webpack://terang-lms-ui/../../../src/client/components/app-router-instance.ts", "webpack://terang-lms-ui/./node_modules/next/node_modules/@swc/helpers/esm/_class_private_field_loose_key.js", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/ppr-navigations.ts", "webpack://terang-lms-ui/../../../src/client/components/links.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/path-has-prefix.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/format-url.ts", "webpack://terang-lms-ui/../../../../src/shared/lib/utils/error-once.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/is-navigating-to-new-root-layout.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/clear-cache-node-data-for-segment-path.ts", "webpack://terang-lms-ui/../../../src/client/components/promise-queue.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/html-bots.ts", "webpack://terang-lms-ui/../../../src/shared/lib/image-loader.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/remove-trailing-slash.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/invalidate-cache-below-flight-segmentpath.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/hmr-refresh-reducer.ts", "webpack://terang-lms-ui/./node_modules/next/dist/server/route-modules/app-page/vendored/contexts/image-config-context.js", "webpack://terang-lms-ui/../../../src/shared/lib/image-external.tsx", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/apply-flight-data.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/prefetch-reducer.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/querystring.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/compute-changed-path.ts", "webpack://terang-lms-ui/../../../../src/client/components/router-reducer/refetch-inactive-parallel-segments.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts", "webpack://terang-lms-ui/../../../../../src/client/components/router-reducer/reducers/navigate-reducer.ts", "webpack://terang-lms-ui/../../../../../src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n} from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport {\n  addSearchParamsIfPageSegment,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\nimport { createEmptyCacheNode } from '../app-router'\nimport { applyRouterStatePatchToTree } from './apply-router-state-patch-to-tree'\nimport { createHrefFromUrl } from './create-href-from-url'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { fillCacheWithNewSubTreeDataButOnlyLoading } from './fill-cache-with-new-subtree-data'\nimport { handleMutable } from './handle-mutable'\nimport type { Mutable, ReadonlyReducerState } from './router-reducer-types'\n\n/**\n * This is a stop-gap until per-segment caching is implemented. It leverages the `aliased` flag that is added\n * to prefetch entries when it's determined that the loading state from that entry should be used for this navigation.\n * This function takes the aliased entry and only applies the loading state to the updated cache node.\n * We should remove this once per-segment fetching is implemented as ideally the prefetch cache will contain a\n * more granular segment map and so the router will be able to simply re-use the loading segment for the new navigation.\n */\nexport function handleAliasedPrefetchEntry(\n  navigatedAt: number,\n  state: ReadonlyReducerState,\n  flightData: string | NormalizedFlightData[],\n  url: URL,\n  mutable: Mutable\n) {\n  let currentTree = state.tree\n  let currentCache = state.cache\n  const href = createHrefFromUrl(url)\n  let applied\n\n  if (typeof flightData === 'string') {\n    return false\n  }\n\n  for (const normalizedFlightData of flightData) {\n    // If the segment doesn't have a loading component, we don't need to do anything.\n    if (!hasLoadingComponentInSeedData(normalizedFlightData.seedData)) {\n      continue\n    }\n\n    let treePatch = normalizedFlightData.tree\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    const { seedData, isRootRender, pathToSegment } = normalizedFlightData\n    // TODO-APP: remove ''\n    const flightSegmentPathWithLeadingEmpty = ['', ...pathToSegment]\n\n    // Segments are keyed by searchParams (e.g. __PAGE__?{\"foo\":\"bar\"}). We might return a less specific, param-less entry,\n    // so we ensure that the final tree contains the correct searchParams (reflected in the URL) are provided in the updated FlightRouterState tree.\n    // We only do this on the first read, as otherwise we'd be overwriting the searchParams that may have already been set\n    treePatch = addSearchParamsToPageSegments(\n      treePatch,\n      Object.fromEntries(url.searchParams)\n    )\n\n    let newTree = applyRouterStatePatchToTree(\n      flightSegmentPathWithLeadingEmpty,\n      currentTree,\n      treePatch,\n      href\n    )\n\n    const newCache = createEmptyCacheNode()\n\n    // The prefetch cache entry was aliased -- this signals that we only fill in the cache with the\n    // loading state and not the actual parallel route seed data.\n    if (isRootRender && seedData) {\n      // Fill in the cache with the new loading / rsc data\n      const rsc = seedData[1]\n      const loading = seedData[3]\n      newCache.loading = loading\n      newCache.rsc = rsc\n\n      // Construct a new tree and apply the aliased loading state for each parallel route\n      fillNewTreeWithOnlyLoadingSegments(\n        navigatedAt,\n        newCache,\n        currentCache,\n        treePatch,\n        seedData\n      )\n    } else {\n      // Copy rsc for the root node of the cache.\n      newCache.rsc = currentCache.rsc\n      newCache.prefetchRsc = currentCache.prefetchRsc\n      newCache.loading = currentCache.loading\n      newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n      // copy the loading state only into the leaf node (the part that changed)\n      fillCacheWithNewSubTreeDataButOnlyLoading(\n        navigatedAt,\n        newCache,\n        currentCache,\n        normalizedFlightData\n      )\n    }\n\n    // If we don't have an updated tree, there's no reason to update the cache, as the tree\n    // dictates what cache nodes to render.\n    if (newTree) {\n      currentTree = newTree\n      currentCache = newCache\n      applied = true\n    }\n  }\n\n  if (!applied) {\n    return false\n  }\n\n  mutable.patchedTree = currentTree\n  mutable.cache = currentCache\n  mutable.canonicalUrl = href\n  mutable.hashFragment = url.hash\n\n  return handleMutable(state, mutable)\n}\n\nfunction hasLoadingComponentInSeedData(seedData: CacheNodeSeedData | null) {\n  if (!seedData) return false\n\n  const parallelRoutes = seedData[2]\n  const loading = seedData[3]\n\n  if (loading) {\n    return true\n  }\n\n  for (const key in parallelRoutes) {\n    if (hasLoadingComponentInSeedData(parallelRoutes[key])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction fillNewTreeWithOnlyLoadingSegments(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null\n) {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    return\n  }\n\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const rsc = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        // copy the layout but null the page segment as that's not meant to be used\n        rsc: segmentForParallelRoute.includes(PAGE_SEGMENT_KEY) ? null : rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillNewTreeWithOnlyLoadingSegments(\n      navigatedAt,\n      newCacheNode,\n      existingCache,\n      parallelRouteState,\n      parallelSeedData\n    )\n  }\n}\n\n/**\n * Add search params to the page segments in the flight router state\n * Page segments that are associated with search params have a page segment key\n * followed by a query string. This function will add those params to the page segment.\n * This is useful if we return an aliased prefetch entry (ie, won't have search params)\n * but the canonical router URL has search params.\n */\nexport function addSearchParamsToPageSegments(\n  flightRouterState: FlightRouterState,\n  searchParams: Record<string, string | string[] | undefined>\n): FlightRouterState {\n  const [segment, parallelRoutes, ...rest] = flightRouterState\n\n  // If it's a page segment, modify the segment by adding search params\n  if (segment.includes(PAGE_SEGMENT_KEY)) {\n    const newSegment = addSearchParamsIfPageSegment(segment, searchParams)\n    return [newSegment, parallelRoutes, ...rest]\n  }\n\n  // Otherwise, recurse through the parallel routes and return a new tree\n  const updatedParallelRoutes: { [key: string]: FlightRouterState } = {}\n\n  for (const [key, parallelRoute] of Object.entries(parallelRoutes)) {\n    updatedParallelRoutes[key] = addSearchParamsToPageSegments(\n      parallelRoute,\n      searchParams\n    )\n  }\n\n  return [segment, updatedParallelRoutes, ...rest]\n}\n", "import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    // See https://github.com/facebook/react/pull/26940\n    ReactDOM.preload(\n      imgAttributes.src,\n      // @ts-expect-error TODO: upgrade to `@types/react-dom@18.3.x`\n      opts\n    )\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].RouterContext;\n\n//# sourceMappingURL=router-context.js.map", "import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n", "import { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ServerPatchAction,\n  ReducerState,\n  ReadonlyReducerState,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyFlightData } from '../apply-flight-data'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\n\nexport function serverPatchReducer(\n  state: ReadonlyReducerState,\n  action: ServerPatchAction\n): ReducerState {\n  const {\n    serverResponse: { flightData, canonicalUrl: canonicalUrlOverride },\n    navigatedAt,\n  } = action\n\n  const mutable: Mutable = {}\n\n  mutable.preserveCustomHistoryState = false\n\n  // Handle case when navigating to page in `pages` from `app`\n  if (typeof flightData === 'string') {\n    return handleExternalUrl(\n      state,\n      mutable,\n      flightData,\n      state.pushRef.pendingPush\n    )\n  }\n\n  let currentTree = state.tree\n  let currentCache = state.cache\n\n  for (const normalizedFlightData of flightData) {\n    const { segmentPath: flightSegmentPath, tree: treePatch } =\n      normalizedFlightData\n\n    const newTree = applyRouterStatePatchToTree(\n      // TODO-APP: remove ''\n      ['', ...flightSegmentPath],\n      currentTree,\n      treePatch,\n      state.canonicalUrl\n    )\n\n    // `applyRouterStatePatchToTree` returns `null` when it determined that the server response is not applicable to the current tree.\n    // In other words, the server responded with a tree that doesn't match what the client is currently rendering.\n    // This can happen if the server patch action took longer to resolve than a subsequent navigation which would have changed the tree.\n    // Previously this case triggered an MPA navigation but it should be safe to simply discard the server response rather than forcing\n    // the entire page to reload.\n    if (newTree === null) {\n      return state\n    }\n\n    if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n      return handleExternalUrl(\n        state,\n        mutable,\n        state.canonicalUrl,\n        state.pushRef.pendingPush\n      )\n    }\n\n    const canonicalUrlOverrideHref = canonicalUrlOverride\n      ? createHrefFromUrl(canonicalUrlOverride)\n      : undefined\n\n    if (canonicalUrlOverrideHref) {\n      mutable.canonicalUrl = canonicalUrlOverrideHref\n    }\n\n    const cache: CacheNode = createEmptyCacheNode()\n    applyFlightData(navigatedAt, currentCache, cache, normalizedFlightData)\n\n    mutable.patchedTree = newTree\n    mutable.cache = cache\n\n    currentCache = cache\n    currentTree = newTree\n  }\n\n  return handleMutable(state, mutable)\n}\n", "export interface ServerReferenceInfo {\n  type: 'server-action' | 'use-cache'\n  usedArgs: [boolean, boolean, boolean, boolean, boolean, boolean]\n  hasRestArgs: boolean\n}\n\n/**\n * Extracts info about the server reference for the given server reference ID by\n * parsing the first byte of the hex-encoded ID.\n *\n * ```\n * Bit positions: [7]      [6] [5] [4] [3] [2] [1]  [0]\n * Bits:          typeBit  argMask                  restArgs\n * ```\n *\n * If the `typeBit` is `1` the server reference represents a `\"use cache\"`\n * function, otherwise a server action.\n *\n * The `argMask` encodes whether the function uses the argument at the\n * respective position.\n *\n * The `restArgs` bit indicates whether the function uses a rest parameter. It's\n * also set to 1 if the function has more than 6 args.\n *\n * @param id hex-encoded server reference ID\n */\nexport function extractInfoFromServerReferenceId(\n  id: string\n): ServerReferenceInfo {\n  const infoByte = parseInt(id.slice(0, 2), 16)\n  const typeBit = (infoByte >> 7) & 0x1\n  const argMask = (infoByte >> 1) & 0x3f\n  const restArgs = infoByte & 0x1\n  const usedArgs = Array(6)\n\n  for (let index = 0; index < 6; index++) {\n    const bitPosition = 5 - index\n    const bit = (argMask >> bitPosition) & 0x1\n    usedArgs[index] = bit === 1\n  }\n\n  return {\n    type: typeBit === 1 ? 'use-cache' : 'server-action',\n    usedArgs: usedArgs as [\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n      boolean,\n    ],\n    hasRestArgs: restArgs === 1,\n  }\n}\n\n/**\n * Creates a sparse array containing only the used arguments based on the\n * provided action info.\n */\nexport function omitUnusedArgs(\n  args: unknown[],\n  info: ServerReferenceInfo\n): unknown[] {\n  const filteredArgs = new Array(args.length)\n\n  for (let index = 0; index < args.length; index++) {\n    if (\n      (index < 6 && info.usedArgs[index]) ||\n      // This assumes that the server reference info byte has the restArgs bit\n      // set to 1 if there are more than 6 args.\n      (index >= 6 && info.hasRestArgs)\n    ) {\n      filteredArgs[index] = args[index]\n    }\n  }\n\n  return filteredArgs\n}\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nvar e={xmlns:\"http://www.w3.org/2000/svg\",width:24,height:24,viewBox:\"0 0 24 24\",fill:\"none\",strokeWidth:1.5,strokeLinecap:\"round\",strokeLinejoin:\"round\"};export{e as default};\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport{forwardRef as p,createElement as s}from\"react\";import u from\"./defaultAttributes.js\";const y=(a,c)=>{const t=p(({color:i=\"currentColor\",size:e=24,strokeWidth:l=1.5,className:m=\"\",children:r,...n},d)=>{const f={ref:d,...u,width:e,height:e,strokeWidth:l,color:i,className:m,...n};return s(\"svg\",f,c?.map(([h,o])=>s(h,{key:o.id,...o}))??[],...Array.isArray(r)?r:[r])});return t.displayName=`${a}Icon`,t};export{y as default};\n", "export { default } from '../shared/lib/image-external';\nexport * from '../shared/lib/image-external';\n\n//# sourceMappingURL=image.js.map", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n", "function _class_private_field_loose_base(receiver, privateKey) {\n    if (!Object.prototype.hasOwnProperty.call(receiver, privateKey)) {\n        throw new TypeError(\"attempted to use private field on non-instance\");\n    }\n\n    return receiver;\n}\nexport { _class_private_field_loose_base as _ };\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  CacheNodeSeedData,\n} from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport {\n  PrefetchCacheEntryStatus,\n  type PrefetchCacheEntry,\n} from './router-reducer-types'\n\nexport function fillLazyItemsTillLeafWithHead(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode | undefined,\n  routerState: FlightRouterState,\n  cacheNodeSeedData: CacheNodeSeedData | null,\n  head: React.ReactNode,\n  prefetchEntry: PrefetchCacheEntry | undefined\n): void {\n  const isLastSegment = Object.keys(routerState[1]).length === 0\n  if (isLastSegment) {\n    newCache.head = head\n    return\n  }\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const parallelRouteState = routerState[1][key]\n    const segmentForParallelRoute = parallelRouteState[0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n\n    // TODO: We should traverse the cacheNodeSeedData tree instead of the router\n    // state tree. Ideally, they would always be the same shape, but because of\n    // the loading.js pattern, cacheNodeSeedData sometimes only represents a\n    // partial tree. That's why this node is sometimes null. Once PPR lands,\n    // loading.js will no longer have special behavior and we can traverse the\n    // data tree instead.\n    //\n    // We should also consider merging the router state tree and the data tree\n    // in the response format, so that we don't have to send the keys twice.\n    // Then the client can convert them into separate representations.\n    const parallelSeedData =\n      cacheNodeSeedData !== null && cacheNodeSeedData[2][key] !== undefined\n        ? cacheNodeSeedData[2][key]\n        : null\n    if (existingCache) {\n      const existingParallelRoutesCacheNode =\n        existingCache.parallelRoutes.get(key)\n      if (existingParallelRoutesCacheNode) {\n        const hasReusablePrefetch =\n          prefetchEntry?.kind === 'auto' &&\n          prefetchEntry.status === PrefetchCacheEntryStatus.reusable\n\n        let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n        const existingCacheNode = parallelRouteCacheNode.get(cacheKey)\n        let newCacheNode: CacheNode\n        if (parallelSeedData !== null) {\n          // New data was sent from the server.\n          const seedNode = parallelSeedData[1]\n          const loading = parallelSeedData[3]\n          newCacheNode = {\n            lazyData: null,\n            rsc: seedNode,\n            // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n            // this path during a navigation, but until PPR is fully implemented\n            // yet it's possible the existing node does have a non-null\n            // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n            // old behavior — no PPR value.\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            loading,\n            parallelRoutes: new Map(existingCacheNode?.parallelRoutes),\n            navigatedAt,\n          }\n        } else if (hasReusablePrefetch && existingCacheNode) {\n          // No new data was sent from the server, but the existing cache node\n          // was prefetched, so we should reuse that.\n          newCacheNode = {\n            lazyData: existingCacheNode.lazyData,\n            rsc: existingCacheNode.rsc,\n            // This is a PPR-only field. Unlike the previous branch, since we're\n            // just cloning the existing cache node, we might as well keep the\n            // PPR value, if it exists.\n            prefetchRsc: existingCacheNode.prefetchRsc,\n            head: existingCacheNode.head,\n            prefetchHead: existingCacheNode.prefetchHead,\n            parallelRoutes: new Map(existingCacheNode.parallelRoutes),\n            loading: existingCacheNode.loading,\n          } as CacheNode\n        } else {\n          // No data available for this node. This will trigger a lazy fetch\n          // during render.\n          newCacheNode = {\n            lazyData: null,\n            rsc: null,\n            prefetchRsc: null,\n            head: null,\n            prefetchHead: null,\n            parallelRoutes: new Map(existingCacheNode?.parallelRoutes),\n            loading: null,\n            navigatedAt,\n          }\n        }\n\n        // Overrides the cache key with the new cache node.\n        parallelRouteCacheNode.set(cacheKey, newCacheNode)\n        // Traverse deeper to apply the head / fill lazy items till the head.\n        fillLazyItemsTillLeafWithHead(\n          navigatedAt,\n          newCacheNode,\n          existingCacheNode,\n          parallelRouteState,\n          parallelSeedData ? parallelSeedData : null,\n          head,\n          prefetchEntry\n        )\n\n        newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n        continue\n      }\n    }\n\n    let newCacheNode: CacheNode\n    if (parallelSeedData !== null) {\n      // New data was sent from the server.\n      const seedNode = parallelSeedData[1]\n      const loading = parallelSeedData[3]\n      newCacheNode = {\n        lazyData: null,\n        rsc: seedNode,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading,\n        navigatedAt,\n      }\n    } else {\n      // No data available for this node. This will trigger a lazy fetch\n      // during render.\n      newCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt,\n      }\n    }\n\n    const existingParallelRoutes = newCache.parallelRoutes.get(key)\n    if (existingParallelRoutes) {\n      existingParallelRoutes.set(cacheKey, newCacheNode)\n    } else {\n      newCache.parallelRoutes.set(key, new Map([[cacheKey, newCacheNode]]))\n    }\n\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      newCacheNode,\n      undefined,\n      parallelRouteState,\n      parallelSeedData,\n      head,\n      prefetchEntry\n    )\n  }\n}\n", "import { addBasePath } from './add-base-path'\n\n/**\n * Function to correctly assign location to URL\n *\n * The method will add basePath, and will also correctly add location (including if it is a relative path)\n * @param location Location that should be added to the url\n * @param url Base URL to which the location should be assigned\n */\nexport function assignLocation(location: string, url: URL): URL {\n  if (location.startsWith('.')) {\n    const urlBase = url.origin + url.pathname\n    return new URL(\n      // In order for a relative path to be added to the current url correctly, the current url must end with a slash\n      // new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n      // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n      (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location\n    )\n  }\n\n  return new URL(addBasePath(location), url.href)\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { Segment } from '../../../server/app-render/types'\nimport { invalidateCacheByRouterState } from './invalidate-cache-by-router-state'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\n/**\n * Common logic for filling cache with new sub tree data.\n */\nfunction fillCacheHelper(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry: PrefetchCacheEntry | undefined,\n  fillLazyItems: boolean\n): void {\n  const {\n    segmentPath,\n    seedData: cacheNodeSeedData,\n    tree: treePatch,\n    head,\n  } = flightData\n  let newCacheNode = newCache\n  let existingCacheNode = existingCache\n\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n\n    // segmentPath is a repeating tuple of parallelRouteKey and segment\n    // we know we've hit the last entry we've reached our final pair\n    const isLastEntry = i === segmentPath.length - 2\n    const cacheKey = createRouterCacheKey(segment)\n\n    const existingChildSegmentMap =\n      existingCacheNode.parallelRoutes.get(parallelRouteKey)\n\n    if (!existingChildSegmentMap) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    let childSegmentMap = newCacheNode.parallelRoutes.get(parallelRouteKey)\n    if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n      childSegmentMap = new Map(existingChildSegmentMap)\n      newCacheNode.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n    }\n\n    const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n    let childCacheNode = childSegmentMap.get(cacheKey)\n\n    if (isLastEntry) {\n      if (\n        cacheNodeSeedData &&\n        (!childCacheNode ||\n          !childCacheNode.lazyData ||\n          childCacheNode === existingChildCacheNode)\n      ) {\n        const incomingSegment = cacheNodeSeedData[0]\n        const rsc = cacheNodeSeedData[1]\n        const loading = cacheNodeSeedData[3]\n\n        childCacheNode = {\n          lazyData: null,\n          // When `fillLazyItems` is false, we only want to fill the RSC data for the layout,\n          // not the page segment.\n          rsc:\n            fillLazyItems || incomingSegment !== PAGE_SEGMENT_KEY ? rsc : null,\n          prefetchRsc: null,\n          head: null,\n          prefetchHead: null,\n          loading,\n          parallelRoutes:\n            fillLazyItems && existingChildCacheNode\n              ? new Map(existingChildCacheNode.parallelRoutes)\n              : new Map(),\n          navigatedAt,\n        }\n\n        if (existingChildCacheNode && fillLazyItems) {\n          invalidateCacheByRouterState(\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch\n          )\n        }\n        if (fillLazyItems) {\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            childCacheNode,\n            existingChildCacheNode,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            prefetchEntry\n          )\n        }\n\n        childSegmentMap.set(cacheKey, childCacheNode)\n      }\n      continue\n    }\n\n    if (!childCacheNode || !existingChildCacheNode) {\n      // Bailout because the existing cache does not have the path to the leaf node\n      // Will trigger lazy fetch in layout-router because of missing segment\n      continue\n    }\n\n    if (childCacheNode === existingChildCacheNode) {\n      childCacheNode = {\n        lazyData: childCacheNode.lazyData,\n        rsc: childCacheNode.rsc,\n        prefetchRsc: childCacheNode.prefetchRsc,\n        head: childCacheNode.head,\n        prefetchHead: childCacheNode.prefetchHead,\n        parallelRoutes: new Map(childCacheNode.parallelRoutes),\n        loading: childCacheNode.loading,\n      } as CacheNode\n      childSegmentMap.set(cacheKey, childCacheNode)\n    }\n\n    // Move deeper into the cache nodes\n    newCacheNode = childCacheNode\n    existingCacheNode = existingChildCacheNode\n  }\n}\n\n/**\n * Fill cache with rsc based on flightDataPath\n */\nexport function fillCacheWithNewSubTreeData(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    true\n  )\n}\n\nexport function fillCacheWithNewSubTreeDataButOnlyLoading(\n  navigatedAt: number,\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): void {\n  fillCacheHelper(\n    navigatedAt,\n    newCache,\n    existingCache,\n    flightData,\n    prefetchEntry,\n    false\n  )\n}\n", "import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n", "import { computeChangedPath } from './compute-changed-path'\nimport type {\n  Mu<PERSON>,\n  ReadonlyReducerState,\n  ReducerState,\n} from './router-reducer-types'\n\nfunction isNotUndefined<T>(value: T): value is Exclude<T, undefined> {\n  return typeof value !== 'undefined'\n}\n\nexport function handleMutable(\n  state: ReadonlyReducerState,\n  mutable: Mutable\n): ReducerState {\n  // shouldScroll is true by default, can override to false.\n  const shouldScroll = mutable.shouldScroll ?? true\n\n  let nextUrl = state.nextUrl\n\n  if (isNotUndefined(mutable.patchedTree)) {\n    // If we received a patched tree, we need to compute the changed path.\n    const changedPath = computeChangedPath(state.tree, mutable.patchedTree)\n    if (changedPath) {\n      // If the tree changed, we need to update the nextUrl\n      nextUrl = changedPath\n    } else if (!nextUrl) {\n      // if the tree ends up being the same (ie, no changed path), and we don't have a nextUrl, then we should use the canonicalUrl\n      nextUrl = state.canonicalUrl\n    }\n    // otherwise this will be a no-op and continue to use the existing nextUrl\n  }\n\n  return {\n    // Set href.\n    canonicalUrl: isNotUndefined(mutable.canonicalUrl)\n      ? mutable.canonicalUrl === state.canonicalUrl\n        ? state.canonicalUrl\n        : mutable.canonicalUrl\n      : state.canonicalUrl,\n    pushRef: {\n      pendingPush: isNotUndefined(mutable.pendingPush)\n        ? mutable.pendingPush\n        : state.pushRef.pendingPush,\n      mpaNavigation: isNotUndefined(mutable.mpaNavigation)\n        ? mutable.mpaNavigation\n        : state.pushRef.mpaNavigation,\n      preserveCustomHistoryState: isNotUndefined(\n        mutable.preserveCustomHistoryState\n      )\n        ? mutable.preserveCustomHistoryState\n        : state.pushRef.preserveCustomHistoryState,\n    },\n    // All navigation requires scroll and focus management to trigger.\n    focusAndScrollRef: {\n      apply: shouldScroll\n        ? isNotUndefined(mutable?.scrollableSegments)\n          ? true\n          : state.focusAndScrollRef.apply\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          false,\n      onlyHashChange: mutable.onlyHashChange || false,\n      hashFragment: shouldScroll\n        ? // Empty hash should trigger default behavior of scrolling layout into view.\n          // #top is handled in layout-router.\n          mutable.hashFragment && mutable.hashFragment !== ''\n          ? // Remove leading # and decode hash to make non-latin hashes work.\n            decodeURIComponent(mutable.hashFragment.slice(1))\n          : state.focusAndScrollRef.hashFragment\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          null,\n      segmentPaths: shouldScroll\n        ? mutable?.scrollableSegments ?? state.focusAndScrollRef.segmentPaths\n        : // If shouldScroll is false then we should not apply scroll and focus management.\n          [],\n    },\n    // Apply cache.\n    cache: mutable.cache ? mutable.cache : state.cache,\n    prefetchCache: mutable.prefetchCache\n      ? mutable.prefetchCache\n      : state.prefetchCache,\n    // Apply patched router state.\n    tree: isNotUndefined(mutable.patchedTree)\n      ? mutable.patchedTree\n      : state.tree,\n    nextUrl,\n  }\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\nimport { handleExternalUrl } from './reducers/navigate-reducer'\nimport type {\n  ReadonlyReducerState,\n  ReducerActions,\n} from './router-reducer-types'\n\n/**\n * Handles the case where the client router attempted to patch the tree but, due to a mismatch, the patch failed.\n * This will perform an MPA navigation to return the router to a valid state.\n */\nexport function handleSegmentMismatch(\n  state: ReadonlyReducerState,\n  action: ReducerActions,\n  treePatch: FlightRouterState\n) {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      'Performing hard navigation because your application experienced an unrecoverable error. If this keeps occurring, please file a Next.js issue.\\n\\n' +\n        'Reason: Segment mismatch\\n' +\n        `Last Action: ${action.type}\\n\\n` +\n        `Current Tree: ${JSON.stringify(state.tree)}\\n\\n` +\n        `Tree Patch Payload: ${JSON.stringify(treePatch)}`\n    )\n  }\n\n  return handleExternalUrl(state, {}, state.canonicalUrl, true)\n}\n", "import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n", "import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n", "'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type { CacheNode } from '../../shared/lib/app-router-context.shared-runtime'\nimport { ACTION_RESTORE } from './router-reducer/router-reducer-types'\nimport type { AppRouterState } from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { dispatchAppRouterAction, useActionQueue } from './use-action-queue'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport {\n  dispatchTraverseAction,\n  publicAppRouterInstance,\n  type AppRouterActionQueue,\n} from './app-router-instance'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nexport function isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n    navigatedAt: -1,\n  }\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const state = useActionQueue(actionQueue)\n  const { canonicalUrl } = state\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = state\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: publicAppRouterInstance,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatchAppRouterAction({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        // TODO: This should access the router methods directly, rather than\n        // go through the public interface.\n        if (redirectType === RedirectType.push) {\n          publicAppRouterInstance.push(url, {})\n        } else {\n          publicAppRouterInstance.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = state\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatchTraverseAction(\n          window.location.href,\n          event.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n        )\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = state\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={state} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              {/* TODO: We should be able to remove this context. useRouter\n                  should import from app-router-instance instead. It's only\n                  necessary because useRouter is shared between Pages and\n                  App Router. We should fork that module, then remove this\n                  context provider. */}\n              <AppRouterContext.Provider value={publicAppRouterInstance}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n", "/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n", "import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n", "import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS\nconst HEADLESS_BROWSER_BOT_UA_RE =\n  /Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightRouterState } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * Invalidate cache one level down from the router state.\n */\nexport function invalidateCacheByRouterState(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState\n): void {\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const segmentForParallelRoute = routerState[1][key][0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n    const existingParallelRoutesCacheNode =\n      existingCache.parallelRoutes.get(key)\n    if (existingParallelRoutesCacheNode) {\n      let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n      parallelRouteCacheNode.delete(cacheKey)\n      newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n    }\n  }\n}\n", "import type {\n  ActionFlightResponse,\n  ActionResult,\n} from '../../../../server/app-render/types'\nimport { callServer } from '../../../app-call-server'\nimport { findSourceMapURL } from '../../../app-find-source-map-url'\nimport {\n  ACTION_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  RSC_CONTENT_TYPE_HEADER,\n} from '../../app-router-headers'\n\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromFetch } from 'react-server-dom-webpack/client'\n// // eslint-disable-next-line import/no-extraneous-dependencies\n// import { encodeReply } from 'react-server-dom-webpack/client'\nconst { createFromFetch, createTemporaryReferenceSet, encodeReply } = (\n  !!process.env.NEXT_RUNTIME\n    ? // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge')\n    : // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client')\n) as typeof import('react-server-dom-webpack/client')\n\nimport {\n  PrefetchKind,\n  type ReadonlyReducerState,\n  type ReducerState,\n  type ServerActionAction,\n  type ServerActionMutable,\n} from '../router-reducer-types'\nimport { assignLocation } from '../../../assign-location'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { handleMutable } from '../handle-mutable'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport {\n  normalizeFlightData,\n  type NormalizedFlightData,\n} from '../../../flight-data-helpers'\nimport { getRedirectError } from '../../redirect'\nimport { RedirectType } from '../../redirect-error'\nimport { createSeededPrefetchCacheEntry } from '../prefetch-cache-utils'\nimport { removeBasePath } from '../../../remove-base-path'\nimport { hasBasePath } from '../../../has-base-path'\nimport {\n  extractInfoFromServerReferenceId,\n  omitUnusedArgs,\n} from '../../../../shared/lib/server-reference-info'\nimport { revalidateEntireCache } from '../../segment-cache'\n\ntype FetchServerActionResult = {\n  redirectLocation: URL | undefined\n  redirectType: RedirectType | undefined\n  actionResult?: ActionResult\n  actionFlightData?: NormalizedFlightData[] | string\n  isPrerender: boolean\n  revalidatedParts: {\n    tag: boolean\n    cookie: boolean\n    paths: string[]\n  }\n}\n\nasync function fetchServerAction(\n  state: ReadonlyReducerState,\n  nextUrl: ReadonlyReducerState['nextUrl'],\n  { actionId, actionArgs }: ServerActionAction\n): Promise<FetchServerActionResult> {\n  const temporaryReferences = createTemporaryReferenceSet()\n  const info = extractInfoFromServerReferenceId(actionId)\n\n  // TODO: Currently, we're only omitting unused args for the experimental \"use\n  // cache\" functions. Once the server reference info byte feature is stable, we\n  // should apply this to server actions as well.\n  const usedArgs =\n    info.type === 'use-cache' ? omitUnusedArgs(actionArgs, info) : actionArgs\n\n  const body = await encodeReply(usedArgs, { temporaryReferences })\n\n  const res = await fetch('', {\n    method: 'POST',\n    headers: {\n      Accept: RSC_CONTENT_TYPE_HEADER,\n      [ACTION_HEADER]: actionId,\n      [NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(\n        JSON.stringify(state.tree)\n      ),\n      ...(process.env.NEXT_DEPLOYMENT_ID\n        ? {\n            'x-deployment-id': process.env.NEXT_DEPLOYMENT_ID,\n          }\n        : {}),\n      ...(nextUrl\n        ? {\n            [NEXT_URL]: nextUrl,\n          }\n        : {}),\n    },\n    body,\n  })\n\n  const redirectHeader = res.headers.get('x-action-redirect')\n  const [location, _redirectType] = redirectHeader?.split(';') || []\n  let redirectType: RedirectType | undefined\n  switch (_redirectType) {\n    case 'push':\n      redirectType = RedirectType.push\n      break\n    case 'replace':\n      redirectType = RedirectType.replace\n      break\n    default:\n      redirectType = undefined\n  }\n\n  const isPrerender = !!res.headers.get(NEXT_IS_PRERENDER_HEADER)\n  let revalidatedParts: FetchServerActionResult['revalidatedParts']\n  try {\n    const revalidatedHeader = JSON.parse(\n      res.headers.get('x-action-revalidated') || '[[],0,0]'\n    )\n    revalidatedParts = {\n      paths: revalidatedHeader[0] || [],\n      tag: !!revalidatedHeader[1],\n      cookie: revalidatedHeader[2],\n    }\n  } catch (e) {\n    revalidatedParts = {\n      paths: [],\n      tag: false,\n      cookie: false,\n    }\n  }\n\n  const redirectLocation = location\n    ? assignLocation(\n        location,\n        new URL(state.canonicalUrl, window.location.href)\n      )\n    : undefined\n\n  const contentType = res.headers.get('content-type')\n\n  if (contentType?.startsWith(RSC_CONTENT_TYPE_HEADER)) {\n    const response: ActionFlightResponse = await createFromFetch(\n      Promise.resolve(res),\n      { callServer, findSourceMapURL, temporaryReferences }\n    )\n\n    if (location) {\n      // if it was a redirection, then result is just a regular RSC payload\n      return {\n        actionFlightData: normalizeFlightData(response.f),\n        redirectLocation,\n        redirectType,\n        revalidatedParts,\n        isPrerender,\n      }\n    }\n\n    return {\n      actionResult: response.a,\n      actionFlightData: normalizeFlightData(response.f),\n      redirectLocation,\n      redirectType,\n      revalidatedParts,\n      isPrerender,\n    }\n  }\n\n  // Handle invalid server action responses\n  if (res.status >= 400) {\n    // The server can respond with a text/plain error message, but we'll fallback to something generic\n    // if there isn't one.\n    const error =\n      contentType === 'text/plain'\n        ? await res.text()\n        : 'An unexpected response was received from the server.'\n\n    throw new Error(error)\n  }\n\n  return {\n    redirectLocation,\n    redirectType,\n    revalidatedParts,\n    isPrerender,\n  }\n}\n\n/*\n * This reducer is responsible for calling the server action and processing any side-effects from the server action.\n * It does not mutate the state by itself but rather delegates to other reducers to do the actual mutation.\n */\nexport function serverActionReducer(\n  state: ReadonlyReducerState,\n  action: ServerActionAction\n): ReducerState {\n  const { resolve, reject } = action\n  const mutable: ServerActionMutable = {}\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  // only pass along the `nextUrl` param (used for interception routes) if the current route was intercepted.\n  // If the route has been intercepted, the action should be as well.\n  // Otherwise the server action might be intercepted with the wrong action id\n  // (ie, one that corresponds with the intercepted route)\n  const nextUrl =\n    state.nextUrl && hasInterceptionRouteInCurrentTree(state.tree)\n      ? state.nextUrl\n      : null\n\n  const navigatedAt = Date.now()\n\n  return fetchServerAction(state, nextUrl, action).then(\n    async ({\n      actionResult,\n      actionFlightData: flightData,\n      redirectLocation,\n      redirectType,\n      isPrerender,\n      revalidatedParts,\n    }) => {\n      let redirectHref: string | undefined\n\n      // honor the redirect type instead of defaulting to push in case of server actions.\n      if (redirectLocation) {\n        if (redirectType === RedirectType.replace) {\n          state.pushRef.pendingPush = false\n          mutable.pendingPush = false\n        } else {\n          state.pushRef.pendingPush = true\n          mutable.pendingPush = true\n        }\n\n        redirectHref = createHrefFromUrl(redirectLocation, false)\n        mutable.canonicalUrl = redirectHref\n      }\n\n      if (!flightData) {\n        resolve(actionResult)\n\n        // If there is a redirect but no flight data we need to do a mpaNavigation.\n        if (redirectLocation) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectLocation.href,\n            state.pushRef.pendingPush\n          )\n        }\n        return state\n      }\n\n      if (typeof flightData === 'string') {\n        // Handle case when navigating to page in `pages` from `app`\n        resolve(actionResult)\n\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      const actionRevalidated =\n        revalidatedParts.paths.length > 0 ||\n        revalidatedParts.tag ||\n        revalidatedParts.cookie\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('SERVER ACTION APPLY FAILED')\n          resolve(actionResult)\n\n          return state\n        }\n\n        // Given the path can only have two items the items are only the router state and rsc for the root.\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          redirectHref ? redirectHref : state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          resolve(actionResult)\n\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          resolve(actionResult)\n\n          return handleExternalUrl(\n            state,\n            mutable,\n            redirectHref || state.canonicalUrl,\n            state.pushRef.pendingPush\n          )\n        }\n\n        // The server sent back RSC data for the server action, so we need to apply it to the cache.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const cache: CacheNode = createEmptyCacheNode()\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = cacheNodeSeedData[3]\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as server actions have to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n\n          mutable.cache = cache\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n          if (actionRevalidated) {\n            await refreshInactiveParallelSegments({\n              navigatedAt,\n              state,\n              updatedTree: newTree,\n              updatedCache: cache,\n              includeNextUrl: Boolean(nextUrl),\n              canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n            })\n          }\n        }\n\n        mutable.patchedTree = newTree\n        currentTree = newTree\n      }\n\n      if (redirectLocation && redirectHref) {\n        if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE && !actionRevalidated) {\n          // Because the RedirectBoundary will trigger a navigation, we need to seed the prefetch cache\n          // with the FlightData that we got from the server action for the target page, so that it's\n          // available when the page is navigated to and doesn't need to be re-fetched.\n          // We only do this if the server action didn't revalidate any data, as in that case the\n          // client cache will be cleared and the data will be re-fetched anyway.\n          // NOTE: We don't do this in the Segment Cache implementation.\n          // Dynamic data should never be placed into the cache, unless it's\n          // \"converted\" to static data using <Link prefetch={true}>. What we\n          // do instead is re-prefetch links and forms whenever the cache is\n          // invalidated.\n          createSeededPrefetchCacheEntry({\n            url: redirectLocation,\n            data: {\n              flightData,\n              canonicalUrl: undefined,\n              couldBeIntercepted: false,\n              prerendered: false,\n              postponed: false,\n              // TODO: We should be able to set this if the server action\n              // returned a fully static response.\n              staleTime: -1,\n            },\n            tree: state.tree,\n            prefetchCache: state.prefetchCache,\n            nextUrl: state.nextUrl,\n            kind: isPrerender ? PrefetchKind.FULL : PrefetchKind.AUTO,\n          })\n          mutable.prefetchCache = state.prefetchCache\n        }\n\n        // If the action triggered a redirect, the action promise will be rejected with\n        // a redirect so that it's handled by RedirectBoundary as we won't have a valid\n        // action result to resolve the promise with. This will effectively reset the state of\n        // the component that called the action as the error boundary will remount the tree.\n        // The status code doesn't matter here as the action handler will have already sent\n        // a response with the correct status code.\n        reject(\n          getRedirectError(\n            hasBasePath(redirectHref)\n              ? removeBasePath(redirectHref)\n              : redirectHref,\n            redirectType || RedirectType.push\n          )\n        )\n      } else {\n        resolve(actionResult)\n      }\n\n      return handleMutable(state, mutable)\n    },\n    (e: any) => {\n      // When the server action is rejected we don't update the state and instead call the reject handler of the promise.\n      reject(e)\n\n      return state\n    }\n  )\n}\n", "import type { FlightRouterState } from '../../../../server/app-render/types'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createRouterCacheKey } from '../create-router-cache-key'\n\nexport function findHeadInCache(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1]\n): [CacheNode, string] | null {\n  return findHeadInCacheImpl(cache, parallelRoutes, '')\n}\n\nfunction findHeadInCacheImpl(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1],\n  keyPrefix: string\n): [CacheNode, string] | null {\n  const isLastItem = Object.keys(parallelRoutes).length === 0\n  if (isLastItem) {\n    // Returns the entire Cache Node of the segment whose head we will render.\n    return [cache, keyPrefix]\n  }\n\n  // First try the 'children' parallel route if it exists\n  // when starting from the \"root\", this corresponds with the main page component\n  if (parallelRoutes.children) {\n    const [segment, childParallelRoutes] = parallelRoutes.children\n    const childSegmentMap = cache.parallelRoutes.get('children')\n    if (childSegmentMap) {\n      const cacheKey = createRouterCacheKey(segment)\n      const cacheNode = childSegmentMap.get(cacheKey)\n      if (cacheNode) {\n        const item = findHeadInCacheImpl(\n          cacheNode,\n          childParallelRoutes,\n          keyPrefix + '/' + cacheKey\n        )\n        if (item) return item\n      }\n    }\n  }\n\n  // if we didn't find metadata in the page slot, check the other parallel routes\n  for (const key in parallelRoutes) {\n    if (key === 'children') continue // already checked above\n\n    const [segment, childParallelRoutes] = parallelRoutes[key]\n    const childSegmentMap = cache.parallelRoutes.get(key)\n    if (!childSegmentMap) {\n      continue\n    }\n\n    const cacheKey = createRouterCacheKey(segment)\n\n    const cacheNode = childSegmentMap.get(cacheKey)\n    if (!cacheNode) {\n      continue\n    }\n\n    const item = findHeadInCacheImpl(\n      cacheNode,\n      childParallelRoutes,\n      keyPrefix + '/' + cacheKey\n    )\n    if (item) {\n      return item\n    }\n  }\n\n  return null\n}\n", "import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n", "export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n", "import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) => {\n    return createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    );\n  },\n);\n\nexport default Icon;\n", "import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(`lucide-${toKebabCase(iconName)}`, className),\n      ...props,\n    }),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n", "/**\n * Entry point to the Segment Cache implementation.\n *\n * All code related to the Segment Cache lives `segment-cache-impl` directory.\n * Callers access it through this indirection.\n *\n * This is to ensure the code is dead code eliminated from the bundle if the\n * flag is disabled.\n *\n * TODO: This is super tedious. Since experimental flags are an essential part\n * of our workflow, we should establish a better pattern for dead code\n * elimination. Ideally it would be done at the bundler level, like how React's\n * build process works. In the React repo, you don't even need to add any extra\n * configuration per experiment — if the code is not reachable, it gets stripped\n * from the build automatically by Rollup. Or, shorter term, we could stub out\n * experimental modules at build time by updating the build config, i.e. a more\n * automated version of what I'm doing manually in this file.\n */\n\nexport type { NavigationResult } from './segment-cache-impl/navigation'\nexport type { PrefetchTask } from './segment-cache-impl/scheduler'\n\nconst notEnabled: any = () => {\n  throw new Error(\n    'Segment Cache experiment is not enabled. This is a bug in Next.js.'\n  )\n}\n\nexport const prefetch: typeof import('./segment-cache-impl/prefetch').prefetch =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/prefetch').prefetch(...args)\n      }\n    : notEnabled\n\nexport const navigate: typeof import('./segment-cache-impl/navigation').navigate =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/navigation').navigate(...args)\n      }\n    : notEnabled\n\nexport const revalidateEntireCache: typeof import('./segment-cache-impl/cache').revalidateEntireCache =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').revalidateEntireCache(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const getCurrentCacheVersion: typeof import('./segment-cache-impl/cache').getCurrentCacheVersion =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache').getCurrentCacheVersion(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const schedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').schedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').schedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const cancelPrefetchTask: typeof import('./segment-cache-impl/scheduler').cancelPrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').cancelPrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const reschedulePrefetchTask: typeof import('./segment-cache-impl/scheduler').reschedulePrefetchTask =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/scheduler').reschedulePrefetchTask(\n          ...args\n        )\n      }\n    : notEnabled\n\nexport const createCacheKey: typeof import('./segment-cache-impl/cache-key').createCacheKey =\n  process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? function (...args) {\n        return require('./segment-cache-impl/cache-key').createCacheKey(...args)\n      }\n    : notEnabled\n\n/**\n * Below are public constants. They're small enough that we don't need to\n * DCE them.\n */\n\nexport const enum NavigationResultTag {\n  MPA,\n  Success,\n  NoOp,\n  Async,\n}\n\n/**\n * The priority of the prefetch task. Higher numbers are higher priority.\n */\nexport const enum PrefetchPriority {\n  /**\n   * Assigned to any visible link that was hovered/touched at some point. This\n   * is not removed on mouse exit, because a link that was momentarily\n   * hovered is more likely to to be interacted with than one that was not.\n   */\n  Intent = 2,\n  /**\n   * The default priority for prefetch tasks.\n   */\n  Default = 1,\n  /**\n   * Assigned to tasks when they spawn non-blocking background work, like\n   * revalidating a partially cached entry to see if more data is available.\n   */\n  Background = 0,\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentSecurityPolicy: string\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  Mutable,\n  ReadonlyReducerState,\n  ReducerState,\n  RefreshAction,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from '../fill-lazy-items-till-leaf-with-head'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\nimport { refreshInactiveParallelSegments } from '../refetch-inactive-parallel-segments'\nimport { revalidateEntireCache } from '../../segment-cache'\n\nexport function refreshReducer(\n  state: ReadonlyReducerState,\n  action: RefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  let currentTree = state.tree\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [\n      currentTree[0],\n      currentTree[1],\n      currentTree[2],\n      'refetch',\n    ],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n  })\n\n  const navigatedAt = Date.now()\n  return cache.lazyData.then(\n    async ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      for (const normalizedFlightData of flightData) {\n        const {\n          tree: treePatch,\n          seedData: cacheNodeSeedData,\n          head,\n          isRootRender,\n        } = normalizedFlightData\n\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n\n        // Handles case where prefetch only returns the router tree patch without rendered components.\n        if (cacheNodeSeedData !== null) {\n          const rsc = cacheNodeSeedData[1]\n          const loading = cacheNodeSeedData[3]\n          cache.rsc = rsc\n          cache.prefetchRsc = null\n          cache.loading = loading\n          fillLazyItemsTillLeafWithHead(\n            navigatedAt,\n            cache,\n            // Existing cache is not passed in as `router.refresh()` has to invalidate the entire cache.\n            undefined,\n            treePatch,\n            cacheNodeSeedData,\n            head,\n            undefined\n          )\n          if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n            revalidateEntireCache(state.nextUrl, newTree)\n          } else {\n            mutable.prefetchCache = new Map()\n          }\n        }\n\n        await refreshInactiveParallelSegments({\n          navigatedAt,\n          state,\n          updatedTree: newTree,\n          updatedCache: cache,\n          includeNextUrl,\n          canonicalUrl: mutable.canonicalUrl || state.canonicalUrl,\n        })\n\n        mutable.cache = cache\n        mutable.patchedTree = newTree\n\n        currentTree = newTree\n      }\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "import { useEffect, useRef, useState } from 'react'\nimport { createPortal } from 'react-dom'\nimport type { FlightRouterState } from '../../server/app-render/types'\n\nconst ANNOUNCER_TYPE = 'next-route-announcer'\nconst ANNOUNCER_ID = '__next-route-announcer__'\n\nfunction getAnnouncerNode() {\n  const existingAnnouncer = document.getElementsByName(ANNOUNCER_TYPE)[0]\n  if (existingAnnouncer?.shadowRoot?.childNodes[0]) {\n    return existingAnnouncer.shadowRoot.childNodes[0] as HTMLElement\n  } else {\n    const container = document.createElement(ANNOUNCER_TYPE)\n    container.style.cssText = 'position:absolute'\n    const announcer = document.createElement('div')\n    announcer.ariaLive = 'assertive'\n    announcer.id = ANNOUNCER_ID\n    announcer.role = 'alert'\n    announcer.style.cssText =\n      'position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal'\n\n    // Use shadow DOM here to avoid any potential CSS bleed\n    const shadow = container.attachShadow({ mode: 'open' })\n    shadow.appendChild(announcer)\n    document.body.appendChild(container)\n    return announcer\n  }\n}\n\nexport function AppRouterAnnouncer({ tree }: { tree: FlightRouterState }) {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const announcer = getAnnouncerNode()\n    setPortalNode(announcer)\n    return () => {\n      const container = document.getElementsByTagName(ANNOUNCER_TYPE)[0]\n      if (container?.isConnected) {\n        document.body.removeChild(container)\n      }\n    }\n  }, [])\n\n  const [routeAnnouncement, setRouteAnnouncement] = useState('')\n  const previousTitle = useRef<string | undefined>(undefined)\n\n  useEffect(() => {\n    let currentTitle = ''\n    if (document.title) {\n      currentTitle = document.title\n    } else {\n      const pageHeader = document.querySelector('h1')\n      if (pageHeader) {\n        currentTitle = pageHeader.innerText || pageHeader.textContent || ''\n      }\n    }\n\n    // Only announce the title change, but not for the first load because screen\n    // readers do that automatically.\n    if (\n      previousTitle.current !== undefined &&\n      previousTitle.current !== currentTitle\n    ) {\n      setRouteAnnouncement(currentTitle)\n    }\n    previousTitle.current = currentTitle\n  }, [tree])\n\n  return portalNode ? createPortal(routeAnnouncement, portalNode) : null\n}\n", "import type {\n  FlightRouterState,\n  FlightDataPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\n\n// TODO-APP: flightSegmentPath will be empty in case of static response, needs to be handled.\nexport function shouldHardNavigate(\n  flightSegmentPath: FlightDataPath,\n  flightRouterState: FlightRouterState\n): boolean {\n  const [segment, parallelRoutes] = flightRouterState\n  // TODO-APP: Check if `as` can be replaced.\n  const [currentSegment, parallelRouteKey] = flightSegmentPath as [\n    Segment,\n    string,\n  ]\n\n  // Check if current segment matches the existing segment.\n  if (!matchSegment(currentSegment, segment)) {\n    // If dynamic parameter in tree doesn't match up with segment path a hard navigation is triggered.\n    if (Array.isArray(currentSegment)) {\n      return true\n    }\n\n    // If the existing segment did not match soft navigation is triggered.\n    return false\n  }\n  const lastSegment = flightSegmentPath.length <= 2\n\n  if (lastSegment) {\n    return false\n  }\n\n  return shouldHardNavigate(\n    getNextFlightSegmentPath(flightSegmentPath),\n    parallelRoutes[parallelRouteKey]\n  )\n}\n", "import {\n  fetchServerResponse,\n  type FetchServerResponseResult,\n} from './fetch-server-response'\nimport {\n  PrefetchCacheEntryStatus,\n  type PrefetchCacheEntry,\n  PrefetchKind,\n  type ReadonlyReducerState,\n} from './router-reducer-types'\nimport { prefetchQueue } from './reducers/prefetch-reducer'\n\nconst INTERCEPTION_CACHE_KEY_MARKER = '%'\n\nexport type AliasedPrefetchCacheEntry = PrefetchCacheEntry & {\n  /** This is a special property that indicates a prefetch entry associated with a different URL\n   * was returned rather than the requested URL. This signals to the router that it should only\n   * apply the part that doesn't depend on searchParams (specifically the loading state).\n   */\n  aliased?: boolean\n}\n\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */\nfunction createPrefetchCacheKeyImpl(\n  url: URL,\n  includeSearchParams: boolean,\n  prefix?: string | null\n) {\n  // Initially we only use the pathname as the cache key. We don't want to include\n  // search params so that multiple URLs with the same search parameter can re-use\n  // loading states.\n  let pathnameFromUrl = url.pathname\n\n  // RSC responses can differ based on search params, specifically in the case where we aren't\n  // returning a partial response (ie with `PrefetchKind.AUTO`).\n  // In the auto case, since loading.js & layout.js won't have access to search params,\n  // we can safely re-use that cache entry. But for full prefetches, we should not\n  // re-use the cache entry as the response may differ.\n  if (includeSearchParams) {\n    // if we have a full prefetch, we can include the search param in the key,\n    // as we'll be getting back a full response. The server might have read the search\n    // params when generating the full response.\n    pathnameFromUrl += url.search\n  }\n\n  if (prefix) {\n    return `${prefix}${INTERCEPTION_CACHE_KEY_MARKER}${pathnameFromUrl}`\n  }\n\n  return pathnameFromUrl\n}\n\nfunction createPrefetchCacheKey(\n  url: URL,\n  kind: PrefetchKind | undefined,\n  nextUrl?: string | null\n) {\n  return createPrefetchCacheKeyImpl(url, kind === PrefetchKind.FULL, nextUrl)\n}\n\nfunction getExistingCacheEntry(\n  url: URL,\n  kind: PrefetchKind = PrefetchKind.TEMPORARY,\n  nextUrl: string | null,\n  prefetchCache: Map<string, PrefetchCacheEntry>,\n  allowAliasing: boolean\n): AliasedPrefetchCacheEntry | undefined {\n  // We first check if there's a more specific interception route prefetch entry\n  // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n  // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n  for (const maybeNextUrl of [nextUrl, null]) {\n    const cacheKeyWithParams = createPrefetchCacheKeyImpl(\n      url,\n      true,\n      maybeNextUrl\n    )\n    const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(\n      url,\n      false,\n      maybeNextUrl\n    )\n\n    // First, we check if we have a cache entry that exactly matches the URL\n    const cacheKeyToUse = url.search\n      ? cacheKeyWithParams\n      : cacheKeyWithoutParams\n\n    const existingEntry = prefetchCache.get(cacheKeyToUse)\n    if (existingEntry && allowAliasing) {\n      // We know we're returning an aliased entry when the pathname matches but the search params don't,\n      const isAliased =\n        existingEntry.url.pathname === url.pathname &&\n        existingEntry.url.search !== url.search\n\n      if (isAliased) {\n        return {\n          ...existingEntry,\n          aliased: true,\n        }\n      }\n\n      return existingEntry\n    }\n\n    // If the request contains search params, and we're not doing a full prefetch, we can return the\n    // param-less entry if it exists.\n    // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n    // but lets us arrive there quicker in the param-full case.\n    const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams)\n    if (\n      process.env.NODE_ENV !== 'development' &&\n      allowAliasing &&\n      url.search &&\n      kind !== PrefetchKind.FULL &&\n      entryWithoutParams &&\n      // We shouldn't return the aliased entry if it was relocated to a new cache key.\n      // Since it's rewritten, it could respond with a completely different loading state.\n      !entryWithoutParams.key.includes(INTERCEPTION_CACHE_KEY_MARKER)\n    ) {\n      return { ...entryWithoutParams, aliased: true }\n    }\n  }\n\n  // If we've gotten to this point, we didn't find a specific cache entry that matched\n  // the request URL.\n  // We attempt a partial match by checking if there's a cache entry with the same pathname.\n  // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n  // This will signal to the router that it should only apply the loading state on the prefetched data.\n  if (\n    process.env.NODE_ENV !== 'development' &&\n    kind !== PrefetchKind.FULL &&\n    allowAliasing\n  ) {\n    for (const cacheEntry of prefetchCache.values()) {\n      if (\n        cacheEntry.url.pathname === url.pathname &&\n        // We shouldn't return the aliased entry if it was relocated to a new cache key.\n        // Since it's rewritten, it could respond with a completely different loading state.\n        !cacheEntry.key.includes(INTERCEPTION_CACHE_KEY_MARKER)\n      ) {\n        return { ...cacheEntry, aliased: true }\n      }\n    }\n  }\n\n  return undefined\n}\n\n/**\n * Returns a prefetch cache entry if one exists. Otherwise creates a new one and enqueues a fetch request\n * to retrieve the prefetch data from the server.\n */\nexport function getOrCreatePrefetchCacheEntry({\n  url,\n  nextUrl,\n  tree,\n  prefetchCache,\n  kind,\n  allowAliasing = true,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'prefetchCache' | 'tree'> & {\n  url: URL\n  kind?: PrefetchKind\n  allowAliasing: boolean\n}): AliasedPrefetchCacheEntry {\n  const existingCacheEntry = getExistingCacheEntry(\n    url,\n    kind,\n    nextUrl,\n    prefetchCache,\n    allowAliasing\n  )\n\n  if (existingCacheEntry) {\n    // Grab the latest status of the cache entry and update it\n    existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry)\n\n    // when `kind` is provided, an explicit prefetch was requested.\n    // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n    const switchedToFullPrefetch =\n      existingCacheEntry.kind !== PrefetchKind.FULL &&\n      kind === PrefetchKind.FULL\n\n    if (switchedToFullPrefetch) {\n      // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n      // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n      // are seeded but without a prefetch intent)\n      existingCacheEntry.data.then((prefetchResponse) => {\n        const isFullPrefetch =\n          Array.isArray(prefetchResponse.flightData) &&\n          prefetchResponse.flightData.some((flightData) => {\n            // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n            return flightData.isRootRender && flightData.seedData !== null\n          })\n\n        if (!isFullPrefetch) {\n          return createLazyPrefetchEntry({\n            tree,\n            url,\n            nextUrl,\n            prefetchCache,\n            // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n            // rather than assuming the same intent as the previous entry, to be consistent with how we\n            // lazily create prefetch entries when intent is left unspecified.\n            kind: kind ?? PrefetchKind.TEMPORARY,\n          })\n        }\n      })\n    }\n\n    // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n    // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n    if (kind && existingCacheEntry.kind === PrefetchKind.TEMPORARY) {\n      existingCacheEntry.kind = kind\n    }\n\n    // We've determined that the existing entry we found is still valid, so we return it.\n    return existingCacheEntry\n  }\n\n  // If we didn't return an entry, create a new one.\n  return createLazyPrefetchEntry({\n    tree,\n    url,\n    nextUrl,\n    prefetchCache,\n    kind: kind || PrefetchKind.TEMPORARY,\n  })\n}\n\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */\nfunction prefixExistingPrefetchCacheEntry({\n  url,\n  nextUrl,\n  prefetchCache,\n  existingCacheKey,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'prefetchCache'> & {\n  url: URL\n  existingCacheKey: string\n}) {\n  const existingCacheEntry = prefetchCache.get(existingCacheKey)\n  if (!existingCacheEntry) {\n    // no-op -- there wasn't an entry to move\n    return\n  }\n\n  const newCacheKey = createPrefetchCacheKey(\n    url,\n    existingCacheEntry.kind,\n    nextUrl\n  )\n  prefetchCache.set(newCacheKey, { ...existingCacheEntry, key: newCacheKey })\n  prefetchCache.delete(existingCacheKey)\n\n  return newCacheKey\n}\n\n/**\n * Use to seed the prefetch cache with data that has already been fetched.\n */\nexport function createSeededPrefetchCacheEntry({\n  nextUrl,\n  tree,\n  prefetchCache,\n  url,\n  data,\n  kind,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'tree' | 'prefetchCache'> & {\n  url: URL\n  data: FetchServerResponseResult\n  kind: PrefetchKind\n}) {\n  // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n  // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n  // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n  const prefetchCacheKey = data.couldBeIntercepted\n    ? createPrefetchCacheKey(url, kind, nextUrl)\n    : createPrefetchCacheKey(url, kind)\n\n  const prefetchEntry = {\n    treeAtTimeOfPrefetch: tree,\n    data: Promise.resolve(data),\n    kind,\n    prefetchTime: Date.now(),\n    lastUsedTime: Date.now(),\n    staleTime: -1,\n    key: prefetchCacheKey,\n    status: PrefetchCacheEntryStatus.fresh,\n    url,\n  } satisfies PrefetchCacheEntry\n\n  prefetchCache.set(prefetchCacheKey, prefetchEntry)\n\n  return prefetchEntry\n}\n\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */\nfunction createLazyPrefetchEntry({\n  url,\n  kind,\n  tree,\n  nextUrl,\n  prefetchCache,\n}: Pick<ReadonlyReducerState, 'nextUrl' | 'tree' | 'prefetchCache'> & {\n  url: URL\n  kind: PrefetchKind\n}): PrefetchCacheEntry {\n  const prefetchCacheKey = createPrefetchCacheKey(url, kind)\n\n  // initiates the fetch request for the prefetch and attaches a listener\n  // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n  const data = prefetchQueue.enqueue(() =>\n    fetchServerResponse(url, {\n      flightRouterState: tree,\n      nextUrl,\n      prefetchKind: kind,\n    }).then((prefetchResponse) => {\n      // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n      // to avoid drift between this cache key prefixing logic\n      // (which is currently directly influenced by the server response)\n      let newCacheKey\n\n      if (prefetchResponse.couldBeIntercepted) {\n        // Determine if we need to prefix the cache key with the nextUrl\n        newCacheKey = prefixExistingPrefetchCacheEntry({\n          url,\n          existingCacheKey: prefetchCacheKey,\n          nextUrl,\n          prefetchCache,\n        })\n      }\n\n      // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n      // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n      // staleTime.\n      if (prefetchResponse.prerendered) {\n        const existingCacheEntry = prefetchCache.get(\n          // if we prefixed the cache key due to route interception, we want to use the new key. Otherwise we use the original key\n          newCacheKey ?? prefetchCacheKey\n        )\n        if (existingCacheEntry) {\n          existingCacheEntry.kind = PrefetchKind.FULL\n          if (prefetchResponse.staleTime !== -1) {\n            // This is the stale time that was collected by the server during\n            // static generation. Use this in place of the default stale time.\n            existingCacheEntry.staleTime = prefetchResponse.staleTime\n          }\n        }\n      }\n\n      return prefetchResponse\n    })\n  )\n\n  const prefetchEntry = {\n    treeAtTimeOfPrefetch: tree,\n    data,\n    kind,\n    prefetchTime: Date.now(),\n    lastUsedTime: null,\n    staleTime: -1,\n    key: prefetchCacheKey,\n    status: PrefetchCacheEntryStatus.fresh,\n    url,\n  }\n\n  prefetchCache.set(prefetchCacheKey, prefetchEntry)\n\n  return prefetchEntry\n}\n\nexport function prunePrefetchCache(\n  prefetchCache: ReadonlyReducerState['prefetchCache']\n) {\n  for (const [href, prefetchCacheEntry] of prefetchCache) {\n    if (\n      getPrefetchEntryCacheStatus(prefetchCacheEntry) ===\n      PrefetchCacheEntryStatus.expired\n    ) {\n      prefetchCache.delete(href)\n    }\n  }\n}\n\n// These values are set by `define-env-plugin` (based on `nextConfig.experimental.staleTimes`)\n// and default to 5 minutes (static) / 0 seconds (dynamic)\nexport const DYNAMIC_STALETIME_MS =\n  Number(process.env.__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME) * 1000\n\nexport const STATIC_STALETIME_MS =\n  Number(process.env.__NEXT_CLIENT_ROUTER_STATIC_STALETIME) * 1000\n\nfunction getPrefetchEntryCacheStatus({\n  kind,\n  prefetchTime,\n  lastUsedTime,\n  staleTime,\n}: PrefetchCacheEntry): PrefetchCacheEntryStatus {\n  if (staleTime !== -1) {\n    // `staleTime` is the value sent by the server during static generation.\n    // When this is available, it takes precedence over any of the heuristics\n    // that follow.\n    //\n    // TODO: When PPR is enabled, the server will *always* return a stale time\n    // when prefetching. We should never use a prefetch entry that hasn't yet\n    // received data from the server. So the only two cases should be 1) we use\n    // the server-generated stale time 2) the unresolved entry is discarded.\n    return Date.now() < prefetchTime + staleTime\n      ? PrefetchCacheEntryStatus.fresh\n      : PrefetchCacheEntryStatus.stale\n  }\n\n  // We will re-use the cache entry data for up to the `dynamic` staletime window.\n  if (Date.now() < (lastUsedTime ?? prefetchTime) + DYNAMIC_STALETIME_MS) {\n    return lastUsedTime\n      ? PrefetchCacheEntryStatus.reusable\n      : PrefetchCacheEntryStatus.fresh\n  }\n\n  // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n  // A stale entry will only re-use the `loading` boundary, not the full data.\n  // This will trigger a \"lazy fetch\" for the full data.\n  if (kind === PrefetchKind.AUTO) {\n    if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n      return PrefetchCacheEntryStatus.stale\n    }\n  }\n\n  // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n  if (kind === PrefetchKind.FULL) {\n    if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n      return PrefetchCacheEntryStatus.reusable\n    }\n  }\n\n  return PrefetchCacheEntryStatus.expired\n}\n", "import {\n  ACTION_NAVIGATE,\n  ACTION_SERVER_PATCH,\n  ACTION_RESTORE,\n  ACTION_REFRESH,\n  ACTION_PREFETCH,\n  ACTION_HMR_REFRESH,\n  ACTION_SERVER_ACTION,\n} from './router-reducer-types'\nimport type {\n  ReducerActions,\n  ReducerState,\n  ReadonlyReducerState,\n} from './router-reducer-types'\nimport { navigateReducer } from './reducers/navigate-reducer'\nimport { serverPatchReducer } from './reducers/server-patch-reducer'\nimport { restoreReducer } from './reducers/restore-reducer'\nimport { refreshReducer } from './reducers/refresh-reducer'\nimport { prefetchReducer } from './reducers/prefetch-reducer'\nimport { hmrRefreshReducer } from './reducers/hmr-refresh-reducer'\nimport { serverActionReducer } from './reducers/server-action-reducer'\n\n/**\n * Reducer that handles the app-router state updates.\n */\nfunction clientReducer(\n  state: ReadonlyReducerState,\n  action: ReducerActions\n): ReducerState {\n  switch (action.type) {\n    case ACTION_NAVIGATE: {\n      return navigateReducer(state, action)\n    }\n    case ACTION_SERVER_PATCH: {\n      return serverPatchReducer(state, action)\n    }\n    case ACTION_RESTORE: {\n      return restoreReducer(state, action)\n    }\n    case ACTION_REFRESH: {\n      return refreshReducer(state, action)\n    }\n    case ACTION_HMR_REFRESH: {\n      return hmrRefreshReducer(state, action)\n    }\n    case ACTION_PREFETCH: {\n      return prefetchReducer(state, action)\n    }\n    case ACTION_SERVER_ACTION: {\n      return serverActionReducer(state, action)\n    }\n    // This case should never be hit as dispatch is strongly typed.\n    default:\n      throw new Error('Unknown action')\n  }\n}\n\nfunction serverReducer(\n  state: ReadonlyReducerState,\n  _action: ReducerActions\n): ReducerState {\n  return state\n}\n\n// we don't run the client reducer on the server, so we use a noop function for better tree shaking\nexport const reducer =\n  typeof window === 'undefined' ? serverReducer : clientReducer\n", "import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n", "import type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../server/app-render/types'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { matchSegment } from '../match-segments'\nimport { addRefreshMarkerToActiveParallelSegments } from './refetch-inactive-parallel-segments'\n\n/**\n * Deep merge of the two router states. Parallel route keys are preserved if the patch doesn't have them.\n */\nfunction applyPatch(\n  initialTree: FlightRouterState,\n  patchTree: FlightRouterState\n): FlightRouterState {\n  const [initialSegment, initialParallelRoutes] = initialTree\n  const [patchSegment, patchParallelRoutes] = patchTree\n\n  // if the applied patch segment is __DEFAULT__ then it can be ignored in favor of the initial tree\n  // this is because the __DEFAULT__ segment is used as a placeholder on navigation\n  if (\n    patchSegment === DEFAULT_SEGMENT_KEY &&\n    initialSegment !== DEFAULT_SEGMENT_KEY\n  ) {\n    return initialTree\n  }\n\n  if (matchSegment(initialSegment, patchSegment)) {\n    const newParallelRoutes: FlightRouterState[1] = {}\n    for (const key in initialParallelRoutes) {\n      const isInPatchTreeParallelRoutes =\n        typeof patchParallelRoutes[key] !== 'undefined'\n      if (isInPatchTreeParallelRoutes) {\n        newParallelRoutes[key] = applyPatch(\n          initialParallelRoutes[key],\n          patchParallelRoutes[key]\n        )\n      } else {\n        newParallelRoutes[key] = initialParallelRoutes[key]\n      }\n    }\n\n    for (const key in patchParallelRoutes) {\n      if (newParallelRoutes[key]) {\n        continue\n      }\n\n      newParallelRoutes[key] = patchParallelRoutes[key]\n    }\n\n    const tree: FlightRouterState = [initialSegment, newParallelRoutes]\n\n    // Copy over the existing tree\n    if (initialTree[2]) {\n      tree[2] = initialTree[2]\n    }\n\n    if (initialTree[3]) {\n      tree[3] = initialTree[3]\n    }\n\n    if (initialTree[4]) {\n      tree[4] = initialTree[4]\n    }\n\n    return tree\n  }\n\n  return patchTree\n}\n\n/**\n * Apply the router state from the Flight response, but skip patching default segments.\n * Useful for patching the router cache when navigating, where we persist the existing default segment if there isn't a new one.\n * Creates a new router state tree.\n */\nexport function applyRouterStatePatchToTree(\n  flightSegmentPath: FlightSegmentPath,\n  flightRouterState: FlightRouterState,\n  treePatch: FlightRouterState,\n  path: string\n): FlightRouterState | null {\n  const [segment, parallelRoutes, url, refetch, isRootLayout] =\n    flightRouterState\n\n  // Root refresh\n  if (flightSegmentPath.length === 1) {\n    const tree: FlightRouterState = applyPatch(flightRouterState, treePatch)\n\n    addRefreshMarkerToActiveParallelSegments(tree, path)\n\n    return tree\n  }\n\n  const [currentSegment, parallelRouteKey] = flightSegmentPath\n\n  // Tree path returned from the server should always match up with the current tree in the browser\n  if (!matchSegment(currentSegment, segment)) {\n    return null\n  }\n\n  const lastSegment = flightSegmentPath.length === 2\n\n  let parallelRoutePatch\n  if (lastSegment) {\n    parallelRoutePatch = applyPatch(parallelRoutes[parallelRouteKey], treePatch)\n  } else {\n    parallelRoutePatch = applyRouterStatePatchToTree(\n      getNextFlightSegmentPath(flightSegmentPath),\n      parallelRoutes[parallelRouteKey],\n      treePatch,\n      path\n    )\n\n    if (parallelRoutePatch === null) {\n      return null\n    }\n  }\n\n  const tree: FlightRouterState = [\n    flightSegmentPath[0],\n    {\n      ...parallelRoutes,\n      [parallelRouteKey]: parallelRoutePatch,\n    },\n    url,\n    refetch,\n  ]\n\n  // Current segment is the root layout\n  if (isRootLayout) {\n    tree[4] = true\n  }\n\n  addRefreshMarkerToActiveParallelSegments(tree, path)\n\n  return tree\n}\n", "import {\n  type AppRouterState,\n  type ReducerActions,\n  type ReducerState,\n  ACTION_REFRESH,\n  ACTION_SERVER_ACTION,\n  ACTION_NAVIGATE,\n  ACTION_RESTORE,\n  type NavigateAction,\n  ACTION_HMR_REFRESH,\n  PrefetchKind,\n  ACTION_PREFETCH,\n} from './router-reducer/router-reducer-types'\nimport { reducer } from './router-reducer/router-reducer'\nimport { startTransition } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { addBasePath } from '../add-base-path'\nimport { createPrefetchURL, isExternalURL } from './app-router'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport type {\n  AppRouterInstance,\n  NavigateOptions,\n  PrefetchOptions,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { setLinkForCurrentNavigation, type LinkInstance } from './links'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport type { ClientInstrumentationHooks } from '../app-index'\n\nexport type DispatchStatePromise = React.Dispatch<ReducerState>\n\nexport type AppRouterActionQueue = {\n  state: AppRouterState\n  dispatch: (payload: ReducerActions, setState: DispatchStatePromise) => void\n  action: (state: AppRouterState, action: ReducerActions) => ReducerState\n\n  onRouterTransitionStart:\n    | ((url: string, type: 'push' | 'replace' | 'traverse') => void)\n    | null\n\n  pending: ActionQueueNode | null\n  needsRefresh?: boolean\n  last: ActionQueueNode | null\n}\n\nexport type ActionQueueNode = {\n  payload: ReducerActions\n  next: ActionQueueNode | null\n  resolve: (value: ReducerState) => void\n  reject: (err: Error) => void\n  discarded?: boolean\n}\n\nfunction runRemainingActions(\n  actionQueue: AppRouterActionQueue,\n  setState: DispatchStatePromise\n) {\n  if (actionQueue.pending !== null) {\n    actionQueue.pending = actionQueue.pending.next\n    if (actionQueue.pending !== null) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runAction({\n        actionQueue,\n        action: actionQueue.pending,\n        setState,\n      })\n    } else {\n      // No more actions are pending, check if a refresh is needed\n      if (actionQueue.needsRefresh) {\n        actionQueue.needsRefresh = false\n        actionQueue.dispatch(\n          {\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          },\n          setState\n        )\n      }\n    }\n  }\n}\n\nasync function runAction({\n  actionQueue,\n  action,\n  setState,\n}: {\n  actionQueue: AppRouterActionQueue\n  action: ActionQueueNode\n  setState: DispatchStatePromise\n}) {\n  const prevState = actionQueue.state\n\n  actionQueue.pending = action\n\n  const payload = action.payload\n  const actionResult = actionQueue.action(prevState, payload)\n\n  function handleResult(nextState: AppRouterState) {\n    // if we discarded this action, the state should also be discarded\n    if (action.discarded) {\n      return\n    }\n\n    actionQueue.state = nextState\n\n    runRemainingActions(actionQueue, setState)\n    action.resolve(nextState)\n  }\n\n  // if the action is a promise, set up a callback to resolve it\n  if (isThenable(actionResult)) {\n    actionResult.then(handleResult, (err) => {\n      runRemainingActions(actionQueue, setState)\n      action.reject(err)\n    })\n  } else {\n    handleResult(actionResult)\n  }\n}\n\nfunction dispatchAction(\n  actionQueue: AppRouterActionQueue,\n  payload: ReducerActions,\n  setState: DispatchStatePromise\n) {\n  let resolvers: {\n    resolve: (value: ReducerState) => void\n    reject: (reason: any) => void\n  } = { resolve: setState, reject: () => {} }\n\n  // most of the action types are async with the exception of restore\n  // it's important that restore is handled quickly since it's fired on the popstate event\n  // and we don't want to add any delay on a back/forward nav\n  // this only creates a promise for the async actions\n  if (payload.type !== ACTION_RESTORE) {\n    // Create the promise and assign the resolvers to the object.\n    const deferredPromise = new Promise<AppRouterState>((resolve, reject) => {\n      resolvers = { resolve, reject }\n    })\n\n    startTransition(() => {\n      // we immediately notify React of the pending promise -- the resolver is attached to the action node\n      // and will be called when the associated action promise resolves\n      setState(deferredPromise)\n    })\n  }\n\n  const newAction: ActionQueueNode = {\n    payload,\n    next: null,\n    resolve: resolvers.resolve,\n    reject: resolvers.reject,\n  }\n\n  // Check if the queue is empty\n  if (actionQueue.pending === null) {\n    // The queue is empty, so add the action and start it immediately\n    // Mark this action as the last in the queue\n    actionQueue.last = newAction\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else if (\n    payload.type === ACTION_NAVIGATE ||\n    payload.type === ACTION_RESTORE\n  ) {\n    // Navigations (including back/forward) take priority over any pending actions.\n    // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n    actionQueue.pending.discarded = true\n\n    // The rest of the current queue should still execute after this navigation.\n    // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n    newAction.next = actionQueue.pending.next\n\n    // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n    if (actionQueue.pending.payload.type === ACTION_SERVER_ACTION) {\n      actionQueue.needsRefresh = true\n    }\n\n    runAction({\n      actionQueue,\n      action: newAction,\n      setState,\n    })\n  } else {\n    // The queue is not empty, so add the action to the end of the queue\n    // It will be started by runRemainingActions after the previous action finishes\n    if (actionQueue.last !== null) {\n      actionQueue.last.next = newAction\n    }\n    actionQueue.last = newAction\n  }\n}\n\nlet globalActionQueue: AppRouterActionQueue | null = null\n\nexport function createMutableActionQueue(\n  initialState: AppRouterState,\n  instrumentationHooks: ClientInstrumentationHooks | null\n): AppRouterActionQueue {\n  const actionQueue: AppRouterActionQueue = {\n    state: initialState,\n    dispatch: (payload: ReducerActions, setState: DispatchStatePromise) =>\n      dispatchAction(actionQueue, payload, setState),\n    action: async (state: AppRouterState, action: ReducerActions) => {\n      const result = reducer(state, action)\n      return result\n    },\n    pending: null,\n    last: null,\n    onRouterTransitionStart:\n      instrumentationHooks !== null &&\n      typeof instrumentationHooks.onRouterTransitionStart === 'function'\n        ? // This profiling hook will be called at the start of every navigation.\n          instrumentationHooks.onRouterTransitionStart\n        : null,\n  }\n\n  if (typeof window !== 'undefined') {\n    // The action queue is lazily created on hydration, but after that point\n    // it doesn't change. So we can store it in a global rather than pass\n    // it around everywhere via props/context.\n    if (globalActionQueue !== null) {\n      throw new Error(\n        'Internal Next.js Error: createMutableActionQueue was called more ' +\n          'than once'\n      )\n    }\n    globalActionQueue = actionQueue\n  }\n\n  return actionQueue\n}\n\nexport function getCurrentAppRouterState(): AppRouterState | null {\n  return globalActionQueue !== null ? globalActionQueue.state : null\n}\n\nfunction getAppRouterActionQueue(): AppRouterActionQueue {\n  if (globalActionQueue === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  return globalActionQueue\n}\n\nfunction getProfilingHookForOnNavigationStart() {\n  if (globalActionQueue !== null) {\n    return globalActionQueue.onRouterTransitionStart\n  }\n  return null\n}\n\nexport function dispatchNavigateAction(\n  href: string,\n  navigateType: NavigateAction['navigateType'],\n  shouldScroll: boolean,\n  linkInstanceRef: LinkInstance | null\n): void {\n  // TODO: This stuff could just go into the reducer. Leaving as-is for now\n  // since we're about to rewrite all the router reducer stuff anyway.\n  const url = new URL(addBasePath(href), location.href)\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    window.next.__pendingUrl = url\n  }\n\n  setLinkForCurrentNavigation(linkInstanceRef)\n\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, navigateType)\n  }\n\n  dispatchAppRouterAction({\n    type: ACTION_NAVIGATE,\n    url,\n    isExternalUrl: isExternalURL(url),\n    locationSearch: location.search,\n    shouldScroll,\n    navigateType,\n    allowAliasing: true,\n  })\n}\n\nexport function dispatchTraverseAction(\n  href: string,\n  tree: FlightRouterState | undefined\n) {\n  const onRouterTransitionStart = getProfilingHookForOnNavigationStart()\n  if (onRouterTransitionStart !== null) {\n    onRouterTransitionStart(href, 'traverse')\n  }\n  dispatchAppRouterAction({\n    type: ACTION_RESTORE,\n    url: new URL(href),\n    tree,\n  })\n}\n\n/**\n * The app router that is exposed through `useRouter`. These are public API\n * methods. Internal Next.js code should call the lower level methods directly\n * (although there's lots of existing code that doesn't do that).\n */\nexport const publicAppRouterInstance: AppRouterInstance = {\n  back: () => window.history.back(),\n  forward: () => window.history.forward(),\n  prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n    ? // Unlike the old implementation, the Segment Cache doesn't store its\n      // data in the router reducer state; it writes into a global mutable\n      // cache. So we don't need to dispatch an action.\n      (href: string, options?: PrefetchOptions) => {\n        const actionQueue = getAppRouterActionQueue()\n        prefetchWithSegmentCache(\n          href,\n          actionQueue.state.nextUrl,\n          actionQueue.state.tree,\n          options?.kind === PrefetchKind.FULL\n        )\n      }\n    : (href: string, options?: PrefetchOptions) => {\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue()\n        const url = createPrefetchURL(href)\n        if (url !== null) {\n          // The prefetch reducer doesn't actually update any state or\n          // trigger a rerender. It just writes to a mutable cache. So we\n          // shouldn't bother calling setState/dispatch; we can just re-run\n          // the reducer directly using the current state.\n          // TODO: Refactor this away from a \"reducer\" so it's\n          // less confusing.\n          prefetchReducer(actionQueue.state, {\n            type: ACTION_PREFETCH,\n            url,\n            kind: options?.kind ?? PrefetchKind.FULL,\n          })\n        }\n      },\n  replace: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'replace', options?.scroll ?? true, null)\n    })\n  },\n  push: (href: string, options?: NavigateOptions) => {\n    startTransition(() => {\n      dispatchNavigateAction(href, 'push', options?.scroll ?? true, null)\n    })\n  },\n  refresh: () => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_REFRESH,\n        origin: window.location.origin,\n      })\n    })\n  },\n  hmrRefresh: () => {\n    if (process.env.NODE_ENV !== 'development') {\n      throw new Error(\n        'hmrRefresh can only be used in development mode. Please use refresh instead.'\n      )\n    } else {\n      startTransition(() => {\n        dispatchAppRouterAction({\n          type: ACTION_HMR_REFRESH,\n          origin: window.location.origin,\n        })\n      })\n    }\n  },\n}\n\n// Exists for debugging purposes. Don't use in application code.\nif (typeof window !== 'undefined' && window.next) {\n  window.next.router = publicAppRouterInstance\n}\n", "var id = 0;\n\nfunction _class_private_field_loose_key(name) {\n    return \"__private_\" + id++ + \"_\" + name;\n}\nexport { _class_private_field_loose_key as _ };\n", "import type {\n  CacheNodeSeedData,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../../server/app-render/types'\nimport type {\n  CacheNode,\n  ChildSegmentMap,\n  HeadData,\n  LoadingModuleData,\n  ReadyCacheNode,\n} from '../../../shared/lib/app-router-context.shared-runtime'\nimport { DEFAULT_SEGMENT_KEY } from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport type { FetchServerResponseResult } from './fetch-server-response'\nimport { isNavigatingToNewRootLayout } from './is-navigating-to-new-root-layout'\nimport { DYNAMIC_STALETIME_MS } from './prefetch-cache-utils'\n\n// This is yet another tree type that is used to track pending promises that\n// need to be fulfilled once the dynamic data is received. The terminal nodes of\n// this tree represent the new Cache Node trees that were created during this\n// request. We can't use the Cache Node tree or Route State tree directly\n// because those include reused nodes, too. This tree is discarded as soon as\n// the navigation response is received.\ntype SPANavigationTask = {\n  // The router state that corresponds to the tree that this Task represents.\n  route: FlightRouterState\n  // The CacheNode that corresponds to the tree that this Task represents. If\n  // `children` is null (i.e. if this is a terminal task node), then `node`\n  // represents a brand new Cache Node tree, which way or may not need to be\n  // filled with dynamic data from the server.\n  node: CacheNode | null\n  // The tree sent to the server during the dynamic request. This is the\n  // same as `route`, except with the `refetch` marker set on dynamic segments.\n  // If all the segments are static, then this will be null, and no server\n  // request is required.\n  dynamicRequestTree: FlightRouterState | null\n  children: Map<string, SPANavigationTask> | null\n}\n\n// A special type used to bail out and trigger a full-page navigation.\ntype MPANavigationTask = {\n  // MPA tasks are distinguised from SPA tasks by having a null `route`.\n  route: null\n  node: null\n  dynamicRequestTree: null\n  children: null\n}\n\nconst MPA_NAVIGATION_TASK: MPANavigationTask = {\n  route: null,\n  node: null,\n  dynamicRequestTree: null,\n  children: null,\n}\n\nexport type Task = SPANavigationTask | MPANavigationTask\n\n// Creates a new Cache Node tree (i.e. copy-on-write) that represents the\n// optimistic result of a navigation, using both the current Cache Node tree and\n// data that was prefetched prior to navigation.\n//\n// At the moment we call this function, we haven't yet received the navigation\n// response from the server. It could send back something completely different\n// from the tree that was prefetched — due to rewrites, default routes, parallel\n// routes, etc.\n//\n// But in most cases, it will return the same tree that we prefetched, just with\n// the dynamic holes filled in. So we optimistically assume this will happen,\n// and accept that the real result could be arbitrarily different.\n//\n// We'll reuse anything that was already in the previous tree, since that's what\n// the server does.\n//\n// New segments (ones that don't appear in the old tree) are assigned an\n// unresolved promise. The data for these promises will be fulfilled later, when\n// the navigation response is received.\n//\n// The tree can be rendered immediately after it is created (that's why this is\n// a synchronous function). Any new trees that do not have prefetch data will\n// suspend during rendering, until the dynamic data streams in.\n//\n// Returns a Task object, which contains both the updated Cache Node and a path\n// to the pending subtrees that need to be resolved by the navigation response.\n//\n// A return value of `null` means there were no changes, and the previous tree\n// can be reused without initiating a server request.\nexport function startPPRNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  const segmentPath: Array<FlightSegmentPath> = []\n  return updateCacheNodeOnNavigation(\n    navigatedAt,\n    oldCacheNode,\n    oldRouterState,\n    newRouterState,\n    false,\n    prefetchData,\n    prefetchHead,\n    isPrefetchHeadPartial,\n    isSamePageNavigation,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction updateCacheNodeOnNavigation(\n  navigatedAt: number,\n  oldCacheNode: CacheNode,\n  oldRouterState: FlightRouterState,\n  newRouterState: FlightRouterState,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  isSamePageNavigation: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task | null {\n  // Diff the old and new trees to reuse the shared layouts.\n  const oldRouterStateChildren = oldRouterState[1]\n  const newRouterStateChildren = newRouterState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  if (!didFindRootLayout) {\n    // We're currently traversing the part of the tree that was also part of\n    // the previous route. If we discover a root layout, then we don't need to\n    // trigger an MPA navigation. See beginRenderingNewRouteTree for context.\n    const isRootLayout = newRouterState[4] === true\n    if (isRootLayout) {\n      // Found a matching root layout.\n      didFindRootLayout = true\n    }\n  }\n\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n\n  // Clone the current set of segment children, even if they aren't active in\n  // the new tree.\n  // TODO: We currently retain all the inactive segments indefinitely, until\n  // there's an explicit refresh, or a parent layout is lazily refreshed. We\n  // rely on this for popstate navigations, which update the Router State Tree\n  // but do not eagerly perform a data fetch, because they expect the segment\n  // data to already be in the Cache Node tree. For highly static sites that\n  // are mostly read-only, this may happen only rarely, causing memory to\n  // leak. We should figure out a better model for the lifetime of inactive\n  // segments, so we can maintain instant back/forward navigations without\n  // leaking memory indefinitely.\n  const prefetchParallelRoutes = new Map(oldParallelRoutes)\n\n  // As we diff the trees, we may sometimes modify (copy-on-write, not mutate)\n  // the Route Tree that was returned by the server — for example, in the case\n  // of default parallel routes, we preserve the currently active segment. To\n  // avoid mutating the original tree, we clone the router state children along\n  // the return path.\n  let patchedRouterStateChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let taskChildren = null\n\n  // Most navigations require a request to fetch additional data from the\n  // server, either because the data was not already prefetched, or because the\n  // target route contains dynamic data that cannot be prefetched.\n  //\n  // However, if the target route is fully static, and it's already completely\n  // loaded into the segment cache, then we can skip the server request.\n  //\n  // This starts off as `false`, and is set to `true` if any of the child\n  // routes requires a dynamic request.\n  let needsDynamicRequest = false\n  // As we traverse the children, we'll construct a FlightRouterState that can\n  // be sent to the server to request the dynamic data. If it turns out that\n  // nothing in the subtree is dynamic (i.e. needsDynamicRequest is false at the\n  // end), then this will be discarded.\n  // TODO: We can probably optimize the format of this data structure to only\n  // include paths that are dynamic. Instead of reusing the\n  // FlightRouterState type.\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n\n  for (let parallelRouteKey in newRouterStateChildren) {\n    const newRouterStateChild: FlightRouterState =\n      newRouterStateChildren[parallelRouteKey]\n    const oldRouterStateChild: FlightRouterState | void =\n      oldRouterStateChildren[parallelRouteKey]\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    const prefetchDataChild: CacheNodeSeedData | void | null =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const newSegmentChild = newRouterStateChild[0]\n    const newSegmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      newSegmentChild,\n    ])\n    const newSegmentKeyChild = createRouterCacheKey(newSegmentChild)\n\n    const oldSegmentChild =\n      oldRouterStateChild !== undefined ? oldRouterStateChild[0] : undefined\n\n    const oldCacheNodeChild =\n      oldSegmentMapChild !== undefined\n        ? oldSegmentMapChild.get(newSegmentKeyChild)\n        : undefined\n\n    let taskChild: Task | null\n    if (newSegmentChild === DEFAULT_SEGMENT_KEY) {\n      // This is another kind of leaf segment — a default route.\n      //\n      // Default routes have special behavior. When there's no matching segment\n      // for a parallel route, Next.js preserves the currently active segment\n      // during a client navigation — but not for initial render. The server\n      // leaves it to the client to account for this. So we need to handle\n      // it here.\n      if (oldRouterStateChild !== undefined) {\n        // Reuse the existing Router State for this segment. We spawn a \"task\"\n        // just to keep track of the updated router state; unlike most, it's\n        // already fulfilled and won't be affected by the dynamic response.\n        taskChild = spawnReusedTask(oldRouterStateChild)\n      } else {\n        // There's no currently active segment. Switch to the \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else if (\n      isSamePageNavigation &&\n      // Check if this is a page segment.\n      // TODO: We're not consistent about how we do this check. Some places\n      // check if the segment starts with PAGE_SEGMENT_KEY, but most seem to\n      // check if there any any children, which is why I'm doing it here. We\n      // should probably encode an empty children set as `null` though. Either\n      // way, we should update all the checks to be consistent.\n      Object.keys(newRouterStateChild[1]).length === 0\n    ) {\n      // We special case navigations to the exact same URL as the current\n      // location. It's a common UI pattern for apps to refresh when you click a\n      // link to the current page. So when this happens, we refresh the dynamic\n      // data in the page segments.\n      //\n      // Note that this does not apply if the any part of the hash or search\n      // query has changed. This might feel a bit weird but it makes more sense\n      // when you consider that the way to trigger this behavior is to click\n      // the same link multiple times.\n      //\n      // TODO: We should probably refresh the *entire* route when this case\n      // occurs, not just the page segments. Essentially treating it the same as\n      // a refresh() triggered by an action, which is the more explicit way of\n      // modeling the UI pattern described above.\n      //\n      // Also note that this only refreshes the dynamic data, not static/\n      // cached data. If the page segment is fully static and prefetched, the\n      // request is skipped. (This is also how refresh() works.)\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    } else if (\n      oldRouterStateChild !== undefined &&\n      oldSegmentChild !== undefined &&\n      matchSegment(newSegmentChild, oldSegmentChild)\n    ) {\n      if (\n        oldCacheNodeChild !== undefined &&\n        oldRouterStateChild !== undefined\n      ) {\n        // This segment exists in both the old and new trees. Recursively update\n        // the children.\n        taskChild = updateCacheNodeOnNavigation(\n          navigatedAt,\n          oldCacheNodeChild,\n          oldRouterStateChild,\n          newRouterStateChild,\n          didFindRootLayout,\n          prefetchDataChild,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          isSamePageNavigation,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      } else {\n        // There's no existing Cache Node for this segment. Switch to the\n        // \"create\" path.\n        taskChild = beginRenderingNewRouteTree(\n          navigatedAt,\n          oldRouterStateChild,\n          newRouterStateChild,\n          oldCacheNodeChild,\n          didFindRootLayout,\n          prefetchDataChild !== undefined ? prefetchDataChild : null,\n          prefetchHead,\n          isPrefetchHeadPartial,\n          newSegmentPathChild,\n          scrollableSegmentsResult\n        )\n      }\n    } else {\n      // This is a new tree. Switch to the \"create\" path.\n      taskChild = beginRenderingNewRouteTree(\n        navigatedAt,\n        oldRouterStateChild,\n        newRouterStateChild,\n        oldCacheNodeChild,\n        didFindRootLayout,\n        prefetchDataChild !== undefined ? prefetchDataChild : null,\n        prefetchHead,\n        isPrefetchHeadPartial,\n        newSegmentPathChild,\n        scrollableSegmentsResult\n      )\n    }\n\n    if (taskChild !== null) {\n      // Recursively propagate up the child tasks.\n\n      if (taskChild.route === null) {\n        // One of the child tasks discovered a change to the root layout.\n        // Immediately unwind from this recursive traversal.\n        return MPA_NAVIGATION_TASK\n      }\n\n      if (taskChildren === null) {\n        taskChildren = new Map()\n      }\n      taskChildren.set(parallelRouteKey, taskChild)\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(newSegmentKeyChild, newCacheNodeChild)\n        prefetchParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n\n      // The child tree's route state may be different from the prefetched\n      // route sent by the server. We need to clone it as we traverse back up\n      // the tree.\n      const taskChildRoute = taskChild.route\n      patchedRouterStateChildren[parallelRouteKey] = taskChildRoute\n\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = taskChildRoute\n      }\n    } else {\n      // The child didn't change. We can use the prefetched router state.\n      patchedRouterStateChildren[parallelRouteKey] = newRouterStateChild\n      dynamicRequestTreeChildren[parallelRouteKey] = newRouterStateChild\n    }\n  }\n\n  if (taskChildren === null) {\n    // No new tasks were spawned.\n    return null\n  }\n\n  const newCacheNode: ReadyCacheNode = {\n    lazyData: null,\n    rsc: oldCacheNode.rsc,\n    // We intentionally aren't updating the prefetchRsc field, since this node\n    // is already part of the current tree, because it would be weird for\n    // prefetch data to be newer than the final data. It probably won't ever be\n    // observable anyway, but it could happen if the segment is unmounted then\n    // mounted again, because LayoutRouter will momentarily switch to rendering\n    // prefetchRsc, via useDeferredValue.\n    prefetchRsc: oldCacheNode.prefetchRsc,\n    head: oldCacheNode.head,\n    prefetchHead: oldCacheNode.prefetchHead,\n    loading: oldCacheNode.loading,\n\n    // Everything is cloned except for the children, which we computed above.\n    parallelRoutes: prefetchParallelRoutes,\n\n    navigatedAt,\n  }\n\n  return {\n    // Return a cloned copy of the router state with updated children.\n    route: patchRouterStateWithNewChildren(\n      newRouterState,\n      patchedRouterStateChildren\n    ),\n    node: newCacheNode,\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(\n          newRouterState,\n          dynamicRequestTreeChildren\n        )\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction beginRenderingNewRouteTree(\n  navigatedAt: number,\n  oldRouterState: FlightRouterState | void,\n  newRouterState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  didFindRootLayout: boolean,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): Task {\n  if (!didFindRootLayout) {\n    // The route tree changed before we reached a layout. (The highest-level\n    // layout in a route tree is referred to as the \"root\" layout.) This could\n    // mean that we're navigating between two different root layouts. When this\n    // happens, we perform a full-page (MPA-style) navigation.\n    //\n    // However, the algorithm for deciding where to start rendering a route\n    // (i.e. the one performed in order to reach this function) is stricter\n    // than the one used to detect a change in the root layout. So just because\n    // we're re-rendering a segment outside of the root layout does not mean we\n    // should trigger a full-page navigation.\n    //\n    // Specifically, we handle dynamic parameters differently: two segments are\n    // considered the same even if their parameter values are different.\n    //\n    // Refer to isNavigatingToNewRootLayout for details.\n    //\n    // Note that we only have to perform this extra traversal if we didn't\n    // already discover a root layout in the part of the tree that is unchanged.\n    // In the common case, this branch is skipped completely.\n    if (\n      oldRouterState === undefined ||\n      isNavigatingToNewRootLayout(oldRouterState, newRouterState)\n    ) {\n      // The root layout changed. Perform a full-page navigation.\n      return MPA_NAVIGATION_TASK\n    }\n  }\n  return createCacheNodeOnNavigation(\n    navigatedAt,\n    newRouterState,\n    existingCacheNode,\n    prefetchData,\n    possiblyPartialPrefetchHead,\n    isPrefetchHeadPartial,\n    segmentPath,\n    scrollableSegmentsResult\n  )\n}\n\nfunction createCacheNodeOnNavigation(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  existingCacheNode: CacheNode | void,\n  prefetchData: CacheNodeSeedData | null,\n  possiblyPartialPrefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Same traversal as updateCacheNodeNavigation, but we switch to this path\n  // once we reach the part of the tree that was not in the previous route. We\n  // don't need to diff against the old tree, we just need to create a new one.\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const routerStateChildren = routerState[1]\n  const isLeafSegment = Object.keys(routerStateChildren).length === 0\n\n  // Even we're rendering inside the \"new\" part of the target tree, we may have\n  // a locally cached segment that we can reuse. This may come from either 1)\n  // the CacheNode tree, which lives in React state and is populated by previous\n  // navigations; or 2) the prefetch cache, which is a separate cache that is\n  // populated by prefetches.\n  let rsc: React.ReactNode\n  let loading: LoadingModuleData | Promise<LoadingModuleData>\n  let head: HeadData | null\n  let cacheNodeNavigatedAt: number\n  if (\n    existingCacheNode !== undefined &&\n    // DYNAMIC_STALETIME_MS defaults to 0, but it can be increased using\n    // the experimental.staleTimes.dynamic config. When set, we'll avoid\n    // refetching dynamic data if it was fetched within the given threshold.\n    existingCacheNode.navigatedAt + DYNAMIC_STALETIME_MS > navigatedAt\n  ) {\n    // We have an existing CacheNode for this segment, and it's not stale. We\n    // should reuse it rather than request a new one.\n    rsc = existingCacheNode.rsc\n    loading = existingCacheNode.loading\n    head = existingCacheNode.head\n\n    // Don't update the navigatedAt timestamp, since we're reusing stale data.\n    cacheNodeNavigatedAt = existingCacheNode.navigatedAt\n  } else if (prefetchData !== null) {\n    // There's no existing CacheNode for this segment, but we do have prefetch\n    // data. If the prefetch data is fully static (i.e. does not contain any\n    // dynamic holes), we don't need to request it from the server.\n    rsc = prefetchData[1]\n    loading = prefetchData[3]\n    head = isLeafSegment ? possiblyPartialPrefetchHead : null\n    // Even though we're accessing the data from the prefetch cache, this is\n    // conceptually a new segment, not a reused one. So we should update the\n    // navigatedAt timestamp.\n    cacheNodeNavigatedAt = navigatedAt\n    const isPrefetchRscPartial = prefetchData[4]\n    if (\n      // Check if the segment data is partial\n      isPrefetchRscPartial ||\n      // Check if the head is partial (only relevant if this is a leaf segment)\n      (isPrefetchHeadPartial && isLeafSegment)\n    ) {\n      // We only have partial data from this segment. Like missing segments, we\n      // must request the full data from the server.\n      return spawnPendingTask(\n        navigatedAt,\n        routerState,\n        prefetchData,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPath,\n        scrollableSegmentsResult\n      )\n    } else {\n      // The prefetch data is fully static, so we can omit it from the\n      // navigation request.\n    }\n  } else {\n    // There's no prefetch for this segment. Everything from this point will be\n    // requested from the server, even if there are static children below it.\n    // Create a terminal task node that will later be fulfilled by\n    // server response.\n    return spawnPendingTask(\n      navigatedAt,\n      routerState,\n      null,\n      possiblyPartialPrefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    )\n  }\n\n  // We already have a full segment we can render, so we don't need to request a\n  // new one from the server. Keep traversing down the tree until we reach\n  // something that requires a dynamic request.\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n  const taskChildren = new Map()\n  const existingCacheNodeChildren =\n    existingCacheNode !== undefined ? existingCacheNode.parallelRoutes : null\n  const cacheNodeChildren = new Map(existingCacheNodeChildren)\n  let dynamicRequestTreeChildren: {\n    [parallelRouteKey: string]: FlightRouterState\n  } = {}\n  let needsDynamicRequest = false\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  } else {\n    for (let parallelRouteKey in routerStateChildren) {\n      const routerStateChild: FlightRouterState =\n        routerStateChildren[parallelRouteKey]\n      const prefetchDataChild: CacheNodeSeedData | void | null =\n        prefetchDataChildren !== null\n          ? prefetchDataChildren[parallelRouteKey]\n          : null\n      const existingSegmentMapChild =\n        existingCacheNodeChildren !== null\n          ? existingCacheNodeChildren.get(parallelRouteKey)\n          : undefined\n      const segmentChild = routerStateChild[0]\n      const segmentPathChild = segmentPath.concat([\n        parallelRouteKey,\n        segmentChild,\n      ])\n      const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n      const existingCacheNodeChild =\n        existingSegmentMapChild !== undefined\n          ? existingSegmentMapChild.get(segmentKeyChild)\n          : undefined\n\n      const taskChild = createCacheNodeOnNavigation(\n        navigatedAt,\n        routerStateChild,\n        existingCacheNodeChild,\n        prefetchDataChild,\n        possiblyPartialPrefetchHead,\n        isPrefetchHeadPartial,\n        segmentPathChild,\n        scrollableSegmentsResult\n      )\n      taskChildren.set(parallelRouteKey, taskChild)\n      const dynamicRequestTreeChild = taskChild.dynamicRequestTree\n      if (dynamicRequestTreeChild !== null) {\n        // Something in the child tree is dynamic.\n        needsDynamicRequest = true\n        dynamicRequestTreeChildren[parallelRouteKey] = dynamicRequestTreeChild\n      } else {\n        dynamicRequestTreeChildren[parallelRouteKey] = routerStateChild\n      }\n      const newCacheNodeChild = taskChild.node\n      if (newCacheNodeChild !== null) {\n        const newSegmentMapChild: ChildSegmentMap = new Map()\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        cacheNodeChildren.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  return {\n    // Since we're inside a new route tree, unlike the\n    // `updateCacheNodeOnNavigation` path, the router state on the children\n    // tasks is always the same as the router state we pass in. So we don't need\n    // to clone/modify it.\n    route: routerState,\n    node: {\n      lazyData: null,\n      // Since this segment is already full, we don't need to use the\n      // `prefetchRsc` field.\n      rsc,\n      prefetchRsc: null,\n      head,\n      prefetchHead: null,\n      loading,\n      parallelRoutes: cacheNodeChildren,\n      navigatedAt: cacheNodeNavigatedAt,\n    },\n    dynamicRequestTree: needsDynamicRequest\n      ? patchRouterStateWithNewChildren(routerState, dynamicRequestTreeChildren)\n      : null,\n    children: taskChildren,\n  }\n}\n\nfunction patchRouterStateWithNewChildren(\n  baseRouterState: FlightRouterState,\n  newChildren: { [parallelRouteKey: string]: FlightRouterState }\n): FlightRouterState {\n  const clone: FlightRouterState = [baseRouterState[0], newChildren]\n  // Based on equivalent logic in apply-router-state-patch-to-tree, but should\n  // confirm whether we need to copy all of these fields. Not sure the server\n  // ever sends, e.g. the refetch marker.\n  if (2 in baseRouterState) {\n    clone[2] = baseRouterState[2]\n  }\n  if (3 in baseRouterState) {\n    clone[3] = baseRouterState[3]\n  }\n  if (4 in baseRouterState) {\n    clone[4] = baseRouterState[4]\n  }\n  return clone\n}\n\nfunction spawnPendingTask(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): SPANavigationTask {\n  // Create a task that will later be fulfilled by data from the server.\n\n  // Clone the prefetched route tree and the `refetch` marker to it. We'll send\n  // this to the server so it knows where to start rendering.\n  const dynamicRequestTree = patchRouterStateWithNewChildren(\n    routerState,\n    routerState[1]\n  )\n  dynamicRequestTree[3] = 'refetch'\n\n  const newTask: Task = {\n    route: routerState,\n\n    // Corresponds to the part of the route that will be rendered on the server.\n    node: createPendingCacheNode(\n      navigatedAt,\n      routerState,\n      prefetchData,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPath,\n      scrollableSegmentsResult\n    ),\n    // Because this is non-null, and it gets propagated up through the parent\n    // tasks, the root task will know that it needs to perform a server request.\n    dynamicRequestTree,\n    children: null,\n  }\n  return newTask\n}\n\nfunction spawnReusedTask(reusedRouterState: FlightRouterState): Task {\n  // Create a task that reuses an existing segment, e.g. when reusing\n  // the current active segment in place of a default route.\n  return {\n    route: reusedRouterState,\n    node: null,\n    dynamicRequestTree: null,\n    children: null,\n  }\n}\n\n// Writes a dynamic server response into the tree created by\n// updateCacheNodeOnNavigation. All pending promises that were spawned by the\n// navigation will be resolved, either with dynamic data from the server, or\n// `null` to indicate that the data is missing.\n//\n// A `null` value will trigger a lazy fetch during render, which will then patch\n// up the tree using the same mechanism as the non-PPR implementation\n// (serverPatchReducer).\n//\n// Usually, the server will respond with exactly the subset of data that we're\n// waiting for — everything below the nearest shared layout. But technically,\n// the server can return anything it wants.\n//\n// This does _not_ create a new tree; it modifies the existing one in place.\n// Which means it must follow the Suspense rules of cache safety.\nexport function listenForDynamicRequest(\n  task: SPANavigationTask,\n  responsePromise: Promise<FetchServerResponseResult>\n) {\n  responsePromise.then(\n    ({ flightData }: FetchServerResponseResult) => {\n      if (typeof flightData === 'string') {\n        // Happens when navigating to page in `pages` from `app`. We shouldn't\n        // get here because should have already handled this during\n        // the prefetch.\n        return\n      }\n      for (const normalizedFlightData of flightData) {\n        const {\n          segmentPath,\n          tree: serverRouterState,\n          seedData: dynamicData,\n          head: dynamicHead,\n        } = normalizedFlightData\n\n        if (!dynamicData) {\n          // This shouldn't happen. PPR should always send back a response.\n          // However, `FlightDataPath` is a shared type and the pre-PPR handling of\n          // this might return null.\n          continue\n        }\n\n        writeDynamicDataIntoPendingTask(\n          task,\n          segmentPath,\n          serverRouterState,\n          dynamicData,\n          dynamicHead\n        )\n      }\n\n      // Now that we've exhausted all the data we received from the server, if\n      // there are any remaining pending tasks in the tree, abort them now.\n      // If there's any missing data, it will trigger a lazy fetch.\n      abortTask(task, null)\n    },\n    (error: any) => {\n      // This will trigger an error during render\n      abortTask(task, error)\n    }\n  )\n}\n\nfunction writeDynamicDataIntoPendingTask(\n  rootTask: SPANavigationTask,\n  segmentPath: FlightSegmentPath,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  // The data sent by the server represents only a subtree of the app. We need\n  // to find the part of the task tree that matches the server response, and\n  // fulfill it using the dynamic data.\n  //\n  // segmentPath represents the parent path of subtree. It's a repeating pattern\n  // of parallel route key and segment:\n  //\n  //   [string, Segment, string, Segment, string, Segment, ...]\n  //\n  // Iterate through the path and finish any tasks that match this payload.\n  let task = rootTask\n  for (let i = 0; i < segmentPath.length; i += 2) {\n    const parallelRouteKey: string = segmentPath[i]\n    const segment: Segment = segmentPath[i + 1]\n    const taskChildren = task.children\n    if (taskChildren !== null) {\n      const taskChild = taskChildren.get(parallelRouteKey)\n      if (taskChild !== undefined) {\n        const taskSegment = taskChild.route[0]\n        if (matchSegment(segment, taskSegment)) {\n          // Found a match for this task. Keep traversing down the task tree.\n          task = taskChild\n          continue\n        }\n      }\n    }\n    // We didn't find a child task that matches the server data. Exit. We won't\n    // abort the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n    return\n  }\n\n  finishTaskUsingDynamicDataPayload(\n    task,\n    serverRouterState,\n    dynamicData,\n    dynamicHead\n  )\n}\n\nfunction finishTaskUsingDynamicDataPayload(\n  task: SPANavigationTask,\n  serverRouterState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n) {\n  if (task.dynamicRequestTree === null) {\n    // Everything in this subtree is already complete. Bail out.\n    return\n  }\n\n  // dynamicData may represent a larger subtree than the task. Before we can\n  // finish the task, we need to line them up.\n  const taskChildren = task.children\n  const taskNode = task.node\n  if (taskChildren === null) {\n    // We've reached the leaf node of the pending task. The server data tree\n    // lines up the pending Cache Node tree. We can now switch to the\n    // normal algorithm.\n    if (taskNode !== null) {\n      finishPendingCacheNode(\n        taskNode,\n        task.route,\n        serverRouterState,\n        dynamicData,\n        dynamicHead\n      )\n      // Set this to null to indicate that this task is now complete.\n      task.dynamicRequestTree = null\n    }\n    return\n  }\n  // The server returned more data than we need to finish the task. Skip over\n  // the extra segments until we reach the leaf task node.\n  const serverChildren = serverRouterState[1]\n  const dynamicDataChildren = dynamicData[2]\n\n  for (const parallelRouteKey in serverRouterState) {\n    const serverRouterStateChild: FlightRouterState =\n      serverChildren[parallelRouteKey]\n    const dynamicDataChild: CacheNodeSeedData | null | void =\n      dynamicDataChildren[parallelRouteKey]\n\n    const taskChild = taskChildren.get(parallelRouteKey)\n    if (taskChild !== undefined) {\n      const taskSegment = taskChild.route[0]\n      if (\n        matchSegment(serverRouterStateChild[0], taskSegment) &&\n        dynamicDataChild !== null &&\n        dynamicDataChild !== undefined\n      ) {\n        // Found a match for this task. Keep traversing down the task tree.\n        return finishTaskUsingDynamicDataPayload(\n          taskChild,\n          serverRouterStateChild,\n          dynamicDataChild,\n          dynamicHead\n        )\n      }\n    }\n    // We didn't find a child task that matches the server data. We won't abort\n    // the task, though, because a different FlightDataPath may be able to\n    // fulfill it (see loop in listenForDynamicRequest). We only abort tasks\n    // once we've run out of data.\n  }\n}\n\nfunction createPendingCacheNode(\n  navigatedAt: number,\n  routerState: FlightRouterState,\n  prefetchData: CacheNodeSeedData | null,\n  prefetchHead: HeadData | null,\n  isPrefetchHeadPartial: boolean,\n  segmentPath: FlightSegmentPath,\n  scrollableSegmentsResult: Array<FlightSegmentPath>\n): ReadyCacheNode {\n  const routerStateChildren = routerState[1]\n  const prefetchDataChildren = prefetchData !== null ? prefetchData[2] : null\n\n  const parallelRoutes = new Map()\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const prefetchDataChild: CacheNodeSeedData | null | void =\n      prefetchDataChildren !== null\n        ? prefetchDataChildren[parallelRouteKey]\n        : null\n\n    const segmentChild = routerStateChild[0]\n    const segmentPathChild = segmentPath.concat([\n      parallelRouteKey,\n      segmentChild,\n    ])\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n\n    const newCacheNodeChild = createPendingCacheNode(\n      navigatedAt,\n      routerStateChild,\n      prefetchDataChild === undefined ? null : prefetchDataChild,\n      prefetchHead,\n      isPrefetchHeadPartial,\n      segmentPathChild,\n      scrollableSegmentsResult\n    )\n\n    const newSegmentMapChild: ChildSegmentMap = new Map()\n    newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n    parallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n  }\n\n  // The head is assigned to every leaf segment delivered by the server. Based\n  // on corresponding logic in fill-lazy-items-till-leaf-with-head.ts\n  const isLeafSegment = parallelRoutes.size === 0\n\n  if (isLeafSegment) {\n    // The segment path of every leaf segment (i.e. page) is collected into\n    // a result array. This is used by the LayoutRouter to scroll to ensure that\n    // new pages are visible after a navigation.\n    // TODO: We should use a string to represent the segment path instead of\n    // an array. We already use a string representation for the path when\n    // accessing the Segment Cache, so we can use the same one.\n    scrollableSegmentsResult.push(segmentPath)\n  }\n\n  const maybePrefetchRsc = prefetchData !== null ? prefetchData[1] : null\n  const maybePrefetchLoading = prefetchData !== null ? prefetchData[3] : null\n  return {\n    lazyData: null,\n    parallelRoutes: parallelRoutes,\n\n    prefetchRsc: maybePrefetchRsc !== undefined ? maybePrefetchRsc : null,\n    prefetchHead: isLeafSegment ? prefetchHead : [null, null],\n\n    // TODO: Technically, a loading boundary could contain dynamic data. We must\n    // have separate `loading` and `prefetchLoading` fields to handle this, like\n    // we do for the segment data and head.\n    loading: maybePrefetchLoading !== undefined ? maybePrefetchLoading : null,\n\n    // Create a deferred promise. This will be fulfilled once the dynamic\n    // response is received from the server.\n    rsc: createDeferredRsc() as React.ReactNode,\n    head: isLeafSegment ? (createDeferredRsc() as React.ReactNode) : null,\n\n    navigatedAt,\n  }\n}\n\nfunction finishPendingCacheNode(\n  cacheNode: CacheNode,\n  taskState: FlightRouterState,\n  serverState: FlightRouterState,\n  dynamicData: CacheNodeSeedData,\n  dynamicHead: HeadData\n): void {\n  // Writes a dynamic response into an existing Cache Node tree. This does _not_\n  // create a new tree, it updates the existing tree in-place. So it must follow\n  // the Suspense rules of cache safety — it can resolve pending promises, but\n  // it cannot overwrite existing data. It can add segments to the tree (because\n  // a missing segment will cause the layout router to suspend).\n  // but it cannot delete them.\n  //\n  // We must resolve every promise in the tree, or else it will suspend\n  // indefinitely. If we did not receive data for a segment, we will resolve its\n  // data promise to `null` to trigger a lazy fetch during render.\n  const taskStateChildren = taskState[1]\n  const serverStateChildren = serverState[1]\n  const dataChildren = dynamicData[2]\n\n  // The router state that we traverse the tree with (taskState) is the same one\n  // that we used to construct the pending Cache Node tree. That way we're sure\n  // to resolve all the pending promises.\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in taskStateChildren) {\n    const taskStateChild: FlightRouterState =\n      taskStateChildren[parallelRouteKey]\n    const serverStateChild: FlightRouterState | void =\n      serverStateChildren[parallelRouteKey]\n    const dataChild: CacheNodeSeedData | null | void =\n      dataChildren[parallelRouteKey]\n\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    const taskSegmentChild = taskStateChild[0]\n    const taskSegmentKeyChild = createRouterCacheKey(taskSegmentChild)\n\n    const cacheNodeChild =\n      segmentMapChild !== undefined\n        ? segmentMapChild.get(taskSegmentKeyChild)\n        : undefined\n\n    if (cacheNodeChild !== undefined) {\n      if (\n        serverStateChild !== undefined &&\n        matchSegment(taskSegmentChild, serverStateChild[0])\n      ) {\n        if (dataChild !== undefined && dataChild !== null) {\n          // This is the happy path. Recursively update all the children.\n          finishPendingCacheNode(\n            cacheNodeChild,\n            taskStateChild,\n            serverStateChild,\n            dataChild,\n            dynamicHead\n          )\n        } else {\n          // The server never returned data for this segment. Trigger a lazy\n          // fetch during render. This shouldn't happen because the Route Tree\n          // and the Seed Data tree sent by the server should always be the same\n          // shape when part of the same server response.\n          abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n        }\n      } else {\n        // The server never returned data for this segment. Trigger a lazy\n        // fetch during render.\n        abortPendingCacheNode(taskStateChild, cacheNodeChild, null)\n      }\n    } else {\n      // The server response matches what was expected to receive, but there's\n      // no matching Cache Node in the task tree. This is a bug in the\n      // implementation because we should have created a node for every\n      // segment in the tree that's associated with this task.\n    }\n  }\n\n  // Use the dynamic data from the server to fulfill the deferred RSC promise\n  // on the Cache Node.\n  const rsc = cacheNode.rsc\n  const dynamicSegmentData = dynamicData[1]\n  if (rsc === null) {\n    // This is a lazy cache node. We can overwrite it. This is only safe\n    // because we know that the LayoutRouter suspends if `rsc` is `null`.\n    cacheNode.rsc = dynamicSegmentData\n  } else if (isDeferredRsc(rsc)) {\n    // This is a deferred RSC promise. We can fulfill it with the data we just\n    // received from the server. If it was already resolved by a different\n    // navigation, then this does nothing because we can't overwrite data.\n    rsc.resolve(dynamicSegmentData)\n  } else {\n    // This is not a deferred RSC promise, nor is it empty, so it must have\n    // been populated by a different navigation. We must not overwrite it.\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved with the dynamic head from\n  // the server.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(dynamicHead)\n  }\n}\n\nexport function abortTask(task: SPANavigationTask, error: any): void {\n  const cacheNode = task.node\n  if (cacheNode === null) {\n    // This indicates the task is already complete.\n    return\n  }\n\n  const taskChildren = task.children\n  if (taskChildren === null) {\n    // Reached the leaf task node. This is the root of a pending cache\n    // node tree.\n    abortPendingCacheNode(task.route, cacheNode, error)\n  } else {\n    // This is an intermediate task node. Keep traversing until we reach a\n    // task node with no children. That will be the root of the cache node tree\n    // that needs to be resolved.\n    for (const taskChild of taskChildren.values()) {\n      abortTask(taskChild, error)\n    }\n  }\n\n  // Set this to null to indicate that this task is now complete.\n  task.dynamicRequestTree = null\n}\n\nfunction abortPendingCacheNode(\n  routerState: FlightRouterState,\n  cacheNode: CacheNode,\n  error: any\n): void {\n  // For every pending segment in the tree, resolve its `rsc` promise to `null`\n  // to trigger a lazy fetch during render.\n  //\n  // Or, if an error object is provided, it will error instead.\n  const routerStateChildren = routerState[1]\n  const parallelRoutes = cacheNode.parallelRoutes\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentMapChild = parallelRoutes.get(parallelRouteKey)\n    if (segmentMapChild === undefined) {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n      continue\n    }\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const cacheNodeChild = segmentMapChild.get(segmentKeyChild)\n    if (cacheNodeChild !== undefined) {\n      abortPendingCacheNode(routerStateChild, cacheNodeChild, error)\n    } else {\n      // This shouldn't happen because we're traversing the same tree that was\n      // used to construct the cache nodes in the first place.\n    }\n  }\n  const rsc = cacheNode.rsc\n  if (isDeferredRsc(rsc)) {\n    if (error === null) {\n      // This will trigger a lazy fetch during render.\n      rsc.resolve(null)\n    } else {\n      // This will trigger an error during rendering.\n      rsc.reject(error)\n    }\n  }\n\n  // Check if this is a leaf segment. If so, it will have a `head` property with\n  // a pending promise that needs to be resolved. If an error was provided, we\n  // will not resolve it with an error, since this is rendered at the root of\n  // the app. We want the segment to error, not the entire app.\n  const head = cacheNode.head\n  if (isDeferredRsc(head)) {\n    head.resolve(null)\n  }\n}\n\nexport function updateCacheNodeOnPopstateRestoration(\n  oldCacheNode: CacheNode,\n  routerState: FlightRouterState\n): ReadyCacheNode {\n  // A popstate navigation reads data from the local cache. It does not issue\n  // new network requests (unless the cache entries have been evicted). So, we\n  // update the cache to drop the prefetch data for any segment whose dynamic\n  // data was already received. This prevents an unnecessary flash back to PPR\n  // state during a back/forward navigation.\n  //\n  // This function clones the entire cache node tree and sets the `prefetchRsc`\n  // field to `null` to prevent it from being rendered. We can't mutate the node\n  // in place because this is a concurrent data structure.\n\n  const routerStateChildren = routerState[1]\n  const oldParallelRoutes = oldCacheNode.parallelRoutes\n  const newParallelRoutes = new Map(oldParallelRoutes)\n  for (let parallelRouteKey in routerStateChildren) {\n    const routerStateChild: FlightRouterState =\n      routerStateChildren[parallelRouteKey]\n    const segmentChild = routerStateChild[0]\n    const segmentKeyChild = createRouterCacheKey(segmentChild)\n    const oldSegmentMapChild = oldParallelRoutes.get(parallelRouteKey)\n    if (oldSegmentMapChild !== undefined) {\n      const oldCacheNodeChild = oldSegmentMapChild.get(segmentKeyChild)\n      if (oldCacheNodeChild !== undefined) {\n        const newCacheNodeChild = updateCacheNodeOnPopstateRestoration(\n          oldCacheNodeChild,\n          routerStateChild\n        )\n        const newSegmentMapChild = new Map(oldSegmentMapChild)\n        newSegmentMapChild.set(segmentKeyChild, newCacheNodeChild)\n        newParallelRoutes.set(parallelRouteKey, newSegmentMapChild)\n      }\n    }\n  }\n\n  // Only show prefetched data if the dynamic data is still pending.\n  //\n  // Tehnically, what we're actually checking is whether the dynamic network\n  // response was received. But since it's a streaming response, this does not\n  // mean that all the dynamic data has fully streamed in. It just means that\n  // _some_ of the dynamic data was received. But as a heuristic, we assume that\n  // the rest dynamic data will stream in quickly, so it's still better to skip\n  // the prefetch state.\n  const rsc = oldCacheNode.rsc\n  const shouldUsePrefetch = isDeferredRsc(rsc) && rsc.status === 'pending'\n\n  return {\n    lazyData: null,\n    rsc,\n    head: oldCacheNode.head,\n\n    prefetchHead: shouldUsePrefetch ? oldCacheNode.prefetchHead : [null, null],\n    prefetchRsc: shouldUsePrefetch ? oldCacheNode.prefetchRsc : null,\n    loading: oldCacheNode.loading,\n\n    // These are the cloned children we computed above\n    parallelRoutes: newParallelRoutes,\n\n    navigatedAt: oldCacheNode.navigatedAt,\n  }\n}\n\nconst DEFERRED = Symbol()\n\ntype PendingDeferredRsc = Promise<React.ReactNode> & {\n  status: 'pending'\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype FulfilledDeferredRsc = Promise<React.ReactNode> & {\n  status: 'fulfilled'\n  value: React.ReactNode\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype RejectedDeferredRsc = Promise<React.ReactNode> & {\n  status: 'rejected'\n  reason: any\n  resolve: (value: React.ReactNode) => void\n  reject: (error: any) => void\n  tag: Symbol\n}\n\ntype DeferredRsc =\n  | PendingDeferredRsc\n  | FulfilledDeferredRsc\n  | RejectedDeferredRsc\n\n// This type exists to distinguish a DeferredRsc from a Flight promise. It's a\n// compromise to avoid adding an extra field on every Cache Node, which would be\n// awkward because the pre-PPR parts of codebase would need to account for it,\n// too. We can remove it once type Cache Node type is more settled.\nfunction isDeferredRsc(value: any): value is DeferredRsc {\n  return value && value.tag === DEFERRED\n}\n\nfunction createDeferredRsc(): PendingDeferredRsc {\n  let resolve: any\n  let reject: any\n  const pendingRsc = new Promise<React.ReactNode>((res, rej) => {\n    resolve = res\n    reject = rej\n  }) as PendingDeferredRsc\n  pendingRsc.status = 'pending'\n  pendingRsc.resolve = (value: React.ReactNode) => {\n    if (pendingRsc.status === 'pending') {\n      const fulfilledRsc: FulfilledDeferredRsc = pendingRsc as any\n      fulfilledRsc.status = 'fulfilled'\n      fulfilledRsc.value = value\n      resolve(value)\n    }\n  }\n  pendingRsc.reject = (error: any) => {\n    if (pendingRsc.status === 'pending') {\n      const rejectedRsc: RejectedDeferredRsc = pendingRsc as any\n      rejectedRsc.status = 'rejected'\n      rejectedRsc.reason = error\n      reject(error)\n    }\n  }\n  pendingRsc.tag = DEFERRED\n  return pendingRsc\n}\n", "import type { FlightRouterState } from '../../server/app-render/types'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { getCurrentAppRouterState } from './app-router-instance'\nimport { createPrefetchURL } from './app-router'\nimport { PrefetchKind } from './router-reducer/router-reducer-types'\nimport { getCurrentCacheVersion } from './segment-cache'\nimport { createCacheKey } from './segment-cache'\nimport {\n  type PrefetchTask,\n  PrefetchPriority,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  reschedulePrefetchTask,\n} from './segment-cache'\nimport { startTransition } from 'react'\n\ntype LinkElement = HTMLAnchorElement | SVGAElement\n\ntype Element = LinkElement | HTMLFormElement\n\n// Properties that are shared between Link and Form instances. We use the same\n// shape for both to prevent a polymorphic de-opt in the VM.\ntype LinkOrFormInstanceShared = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\nexport type FormInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: null\n}\n\ntype PrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: string\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype NonPrefetchableLinkInstance = LinkOrFormInstanceShared & {\n  prefetchHref: null\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n}\n\ntype PrefetchableInstance = PrefetchableLinkInstance | FormInstance\n\nexport type LinkInstance =\n  | PrefetchableLinkInstance\n  | NonPrefetchableLinkInstance\n\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation: LinkInstance | null = null\n\n// Status object indicating link is pending\nexport const PENDING_LINK_STATUS = { pending: true }\n\n// Status object indicating link is idle\nexport const IDLE_LINK_STATUS = { pending: false }\n\n// Updates the loading state when navigating between links\n// - Resets the previous link's loading state\n// - Sets the new link's loading state\n// - Updates tracking of current navigation\nexport function setLinkForCurrentNavigation(link: LinkInstance | null) {\n  startTransition(() => {\n    linkForMostRecentNavigation?.setOptimisticLinkStatus(IDLE_LINK_STATUS)\n    link?.setOptimisticLinkStatus(PENDING_LINK_STATUS)\n    linkForMostRecentNavigation = link\n  })\n}\n\n// Unmounts the current link instance from navigation tracking\nexport function unmountLinkForCurrentNavigation(link: LinkInstance) {\n  if (linkForMostRecentNavigation === link) {\n    linkForMostRecentNavigation = null\n  }\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable:\n  | WeakMap<Element, PrefetchableInstance>\n  | Map<Element, PrefetchableInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible: Set<PrefetchableInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction observeVisibility(element: Element, instance: PrefetchableInstance) {\n  const existingInstance = prefetchable.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountPrefetchableInstance(element)\n  }\n  // Only track prefetchable links that have a valid prefetch URL\n  prefetchable.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction coercePrefetchableUrl(href: string): URL | null {\n  try {\n    return createPrefetchURL(href)\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return null\n  }\n}\n\nexport function mountLinkInstance(\n  element: LinkElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL,\n  prefetchEnabled: boolean,\n  setOptimisticLinkStatus: (status: { pending: boolean }) => void\n): LinkInstance {\n  if (prefetchEnabled) {\n    const prefetchURL = coercePrefetchableUrl(href)\n    if (prefetchURL !== null) {\n      const instance: PrefetchableLinkInstance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus,\n      }\n      // We only observe the link's visibility if it's prefetchable. For\n      // example, this excludes links to external URLs.\n      observeVisibility(element, instance)\n      return instance\n    }\n  }\n  // If the link is not prefetchable, we still create an instance so we can\n  // track its optimistic state (i.e. useLinkStatus).\n  const instance: NonPrefetchableLinkInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: null,\n    setOptimisticLinkStatus,\n  }\n  return instance\n}\n\nexport function mountFormInstance(\n  element: HTMLFormElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n): void {\n  const prefetchURL = coercePrefetchableUrl(href)\n  if (prefetchURL === null) {\n    // This href is not prefetchable, so we don't track it.\n    // TODO: We currently observe/unobserve a form every time its href changes.\n    // For Links, this isn't a big deal because the href doesn't usually change,\n    // but for forms it's extremely common. We should optimize this.\n    return\n  }\n  const instance: FormInstance = {\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n    prefetchHref: prefetchURL.href,\n    setOptimisticLinkStatus: null,\n  }\n  observeVisibility(element, instance)\n}\n\nexport function unmountPrefetchableInstance(element: Element) {\n  const instance = prefetchable.get(element)\n  if (instance !== undefined) {\n    prefetchable.delete(element)\n    prefetchableAndVisible.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nexport function onLinkVisibilityChanged(element: Element, isVisible: boolean) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    prefetchableAndVisible.add(instance)\n  } else {\n    prefetchableAndVisible.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nexport function onNavigationIntent(\n  element: HTMLAnchorElement | SVGAElement,\n  unstable_upgradeToDynamicPrefetch: boolean\n) {\n  const instance = prefetchable.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    if (\n      process.env.__NEXT_DYNAMIC_ON_HOVER &&\n      unstable_upgradeToDynamicPrefetch\n    ) {\n      // Switch to a full, dynamic prefetch\n      instance.kind = PrefetchKind.FULL\n    }\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: PrefetchableInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with reschedulePrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  const appRouterState = getCurrentAppRouterState()\n  if (appRouterState !== null) {\n    const treeAtTimeOfPrefetch = appRouterState.tree\n    if (existingPrefetchTask === null) {\n      // Initiate a prefetch task.\n      const nextUrl = appRouterState.nextUrl\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    } else {\n      // We already have an old task object that we can reschedule. This is\n      // effectively the same as canceling the old task and creating a new one.\n      reschedulePrefetchTask(\n        existingPrefetchTask,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n    }\n\n    // Keep track of the cache version at the time the prefetch was requested.\n    // This is used to check if the prefetch is stale.\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of prefetchableAndVisible) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: PrefetchableInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n", "// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n", "let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n", "import type { FlightRouterState } from '../../../server/app-render/types'\n\nexport function isNavigatingToNewRootLayout(\n  currentTree: FlightRouterState,\n  nextTree: FlightRouterState\n): boolean {\n  // Compare segments\n  const currentTreeSegment = currentTree[0]\n  const nextTreeSegment = nextTree[0]\n\n  // If any segment is different before we find the root layout, the root layout has changed.\n  // E.g. /same/(group1)/layout.js -> /same/(group2)/layout.js\n  // First segment is 'same' for both, keep looking. (group1) changed to (group2) before the root layout was found, it must have changed.\n  if (Array.isArray(currentTreeSegment) && Array.isArray(nextTreeSegment)) {\n    // Compare dynamic param name and type but ignore the value, different values would not affect the current root layout\n    // /[name] - /slug1 and /slug2, both values (slug1 & slug2) still has the same layout /[name]/layout.js\n    if (\n      currentTreeSegment[0] !== nextTreeSegment[0] ||\n      currentTreeSegment[2] !== nextTreeSegment[2]\n    ) {\n      return true\n    }\n  } else if (currentTreeSegment !== nextTreeSegment) {\n    return true\n  }\n\n  // Current tree root layout found\n  if (currentTree[4]) {\n    // If the next tree doesn't have the root layout flag, it must have changed.\n    return !nextTree[4]\n  }\n  // Current tree didn't have its root layout here, must have changed.\n  if (nextTree[4]) {\n    return true\n  }\n  // We can't assume it's `parallelRoutes.children` here in case the root layout is `app/@something/layout.js`\n  // But it's not possible to be more than one parallelRoutes before the root layout is found\n  // TODO-APP: change to traverse all parallel routes\n  const currentTreeChild = Object.values(currentTree[1])[0]\n  const nextTreeChild = Object.values(nextTree[1])[0]\n  if (!currentTreeChild || !nextTreeChild) return true\n  return isNavigatingToNewRootLayout(currentTreeChild, nextTreeChild)\n}\n", "import type { FlightSegmentPath } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * This will clear the CacheNode data for a particular segment path. This will cause a lazy-fetch in layout router to fill in new data.\n */\nexport function clearCacheNodeDataForSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n\n  const [parallelRouteKey, segment] = flightSegmentPath\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap?.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  // In case of last segment start off the fetch at this level and don't copy further down.\n  if (isLastEntry) {\n    if (\n      !childCacheNode ||\n      !childCacheNode.lazyData ||\n      childCacheNode === existingChildCacheNode\n    ) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Start fetch in the place where the existing cache doesn't have the data yet.\n    if (!childCacheNode) {\n      childSegmentMap.set(cacheKey, {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      })\n    }\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n      loading: childCacheNode.loading,\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  return clearCacheNodeDataForSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n", "/*\n    This is a simple promise queue that allows you to limit the number of concurrent promises\n    that are running at any given time. It's used to limit the number of concurrent\n    prefetch requests that are being made to the server but could be used for other\n    things as well.\n*/\nexport class PromiseQueue {\n  #maxConcurrency: number\n  #runningCount: number\n  #queue: Array<{\n    promiseFn: Promise<any>\n    task: () => void\n  }>\n\n  constructor(maxConcurrency = 5) {\n    this.#maxConcurrency = maxConcurrency\n    this.#runningCount = 0\n    this.#queue = []\n  }\n\n  enqueue<T>(promiseFn: () => Promise<T>): Promise<T> {\n    let taskResolve: (value: T | PromiseLike<T>) => void\n    let taskReject: (reason?: any) => void\n\n    const taskPromise = new Promise((resolve, reject) => {\n      taskResolve = resolve\n      taskReject = reject\n    }) as Promise<T>\n\n    const task = async () => {\n      try {\n        this.#runningCount++\n        const result = await promiseFn()\n        taskResolve(result)\n      } catch (error) {\n        taskReject(error)\n      } finally {\n        this.#runningCount--\n        this.#processNext()\n      }\n    }\n\n    const enqueueResult = { promiseFn: taskPromise, task }\n    // wonder if we should take a LIFO approach here\n    this.#queue.push(enqueueResult)\n    this.#processNext()\n\n    return taskPromise\n  }\n\n  bump(promiseFn: Promise<any>) {\n    const index = this.#queue.findIndex((item) => item.promiseFn === promiseFn)\n\n    if (index > -1) {\n      const bumpedItem = this.#queue.splice(index, 1)[0]\n      this.#queue.unshift(bumpedItem)\n      this.#processNext(true)\n    }\n  }\n\n  #processNext(forced = false) {\n    if (\n      (this.#runningCount < this.#maxConcurrency || forced) &&\n      this.#queue.length > 0\n    ) {\n      this.#queue.shift()?.task()\n    }\n  }\n}\n", "// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n", "import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } = require('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } = require('./match-remote-pattern')\n        if (!hasRemoteMatch(config.domains, config.remotePatterns, parsedSrc)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightSegmentPath } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\nimport { getNextFlightSegmentPath } from '../../flight-data-helpers'\n\n/**\n * Fill cache up to the end of the flightSegmentPath, invalidating anything below it.\n */\nexport function invalidateCacheBelowFlightSegmentPath(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath\n): void {\n  const isLastEntry = flightSegmentPath.length <= 2\n  const [parallelRouteKey, segment] = flightSegmentPath\n\n  const cacheKey = createRouterCacheKey(segment)\n\n  const existingChildSegmentMap =\n    existingCache.parallelRoutes.get(parallelRouteKey)\n\n  if (!existingChildSegmentMap) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  let childSegmentMap = newCache.parallelRoutes.get(parallelRouteKey)\n  if (!childSegmentMap || childSegmentMap === existingChildSegmentMap) {\n    childSegmentMap = new Map(existingChildSegmentMap)\n    newCache.parallelRoutes.set(parallelRouteKey, childSegmentMap)\n  }\n\n  // In case of last entry don't copy further down.\n  if (isLastEntry) {\n    childSegmentMap.delete(cacheKey)\n    return\n  }\n\n  const existingChildCacheNode = existingChildSegmentMap.get(cacheKey)\n  let childCacheNode = childSegmentMap.get(cacheKey)\n\n  if (!childCacheNode || !existingChildCacheNode) {\n    // Bailout because the existing cache does not have the path to the leaf node\n    // Will trigger lazy fetch in layout-router because of missing segment\n    return\n  }\n\n  if (childCacheNode === existingChildCacheNode) {\n    childCacheNode = {\n      lazyData: childCacheNode.lazyData,\n      rsc: childCacheNode.rsc,\n      prefetchRsc: childCacheNode.prefetchRsc,\n      head: childCacheNode.head,\n      prefetchHead: childCacheNode.prefetchHead,\n      parallelRoutes: new Map(childCacheNode.parallelRoutes),\n    } as CacheNode\n    childSegmentMap.set(cacheKey, childCacheNode)\n  }\n\n  invalidateCacheBelowFlightSegmentPath(\n    childCacheNode,\n    existingChildCacheNode,\n    getNextFlightSegmentPath(flightSegmentPath)\n  )\n}\n", "import { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  HmrRefreshAction,\n  Mutable,\n} from '../router-reducer-types'\nimport { handleExternalUrl } from './navigate-reducer'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { handleSegmentMismatch } from '../handle-segment-mismatch'\nimport { hasInterceptionRouteInCurrentTree } from './has-interception-route-in-current-tree'\n\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(\n  state: ReadonlyReducerState,\n  action: HmrRefreshAction\n): ReducerState {\n  const { origin } = action\n  const mutable: Mutable = {}\n  const href = state.canonicalUrl\n\n  mutable.preserveCustomHistoryState = false\n\n  const cache: CacheNode = createEmptyCacheNode()\n  // If the current tree was intercepted, the nextUrl should be included in the request.\n  // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n  const includeNextUrl = hasInterceptionRouteInCurrentTree(state.tree)\n\n  // TODO-APP: verify that `href` is not an external url.\n  // Fetch data from the root of the tree.\n  const navigatedAt = Date.now()\n  cache.lazyData = fetchServerResponse(new URL(href, origin), {\n    flightRouterState: [state.tree[0], state.tree[1], state.tree[2], 'refetch'],\n    nextUrl: includeNextUrl ? state.nextUrl : null,\n    isHmrRefresh: true,\n  })\n\n  return cache.lazyData.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride }) => {\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(\n          state,\n          mutable,\n          flightData,\n          state.pushRef.pendingPush\n        )\n      }\n\n      // Remove cache.lazyData as it has been resolved at this point.\n      cache.lazyData = null\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n\n      for (const normalizedFlightData of flightData) {\n        const { tree: treePatch, isRootRender } = normalizedFlightData\n        if (!isRootRender) {\n          // TODO-APP: handle this case better\n          console.log('REFRESH FAILED')\n          return state\n        }\n\n        const newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          [''],\n          currentTree,\n          treePatch,\n          state.canonicalUrl\n        )\n\n        if (newTree === null) {\n          return handleSegmentMismatch(state, action, treePatch)\n        }\n\n        if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n          return handleExternalUrl(\n            state,\n            mutable,\n            href,\n            state.pushRef.pendingPush\n          )\n        }\n\n        const canonicalUrlOverrideHref = canonicalUrlOverride\n          ? createHrefFromUrl(canonicalUrlOverride)\n          : undefined\n\n        if (canonicalUrlOverride) {\n          mutable.canonicalUrl = canonicalUrlOverrideHref\n        }\n        const applied = applyFlightData(\n          navigatedAt,\n          currentCache,\n          cache,\n          normalizedFlightData\n        )\n\n        if (applied) {\n          mutable.cache = cache\n          currentCache = cache\n        }\n\n        mutable.patchedTree = newTree\n        mutable.canonicalUrl = href\n\n        currentTree = newTree\n      }\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n\nfunction hmrRefreshReducerNoop(\n  state: ReadonlyReducerState,\n  _action: HmrRefreshAction\n): ReducerState {\n  return state\n}\n\nexport const hmrRefreshReducer =\n  process.env.NODE_ENV === 'production'\n    ? hmrRefreshReducerNoop\n    : hmrRefreshReducerImpl\n", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].ImageConfigContext;\n\n//# sourceMappingURL=image-config-context.js.map", "import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// @ts-ignore - This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n", "import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport { fillLazyItemsTillLeafWithHead } from './fill-lazy-items-till-leaf-with-head'\nimport { fillCacheWithNewSubTreeData } from './fill-cache-with-new-subtree-data'\nimport type { PrefetchCacheEntry } from './router-reducer-types'\nimport type { NormalizedFlightData } from '../../flight-data-helpers'\n\nexport function applyFlightData(\n  navigatedAt: number,\n  existingCache: CacheNode,\n  cache: CacheNode,\n  flightData: NormalizedFlightData,\n  prefetchEntry?: PrefetchCacheEntry\n): boolean {\n  // The one before last item is the router state tree patch\n  const { tree: treePatch, seedData, head, isRootRender } = flightData\n\n  // Handles case where prefetch only returns the router tree patch without rendered components.\n  if (seedData === null) {\n    return false\n  }\n\n  if (isRootRender) {\n    const rsc = seedData[1]\n    const loading = seedData[3]\n    cache.loading = loading\n    cache.rsc = rsc\n    // This is a PPR-only field. When PPR is enabled, we shouldn't hit\n    // this path during a navigation, but until PPR is fully implemented\n    // yet it's possible the existing node does have a non-null\n    // `prefetchRsc`. As an incremental step, we'll just de-opt to the\n    // old behavior — no PPR value.\n    cache.prefetchRsc = null\n    fillLazyItemsTillLeafWithHead(\n      navigatedAt,\n      cache,\n      existingCache,\n      treePatch,\n      seedData,\n      head,\n      prefetchEntry\n    )\n  } else {\n    // Copy rsc for the root node of the cache.\n    cache.rsc = existingCache.rsc\n    // This is a PPR-only field. Unlike the previous branch, since we're\n    // just cloning the existing cache node, we might as well keep the\n    // PPR value, if it exists.\n    cache.prefetchRsc = existingCache.prefetchRsc\n    cache.parallelRoutes = new Map(existingCache.parallelRoutes)\n    cache.loading = existingCache.loading\n    // Create a copy of the existing cache with the rsc applied.\n    fillCacheWithNewSubTreeData(\n      navigatedAt,\n      cache,\n      existingCache,\n      flightData,\n      prefetchEntry\n    )\n  }\n\n  return true\n}\n", "import type {\n  PrefetchAction,\n  ReducerState,\n  ReadonlyReducerState,\n} from '../router-reducer-types'\nimport { PromiseQueue } from '../../promise-queue'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nexport const prefetchQueue = new PromiseQueue(5)\n\nexport const prefetchReducer = process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ? identityReducerWhenSegmentCacheIsEnabled\n  : prefetchReducerImpl\n\nfunction identityReducerWhenSegmentCacheIsEnabled<T>(state: T): T {\n  // Unlike the old implementation, the Segment Cache doesn't store its data in\n  // the router reducer state.\n  //\n  // This shouldn't be reachable because we wrap the prefetch API in a check,\n  // too, which prevents the action from being dispatched. But it's here for\n  // clarity + code elimination.\n  return state\n}\n\nfunction prefetchReducerImpl(\n  state: ReadonlyReducerState,\n  action: PrefetchAction\n): ReducerState {\n  // let's prune the prefetch cache before we do anything else\n  prunePrefetchCache(state.prefetchCache)\n\n  const { url } = action\n\n  getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    prefetchCache: state.prefetchCache,\n    kind: action.kind,\n    tree: state.tree,\n    allowAliasing: true,\n  })\n\n  return state\n}\n", "import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n", "import type {\n  FlightRouterState,\n  Segment,\n} from '../../../server/app-render/types'\nimport { INTERCEPTION_ROUTE_MARKERS } from '../../../shared/lib/router/utils/interception-routes'\nimport type { Params } from '../../../server/request/params'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../shared/lib/segment'\nimport { matchSegment } from '../match-segments'\n\nconst removeLeadingSlash = (segment: string): string => {\n  return segment[0] === '/' ? segment.slice(1) : segment\n}\n\nconst segmentToPathname = (segment: Segment): string => {\n  if (typeof segment === 'string') {\n    // 'children' is not a valid path -- it's technically a parallel route that corresponds with the current segment's page\n    // if we don't skip it, then the computed pathname might be something like `/children` which doesn't make sense.\n    if (segment === 'children') return ''\n\n    return segment\n  }\n\n  return segment[1]\n}\n\nfunction normalizeSegments(segments: string[]): string {\n  return (\n    segments.reduce((acc, segment) => {\n      segment = removeLeadingSlash(segment)\n      if (segment === '' || isGroupSegment(segment)) {\n        return acc\n      }\n\n      return `${acc}/${segment}`\n    }, '') || '/'\n  )\n}\n\nexport function extractPathFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): string | undefined {\n  const segment = Array.isArray(flightRouterState[0])\n    ? flightRouterState[0][1]\n    : flightRouterState[0]\n\n  if (\n    segment === DEFAULT_SEGMENT_KEY ||\n    INTERCEPTION_ROUTE_MARKERS.some((m) => segment.startsWith(m))\n  )\n    return undefined\n\n  if (segment.startsWith(PAGE_SEGMENT_KEY)) return ''\n\n  const segments = [segmentToPathname(segment)]\n  const parallelRoutes = flightRouterState[1] ?? {}\n\n  const childrenPath = parallelRoutes.children\n    ? extractPathFromFlightRouterState(parallelRoutes.children)\n    : undefined\n\n  if (childrenPath !== undefined) {\n    segments.push(childrenPath)\n  } else {\n    for (const [key, value] of Object.entries(parallelRoutes)) {\n      if (key === 'children') continue\n\n      const childPath = extractPathFromFlightRouterState(value)\n\n      if (childPath !== undefined) {\n        segments.push(childPath)\n      }\n    }\n  }\n\n  return normalizeSegments(segments)\n}\n\nfunction computeChangedPathImpl(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const [segmentA, parallelRoutesA] = treeA\n  const [segmentB, parallelRoutesB] = treeB\n\n  const normalizedSegmentA = segmentToPathname(segmentA)\n  const normalizedSegmentB = segmentToPathname(segmentB)\n\n  if (\n    INTERCEPTION_ROUTE_MARKERS.some(\n      (m) =>\n        normalizedSegmentA.startsWith(m) || normalizedSegmentB.startsWith(m)\n    )\n  ) {\n    return ''\n  }\n\n  if (!matchSegment(segmentA, segmentB)) {\n    // once we find where the tree changed, we compute the rest of the path by traversing the tree\n    return extractPathFromFlightRouterState(treeB) ?? ''\n  }\n\n  for (const parallelRouterKey in parallelRoutesA) {\n    if (parallelRoutesB[parallelRouterKey]) {\n      const changedPath = computeChangedPathImpl(\n        parallelRoutesA[parallelRouterKey],\n        parallelRoutesB[parallelRouterKey]\n      )\n      if (changedPath !== null) {\n        return `${segmentToPathname(segmentB)}/${changedPath}`\n      }\n    }\n  }\n\n  return null\n}\n\nexport function computeChangedPath(\n  treeA: FlightRouterState,\n  treeB: FlightRouterState\n): string | null {\n  const changedPath = computeChangedPathImpl(treeA, treeB)\n\n  if (changedPath == null || changedPath === '/') {\n    return changedPath\n  }\n\n  // lightweight normalization to remove route groups\n  return normalizeSegments(changedPath.split('/'))\n}\n\n/**\n * Recursively extracts dynamic parameters from FlightRouterState.\n */\nexport function getSelectedParams(\n  currentTree: FlightRouterState,\n  params: Params = {}\n): Params {\n  const parallelRoutes = currentTree[1]\n\n  for (const parallelRoute of Object.values(parallelRoutes)) {\n    const segment = parallelRoute[0]\n    const isDynamicParameter = Array.isArray(segment)\n    const segmentValue = isDynamicParameter ? segment[1] : segment\n    if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) continue\n\n    // Ensure catchAll and optional catchall are turned into an array\n    const isCatchAll =\n      isDynamicParameter && (segment[2] === 'c' || segment[2] === 'oc')\n\n    if (isCatchAll) {\n      params[segment[0]] = segment[1].split('/')\n    } else if (isDynamicParameter) {\n      params[segment[0]] = segment[1]\n    }\n\n    params = getSelectedParams(parallelRoute, params)\n  }\n\n  return params\n}\n", "import type { FlightRouterState } from '../../../server/app-render/types'\nimport type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterState } from './router-reducer-types'\nimport { applyFlightData } from './apply-flight-data'\nimport { fetchServerResponse } from './fetch-server-response'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\ninterface RefreshInactiveParallelSegments {\n  navigatedAt: number\n  state: AppRouterState\n  updatedTree: FlightRouterState\n  updatedCache: CacheNode\n  includeNextUrl: boolean\n  canonicalUrl: string\n}\n\n/**\n * Refreshes inactive segments that are still in the current FlightRouterState.\n * A segment is considered \"inactive\" when the server response indicates it didn't match to a page component.\n * This happens during a soft-navigation, where the server will want to patch in the segment\n * with the \"default\" component, but we explicitly ignore the server in this case\n * and keep the existing state for that segment. New data for inactive segments are inherently\n * not part of the server response when we patch the tree, because they were associated with a response\n * from an earlier navigation/request. For each segment, once it becomes \"active\", we encode the URL that provided\n * the data for it. This function traverses parallel routes looking for these markers so that it can re-fetch\n * and patch the new data into the tree.\n */\nexport async function refreshInactiveParallelSegments(\n  options: RefreshInactiveParallelSegments\n) {\n  const fetchedSegments = new Set<string>()\n  await refreshInactiveParallelSegmentsImpl({\n    ...options,\n    rootTree: options.updatedTree,\n    fetchedSegments,\n  })\n}\n\nasync function refreshInactiveParallelSegmentsImpl({\n  navigatedAt,\n  state,\n  updatedTree,\n  updatedCache,\n  includeNextUrl,\n  fetchedSegments,\n  rootTree = updatedTree,\n  canonicalUrl,\n}: RefreshInactiveParallelSegments & {\n  fetchedSegments: Set<string>\n  rootTree: FlightRouterState\n}) {\n  const [, parallelRoutes, refetchPath, refetchMarker] = updatedTree\n  const fetchPromises = []\n\n  if (\n    refetchPath &&\n    refetchPath !== canonicalUrl &&\n    refetchMarker === 'refresh' &&\n    // it's possible for the tree to contain multiple segments that contain data at the same URL\n    // we keep track of them so we can dedupe the requests\n    !fetchedSegments.has(refetchPath)\n  ) {\n    fetchedSegments.add(refetchPath) // Mark this URL as fetched\n\n    // Eagerly kick off the fetch for the refetch path & the parallel routes. This should be fine to do as they each operate\n    // independently on their own cache nodes, and `applyFlightData` will copy anything it doesn't care about from the existing cache.\n    const fetchPromise = fetchServerResponse(\n      new URL(refetchPath, location.origin),\n      {\n        // refetch from the root of the updated tree, otherwise it will be scoped to the current segment\n        // and might not contain the data we need to patch in interception route data (such as dynamic params from a previous segment)\n        flightRouterState: [rootTree[0], rootTree[1], rootTree[2], 'refetch'],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n      }\n    ).then(({ flightData }) => {\n      if (typeof flightData !== 'string') {\n        for (const flightDataPath of flightData) {\n          // we only pass the new cache as this function is called after clearing the router cache\n          // and filling in the new page data from the server. Meaning the existing cache is actually the cache that's\n          // just been created & has been written to, but hasn't been \"committed\" yet.\n          applyFlightData(\n            navigatedAt,\n            updatedCache,\n            updatedCache,\n            flightDataPath\n          )\n        }\n      } else {\n        // When flightData is a string, it suggests that the server response should have triggered an MPA navigation\n        // I'm not 100% sure of this decision, but it seems unlikely that we'd want to introduce a redirect side effect\n        // when refreshing on-screen data, so handling this has been ommitted.\n      }\n    })\n\n    fetchPromises.push(fetchPromise)\n  }\n\n  for (const key in parallelRoutes) {\n    const parallelFetchPromise = refreshInactiveParallelSegmentsImpl({\n      navigatedAt,\n      state,\n      updatedTree: parallelRoutes[key],\n      updatedCache,\n      includeNextUrl,\n      fetchedSegments,\n      rootTree,\n      canonicalUrl,\n    })\n\n    fetchPromises.push(parallelFetchPromise)\n  }\n\n  await Promise.all(fetchPromises)\n}\n\n/**\n * Walks the current parallel segments to determine if they are \"active\".\n * An active parallel route will have a `__PAGE__` segment in the FlightRouterState.\n * As opposed to a `__DEFAULT__` segment, which means there was no match for that parallel route.\n * We add a special marker here so that we know how to refresh its data when the router is revalidated.\n */\nexport function addRefreshMarkerToActiveParallelSegments(\n  tree: FlightRouterState,\n  path: string\n) {\n  const [segment, parallelRoutes, , refetchMarker] = tree\n  // a page segment might also contain concatenated search params, so we do a partial match on the key\n  if (segment.includes(PAGE_SEGMENT_KEY) && refetchMarker !== 'refresh') {\n    tree[2] = path\n    tree[3] = 'refresh'\n  }\n\n  for (const key in parallelRoutes) {\n    addRefreshMarkerToActiveParallelSegments(parallelRoutes[key], path)\n  }\n}\n", "import { createHrefFromUrl } from '../create-href-from-url'\nimport type {\n  ReadonlyReducerState,\n  ReducerState,\n  RestoreAction,\n} from '../router-reducer-types'\nimport { extractPathFromFlightRouterState } from '../compute-changed-path'\nimport { updateCacheNodeOnPopstateRestoration } from '../ppr-navigations'\n\nexport function restoreReducer(\n  state: ReadonlyReducerState,\n  action: RestoreAction\n): ReducerState {\n  const { url, tree } = action\n  const href = createHrefFromUrl(url)\n  // This action is used to restore the router state from the history state.\n  // However, it's possible that the history state no longer contains the `FlightRouterState`.\n  // We will copy over the internal state on pushState/replaceState events, but if a history entry\n  // occurred before hydration, or if the user navigated to a hash using a regular anchor link,\n  // the history state will not contain the `FlightRouterState`.\n  // In this case, we'll continue to use the existing tree so the router doesn't get into an invalid state.\n  const treeToRestore = tree || state.tree\n\n  const oldCache = state.cache\n  const newCache = process.env.__NEXT_PPR\n    ? // When PPR is enabled, we update the cache to drop the prefetch\n      // data for any segment whose dynamic data was already received. This\n      // prevents an unnecessary flash back to PPR state during a\n      // back/forward navigation.\n      updateCacheNodeOnPopstateRestoration(oldCache, treeToRestore)\n    : oldCache\n\n  return {\n    // Set canonical url\n    canonicalUrl: href,\n    pushRef: {\n      pendingPush: false,\n      mpaNavigation: false,\n      // Ensures that the custom history state that was set is preserved when applying this update.\n      preserveCustomHistoryState: true,\n    },\n    focusAndScrollRef: state.focusAndScrollRef,\n    cache: newCache,\n    prefetchCache: state.prefetchCache,\n    // Restore provided tree\n    tree: treeToRestore,\n    nextUrl: extractPathFromFlightRouterState(treeToRestore) ?? url.pathname,\n  }\n}\n", "import type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../../server/app-render/types'\nimport { fetchServerResponse } from '../fetch-server-response'\nimport { createHrefFromUrl } from '../create-href-from-url'\nimport { invalidateCacheBelowFlightSegmentPath } from '../invalidate-cache-below-flight-segmentpath'\nimport { applyRouterStatePatchToTree } from '../apply-router-state-patch-to-tree'\nimport { shouldHardNavigate } from '../should-hard-navigate'\nimport { isNavigatingToNewRootLayout } from '../is-navigating-to-new-root-layout'\nimport {\n  PrefetchCacheEntryStatus,\n  type Mutable,\n  type NavigateAction,\n  type ReadonlyReducerState,\n  type ReducerState,\n} from '../router-reducer-types'\nimport { handleMutable } from '../handle-mutable'\nimport { applyFlightData } from '../apply-flight-data'\nimport { prefetchQueue } from './prefetch-reducer'\nimport { createEmptyCacheNode } from '../../app-router'\nimport { DEFAULT_SEGMENT_KEY } from '../../../../shared/lib/segment'\nimport { listenForDynamicRequest, startPPRNavigation } from '../ppr-navigations'\nimport {\n  getOrCreatePrefetchCacheEntry,\n  prunePrefetchCache,\n} from '../prefetch-cache-utils'\nimport { clearCacheNodeDataForSegmentPath } from '../clear-cache-node-data-for-segment-path'\nimport { handleAliasedPrefetchEntry } from '../aliased-prefetch-navigations'\nimport {\n  navigate as navigateUsingSegmentCache,\n  NavigationResultTag,\n  type NavigationResult,\n} from '../../segment-cache'\n\nexport function handleExternalUrl(\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  url: string,\n  pendingPush: boolean\n) {\n  mutable.mpaNavigation = true\n  mutable.canonicalUrl = url\n  mutable.pendingPush = pendingPush\n  mutable.scrollableSegments = undefined\n\n  return handleMutable(state, mutable)\n}\n\nfunction generateSegmentsFromPatch(\n  flightRouterPatch: FlightRouterState\n): FlightSegmentPath[] {\n  const segments: FlightSegmentPath[] = []\n  const [segment, parallelRoutes] = flightRouterPatch\n\n  if (Object.keys(parallelRoutes).length === 0) {\n    return [[segment]]\n  }\n\n  for (const [parallelRouteKey, parallelRoute] of Object.entries(\n    parallelRoutes\n  )) {\n    for (const childSegment of generateSegmentsFromPatch(parallelRoute)) {\n      // If the segment is empty, it means we are at the root of the tree\n      if (segment === '') {\n        segments.push([parallelRouteKey, ...childSegment])\n      } else {\n        segments.push([segment, parallelRouteKey, ...childSegment])\n      }\n    }\n  }\n\n  return segments\n}\n\nfunction triggerLazyFetchForLeafSegments(\n  newCache: CacheNode,\n  currentCache: CacheNode,\n  flightSegmentPath: FlightSegmentPath,\n  treePatch: FlightRouterState\n) {\n  let appliedPatch = false\n\n  newCache.rsc = currentCache.rsc\n  newCache.prefetchRsc = currentCache.prefetchRsc\n  newCache.loading = currentCache.loading\n  newCache.parallelRoutes = new Map(currentCache.parallelRoutes)\n\n  const segmentPathsToFill = generateSegmentsFromPatch(treePatch).map(\n    (segment) => [...flightSegmentPath, ...segment]\n  )\n\n  for (const segmentPaths of segmentPathsToFill) {\n    clearCacheNodeDataForSegmentPath(newCache, currentCache, segmentPaths)\n\n    appliedPatch = true\n  }\n\n  return appliedPatch\n}\n\nfunction handleNavigationResult(\n  url: URL,\n  state: ReadonlyReducerState,\n  mutable: Mutable,\n  pendingPush: boolean,\n  result: NavigationResult\n): ReducerState {\n  switch (result.tag) {\n    case NavigationResultTag.MPA: {\n      // Perform an MPA navigation.\n      const newUrl = result.data\n      return handleExternalUrl(state, mutable, newUrl, pendingPush)\n    }\n    case NavigationResultTag.NoOp: {\n      // The server responded with no change to the current page. However, if\n      // the URL changed, we still need to update that.\n      const newCanonicalUrl = result.data.canonicalUrl\n      mutable.canonicalUrl = newCanonicalUrl\n\n      // Check if the only thing that changed was the hash fragment.\n      const oldUrl = new URL(state.canonicalUrl, url)\n      const onlyHashChange =\n        // We don't need to compare the origins, because client-driven\n        // navigations are always same-origin.\n        url.pathname === oldUrl.pathname &&\n        url.search === oldUrl.search &&\n        url.hash !== oldUrl.hash\n      if (onlyHashChange) {\n        // The only updated part of the URL is the hash.\n        mutable.onlyHashChange = true\n        mutable.shouldScroll = result.data.shouldScroll\n        mutable.hashFragment = url.hash\n        // Setting this to an empty array triggers a scroll for all new and\n        // updated segments. See `ScrollAndFocusHandler` for more details.\n        mutable.scrollableSegments = []\n      }\n\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Success: {\n      // Received a new result.\n      mutable.cache = result.data.cacheNode\n      mutable.patchedTree = result.data.flightRouterState\n      mutable.canonicalUrl = result.data.canonicalUrl\n      mutable.scrollableSegments = result.data.scrollableSegments\n      mutable.shouldScroll = result.data.shouldScroll\n      mutable.hashFragment = result.data.hash\n      return handleMutable(state, mutable)\n    }\n    case NavigationResultTag.Async: {\n      return result.data.then(\n        (asyncResult) =>\n          handleNavigationResult(url, state, mutable, pendingPush, asyncResult),\n        // If the navigation failed, return the current state.\n        // TODO: This matches the current behavior but we need to do something\n        // better here if the network fails.\n        () => {\n          return state\n        }\n      )\n    }\n    default: {\n      result satisfies never\n      return state\n    }\n  }\n}\n\nexport function navigateReducer(\n  state: ReadonlyReducerState,\n  action: NavigateAction\n): ReducerState {\n  const { url, isExternalUrl, navigateType, shouldScroll, allowAliasing } =\n    action\n  const mutable: Mutable = {}\n  const { hash } = url\n  const href = createHrefFromUrl(url)\n  const pendingPush = navigateType === 'push'\n  // we want to prune the prefetch cache on every navigation to avoid it growing too large\n  prunePrefetchCache(state.prefetchCache)\n\n  mutable.preserveCustomHistoryState = false\n  mutable.pendingPush = pendingPush\n\n  if (isExternalUrl) {\n    return handleExternalUrl(state, mutable, url.toString(), pendingPush)\n  }\n\n  // Handles case where `<meta http-equiv=\"refresh\">` tag is present,\n  // which will trigger an MPA navigation.\n  if (document.getElementById('__next-page-redirect')) {\n    return handleExternalUrl(state, mutable, href, pendingPush)\n  }\n\n  if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // (Very Early Experimental Feature) Segment Cache\n    //\n    // Bypass the normal prefetch cache and use the new per-segment cache\n    // implementation instead. This is only supported if PPR is enabled, too.\n    //\n    // Temporary glue code between the router reducer and the new navigation\n    // implementation. Eventually we'll rewrite the router reducer to a\n    // state machine.\n    const result = navigateUsingSegmentCache(\n      url,\n      state.cache,\n      state.tree,\n      state.nextUrl,\n      shouldScroll\n    )\n    return handleNavigationResult(url, state, mutable, pendingPush, result)\n  }\n\n  const prefetchValues = getOrCreatePrefetchCacheEntry({\n    url,\n    nextUrl: state.nextUrl,\n    tree: state.tree,\n    prefetchCache: state.prefetchCache,\n    allowAliasing,\n  })\n  const { treeAtTimeOfPrefetch, data } = prefetchValues\n\n  prefetchQueue.bump(data)\n\n  return data.then(\n    ({ flightData, canonicalUrl: canonicalUrlOverride, postponed }) => {\n      const navigatedAt = Date.now()\n\n      let isFirstRead = false\n      // we only want to mark this once\n      if (!prefetchValues.lastUsedTime) {\n        // important: we should only mark the cache node as dirty after we unsuspend from the call above\n        prefetchValues.lastUsedTime = navigatedAt\n        isFirstRead = true\n      }\n\n      if (prefetchValues.aliased) {\n        const result = handleAliasedPrefetchEntry(\n          navigatedAt,\n          state,\n          flightData,\n          url,\n          mutable\n        )\n\n        // We didn't return new router state because we didn't apply the aliased entry for some reason.\n        // We'll re-invoke the navigation handler but ensure that we don't attempt to use the aliased entry. This\n        // will create an on-demand prefetch entry.\n        if (result === false) {\n          return navigateReducer(state, { ...action, allowAliasing: false })\n        }\n\n        return result\n      }\n\n      // Handle case when navigating to page in `pages` from `app`\n      if (typeof flightData === 'string') {\n        return handleExternalUrl(state, mutable, flightData, pendingPush)\n      }\n\n      const updatedCanonicalUrl = canonicalUrlOverride\n        ? createHrefFromUrl(canonicalUrlOverride)\n        : href\n\n      const onlyHashChange =\n        !!hash &&\n        state.canonicalUrl.split('#', 1)[0] ===\n          updatedCanonicalUrl.split('#', 1)[0]\n\n      // If only the hash has changed, the server hasn't sent us any new data. We can just update\n      // the mutable properties responsible for URL and scroll handling and return early.\n      if (onlyHashChange) {\n        mutable.onlyHashChange = true\n        mutable.canonicalUrl = updatedCanonicalUrl\n        mutable.shouldScroll = shouldScroll\n        mutable.hashFragment = hash\n        mutable.scrollableSegments = []\n        return handleMutable(state, mutable)\n      }\n\n      let currentTree = state.tree\n      let currentCache = state.cache\n      let scrollableSegments: FlightSegmentPath[] = []\n      for (const normalizedFlightData of flightData) {\n        const {\n          pathToSegment: flightSegmentPath,\n          seedData,\n          head,\n          isHeadPartial,\n          isRootRender,\n        } = normalizedFlightData\n        let treePatch = normalizedFlightData.tree\n\n        // TODO-APP: remove ''\n        const flightSegmentPathWithLeadingEmpty = ['', ...flightSegmentPath]\n\n        // Create new tree based on the flightSegmentPath and router state patch\n        let newTree = applyRouterStatePatchToTree(\n          // TODO-APP: remove ''\n          flightSegmentPathWithLeadingEmpty,\n          currentTree,\n          treePatch,\n          href\n        )\n\n        // If the tree patch can't be applied to the current tree then we use the tree at time of prefetch\n        // TODO-APP: This should instead fill in the missing pieces in `currentTree` with the data from `treeAtTimeOfPrefetch`, then apply the patch.\n        if (newTree === null) {\n          newTree = applyRouterStatePatchToTree(\n            // TODO-APP: remove ''\n            flightSegmentPathWithLeadingEmpty,\n            treeAtTimeOfPrefetch,\n            treePatch,\n            href\n          )\n        }\n\n        if (newTree !== null) {\n          if (\n            // This is just a paranoid check. When a route is PPRed, the server\n            // will send back a static response that's rendered from\n            // the root. If for some reason it doesn't, we fall back to the\n            // non-PPR implementation.\n            // TODO: We should get rid of the else branch and do all navigations\n            // via startPPRNavigation. The current structure is just\n            // an incremental step.\n            seedData &&\n            isRootRender &&\n            postponed\n          ) {\n            const task = startPPRNavigation(\n              navigatedAt,\n              currentCache,\n              currentTree,\n              treePatch,\n              seedData,\n              head,\n              isHeadPartial,\n              false,\n              scrollableSegments\n            )\n\n            if (task !== null) {\n              if (task.route === null) {\n                // Detected a change to the root layout. Perform an full-\n                // page navigation.\n                return handleExternalUrl(state, mutable, href, pendingPush)\n              }\n              // Use the tree computed by startPPRNavigation instead\n              // of the one computed by applyRouterStatePatchToTree.\n              // TODO: We should remove applyRouterStatePatchToTree\n              // from the PPR path entirely.\n              const patchedRouterState: FlightRouterState = task.route\n              newTree = patchedRouterState\n\n              const newCache = task.node\n              if (newCache !== null) {\n                // We've created a new Cache Node tree that contains a prefetched\n                // version of the next page. This can be rendered instantly.\n                mutable.cache = newCache\n              }\n              const dynamicRequestTree = task.dynamicRequestTree\n              if (dynamicRequestTree !== null) {\n                // The prefetched tree has dynamic holes in it. We initiate a\n                // dynamic request to fill them in.\n                //\n                // Do not block on the result. We'll immediately render the Cache\n                // Node tree and suspend on the dynamic parts. When the request\n                // comes in, we'll fill in missing data and ping React to\n                // re-render. Unlike the lazy fetching model in the non-PPR\n                // implementation, this is modeled as a single React update +\n                // streaming, rather than multiple top-level updates. (However,\n                // even in the new model, we'll still need to sometimes update the\n                // root multiple times per navigation, like if the server sends us\n                // a different response than we expected. For now, we revert back\n                // to the lazy fetching mechanism in that case.)\n                const dynamicRequest = fetchServerResponse(url, {\n                  flightRouterState: dynamicRequestTree,\n                  nextUrl: state.nextUrl,\n                })\n\n                listenForDynamicRequest(task, dynamicRequest)\n                // We store the dynamic request on the `lazyData` property of the CacheNode\n                // because we're not going to await the dynamic request here. Since we're not blocking\n                // on the dynamic request, `layout-router` will\n                // task.node.lazyData = dynamicRequest\n              } else {\n                // The prefetched tree does not contain dynamic holes — it's\n                // fully static. We can skip the dynamic request.\n              }\n            } else {\n              // Nothing changed, so reuse the old cache.\n              // TODO: What if the head changed but not any of the segment data?\n              // Is that possible? If so, we should clone the whole tree and\n              // update the head.\n              newTree = treePatch\n            }\n          } else {\n            // The static response does not include any dynamic holes, so\n            // there's no need to do a second request.\n            // TODO: As an incremental step this just reverts back to the\n            // non-PPR implementation. We can simplify this branch further,\n            // given that PPR prefetches are always static and return the whole\n            // tree. Or in the meantime we could factor it out into a\n            // separate function.\n\n            if (isNavigatingToNewRootLayout(currentTree, newTree)) {\n              return handleExternalUrl(state, mutable, href, pendingPush)\n            }\n\n            const cache: CacheNode = createEmptyCacheNode()\n            let applied = false\n\n            if (\n              prefetchValues.status === PrefetchCacheEntryStatus.stale &&\n              !isFirstRead\n            ) {\n              // When we have a stale prefetch entry, we only want to re-use the loading state of the route we're navigating to, to support instant loading navigations\n              // this will trigger a lazy fetch for the actual page data by nulling the `rsc` and `prefetchRsc` values for page data,\n              // while copying over the `loading` for the segment that contains the page data.\n              // We only do this on subsequent reads, as otherwise there'd be no loading data to re-use.\n\n              // We skip this branch if only the hash fragment has changed, as we don't want to trigger a lazy fetch in that case\n              applied = triggerLazyFetchForLeafSegments(\n                cache,\n                currentCache,\n                flightSegmentPath,\n                treePatch\n              )\n              // since we re-used the stale cache's loading state & refreshed the data,\n              // update the `lastUsedTime` so that it can continue to be re-used for the next 30s\n              prefetchValues.lastUsedTime = navigatedAt\n            } else {\n              applied = applyFlightData(\n                navigatedAt,\n                currentCache,\n                cache,\n                normalizedFlightData,\n                prefetchValues\n              )\n            }\n\n            const hardNavigate = shouldHardNavigate(\n              // TODO-APP: remove ''\n              flightSegmentPathWithLeadingEmpty,\n              currentTree\n            )\n\n            if (hardNavigate) {\n              // Copy rsc for the root node of the cache.\n              cache.rsc = currentCache.rsc\n              cache.prefetchRsc = currentCache.prefetchRsc\n\n              invalidateCacheBelowFlightSegmentPath(\n                cache,\n                currentCache,\n                flightSegmentPath\n              )\n              // Ensure the existing cache value is used when the cache was not invalidated.\n              mutable.cache = cache\n            } else if (applied) {\n              mutable.cache = cache\n              // If we applied the cache, we update the \"current cache\" value so any other\n              // segments in the FlightDataPath will be able to reference the updated cache.\n              currentCache = cache\n            }\n\n            for (const subSegment of generateSegmentsFromPatch(treePatch)) {\n              const scrollableSegmentPath = [\n                ...flightSegmentPath,\n                ...subSegment,\n              ]\n              // Filter out the __DEFAULT__ paths as they shouldn't be scrolled to in this case.\n              if (\n                scrollableSegmentPath[scrollableSegmentPath.length - 1] !==\n                DEFAULT_SEGMENT_KEY\n              ) {\n                scrollableSegments.push(scrollableSegmentPath)\n              }\n            }\n          }\n\n          currentTree = newTree\n        }\n      }\n\n      mutable.patchedTree = currentTree\n      mutable.canonicalUrl = updatedCanonicalUrl\n      mutable.scrollableSegments = scrollableSegments\n      mutable.hashFragment = hash\n      mutable.shouldScroll = shouldScroll\n\n      return handleMutable(state, mutable)\n    },\n    () => state\n  )\n}\n", "import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["addSearchParamsToPageSegments", "handleAliasedPrefetchEntry", "navigatedAt", "state", "flightData", "url", "mutable", "applied", "currentTree", "tree", "currentCache", "cache", "href", "createHrefFromUrl", "normalizedFlightData", "hasLoadingComponentInSeedData", "seedData", "parallelRoutes", "key", "treePatch", "Object", "fromEntries", "searchParams", "isRootRender", "pathToSegment", "flightSegmentPathWithLeadingEmpty", "newTree", "applyRouterStatePatchToTree", "newCache", "createEmptyCacheNode", "rsc", "loading", "fillNewTreeWithOnlyLoadingSegments", "existingCache", "routerState", "cacheNodeSeedData", "isLastSegment", "keys", "length", "newCacheNode", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "parallelSeedData", "undefined", "lazyData", "includes", "PAGE_SEGMENT_KEY", "prefetchRsc", "head", "prefetchHead", "Map", "existingParallelRoutes", "get", "set", "fillCacheWithNewSubTreeDataButOnlyLoading", "patchedTree", "canonicalUrl", "hashFragment", "hash", "handleMutable", "flightRouterState", "segment", "rest", "addSearchParamsIfPageSegment", "updatedParallelRoutes", "parallelRoute", "entries", "useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup", "getImgProps", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "src", "default", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "_state", "c", "config", "blur<PERSON>idth", "blurHeight", "sizes", "unoptimized", "priority", "className", "quality", "width", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "blurDataURL", "fetchPriority", "decoding", "layout", "objectFit", "objectPosition", "lazyBoundary", "lazyRoot", "imgConf", "showAltText", "blurComplete", "defaultLoader", "imageConfigDefault", "allSizes", "deviceSizes", "imageSizes", "sort", "a", "b", "qualities", "loader", "srcSet", "isDefaultLoader", "customImageLoader", "_", "opts", "obj", "layoutStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutSizes", "staticSrc", "widthInt", "heightInt", "isStaticImageData", "staticImageData", "JSON", "stringify", "ratio", "Math", "round", "isLazy", "startsWith", "dangerouslyAllowSVG", "split", "endsWith", "qualityInt", "imgStyle", "assign", "position", "left", "top", "right", "bottom", "color", "backgroundImage", "getImageBlurSvg", "placeholder<PERSON><PERSON><PERSON>", "backgroundSize", "backgroundPosition", "backgroundRepeat", "imgAttributes", "generateImgAttrs", "widths", "kind", "getWidths", "viewportWidthRe", "percentSizes", "match", "exec", "smallestRatio", "min", "filter", "s", "Set", "map", "find", "p", "w", "last", "i", "join", "props", "meta", "Image", "configEnv", "process", "handleLoading", "img", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "sizesInput", "decode", "Promise", "resolve", "catch", "then", "parentElement", "event", "Event", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "getDynamicProps", "use", "Boolean", "fetchpriority", "globalThis", "__NEXT_IMAGE_IMPORTED", "ImageElement", "forwardRef", "forwardedRef", "setShowAltText", "onError", "ownRef", "complete", "ref", "data-nimg", "ImagePreload", "isAppRouter", "as", "imageSrcSet", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "Head", "link", "rel", "pagesRouter", "useContext", "RouterContext", "configContext", "ImageConfigContext", "useMemo", "useEffect", "useState", "imgMeta", "addPathPrefix", "path", "prefix", "pathname", "query", "parsePath", "serverPatchReducer", "action", "serverResponse", "canonicalUrlOverride", "preserveCustomHistoryState", "handleExternalUrl", "pushRef", "pendingPush", "segmentPath", "flightSegmentPath", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "applyFlightData", "extractInfoFromServerReferenceId", "id", "infoByte", "slice", "argMask", "usedArgs", "Array", "index", "bit", "bitPosition", "type", "typeBit", "hasRestArgs", "restArgs", "omit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args", "info", "filteredArgs", "LinkComponent", "useLinkStatus", "formatStringOrUrl", "urlObjOrString", "formatUrl", "children", "child", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "linkInstanceRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "replace", "shallow", "scroll", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "onNavigate", "unstable_dynamicOnHover", "restProps", "router", "React", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "resolvedHref", "Children", "only", "childRef", "observeLinkVisibilityOnMount", "mountLinkInstance", "element", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "childProps", "mergedRef", "e", "defaultPrevented", "linkClicked", "nodeName", "isAnchorNodeName", "toUpperCase", "isModifiedEvent", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "which", "hasAttribute", "isLocalURL", "location", "startTransition", "navigate", "dispatchNavigateAction", "onNavigationIntent", "env", "NODE_ENV", "upgradeToDynamicPrefetch", "isAbsoluteUrl", "addBasePath", "cloneElement", "LinkStatusContext", "Provider", "createContext", "fillLazyItemsTillLeafWithHead", "prefetchEntry", "existingParallelRoutesCacheNode", "hasReusablePrefetch", "status", "PrefetchCacheEntryStatus", "reusable", "parallelRouteCacheNode", "existingCacheNode", "seedNode", "assignLocation", "urlBase", "origin", "URL", "fillCacheWithNewSubTreeData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillLazyItems", "parallelRouteKey", "isLastEntry", "existingChildSegmentMap", "childSegmentMap", "existingChildCacheNode", "childCacheNode", "incomingSegment", "invalidateCacheByRouterState", "DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "result", "used", "ABSOLUTE_URL_REGEX", "protocol", "hostname", "port", "window", "substring", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "App", "ctx", "getInitialProps", "performance", "every", "method", "Error", "constructor", "page", "code", "message", "error", "stack", "isNotUndefined", "shouldScroll", "nextUrl", "changedPath", "computeChangedPath", "mpaNavigation", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "decodeURIComponent", "segmentPaths", "prefetchCache", "handleSegmentMismatch", "removeBasePath", "required", "normalizePathTrailingSlash", "basePath", "createPrefetchURL", "AppRouter", "isExternalURL", "globalMutable", "isBot", "navigator", "userAgent", "HistoryUpdater", "appRouterState", "useInsertionEffect", "historyState", "history", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pushState", "replaceState", "copyNextJsInternalHistoryState", "data", "currentState", "headCacheNode", "resolvedPrefetchRsc", "useDeferredValue", "Router", "actionQueue", "assetPrefix", "globalError", "useActionQueue", "has<PERSON>ase<PERSON><PERSON>", "handlePageShow", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchAppRouterAction", "ACTION_RESTORE", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "reason", "isRedirectError", "getURLFromRedirectError", "redirectType", "getRedirectTypeFromError", "RedirectType", "push", "publicAppRouterInstance", "unresolvedThenable", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "dispatchTraverseAction", "matchingHead", "findHeadInCache", "pathParams", "getSelectedParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "RedirectBoundary", "AppRouterAnnouncer", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "errorStyles", "RuntimeStyles", "PathParamsContext", "PathnameContext", "SearchParamsContext", "GlobalLayoutRouterContext", "LayoutRouterContext", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "useNavFailureHandler", "DefaultGlobalError", "runtimeStyles", "runtimeStyleChanged", "forceUpdate", "renderedStylesSize", "size", "changed", "add", "delete", "precedence", "dplId", "_N_E_STYLE_LOAD", "len", "cb", "hashIndex", "indexOf", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "pathHasPrefix", "HTML_LIMITED_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "getBotType", "HEADLESS_BROWSER_BOT_UA_RE", "source", "isHtmlLimitedBotUA", "isDomBotUA", "serverActionReducer", "createFromFetch", "createTemporaryReferenceSet", "encodeReply", "require", "fetchServerAction", "revalidatedParts", "actionId", "actionArgs", "temporaryReferences", "body", "fetch", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "encodeURIComponent", "NEXT_URL", "redirectHeader", "_redirectType", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_IS_PRERENDER_HEADER", "revalidatedHeader", "parse", "paths", "tag", "cookie", "redirectLocation", "contentType", "response", "callServer", "findSourceMapURL", "actionFlightData", "normalizeFlightData", "f", "actionResult", "text", "reject", "hasInterceptionRouteInCurrentTree", "Date", "now", "redirectHref", "actionRevalidated", "console", "log", "refreshInactiveParallelSegments", "updatedTree", "updatedCache", "includeNextUrl", "createSeededPrefetchCacheEntry", "couldBeIntercepted", "prerendered", "postponed", "staleTime", "getRedirectError", "findHeadInCacheImpl", "keyPrefix", "isLastItem", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "cacheNode", "item", "NavigationResultTag", "PrefetchPriority", "cancelPrefetchTask", "createCacheKey", "getCurrentCacheVersion", "reschedulePrefetchTask", "revalidateEntireCache", "schedulePrefetchTask", "notEnabled", "VALID_LOADERS", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "remotePatterns", "refreshReducer", "fetchServerResponse", "svgWidth", "svgHeight", "viewBox", "std", "preserveAspectRatio", "ANNOUNCER_TYPE", "portalNode", "setPortalNode", "getAnnouncerNode", "announcer", "existingAnnouncer", "document", "getElementsByName", "shadowRoot", "childNodes", "container", "createElement", "cssText", "ariaLive", "ANNOUNCER_ID", "role", "attachShadow", "mode", "append<PERSON><PERSON><PERSON>", "getElementsByTagName", "isConnected", "<PERSON><PERSON><PERSON><PERSON>", "routeAnnouncement", "setRouteAnnouncement", "previousTitle", "currentTitle", "title", "pageHeader", "querySelector", "innerText", "textContent", "createPortal", "shouldHardNavigate", "currentSegment", "matchSegment", "getNextFlightSegmentPath", "isArray", "DYNAMIC_STALETIME_MS", "STATIC_STALETIME_MS", "getOrCreatePrefetchCacheEntry", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "createPrefetchCacheKeyImpl", "includeSearchParams", "pathnameFromUrl", "search", "INTERCEPTION_CACHE_KEY_MARKER", "createPrefetchCacheKey", "allowAliasing", "existingCacheEntry", "getExistingCacheEntry", "maybeNextUrl", "TEMPORARY", "cacheKeyWithParams", "cacheKeyWithoutParams", "cacheKeyToUse", "existingEntry", "isAliased", "aliased", "entryWithoutParams", "cacheEntry", "values", "getPrefetchEntryCacheStatus", "isFullPrefetch", "prefetchResponse", "some", "createLazyPrefetchEntry", "prefetchCache<PERSON>ey", "treeAtTimeOfPrefetch", "prefetchTime", "lastUsedTime", "fresh", "prefetchQueue", "enqueue", "prefetchKind", "newCache<PERSON>ey", "prefixExistingPrefetchCacheEntry", "existingCacheKey", "prefetchCacheEntry", "expired", "stale", "reducer", "serverReducer", "_action", "clientReducer", "removeTrailingSlash", "parallelRoutePatch", "refetch", "isRootLayout", "applyPatch", "addRefreshMarkerToActiveParallelSegments", "lastSegment", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "DEFAULT_SEGMENT_KEY", "newParallelRoutes", "createMutableActionQueue", "getCurrentAppRouterState", "runRemainingActions", "setState", "pending", "next", "runAction", "needsRefresh", "dispatch", "ACTION_REFRESH", "prevState", "payload", "handleResult", "nextState", "discarded", "isThenable", "err", "initialState", "<PERSON><PERSON><PERSON><PERSON>", "dispatchAction", "resolvers", "deferred<PERSON><PERSON><PERSON>", "newAction", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "onRouterTransitionStart", "globalActionQueue", "getProfilingHookForOnNavigationStart", "navigateType", "setLinkForCurrentNavigation", "isExternalUrl", "locationSearch", "back", "forward", "options", "getAppRouterActionQueue", "prefetchReducer", "ACTION_PREFETCH", "refresh", "hmrRefresh", "abortTask", "listenForDynamicRequest", "startPPRNavigation", "updateCacheNodeOnPopstateRestoration", "oldCacheNode", "routerStateChildren", "oldParallelRoutes", "routerStateChild", "segmentChild", "segmentKeyChild", "oldSegmentMapChild", "oldCacheNodeChild", "newCacheNodeChild", "newSegmentMapChild", "shouldUsePrefetch", "isDeferredRsc", "MPA_NAVIGATION_TASK", "route", "node", "dynamicRequestTree", "oldRouterState", "newRouterState", "prefetchData", "isPrefetchHeadPartial", "isSamePageNavigation", "scrollableSegmentsResult", "updateCacheNodeOnNavigation", "didFindRootLayout", "oldRouterStateChildren", "newRouterStateChildren", "prefetchDataChildren", "prefetchParallelRoutes", "patchedRouterStateChildren", "task<PERSON><PERSON><PERSON><PERSON>", "needsDynamicRequest", "dynamicRequestTreeChildren", "task<PERSON><PERSON><PERSON>", "newRouterStateChild", "oldRouterStateChild", "prefetchDataChild", "newSegmentChild", "newSegment<PERSON>ath<PERSON><PERSON><PERSON>", "concat", "newSegmentKeyChild", "oldSegment<PERSON>hild", "spawnReusedTask", "reusedRouterState", "beginRenderingNewRouteTree", "taskChildRoute", "dynamicRequestTreeChild", "patchRouterStateWithNewChildren", "possiblyPartialPrefetchHead", "createCacheNodeOnNavigation", "cacheNodeNavigatedAt", "isLeafSegment", "spawnPendingTask", "existingCacheNodeChildren", "cacheNodeChildren", "existingSegmentMapChild", "segmentPathChild", "baseRouterState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clone", "newTask", "createPendingCacheNode", "maybePrefetchRsc", "maybePrefetchLoading", "createDeferredRsc", "task", "responsePromise", "serverRouterState", "dynamicData", "dynamicHead", "writeDynamicDataIntoPendingTask", "rootTask", "taskSegment", "finishTaskUsingDynamicDataPayload", "taskNode", "finishPendingCacheNode", "taskState", "serverState", "taskStateChildren", "serverStateChildren", "dataChildren", "taskStateChild", "serverStateChild", "dataChild", "segmentMapChild", "taskSegmentChild", "taskSegmentKeyChild", "cacheNodeChild", "abortPendingCacheNode", "dynamicSegmentData", "serverChildren", "dynamicDataChildren", "serverRouterStateChild", "dynamicDataChild", "DEFERRED", "Symbol", "pendingRsc", "rej", "fulfilledRsc", "rejectedRsc", "PENDING_LINK_STATUS", "mountFormInstance", "onLinkVisibilityChanged", "pingVisibleLinks", "linkForMostRecentNavigation", "prefetchable", "WeakMap", "prefetchableAndVisible", "observer", "IntersectionObserver", "handleIntersect", "entry", "isVisible", "intersectionRatio", "rootMargin", "observeVisibility", "instance", "existingInstance", "observe", "coercePrefetchableUrl", "reportErrorFn", "reportError", "prefetchURL", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "prefetchHref", "unobserve", "rescheduleLinkPrefetch", "unstable_upgradeToDynamicPrefetch", "existingPrefetchTask", "currentCacheVersion", "Intent", "<PERSON><PERSON><PERSON>", "scheduleSegmentPrefetchTask", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "host", "String", "querystring", "urlQueryToSearchParams", "slashes", "errorOnce", "nextTree", "currentTreeSegment", "nextTreeSegment", "currentTreeChild", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clearCacheNodeDataForSegmentPath", "PromiseQueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "bump", "findIndex", "bumpedItem", "splice", "unshift", "maxConcurrency", "forced", "shift", "q", "reduce", "prev", "cur", "abs", "DEFAULT_Q", "__next_img_default", "invalidateCacheBelowFlightSegmentPath", "hmrRefreshReducer", "hmrRefreshReducerNoop", "hmrRefreshReducerImpl", "getImageProps", "imgProps", "prefetchReducerImpl", "identityReducerWhenSegmentCacheIsEnabled", "searchParamsToUrlQuery", "existing", "stringifyUrlQueryParam", "param", "isNaN", "URLSearchParams", "append", "searchParamsList", "extractPathFromFlightRouterState", "params", "isDynamicParameter", "segmentValue", "removeLeadingSlash", "segmentToPathname", "normalizeSegments", "segments", "acc", "isGroupSegment", "INTERCEPTION_ROUTE_MARKERS", "m", "<PERSON><PERSON><PERSON>", "child<PERSON><PERSON>", "treeA", "treeB", "computeChangedPathImpl", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "parallel<PERSON><PERSON>er<PERSON>ey", "refetch<PERSON><PERSON><PERSON>", "fetchedSegments", "refreshInactiveParallelSegmentsImpl", "rootTree", "refetch<PERSON>ath", "fetchPromises", "has", "fetchPromise", "flightDataPath", "parallelFetchPromise", "all", "restoreReducer", "treeToRestore", "<PERSON><PERSON><PERSON>", "navigateReducer", "toString", "getElementById", "prefetchValues", "isFirstRead", "updatedCanonicalUrl", "isHeadPartial", "patchedRouterState", "dynamicRequest", "subSegment", "appliedPatch", "segmentPathsToFill", "generateSegmentsFromPatch", "scrollableSegmentPath", "flightRouterPatch", "childSegment", "locationOrigin", "resolved"], "sourceRoot": ""}