try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="caefa426-d624-4e84-b372-6a0963fb459f",e._sentryDebugIdIdentifier="sentry-dbid-caefa426-d624-4e84-b372-6a0963fb459f")}catch(e){}(()=>{var e={};e.id=2096,e.ids=[2096],e.modules={94:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(55732).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6902:(e,t,a)=>{Promise.resolve().then(a.bind(a,28782))},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>c});var s=a(91754);a(93491);var r=a(82233);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10933:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>o,routeModule:()=>u,tree:()=>l});var s=a(95500),r=a(56947),n=a(26052),i=a(13636),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(t,d);let l={children:["",{children:["dashboard",{children:["admin",{children:["subscriptions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,90964)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\subscriptions\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,28782)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e),async e=>(await Promise.resolve().then(a.bind(a,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e),async e=>(await Promise.resolve().then(a.bind(a,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\subscriptions\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/admin/subscriptions/page",pathname:"/dashboard/admin/subscriptions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22646:(e,t,a)=>{"use strict";a.d(t,{C1:()=>N,bL:()=>v});var s=a(93491),r=a(42014),n=a(10158),i=a(18682),d=a(76322),l=a(78476),o=a(96432),c=a(55462),u=a(90604),p=a(91754),x="Checkbox",[m,h]=(0,n.A)(x),[y,f]=m(x);function g(e){let{__scopeCheckbox:t,checked:a,children:r,defaultChecked:n,disabled:i,form:l,name:o,onCheckedChange:c,required:u,value:m="on",internal_do_not_use_render:h}=e,[f,g]=(0,d.i)({prop:a,defaultProp:n??!1,onChange:c,caller:x}),[b,j]=s.useState(null),[v,C]=s.useState(null),N=s.useRef(!1),w=!b||!!l||!!b.closest("form"),k={checked:f,disabled:i,setChecked:g,control:b,setControl:j,name:o,form:l,value:m,hasConsumerStoppedPropagationRef:N,required:u,defaultChecked:!T(n)&&n,isFormControl:w,bubbleInput:v,setBubbleInput:C};return(0,p.jsx)(y,{scope:t,...k,children:"function"==typeof h?h(k):r})}var b="CheckboxTrigger",j=s.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:a,...n},d)=>{let{control:l,value:o,disabled:c,checked:x,required:m,setControl:h,setChecked:y,hasConsumerStoppedPropagationRef:g,isFormControl:j,bubbleInput:v}=f(b,e),C=(0,r.s)(d,h),N=s.useRef(x);return s.useEffect(()=>{let e=l?.form;if(e){let t=()=>y(N.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,y]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":T(x)?"mixed":x,"aria-required":m,"data-state":A(x),"data-disabled":c?"":void 0,disabled:c,value:o,...n,ref:C,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(a,e=>{y(e=>!!T(e)||!e),v&&j&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});j.displayName=b;var v=s.forwardRef((e,t)=>{let{__scopeCheckbox:a,name:s,checked:r,defaultChecked:n,required:i,disabled:d,value:l,onCheckedChange:o,form:c,...u}=e;return(0,p.jsx)(g,{__scopeCheckbox:a,checked:r,defaultChecked:n,disabled:d,required:i,onCheckedChange:o,name:s,form:c,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(j,{...u,ref:t,__scopeCheckbox:a}),e&&(0,p.jsx)(k,{__scopeCheckbox:a})]})})});v.displayName=x;var C="CheckboxIndicator",N=s.forwardRef((e,t)=>{let{__scopeCheckbox:a,forceMount:s,...r}=e,n=f(C,a);return(0,p.jsx)(c.C,{present:s||T(n.checked)||!0===n.checked,children:(0,p.jsx)(u.sG.span,{"data-state":A(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});N.displayName=C;var w="CheckboxBubbleInput",k=s.forwardRef(({__scopeCheckbox:e,...t},a)=>{let{control:n,hasConsumerStoppedPropagationRef:i,checked:d,defaultChecked:c,required:x,disabled:m,name:h,value:y,form:g,bubbleInput:b,setBubbleInput:j}=f(w,e),v=(0,r.s)(a,j),C=(0,l.Z)(d),N=(0,o.X)(n);s.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(C!==d&&e){let a=new Event("click",{bubbles:t});b.indeterminate=T(d),e.call(b,!T(d)&&d),b.dispatchEvent(a)}},[b,C,d,i]);let k=s.useRef(!T(d)&&d);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??k.current,required:x,disabled:m,name:h,value:y,form:g,...t,tabIndex:-1,ref:v,style:{...t.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function T(e){return"indeterminate"===e}function A(e){return T(e)?"indeterminate":e?"checked":"unchecked"}k.displayName=w},26711:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(55732).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},28354:e=>{"use strict";e.exports=require("util")},28782:(e,t,a)=>{"use strict";let s;a.r(t),a.d(t,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var r=a(63033),n=a(1472),i=a(7688),d=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\layout.tsx","default");let l={...r},o="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;s="function"==typeof d?new Proxy(d,{apply:(e,t,a)=>{let s,r,n;try{let e=o?.getStore();s=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin",componentType:"Layout",sentryTraceHeader:s,baggageHeader:r,headers:n}).apply(t,a)}}):d;let c=void 0,u=void 0,p=void 0,x=s},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31667:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(55732).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},40636:(e,t,a)=>{"use strict";a.d(t,{Table:()=>n,TableBody:()=>d,TableCell:()=>c,TableHead:()=>o,TableHeader:()=>i,TableRow:()=>l});var s=a(91754);a(93491);var r=a(82233);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...t,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function d({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...t,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function o({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},41692:e=>{"use strict";e.exports=require("node:tls")},42761:(e,t,a)=>{Promise.resolve().then(a.bind(a,54086))},44708:e=>{"use strict";e.exports=require("node:https")},46814:(e,t,a)=>{Promise.resolve().then(a.bind(a,49540))},48161:e=>{"use strict";e.exports=require("node:os")},49540:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(91754);function r({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}a(93491),a(76328)},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54086:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(91754),r=a(93491),n=a(9260),i=a(56682),d=a(59672),l=a(80601),o=a(40636),c=a(92681),u=a(69122),p=a(73225),x=a(15624),m=a(83939),h=a(81012),y=a(31667),f=a(86857),g=a(94),b=a(2804),j=a(26711),v=a(79233),C=a(57e3),N=a(49056);function w(){let[e,t]=(0,r.useState)([]),[a,w]=(0,r.useState)({totalInstitutions:0,paidInstitutions:0,unpaidInstitutions:0,overdueInstitutions:0,totalStudents:0,totalTeachers:0}),[k,T]=(0,r.useState)(!0),[A,S]=(0,r.useState)(""),[_,P]=(0,r.useState)("all"),[q,R]=(0,r.useState)("all"),[M,E]=(0,r.useState)([]),[I,D]=(0,r.useState)(!1),[G,H]=(0,r.useState)(void 0),[U,B]=(0,r.useState)(!1);(0,r.useRef)(null);let L=(0,r.useCallback)(async()=>{try{T(!0);let e=new URLSearchParams({search:A,status:_,plan:q,limit:"100"}),a=await fetch(`/api/subscriptions?${e}`),s=await a.json();s.success&&s.data?(t(s.data.institutions),w(s.data.summary)):h.oR.error(s.error||"Failed to fetch subscription data")}catch(e){console.error("Error fetching institutions:",e),h.oR.error("Failed to fetch subscription data")}finally{T(!1)}},[A,_,q]),W=async e=>{if(0===M.length)return void h.oR.error("Please select institutions to update");try{D(!0);let t=await fetch("/api/subscriptions",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({institutionIds:M,paymentStatus:e,paymentDueDate:G?(0,N.GP)(G,"yyyy-MM-dd"):null})}),a=await t.json();a.success?(h.oR.success(`Successfully updated ${M.length} institution(s)`),E([]),H(void 0),L()):h.oR.error(a.error||"Failed to update payment status")}catch(e){console.error("Error updating payment status:",e),h.oR.error("Failed to update payment status")}finally{D(!1)}},F=e=>{E(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},z=e=>{switch(e){case"paid":return(0,s.jsx)(l.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Paid"});case"unpaid":return(0,s.jsx)(l.E,{variant:"destructive",children:"Unpaid"});default:return(0,s.jsx)(l.E,{variant:"secondary",children:e})}},K=e=>(0,s.jsx)(l.E,{variant:"outline",className:{basic:"bg-blue-100 text-blue-800",standard:"bg-purple-100 text-purple-800",premium:"bg-orange-100 text-orange-800"}[e]||"bg-gray-100 text-gray-800","data-sentry-element":"Badge","data-sentry-component":"getPlanBadge","data-sentry-source-file":"page.tsx",children:e.charAt(0).toUpperCase()+e.slice(1)}),Z=e=>!!e&&new Date(e)<new Date,V=e=>{if(!e)return"No due date";let t=new Date(e),a=Z(e);return(0,s.jsxs)("span",{className:a?"text-red-600 font-medium":"text-gray-600","data-sentry-component":"formatDueDate","data-sentry-source-file":"page.tsx",children:[(0,N.GP)(t,"MMM dd, yyyy"),a&&" (Overdue)"]})};return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"SubscriptionsPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Billing & Subscriptions"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage institution billing, payment status, and subscription details"})]})}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Total Institutions"}),(0,s.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground","data-sentry-element":"Building","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:a.totalInstitutions}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.totalStudents," students, ",a.totalTeachers," teachers"]})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Paid Institutions"}),(0,s.jsx)(f.A,{className:"h-4 w-4 text-green-600","data-sentry-element":"CheckCircle","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.paidInstitutions}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.totalInstitutions>0?Math.round(a.paidInstitutions/a.totalInstitutions*100):0,"% of total"]})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Unpaid Institutions"}),(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-600","data-sentry-element":"XCircle","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:a.unpaidInstitutions}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[a.totalInstitutions>0?Math.round(a.unpaidInstitutions/a.totalInstitutions*100):0,"% of total"]})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2","data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{className:"text-sm font-medium","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Overdue Payments"}),(0,s.jsx)(b.A,{className:"h-4 w-4 text-orange-600","data-sentry-element":"AlertTriangle","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:a.overdueInstitutions}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Require immediate attention"})]})]})]}),(0,s.jsxs)(n.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(n.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(n.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"Institution Billing"}),(0,s.jsx)(n.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"View and manage payment status for all institutions"})]}),(0,s.jsxs)(n.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(j.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground","data-sentry-element":"Search","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(d.p,{placeholder:"Search institutions...",value:A,onChange:e=>S(e.target.value),className:"pl-8","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(u.l6,{value:_,onValueChange:P,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{className:"w-[180px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{placeholder:"Payment Status","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Status"}),(0,s.jsx)(u.eb,{value:"paid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Paid"}),(0,s.jsx)(u.eb,{value:"unpaid","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Unpaid"})]})]}),(0,s.jsxs)(u.l6,{value:q,onValueChange:R,"data-sentry-element":"Select","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.bq,{className:"w-[180px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(u.yv,{placeholder:"Subscription Plan","data-sentry-element":"SelectValue","data-sentry-source-file":"page.tsx"})}),(0,s.jsxs)(u.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(u.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"All Plans"}),(0,s.jsx)(u.eb,{value:"basic","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Basic"}),(0,s.jsx)(u.eb,{value:"standard","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Standard"}),(0,s.jsx)(u.eb,{value:"premium","data-sentry-element":"SelectItem","data-sentry-source-file":"page.tsx",children:"Premium"})]})]})]}),M.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-4 mb-4 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:[M.length," institution(s) selected"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(m.AM,{open:U,onOpenChange:B,children:[(0,s.jsx)(m.Wv,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),G?(0,N.GP)(G,"MMM dd, yyyy"):"Set Due Date"]})}),(0,s.jsx)(m.hl,{className:"w-auto p-0",children:(0,s.jsx)(x.V,{mode:"single",selected:G,onSelect:e=>{H(e),B(!1)},initialFocus:!0})})]}),(0,s.jsx)(i.$,{size:"sm",onClick:()=>W("paid"),disabled:I,className:"bg-green-600 hover:bg-green-700",children:"Mark as Paid"}),(0,s.jsx)(i.$,{size:"sm",variant:"destructive",onClick:()=>W("unpaid"),disabled:I,children:"Mark as Unpaid"}),(0,s.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>E([]),children:"Clear Selection"})]})]}),(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(o.Table,{"data-sentry-element":"Table","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(o.TableRow,{"data-sentry-element":"TableRow","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(o.TableHead,{className:"w-12","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(p.S,{checked:M.length===e.length&&e.length>0,onCheckedChange:()=>{M.length===e.length?E([]):E(e.map(e=>e.id))},"data-sentry-element":"Checkbox","data-sentry-source-file":"page.tsx"})}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Institution"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Plan"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Billing Cycle"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Payment Status"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Due Date"}),(0,s.jsx)(o.TableHead,{"data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx",children:"Students/Teachers"}),(0,s.jsx)(o.TableHead,{className:"w-12","data-sentry-element":"TableHead","data-sentry-source-file":"page.tsx"})]})}),(0,s.jsx)(o.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"page.tsx",children:k?(0,s.jsx)(o.TableRow,{children:(0,s.jsx)(o.TableCell,{colSpan:8,className:"text-center py-8",children:"Loading institutions..."})}):0===e.length?(0,s.jsx)(o.TableRow,{children:(0,s.jsx)(o.TableCell,{colSpan:8,className:"text-center py-8",children:"No institutions found"})}):e.map(e=>(0,s.jsxs)(o.TableRow,{children:[(0,s.jsx)(o.TableCell,{children:(0,s.jsx)(p.S,{checked:M.includes(e.id),onCheckedChange:()=>F(e.id)})}),(0,s.jsx)(o.TableCell,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.name}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:e.type})]})}),(0,s.jsx)(o.TableCell,{children:K(e.subscription_plan)}),(0,s.jsx)(o.TableCell,{className:"capitalize",children:e.billing_cycle}),(0,s.jsx)(o.TableCell,{children:z(e.payment_status)}),(0,s.jsx)(o.TableCell,{children:V(e.payment_due_date)}),(0,s.jsx)(o.TableCell,{children:(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("div",{children:[e.actual_student_count,"/",e.student_count," students"]}),(0,s.jsxs)("div",{className:"text-muted-foreground",children:[e.actual_teacher_count,"/",e.teacher_count," teachers"]})]})}),(0,s.jsx)(o.TableCell,{children:(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",className:"h-8 w-8 p-0",children:(0,s.jsx)(C.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(c.SQ,{align:"end",children:[(0,s.jsx)(c._2,{onClick:()=>{E([e.id]),W("paid")},className:"text-green-600",children:"Mark as Paid"}),(0,s.jsx)(c._2,{onClick:()=>{E([e.id]),W("unpaid")},className:"text-red-600",children:"Mark as Unpaid"})]})]})})]},e.id))})]})})]})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73225:(e,t,a)=>{"use strict";a.d(t,{S:()=>d});var s=a(91754);a(93491);var r=a(22646),n=a(87435),i=a(82233);function d({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,s.jsx)(n.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77609:(e,t,a)=>{Promise.resolve().then(a.bind(a,90964))},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},86857:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(55732).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},90964:(e,t,a)=>{"use strict";let s;a.r(t),a.d(t,{default:()=>x,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>p});var r=a(63033),n=a(1472),i=a(7688),d=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\subscriptions\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\admin\\subscriptions\\page.tsx","default");let l={...r},o="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;s="function"==typeof d?new Proxy(d,{apply:(e,t,a)=>{let s,r,n;try{let e=o?.getStore();s=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin/subscriptions",componentType:"Page",sentryTraceHeader:s,baggageHeader:r,headers:n}).apply(t,a)}}):d;let c=void 0,u=void 0,p=void 0,x=s},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,3168,8103,4463,8134,8634,5660],()=>a(10933));module.exports=s})();
//# sourceMappingURL=page.js.map