try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="f88a3b7b-9352-41a1-a9a0-8e3fdcffb281",e._sentryDebugIdIdentifier="sentry-dbid-f88a3b7b-9352-41a1-a9a0-8e3fdcffb281")}catch(e){}"use strict";(()=>{var e={};e.id=6874,e.ids=[6874],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30742:(e,s,r)=>{r.r(s),r.d(s,{patchFetch:()=>j,routeModule:()=>v,serverHooks:()=>T,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>N});var t={};r.r(t),r.d(t,{DELETE:()=>w,GET:()=>h,HEAD:()=>A,OPTIONS:()=>b,PATCH:()=>I,POST:()=>g,PUT:()=>y});var n=r(3690),o=r(56947),l=r(75250),a=r(63033),d=r(62187),i=r(18621),u=r(32230),c=r(74683),p=r(7688);async function m(e){try{let s,{searchParams:r}=new URL(e.url),t=r.get("classId"),n=r.get("studentId");t&&n?s=(0,c.Uo)((0,c.eq)(u.classEnrollments.classId,parseInt(t)),(0,c.eq)(u.classEnrollments.studentId,parseInt(n))):t?s=(0,c.eq)(u.classEnrollments.classId,parseInt(t)):n&&(s=(0,c.eq)(u.classEnrollments.studentId,parseInt(n)));let o=s?await i.db.select({id:u.classEnrollments.id,studentId:u.classEnrollments.studentId,classId:u.classEnrollments.classId,enrolledAt:u.classEnrollments.enrolledAt,status:u.classEnrollments.status,createdAt:u.classEnrollments.createdAt,updatedAt:u.classEnrollments.updatedAt,studentName:u.users.name,studentEmail:u.users.email,className:u.classes.name}).from(u.classEnrollments).leftJoin(u.users,(0,c.eq)(u.classEnrollments.studentId,u.users.id)).leftJoin(u.classes,(0,c.eq)(u.classEnrollments.classId,u.classes.id)).where(s):await i.db.select({id:u.classEnrollments.id,studentId:u.classEnrollments.studentId,classId:u.classEnrollments.classId,enrolledAt:u.classEnrollments.enrolledAt,status:u.classEnrollments.status,createdAt:u.classEnrollments.createdAt,updatedAt:u.classEnrollments.updatedAt,studentName:u.users.name,studentEmail:u.users.email,className:u.classes.name}).from(u.classEnrollments).leftJoin(u.users,(0,c.eq)(u.classEnrollments.studentId,u.users.id)).leftJoin(u.classes,(0,c.eq)(u.classEnrollments.classId,u.classes.id));return d.NextResponse.json({success:!0,data:o})}catch(e){return console.error("Error fetching class enrollments:",e),d.NextResponse.json({success:!1,error:"Failed to fetch class enrollments"},{status:500})}}async function x(e){try{let{studentId:s,classId:r,status:t="active"}=await e.json();if(!s||!r)return d.NextResponse.json({success:!1,error:"Student ID and Class ID are required"},{status:400});if((await i.db.select().from(u.classEnrollments).where((0,c.Uo)((0,c.eq)(u.classEnrollments.studentId,s),(0,c.eq)(u.classEnrollments.classId,r))).limit(1)).length>0)return d.NextResponse.json({success:!1,error:"Student is already enrolled in this class"},{status:409});let n=await i.db.select().from(u.users).where((0,c.Uo)((0,c.eq)(u.users.id,s),(0,c.eq)(u.users.role,"student"))).limit(1);if(0===n.length)return d.NextResponse.json({success:!1,error:"Student not found or user is not a student"},{status:404});let o=await i.db.select().from(u.classes).where((0,c.eq)(u.classes.id,r)).limit(1);if(0===o.length)return d.NextResponse.json({success:!1,error:"Class not found"},{status:404});let l=await i.db.insert(u.classEnrollments).values({studentId:s,classId:r,status:t,enrolledAt:new Date,createdAt:new Date,updatedAt:new Date}).returning();return d.NextResponse.json({success:!0,data:l[0],message:"Student enrolled successfully"},{status:201})}catch(e){return console.error("Error creating class enrollment:",e),d.NextResponse.json({success:!1,error:"Failed to create class enrollment"},{status:500})}}let q={...a},f="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function E(e,s){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,r,t)=>{let n;try{let e=f?.getStore();n=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:s,parameterizedRoute:"/api/class-enrollments",headers:n}).apply(r,t)}})}let h=E(m,"GET"),g=E(x,"POST"),y=E(void 0,"PUT"),I=E(void 0,"PATCH"),w=E(void 0,"DELETE"),A=E(void 0,"HEAD"),b=E(void 0,"OPTIONS"),v=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/class-enrollments/route",pathname:"/api/class-enrollments",filename:"route",bundlePath:"app/api/class-enrollments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\class-enrollments\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:S,workUnitAsyncStorage:N,serverHooks:T}=v;function j(){return(0,l.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:N})}},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[5250,7688,8036,138,1617,2957],()=>r(30742));module.exports=t})();
//# sourceMappingURL=route.js.map