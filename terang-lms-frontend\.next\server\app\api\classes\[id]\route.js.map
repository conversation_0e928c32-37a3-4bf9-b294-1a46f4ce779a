{"version": 3, "file": "../app/api/classes/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,4aCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,IAAE,CAAE,CAAG,MAAMD,EACfE,EAAUC,EADKH,MACLG,CAASF,EAAAA,CAAAA,GAErBG,MAAMF,GACR,IADQA,CAAAA,EAAU,EACXG,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,IAAMC,EAAY,MAAMC,CAAlBD,CAAkBC,EAAAA,CACrBC,MAAM,CAAC,CACNV,EAAAA,CAAIW,EAAAA,OAAOA,CAACX,EAAE,CACdY,IAAAA,CAAMD,EAAAA,OAAOA,CAACC,IAAI,CAClBC,WAAAA,CAAaF,EAAAA,OAAOA,CAACE,WAAW,CAChCC,aAAAA,CAAeH,EAAAA,OAAOA,CAACG,aAAa,CACpCC,SAAAA,CAAWJ,EAAAA,OAAOA,CAACI,SAAS,CAC5BC,YAAAA,CAAcL,EAAAA,OAAOA,CAACK,YAAY,CAClCC,SAAAA,CAAWN,EAAAA,OAAOA,CAACM,SAAS,CAC5BC,SAAAA,CAAWP,EAAAA,OAAOA,CAACO,SAAS,CAC5BC,WAAAA,CAAaC,EAAAA,KAAKA,CAACR,IAAI,CACvBS,YAAAA,CAAcD,EAAAA,KAAKA,CAACE,KAAAA,CACtB,EACCC,IAAI,CAACZ,EAAAA,OAAAA,CAAAA,CACLa,QAAQ,CAACJ,EAAAA,KAAAA,CAAOK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGd,EAAAA,OAAOA,CAACI,SAAS,CAAEK,EAAAA,KAAKA,CAACpB,EAAE,GAC9C0B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGd,EAAAA,OAAOA,CAACX,EAAE,CAAEC,IACrB0B,GADqB1B,CAAAA,CAAAA,CACf,GAET,GAAyB,GAAG,CAAxBO,EAAUoB,MAAM,CAAhBpB,OACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIvE,IAAMsB,EAAkB,MAAMpB,EAAAA,EAAAA,CAC3BC,EADGmB,IACG,CAAC,CACNC,QAAAA,CAAUC,EAAAA,OAAOA,CAAC/B,EAAE,CACpBgC,UAAAA,CAAYD,EAAAA,OAAOA,CAACnB,IAAI,CACxBqB,iBAAAA,CAAmBF,EAAAA,OAAOA,CAAClB,WAAW,CACtCqB,UAAAA,CAAYH,EAAAA,OAAOA,CAACG,UAAU,CAC9BC,UAAAA,CAAYJ,EAAAA,OAAOA,CAACK,IAAI,CACxBC,UAAAA,CAAYC,EAAAA,iBAAiBA,CAACD,UAAAA,GAE/Bd,IAAI,CAACe,EAAAA,iBAAAA,CAAAA,CACLd,QAAQ,CAACO,EAAAA,OAAAA,CAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACa,EAAAA,iBAAAA,CAAkBR,QAAQ,CAAEC,EAAAA,OAAAA,CAAQ/B,EAAE,GAC3D0B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGa,EAAAA,iBAAAA,CAAkBrC,OAAO,CAAEA,IAEjCsC,EAAmB,CAFctC,CAAAA,CAAAA,CAGlCO,CAAS,CAAC,EAAE,MADX+B,WAEJV,EACAW,YAAAA,CADAX,EAEAY,WAAAA,CAAaZ,EAAgBD,MAAAA,EAG/B,OAAOxB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEqC,OAAAA,EAAS,EAAMC,KAAAA,CAAOJ,CAAiB,EACpE,CAAE,MAAOjC,EAAO,CAEd,EAFOA,KACPsC,OAAAA,CAAQtC,KAAK,CAAC,wBAAyBA,GAChCF,EADgCE,CAAAA,WAChCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAesC,EACpB/C,CAAoB,CACpB,CAFoB+C,OAElB9C,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAUC,EADKH,MACLG,CAASF,EAAAA,CAEnB,MAAEY,CAAI,CAAEC,aAAW,cAAEG,CAAY,WAAED,CAAS,CAAE,CADvC,EAC0C+B,IADpChD,EAAQO,IAAI,CAAZP,EAGnB,GAAIK,MAAMF,GACR,IADQA,CAAAA,EACDG,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,IAAMwC,EAAgB,MAAMtC,EAAAA,EAAAA,CACzBC,MAAM,GACNa,IAAI,CAACZ,EAAAA,OAAAA,CAAAA,CACLe,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACd,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrB0B,GADqB1B,CAAAA,CAAAA,CACf,GAET,GAA6B,GAAG,CAA5B8C,EAAcnB,MAAM,CACtB,IADEmB,GACK3C,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIvE,GAAIQ,GAAaA,IAAcgC,CAAa,CAAC,EAAE,CAAChC,SAAS,CAAE,CACzD,IAAMiC,EAAU,KAAVA,CAAgBvC,EAAAA,EAAAA,CACnBC,MAAM,GACNa,IAAI,CAACH,EAAAA,KAAAA,CAAAA,CACLM,KAAK,CACJuB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACExB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,EAAAA,KAAAA,CAAMpB,EAAE,CAAEe,GACbU,CAAAA,EAAAA,EAAAA,CADaV,CACbU,CAAAA,CAAGL,EAAAA,KAAAA,CAAMN,aAAa,CAAEiC,CAAa,CAAC,EAAE,CAACjC,aAAa,EACtDW,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGL,EAAAA,KAAAA,CAAM8B,IAAI,CAAE,aAGlBvB,KAAK,CAAC,GAET,GAAuB,GAAG,CAAtBqB,EAAQpB,KAARoB,CAAc,CAChB,OAAO5C,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,sCAAsC,CAC/C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,IAAM4C,EAAe,MAAM1C,EAAAA,EAAAA,CACxB2C,MAAM,CAACzC,EAAAA,OAAAA,CAAAA,CACP0C,GAAG,CAAC,CACHzC,IAAAA,CAAMA,GAAQmC,CAARnC,CAAsB,EAAE,CAACA,IAAI,CACnCC,WAAAA,CAAaA,GAAekC,CAAa,CAAC,EAAE,CAAClC,GAAhCA,QAA2C,CACxDG,YAAAA,CAAcA,GAAgB+B,CAAa,CAAC,EAAE,CAAC/B,IAAjCA,QAA6C,CAC3DD,SAAAA,CAAWA,GAAagC,CAAa,CAAC,EAAE,CAAChC,CAA9BA,QAAuC,CAClDG,SAAAA,CAAW,IAAIoC,IAAAA,GAEhB5B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGd,EAAAA,OAAOA,CAACX,EAAE,CAAEC,IACrBsD,SAAS,GAEZ,OAAOnD,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBqC,OAAAA,EAAS,EACTC,KAAAA,CAAOQ,CAAY,CAAC,EAAE,CACtBK,OAAAA,CAAS,4BACX,EACF,CAAE,MAAOlD,EAAO,CAEd,EAFOA,KACPsC,OAAAA,CAAQtC,KAAK,CAAC,wBAAyBA,GAChCF,EADgCE,CAAAA,WAChCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAekD,EACpB3D,CAAoB,CACpB,IAFoB2D,IAElB1D,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAUC,EADKH,MACLG,CAASF,EAAAA,CAAAA,GAErBG,MAAMF,GACR,IADQA,CAAAA,EAAU,EACXG,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIxE,IAAMwC,EAAgB,MAAMtC,EAAAA,EAAAA,CACzBC,MAAM,GACNa,IAAI,CAACZ,EAAAA,OAAAA,CAAAA,CACLe,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACd,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IACrB0B,GADqB1B,CAAAA,CAAAA,CACf,GAET,GAAI8C,GAA4B,GAAdnB,MAAM,CACtB,OAAOxB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,kBAAkB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAqBvE,OAdA,MAAME,EAAAA,EAAAA,CACHiD,MAAM,CAACC,EAAAA,gBAAAA,CAAAA,CACPjC,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGkC,EAAAA,gBAAAA,CAAiB1D,OAAO,CAAEA,IAGtC,GAHsCA,CAAAA,CAAAA,CAGhCQ,EAAAA,EAAAA,CACHiD,MAAM,CAACpB,EAAAA,iBAAAA,CAAAA,CACPZ,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGa,EAAAA,iBAAAA,CAAkBrC,OAAO,CAAEA,IAGvC,GAHuCA,CAAAA,CAAAA,CAGjCQ,EAAAA,EAAAA,CACHiD,MAAM,CAAC/C,EAAAA,OAAAA,CAAAA,CACPe,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGd,EAAAA,OAAAA,CAAQX,EAAE,CAAEC,IAEjBG,EAAAA,CAFiBH,CAAAA,CAAAA,SAEjBG,CAAaC,IAAI,CAAC,CACvBqC,OAAAA,EAAS,EAAMc,OAAAA,CAAS,4BAC1B,EACF,CAAE,MAAOlD,EAAO,CAEd,EAFOA,KACPsC,OAAAA,CAAQtC,KAAK,CAAC,wBAAyBA,GAChCF,EADgCE,CAAAA,WAChCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CC9LA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAAI,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACpC,KAAM,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,mBAAmB,SACvC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAeqD,EAA4B,GAAH,EAAQ,EAEnD,EAAO,EAAH,OAA4C,EAA9B,IAAoC,EAEtD,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,+BACA,6BACA,iBACA,uCACA,CAAK,CACL,kJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/classes/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?a5c3", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { classes, users, courseEnrollments, courses, classEnrollments } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/classes/[id] - Get a specific class\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const classId = parseInt(id);\r\n    \r\n    if (isNaN(classId)) {\r\n      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 });\r\n    }\r\n\r\n    // Get the class with teacher information\r\n    const classData = await db\r\n      .select({\r\n        id: classes.id,\r\n        name: classes.name,\r\n        description: classes.description,\r\n        institutionId: classes.institutionId,\r\n        teacherId: classes.teacherId,\r\n        coverPicture: classes.coverPicture,\r\n        createdAt: classes.createdAt,\r\n        updatedAt: classes.updatedAt,\r\n        teacherName: users.name,\r\n        teacherEmail: users.email\r\n      })\r\n      .from(classes)\r\n      .leftJoin(users, eq(classes.teacherId, users.id))\r\n      .where(eq(classes.id, classId))\r\n      .limit(1);\r\n\r\n    if (classData.length === 0) {\r\n      return NextResponse.json({ error: 'Class not found' }, { status: 404 });\r\n    }\r\n\r\n    // Get enrolled courses for this class\r\n    const enrolledCourses = await db\r\n      .select({\r\n        courseId: courses.id,\r\n        courseName: courses.name,\r\n        courseDescription: courses.description,\r\n        courseCode: courses.courseCode,\r\n        courseType: courses.type,\r\n        enrolledAt: courseEnrollments.enrolledAt\r\n      })\r\n      .from(courseEnrollments)\r\n      .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))\r\n      .where(eq(courseEnrollments.classId, classId));\r\n\r\n    const classWithDetails = {\r\n      ...classData[0],\r\n      enrolledCourses,\r\n      studentCount: 0, // TODO: Calculate actual student count\r\n      courseCount: enrolledCourses.length\r\n    };\r\n\r\n    return NextResponse.json({ success: true, class: classWithDetails });\r\n  } catch (error) {\r\n    console.error('Error fetching class:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/classes/[id] - Update a specific class\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const classId = parseInt(id);\r\n    const body = await request.json();\r\n    const { name, description, coverPicture, teacherId } = body;\r\n\r\n    if (isNaN(classId)) {\r\n      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 });\r\n    }\r\n\r\n    // Check if class exists\r\n    const existingClass = await db\r\n      .select()\r\n      .from(classes)\r\n      .where(eq(classes.id, classId))\r\n      .limit(1);\r\n\r\n    if (existingClass.length === 0) {\r\n      return NextResponse.json({ error: 'Class not found' }, { status: 404 });\r\n    }\r\n\r\n    // If teacherId is being updated, verify the new teacher\r\n    if (teacherId && teacherId !== existingClass[0].teacherId) {\r\n      const teacher = await db\r\n        .select()\r\n        .from(users)\r\n        .where(\r\n          and(\r\n            eq(users.id, teacherId),\r\n            eq(users.institutionId, existingClass[0].institutionId),\r\n            eq(users.role, 'teacher')\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (teacher.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Teacher not found or not authorized' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Update the class\r\n    const updatedClass = await db\r\n      .update(classes)\r\n      .set({\r\n        name: name || existingClass[0].name,\r\n        description: description || existingClass[0].description,\r\n        coverPicture: coverPicture || existingClass[0].coverPicture,\r\n        teacherId: teacherId || existingClass[0].teacherId,\r\n        updatedAt: new Date()\r\n      })\r\n      .where(eq(classes.id, classId))\r\n      .returning();\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      class: updatedClass[0],\r\n      message: 'Class updated successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating class:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/classes/[id] - Delete a specific class\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const classId = parseInt(id);\r\n\r\n    if (isNaN(classId)) {\r\n      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 });\r\n    }\r\n\r\n    // Check if class exists\r\n    const existingClass = await db\r\n      .select()\r\n      .from(classes)\r\n      .where(eq(classes.id, classId))\r\n      .limit(1);\r\n\r\n    if (existingClass.length === 0) {\r\n      return NextResponse.json({ error: 'Class not found' }, { status: 404 });\r\n    }\r\n\r\n    // TODO: Check if class has enrolled students or courses\r\n    // For now, we'll allow deletion\r\n\r\n    // Delete related class enrollments first (foreign key constraint)\r\n    await db\r\n      .delete(classEnrollments)\r\n      .where(eq(classEnrollments.classId, classId));\r\n\r\n    // Then delete course enrollments (foreign key constraint)\r\n    await db\r\n      .delete(courseEnrollments)\r\n      .where(eq(courseEnrollments.classId, classId));\r\n\r\n    // Delete the class\r\n    await db\r\n      .delete(classes)\r\n      .where(eq(classes.id, classId));\r\n\r\n    return NextResponse.json({\r\n      success: true, message: 'Class deleted successfully'\r\n    });\r\n  } catch (error) {\r\n    console.error('Error deleting class:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/classes/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\classes\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/classes/[id]/route\",\n        pathname: \"/api/classes/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/classes/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\classes\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "classId", "parseInt", "isNaN", "NextResponse", "json", "error", "status", "classData", "db", "select", "classes", "name", "description", "institutionId", "teacherId", "coverPicture", "createdAt", "updatedAt", "<PERSON><PERSON><PERSON>", "users", "teacherEmail", "email", "from", "leftJoin", "eq", "where", "limit", "length", "enrolledCourses", "courseId", "courses", "courseName", "courseDescription", "courseCode", "courseType", "type", "enrolledAt", "courseEnrollments", "classWithDetails", "studentCount", "courseCount", "success", "class", "console", "PUT", "body", "existingClass", "teacher", "and", "role", "updatedClass", "update", "set", "Date", "returning", "message", "DELETE", "delete", "classEnrollments", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}