try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="21eaa83d-18dd-433c-b689-8e158306a1a1",e._sentryDebugIdIdentifier="sentry-dbid-21eaa83d-18dd-433c-b689-8e158306a1a1")}catch(e){}(()=>{var e={};e.id=8647,e.ids=[8647],e.modules={1683:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});var s=r(138);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let i=(0,s.lw)(process.env.DATABASE_URL);async function n(e,...t){return await i(e,...t)}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>T,serverHooks:()=>O,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>_,HEAD:()=>m,OPTIONS:()=>R,PATCH:()=>h,POST:()=>q,PUT:()=>f});var i=r(3690),n=r(56947),o=r(75250),u=r(63033),a=r(62187),c=r(1683),p=r(7688);async function d(e){try{let t,r,{searchParams:s}=new URL(e.url),i=s.get("search"),n=parseInt(s.get("limit")||"50"),o=parseInt(s.get("offset")||"0");if(i){let e=`%${i.toLowerCase()}%`;t=await (0,c.P)`
        SELECT
          id,
          name,
          type,
          subscription_plan,
          billing_cycle,
          payment_status,
          payment_due_date,
          student_count,
          teacher_count,
          created_at,
          updated_at
        FROM institutions
        WHERE LOWER(name) LIKE ${e} OR LOWER(type::text) LIKE ${e}
        ORDER BY created_at DESC
        LIMIT ${n}
        OFFSET ${o}
      `,r=await (0,c.P)`
        SELECT COUNT(*) as total
        FROM institutions
        WHERE LOWER(name) LIKE ${e} OR LOWER(type::text) LIKE ${e}
      `}else t=await (0,c.P)`
        SELECT
          id,
          name,
          type,
          subscription_plan,
          billing_cycle,
          payment_status,
          payment_due_date,
          student_count,
          teacher_count,
          created_at,
          updated_at
        FROM institutions
        ORDER BY created_at DESC
        LIMIT ${n}
        OFFSET ${o}
      `,r=await (0,c.P)`
        SELECT COUNT(*) as total
        FROM institutions
      `;return a.NextResponse.json({success:!0,data:{institutions:t,total:parseInt(r[0].total),limit:n,offset:o},message:"Institutions retrieved successfully"})}catch(e){return console.error("Get institutions error:",e),a.NextResponse.json({success:!1,error:"Failed to retrieve institutions"},{status:500})}}async function l(e){try{let{name:t,type:r,subscriptionPlan:s,billingCycle:i,studentCount:n,teacherCount:o,paymentStatus:u}=await e.json();if(!t||!r)return a.NextResponse.json({success:!1,error:"Name and type are required"},{status:400});let p=new Date;p.setDate(p.getDate()+("yearly"===i?365:30));let d=await (0,c.P)`
      INSERT INTO institutions (
        name,
        type,
        subscription_plan,
        billing_cycle,
        payment_status,
        payment_due_date,
        student_count,
        teacher_count
      ) VALUES (
        ${t},
        ${r},
        ${s||"basic"},
        ${i||"monthly"},
        ${u||"unpaid"},
        ${p.toISOString()},
        ${n||0},
        ${o||0}
      ) RETURNING *
    `;return a.NextResponse.json({success:!0,data:d[0],message:"Institution created successfully"})}catch(e){return console.error("Create institution error:",e),a.NextResponse.json({success:!1,error:"Failed to create institution"},{status:500})}}let x={...u},y="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;function E(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,r,s)=>{let i;try{let e=y?.getStore();i=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/institutions",headers:i}).apply(r,s)}})}let _=E(d,"GET"),q=E(l,"POST"),f=E(void 0,"PUT"),h=E(void 0,"PATCH"),g=E(void 0,"DELETE"),m=E(void 0,"HEAD"),R=E(void 0,"OPTIONS"),T=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/institutions/route",pathname:"/api/institutions",filename:"route",bundlePath:"app/api/institutions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\institutions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:w,serverHooks:O}=T;function S(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:w})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=44725,e.exports=t},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5250,7688,8036,138],()=>r(41573));module.exports=s})();
//# sourceMappingURL=route.js.map