{"version": 3, "file": "1617.js", "mappings": "mhBAIA,iBAAiC,GAAuB,CACxD,QAAU,GAAU,mCACpB,OACA,8BACA,2BACA,uBACA,CAEA,SACA,2BACA,CACA,CACA,gBAA0B,IAAQ,CAClC,QAAU,GAAU,gBACpB,YACA,uBACA,KACA,WACA,iCACA,2BAEA,aACA,sCAA4D,eAAe,GAC3E,kBAAuB,EAAU,EAAE,uCAA2C,EAE9E,sBACA,uCACA,CACA,oBACA,eAEA,CACA,gBAAuC,GAAuB,CAC9D,QAAU,GAAU,yCACpB,OACA,sCACA,2BACA,uBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAgC,IAAQ,CACxC,QAAU,GAAU,sBACpB,aACA,sBACA,KACA,WACA,iCACA,2BAEA,aACA,qCAA2D,eAAe,GAC1E,kBAAuB,EAAU,EAAE,uCAA2C,EAE9E,CACA,iBAA4B,EAC5B,SAAU,YAAiB,QAAsB,aACjD,mBACA,wCAEA,yCACA,qGCnEA,iBAA4B,GAAuB,CACnD,QAAU,GAAU,8BACpB,GACA,wBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,sBACA,kBACA,CACA,oBACA,sBACA,CACA,CACA,gBAAkC,GAAuB,CACzD,QAAU,GAAU,uBACpB,gBACA,gCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,aACA,YACA,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,iBACA,SAEA,QACA,oCCnDA,mBACA,cACA,kFCCA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,GACA,0BACA,CAIA,gBACA,oBAAwB,QAAG,oBAC3B,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,CACA,cACA,mBACA,kDC1BA,SACA,iBACA,aACA,cACA,iBACA,2BACA,uBACA,uBACA,2BACA,6BACA,6BACA,0BACA,yBACA,6BACA,6BACA,yBACA,6BACA,2BACA,2CAEA,QAAU,GAAU,WACpB,KACA,SACA,SACA,QACA,QACA,UACA,WACA,WACA,SACA,UACA,YACA,SACA,WACA,kBACA,iBACA,yBACA,OACA,sBACA,QACA,CACA,oBACA,QACA,CAEA,sBACA,gFAEA,wEC9CA,iBAAsC,IAAe,CACrD,QAAU,GAAU,4BACpB,aACA,oBAAwB,QAAG,QAC3B,CACA,uECNA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,iBACpB,gBACA,wBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,uBACpB,KACA,UACA,CACA,aACA,YACA,CACA,oBACA,wBACA,CACA,sBACA,sBACA,IACA,oBACA,CAAQ,MAER,CAEA,QACA,CACA,CACA,cACA,mBACA,6FChCA,iBAA4B,GAAuB,CACnD,mBACA,2BACA,oBACA,iBACA,2BACA,uBACA,CACA,QAAU,GAAU,kBAEpB,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,YACA,uBACA,KACA,WACA,iCACA,2BAEA,aACA,qCAA2D,eAAe,GAC1E,aAAkB,EAAU,EAAE,uCAA2C,EAEzE,CACA,iBAAuB,EACvB,IAAU,iBAAe,CAAE,OAAsB,MACjD,8CACA,oCCnCA,4NCOA,SACA,QAAU,GAAU,EAAI,IAAoB,CAE5C,cACA,UAH4C,GAG5C,+BAgBA,QACA,QAAU,GAAU,gBACpB,kBACA,GACA,iCACA,CACA,SACA,oBACA,CACA,CACA,QACA,eAEA,aADA,mBACA,GACA,GAAU,QAAE,GAAQ,IAAK,GACzB,QAAiC,IAAK,gBACtC,qBACA,aAAwC,IAAK,sBAAyC,IAAK,cAE3F,CAEA,CACA,QAAU,GAAU,QAEpB,UACA,sBAEA,cACA,UAEA,OADA,wCACA,KAEA,WACA,OAAW,GAAM,wCACjB,0DAKA,OAJA,kBACA,2BACA,+CACA,CAAO,EACP,CACA,CAAK,CACL,CACA,gCACA,sBAAmC,IACnC,qDACA,oCAAoD,QACpD,CAAK,EACL,CACA,SACA,aACA,cACA,gBACA,eACA,kBACA,CAAM,MApEN,EAqEA,UACA,GAAU,QAAE,MACZ,OAAiB,gCAEjB,GAAU,QAAE,MACZ,OAAiB,0BAEjB,cACA,OAAiB,kBAEjB,qBACA,mBACA,2BACA,UACA,cACA,oBAIA,OADA,mBACA,oCACA,CACA,GAAU,QAAE,MACZ,sDACA,KACA,qCACS,EAET,GAAU,QAAE,GAAQ,IAAK,GACzB,QAAiC,IAAK,gBACtC,IAAgC,IAAK,cACrC,OACA,kBAA8C,IAAO,qBACrD,UAEA,CACA,GAAU,QAAE,GAAQ,GAAM,GAC1B,2BACA,8BACA,OAAmB,oBAEnB,cAAuC,IAAK,gBAC5C,OACA,YAA2B,IAAO,wBAAoD,IAAK,2CAAsG,IAAK,wBACtM,UAEA,CACA,GAAU,QAAE,OACZ,QAAiC,GAAc,SAC/C,IAA+B,GAAc,OAC7C,OACA,kBAA8C,GAAc,6BAC5D,UAEA,CACA,GAAU,QAAE,OACZ,GAAY,QAAE,YACd,OAAmB,gDAEnB,8DACA,GAAY,QAAE,MACd,8CAEA,KACA,OAAmB,wCAEnB,eAIA,OAHA,GACA,mBAEA,CAAiB,wCACjB,OACU,QAAE,MACZ,CAAiB,gDAEP,QAAE,qCACZ,CAAiB,+BAEP,QAAE,GAAQ,GAAQ,EAC5B,WACA,CAAmB,4BAEnB,iCACA,WACA,QACA,YACA,iBACA,IAEU,QAAQ,IAClB,SACA,CAAmB,6CAEnB,CAAiB,6BAEjB,KACA,0BACA,gDAEA,iCACA,WACA,WACA,WACA,IAEA,EACA,CAAiB,wCAEjB,CAAe,+CACf,CAAK,EAhLL,OAAmB,kBACnB,eACA,aACA,2BACA,oBACA,WACA,eAEA,8BAGA,QAsKA,CACA,kBAA0B,eAAc,EACxC,YACA,aAEA,2CACA,oBAEA,sBACA,YAEA,uBACA,yBACA,sBACA,qBAEA,IACA,CACA,yCACA,CACA,SACA,YAEA,aACA,WACA,KAEA,qBACA,CACA,WAEA,OADA,mCAAqD,sBAA8B,EACnF,IACA,CACA,eAEA,OADA,2BACA,KAQA,MACA,oBACA,CACA,CACA,QACA,eACA,YACA,CACA,QAAU,GAAU,SACpB,MACA,SACA,oBACA,CACA,CAIA,cACA,kGAEA,OACA,uBACA,EACA,GACA,qBACA,EACA,EACA,KACA,KACA,CACA,SAKA,mBACA,aACA,cACA,CACA,QAAU,GAAU,UACpB,MACA,SACA,oBACA,CACA,CAIA,mBACA,SAIA,eAHA,qCACA,oBAEA,aACA,wBAEA,eACA,CACA,KAIA,QAHA,WACA,gBACA,EAKA,WAHA,YACA,eACA,EAKA,MAHA,YACA,wBACA,EAYA,OAVA,cACA,SACA,2BACA,iBACA,UAEA,UAEA,eACA,EAKA,aAHA,YACA,eACA,EAKA,cAHA,YACA,eACA,EAKA,QAHA,cACA,iBACA,EAEA,CAAC,UAAkB,EACnB,KACA,QACA,iBACA,WACA,iBACA,CACA,QAAY,GAAU,gBAEtB,oBACA,SACA,gBAGA,QACA,sCACA,CACA,CACA,YACA,CAAC,UAAkB,CACnB,SACA,eACA,WACA,CACA,QAAU,GAAU,gBACpB,SACA,oBACA,CACA,CAIA,gBACA,iBACA,GAAQ,QAAE,OACV,kBACA,yCAAqD,OAAO,iBAE5D,iBAEA,GAAQ,QAAE,OAAc,QAAE,aAC1B,wBACA,yCAAqD,aAAa,iBAElE,kDACA,CACA,QACA,CAAG,CACH,CACA,yCACA,SACA,QAAU,GAAU,UAEjB,GAAc,GAEjB,KACA,cAAgB,yCAA4C,EAC5D,KAAS,GAAc,GACvB,OACA,eACA,SACA,iBACA,QACA,cACA,UACA,CACA,CACA,SACA,oBACA,CACA,CAOA,GAAM,6BACN,oBACA,EACA,IAAK,6BACL,oBACA,EACA,GAAQ,6BACR,oBACA,0FC1ZA,iBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,OACA,8BACA,wBACA,mBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,UACA,kBACA,KACA,WACA,2BACA,kBACA,CACA,4BACA,qBACA,SACA,CACA,oBACA,6CACA,WAAwB,eAAe,IAAI,WAAW,GAChD,wBACN,UAEA,WAAwB,eAAe,GAGvC,CACA,gBAAqC,IAAe,CACpD,QAAU,GAAU,uCACpB,OACA,oCACA,wBACA,mBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA8B,IAAQ,CACtC,QAAU,GAAU,oBACpB,UACA,kBACA,KACA,WACA,2BACA,mBAEA,4BACA,qBACA,SACA,CACA,wBACA,oBACA,6CACA,WAAwB,eAAe,IAAI,WAAW,GAChD,wBACN,UAEA,WAAwB,eAAe,GAGvC,CACA,gBAAqC,IAAe,CACpD,QAAU,GAAU,uCACpB,OACA,oCACA,wBACA,mBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA8B,IAAQ,CACtC,QAAU,GAAU,oBACpB,UACA,kBACA,KACA,WACA,2BACA,mBAEA,0BACA,wBACA,oBACA,6CACA,WAAwB,eAAe,IAAI,WAAW,GAChD,wBACN,UAEA,WAAwB,eAAe,GAGvC,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,UACA,4HACA,CACA,mECrHA,SACA,QAAU,GAAU,qBACpB,SACA,cACA,CACA,CACA,QACA,QAAU,GAAU,kBACpB,mBACA,GACA,4BACA,CACA,cACA,gBACA,IACA,wBACA,CAAQ,MACR,gBACA,CACA,CAAK,EACL,2BAAiE,aAA6B,MAC9F,4BAAgC,EAAM,EAAE,EAAU,EAClD,CACA,CACA,QACA,QAAU,GAAU,eACpB,WACA,CACA,gDCxBA,SACA,eACA,YACA,CACA,QAAU,GAAU,4BACpB,eACA,YACA,WAEA,KAEA,CACA,QACA,iBACA,aACA,0BACA,CACA,QAAU,GAAU,2BACpB,SACA,OAAiB,IAAK,gBACtB,SAEA,OAAiB,IAAK,cAGtB,8BAA6C,IAAK,qBAFlD,kBAKA,OAAiB,GAAc,CAC/B,OACA,KAAkB,GAAc,EAChC,gBACA,UACA,EAEA,OAAiB,IAAK,iBACtB,QAA6B,IAAK,iBAClC,MACA,SAEA,SAOA,OANA,uBACA,eACA,KACA,yBAEA,CAAO,EACP,CACA,CACA,iBACQ,QAAE,GAAQ,GAAM,EACxB,sCAEA,CACA,CACA,CACA,QACA,eACA,YACA,CACA,QAAU,GAAU,EAAI,IAAgC,CACxD,eACA,kBACA,GAHwD,CAGxD,wBAEA,KAEA,CACA,gBACA,+BACA,CAIA,gBACA,iBACA,EACA,sCAEA,CACA,gBACA,WAAa,IAAG,iCAChB,CACA,gBACA,OAAS,IAAG,2BACJ,QAAE,GAAI,GAAM,EACpB,OAEQ,QAAE,GAAI,IAAG,EACjB,OAEQ,QAAE,GAAI,IAAG,UACjB,OAEA,GAEA,CCnGA,cAEA,OADA,iFACA,iCACA,CACA,cAEA,MADA,kFACA,gBAEA,EADA,0BAA4D,mBAAsB,EAAE,YAAc,EAE/F,GACH,CACA,cACA,QACA,CACA,QACA,QAAU,GAAU,gBAEpB,QACA,iBACA,oBACA,GACA,mDACA,CACA,mBACA,8BACA,cAAgC,IAAK,0BACrC,UAAmC,IAAK,sBACxC,KAAmB,EAAO,GAAG,EAAU,GAAG,OAAY,EAItD,OAHA,eACA,yBAEA,cAEA,cACA,QAAyB,IAAK,0BAC9B,IAA4B,IAAK,sBACjC,KAAwB,EAAO,GAAG,EAAU,EAC5C,0BACA,6BAA+C,IAAK,mBACpD,SAA6B,EAAS,GAAG,OAAY,EACrD,kCACA,CACA,uBACA,CACA,CACA,aACA,cACA,oBACA,CACA,CClDA,sBACA,QAAU,GAAU,6BACpB,SAAgB,UAAgB,EAChC,SACA,yBACA,YACA,CACA,CACA,sBACA,mBACA,uBAA2B;AAC3B,UAAU,EAAO,GACjB,aACA,cACA,aACA,gCACA,iBACA,CACA,CACA,kBACA,QAAU,GAAU,0CACpB,CACA,OAAY,mBAAqB,CACjC,CACA,iJCvBA,iBAAyB,IAAI,CAC7B,QAAU,GAAU,eCiCpB,QACA,QAAU,GAAU,aAEpB,OACA,gBACA,gBAAsB,EAAW,UACjC,CACA,qBACA,0FACA,6DACA,EAAiC,QAAG;8BACpC,EAAgC,IAAG,eAA8B,GAAG,IAAG,gBAA8B;;;;;EAKrG,QACA,UAA0B,QAAG,+BAA+B,IAAG,eAA8B,GAC7F,mBAIA,MAHA,aACM,QAAG,oCAAoC,IAAG,eAA8B,GAAG,IAAG,gBAA8B,kCAClH,CACA,UACA,wBACA,qBACA,4CACA,mBACA,gBAA6B,IAAG,QAEhC,iBACY,QAAG,eAAe,IAAG,eAA8B,GAAG,IAAG,gBAA8B,gCAAgC,OAAe,IAAI,eAAuB,GAE7K,CAEA,CAAK,CACL,CACA,cACA,UAAe,EAAK,GAEpB,eACA,UAAe,IAAQ,EAEvB,gBACA,UAAe,qBAAwB,GAEvC,gBACA,qBACA,OAA2B,QAAG,SAC9B,2BACA,OAAyB,QAAG,GAAG,IAAG,wBAAwB,MAAM,QAAQ,IACxE,cACA,OAA2B,QAAG,MAI9B,OADA,OAAuB,QAAG,KACf,IAAG,QACd,CACA,wBAAqB,iCAAmC,EACxD,2BACA,IAAqC,QAAG,cAAc,uBAAiC,iBAAqB,EAAE,SAC9G,IAA6B,QAAG,UAAU,EAAM,SAChD,MAAW,QAAG,GAAG,EAAQ,cAAc,EAAM,EAAE,EAAS,EAAE,EAAa,EAEvE,oBACA,QAA+B,IAAK,iBACpC,wBACA,6CAEA,WACA,OAAW,IAAG,wBACd,WACA,QAAoC,IAAG,yBACvC,EAAkB,QAAG,GAAG,IAAG,6CAA+C,IAAI,EAAM,SACpF,MACA,GAAqB,IAAG,YAExB,IACK,EACL,CACA,wBAAqB,sDAAqD,EAC1E,2BACA,IAA4B,IAAO,cACnC,IAA8B,IAAO,gBACrC,IAAgC,IAAO,sBACvC,iBACA,EAAqB,QAAG,GAAG,EAAc,QAAG,GAAG,IAAG,eAAyB,UAAY,EAAE,IAAG,eAA2B,EAAE,GAAS,QAAG,IAAI,IAAG,eAAmB,EAAE,EACjK,2BACA,KAA4B,IAAG,OAAO,IAAG,wCACzC,qBACA,IAAqC,QAAG,cAAc,uBAAiC,iBAAsB,EAAE,SAC/G,IAA6B,QAAG,UAAU,EAAM,SAChD,MAAW,QAAG,GAAG,EAAQ,SAAS,GAAU,MAAM,EAAO,EAAE,EAAQ,EAAE,EAAS,EAAE,EAAS,EAAE,EAAa,EAaxG,gCAA2B,MAAwB,EAAI,EACvD,eACA,cAAqC,QAAO,MAC5C,SACA,GAAU,QAAE,GAAQ,IAAG,8BACvB,OAAmB,IAAG,gCACd,GAAS,QAAE,GAAQ,IAAG,WAAa,QAAE,GAAQ,IAAG,GACxD,MAAsB,QAAE,GAAQ,IAAG,kBACnC,EACA,OACA,IAAgB,IAAG,CACnB,qBACA,CAAoB,OAAE,GAAI,IAAQ,EACT,IAAG,4CAE5B,KAKA,UAEY,QAAE,GAAQ,IAAG,WACzB,OAAqB,QAAG,OAAO,IAAG,0BAA8B,EAEhE,EAAQ,GAAS,QAAE,GAAQ,GAAM,IACjC,EACA,OAAqB,IAAG,6CAExB,WAMA,OAHA,OACA,OAAmB,QAAG,MAEtB,CACA,CAAK,EACL,OAAW,IAAG,QACd,CACA,cACA,oBACA,OAEA,SACA,4BACA,OACA,OAAwB,QAAG,KAE3B,cACA,YAA4C,QAAG,kBAC/C,OAAkC,QAAG,OAAO,KAAY,SACxD,GAAU,QAAE,GAAQ,IAAO,GAC3B,QAAgC,IAAO,cACvC,IAAkC,IAAO,gBACzC,IAAoC,IAAO,sBAC3C,uBACA,OACU,QAAG,GAAG,IAAG,kBAAyB,MAAM,GAAY,EAAE,EAAc,QAAG,GAAG,IAAG,eAAyB,UAAY,EAAE,IAAG,eAA2B,EAAE,GAAS,QAAG,IAAI,IAAG,eAAmB,EAAE,EAAE,EAAM,EAE9M,EAAQ,OAAS,QAAE,GAAQ,IAAI,GAC/B,QAA+B,GAAc,OAC7C,IAAiC,GAAc,SAC/C,IAAmC,GAAc,eACjD,uBACA,OACU,QAAG,GAAG,IAAG,kBAAyB,MAAM,GAAY,EAAE,EAAa,QAAG,GAAG,IAAG,eAAwB,UAAY,EAAE,IAAG,eAA0B,EAAE,GAAS,QAAG,IAAI,IAAG,eAAmB,EAAE,EAAE,EAAM,EAE3M,EAAQ,IACR,OACU,QAAG,GAAG,IAAG,kBAAyB,MAAM,GAAY,EAAE,EAAM,EAAE,EAAM,EAG9E,eACA,OAAwB,QAAG,IAE3B,CACA,OAAW,IAAG,QACd,CACA,kBACA,GAAQ,QAAE,GAAQ,IAAK,KAAW,IAAK,kBACvC,MAAqB,QAAG,GAAG,IAAG,cAAkB,IAAK,uBAAuB,EAI5E,OAHA,EAAgB,IAAK,iBACrB,GAAmB,QAAG,GAAG,IAAG,cAAkB,IAAK,iBAAiB,GAAG,GAAS,EAEnE,QAAG,GAAG,GAAU,EAAE,IAAG,cAAkB,IAAK,eAAe,EAExE,QACA,CACA,kBACA,WACA,SACA,aACA,QACA,SACA,QACA,QACA,UACA,UACA,QACA,SACA,gBACA,WACA,eACG,EACH,IAaA,EASA,EAIA,EA1BA,KAAqC,QAAmB,IACxD,eACA,KAAmM,EAAnM,GAAU,QAAE,SAAU,GAAM,GAAK,QAAY,mBAAqB,QAAE,GAAQ,GAAQ,YAAoB,QAAE,GAAQ,GAAU,EAAU,GAAc,OAAS,QAAE,GAAQ,IAAG,SAAa,QAAY,SAEnM,eAFmM,QACnM,QAAW,EAAO,UAAuB,IAAK,iBAAmB,QAAY,MAAkB,IAAK,qBACpG,CACA,MAA0B,QAAY,eACtC,aACA,SAAmB,kBAAkB,+BAA+B,EAAU,KAAK,aAAa,oBAAoB,EAAU,wDAE9H,EAEA,uBACA,sBAEA,IACA,UAAwC,QAAG,YAAc,QAAG,iBAAiB,IAAG,WAAmB,QAAG,MAAM,IAE5G,2CAAwD,EAAe,EACvE,yBACA,qBACA,IAA6B,QAAG,UAAU,EAAM,SAChD,IAA+B,QAAG,WAAW,EAAO,QAEpD,gBACA,GAAmB,QAAG,aAAa,IAAG,QAAe,QAAG,OAAM,EAG9D,eACA,GAAmB,QAAG,aAAa,IAAG,QAAe,QAAG,OAAM,EAE9D,mDAA4F,QAAG,UAAU,EAAM,SAC/G,IAA+B,QAAG,WAAW,EAAO,SACpD,EAA6B,IAAG,SAChC,MACA,MAAwB,QAAG,QAAQ,IAAG,iBAA6B,EACnE,aACA,SACU,QAAG,OAAO,IAAG,MACvB,qDACY,QAAG,MACH,GAGZ,gBACA,SAAyB,QAAG,WACpB,qBACR,SAAyB,QAAG,gBAE5B,WACA,CACA,MAAuB,QAAG,GAAG,EAAQ,QAAQ,GAAa,EAAE,GAAW,OAAO,EAAS,EAAE,EAAS,EAAE,EAAS,EAAE,EAAW,EAAE,EAAU,EAAE,EAAW,EAAE,EAAS,EAAE,EAAU,EAAE,EAAiB,SAC7L,WACA,6BAEA,CACA,CACA,wBACA,cACA,MACA,uEAEA,aACA,wCAA2C,gBAAyB,EAEpE,wBACA,6BAAoC,2BAAyB,EAC7D,EAEA,CACA,wBACA,aACA,kBAAmB,oDAChB,EACH,IAEA,EAFA,EAAsB,QAAG,IAAI,WAAoB,IACjD,EAAuB,QAAG,IAAI,WAAqB,GAEnD,kBACA,SACA,eACA,GAAY,QAAE,GAAgB,IAAQ,EACtC,OAA6B,IAAG,0BACtB,GAAS,QAAE,GAAgB,IAAG,GACxC,YAA0B,uBAAsC,KAChE,uBACgB,QAAE,GAAQ,IAAQ,GAClC,kBAA6C,IAAG,oBAEhD,CACA,OAA6B,QAAG,GAAG,EAAc,EACjD,EAAU,IACV,OAA6B,QAAG,GAAG,EAAc,GAGjD,EAAmB,QAAG,aAAa,IAAG,QAAqB,QAAG,OAAO,EAErE,mDAA4F,QAAG,UAAU,EAAM,SAC/G,EAA0B,IAAG,QAAQ,GAAM,EAAE,YAAoB,GACjE,IAA+B,QAAG,WAAW,EAAO,SACpD,MAAW,QAAG,GAAG,EAAU,EAAE,EAAc,EAAE,EAAW,EAAE,EAAW,EAAE,EAAS,EAAE,EAAU,EAE5F,wBAAqB,iFAAgG,EACrH,SAEA,iBADA,EAA0B,IAAK,kBAC/B,0CACA,QACA,QAAsB,IAAG,6CAEzB,KAEU,QAAE,CADZ,EACsB,IAAG,EACzB,OAFA,GAIA,OAJA,EAIA,eAKA,eADA,OAAyB,IAAG,iBAC5B,cACA,SACA,kBACA,WACA,eAAqC,QAAE,GAAW,IAAK,oBACvD,yBACA,oBACA,EAAmC,QAAE,GAAkB,IAAG,IAAsB,IAAG,YACnF,SACA,EAAc,wCAKd,OAA6B,QAAG,eALlB,CACd,qBACA,EAA+B,QAAE,GAAmB,IAAG,IAAuB,IAAG,YACjF,SACA,EAAc,IAId,SAEA,CACA,UACA,cACA,OAA6B,QAAG,KAEhC,CAEA,2BACA,EAAsB,IAAG,SACzB,IAAqC,QAAG,cAAc,uBAAiC,iBAAqB,EAAE,SAC9G,IAAuC,QAAG,gBAAgB,EAAW,SACrE,SAA4D,QAAG,kCAC/D,MAAW,QAAG,GAAG,EAAQ,cAAc,GAAO,EAAE,GAAa,EAAE,EAAc,EAAE,EAAU,EAAE,EAAc,EAAE,EAAa,EAExH,wCAAsC,8BAAgC,EACtE,QAA2C,QAAG,uBAC9C,IAAuC,QAAG,uBAC1C,MAAW,QAAG,4BAA4B,GAAiB,EAAE,EAAK,EAAE,EAAc,EAElF,iBACA,GAAQ,QAAE,GAAU,IAAO,GAAK,QAAE,GAAU,IAAM,EAClD,aACM,GAAS,QAAE,GAAU,IAAS,EACpC,gBACM,GAAS,QAAE,GAAU,IAAM,EACjC,aACM,GAAS,QAAE,GAAU,IAAW,GAAK,QAAE,GAAU,IAAiB,EACxE,kBACM,GAAS,QAAE,GAAU,IAAM,GAAK,QAAE,GAAU,IAAY,EAC9D,kBACM,GAAS,QAAE,GAAU,IAAM,EACjC,kBAEA,YAEA,CACA,gBACA,kBACA,mBACA,2BACA,6BACA,+BACA,iCACA,cACA,CAAK,CACL,CAkeA,+BACA,aACA,SACA,gBACA,QACA,cACA,cACA,aACA,sBACA,SACG,EACH,IAmIA,EAnIA,KACA,WACA,KACA,UAEA,EADA,0BACA,eACA,aACA,QACA,MAAe,EAAkB,KACjC,WADiC,QACjC,OACA,UACA,YACA,EAAO,MACD,CACN,yBACA,0CAAwE,EAAkB,QAE1F,QAF0F,EAE1F,EACA,2CAA2F,QAAY,YACvG,KAA4B,EAAsB,IAClD,CACA,SACA,KACA,CAJkD,EAIlD,WACA,SACA,yCACA,YAGA,iBACA,WACA,OAEA,UAGA,aACA,uFAEA,EAAQ,IACR,yBAEA,gBACA,mBACA,QAA+B,gBAA6B,CAC5D,CACA,SAKA,GAJA,QACA,iEAAuH,0CAA4D,EAGnL,SAEA,8BADA,wCAAuF,GAAG,MAAE,YAE5F,QACA,QACA,MAAmB,EAA6B,IAChD,CAAW,EAGX,cAAmB,MAJ6B,EAI7B,GAAe,IAClC,QACA,MAAiB,QAAE,GAAQ,IAAG,yCAC9B,QACA,MAAiB,QAAE,GAAQ,GAAM,EAAI,EAAkB,OACvD,SADuD,UACvD,OACA,UACA,aACS,EAET,+CAA8F,QAAmB,kBAYjH,QACA,QACA,cACA,WACA,CAAQ,EAfR,kBACA,QAEA,WACY,QAAE,GAAe,GAAM,EAClB,EAAkB,KAEpB,EAAsB,MAErC,GAJmC,CAInC,MACA,IAHqC,MAGrC,CAKQ,IACR,MAAmC,QAAiB,QAEpD,IADkC,QAAkB,oBACpD,CACA,KAAsC,EAAW,GAAG,EAAsB,EAC1E,EAAwB,QAAG,IAC3B,aACA,OAA2B,QAAE,CACf,EAAkB,mBAClB,EAAkB,QAIhC,QAJgC,4BAIhC,EACA,aACA,SACA,gBACA,WACA,iBACA,YAAuB,QAAE,GAAW,IAAG,UAA6C,SAAW,CAAI,cAA2C,EAC9I,aACA,SACA,qBACA,CAAS,EACT,EAAsB,QAAG,GAAG,IAAG,eAAgC,GAAG,IAAG,oBAAoB,QACzF,QACA,GAAc,QAAG,OACjB,UAAqB,GAAQ,SAAsB,IACnD,QACA,gBACA,UACA,CAAS,EACT,QACA,QACA,QACA,QACA,qBACA,UACA,sBACS,CACT,CACA,CACA,gBACA,UAAgB,EAAY,CAAG,yCAA0C,SAAmB,MAAM,EAAW,IAAK,EAIlH,GADA,EAAY,QAAG,MACf,GACA,MAAkB,QAAG,oBAAoB,IAAG,MAC5C,MACA,EAAa,yBAA8B,KAAc,QAAG,GAAG,IAAG,eAAe,EAAW,GAAG,EAAM,GAAG,GAAG,IAAG,oBAAoB,EAAI,QAAE,GAAS,IAAG,mBAE5I,QAAG,MACH,GACE,QAAE,GAAsB,IAAI,GACtC,GAAgB,QAAG,qBAAqB,EAAM,EAAE,WAAqB,QAAG,aAAa,IAAG,QAAe,QAAG,MAAM,SAAW,iBAE3H,QACA,aACA,aACA,mBACA,UACA,4BACA,WACA,CAAO,CACP,qCAEA,yBACA,MAAiB,EAAY,KAC7B,KAD6B,EAC7B,EAAoB,CACpB,aACA,QACA,MAAmB,IAAG,SACtB,CAAW,EACX,QACA,QACA,SACA,UACA,gBACS,EACT,SACA,SACA,SACA,MAEA,EAAiB,EAAY,KAE7B,KAF6B,CAE7B,mBACA,MAAe,QAAE,GAAS,IAAO,QAAiB,GAAQ,KAAW,IACrE,SAAkB,CAClB,mBAA2C,QAAe,KAC1D,QACA,MAAiB,QAAE,GAAS,GAAM,EAAI,EAAkB,MACxD,EAAS,EACT,QACA,QACA,QACA,SACA,UACA,gBACO,CACP,EAAM,IACN,yBACA,MAAe,EAAY,KAC3B,KAD2B,EAC3B,EAAkB,CAClB,yBAAqC,EAAO,KAC5C,QACA,MAAiB,QAAE,GAAQ,GAAM,EAAI,EAAkB,MACvD,EAAS,EACT,QACA,QACA,QACA,SACA,UACA,gBACO,EAEP,OACA,oBACA,MACA,WACA,CACA,CACA,CCjmCA,QACA,QAAU,GAAU,0BACpB,mBACA,GACA,aAAoB,KACpB,CACA,SACA,WACA,OACA,OACA,yBACA,mBACA,KAEA,EAEA,OAAiB,GAAc,CAC/B,OACA,KAAoB,GAAc,EAClC,yBACA,EAAmB,GAAc,iBACjC,KAEA,EAEA,sBACA,YAGA,OADoB,QAAE,GAAW,GAAQ,qBAAgC,QAAE,GAAW,IAAI,IAAa,GAAc,mBACrH,IACA,GAAQ,QAAE,GAAQ,IAAG,WACrB,+DACA,aAEA,gBAEA,OADA,sBACA,CACA,CACA,GAAQ,QAAE,GAAQ,IAAG,GACrB,mCACA,QAEA,aACA,2BAAmC,EAAK,yJAExC,OACA,CAAQ,OAAE,GAAQ,GAAM,EACxB,kBACA,UACA,EACA,IAAc,EACd,UACA,QACA,GAHqC,CAGnB,EAAsB,0DAKxC,EAEA,6BACA,EAEA,+BACA,CACA,CCtEA,QACA,QAAU,GAAU,qBAEpB,qBACA,6BAEA,CCNA,QACA,QAAU,GAAU,kBACpB,mCACA,SACA,0BACA,CACA,WACA,iBACA,IACA,MACA,GAEA,IAEA,MADA,MACA,CACA,EAEA,CACA,UACA,+BACA,CACA,gBCoCA,oBACA,CAAM,OAAE,GAAQ,IAAO,EACvB,GAAkB,IAAM,KAAO,EAAM,IAAM,EAAE,GAAG,EAAM,IAAK,kBAAkB,IAAU,IAAK,mBAEtF,QAAE,GAAQ,GAAQ,EACxB,mBAEM,QAAE,GAAQ,IAAG,EACnB,iBAEA,GClDA,QACA,QAAU,GAAU,oBACpB,OACA,QACA,QACA,YACA,QACA,gBACA,qBACA,uBACA,uBACA,YACA,2BAEA,yBAEA,UAEA,YAEA,OADA,iBACA,KAQA,QACA,IAEA,EAFA,gBAgBA,OAZA,EADA,YACA,YACe,QAAE,CAJjB,EAIuB,GAAQ,EAC/B,mBACA,kDAEe,QAAE,CARjB,EAQuB,GACvB,EAAmB,GAAc,iBAClB,QAAE,CAVjB,EAUuB,IAAG,EAC1B,GAEe,QAAe,CAb9B,GAeA,OACA,MAhBA,EAiBA,SACA,kBACA,qBACA,qBACA,uBACA,sBACA,CAAK,0BACL,CACA,CACA,gBAAuC,EACvC,QAAU,GAAU,IADoC,oBACpC,CACpB,CACA,QACA,oBACA,UACA,gBACA,QACA,QACA,mBACA,+BACA,OAAgB,uEAAsE,EAkBtF,aAjBA,QACA,aACA,WACA,QACA,QAAgB,KAAW,CAC3B,WACA,iBAEA,uBACA,eACA,eACA,QACA,iBACA,oBAEA,eAAqB,QAAgB,IACrC,0DAAsE,qBAAyB,GACxE,EAAgB,0BACvC,CAEA,gBACA,2BAEA,gBACA,cACA,qBACA,EAAwB,QAAgB,IACxC,aAAyB,EAAgB,0BACzC,+DACA,sBAAkC,EAAU,kCAE5C,2BACA,sEACA,qBACA,sBACA,GAEA,qBAA8C,QAAE,GAAQ,IAAG,IAC3D,MAA4B,QAAE,GAAQ,GAAQ,qBAA6B,QAAE,GAAQ,IAAI,IAAU,GAAc,mBAAyB,IAAK,iBAC/I,uBACA,CAcA,GAZA,sBACA,KACA,UACA,mBACA,IAAgB,EAAqB,CAAG,kBAAH,CAAG,wBAA+C,GAEvF,EAEA,mBACA,uBAEA,2BAA+B,uCAAgD,EAC/E,mBACA,UACA,WACA,+BACA,KAEA,aACA,4CACA,6DAEA,+BACA,KAEA,aACA,YACA,+BACA,KAEA,YACA,4CACA,6DAEA,8BAGA,CAEA,YAEA,CA4BA,oCAaA,2CA4BA,sCA4BA,qCAaA,8CA4BA,oCA2BA,sCAYA,6CACA,uBACA,WACA,oCACA,IAAW,QAAY,iDACvB,YACA,iHAIA,OADA,oCAAsC,wBAA0B,EAChE,KAEA,CA0BA,yCA0BA,4CA0BA,gDAyCA,qDA0BA,2CAyCA,6CAEA,oBAEA,OADA,oCACA,KA+BA,SAUA,MATA,sBACA,KACA,UACA,mBACA,IAAc,EAAqB,CAAG,kBAAH,CAAG,wBAA+C,GAErF,EAEA,oBACA,KAwBA,UAUA,MATA,sBACA,KACA,UACA,mBACA,IAAc,EAAqB,CAAG,kBAAH,CAAG,wBAA+C,GAErF,EAEA,qBACA,KAEA,cACA,4BACA,WACA,UACA,mBACA,IAAc,EAAqB,CAAG,kBAAH,CAAG,0BAAiD,GAGvF,6CACM,IACN,sBAEA,YAEA,cACA,4BACA,WACA,UACA,mBACA,IAAc,EAAqB,CAAG,kBAAH,CAAG,0BAAiD,IAGvF,yBACA,kCACA,0CAEA,qBAEA,EAAM,IAEN,kCACA,wCAFA,EAIA,oBAJA,EAOA,YAkBA,SAMA,OALA,kCACA,wCAEA,oBAEA,KAkBA,UAMA,OALA,kCACA,yCAEA,qBAEA,IACA,CAWA,UAA2B,EAE3B,OADA,oCAAkC,YAClC,KAGA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CACA,MACA,SAEA,GADA,UAAuB,EAAgB,oBACvC,kBACA,yCAA6D,EAAgB,UAE7E,IAF6E,GAE7E,UACA,IAAU,GAAQ,wDAClB,IAAU,EAAqB,CAAG,kBAAH,SAAG,4BAA0D,EAE5F,CAEA,oBACA,iBACA,mBACA,IAAU,EAAqB,CAAG,kBAAH,EAAG,gDAA0E,EAE5G,CACA,WACA,WACA,CACA,cAEA,OADA,6BAA6C,SAAU,8BAAuC,QAAuB,WAAgB,CAAI,kCACzI,KAEA,CACA,kBACA,QAAU,GAAU,YAEpB,aACA,IAAY,2FAAoF,KAChG,MACA,kGAEA,WAAY,GAAS,EACrB,OAAW,GAAM,6CACjB,MAAyB,QAAmB,IAC5C,4DACA,cACA,cACO,IAEP,OADA,wBACA,aACA,CAAK,CACL,CAQA,WACA,uBACA,CACA,UAEA,YAEA,OADA,iBACA,KAEA,WACW,GAAM,yCACjB,0CAEA,CAGA,gBACA,mBACA,wBACA,OACA,QACA,aACA,EAAK,EACL,eACA,IAAW,QAAY,0DACvB,YACA,iHAIA,2BACA,CACA,CAjBA,QAAW,IAAgB,EAAY,EAkBvC,QAlBuC,EAkBvC,EACA,QACA,WACA,YACA,gBACA,UACA,aACA,CAAC,CACD,gBACA,gBACA,oBACA,qBACA,kBACA,iBC1zBA,UACA,QAAU,GAAU,mBACpB,QACA,0BACA,GACA,aAAmB,QAAE,GAAU,GAAS,SACxC,mBAAyB,QAAE,GAAU,GAAS,QAC9C,CACA,cACA,WAeA,OAAa,GAdb,IACA,sBACA,SAEA,UACA,IAAY,GAAY,CACxB,WACA,sDAAiF,GAAI,CACrF,EACA,IAEA,IAAY,EAAqB,OAAG,YAAH,SAAG,4BAA0D,GAGjF,CACb,EACA,WACA,WAyBA,OAAa,OAxBb,YACA,WAAiB,EAAe,CAChC,YADgC,GAChC,EACA,eACA,uBACA,UACA,CAAO,CACP,EAiBa,eAhBb,YACA,WAAiB,EAAe,CAChC,YADgC,GAChC,EACA,eACA,uBACA,WACA,CAAO,CACP,EASa,iBARb,cACA,WAAiB,EAAe,CAChC,YADgC,GAChC,EACA,eACA,uBACA,aAAoB,EACpB,CAAO,CACP,CACa,CACb,CACA,UACA,WAAe,EAAe,CAC9B,YAD8B,GAC9B,EACA,eACA,yBACA,CAAK,CACL,CACA,kBACA,WAAe,EAAe,CAC9B,YAD8B,GAC9B,EACA,eACA,0BACA,WACA,CAAK,CACL,CACA,sBACA,WAAe,EAAe,CAC9B,YAD8B,GAC9B,EACA,eACA,0BACA,aAAkB,EAClB,CAAK,CACL,CAEA,aAIA,OAHA,cACA,kBAAyB,EAAS,qBAElC,aAEA,CC3EA,SACA,qBACA,aACA,eACA,eACA,eACA,CACA,QAAU,GAAU,mBACpB,WACA,YAEA,OADA,iBACA,KAEA,OACA,cACA,WACM,OAAY,eAClB,aACA,aACA,eACA,wBACA,CACA,CACA,iBAA2B,EAC3B,UADuC,EACvC,WACA,QACA,eACA,eACA,iBAAoB,+BACpB,eAAqB,QAAgB,IACrC,0DAAsE,qBAAyB,EAC/F,CACA,QAAU,GAAU,aACpB,OACA,UACA,oBACA,YACA,QAEA,MAAsB,QAAgB,CADtC,GAMA,MAJA,oBACA,iCAEA,iBALA,EAMA,KAEA,4BACA,CAAQ,OAAE,GAAQ,IAAO,EACzB,EAAmB,IAAK,iBACT,QAAE,GAAQ,GAAQ,EACjC,mBAEA,EAAiB,GAAc,iBAE/B,cACA,cACA,MAAwB,QAAgB,IACxC,8DACA,sBAAkC,EAAU,kCAE5C,yBACA,yBAA0C,QAAE,kBAAmB,IAAG,mDAClE,IACA,UACA,kBAA8B,IAAK,iBACnC,IAAgB,EAAqB,CAAG,kBAAH,CAAG,wBAA+C,GAEvF,aACA,EACA,IAAgB,EAAqB,CAAG,kBAAH,CAAG,wBAA+C,GAGvF,CAEA,GADA,2BAA+B,6BAAuC,EACtE,mBACA,UACA,WACA,+BACA,KAEA,aACA,4CACA,6DAEA,+BACA,KAEA,aACA,+BACA,KAEA,YACA,4CACA,6DAEA,8BAGA,CAEA,YAEA,CACA,iCACA,mCACA,mCACA,iCAkCA,SAEA,OADA,oBACA,KAEA,aACA,QACA,kBAA+B,mBAAoB,IAAK,kBACxD,mBACA,MAA0B,QAAgB,mBAC1C,0CAAkE,QAAE,kBAAmB,IAAG,GAC1F,+CACA,OACA,CACA,gCACA,MAA6B,QAAgB,UAC7C,wBAAiD,QAAE,SAAa,IAAG,GACnE,sCACA,OACA,CACA,CACA,CAIA,OAFA,8BACA,sBAA4B,QAAmB,IAC/C,KAGA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,YACA,0GACA,cACA,OAAc,EAAgB,kBAC9B,CAAK,mBAEL,OADA,+CACA,CACA,CACA,WACA,uBACA,CACA,SAEA,aAEA,OADA,iBACA,KAEA,WACA,yCACA,CAEA,oBACA,6CACA,4BACA,IAAU,EAAqB,CAC/B,MAAe,QAAY,IADI,CACJ,eAC3B,2BACA,mBACA,CAAO,GACP,MACA,CACA,WACA,YAEA,CCtNA,SACA,uBACA,aACA,eACA,eACA,gBACA,6BACA,CACA,QAAU,GAAU,oBACpB,UAEA,YAEA,OADA,iBACA,KAEA,wBAEA,OADA,+BACA,KAEA,UAEA,OADA,2BACA,OACA,+DAEA,gBACA,SACA,aAA8B,IAAK,iBACnC,6BACA,WACA,KAAyB,QAAE,GAAW,IAAG,QAAmB,IAAK,QACjE,CACA,QACA,CAAK,EACL,cACA,WACA,EACA,aACA,aACA,cACA,GACA,6BACA,wBACA,CACA,UACA,iCAAuE,IAAY,EACnF,IAAS,EAD0E,CAC1E,KAAE,GAAS,IAAG,IAAM,QAAY,YAAY,GAAO,sBAC5D,YACA,sHAGA,sEACA,CACA,CACA,iBAA2B,EAC3B,UADuC,EACvC,eACA,QACA,eACA,eACA,mBAAoB,wDACpB,CACA,QAAU,GAAU,aACpB,MACA,aACA,8BAAuC,IAAK,kBAG5C,OAFA,8BACA,sBAA4B,QAAmB,IAC/C,KAwBA,wBAAiC,EACjC,qBACA,uBAA+B,QAAG,iBAC5B,CACN,SACA,4LACA,cAAsC,QAAG,UAAU,QAAa,QAChE,wBAA+B,QAAG,IAAI,IAAG,QAAmB,GAAG,GAAU,YAEzE,YA+BA,sBACA,wCACA,YACA,+IAGA,cAAoC,QAAG,UAAU,QAAa,SAC9D,gBAAgD,QAAG,UAAU,cAAmB,SAChF,aAA0C,QAAG,UAAU,WAAgB,SACvE,gDAAkE,OAAY,2BAC9E,KAGA,OAFA,4LACA,uBAA6B,QAAG,IAAI,IAAG,QAAmB,GAAG,GAAgB,gBAAgB,EAAO,EAAE,EAAS,EAAE,EAAY,EAC7H,KAGA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,YACA,OAAW,GAAM,4CACjB,oGACA,cACA,OAAgB,EAAgB,kBAChC,CAAO,mBAEP,CACA,WACA,uBACA,CACA,UAEA,YAEA,OADA,iBACA,KAEA,WACW,GAAM,yCACjB,0CAEA,CAEA,oBACA,6CACA,4BACA,IAAU,EAAqB,CAC/B,MAAe,QAAY,IADI,CACJ,eAC3B,2BACA,mBACA,CAAO,GACP,MACA,CACA,WACA,YAEA,CChMA,iBAA2B,EAC3B,UADuC,EACvC,SACA,QACA,eACA,eACA,mBAAoB,aACpB,CACA,QAAU,GAAU,aACpB,OACA,YA8BA,SAEA,OADA,oBACA,KAEA,8BAAuC,IAAK,kBAG5C,OAFA,8BACA,sBAA4B,QAAmB,IAC/C,KAGA,SACA,iDACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,YACA,OAAW,GAAM,4CACjB,oGACA,cACA,OAAgB,EAAgB,kBAChC,CAAO,mBAEP,CACA,WACA,uBACA,CACA,UAEA,YAEA,OADA,iBACA,KAEA,WACW,GAAM,yCACjB,0CAEA,CAEA,oBACA,6CACA,4BACA,IAAU,EAAqB,CAC/B,MAAe,QAAY,IADI,CACJ,eAC3B,2BACA,mBACA,CAAO,GACP,MACA,CACA,WACA,YAEA,CClGA,iBAA6B,IAAG,CAChC,eACA,6DACA,cACA,qBACA,uBACA,uBACA,SACA,UAEA,CACA,IACA,KACA,SAAU,GAAU,oBACpB,qCACA,eACA,wBACA,MAAW,QAAG,yBAAyB,EAAO,EAAE,IAAG,sBAA4B,EAAE,EAAQ,EACzF,CACA,uBACA,MAAW,QAAG,iCAAiC,EAAO,EAAE,IAAG,sBAA4B,EAAE,GAAS,EAGlG,YAEA,OADA,aACA,KAEA,UACA,qEACA,EACA,EAEA,CACA,SACA,0BACA,CACA,WACA,iBACA,IACA,MACA,GAEA,IAEA,MADA,MACA,CACA,EAEA,CACA,CC5CA,SACA,2BACA,kBACA,cACA,qBACA,aACA,mBACA,eACA,cACA,CACA,QAAU,GAAU,6BACpB,YACA,cACA,gBACA,YACA,mBACA,WACA,iBACA,aACA,aACA,KAA0B,CAC1B,OAEA,CACA,aACA,cACA,gBACA,YACA,mBACA,WACA,iBACA,aACA,aACA,GAAiB,cAAsB,CAAI,QAAU,CACrD,QAEA,CACA,CACA,iBAAgC,EAChC,UAD4C,EAC5C,mBACA,QACA,kBACA,cACA,qBACA,aACA,mBACA,eACA,eACA,cACA,WACA,CACA,QAAU,GAAU,sBAEpB,YACA,OAAW,GAAM,6CACjB,UAAc,gBAAoB,cAClC,iCACA,EACA,OACA,EACA,GACA,QACA,YACA,GAAqB,QAAgB,sDAErC,oBACA,KAEA,CACA,EAEA,CAAK,CACL,CACA,WACA,uBACA,CACA,YACA,mDACA,2BACA,mBACA,iCACA,iBACA,6BACA,wBACA,mCACK,CACL,CAEA,SACA,4BAEA,SACA,uBACA,iCACA,aAAa,eACb,CACA,QACA,gCAEA,UAEA,YAEA,OADA,iBACA,KAEA,UACA,OAAW,GAAM,yCACjB,+CAEA,CACA,CClHA,iBAAoB,EACpB,UADgC,EAChC,SACA,QACA,eACA,WACA,aACA,qBACA,CACA,QAAU,GAAU,UAEpB,SACA,gBAEA,WACA,kBAEA,eACA,iCACA,CACA,WACA,YAGA,wBACA,QACA,CACA,CCzBA,iBAAwC,EACxC,UADoD,EACpD,OACA,QACA,eACA,eACA,aAAoB,OACpB,CACA,QAAU,GAAU,8BACpB,OACA,eACA,mCACA,+DAGA,OADA,4BACA,IACA,CACA,aACA,qCACA,+DAGA,OADA,0BACA,KAGA,SACA,kEACA,CACA,QACA,IAAY,gBAA6B,uCACzC,QACA,CAEA,YACA,OAAW,GAAM,4CACjB,8EAEA,CACA,WACA,uBACA,CACA,UAEA,YAEA,OADA,iBACA,KAEA,WACW,GAAM,yCACjB,0CAEA,CCtCA,SACA,mBAeA,GAdA,eACA,eACA,UACA,gBACA,wBACA,8BACA,SACA,EAAM,CACN,cACA,aAAoB,CACpB,gBAAuB,CACvB,SACA,EACA,cACA,cACA,6CACA,kBAAoC,GACpC,aACA,cACA,qBACA,gBACA,EACA,EACA,EAIA,cAAoB,qBACpB,EACA,CACA,QAAU,GAAU,cACpB,OAiCA,cACA,WAeA,OAAa,GAdb,IACA,sBACA,SAAoB,GAAY,aAEhC,UACA,IAAY,GAAY,CACxB,WACA,sDAAiF,GAAI,CACrF,EACA,IAEA,IAAY,EAAqB,OAAG,YAAH,SAAG,4BAA0D,GAGjF,CACb,EACA,YACA,WAAe,GAAc,QAAG,GAAH,OAAG,uBAAwC,CACxE,CACA,OAoBA,WACA,WAoCA,OAAa,OAnCb,YACA,WAAiB,EAAe,CAChC,YADgC,GAChC,EACA,kBACA,kBACA,UACA,CAAO,CACP,EA4Ba,eA3Bb,YACA,WAAiB,EAAe,CAChC,YADgC,GAChC,EACA,kBACA,kBACA,WACA,WACA,CAAO,CACP,EAmBa,iBAlBb,cACA,WAAiB,EAAe,CAChC,YADgC,GAChC,EACA,kBACA,kBACA,WACA,aAAoB,EACpB,CAAO,CACP,EAUa,OATb,YACA,WAAiB,GAAe,wBAChC,EAOa,OANb,YACA,WAAiB,GAAe,wBAChC,EAIa,OAHb,YACA,WAAiB,GAAY,wBAC7B,CACa,CACb,CACA,UACA,WAAe,EAAe,CAC9B,YAD8B,GAC9B,EACA,qBACA,qBACK,CACL,CACA,kBACA,WAAe,EAAe,CAC9B,YAD8B,GAC9B,EACA,qBACA,qBACA,WACA,CAAK,CACL,CACA,sBACA,WAAe,EAAe,CAC9B,YAD8B,GAC9B,EACA,qBACA,qBACA,aAAkB,EAClB,CAAK,CACL,CA4BA,UACA,WAAe,GAAe,4BAC9B,CAyBA,UACA,WAAe,GAAe,4BAC9B,CAyBA,UACA,WAAe,GAAY,4BAC3B,CACA,2BACA,WAAe,GAAyB,4BACxC,CACA,UACA,WACA,yBAA+C,IAAG,mBAClD,6BACA,4BACA,EACA,OACA,OACA,IAEA,WAAe,GACf,EADoB,EACpB,iCACA,EACA,EACA,qBAEA,CACA,iBACA,oCACA,CACA,CClSA,SACA,QAAU,GAAU,SACpB,CACA,oBACA,WACA,WACA,CACA,QAAU,GAAU,oBACpB,OAEA,CACA,mBACA,CACA,kBACA,CACA,CACA,uBACA,SAAwB,EAAI,GAAG,kBAAuB,EAEtD,EADA,kBACA,UAIA,MAFA,mBADA,yCACA,CACA,+CAEA,CCnBA,SACA,qBACA,aACA,aACA,qBACA,mBACA,qCACA,mBAA2B,8BAE3B,0BACA,yBAEA,CACA,UACA,WACA,kBAEA,eACA,QACA,CAEA,YAEA,OADA,iBACA,KAEA,QAAU,GAAU,oBAEpB,0BAEA,sBACA,wBAAiC,QAAE,YAAa,KAAS,6BAOzD,2CANA,IACA,gBACA,CAAQ,SACR,UAAkB,EAAiB,MACnC,CASA,QAVmC,OAUnC,qIACA,IACA,0BACA,IACA,qBAAgC,iCAAmC,EACnE,EACA,QACA,CAAQ,SACR,UAAkB,EAAiB,MACnC,CAEA,qBACA,IACA,gBACA,CAAQ,SACR,UAAkB,EAAiB,MACnC,CAEA,QAHmC,MAGnC,yBACA,2BACA,4BAAuC,GAAS,KAChD,CADgD,GAChD,sBACA,8BACA,iCAEA,eACA,MACA,IACA,WACA,CAAU,SACV,UAAoB,EAAiB,MACrC,CASA,OARA,CAFqC,KAErC,eACA,4BAAyC,GAAS,KAClD,CADkD,CAGlD,6DACA,8BACA,yBAEA,CACA,CACA,QACA,CACA,IACA,gBACA,CAAM,SACN,UAAgB,EAAiB,MACjC,CACA,CACA,CACA,SACA,eACA,cACA,CACA,QAAU,GAAU,cAEpB,aACA,OAAW,GAAM,yCASjB,EARuB,CAAM,4CAC7B,kBACA,2BACA,OACA,OACA,KAGA,8BAEA,CACA,OACA,yBACA,2BACA,OACA,OACA,IACA,KACA,CAEA,iBAEA,cADA,yBAEA,UAEA,CACA,CACA,MAAM,WAAsB,GAC5B,OADsC,KACtC,WACA,aACA,cACA,kBACA,CACA,QAAU,GAAU,kBACpB,WACA,UAAc,CACd,CAEA,sBAHsC,EAGtC,GACA,SAUA,OATA,kBACA,0BAAqC,iBAAsB,GAE3D,cACA,qBAEA,gCACA,mDAEW,IAAG,iBACd,CACA,kBACA,4BAAgC,QAAG,mBAAmB,gCAAqC,EAC3F,CACA,CC5JA,QACA,aACA,cACA,EACA,IACA,aACA,cACA,CACA,kBAAoC,GACpC,YADmD,CACnD,kBACA,eACA,cACA,cACA,cACA,8BACA,0BACA,2BACA,CACA,QAAU,GAAU,0BACpB,kBAEA,YAAsC,mBACtC,MAAmB,QAAgB,sBACnC,uCACA,WAAY,8CAAiD,KAC7D,UACA,4CACA,EACA,MACA,EACA,eACA,MACA,WACA,IAIA,iDACA,QACA,MACA,EACA,eACA,MACA,WACA,IAGA,wBACA,CACA,aACA,0CACA,SAEA,oBACA,wBACA,2BAEA,SAA6B,QAAY,yCACzC,CACA,QAA4B,EAC5B,MAAmB,QAAgB,sBAEnC,OADA,uCACA,iBACA,eACA,EACA,4BACA,MACA,2BAEA,eACA,CAEA,WAA+B,IAC/B,MAAmB,QAAgB,sBAEnC,OADA,uCACA,mCAAsD,wCAAsD,iBAC5G,CAEA,wBACA,mCAEA,CACA,iBAA8B,GAC9B,MADuC,MACvC,UAAmD,EACnD,SACA,cACA,cACA,eACA,4BACA,0BAAwC,EACxC,QADkD,EAClD,cAAsC,EACtC,CACA,MAF+C,CAE/C,CAAU,GAAU,oBACpB,YACA,OACA,MACA,4BACA,cACA,YACA,EACA,YACA,WACA,EACA,EACA,EACA,EACA,EAEA,CACA,eACA,SACA,KACA,gBACA,mBACA,eACA,UACA,OACA,iCACA,eACA,mCACA,CAAS,EAET,CAEA,OADA,qCACA,gCACA,CAEA,iBAGA,OAFA,0BACA,4BAA2D,4BAAoC,CAE/F,CAEA,wBACA,6BAA6C,4BAAqC,CAClF,CAEA,iBAEA,cADA,0BAEA,cAEA,CACA,wBAA8C,EAC9C,0DACA,CACA,CACA,iBAA8B,KAC9B,QAAU,GAAU,EAAI,IACxB,sBACA,IAF6C,EAE7C,oDACA,CACA,CCvJA,SACA,oBAA2C,EAC3C,cACA,eACA,eACA,kBACA,CACA,QAAU,GAAU,mBACpB,iBACA,WAAe,GAAe,4BAC9B,2BACA,yBACK,CACL,CACA,cACI,IAAK,eAAe,IAAK,4BACzB,IAAK,eAAe,IAAK,0BACzB,IAAK,eAAe,IAAK,qBACzB,IAAK,eAAe,IAAK,yBACzB,IAAK,0BACL,IAAK,0BACL,IAAK,0BACL,IAAK,0BACL,IAAK,yBACT,CACA,CACA,qBACA,oBACA,SACA,iBACA,uDACA,YACA,yBACA,aACA,aACA,qBAIA,MAHA,6EACA,cAEA,QACA,CACA,CAAO,CACP,CACA,CAAG,CACH,CACA,iBAA+B,GAC/B,OADyC,CAC/B,GAAU,qBACpB,aAEA,OADA,iBACA,mBACA,WACA,mBAEA,EAEA,CACA,eACA,4BACA,CACA,CACA,kBAAsC,EACtC,IACA,EAMA,EAPA,MAAsB,EAAS,CAAG,MAAH,CAAG,SAAuB,EAQzD,GANA,cACA,MAAiB,EACb,WAD0B,EAC1B,EACJ,aAGA,UACA,MAAyB,QAA6B,CACtD,SACM,IAA2B,EAEjC,GACA,oBACA,gBACA,8BAEA,CAEA,UADA,eAAuD,gBAA6B,EACpF,iBACA,SACA,EACA,EACA,GAOA,OALA,YACA,iBACA,UACA,wCAEA,CACA,CACA,kBACA,yBAEA,UADqB,QAAI,OACzB,MAEA,GAAM,QAAQ,QACd,IAAY,4BAAuC,KACnD,oBACA,uBACA,qBAAc,QAA+B,EAE7C,UADwB,QAAI,MAC5B,EACA,CAEA,UADqB,QAAI,IACzB,EACA,CACA,oBACA,CAMC,YAA0B,EAD3B,KAHA,YACA,YAAuB,GACvB,0GCvHA,iBAAgC,GAAsB,CACtD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,cACA,CACA,4BACA,mBACA,EAEA,SACA,CACA,CACA,gBAAgC,GAAsB,CACtD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,cACA,CAEA,sBACA,gBACA,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,YACjD,kBACA,SAEA,QACA,CCpDA,gBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,kCACA,0BACA,sBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,iBACA,CACA,4BACA,mBACA,EAEA,SACA,CACA,CACA,gBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,kCACA,yBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,iBACA,CAEA,sBACA,gBACA,CACA,CACA,gBACA,IAAU,iBAAiB,QAAsB,YACjD,kBACA,SAEA,QACA,eCzDA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,KACA,2BACA,4BACA,8BAGA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,0BACA,kCACA,aACA,2CAAqD,YAAY,EACjE,CACA,CACA,SAAS,EAAI,MAAU,CAAV,CACb,SAAU,YAAe,CAAE,OAAsB,MACjD,iBACA,CC3BA,gBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,GACA,0BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,CACA,cACA,mBACA,CCjBA,gBAAoC,IAAe,CACnD,QAAU,GAAU,sCACpB,OACA,mCACA,0BACA,8BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA6B,IAAQ,CACrC,QAAU,GAAU,mBACpB,QACA,KACA,qBACA,KACA,WACA,wDACA,uCACA,2CAEA,aACA,oBAEA,sBACA,uDACA,CACA,oBACA,mDACA,CACA,CACA,cACA,cACA,SAAY,YAAe,CAAE,OAAsB,MACnD,mBACA,CACA,cC1CA,iBAAuC,IAAe,CACtD,QAAU,GAAU,yCACpB,GACA,qCACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAgC,IAAQ,CACxC,QAAU,GAAU,sBACpB,aACA,wBACA,CACA,4BACA,mBACA,qBAEA,CACA,CACA,CACA,cACA,mBACA,CC3BA,gBAA4B,IAAe,CAC3C,QAAU,GAAU,iBACpB,gBACA,0BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,aACA,YACA,CACA,CACA,cACA,mBACA,eCjBA,iBAAgC,IAAe,CAC/C,QAAU,GAAU,kCACpB,KACA,+BACA,4BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,yCACA,8CACA,cACA,sBAAqC,YAAY,KACjD,qBAA2C,eAAe,MAC1D,iBAAsB,EAAO,EAAE,EAAU,EAEzC,CACA,iBAA2B,EAC3B,SAAU,YAAe,CAAE,OAAsB,MACjD,iBACA,0BCxBA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,iBACpB,gBACA,yBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA0B,IAAQ,CAClC,QAAU,GAAU,WACpB,aACA,YACA,CACA,sBACA,oCACA,sEACA,CACA,oBACA,QAAa,EAAE,KAAS,GAAG,KAAS,GAAG,MAAU,EAEjD,CACA,gBAA+B,IAAe,CAC9C,QAAU,GAAU,oBACpB,gBACA,2BACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,YACA,CACA,sBACA,oCACA,OAAa,qEACb,CACA,oBACA,QAAa,EAAE,IAAQ,GAAG,IAAQ,GAAG,KAAS,CAC9C,CACA,CACA,gBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,0BAGA,SAFA,QAGA,CC3DA,gBAA+B,IAAe,CAC9C,QAAU,GAAU,iCACpB,GACA,6BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,eACA,CACA,CACA,cACA,mBACA,CClBA,gBAAgC,IAAe,CAC/C,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,gBACA,CACA,CACA,cACA,mBACA,eCjBA,iBAAkC,IAAe,CACjD,QAAU,GAAU,oCACpB,GACA,+BACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,aACA,aACA,CACA,sBACA,uBACA,kCACA,kDAEA,gBAEA,oBACA,UAAe,KAAS,GAAG,KAAS,GAEpC,CACA,iBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,+BACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA4B,IAAQ,CACpC,QAAU,GAAU,iBACpB,cACA,aACA,CACA,sBACA,uBACA,kCACA,OAAe,8CACf,CACA,QACA,CACA,oBACA,UAAe,IAAQ,GAAG,IAAQ,GAElC,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,0BAGA,UAFA,QAGA,CC5DA,iBAEA,mBADA,oBAEA,YAAkB,IAAO,IACzB,qBAEA,yBACA,CACA,eACA,MAhBA,YACA,SACA,YAAkB,WAAgB,KAClC,2CAEA,wBACA,EAUA,GACA,IACA,IADA,EACA,CACA,KACA,6BACA,uBAOA,GANA,KAEA,eACA,qBACA,MAEA,cACA,cAEA,OADA,MAGA,OADA,KACA,MAEA,wCACA,CChCA,iBAAgC,IAAe,CAC/C,QAAU,GAAU,qBACpB,gBACA,6BACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,uBACA,CACA,sBACA,OAAW,GAAS,EACpB,CACA,GAFoB,cAEpB,GACA,eAAoB,MAAU,EAAE,KAAS,GAEzC,CACA,iBAAsC,IAAe,CACrD,QAAU,GAAU,2BACpB,gBACA,kCACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA+B,IAAQ,CACvC,QAAU,GAAU,qBACpB,aACA,uBACA,CACA,sBACA,MAAmB,GAAS,GAC5B,GAD4B,GAC5B,CAAa,cACb,CACA,oBACA,eAAoB,KAAS,EAAE,IAAQ,GAEvC,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,aACjD,0BAGA,UAFA,SAGA,CC3DA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,KACA,2BACA,oBACA,CAEA,SACA,4BACA,CACA,CACA,iBAAqB,IAAQ,CAC7B,QAAU,GAAU,uBACpB,KACA,UACA,CACA,aACA,YACA,CACA,sBACA,mBACA,qBAEA,CACA,CAEA,eACA,oBACA,gBC3BA,kBAAgC,GAAsB,CACtD,QAAU,GAAU,kCACpB,GACA,8BACA,CAEA,SACA,4BACA,CACA,CACA,iBAAyB,IAAQ,CACjC,QAAU,GAAU,eACpB,aACA,gBACA,CACA,sBACA,mBACA,UAEA,CACA,CAEA,eACA,oBACA,CCzBA,iBAAmC,IAAe,CAClD,QAAU,GAAU,qCACpB,GACA,kCACA,0BACA,sBACA,CAEA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA4B,IAAQ,CACpC,QAAU,GAAU,kBACpB,aACA,mBACA,CACA,CACA,eACA,oBACA,+DCtBA,kBAAoC,IAAe,CACnD,QAAU,GAAU,sCACpB,KACA,mCACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA6B,IAAQ,CACrC,QAAU,GAAU,kBACpB,mCACA,aACA,aAAkB,gBAAgB,GAElC,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CCxBA,iBAAkC,IAAe,CACjD,QAAU,GAAU,oCACpB,KACA,gCACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA2B,IAAQ,CACnC,QAAU,GAAU,gBACpB,mCACA,aACA,iBAAsB,gBAAgB,EACtC,CACA,oBACA,wBACA,CACA,sBACA,4DACA,CACA,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CC9BA,iBAAoC,IAAe,CACnD,QAAU,GAAU,yBACpB,kBACA,mCACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAA6B,IAAQ,CACrC,QAAU,GAAU,mBACpB,kCACA,aACA,mBAAwB,gBAAgB,GAExC,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CCxBA,iBAA8B,IAAe,CAC7C,QAAU,GAAU,mBACpB,kBACA,4BACA,oCAGA,SACA,cACA,EACA,YAEA,CACA,CACA,iBAAuB,IAAQ,CAC/B,QAAU,GAAU,aACpB,kCACA,aACA,gBAAqB,gBAAgB,EACrC,CACA,oBACA,wBACA,CACA,sBACA,4DACA,CACA,CACA,iBACA,SAAU,YAAe,CAAE,OAAsB,MACjD,kBACA,CE9BA,iDACA,kCACA,kBAAsB,IAAK,CAC3B,QAAU,GAAU,mBAEpB,uBAAkC,CAAE,IAAK,SACzC,qBACA,YACA,CAAG,GAEH,QAEA,QAEG,IAAK,oCAEL,IAAK,+BAmCR,gBAjCA,uBACA,oBACA,yBDUA,CACA,KCXgE,CDWtD,GACV,SAAa,GACb,GCbmF,IDaxE,MACX,IAAQ,GACR,IAAQ,GACR,UAAc,GACd,IAAQ,MACR,eAAmB,GACnB,IAAQ,GACR,OAAW,MACX,QAAY,GACZ,IAAQ,MACR,KAAS,MACT,IAAQ,GACR,OAAW,GACX,QAAY,GACZ,OAAW,MACX,KAAS,IACT,QAAY,IACZ,IAAQ,IACR,MAAU,OACV,QAAY,IACZ,WAAe,IACf,IAAQ,OACR,IAAQ,OACR,SAAa,OACb,IAAQ,OACR,OAAW,OACX,GAAO,IACP,OAAW,IACX,SAAa,IACb,MAAU,GACV,GC3CmF,EACnF,qBACA,gCAEA,aACA,MAFA,EAEA,SAEA,OADA,cAHA,EAGA,uBACA,MACK,GAEL,qBACA,gCACA,EACA,WAEA,GADA,4BACA,IAGA,qBAMA,OALA,EAAQ,IAAK,mBACb,EAAQ,IAAK,8BACb,GACA,oCAEA,iBACA,eACA,0BACA,EAEA,CAAG,EACH,EAEA,yECrDA,iBAAqC,IAAe,CACpD,QAAU,GAAU,2BACpB,6BACA,MACA,SAAc,QAAmB,CACjC,gCACA,cACA,eACA,iBACA,CACA,EAAM,IACN,+BACA,aACA,EAIA,OAFA,0BACA,uBACA,KAEA,gCACA,MACA,SAAc,QAAmB,CACjC,gCACA,iBACA,eACA,iBACA,CACA,EAAM,IACN,+BACA,gBACA,EAIA,OAFA,0BACA,uBACA,IACA,CACA,yECnCA,iBAA4B,IAAe,CAC3C,QAAU,GAAU,8BACpB,KACA,2BACA,8BAGA,SACA,2BACA,CACA,CACA,gBAAqB,IAAQ,CAC7B,QAAU,GAAU,WACpB,kCACA,aACA,YACA,CACA,CACA,iBAAuB,EACvB,SAAU,YAAe,CAAE,OAAsB,MACjD,iBACA,8DCtBA,iBAA+B,IAAe,CAC9C,QAAU,GAAU,oBACpB,gBACA,8BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,aACA,eACA,CACA,CACA,cACA,mBACA,yECjBA,iBAA+B,IAAe,CAC9C,QAAU,GAAU,oBACpB,kBACA,8BACA,4BACA,8BAGA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,cACpB,yBACA,mCACA,aACA,iDAA2D,YAAY,EACvE,CACA,CACA,iBAA0B,EAC1B,IAAU,iBAAe,CAAE,OAAsB,MACjD,iBACA,uEC3BA,iBAA6B,IAAe,CAC5C,QAAU,GAAU,+BACpB,GACA,yBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAsB,IAAQ,CAC9B,QAAU,GAAU,wBACpB,KACA,UACA,CACA,aACA,aACA,CACA,oBACA,wBACA,CACA,sBACA,sBACA,IACA,oBACA,CAAQ,MAER,CAEA,QACA,CACA,CACA,cACA,mBACA,0CCeA,gBACA,4CACA,sBACA,SAEA,qBAQA,MAPQ,QAAE,GAAQ,GAAM,GAAK,QAAE,GAAQ,IAAG,GAAK,QAAE,GAAQ,IAAG,UAC5D,QAAoB,eAAsB,EAC3B,QAAE,GAAQ,IAAK,EAC9B,cAA+C,IAAK,qBAEpD,kBAEA,CACA,CAAG,IACH,wIA5DA,kBACA,SACA,WACA,SAAgB,UAAa,UAC7B,EAEA,EADU,QAAE,GAAQ,GAAM,EAC1B,EACiB,QAAE,GAAQ,IAAG,EAC9B,UAEA,cAEA,QACA,2BACA,gBACA,QACA,UAEA,WACU,CACV,WACA,6CACA,MAAqC,QAAE,GAAQ,GAAM,iBACrD,WACA,OAEc,8BAAkF,QAAY,WAC5G,UAFA,eAAwD,QAAY,SAIpE,CACA,CAEA,QACA,CAAK,CACL,IAEA,8BACA,iCACA,0BACA,YAIA,QACA,CAiBA,gBACA,qBACA,iBACA,uBACA,SAEA,2BACA,YACA,SAGA,QACA,CACA,gBACA,gEACQ,QAAE,GAAQ,IAAG,GAAK,QAAE,GAAQ,GAAM,EAC1C,MAEA,OAAuB,IAAK,KAAc,IAAK,uBAG/C,gBACA,gCAEA,4BACA,CACA,gBACA,eACA,qDACA,mBACA,sBACA,YACA,EACA,oEAIA,CACA,cACA,SAAe,IAAK,iBAKpB,cACA,MAAS,QAAE,GAAQ,GAAQ,YAAoB,QAAE,GAAQ,IAAI,IAAU,GAAc,OAAS,QAAE,GAAQ,IAAG,WAAmB,IAAK,mBAAyB,IAAK,gBAAsB,IAAK,kBAE5L,gBACA,OACA,yCACA,6BACA,CACA,CAGA,cACA,iCACA,8BADA,SAEA,iBACA,4BACA,kFAEA,CACA,iBACA,4BACA,6BAEA,CACA,iBACA,4BACA,6BAEA,CACA,oBACA,4DAGA,qBACA,gCACA,2CAEA,CACA,iBACA,4BACA,6CAEA,QACA,6DC1JA,gIEmQA,2BACA,SACA,QACA,EACA,EACA,eACA,aACA,2BACA,OACA,oCACA,YAAoC,QAAE,WACtC,EACA,wBACA,EACA,YACA,GACA,MACA,KACA,EACA,wBACA,EACA,YACA,GAGA,EAAM,IACN,IAEA,EAFA,UACA,UAGA,EADU,QAAE,GAAQ,GAAM,EAC1B,EACiB,QAAE,GAAQ,IAAG,EAC9B,UAEA,cAEA,gDACA,CAEA,QACA,qEDnSA,SACA,QAAU,GAAU,wBAEpB,QAEA,iBACA,KACA,eACA,WACA,CAEA,SACA,sCACA,CACA,CACA,QACA,mBACA,aACA,eACA,WACA,CACA,QAAU,GAAU,iBACpB,QACA,KACA,UACA,qBAA2B,WAAW,IAAO,cAAc,GAAG,sCAAoD,KAElH,qCCLA,SACA,mBACA,mBACA,uBACA,oBACA,2BAA+C,IAAK,cAEpD,QAAU,GAAU,aACpB,oBACA,UAEA,QACA,iBACA,aACA,aACA,CACA,QAAU,GAAU,cAEpB,kBACA,qBACA,2BACA,cACA,iBACA,CACA,QAAU,GAAU,QACpB,iBACA,YACA,iBACA,qBACA,YACA,iBAGA,OADA,cACA,CACA,CACA,CACA,kBACA,mBACA,2BACA,aACA,CACA,QAAU,GAAU,SACpB,iBACA,YACA,iBACA,qBACA,aAGA,OADA,cACA,CACA,CACA,CACA,aACA,OACA,GAAO,MACP,OAAW,MACX,EAAM,MACN,MAAU,MACV,EAAM,MACN,GAAO,MACP,KAAS,MACT,OAAW,MACX,MAAU,MACV,SAAa,MACb,IAAQ,MACR,EAAM,MACN,GAAO,MACP,EAAM,MACN,GAAO,MACP,UAAc,MACd,SAAa,MACb,OAAW,MACX,QAAY,MACZ,UAAc,MACd,EAAM,MACN,GAAO,MAEP,CACA,aACA,OACA,GAAO,MACP,GAAO,KACP,IAAQ,KAER,CACA,gBACA,2CAAkE,QAAE,WAAoB,IAAK,GAC7F,cAEA,SACA,KACA,KACA,iCACA,GAAQ,QAAE,GAAQ,IAAK,GACvB,MAAqB,QAAkB,IACvC,OAUA,aATA,OACA,MACA,SACA,SAAsB,IAAK,cAC3B,SAAsB,IAAK,gBAC3B,UAAuB,IAAK,iBAC5B,0BAAqD,CACrD,8BAEA,cACA,EAAc,IAAK,mBAEnB,WACA,wBAGA,QAAgC,IAAK,gCAAoC,IAAK,6BAC9E,KACA,8BACc,QAAE,GAAc,IAC9B,aAD+C,EAC/C,mBAIA,EAAM,OAAS,QAAE,OACjB,IAKA,EALA,EAAqB,QAAkB,UACvC,OAKA,8BAJA,SACA,aAIA,MACA,UACA,kBACA,GACA,uBAEA,EAAU,IACV,QACA,OACA,YAA2B,CAC3B,YACA,GAEA,mBAGA,CAEA,OAAW,yBACX,CACA,gBACA,aACA,EACA,sBACA,mCACA,EACA,mBACA,GAGA,CAgBA,kBACA,GAAM,QAAE,gBACR,OACA,uBACA,gCAGA,QAA8C,QAAkB,qBAChE,MACA,YACA,UAAgB,kBAAyB,IAAK,cAAc,wBAG5D,WACA,MACA,sBAA8B,EAAsB,wBAEpD,oBACA,IAA0C,QAAkB,KAC5D,MACA,YACA,UAAgB,EAAY,IAAK,cAAc,wBAG/C,SACA,2BACA,aAEA,8GACA,UAGA,cACA,2BACA,2CAAiD,eAAsB,cAAc,EAAsB,IAC3G,MACA,yCAA+C,EAAsB,SAAS,cAAqB,IAAK,cAAc,kCAGtH,SAA6B,QAAE,sBAC/B,OACA,8BACA,8BAGA,aACA,sDAA0D,EAAkB,GAAG,YAAmB,GAElG,CACA,cACA,OACA,IAjEA,cACA,aAgEA,EA9DA,EACA,EACA,6CAEA,EA2DA,KAxDA,cACA,aAuDA,EAvDA,IACA,CAuDA,CACA,yQCrPA,sBACA,CAAM,QAAoB,KAAa,QAAY,KAAY,QAAE,GAAQ,IAAK,GAAM,QAAE,GAAQ,IAAW,GAAM,QAAE,GAAQ,GAAM,GAAM,QAAE,GAAQ,IAAK,GAAM,QAAE,GAAQ,IAAI,EAGxK,EAFA,IAAe,IAAK,KAGpB,CACA,aACS,QAAG,GAAG,GAAM,IAAI,OAAyB,EAElD,SACS,QAAG,GAAG,GAAM,KAAK,OAAyB,EAEnD,iBACA,eACA,eAEA,2BAIe,IAAG,CADlB,aACkB,EAEF,CAChB,IAAQ,IAAW,MACf,IAAG,YAAsB,IAAW,WACxC,IAAQ,IAAW,MACnB,CACA,CACA,iBACA,eACA,eAEA,2BAIe,IAAG,CADlB,aACkB,EAEF,CAChB,IAAQ,IAAW,MACf,IAAG,YAAsB,IAAW,UACxC,IAAQ,IAAW,MACnB,CACA,CACA,cACA,MAAS,QAAG,OAAO,EAAU,EAE7B,aACS,QAAG,GAAG,GAAM,IAAI,OAAyB,EAElD,SACS,QAAG,GAAG,GAAM,KAAK,OAAyB,EAEnD,SACS,QAAG,GAAG,GAAM,IAAI,OAAyB,EAElD,SACS,QAAG,GAAG,GAAM,KAAK,OAAyB,EAEnD,uBACA,iBACA,aACa,QAAG,QAEL,QAAG,GAAG,GAAQ,KAAK,iBAA0C,EAE/D,QAAG,GAAG,GAAQ,KAAK,OAA4B,EAExD,uBACA,iBACA,aACa,QAAG,OAEL,QAAG,GAAG,GAAQ,SAAS,iBAA0C,EAEnE,QAAG,GAAG,GAAQ,SAAS,OAA4B,EAE5D,cACA,MAAS,QAAG,GAAG,GAAO,SAEtB,cACA,MAAS,QAAG,GAAG,GAAO,aAEtB,cACA,MAAS,QAAG,UAAU,EAAS,EAE/B,cACA,MAAS,QAAG,cAAc,EAAS,EAEnC,kBACA,MAAS,QAAG,GAAG,GAAQ,UAAU,QAA0B,MAAM,EACjE,EACA,GACI,EAEJ,kBACA,MAAS,QAAG,GAAG,GAAQ,cAAc,EACrC,EACA,GACA,CAAK,MAAM,OAAyB,EAEpC,gBACA,MAAS,QAAG,GAAG,GAAQ,OAAO,EAAM,EAEpC,gBACA,MAAS,QAAG,GAAG,GAAQ,WAAW,EAAM,EAExC,gBACA,MAAS,QAAG,GAAG,GAAQ,QAAQ,EAAM,EAErC,gBACA,MAAS,QAAG,GAAG,GAAQ,YAAY,EAAM,wEC1HzC,iBAAwC,IAAe,CACvD,QAAU,GAAU,0CACpB,KACA,uCACA,kBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAAiC,IAAQ,CACzC,QAAU,GAAU,uBACpB,IACA,oDACA,KACA,WACA,iBAEA,aACA,0BAEA,CACA,qCACA,cACA,mDAEA,gBAAkC,IAAe,CACjD,QAAU,GAAU,oCACpB,KACA,iCACA,kBACA,CAEA,SACA,aACA,EACA,YAEA,CACA,CACA,gBAA2B,IAAQ,CACnC,QAAU,GAAU,iBACpB,sBACA,mDACA,KACA,WACA,iBAEA,aACA,0BAEA,CACA,gBACA,iCAEA,OACA,oBACA,kBACA,CACA,WACA,aACA,SACA,MACA,GAEA,QACA,EAbA,0BAcA,OACA,oBACA,kBACA,CACA,WACA,4BACA,SACA,MACA,GAEA,QACA,EAzBA,WACA,0DC3DA,SACA,QAAU,GAAU,yBACpB,iBACA,QACA,iBACA,MACA,iBACA,QACA,SACA,YACA,CACA,CAIA,CACA,kBACA,QAAU,GAAU,6DClBpB,uCAEA,gBACA,0BACA,SAEA,kBACA,SAEA,8CACA,YACA,UAAgB,oBAAyB,gIAGzC,2CACA,KACA,SACA,uBACA,SAEA,0BACA,CAEA,QACA,CAvBA,gGCAA,cACA,MAAS,QAAG,GAAG,GAAQ,IACvB,CACA,cACA,MAAS,QAAG,GAAG,GAAQ,4BEHvB,EACA,kCACA,OACA,uBACA,GAGA,GACA,mCDTA,CCSsD,QAAU,EAErD,OAAI,CACf,yBACA,EACA,IACA,IACA,WACA,CAAY,SAMZ,MALA,aACA,4BACA,oDAEA,CAAa,EACb,CACA,EAAY,OACZ,OACA,CACA,GAEA,EACA,IAxBA,GA2BA,yEC/BA,iBAA+B,GAAsB,CACrD,QAAU,GAAU,iCACpB,GACA,6BACA,CAEA,SACA,2BACA,CACA,CACA,gBAAwB,IAAQ,CAChC,QAAU,GAAU,aACpB,cACA,eACA,CACA,4BACA,mBACA,mBAEA,CACA,CACA,CACA,cACA,mBACA,2GCzBA,mCACA,gCACA,2CACA,qCACA,iCACA,gCACA,2CACA,sCACA,SACA,QAAU,GAAU,iBAEpB,QACA,KAAU,GAAS,CACnB,SACA,eACA,UACA,qBACA,WACA,UACA,oBACA,GAKG,GAAS,GAKZ,IAEA,IAEA,IAEA,IAKA,IAEA,OAEA,OAEA,sBACA,OACA,KAAS,GAAS,YAClB,UACA,SACA,CACA,CAIA,cACA,SAAe,GAAS,EAExB,cACA,SAAY,eAA0B,GAAG,EAAM,GAAS,EAAE,+DC7D1D,iBAA8B,IAAe,CAC7C,QAAU,GAAU,gCACpB,GACA,6BACA,0BACA,sBACA,CAEA,SACA,2BACA,CACA,CACA,gBAAuB,IAAQ,CAC/B,QAAU,GAAU,aACpB,aACA,cACA,CACA,CACA,cACA,mBACA,4DCrBA,SACA,QAAU,GAAU,kBACpB,mBACA,OACA,aACA,OACA,iBACA,WACA,eACA,cACA,cACA,YACA,kBACA,kBACA,WACA,aACA,gBACA,CACA,CAYA,QACA,YAOA,UAEA,OADA,uBACA,KASA,WAGA,OAFA,sBACA,0BACA,IACA,CAOA,cAGA,OAFA,wBACA,0BACA,KAKA,yBAQA,eAGA,OAFA,yBACA,0BACA,KAKA,2BAMA,aAGA,OAFA,0BACA,uBACA,KAGA,WACA,uBACA,oBACA,CACA,0BCnGA,SACA,QAAU,GAAU,wBAEpB,UAEA,sBAEA,qBACA,kBACA,oBACA,SAAc,8BAAgC,IAC9C,YAAe,qDACf,EACA,IACA,0BACA,0BAEA,CACA,YAEA,OADA,wCACA,KAEA,YAEA,OADA,wCACA,KAGA,SACA,oBACA,CACA,CACA,QACA,iBACA,aACA,2BACA,0BACA,0BAEA,QAAU,GAAU,iBACpB,UACA,QACA,SACA,WACA,SAAY,8BAAgC,iBAC5C,mBACA,mBACA,GACA,WAAiB,GAAS,KAC1B,EACA,WAA8B,GAAS,KACvC,EACA,CACA,aAAsB,YAAiB,KAEvC,gBCnDA,gBACA,SAAY,EAAM,GAAS,EAAE,GAAG,YAAkB,SAElD,QACA,iBACA,YACA,cACA,CACA,QAAU,GAAU,EAAI,IAExB,SAEA,0BACA,mBAEA,OADA,+BACA,KAGA,SACA,kEACA,CACA,CACA,QACA,QAAU,GAAU,EAAI,IAExB,kBACA,GACA,WACA,CACA,CANqD,EAMrD,MACA,yBACA,CACA,CACA,QACA,qBACA,aACA,eACA,uDACA,uBACA,CACA,QAAU,GAAU,EAAI,IAAoB,CAC5C,QACA,KACA,WAH4C,MAG5C,GACA,UACA,gBACA,CACA,CCpDA,kBACA,YAA0B,WAAwB,KAClD,WACA,aACA,IACA,QACA,CACA,WACA,2CAEA,OAGA,iBAAmC,EACnC,yCAGA,8CCVA,gBAA8B,EAC9B,WAD2C,OAC3C,UACA,CAAU,GAAU,oBACpB,SACA,qCACA,CACA,iBAA8B,EAE9B,OADA,iCAAkC,YAAc,EAChD,IACA,CACA,YAIA,OAHA,wBACA,yBACA,gCACA,KAEA,qBAMA,OALA,uBACA,KACA,cACA,aACA,EACA,IACA,CAEA,sBACA,oCAAyC,gBAAc,GAC1C,OAAI,CACjB,QACA,UAA8B,EAAiB,IAE/C,EAAqB,SAF0B,EAE1B,iBADrB,IACqB,IAQrB,OANA,YACA,uBAEA,YACA,uBAEA,UACA,CAAS,CACT,EACA,GAGA,CAEA,0BACA,2BACA,CACA,CACA,gBAAuB,GAAM,CAC7B,iBACA,cACA,cAA0B,EAAa,aAEvC,WACA,YACA,CACA,QAAU,GAAU,aAEpB,kBACA,QAAU,GAAU,sBACpB,aACA,wBACA,CACA,aACA,+BACA,gCACA,4BACA,CACA,eACA,YACA,aACA,cACA,EACA,MAEA,OADA,6BACA,KAEA,OAEA,OADA,8BACA,KAEA,aAEA,OADA,+BACA,KAEA,YAEA,OADA,8BACA,KA+BA,MAEA,OADA,2BACA,KAEA,CACA,QACA,QAAU,GAAU,EAAI,IAAe,aACvC,OADuC,CACvC,CACA,YACA,iBACA,YACA,kBACA,CACA,KACA,UACA,KACA,YAEA,kBACA,QAAU,GAAU,+BACpB,OACA,2BACA,0BACA,kBACA,CAEA,SACA,uCACA,aACA,EACA,YACA,EAEA,CACA,CACA,kBACA,qBACA,WACA,kBACA,aACA,iBAEA,YACA,CAAU,GAAU,YACpB,aACA,SAAc,6BAA6B,GAAG,wCAA+C,GAE7F,sBAIA,MAHA,oBACA,GDtHA,YACA,gBAzCA,SACA,SACA,IACA,KACA,kBACA,WACA,YACA,YACA,WAEA,KACA,IACA,QACA,CAEA,GADA,KACA,UACA,KACA,QACA,CACA,YACA,qBACA,UACA,IACA,QACA,CACA,KAAmB,IAAnB,EACA,cAEA,KAAmB,IAAnB,EAAmB,CACnB,kBACA,UACA,IACA,QACA,CACA,mBACA,UACA,GACA,CACA,aAGA,KACA,QACA,ECmH0B,IAE1B,+CACA,CACA,yBACA,YACA,iBAAiC,QAAE,uGAEnC,ID1HA,WC2HsB,CD3HtB,EACA,QAAW,EAAE,SACb,iBACA,KAEA,mBACA,IAAiB,4CAAiD,GAElE,GAAc,EAAK,GAChB,WAAa,GCkHM,EACtB,CACA", "sources": ["webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/timestamp.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/date.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/tracing-utils.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/uuid.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/column.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/date.common.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/json.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/time.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/table.utils.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/sql/sql.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/numeric.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/logger.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/alias.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/casing.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/errors.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/view-base.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/dialect.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/selection-proxy.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/query-builders/query-builder.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/query-promise.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/utils.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/select.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/query-builder.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/update.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/insert.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/delete.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/count.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/query.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/raw.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/query-builders/refresh-materialized-view.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/db.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/cache/core/cache.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/session.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/neon-http/session.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/neon-http/driver.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/bigint.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/bigserial.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/char.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/cidr.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/custom.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/double-precision.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/inet.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/interval.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/line.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/macaddr.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/macaddr8.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/point.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/postgis_extension/utils.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/postgis_extension/geometry.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/real.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/smallint.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/smallserial.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/vector_extension/bit.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/vector_extension/halfvec.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/vector_extension/sparsevec.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/vector_extension/vector.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/all.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/table.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/int.common.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/text.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/boolean.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/varchar.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/jsonb.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/utils.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/view-common.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/primary-keys.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/relations.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/sql/expressions/conditions.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/enum.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/subquery.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/entity.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/sql/expressions/select.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/version.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/tracing.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/integer.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/table.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/serial.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/column-builder.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/foreign-keys.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/unique-constraint.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/utils/array.js", "webpack://terang-lms-ui/./node_modules/drizzle-orm/pg-core/columns/common.js"], "sourcesContent": ["import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgDateColumnBaseBuilder } from \"./date.common.js\";\nclass PgTimestampBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgTimestampBuilder\";\n  constructor(name, withTimezone, precision) {\n    super(name, \"date\", \"PgTimestamp\");\n    this.config.withTimezone = withTimezone;\n    this.config.precision = precision;\n  }\n  /** @internal */\n  build(table) {\n    return new PgTimestamp(table, this.config);\n  }\n}\nclass PgTimestamp extends PgColumn {\n  static [entityKind] = \"PgTimestamp\";\n  withTimezone;\n  precision;\n  constructor(table, config) {\n    super(table, config);\n    this.withTimezone = config.withTimezone;\n    this.precision = config.precision;\n  }\n  getSQLType() {\n    const precision = this.precision === void 0 ? \"\" : ` (${this.precision})`;\n    return `timestamp${precision}${this.withTimezone ? \" with time zone\" : \"\"}`;\n  }\n  mapFromDriverValue = (value) => {\n    return new Date(this.withTimezone ? value : value + \"+0000\");\n  };\n  mapToDriverValue = (value) => {\n    return value.toISOString();\n  };\n}\nclass PgTimestampStringBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgTimestampStringBuilder\";\n  constructor(name, withTimezone, precision) {\n    super(name, \"string\", \"PgTimestampString\");\n    this.config.withTimezone = withTimezone;\n    this.config.precision = precision;\n  }\n  /** @internal */\n  build(table) {\n    return new PgTimestampString(\n      table,\n      this.config\n    );\n  }\n}\nclass PgTimestampString extends PgColumn {\n  static [entityKind] = \"PgTimestampString\";\n  withTimezone;\n  precision;\n  constructor(table, config) {\n    super(table, config);\n    this.withTimezone = config.withTimezone;\n    this.precision = config.precision;\n  }\n  getSQLType() {\n    const precision = this.precision === void 0 ? \"\" : `(${this.precision})`;\n    return `timestamp${precision}${this.withTimezone ? \" with time zone\" : \"\"}`;\n  }\n}\nfunction timestamp(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config?.mode === \"string\") {\n    return new PgTimestampStringBuilder(name, config.withTimezone ?? false, config.precision);\n  }\n  return new PgTimestampBuilder(name, config?.withTimezone ?? false, config?.precision);\n}\nexport {\n  PgTimestamp,\n  PgTimestampBuilder,\n  PgTimestampString,\n  PgTimestampStringBuilder,\n  timestamp\n};\n//# sourceMappingURL=timestamp.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgDateColumnBaseBuilder } from \"./date.common.js\";\nclass PgDateBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgDateBuilder\";\n  constructor(name) {\n    super(name, \"date\", \"PgDate\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgDate(table, this.config);\n  }\n}\nclass PgDate extends PgColumn {\n  static [entityKind] = \"PgDate\";\n  getSQLType() {\n    return \"date\";\n  }\n  mapFromDriverValue(value) {\n    return new Date(value);\n  }\n  mapToDriverValue(value) {\n    return value.toISOString();\n  }\n}\nclass PgDateStringBuilder extends PgDateColumnBaseBuilder {\n  static [entityKind] = \"PgDateStringBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgDateString\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgDateString(\n      table,\n      this.config\n    );\n  }\n}\nclass PgDateString extends PgColumn {\n  static [entityKind] = \"PgDateString\";\n  getSQLType() {\n    return \"date\";\n  }\n}\nfunction date(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config?.mode === \"date\") {\n    return new PgDateBuilder(name);\n  }\n  return new PgDateStringBuilder(name);\n}\nexport {\n  PgDate,\n  PgDateBuilder,\n  PgDateString,\n  PgDateStringBuilder,\n  date\n};\n//# sourceMappingURL=date.js.map", "function iife(fn, ...args) {\n  return fn(...args);\n}\nexport {\n  iife\n};\n//# sourceMappingURL=tracing-utils.js.map", "import { entityKind } from \"../../entity.js\";\nimport { sql } from \"../../sql/sql.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgUUIDBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgUUIDBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgUUID\");\n  }\n  /**\n   * Adds `default gen_random_uuid()` to the column definition.\n   */\n  defaultRandom() {\n    return this.default(sql`gen_random_uuid()`);\n  }\n  /** @internal */\n  build(table) {\n    return new PgUUID(table, this.config);\n  }\n}\nclass PgUUID extends PgColumn {\n  static [entityKind] = \"PgUUID\";\n  getSQLType() {\n    return \"uuid\";\n  }\n}\nfunction uuid(name) {\n  return new PgUUIDBuilder(name ?? \"\");\n}\nexport {\n  PgUUID,\n  PgUUIDBuilder,\n  uuid\n};\n//# sourceMappingURL=uuid.js.map", "import { entityKind } from \"./entity.js\";\nclass Column {\n  constructor(table, config) {\n    this.table = table;\n    this.config = config;\n    this.name = config.name;\n    this.keyAsName = config.keyAsName;\n    this.notNull = config.notNull;\n    this.default = config.default;\n    this.defaultFn = config.defaultFn;\n    this.onUpdateFn = config.onUpdateFn;\n    this.hasDefault = config.hasDefault;\n    this.primary = config.primaryKey;\n    this.isUnique = config.isUnique;\n    this.uniqueName = config.uniqueName;\n    this.uniqueType = config.uniqueType;\n    this.dataType = config.dataType;\n    this.columnType = config.columnType;\n    this.generated = config.generated;\n    this.generatedIdentity = config.generatedIdentity;\n  }\n  static [entityKind] = \"Column\";\n  name;\n  keyAsName;\n  primary;\n  notNull;\n  default;\n  defaultFn;\n  onUpdateFn;\n  hasDefault;\n  isUnique;\n  uniqueName;\n  uniqueType;\n  dataType;\n  columnType;\n  enumValues = void 0;\n  generated = void 0;\n  generatedIdentity = void 0;\n  config;\n  mapFromDriverValue(value) {\n    return value;\n  }\n  mapToDriverValue(value) {\n    return value;\n  }\n  // ** @internal */\n  shouldDisableInsert() {\n    return this.config.generated !== void 0 && this.config.generated.type !== \"byDefault\";\n  }\n}\nexport {\n  Column\n};\n//# sourceMappingURL=column.js.map", "import { entityKind } from \"../../entity.js\";\nimport { sql } from \"../../sql/sql.js\";\nimport { PgColumnBuilder } from \"./common.js\";\nclass PgDateColumnBaseBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgDateColumnBaseBuilder\";\n  defaultNow() {\n    return this.default(sql`now()`);\n  }\n}\nexport {\n  PgDateColumnBaseBuilder\n};\n//# sourceMappingURL=date.common.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgJsonBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgJsonBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"Pg<PERSON><PERSON>\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgJson(table, this.config);\n  }\n}\nclass PgJson extends PgColumn {\n  static [entityKind] = \"PgJson\";\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return \"json\";\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      try {\n        return JSON.parse(value);\n      } catch {\n        return value;\n      }\n    }\n    return value;\n  }\n}\nfunction json(name) {\n  return new PgJsonBuilder(name ?? \"\");\n}\nexport {\n  Pg<PERSON><PERSON>,\n  PgJsonBuilder,\n  json\n};\n//# sourceMappingURL=json.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgDateColumnBaseBuilder } from \"./date.common.js\";\nclass PgTimeBuilder extends PgDateColumnBaseBuilder {\n  constructor(name, withTimezone, precision) {\n    super(name, \"string\", \"PgTime\");\n    this.withTimezone = withTimezone;\n    this.precision = precision;\n    this.config.withTimezone = withTimezone;\n    this.config.precision = precision;\n  }\n  static [entityKind] = \"PgTimeBuilder\";\n  /** @internal */\n  build(table) {\n    return new PgTime(table, this.config);\n  }\n}\nclass PgTime extends PgColumn {\n  static [entityKind] = \"PgTime\";\n  withTimezone;\n  precision;\n  constructor(table, config) {\n    super(table, config);\n    this.withTimezone = config.withTimezone;\n    this.precision = config.precision;\n  }\n  getSQLType() {\n    const precision = this.precision === void 0 ? \"\" : `(${this.precision})`;\n    return `time${precision}${this.withTimezone ? \" with time zone\" : \"\"}`;\n  }\n}\nfunction time(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgTimeBuilder(name, config.withTimezone ?? false, config.precision);\n}\nexport {\n  PgTime,\n  PgTimeBuilder,\n  time\n};\n//# sourceMappingURL=time.js.map", "const TableName = Symbol.for(\"drizzle:Name\");\nexport {\n  TableName\n};\n//# sourceMappingURL=table.utils.js.map", "import { entityKind, is } from \"../entity.js\";\nimport { isPgEnum } from \"../pg-core/columns/enum.js\";\nimport { Subquery } from \"../subquery.js\";\nimport { tracer } from \"../tracing.js\";\nimport { ViewBaseConfig } from \"../view-common.js\";\nimport { Column } from \"../column.js\";\nimport { IsAlias, Table } from \"../table.js\";\nclass FakePrimitiveParam {\n  static [entityKind] = \"FakePrimitiveParam\";\n}\nfunction isSQLWrapper(value) {\n  return value !== null && value !== void 0 && typeof value.getSQL === \"function\";\n}\nfunction mergeQueries(queries) {\n  const result = { sql: \"\", params: [] };\n  for (const query of queries) {\n    result.sql += query.sql;\n    result.params.push(...query.params);\n    if (query.typings?.length) {\n      if (!result.typings) {\n        result.typings = [];\n      }\n      result.typings.push(...query.typings);\n    }\n  }\n  return result;\n}\nclass StringChunk {\n  static [entityKind] = \"StringChunk\";\n  value;\n  constructor(value) {\n    this.value = Array.isArray(value) ? value : [value];\n  }\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nclass SQL {\n  constructor(queryChunks) {\n    this.queryChunks = queryChunks;\n    for (const chunk of queryChunks) {\n      if (is(chunk, Table)) {\n        const schemaName = chunk[Table.Symbol.Schema];\n        this.usedTables.push(\n          schemaName === void 0 ? chunk[Table.Symbol.Name] : schemaName + \".\" + chunk[Table.Symbol.Name]\n        );\n      }\n    }\n  }\n  static [entityKind] = \"SQL\";\n  /** @internal */\n  decoder = noopDecoder;\n  shouldInlineParams = false;\n  /** @internal */\n  usedTables = [];\n  append(query) {\n    this.queryChunks.push(...query.queryChunks);\n    return this;\n  }\n  toQuery(config) {\n    return tracer.startActiveSpan(\"drizzle.buildSQL\", (span) => {\n      const query = this.buildQueryFromSourceParams(this.queryChunks, config);\n      span?.setAttributes({\n        \"drizzle.query.text\": query.sql,\n        \"drizzle.query.params\": JSON.stringify(query.params)\n      });\n      return query;\n    });\n  }\n  buildQueryFromSourceParams(chunks, _config) {\n    const config = Object.assign({}, _config, {\n      inlineParams: _config.inlineParams || this.shouldInlineParams,\n      paramStartIndex: _config.paramStartIndex || { value: 0 }\n    });\n    const {\n      casing,\n      escapeName,\n      escapeParam,\n      prepareTyping,\n      inlineParams,\n      paramStartIndex\n    } = config;\n    return mergeQueries(chunks.map((chunk) => {\n      if (is(chunk, StringChunk)) {\n        return { sql: chunk.value.join(\"\"), params: [] };\n      }\n      if (is(chunk, Name)) {\n        return { sql: escapeName(chunk.value), params: [] };\n      }\n      if (chunk === void 0) {\n        return { sql: \"\", params: [] };\n      }\n      if (Array.isArray(chunk)) {\n        const result = [new StringChunk(\"(\")];\n        for (const [i, p] of chunk.entries()) {\n          result.push(p);\n          if (i < chunk.length - 1) {\n            result.push(new StringChunk(\", \"));\n          }\n        }\n        result.push(new StringChunk(\")\"));\n        return this.buildQueryFromSourceParams(result, config);\n      }\n      if (is(chunk, SQL)) {\n        return this.buildQueryFromSourceParams(chunk.queryChunks, {\n          ...config,\n          inlineParams: inlineParams || chunk.shouldInlineParams\n        });\n      }\n      if (is(chunk, Table)) {\n        const schemaName = chunk[Table.Symbol.Schema];\n        const tableName = chunk[Table.Symbol.Name];\n        return {\n          sql: schemaName === void 0 || chunk[IsAlias] ? escapeName(tableName) : escapeName(schemaName) + \".\" + escapeName(tableName),\n          params: []\n        };\n      }\n      if (is(chunk, Column)) {\n        const columnName = casing.getColumnCasing(chunk);\n        if (_config.invokeSource === \"indexes\") {\n          return { sql: escapeName(columnName), params: [] };\n        }\n        const schemaName = chunk.table[Table.Symbol.Schema];\n        return {\n          sql: chunk.table[IsAlias] || schemaName === void 0 ? escapeName(chunk.table[Table.Symbol.Name]) + \".\" + escapeName(columnName) : escapeName(schemaName) + \".\" + escapeName(chunk.table[Table.Symbol.Name]) + \".\" + escapeName(columnName),\n          params: []\n        };\n      }\n      if (is(chunk, View)) {\n        const schemaName = chunk[ViewBaseConfig].schema;\n        const viewName = chunk[ViewBaseConfig].name;\n        return {\n          sql: schemaName === void 0 || chunk[ViewBaseConfig].isAlias ? escapeName(viewName) : escapeName(schemaName) + \".\" + escapeName(viewName),\n          params: []\n        };\n      }\n      if (is(chunk, Param)) {\n        if (is(chunk.value, Placeholder)) {\n          return { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: [\"none\"] };\n        }\n        const mappedValue = chunk.value === null ? null : chunk.encoder.mapToDriverValue(chunk.value);\n        if (is(mappedValue, SQL)) {\n          return this.buildQueryFromSourceParams([mappedValue], config);\n        }\n        if (inlineParams) {\n          return { sql: this.mapInlineParam(mappedValue, config), params: [] };\n        }\n        let typings = [\"none\"];\n        if (prepareTyping) {\n          typings = [prepareTyping(chunk.encoder)];\n        }\n        return { sql: escapeParam(paramStartIndex.value++, mappedValue), params: [mappedValue], typings };\n      }\n      if (is(chunk, Placeholder)) {\n        return { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: [\"none\"] };\n      }\n      if (is(chunk, SQL.Aliased) && chunk.fieldAlias !== void 0) {\n        return { sql: escapeName(chunk.fieldAlias), params: [] };\n      }\n      if (is(chunk, Subquery)) {\n        if (chunk._.isWith) {\n          return { sql: escapeName(chunk._.alias), params: [] };\n        }\n        return this.buildQueryFromSourceParams([\n          new StringChunk(\"(\"),\n          chunk._.sql,\n          new StringChunk(\") \"),\n          new Name(chunk._.alias)\n        ], config);\n      }\n      if (isPgEnum(chunk)) {\n        if (chunk.schema) {\n          return { sql: escapeName(chunk.schema) + \".\" + escapeName(chunk.enumName), params: [] };\n        }\n        return { sql: escapeName(chunk.enumName), params: [] };\n      }\n      if (isSQLWrapper(chunk)) {\n        if (chunk.shouldOmitSQLParens?.()) {\n          return this.buildQueryFromSourceParams([chunk.getSQL()], config);\n        }\n        return this.buildQueryFromSourceParams([\n          new StringChunk(\"(\"),\n          chunk.getSQL(),\n          new StringChunk(\")\")\n        ], config);\n      }\n      if (inlineParams) {\n        return { sql: this.mapInlineParam(chunk, config), params: [] };\n      }\n      return { sql: escapeParam(paramStartIndex.value++, chunk), params: [chunk], typings: [\"none\"] };\n    }));\n  }\n  mapInlineParam(chunk, { escapeString }) {\n    if (chunk === null) {\n      return \"null\";\n    }\n    if (typeof chunk === \"number\" || typeof chunk === \"boolean\") {\n      return chunk.toString();\n    }\n    if (typeof chunk === \"string\") {\n      return escapeString(chunk);\n    }\n    if (typeof chunk === \"object\") {\n      const mappedValueAsString = chunk.toString();\n      if (mappedValueAsString === \"[object Object]\") {\n        return escapeString(JSON.stringify(chunk));\n      }\n      return escapeString(mappedValueAsString);\n    }\n    throw new Error(\"Unexpected param value: \" + chunk);\n  }\n  getSQL() {\n    return this;\n  }\n  as(alias) {\n    if (alias === void 0) {\n      return this;\n    }\n    return new SQL.Aliased(this, alias);\n  }\n  mapWith(decoder) {\n    this.decoder = typeof decoder === \"function\" ? { mapFromDriverValue: decoder } : decoder;\n    return this;\n  }\n  inlineParams() {\n    this.shouldInlineParams = true;\n    return this;\n  }\n  /**\n   * This method is used to conditionally include a part of the query.\n   *\n   * @param condition - Condition to check\n   * @returns itself if the condition is `true`, otherwise `undefined`\n   */\n  if(condition) {\n    return condition ? this : void 0;\n  }\n}\nclass Name {\n  constructor(value) {\n    this.value = value;\n  }\n  static [entityKind] = \"Name\";\n  brand;\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction name(value) {\n  return new Name(value);\n}\nfunction isDriverValueEncoder(value) {\n  return typeof value === \"object\" && value !== null && \"mapToDriverValue\" in value && typeof value.mapToDriverValue === \"function\";\n}\nconst noopDecoder = {\n  mapFromDriverValue: (value) => value\n};\nconst noopEncoder = {\n  mapToDriverValue: (value) => value\n};\nconst noopMapper = {\n  ...noopDecoder,\n  ...noopEncoder\n};\nclass Param {\n  /**\n   * @param value - Parameter value\n   * @param encoder - Encoder to convert the value to a driver parameter\n   */\n  constructor(value, encoder = noopEncoder) {\n    this.value = value;\n    this.encoder = encoder;\n  }\n  static [entityKind] = \"Param\";\n  brand;\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction param(value, encoder) {\n  return new Param(value, encoder);\n}\nfunction sql(strings, ...params) {\n  const queryChunks = [];\n  if (params.length > 0 || strings.length > 0 && strings[0] !== \"\") {\n    queryChunks.push(new StringChunk(strings[0]));\n  }\n  for (const [paramIndex, param2] of params.entries()) {\n    queryChunks.push(param2, new StringChunk(strings[paramIndex + 1]));\n  }\n  return new SQL(queryChunks);\n}\n((sql2) => {\n  function empty() {\n    return new SQL([]);\n  }\n  sql2.empty = empty;\n  function fromList(list) {\n    return new SQL(list);\n  }\n  sql2.fromList = fromList;\n  function raw(str) {\n    return new SQL([new StringChunk(str)]);\n  }\n  sql2.raw = raw;\n  function join(chunks, separator) {\n    const result = [];\n    for (const [i, chunk] of chunks.entries()) {\n      if (i > 0 && separator !== void 0) {\n        result.push(separator);\n      }\n      result.push(chunk);\n    }\n    return new SQL(result);\n  }\n  sql2.join = join;\n  function identifier(value) {\n    return new Name(value);\n  }\n  sql2.identifier = identifier;\n  function placeholder2(name2) {\n    return new Placeholder(name2);\n  }\n  sql2.placeholder = placeholder2;\n  function param2(value, encoder) {\n    return new Param(value, encoder);\n  }\n  sql2.param = param2;\n})(sql || (sql = {}));\n((SQL2) => {\n  class Aliased {\n    constructor(sql2, fieldAlias) {\n      this.sql = sql2;\n      this.fieldAlias = fieldAlias;\n    }\n    static [entityKind] = \"SQL.Aliased\";\n    /** @internal */\n    isSelectionField = false;\n    getSQL() {\n      return this.sql;\n    }\n    /** @internal */\n    clone() {\n      return new Aliased(this.sql, this.fieldAlias);\n    }\n  }\n  SQL2.Aliased = Aliased;\n})(SQL || (SQL = {}));\nclass Placeholder {\n  constructor(name2) {\n    this.name = name2;\n  }\n  static [entityKind] = \"Placeholder\";\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction placeholder(name2) {\n  return new Placeholder(name2);\n}\nfunction fillPlaceholders(params, values) {\n  return params.map((p) => {\n    if (is(p, Placeholder)) {\n      if (!(p.name in values)) {\n        throw new Error(`No value for placeholder \"${p.name}\" was provided`);\n      }\n      return values[p.name];\n    }\n    if (is(p, Param) && is(p.value, Placeholder)) {\n      if (!(p.value.name in values)) {\n        throw new Error(`No value for placeholder \"${p.value.name}\" was provided`);\n      }\n      return p.encoder.mapToDriverValue(values[p.value.name]);\n    }\n    return p;\n  });\n}\nconst IsDrizzleView = Symbol.for(\"drizzle:IsDrizzleView\");\nclass View {\n  static [entityKind] = \"View\";\n  /** @internal */\n  [ViewBaseConfig];\n  /** @internal */\n  [IsDrizzleView] = true;\n  constructor({ name: name2, schema, selectedFields, query }) {\n    this[ViewBaseConfig] = {\n      name: name2,\n      originalName: name2,\n      schema,\n      selectedFields,\n      query,\n      isExisting: !query,\n      isAlias: false\n    };\n  }\n  getSQL() {\n    return new SQL([this]);\n  }\n}\nfunction isView(view) {\n  return typeof view === \"object\" && view !== null && IsDrizzleView in view;\n}\nfunction getViewName(view) {\n  return view[ViewBaseConfig].name;\n}\nColumn.prototype.getSQL = function() {\n  return new SQL([this]);\n};\nTable.prototype.getSQL = function() {\n  return new SQL([this]);\n};\nSubquery.prototype.getSQL = function() {\n  return new SQL([this]);\n};\nexport {\n  FakePrimitiveParam,\n  Name,\n  Param,\n  Placeholder,\n  SQL,\n  StringChunk,\n  View,\n  fillPlaceholders,\n  getViewName,\n  isDriverValueEncoder,\n  isSQLWrapper,\n  isView,\n  name,\n  noopDecoder,\n  noopEncoder,\n  noopMapper,\n  param,\n  placeholder,\n  sql\n};\n//# sourceMappingURL=sql.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgNumericBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgNumericBuilder\";\n  constructor(name, precision, scale) {\n    super(name, \"string\", \"PgNumeric\");\n    this.config.precision = precision;\n    this.config.scale = scale;\n  }\n  /** @internal */\n  build(table) {\n    return new PgNumeric(table, this.config);\n  }\n}\nclass PgNumeric extends PgColumn {\n  static [entityKind] = \"PgNumeric\";\n  precision;\n  scale;\n  constructor(table, config) {\n    super(table, config);\n    this.precision = config.precision;\n    this.scale = config.scale;\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") return value;\n    return String(value);\n  }\n  getSQLType() {\n    if (this.precision !== void 0 && this.scale !== void 0) {\n      return `numeric(${this.precision}, ${this.scale})`;\n    } else if (this.precision === void 0) {\n      return \"numeric\";\n    } else {\n      return `numeric(${this.precision})`;\n    }\n  }\n}\nclass PgNumericNumberBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgNumericNumberBuilder\";\n  constructor(name, precision, scale) {\n    super(name, \"number\", \"PgNumericNumber\");\n    this.config.precision = precision;\n    this.config.scale = scale;\n  }\n  /** @internal */\n  build(table) {\n    return new PgNumericNumber(\n      table,\n      this.config\n    );\n  }\n}\nclass PgNumericNumber extends PgColumn {\n  static [entityKind] = \"PgNumericNumber\";\n  precision;\n  scale;\n  constructor(table, config) {\n    super(table, config);\n    this.precision = config.precision;\n    this.scale = config.scale;\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"number\") return value;\n    return Number(value);\n  }\n  mapToDriverValue = String;\n  getSQLType() {\n    if (this.precision !== void 0 && this.scale !== void 0) {\n      return `numeric(${this.precision}, ${this.scale})`;\n    } else if (this.precision === void 0) {\n      return \"numeric\";\n    } else {\n      return `numeric(${this.precision})`;\n    }\n  }\n}\nclass PgNumericBigIntBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgNumericBigIntBuilder\";\n  constructor(name, precision, scale) {\n    super(name, \"bigint\", \"PgNumericBigInt\");\n    this.config.precision = precision;\n    this.config.scale = scale;\n  }\n  /** @internal */\n  build(table) {\n    return new PgNumericBigInt(\n      table,\n      this.config\n    );\n  }\n}\nclass PgNumericBigInt extends PgColumn {\n  static [entityKind] = \"PgNumericBigInt\";\n  precision;\n  scale;\n  constructor(table, config) {\n    super(table, config);\n    this.precision = config.precision;\n    this.scale = config.scale;\n  }\n  mapFromDriverValue = BigInt;\n  mapToDriverValue = String;\n  getSQLType() {\n    if (this.precision !== void 0 && this.scale !== void 0) {\n      return `numeric(${this.precision}, ${this.scale})`;\n    } else if (this.precision === void 0) {\n      return \"numeric\";\n    } else {\n      return `numeric(${this.precision})`;\n    }\n  }\n}\nfunction numeric(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  const mode = config?.mode;\n  return mode === \"number\" ? new PgNumericNumberBuilder(name, config?.precision, config?.scale) : mode === \"bigint\" ? new PgNumericBigIntBuilder(name, config?.precision, config?.scale) : new PgNumericBuilder(name, config?.precision, config?.scale);\n}\nconst decimal = numeric;\nexport {\n  PgNumeric,\n  PgNumericBigInt,\n  PgNumericBigIntBuilder,\n  PgNumericBuilder,\n  PgNumericNumber,\n  PgNumericNumberBuilder,\n  decimal,\n  numeric\n};\n//# sourceMappingURL=numeric.js.map", "import { entityKind } from \"./entity.js\";\nclass ConsoleLogWriter {\n  static [entityKind] = \"ConsoleLogWriter\";\n  write(message) {\n    console.log(message);\n  }\n}\nclass DefaultLogger {\n  static [entityKind] = \"DefaultLogger\";\n  writer;\n  constructor(config) {\n    this.writer = config?.writer ?? new ConsoleLogWriter();\n  }\n  logQuery(query, params) {\n    const stringifiedParams = params.map((p) => {\n      try {\n        return JSON.stringify(p);\n      } catch {\n        return String(p);\n      }\n    });\n    const paramsStr = stringifiedParams.length ? ` -- params: [${stringifiedParams.join(\", \")}]` : \"\";\n    this.writer.write(`Query: ${query}${paramsStr}`);\n  }\n}\nclass NoopLogger {\n  static [entityKind] = \"NoopLogger\";\n  logQuery() {\n  }\n}\nexport {\n  ConsoleLogWriter,\n  DefaultLogger,\n  NoopLogger\n};\n//# sourceMappingURL=logger.js.map", "import { Column } from \"./column.js\";\nimport { entityKind, is } from \"./entity.js\";\nimport { SQL, sql } from \"./sql/sql.js\";\nimport { Table } from \"./table.js\";\nimport { ViewBaseConfig } from \"./view-common.js\";\nclass ColumnAliasProxyHandler {\n  constructor(table) {\n    this.table = table;\n  }\n  static [entityKind] = \"ColumnAliasProxyHandler\";\n  get(columnObj, prop) {\n    if (prop === \"table\") {\n      return this.table;\n    }\n    return columnObj[prop];\n  }\n}\nclass TableAliasProxyHandler {\n  constructor(alias, replaceOriginalName) {\n    this.alias = alias;\n    this.replaceOriginalName = replaceOriginalName;\n  }\n  static [entityKind] = \"TableAliasProxyHandler\";\n  get(target, prop) {\n    if (prop === Table.Symbol.IsAlias) {\n      return true;\n    }\n    if (prop === Table.Symbol.Name) {\n      return this.alias;\n    }\n    if (this.replaceOriginalName && prop === Table.Symbol.OriginalName) {\n      return this.alias;\n    }\n    if (prop === ViewBaseConfig) {\n      return {\n        ...target[ViewBaseConfig],\n        name: this.alias,\n        isAlias: true\n      };\n    }\n    if (prop === Table.Symbol.Columns) {\n      const columns = target[Table.Symbol.Columns];\n      if (!columns) {\n        return columns;\n      }\n      const proxiedColumns = {};\n      Object.keys(columns).map((key) => {\n        proxiedColumns[key] = new Proxy(\n          columns[key],\n          new ColumnAliasProxyHandler(new Proxy(target, this))\n        );\n      });\n      return proxiedColumns;\n    }\n    const value = target[prop];\n    if (is(value, Column)) {\n      return new Proxy(value, new ColumnAliasProxyHandler(new Proxy(target, this)));\n    }\n    return value;\n  }\n}\nclass RelationTableAliasProxyHandler {\n  constructor(alias) {\n    this.alias = alias;\n  }\n  static [entityKind] = \"RelationTableAliasProxyHandler\";\n  get(target, prop) {\n    if (prop === \"sourceTable\") {\n      return aliasedTable(target.sourceTable, this.alias);\n    }\n    return target[prop];\n  }\n}\nfunction aliasedTable(table, tableAlias) {\n  return new Proxy(table, new TableAliasProxyHandler(tableAlias, false));\n}\nfunction aliasedRelation(relation, tableAlias) {\n  return new Proxy(relation, new RelationTableAliasProxyHandler(tableAlias));\n}\nfunction aliasedTableColumn(column, tableAlias) {\n  return new Proxy(\n    column,\n    new ColumnAliasProxyHandler(new Proxy(column.table, new TableAliasProxyHandler(tableAlias, false)))\n  );\n}\nfunction mapColumnsInAliasedSQLToAlias(query, alias) {\n  return new SQL.Aliased(mapColumnsInSQLToAlias(query.sql, alias), query.fieldAlias);\n}\nfunction mapColumnsInSQLToAlias(query, alias) {\n  return sql.join(query.queryChunks.map((c) => {\n    if (is(c, Column)) {\n      return aliasedTableColumn(c, alias);\n    }\n    if (is(c, SQL)) {\n      return mapColumnsInSQLToAlias(c, alias);\n    }\n    if (is(c, SQL.Aliased)) {\n      return mapColumnsInAliasedSQLToAlias(c, alias);\n    }\n    return c;\n  }));\n}\nexport {\n  ColumnAliasProxyHandler,\n  RelationTableAliasProxyHandler,\n  TableAliasProxyHandler,\n  aliasedRelation,\n  aliasedTable,\n  aliasedTableColumn,\n  mapColumnsInAliasedSQLToAlias,\n  mapColumnsInSQLToAlias\n};\n//# sourceMappingURL=alias.js.map", "import { entityKind } from \"./entity.js\";\nimport { Table } from \"./table.js\";\nfunction toSnakeCase(input) {\n  const words = input.replace(/['\\u2019]/g, \"\").match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n  return words.map((word) => word.toLowerCase()).join(\"_\");\n}\nfunction toCamelCase(input) {\n  const words = input.replace(/['\\u2019]/g, \"\").match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n  return words.reduce((acc, word, i) => {\n    const formattedWord = i === 0 ? word.toLowerCase() : `${word[0].toUpperCase()}${word.slice(1)}`;\n    return acc + formattedWord;\n  }, \"\");\n}\nfunction noopCase(input) {\n  return input;\n}\nclass CasingCache {\n  static [entityKind] = \"CasingCache\";\n  /** @internal */\n  cache = {};\n  cachedTables = {};\n  convert;\n  constructor(casing) {\n    this.convert = casing === \"snake_case\" ? toSnakeCase : casing === \"camelCase\" ? toCamelCase : noopCase;\n  }\n  getColumnCasing(column) {\n    if (!column.keyAsName) return column.name;\n    const schema = column.table[Table.Symbol.Schema] ?? \"public\";\n    const tableName = column.table[Table.Symbol.OriginalName];\n    const key = `${schema}.${tableName}.${column.name}`;\n    if (!this.cache[key]) {\n      this.cacheTable(column.table);\n    }\n    return this.cache[key];\n  }\n  cacheTable(table) {\n    const schema = table[Table.Symbol.Schema] ?? \"public\";\n    const tableName = table[Table.Symbol.OriginalName];\n    const tableKey = `${schema}.${tableName}`;\n    if (!this.cachedTables[tableKey]) {\n      for (const column of Object.values(table[Table.Symbol.Columns])) {\n        const columnKey = `${tableKey}.${column.name}`;\n        this.cache[columnKey] = this.convert(column.name);\n      }\n      this.cachedTables[tableKey] = true;\n    }\n  }\n  clearCache() {\n    this.cache = {};\n    this.cachedTables = {};\n  }\n}\nexport {\n  CasingCache,\n  toCamelCase,\n  toSnakeCase\n};\n//# sourceMappingURL=casing.js.map", "import { entityKind } from \"./entity.js\";\nclass DrizzleError extends Error {\n  static [entityKind] = \"DrizzleError\";\n  constructor({ message, cause }) {\n    super(message);\n    this.name = \"DrizzleError\";\n    this.cause = cause;\n  }\n}\nclass DrizzleQueryError extends Error {\n  constructor(query, params, cause) {\n    super(`Failed query: ${query}\nparams: ${params}`);\n    this.query = query;\n    this.params = params;\n    this.cause = cause;\n    Error.captureStackTrace(this, DrizzleQueryError);\n    if (cause) this.cause = cause;\n  }\n}\nclass TransactionRollbackError extends DrizzleError {\n  static [entityKind] = \"TransactionRollbackError\";\n  constructor() {\n    super({ message: \"Rollback\" });\n  }\n}\nexport {\n  DrizzleError,\n  DrizzleQueryError,\n  TransactionRollbackError\n};\n//# sourceMappingURL=errors.js.map", "import { entityKind } from \"../entity.js\";\nimport { View } from \"../sql/sql.js\";\nclass PgViewBase extends View {\n  static [entityKind] = \"PgViewBase\";\n}\nexport {\n  PgViewBase\n};\n//# sourceMappingURL=view-base.js.map", "import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from \"../alias.js\";\nimport { CasingCache } from \"../casing.js\";\nimport { Column } from \"../column.js\";\nimport { entityKind, is } from \"../entity.js\";\nimport { DrizzleError } from \"../errors.js\";\nimport {\n  PgColumn,\n  PgDate,\n  PgDateString,\n  PgJson,\n  PgJsonb,\n  PgNumeric,\n  PgTime,\n  PgTimestamp,\n  PgTimestampString,\n  PgUUID\n} from \"./columns/index.js\";\nimport { PgTable } from \"./table.js\";\nimport {\n  getOperators,\n  getOrderByOperators,\n  Many,\n  normalizeRelation,\n  One\n} from \"../relations.js\";\nimport { and, eq, View } from \"../sql/index.js\";\nimport {\n  Param,\n  SQL,\n  sql\n} from \"../sql/sql.js\";\nimport { Subquery } from \"../subquery.js\";\nimport { getTableName, getTableUniqueName, Table } from \"../table.js\";\nimport { orderSelectedFields } from \"../utils.js\";\nimport { ViewBaseConfig } from \"../view-common.js\";\nimport { PgViewBase } from \"./view-base.js\";\nclass PgDialect {\n  static [entityKind] = \"PgDialect\";\n  /** @internal */\n  casing;\n  constructor(config) {\n    this.casing = new CasingCache(config?.casing);\n  }\n  async migrate(migrations, session, config) {\n    const migrationsTable = typeof config === \"string\" ? \"__drizzle_migrations\" : config.migrationsTable ?? \"__drizzle_migrations\";\n    const migrationsSchema = typeof config === \"string\" ? \"drizzle\" : config.migrationsSchema ?? \"drizzle\";\n    const migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at bigint\n\t\t\t)\n\t\t`;\n    await session.execute(sql`CREATE SCHEMA IF NOT EXISTS ${sql.identifier(migrationsSchema)}`);\n    await session.execute(migrationTableCreate);\n    const dbMigrations = await session.all(\n      sql`select id, hash, created_at from ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} order by created_at desc limit 1`\n    );\n    const lastDbMigration = dbMigrations[0];\n    await session.transaction(async (tx) => {\n      for await (const migration of migrations) {\n        if (!lastDbMigration || Number(lastDbMigration.created_at) < migration.folderMillis) {\n          for (const stmt of migration.sql) {\n            await tx.execute(sql.raw(stmt));\n          }\n          await tx.execute(\n            sql`insert into ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} (\"hash\", \"created_at\") values(${migration.hash}, ${migration.folderMillis})`\n          );\n        }\n      }\n    });\n  }\n  escapeName(name) {\n    return `\"${name}\"`;\n  }\n  escapeParam(num) {\n    return `$${num + 1}`;\n  }\n  escapeString(str) {\n    return `'${str.replace(/'/g, \"''\")}'`;\n  }\n  buildWithCTE(queries) {\n    if (!queries?.length) return void 0;\n    const withSqlChunks = [sql`with `];\n    for (const [i, w] of queries.entries()) {\n      withSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n      if (i < queries.length - 1) {\n        withSqlChunks.push(sql`, `);\n      }\n    }\n    withSqlChunks.push(sql` `);\n    return sql.join(withSqlChunks);\n  }\n  buildDeleteQuery({ table, where, returning, withList }) {\n    const withSql = this.buildWithCTE(withList);\n    const returningSql = returning ? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}` : void 0;\n    const whereSql = where ? sql` where ${where}` : void 0;\n    return sql`${withSql}delete from ${table}${whereSql}${returningSql}`;\n  }\n  buildUpdateSet(table, set) {\n    const tableColumns = table[Table.Symbol.Columns];\n    const columnNames = Object.keys(tableColumns).filter(\n      (colName) => set[colName] !== void 0 || tableColumns[colName]?.onUpdateFn !== void 0\n    );\n    const setSize = columnNames.length;\n    return sql.join(columnNames.flatMap((colName, i) => {\n      const col = tableColumns[colName];\n      const value = set[colName] ?? sql.param(col.onUpdateFn(), col);\n      const res = sql`${sql.identifier(this.casing.getColumnCasing(col))} = ${value}`;\n      if (i < setSize - 1) {\n        return [res, sql.raw(\", \")];\n      }\n      return [res];\n    }));\n  }\n  buildUpdateQuery({ table, set, where, returning, withList, from, joins }) {\n    const withSql = this.buildWithCTE(withList);\n    const tableName = table[PgTable.Symbol.Name];\n    const tableSchema = table[PgTable.Symbol.Schema];\n    const origTableName = table[PgTable.Symbol.OriginalName];\n    const alias = tableName === origTableName ? void 0 : tableName;\n    const tableSql = sql`${tableSchema ? sql`${sql.identifier(tableSchema)}.` : void 0}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`}`;\n    const setSql = this.buildUpdateSet(table, set);\n    const fromSql = from && sql.join([sql.raw(\" from \"), this.buildFromTable(from)]);\n    const joinsSql = this.buildJoins(joins);\n    const returningSql = returning ? sql` returning ${this.buildSelection(returning, { isSingleTable: !from })}` : void 0;\n    const whereSql = where ? sql` where ${where}` : void 0;\n    return sql`${withSql}update ${tableSql} set ${setSql}${fromSql}${joinsSql}${whereSql}${returningSql}`;\n  }\n  /**\n   * Builds selection SQL with provided fields/expressions\n   *\n   * Examples:\n   *\n   * `select <selection> from`\n   *\n   * `insert ... returning <selection>`\n   *\n   * If `isSingleTable` is true, then columns won't be prefixed with table name\n   */\n  buildSelection(fields, { isSingleTable = false } = {}) {\n    const columnsLen = fields.length;\n    const chunks = fields.flatMap(({ field }, i) => {\n      const chunk = [];\n      if (is(field, SQL.Aliased) && field.isSelectionField) {\n        chunk.push(sql.identifier(field.fieldAlias));\n      } else if (is(field, SQL.Aliased) || is(field, SQL)) {\n        const query = is(field, SQL.Aliased) ? field.sql : field;\n        if (isSingleTable) {\n          chunk.push(\n            new SQL(\n              query.queryChunks.map((c) => {\n                if (is(c, PgColumn)) {\n                  return sql.identifier(this.casing.getColumnCasing(c));\n                }\n                return c;\n              })\n            )\n          );\n        } else {\n          chunk.push(query);\n        }\n        if (is(field, SQL.Aliased)) {\n          chunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n        }\n      } else if (is(field, Column)) {\n        if (isSingleTable) {\n          chunk.push(sql.identifier(this.casing.getColumnCasing(field)));\n        } else {\n          chunk.push(field);\n        }\n      }\n      if (i < columnsLen - 1) {\n        chunk.push(sql`, `);\n      }\n      return chunk;\n    });\n    return sql.join(chunks);\n  }\n  buildJoins(joins) {\n    if (!joins || joins.length === 0) {\n      return void 0;\n    }\n    const joinsArray = [];\n    for (const [index, joinMeta] of joins.entries()) {\n      if (index === 0) {\n        joinsArray.push(sql` `);\n      }\n      const table = joinMeta.table;\n      const lateralSql = joinMeta.lateral ? sql` lateral` : void 0;\n      const onSql = joinMeta.on ? sql` on ${joinMeta.on}` : void 0;\n      if (is(table, PgTable)) {\n        const tableName = table[PgTable.Symbol.Name];\n        const tableSchema = table[PgTable.Symbol.Schema];\n        const origTableName = table[PgTable.Symbol.OriginalName];\n        const alias = tableName === origTableName ? void 0 : joinMeta.alias;\n        joinsArray.push(\n          sql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${tableSchema ? sql`${sql.identifier(tableSchema)}.` : void 0}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`}${onSql}`\n        );\n      } else if (is(table, View)) {\n        const viewName = table[ViewBaseConfig].name;\n        const viewSchema = table[ViewBaseConfig].schema;\n        const origViewName = table[ViewBaseConfig].originalName;\n        const alias = viewName === origViewName ? void 0 : joinMeta.alias;\n        joinsArray.push(\n          sql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${viewSchema ? sql`${sql.identifier(viewSchema)}.` : void 0}${sql.identifier(origViewName)}${alias && sql` ${sql.identifier(alias)}`}${onSql}`\n        );\n      } else {\n        joinsArray.push(\n          sql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${table}${onSql}`\n        );\n      }\n      if (index < joins.length - 1) {\n        joinsArray.push(sql` `);\n      }\n    }\n    return sql.join(joinsArray);\n  }\n  buildFromTable(table) {\n    if (is(table, Table) && table[Table.Symbol.IsAlias]) {\n      let fullName = sql`${sql.identifier(table[Table.Symbol.OriginalName])}`;\n      if (table[Table.Symbol.Schema]) {\n        fullName = sql`${sql.identifier(table[Table.Symbol.Schema])}.${fullName}`;\n      }\n      return sql`${fullName} ${sql.identifier(table[Table.Symbol.Name])}`;\n    }\n    return table;\n  }\n  buildSelectQuery({\n    withList,\n    fields,\n    fieldsFlat,\n    where,\n    having,\n    table,\n    joins,\n    orderBy,\n    groupBy,\n    limit,\n    offset,\n    lockingClause,\n    distinct,\n    setOperators\n  }) {\n    const fieldsList = fieldsFlat ?? orderSelectedFields(fields);\n    for (const f of fieldsList) {\n      if (is(f.field, Column) && getTableName(f.field.table) !== (is(table, Subquery) ? table._.alias : is(table, PgViewBase) ? table[ViewBaseConfig].name : is(table, SQL) ? void 0 : getTableName(table)) && !((table2) => joins?.some(\n        ({ alias }) => alias === (table2[Table.Symbol.IsAlias] ? getTableName(table2) : table2[Table.Symbol.BaseName])\n      ))(f.field.table)) {\n        const tableName = getTableName(f.field.table);\n        throw new Error(\n          `Your \"${f.path.join(\"->\")}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`\n        );\n      }\n    }\n    const isSingleTable = !joins || joins.length === 0;\n    const withSql = this.buildWithCTE(withList);\n    let distinctSql;\n    if (distinct) {\n      distinctSql = distinct === true ? sql` distinct` : sql` distinct on (${sql.join(distinct.on, sql`, `)})`;\n    }\n    const selection = this.buildSelection(fieldsList, { isSingleTable });\n    const tableSql = this.buildFromTable(table);\n    const joinsSql = this.buildJoins(joins);\n    const whereSql = where ? sql` where ${where}` : void 0;\n    const havingSql = having ? sql` having ${having}` : void 0;\n    let orderBySql;\n    if (orderBy && orderBy.length > 0) {\n      orderBySql = sql` order by ${sql.join(orderBy, sql`, `)}`;\n    }\n    let groupBySql;\n    if (groupBy && groupBy.length > 0) {\n      groupBySql = sql` group by ${sql.join(groupBy, sql`, `)}`;\n    }\n    const limitSql = typeof limit === \"object\" || typeof limit === \"number\" && limit >= 0 ? sql` limit ${limit}` : void 0;\n    const offsetSql = offset ? sql` offset ${offset}` : void 0;\n    const lockingClauseSql = sql.empty();\n    if (lockingClause) {\n      const clauseSql = sql` for ${sql.raw(lockingClause.strength)}`;\n      if (lockingClause.config.of) {\n        clauseSql.append(\n          sql` of ${sql.join(\n            Array.isArray(lockingClause.config.of) ? lockingClause.config.of : [lockingClause.config.of],\n            sql`, `\n          )}`\n        );\n      }\n      if (lockingClause.config.noWait) {\n        clauseSql.append(sql` nowait`);\n      } else if (lockingClause.config.skipLocked) {\n        clauseSql.append(sql` skip locked`);\n      }\n      lockingClauseSql.append(clauseSql);\n    }\n    const finalQuery = sql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}${lockingClauseSql}`;\n    if (setOperators.length > 0) {\n      return this.buildSetOperations(finalQuery, setOperators);\n    }\n    return finalQuery;\n  }\n  buildSetOperations(leftSelect, setOperators) {\n    const [setOperator, ...rest] = setOperators;\n    if (!setOperator) {\n      throw new Error(\"Cannot pass undefined values to any set operator\");\n    }\n    if (rest.length === 0) {\n      return this.buildSetOperationQuery({ leftSelect, setOperator });\n    }\n    return this.buildSetOperations(\n      this.buildSetOperationQuery({ leftSelect, setOperator }),\n      rest\n    );\n  }\n  buildSetOperationQuery({\n    leftSelect,\n    setOperator: { type, isAll, rightSelect, limit, orderBy, offset }\n  }) {\n    const leftChunk = sql`(${leftSelect.getSQL()}) `;\n    const rightChunk = sql`(${rightSelect.getSQL()})`;\n    let orderBySql;\n    if (orderBy && orderBy.length > 0) {\n      const orderByValues = [];\n      for (const singleOrderBy of orderBy) {\n        if (is(singleOrderBy, PgColumn)) {\n          orderByValues.push(sql.identifier(singleOrderBy.name));\n        } else if (is(singleOrderBy, SQL)) {\n          for (let i = 0; i < singleOrderBy.queryChunks.length; i++) {\n            const chunk = singleOrderBy.queryChunks[i];\n            if (is(chunk, PgColumn)) {\n              singleOrderBy.queryChunks[i] = sql.identifier(chunk.name);\n            }\n          }\n          orderByValues.push(sql`${singleOrderBy}`);\n        } else {\n          orderByValues.push(sql`${singleOrderBy}`);\n        }\n      }\n      orderBySql = sql` order by ${sql.join(orderByValues, sql`, `)} `;\n    }\n    const limitSql = typeof limit === \"object\" || typeof limit === \"number\" && limit >= 0 ? sql` limit ${limit}` : void 0;\n    const operatorChunk = sql.raw(`${type} ${isAll ? \"all \" : \"\"}`);\n    const offsetSql = offset ? sql` offset ${offset}` : void 0;\n    return sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n  }\n  buildInsertQuery({ table, values: valuesOrSelect, onConflict, returning, withList, select, overridingSystemValue_ }) {\n    const valuesSqlList = [];\n    const columns = table[Table.Symbol.Columns];\n    const colEntries = Object.entries(columns).filter(([_, col]) => !col.shouldDisableInsert());\n    const insertOrder = colEntries.map(\n      ([, column]) => sql.identifier(this.casing.getColumnCasing(column))\n    );\n    if (select) {\n      const select2 = valuesOrSelect;\n      if (is(select2, SQL)) {\n        valuesSqlList.push(select2);\n      } else {\n        valuesSqlList.push(select2.getSQL());\n      }\n    } else {\n      const values = valuesOrSelect;\n      valuesSqlList.push(sql.raw(\"values \"));\n      for (const [valueIndex, value] of values.entries()) {\n        const valueList = [];\n        for (const [fieldName, col] of colEntries) {\n          const colValue = value[fieldName];\n          if (colValue === void 0 || is(colValue, Param) && colValue.value === void 0) {\n            if (col.defaultFn !== void 0) {\n              const defaultFnResult = col.defaultFn();\n              const defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n              valueList.push(defaultValue);\n            } else if (!col.default && col.onUpdateFn !== void 0) {\n              const onUpdateFnResult = col.onUpdateFn();\n              const newValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n              valueList.push(newValue);\n            } else {\n              valueList.push(sql`default`);\n            }\n          } else {\n            valueList.push(colValue);\n          }\n        }\n        valuesSqlList.push(valueList);\n        if (valueIndex < values.length - 1) {\n          valuesSqlList.push(sql`, `);\n        }\n      }\n    }\n    const withSql = this.buildWithCTE(withList);\n    const valuesSql = sql.join(valuesSqlList);\n    const returningSql = returning ? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}` : void 0;\n    const onConflictSql = onConflict ? sql` on conflict ${onConflict}` : void 0;\n    const overridingSql = overridingSystemValue_ === true ? sql`overriding system value ` : void 0;\n    return sql`${withSql}insert into ${table} ${insertOrder} ${overridingSql}${valuesSql}${onConflictSql}${returningSql}`;\n  }\n  buildRefreshMaterializedViewQuery({ view, concurrently, withNoData }) {\n    const concurrentlySql = concurrently ? sql` concurrently` : void 0;\n    const withNoDataSql = withNoData ? sql` with no data` : void 0;\n    return sql`refresh materialized view${concurrentlySql} ${view}${withNoDataSql}`;\n  }\n  prepareTyping(encoder) {\n    if (is(encoder, PgJsonb) || is(encoder, PgJson)) {\n      return \"json\";\n    } else if (is(encoder, PgNumeric)) {\n      return \"decimal\";\n    } else if (is(encoder, PgTime)) {\n      return \"time\";\n    } else if (is(encoder, PgTimestamp) || is(encoder, PgTimestampString)) {\n      return \"timestamp\";\n    } else if (is(encoder, PgDate) || is(encoder, PgDateString)) {\n      return \"date\";\n    } else if (is(encoder, PgUUID)) {\n      return \"uuid\";\n    } else {\n      return \"none\";\n    }\n  }\n  sqlToQuery(sql2, invokeSource) {\n    return sql2.toQuery({\n      casing: this.casing,\n      escapeName: this.escapeName,\n      escapeParam: this.escapeParam,\n      escapeString: this.escapeString,\n      prepareTyping: this.prepareTyping,\n      invokeSource\n    });\n  }\n  // buildRelationalQueryWithPK({\n  // \tfullSchema,\n  // \tschema,\n  // \ttableNamesMap,\n  // \ttable,\n  // \ttableConfig,\n  // \tqueryConfig: config,\n  // \ttableAlias,\n  // \tisRoot = false,\n  // \tjoinOn,\n  // }: {\n  // \tfullSchema: Record<string, unknown>;\n  // \tschema: TablesRelationalConfig;\n  // \ttableNamesMap: Record<string, string>;\n  // \ttable: PgTable;\n  // \ttableConfig: TableRelationalConfig;\n  // \tqueryConfig: true | DBQueryConfig<'many', true>;\n  // \ttableAlias: string;\n  // \tisRoot?: boolean;\n  // \tjoinOn?: SQL;\n  // }): BuildRelationalQueryResult<PgTable, PgColumn> {\n  // \t// For { \"<relation>\": true }, return a table with selection of all columns\n  // \tif (config === true) {\n  // \t\tconst selectionEntries = Object.entries(tableConfig.columns);\n  // \t\tconst selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = selectionEntries.map((\n  // \t\t\t[key, value],\n  // \t\t) => ({\n  // \t\t\tdbKey: value.name,\n  // \t\t\ttsKey: key,\n  // \t\t\tfield: value as PgColumn,\n  // \t\t\trelationTableTsKey: undefined,\n  // \t\t\tisJson: false,\n  // \t\t\tselection: [],\n  // \t\t}));\n  // \t\treturn {\n  // \t\t\ttableTsKey: tableConfig.tsName,\n  // \t\t\tsql: table,\n  // \t\t\tselection,\n  // \t\t};\n  // \t}\n  // \t// let selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n  // \t// let selectionForBuild = selection;\n  // \tconst aliasedColumns = Object.fromEntries(\n  // \t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n  // \t);\n  // \tconst aliasedRelations = Object.fromEntries(\n  // \t\tObject.entries(tableConfig.relations).map(([key, value]) => [key, aliasedRelation(value, tableAlias)]),\n  // \t);\n  // \tconst aliasedFields = Object.assign({}, aliasedColumns, aliasedRelations);\n  // \tlet where, hasUserDefinedWhere;\n  // \tif (config.where) {\n  // \t\tconst whereSql = typeof config.where === 'function' ? config.where(aliasedFields, operators) : config.where;\n  // \t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n  // \t\thasUserDefinedWhere = !!where;\n  // \t}\n  // \twhere = and(joinOn, where);\n  // \t// const fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased; isExtra?: boolean }[] = [];\n  // \tlet joins: Join[] = [];\n  // \tlet selectedColumns: string[] = [];\n  // \t// Figure out which columns to select\n  // \tif (config.columns) {\n  // \t\tlet isIncludeMode = false;\n  // \t\tfor (const [field, value] of Object.entries(config.columns)) {\n  // \t\t\tif (value === undefined) {\n  // \t\t\t\tcontinue;\n  // \t\t\t}\n  // \t\t\tif (field in tableConfig.columns) {\n  // \t\t\t\tif (!isIncludeMode && value === true) {\n  // \t\t\t\t\tisIncludeMode = true;\n  // \t\t\t\t}\n  // \t\t\t\tselectedColumns.push(field);\n  // \t\t\t}\n  // \t\t}\n  // \t\tif (selectedColumns.length > 0) {\n  // \t\t\tselectedColumns = isIncludeMode\n  // \t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n  // \t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n  // \t\t}\n  // \t} else {\n  // \t\t// Select all columns if selection is not specified\n  // \t\tselectedColumns = Object.keys(tableConfig.columns);\n  // \t}\n  // \t// for (const field of selectedColumns) {\n  // \t// \tconst column = tableConfig.columns[field]! as PgColumn;\n  // \t// \tfieldsSelection.push({ tsKey: field, value: column });\n  // \t// }\n  // \tlet initiallySelectedRelations: {\n  // \t\ttsKey: string;\n  // \t\tqueryConfig: true | DBQueryConfig<'many', false>;\n  // \t\trelation: Relation;\n  // \t}[] = [];\n  // \t// let selectedRelations: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n  // \t// Figure out which relations to select\n  // \tif (config.with) {\n  // \t\tinitiallySelectedRelations = Object.entries(config.with)\n  // \t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n  // \t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n  // \t}\n  // \tconst manyRelations = initiallySelectedRelations.filter((r) =>\n  // \t\tis(r.relation, Many)\n  // \t\t&& (schema[tableNamesMap[r.relation.referencedTable[Table.Symbol.Name]]!]?.primaryKey.length ?? 0) > 0\n  // \t);\n  // \t// If this is the last Many relation (or there are no Many relations), we are on the innermost subquery level\n  // \tconst isInnermostQuery = manyRelations.length < 2;\n  // \tconst selectedExtras: {\n  // \t\ttsKey: string;\n  // \t\tvalue: SQL.Aliased;\n  // \t}[] = [];\n  // \t// Figure out which extras to select\n  // \tif (isInnermostQuery && config.extras) {\n  // \t\tconst extras = typeof config.extras === 'function'\n  // \t\t\t? config.extras(aliasedFields, { sql })\n  // \t\t\t: config.extras;\n  // \t\tfor (const [tsKey, value] of Object.entries(extras)) {\n  // \t\t\tselectedExtras.push({\n  // \t\t\t\ttsKey,\n  // \t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n  // \t\t\t});\n  // \t\t}\n  // \t}\n  // \t// Transform `fieldsSelection` into `selection`\n  // \t// `fieldsSelection` shouldn't be used after this point\n  // \t// for (const { tsKey, value, isExtra } of fieldsSelection) {\n  // \t// \tselection.push({\n  // \t// \t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n  // \t// \t\ttsKey,\n  // \t// \t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n  // \t// \t\trelationTableTsKey: undefined,\n  // \t// \t\tisJson: false,\n  // \t// \t\tisExtra,\n  // \t// \t\tselection: [],\n  // \t// \t});\n  // \t// }\n  // \tlet orderByOrig = typeof config.orderBy === 'function'\n  // \t\t? config.orderBy(aliasedFields, orderByOperators)\n  // \t\t: config.orderBy ?? [];\n  // \tif (!Array.isArray(orderByOrig)) {\n  // \t\torderByOrig = [orderByOrig];\n  // \t}\n  // \tconst orderBy = orderByOrig.map((orderByValue) => {\n  // \t\tif (is(orderByValue, Column)) {\n  // \t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n  // \t\t}\n  // \t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n  // \t});\n  // \tconst limit = isInnermostQuery ? config.limit : undefined;\n  // \tconst offset = isInnermostQuery ? config.offset : undefined;\n  // \t// For non-root queries without additional config except columns, return a table with selection\n  // \tif (\n  // \t\t!isRoot\n  // \t\t&& initiallySelectedRelations.length === 0\n  // \t\t&& selectedExtras.length === 0\n  // \t\t&& !where\n  // \t\t&& orderBy.length === 0\n  // \t\t&& limit === undefined\n  // \t\t&& offset === undefined\n  // \t) {\n  // \t\treturn {\n  // \t\t\ttableTsKey: tableConfig.tsName,\n  // \t\t\tsql: table,\n  // \t\t\tselection: selectedColumns.map((key) => ({\n  // \t\t\t\tdbKey: tableConfig.columns[key]!.name,\n  // \t\t\t\ttsKey: key,\n  // \t\t\t\tfield: tableConfig.columns[key] as PgColumn,\n  // \t\t\t\trelationTableTsKey: undefined,\n  // \t\t\t\tisJson: false,\n  // \t\t\t\tselection: [],\n  // \t\t\t})),\n  // \t\t};\n  // \t}\n  // \tconst selectedRelationsWithoutPK:\n  // \t// Process all relations without primary keys, because they need to be joined differently and will all be on the same query level\n  // \tfor (\n  // \t\tconst {\n  // \t\t\ttsKey: selectedRelationTsKey,\n  // \t\t\tqueryConfig: selectedRelationConfigValue,\n  // \t\t\trelation,\n  // \t\t} of initiallySelectedRelations\n  // \t) {\n  // \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n  // \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n  // \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n  // \t\tconst relationTable = schema[relationTableTsName]!;\n  // \t\tif (relationTable.primaryKey.length > 0) {\n  // \t\t\tcontinue;\n  // \t\t}\n  // \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n  // \t\tconst joinOn = and(\n  // \t\t\t...normalizedRelation.fields.map((field, i) =>\n  // \t\t\t\teq(\n  // \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n  // \t\t\t\t\taliasedTableColumn(field, tableAlias),\n  // \t\t\t\t)\n  // \t\t\t),\n  // \t\t);\n  // \t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n  // \t\t\tfullSchema,\n  // \t\t\tschema,\n  // \t\t\ttableNamesMap,\n  // \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n  // \t\t\ttableConfig: schema[relationTableTsName]!,\n  // \t\t\tqueryConfig: selectedRelationConfigValue,\n  // \t\t\ttableAlias: relationTableAlias,\n  // \t\t\tjoinOn,\n  // \t\t\tnestedQueryRelation: relation,\n  // \t\t});\n  // \t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n  // \t\tjoins.push({\n  // \t\t\ton: sql`true`,\n  // \t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n  // \t\t\talias: relationTableAlias,\n  // \t\t\tjoinType: 'left',\n  // \t\t\tlateral: true,\n  // \t\t});\n  // \t\tselectedRelations.push({\n  // \t\t\tdbKey: selectedRelationTsKey,\n  // \t\t\ttsKey: selectedRelationTsKey,\n  // \t\t\tfield,\n  // \t\t\trelationTableTsKey: relationTableTsName,\n  // \t\t\tisJson: true,\n  // \t\t\tselection: builtRelation.selection,\n  // \t\t});\n  // \t}\n  // \tconst oneRelations = initiallySelectedRelations.filter((r): r is typeof r & { relation: One } =>\n  // \t\tis(r.relation, One)\n  // \t);\n  // \t// Process all One relations with PKs, because they can all be joined on the same level\n  // \tfor (\n  // \t\tconst {\n  // \t\t\ttsKey: selectedRelationTsKey,\n  // \t\t\tqueryConfig: selectedRelationConfigValue,\n  // \t\t\trelation,\n  // \t\t} of oneRelations\n  // \t) {\n  // \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n  // \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n  // \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n  // \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n  // \t\tconst relationTable = schema[relationTableTsName]!;\n  // \t\tif (relationTable.primaryKey.length === 0) {\n  // \t\t\tcontinue;\n  // \t\t}\n  // \t\tconst joinOn = and(\n  // \t\t\t...normalizedRelation.fields.map((field, i) =>\n  // \t\t\t\teq(\n  // \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n  // \t\t\t\t\taliasedTableColumn(field, tableAlias),\n  // \t\t\t\t)\n  // \t\t\t),\n  // \t\t);\n  // \t\tconst builtRelation = this.buildRelationalQueryWithPK({\n  // \t\t\tfullSchema,\n  // \t\t\tschema,\n  // \t\t\ttableNamesMap,\n  // \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n  // \t\t\ttableConfig: schema[relationTableTsName]!,\n  // \t\t\tqueryConfig: selectedRelationConfigValue,\n  // \t\t\ttableAlias: relationTableAlias,\n  // \t\t\tjoinOn,\n  // \t\t});\n  // \t\tconst field = sql`case when ${sql.identifier(relationTableAlias)} is null then null else json_build_array(${\n  // \t\t\tsql.join(\n  // \t\t\t\tbuiltRelation.selection.map(({ field }) =>\n  // \t\t\t\t\tis(field, SQL.Aliased)\n  // \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n  // \t\t\t\t\t\t: is(field, Column)\n  // \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n  // \t\t\t\t\t\t: field\n  // \t\t\t\t),\n  // \t\t\t\tsql`, `,\n  // \t\t\t)\n  // \t\t}) end`.as(selectedRelationTsKey);\n  // \t\tconst isLateralJoin = is(builtRelation.sql, SQL);\n  // \t\tjoins.push({\n  // \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n  // \t\t\ttable: is(builtRelation.sql, SQL)\n  // \t\t\t\t? new Subquery(builtRelation.sql, {}, relationTableAlias)\n  // \t\t\t\t: aliasedTable(builtRelation.sql, relationTableAlias),\n  // \t\t\talias: relationTableAlias,\n  // \t\t\tjoinType: 'left',\n  // \t\t\tlateral: is(builtRelation.sql, SQL),\n  // \t\t});\n  // \t\tselectedRelations.push({\n  // \t\t\tdbKey: selectedRelationTsKey,\n  // \t\t\ttsKey: selectedRelationTsKey,\n  // \t\t\tfield,\n  // \t\t\trelationTableTsKey: relationTableTsName,\n  // \t\t\tisJson: true,\n  // \t\t\tselection: builtRelation.selection,\n  // \t\t});\n  // \t}\n  // \tlet distinct: PgSelectConfig['distinct'];\n  // \tlet tableFrom: PgTable | Subquery = table;\n  // \t// Process first Many relation - each one requires a nested subquery\n  // \tconst manyRelation = manyRelations[0];\n  // \tif (manyRelation) {\n  // \t\tconst {\n  // \t\t\ttsKey: selectedRelationTsKey,\n  // \t\t\tqueryConfig: selectedRelationQueryConfig,\n  // \t\t\trelation,\n  // \t\t} = manyRelation;\n  // \t\tdistinct = {\n  // \t\t\ton: tableConfig.primaryKey.map((c) => aliasedTableColumn(c as PgColumn, tableAlias)),\n  // \t\t};\n  // \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n  // \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n  // \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n  // \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n  // \t\tconst joinOn = and(\n  // \t\t\t...normalizedRelation.fields.map((field, i) =>\n  // \t\t\t\teq(\n  // \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n  // \t\t\t\t\taliasedTableColumn(field, tableAlias),\n  // \t\t\t\t)\n  // \t\t\t),\n  // \t\t);\n  // \t\tconst builtRelationJoin = this.buildRelationalQueryWithPK({\n  // \t\t\tfullSchema,\n  // \t\t\tschema,\n  // \t\t\ttableNamesMap,\n  // \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n  // \t\t\ttableConfig: schema[relationTableTsName]!,\n  // \t\t\tqueryConfig: selectedRelationQueryConfig,\n  // \t\t\ttableAlias: relationTableAlias,\n  // \t\t\tjoinOn,\n  // \t\t});\n  // \t\tconst builtRelationSelectionField = sql`case when ${\n  // \t\t\tsql.identifier(relationTableAlias)\n  // \t\t} is null then '[]' else json_agg(json_build_array(${\n  // \t\t\tsql.join(\n  // \t\t\t\tbuiltRelationJoin.selection.map(({ field }) =>\n  // \t\t\t\t\tis(field, SQL.Aliased)\n  // \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n  // \t\t\t\t\t\t: is(field, Column)\n  // \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n  // \t\t\t\t\t\t: field\n  // \t\t\t\t),\n  // \t\t\t\tsql`, `,\n  // \t\t\t)\n  // \t\t})) over (partition by ${sql.join(distinct.on, sql`, `)}) end`.as(selectedRelationTsKey);\n  // \t\tconst isLateralJoin = is(builtRelationJoin.sql, SQL);\n  // \t\tjoins.push({\n  // \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n  // \t\t\ttable: isLateralJoin\n  // \t\t\t\t? new Subquery(builtRelationJoin.sql as SQL, {}, relationTableAlias)\n  // \t\t\t\t: aliasedTable(builtRelationJoin.sql as PgTable, relationTableAlias),\n  // \t\t\talias: relationTableAlias,\n  // \t\t\tjoinType: 'left',\n  // \t\t\tlateral: isLateralJoin,\n  // \t\t});\n  // \t\t// Build the \"from\" subquery with the remaining Many relations\n  // \t\tconst builtTableFrom = this.buildRelationalQueryWithPK({\n  // \t\t\tfullSchema,\n  // \t\t\tschema,\n  // \t\t\ttableNamesMap,\n  // \t\t\ttable,\n  // \t\t\ttableConfig,\n  // \t\t\tqueryConfig: {\n  // \t\t\t\t...config,\n  // \t\t\t\twhere: undefined,\n  // \t\t\t\torderBy: undefined,\n  // \t\t\t\tlimit: undefined,\n  // \t\t\t\toffset: undefined,\n  // \t\t\t\twith: manyRelations.slice(1).reduce<NonNullable<typeof config['with']>>(\n  // \t\t\t\t\t(result, { tsKey, queryConfig: configValue }) => {\n  // \t\t\t\t\t\tresult[tsKey] = configValue;\n  // \t\t\t\t\t\treturn result;\n  // \t\t\t\t\t},\n  // \t\t\t\t\t{},\n  // \t\t\t\t),\n  // \t\t\t},\n  // \t\t\ttableAlias,\n  // \t\t});\n  // \t\tselectedRelations.push({\n  // \t\t\tdbKey: selectedRelationTsKey,\n  // \t\t\ttsKey: selectedRelationTsKey,\n  // \t\t\tfield: builtRelationSelectionField,\n  // \t\t\trelationTableTsKey: relationTableTsName,\n  // \t\t\tisJson: true,\n  // \t\t\tselection: builtRelationJoin.selection,\n  // \t\t});\n  // \t\t// selection = builtTableFrom.selection.map((item) =>\n  // \t\t// \tis(item.field, SQL.Aliased)\n  // \t\t// \t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n  // \t\t// \t\t: item\n  // \t\t// );\n  // \t\t// selectionForBuild = [{\n  // \t\t// \tdbKey: '*',\n  // \t\t// \ttsKey: '*',\n  // \t\t// \tfield: sql`${sql.identifier(tableAlias)}.*`,\n  // \t\t// \tselection: [],\n  // \t\t// \tisJson: false,\n  // \t\t// \trelationTableTsKey: undefined,\n  // \t\t// }];\n  // \t\t// const newSelectionItem: (typeof selection)[number] = {\n  // \t\t// \tdbKey: selectedRelationTsKey,\n  // \t\t// \ttsKey: selectedRelationTsKey,\n  // \t\t// \tfield,\n  // \t\t// \trelationTableTsKey: relationTableTsName,\n  // \t\t// \tisJson: true,\n  // \t\t// \tselection: builtRelationJoin.selection,\n  // \t\t// };\n  // \t\t// selection.push(newSelectionItem);\n  // \t\t// selectionForBuild.push(newSelectionItem);\n  // \t\ttableFrom = is(builtTableFrom.sql, PgTable)\n  // \t\t\t? builtTableFrom.sql\n  // \t\t\t: new Subquery(builtTableFrom.sql, {}, tableAlias);\n  // \t}\n  // \tif (selectedColumns.length === 0 && selectedRelations.length === 0 && selectedExtras.length === 0) {\n  // \t\tthrow new DrizzleError(`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")`);\n  // \t}\n  // \tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'];\n  // \tfunction prepareSelectedColumns() {\n  // \t\treturn selectedColumns.map((key) => ({\n  // \t\t\tdbKey: tableConfig.columns[key]!.name,\n  // \t\t\ttsKey: key,\n  // \t\t\tfield: tableConfig.columns[key] as PgColumn,\n  // \t\t\trelationTableTsKey: undefined,\n  // \t\t\tisJson: false,\n  // \t\t\tselection: [],\n  // \t\t}));\n  // \t}\n  // \tfunction prepareSelectedExtras() {\n  // \t\treturn selectedExtras.map((item) => ({\n  // \t\t\tdbKey: item.value.fieldAlias,\n  // \t\t\ttsKey: item.tsKey,\n  // \t\t\tfield: item.value,\n  // \t\t\trelationTableTsKey: undefined,\n  // \t\t\tisJson: false,\n  // \t\t\tselection: [],\n  // \t\t}));\n  // \t}\n  // \tif (isRoot) {\n  // \t\tselection = [\n  // \t\t\t...prepareSelectedColumns(),\n  // \t\t\t...prepareSelectedExtras(),\n  // \t\t];\n  // \t}\n  // \tif (hasUserDefinedWhere || orderBy.length > 0) {\n  // \t\ttableFrom = new Subquery(\n  // \t\t\tthis.buildSelectQuery({\n  // \t\t\t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n  // \t\t\t\tfields: {},\n  // \t\t\t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n  // \t\t\t\t\tpath: [],\n  // \t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n  // \t\t\t\t})),\n  // \t\t\t\tjoins,\n  // \t\t\t\tdistinct,\n  // \t\t\t}),\n  // \t\t\t{},\n  // \t\t\ttableAlias,\n  // \t\t);\n  // \t\tselectionForBuild = selection.map((item) =>\n  // \t\t\tis(item.field, SQL.Aliased)\n  // \t\t\t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n  // \t\t\t\t: item\n  // \t\t);\n  // \t\tjoins = [];\n  // \t\tdistinct = undefined;\n  // \t}\n  // \tconst result = this.buildSelectQuery({\n  // \t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n  // \t\tfields: {},\n  // \t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n  // \t\t\tpath: [],\n  // \t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n  // \t\t})),\n  // \t\twhere,\n  // \t\tlimit,\n  // \t\toffset,\n  // \t\tjoins,\n  // \t\torderBy,\n  // \t\tdistinct,\n  // \t});\n  // \treturn {\n  // \t\ttableTsKey: tableConfig.tsName,\n  // \t\tsql: result,\n  // \t\tselection,\n  // \t};\n  // }\n  buildRelationalQueryWithoutPK({\n    fullSchema,\n    schema,\n    tableNamesMap,\n    table,\n    tableConfig,\n    queryConfig: config,\n    tableAlias,\n    nestedQueryRelation,\n    joinOn\n  }) {\n    let selection = [];\n    let limit, offset, orderBy = [], where;\n    const joins = [];\n    if (config === true) {\n      const selectionEntries = Object.entries(tableConfig.columns);\n      selection = selectionEntries.map(([key, value]) => ({\n        dbKey: value.name,\n        tsKey: key,\n        field: aliasedTableColumn(value, tableAlias),\n        relationTableTsKey: void 0,\n        isJson: false,\n        selection: []\n      }));\n    } else {\n      const aliasedColumns = Object.fromEntries(\n        Object.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)])\n      );\n      if (config.where) {\n        const whereSql = typeof config.where === \"function\" ? config.where(aliasedColumns, getOperators()) : config.where;\n        where = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n      }\n      const fieldsSelection = [];\n      let selectedColumns = [];\n      if (config.columns) {\n        let isIncludeMode = false;\n        for (const [field, value] of Object.entries(config.columns)) {\n          if (value === void 0) {\n            continue;\n          }\n          if (field in tableConfig.columns) {\n            if (!isIncludeMode && value === true) {\n              isIncludeMode = true;\n            }\n            selectedColumns.push(field);\n          }\n        }\n        if (selectedColumns.length > 0) {\n          selectedColumns = isIncludeMode ? selectedColumns.filter((c) => config.columns?.[c] === true) : Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n        }\n      } else {\n        selectedColumns = Object.keys(tableConfig.columns);\n      }\n      for (const field of selectedColumns) {\n        const column = tableConfig.columns[field];\n        fieldsSelection.push({ tsKey: field, value: column });\n      }\n      let selectedRelations = [];\n      if (config.with) {\n        selectedRelations = Object.entries(config.with).filter((entry) => !!entry[1]).map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey] }));\n      }\n      let extras;\n      if (config.extras) {\n        extras = typeof config.extras === \"function\" ? config.extras(aliasedColumns, { sql }) : config.extras;\n        for (const [tsKey, value] of Object.entries(extras)) {\n          fieldsSelection.push({\n            tsKey,\n            value: mapColumnsInAliasedSQLToAlias(value, tableAlias)\n          });\n        }\n      }\n      for (const { tsKey, value } of fieldsSelection) {\n        selection.push({\n          dbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey].name,\n          tsKey,\n          field: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n          relationTableTsKey: void 0,\n          isJson: false,\n          selection: []\n        });\n      }\n      let orderByOrig = typeof config.orderBy === \"function\" ? config.orderBy(aliasedColumns, getOrderByOperators()) : config.orderBy ?? [];\n      if (!Array.isArray(orderByOrig)) {\n        orderByOrig = [orderByOrig];\n      }\n      orderBy = orderByOrig.map((orderByValue) => {\n        if (is(orderByValue, Column)) {\n          return aliasedTableColumn(orderByValue, tableAlias);\n        }\n        return mapColumnsInSQLToAlias(orderByValue, tableAlias);\n      });\n      limit = config.limit;\n      offset = config.offset;\n      for (const {\n        tsKey: selectedRelationTsKey,\n        queryConfig: selectedRelationConfigValue,\n        relation\n      } of selectedRelations) {\n        const normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n        const relationTableName = getTableUniqueName(relation.referencedTable);\n        const relationTableTsName = tableNamesMap[relationTableName];\n        const relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n        const joinOn2 = and(\n          ...normalizedRelation.fields.map(\n            (field2, i) => eq(\n              aliasedTableColumn(normalizedRelation.references[i], relationTableAlias),\n              aliasedTableColumn(field2, tableAlias)\n            )\n          )\n        );\n        const builtRelation = this.buildRelationalQueryWithoutPK({\n          fullSchema,\n          schema,\n          tableNamesMap,\n          table: fullSchema[relationTableTsName],\n          tableConfig: schema[relationTableTsName],\n          queryConfig: is(relation, One) ? selectedRelationConfigValue === true ? { limit: 1 } : { ...selectedRelationConfigValue, limit: 1 } : selectedRelationConfigValue,\n          tableAlias: relationTableAlias,\n          joinOn: joinOn2,\n          nestedQueryRelation: relation\n        });\n        const field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier(\"data\")}`.as(selectedRelationTsKey);\n        joins.push({\n          on: sql`true`,\n          table: new Subquery(builtRelation.sql, {}, relationTableAlias),\n          alias: relationTableAlias,\n          joinType: \"left\",\n          lateral: true\n        });\n        selection.push({\n          dbKey: selectedRelationTsKey,\n          tsKey: selectedRelationTsKey,\n          field,\n          relationTableTsKey: relationTableTsName,\n          isJson: true,\n          selection: builtRelation.selection\n        });\n      }\n    }\n    if (selection.length === 0) {\n      throw new DrizzleError({ message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")` });\n    }\n    let result;\n    where = and(joinOn, where);\n    if (nestedQueryRelation) {\n      let field = sql`json_build_array(${sql.join(\n        selection.map(\n          ({ field: field2, tsKey, isJson }) => isJson ? sql`${sql.identifier(`${tableAlias}_${tsKey}`)}.${sql.identifier(\"data\")}` : is(field2, SQL.Aliased) ? field2.sql : field2\n        ),\n        sql`, `\n      )})`;\n      if (is(nestedQueryRelation, Many)) {\n        field = sql`coalesce(json_agg(${field}${orderBy.length > 0 ? sql` order by ${sql.join(orderBy, sql`, `)}` : void 0}), '[]'::json)`;\n      }\n      const nestedSelection = [{\n        dbKey: \"data\",\n        tsKey: \"data\",\n        field: field.as(\"data\"),\n        isJson: true,\n        relationTableTsKey: tableConfig.tsName,\n        selection\n      }];\n      const needsSubquery = limit !== void 0 || offset !== void 0 || orderBy.length > 0;\n      if (needsSubquery) {\n        result = this.buildSelectQuery({\n          table: aliasedTable(table, tableAlias),\n          fields: {},\n          fieldsFlat: [{\n            path: [],\n            field: sql.raw(\"*\")\n          }],\n          where,\n          limit,\n          offset,\n          orderBy,\n          setOperators: []\n        });\n        where = void 0;\n        limit = void 0;\n        offset = void 0;\n        orderBy = [];\n      } else {\n        result = aliasedTable(table, tableAlias);\n      }\n      result = this.buildSelectQuery({\n        table: is(result, PgTable) ? result : new Subquery(result, {}, tableAlias),\n        fields: {},\n        fieldsFlat: nestedSelection.map(({ field: field2 }) => ({\n          path: [],\n          field: is(field2, Column) ? aliasedTableColumn(field2, tableAlias) : field2\n        })),\n        joins,\n        where,\n        limit,\n        offset,\n        orderBy,\n        setOperators: []\n      });\n    } else {\n      result = this.buildSelectQuery({\n        table: aliasedTable(table, tableAlias),\n        fields: {},\n        fieldsFlat: selection.map(({ field }) => ({\n          path: [],\n          field: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field\n        })),\n        joins,\n        where,\n        limit,\n        offset,\n        orderBy,\n        setOperators: []\n      });\n    }\n    return {\n      tableTsKey: tableConfig.tsName,\n      sql: result,\n      selection\n    };\n  }\n}\nexport {\n  PgDialect\n};\n//# sourceMappingURL=dialect.js.map", "import { ColumnAliasProxy<PERSON>and<PERSON>, TableAliasProxyHandler } from \"./alias.js\";\nimport { Column } from \"./column.js\";\nimport { entityKind, is } from \"./entity.js\";\nimport { SQL, View } from \"./sql/sql.js\";\nimport { Subquery } from \"./subquery.js\";\nimport { ViewBaseConfig } from \"./view-common.js\";\nclass SelectionProxyHandler {\n  static [entityKind] = \"SelectionProxyHandler\";\n  config;\n  constructor(config) {\n    this.config = { ...config };\n  }\n  get(subquery, prop) {\n    if (prop === \"_\") {\n      return {\n        ...subquery[\"_\"],\n        selectedFields: new Proxy(\n          subquery._.selectedFields,\n          this\n        )\n      };\n    }\n    if (prop === ViewBaseConfig) {\n      return {\n        ...subquery[ViewBaseConfig],\n        selectedFields: new Proxy(\n          subquery[ViewBaseConfig].selectedFields,\n          this\n        )\n      };\n    }\n    if (typeof prop === \"symbol\") {\n      return subquery[prop];\n    }\n    const columns = is(subquery, Subquery) ? subquery._.selectedFields : is(subquery, View) ? subquery[ViewBaseConfig].selectedFields : subquery;\n    const value = columns[prop];\n    if (is(value, SQL.Aliased)) {\n      if (this.config.sqlAliasedBehavior === \"sql\" && !value.isSelectionField) {\n        return value.sql;\n      }\n      const newValue = value.clone();\n      newValue.isSelectionField = true;\n      return newValue;\n    }\n    if (is(value, SQL)) {\n      if (this.config.sqlBehavior === \"sql\") {\n        return value;\n      }\n      throw new Error(\n        `You tried to reference \"${prop}\" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using \".as('alias')\" method.`\n      );\n    }\n    if (is(value, Column)) {\n      if (this.config.alias) {\n        return new Proxy(\n          value,\n          new ColumnAliasProxyHandler(\n            new Proxy(\n              value.table,\n              new TableAliasProxyHandler(this.config.alias, this.config.replaceOriginalName ?? false)\n            )\n          )\n        );\n      }\n      return value;\n    }\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n    return new Proxy(value, new SelectionProxyHandler(this.config));\n  }\n}\nexport {\n  SelectionProxyHandler\n};\n//# sourceMappingURL=selection-proxy.js.map", "import { entityKind } from \"../entity.js\";\nclass TypedQueryBuilder {\n  static [entityKind] = \"TypedQueryBuilder\";\n  /** @internal */\n  getSelectedFields() {\n    return this._.selectedFields;\n  }\n}\nexport {\n  TypedQueryBuilder\n};\n//# sourceMappingURL=query-builder.js.map", "import { entityKind } from \"./entity.js\";\nclass QueryPromise {\n  static [entityKind] = \"QueryPromise\";\n  [Symbol.toStringTag] = \"QueryPromise\";\n  catch(onRejected) {\n    return this.then(void 0, onRejected);\n  }\n  finally(onFinally) {\n    return this.then(\n      (value) => {\n        onFinally?.();\n        return value;\n      },\n      (reason) => {\n        onFinally?.();\n        throw reason;\n      }\n    );\n  }\n  then(onFulfilled, onRejected) {\n    return this.execute().then(onFulfilled, onRejected);\n  }\n}\nexport {\n  QueryPromise\n};\n//# sourceMappingURL=query-promise.js.map", "import { is } from \"../entity.js\";\nimport { PgTable } from \"./table.js\";\nimport { SQL } from \"../sql/sql.js\";\nimport { Subquery } from \"../subquery.js\";\nimport { Schema, Table } from \"../table.js\";\nimport { ViewBaseConfig } from \"../view-common.js\";\nimport { CheckBuilder } from \"./checks.js\";\nimport { ForeignKeyBuilder } from \"./foreign-keys.js\";\nimport { IndexBuilder } from \"./indexes.js\";\nimport { PgPolicy } from \"./policies.js\";\nimport { PrimaryKeyBuilder } from \"./primary-keys.js\";\nimport { UniqueConstraintBuilder } from \"./unique-constraint.js\";\nimport { PgViewConfig } from \"./view-common.js\";\nimport { PgMaterializedViewConfig } from \"./view.js\";\nfunction getTableConfig(table) {\n  const columns = Object.values(table[Table.Symbol.Columns]);\n  const indexes = [];\n  const checks = [];\n  const primaryKeys = [];\n  const foreignKeys = Object.values(table[PgTable.Symbol.InlineForeignKeys]);\n  const uniqueConstraints = [];\n  const name = table[Table.Symbol.Name];\n  const schema = table[Table.Symbol.Schema];\n  const policies = [];\n  const enableRLS = table[PgTable.Symbol.EnableRLS];\n  const extraConfigBuilder = table[PgTable.Symbol.ExtraConfigBuilder];\n  if (extraConfigBuilder !== void 0) {\n    const extraConfig = extraConfigBuilder(table[Table.Symbol.ExtraConfigColumns]);\n    const extraValues = Array.isArray(extraConfig) ? extraConfig.flat(1) : Object.values(extraConfig);\n    for (const builder of extraValues) {\n      if (is(builder, IndexBuilder)) {\n        indexes.push(builder.build(table));\n      } else if (is(builder, CheckBuilder)) {\n        checks.push(builder.build(table));\n      } else if (is(builder, UniqueConstraintBuilder)) {\n        uniqueConstraints.push(builder.build(table));\n      } else if (is(builder, PrimaryKeyBuilder)) {\n        primaryKeys.push(builder.build(table));\n      } else if (is(builder, ForeignKeyBuilder)) {\n        foreignKeys.push(builder.build(table));\n      } else if (is(builder, PgPolicy)) {\n        policies.push(builder);\n      }\n    }\n  }\n  return {\n    columns,\n    indexes,\n    foreignKeys,\n    checks,\n    primaryKeys,\n    uniqueConstraints,\n    name,\n    schema,\n    policies,\n    enableRLS\n  };\n}\nfunction extractUsedTable(table) {\n  if (is(table, PgTable)) {\n    return [table[Schema] ? `${table[Schema]}.${table[Table.Symbol.BaseName]}` : table[Table.Symbol.BaseName]];\n  }\n  if (is(table, Subquery)) {\n    return table._.usedTables ?? [];\n  }\n  if (is(table, SQL)) {\n    return table.usedTables ?? [];\n  }\n  return [];\n}\nfunction getViewConfig(view) {\n  return {\n    ...view[ViewBaseConfig],\n    ...view[PgViewConfig]\n  };\n}\nfunction getMaterializedViewConfig(view) {\n  return {\n    ...view[ViewBaseConfig],\n    ...view[PgMaterializedViewConfig]\n  };\n}\nexport {\n  extractUsedTable,\n  getMaterializedViewConfig,\n  getTableConfig,\n  getViewConfig\n};\n//# sourceMappingURL=utils.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { PgViewBase } from \"../view-base.js\";\nimport { TypedQueryBuilder } from \"../../query-builders/query-builder.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { SQL, View } from \"../../sql/sql.js\";\nimport { Subquery } from \"../../subquery.js\";\nimport { Table } from \"../../table.js\";\nimport { tracer } from \"../../tracing.js\";\nimport {\n  applyMixins,\n  getTableColumns,\n  getTableLikeName,\n  haveSameKeys\n} from \"../../utils.js\";\nimport { orderSelectedFields } from \"../../utils.js\";\nimport { ViewBaseConfig } from \"../../view-common.js\";\nimport { extractUsedTable } from \"../utils.js\";\nclass PgSelectBuilder {\n  static [entityKind] = \"PgSelectBuilder\";\n  fields;\n  session;\n  dialect;\n  withList = [];\n  distinct;\n  constructor(config) {\n    this.fields = config.fields;\n    this.session = config.session;\n    this.dialect = config.dialect;\n    if (config.withList) {\n      this.withList = config.withList;\n    }\n    this.distinct = config.distinct;\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  /**\n   * Specify the table, subquery, or other target that you're\n   * building a select query against.\n   *\n   * {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FROM | Postgres from documentation}\n   */\n  from(source) {\n    const isPartialSelect = !!this.fields;\n    const src = source;\n    let fields;\n    if (this.fields) {\n      fields = this.fields;\n    } else if (is(src, Subquery)) {\n      fields = Object.fromEntries(\n        Object.keys(src._.selectedFields).map((key) => [key, src[key]])\n      );\n    } else if (is(src, PgViewBase)) {\n      fields = src[ViewBaseConfig].selectedFields;\n    } else if (is(src, SQL)) {\n      fields = {};\n    } else {\n      fields = getTableColumns(src);\n    }\n    return new PgSelectBase({\n      table: src,\n      fields,\n      isPartialSelect,\n      session: this.session,\n      dialect: this.dialect,\n      withList: this.withList,\n      distinct: this.distinct\n    }).setToken(this.authToken);\n  }\n}\nclass PgSelectQueryBuilderBase extends TypedQueryBuilder {\n  static [entityKind] = \"PgSelectQueryBuilder\";\n  _;\n  config;\n  joinsNotNullableMap;\n  tableName;\n  isPartialSelect;\n  session;\n  dialect;\n  cacheConfig = void 0;\n  usedTables = /* @__PURE__ */ new Set();\n  constructor({ table, fields, isPartialSelect, session, dialect, withList, distinct }) {\n    super();\n    this.config = {\n      withList,\n      table,\n      fields: { ...fields },\n      distinct,\n      setOperators: []\n    };\n    this.isPartialSelect = isPartialSelect;\n    this.session = session;\n    this.dialect = dialect;\n    this._ = {\n      selectedFields: fields,\n      config: this.config\n    };\n    this.tableName = getTableLikeName(table);\n    this.joinsNotNullableMap = typeof this.tableName === \"string\" ? { [this.tableName]: true } : {};\n    for (const item of extractUsedTable(table)) this.usedTables.add(item);\n  }\n  /** @internal */\n  getUsedTables() {\n    return [...this.usedTables];\n  }\n  createJoin(joinType, lateral) {\n    return (table, on) => {\n      const baseTableName = this.tableName;\n      const tableName = getTableLikeName(table);\n      for (const item of extractUsedTable(table)) this.usedTables.add(item);\n      if (typeof tableName === \"string\" && this.config.joins?.some((join) => join.alias === tableName)) {\n        throw new Error(`Alias \"${tableName}\" is already used in this query`);\n      }\n      if (!this.isPartialSelect) {\n        if (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === \"string\") {\n          this.config.fields = {\n            [baseTableName]: this.config.fields\n          };\n        }\n        if (typeof tableName === \"string\" && !is(table, SQL)) {\n          const selection = is(table, Subquery) ? table._.selectedFields : is(table, View) ? table[ViewBaseConfig].selectedFields : table[Table.Symbol.Columns];\n          this.config.fields[tableName] = selection;\n        }\n      }\n      if (typeof on === \"function\") {\n        on = on(\n          new Proxy(\n            this.config.fields,\n            new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n          )\n        );\n      }\n      if (!this.config.joins) {\n        this.config.joins = [];\n      }\n      this.config.joins.push({ on, table, joinType, alias: tableName, lateral });\n      if (typeof tableName === \"string\") {\n        switch (joinType) {\n          case \"left\": {\n            this.joinsNotNullableMap[tableName] = false;\n            break;\n          }\n          case \"right\": {\n            this.joinsNotNullableMap = Object.fromEntries(\n              Object.entries(this.joinsNotNullableMap).map(([key]) => [key, false])\n            );\n            this.joinsNotNullableMap[tableName] = true;\n            break;\n          }\n          case \"cross\":\n          case \"inner\": {\n            this.joinsNotNullableMap[tableName] = true;\n            break;\n          }\n          case \"full\": {\n            this.joinsNotNullableMap = Object.fromEntries(\n              Object.entries(this.joinsNotNullableMap).map(([key]) => [key, false])\n            );\n            this.joinsNotNullableMap[tableName] = false;\n            break;\n          }\n        }\n      }\n      return this;\n    };\n  }\n  /**\n   * Executes a `left join` operation by adding another table to the current query.\n   *\n   * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User; pets: Pet | null; }[] = await db.select()\n   *   .from(users)\n   *   .leftJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number; petId: number | null; }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .leftJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  leftJoin = this.createJoin(\"left\", false);\n  /**\n   * Executes a `left join lateral` operation by adding subquery to the current query.\n   *\n   * A `lateral` join allows the right-hand expression to refer to columns from the left-hand side.\n   *\n   * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#left-join-lateral}\n   *\n   * @param table the subquery to join.\n   * @param on the `on` clause.\n   */\n  leftJoinLateral = this.createJoin(\"left\", true);\n  /**\n   * Executes a `right join` operation by adding another table to the current query.\n   *\n   * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User | null; pets: Pet; }[] = await db.select()\n   *   .from(users)\n   *   .rightJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number | null; petId: number; }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .rightJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  rightJoin = this.createJoin(\"right\", false);\n  /**\n   * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n   *\n   * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User; pets: Pet; }[] = await db.select()\n   *   .from(users)\n   *   .innerJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number; petId: number; }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .innerJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  innerJoin = this.createJoin(\"inner\", false);\n  /**\n   * Executes an `inner join lateral` operation, creating a new table by combining rows from two queries that have matching values.\n   *\n   * A `lateral` join allows the right-hand expression to refer to columns from the left-hand side.\n   *\n   * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join-lateral}\n   *\n   * @param table the subquery to join.\n   * @param on the `on` clause.\n   */\n  innerJoinLateral = this.createJoin(\"inner\", true);\n  /**\n   * Executes a `full join` operation by combining rows from two tables into a new table.\n   *\n   * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n   *\n   * @param table the table to join.\n   * @param on the `on` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users and their pets\n   * const usersWithPets: { user: User | null; pets: Pet | null; }[] = await db.select()\n   *   .from(users)\n   *   .fullJoin(pets, eq(users.id, pets.ownerId))\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number | null; petId: number | null; }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .fullJoin(pets, eq(users.id, pets.ownerId))\n   * ```\n   */\n  fullJoin = this.createJoin(\"full\", false);\n  /**\n   * Executes a `cross join` operation by combining rows from two tables into a new table.\n   *\n   * Calling this method retrieves all rows from both main and joined tables, merging all rows from each table.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#cross-join}\n   *\n   * @param table the table to join.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all users, each user with every pet\n   * const usersWithPets: { user: User; pets: Pet; }[] = await db.select()\n   *   .from(users)\n   *   .crossJoin(pets)\n   *\n   * // Select userId and petId\n   * const usersIdsAndPetIds: { userId: number; petId: number; }[] = await db.select({\n   *   userId: users.id,\n   *   petId: pets.id,\n   * })\n   *   .from(users)\n   *   .crossJoin(pets)\n   * ```\n   */\n  crossJoin = this.createJoin(\"cross\", false);\n  /**\n   * Executes a `cross join lateral` operation by combining rows from two queries into a new table.\n   *\n   * A `lateral` join allows the right-hand expression to refer to columns from the left-hand side.\n   *\n   * Calling this method retrieves all rows from both main and joined queries, merging all rows from each query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/joins#cross-join-lateral}\n   *\n   * @param table the query to join.\n   */\n  crossJoinLateral = this.createJoin(\"cross\", true);\n  createSetOperator(type, isAll) {\n    return (rightSelection) => {\n      const rightSelect = typeof rightSelection === \"function\" ? rightSelection(getPgSetOperators()) : rightSelection;\n      if (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n        throw new Error(\n          \"Set operator error (union / intersect / except): selected fields are not the same or are in a different order\"\n        );\n      }\n      this.config.setOperators.push({ type, isAll, rightSelect });\n      return this;\n    };\n  }\n  /**\n   * Adds `union` set operator to the query.\n   *\n   * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all unique names from customers and users tables\n   * await db.select({ name: users.name })\n   *   .from(users)\n   *   .union(\n   *     db.select({ name: customers.name }).from(customers)\n   *   );\n   * // or\n   * import { union } from 'drizzle-orm/pg-core'\n   *\n   * await union(\n   *   db.select({ name: users.name }).from(users),\n   *   db.select({ name: customers.name }).from(customers)\n   * );\n   * ```\n   */\n  union = this.createSetOperator(\"union\", false);\n  /**\n   * Adds `union all` set operator to the query.\n   *\n   * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all transaction ids from both online and in-store sales\n   * await db.select({ transaction: onlineSales.transactionId })\n   *   .from(onlineSales)\n   *   .unionAll(\n   *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n   *   );\n   * // or\n   * import { unionAll } from 'drizzle-orm/pg-core'\n   *\n   * await unionAll(\n   *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n   *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n   * );\n   * ```\n   */\n  unionAll = this.createSetOperator(\"union\", true);\n  /**\n   * Adds `intersect` set operator to the query.\n   *\n   * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select course names that are offered in both departments A and B\n   * await db.select({ courseName: depA.courseName })\n   *   .from(depA)\n   *   .intersect(\n   *     db.select({ courseName: depB.courseName }).from(depB)\n   *   );\n   * // or\n   * import { intersect } from 'drizzle-orm/pg-core'\n   *\n   * await intersect(\n   *   db.select({ courseName: depA.courseName }).from(depA),\n   *   db.select({ courseName: depB.courseName }).from(depB)\n   * );\n   * ```\n   */\n  intersect = this.createSetOperator(\"intersect\", false);\n  /**\n   * Adds `intersect all` set operator to the query.\n   *\n   * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all products and quantities that are ordered by both regular and VIP customers\n   * await db.select({\n   *   productId: regularCustomerOrders.productId,\n   *   quantityOrdered: regularCustomerOrders.quantityOrdered\n   * })\n   * .from(regularCustomerOrders)\n   * .intersectAll(\n   *   db.select({\n   *     productId: vipCustomerOrders.productId,\n   *     quantityOrdered: vipCustomerOrders.quantityOrdered\n   *   })\n   *   .from(vipCustomerOrders)\n   * );\n   * // or\n   * import { intersectAll } from 'drizzle-orm/pg-core'\n   *\n   * await intersectAll(\n   *   db.select({\n   *     productId: regularCustomerOrders.productId,\n   *     quantityOrdered: regularCustomerOrders.quantityOrdered\n   *   })\n   *   .from(regularCustomerOrders),\n   *   db.select({\n   *     productId: vipCustomerOrders.productId,\n   *     quantityOrdered: vipCustomerOrders.quantityOrdered\n   *   })\n   *   .from(vipCustomerOrders)\n   * );\n   * ```\n   */\n  intersectAll = this.createSetOperator(\"intersect\", true);\n  /**\n   * Adds `except` set operator to the query.\n   *\n   * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all courses offered in department A but not in department B\n   * await db.select({ courseName: depA.courseName })\n   *   .from(depA)\n   *   .except(\n   *     db.select({ courseName: depB.courseName }).from(depB)\n   *   );\n   * // or\n   * import { except } from 'drizzle-orm/pg-core'\n   *\n   * await except(\n   *   db.select({ courseName: depA.courseName }).from(depA),\n   *   db.select({ courseName: depB.courseName }).from(depB)\n   * );\n   * ```\n   */\n  except = this.createSetOperator(\"except\", false);\n  /**\n   * Adds `except all` set operator to the query.\n   *\n   * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all products that are ordered by regular customers but not by VIP customers\n   * await db.select({\n   *   productId: regularCustomerOrders.productId,\n   *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n   * })\n   * .from(regularCustomerOrders)\n   * .exceptAll(\n   *   db.select({\n   *     productId: vipCustomerOrders.productId,\n   *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n   *   })\n   *   .from(vipCustomerOrders)\n   * );\n   * // or\n   * import { exceptAll } from 'drizzle-orm/pg-core'\n   *\n   * await exceptAll(\n   *   db.select({\n   *     productId: regularCustomerOrders.productId,\n   *     quantityOrdered: regularCustomerOrders.quantityOrdered\n   *   })\n   *   .from(regularCustomerOrders),\n   *   db.select({\n   *     productId: vipCustomerOrders.productId,\n   *     quantityOrdered: vipCustomerOrders.quantityOrdered\n   *   })\n   *   .from(vipCustomerOrders)\n   * );\n   * ```\n   */\n  exceptAll = this.createSetOperator(\"except\", true);\n  /** @internal */\n  addSetOperators(setOperators) {\n    this.config.setOperators.push(...setOperators);\n    return this;\n  }\n  /**\n   * Adds a `where` clause to the query.\n   *\n   * Calling this method will select only those rows that fulfill a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n   *\n   * @param where the `where` clause.\n   *\n   * @example\n   * You can use conditional operators and `sql function` to filter the rows to be selected.\n   *\n   * ```ts\n   * // Select all cars with green color\n   * await db.select().from(cars).where(eq(cars.color, 'green'));\n   * // or\n   * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n   * ```\n   *\n   * You can logically combine conditional operators with `and()` and `or()` operators:\n   *\n   * ```ts\n   * // Select all BMW cars with a green color\n   * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n   *\n   * // Select all cars with the green or blue color\n   * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n   * ```\n   */\n  where(where) {\n    if (typeof where === \"function\") {\n      where = where(\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n        )\n      );\n    }\n    this.config.where = where;\n    return this;\n  }\n  /**\n   * Adds a `having` clause to the query.\n   *\n   * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n   *\n   * @param having the `having` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Select all brands with more than one car\n   * await db.select({\n   * \tbrand: cars.brand,\n   * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n   * })\n   *   .from(cars)\n   *   .groupBy(cars.brand)\n   *   .having(({ count }) => gt(count, 1));\n   * ```\n   */\n  having(having) {\n    if (typeof having === \"function\") {\n      having = having(\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n        )\n      );\n    }\n    this.config.having = having;\n    return this;\n  }\n  groupBy(...columns) {\n    if (typeof columns[0] === \"function\") {\n      const groupBy = columns[0](\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"alias\", sqlBehavior: \"sql\" })\n        )\n      );\n      this.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n    } else {\n      this.config.groupBy = columns;\n    }\n    return this;\n  }\n  orderBy(...columns) {\n    if (typeof columns[0] === \"function\") {\n      const orderBy = columns[0](\n        new Proxy(\n          this.config.fields,\n          new SelectionProxyHandler({ sqlAliasedBehavior: \"alias\", sqlBehavior: \"sql\" })\n        )\n      );\n      const orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n      if (this.config.setOperators.length > 0) {\n        this.config.setOperators.at(-1).orderBy = orderByArray;\n      } else {\n        this.config.orderBy = orderByArray;\n      }\n    } else {\n      const orderByArray = columns;\n      if (this.config.setOperators.length > 0) {\n        this.config.setOperators.at(-1).orderBy = orderByArray;\n      } else {\n        this.config.orderBy = orderByArray;\n      }\n    }\n    return this;\n  }\n  /**\n   * Adds a `limit` clause to the query.\n   *\n   * Calling this method will set the maximum number of rows that will be returned by this query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n   *\n   * @param limit the `limit` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Get the first 10 people from this query.\n   * await db.select().from(people).limit(10);\n   * ```\n   */\n  limit(limit) {\n    if (this.config.setOperators.length > 0) {\n      this.config.setOperators.at(-1).limit = limit;\n    } else {\n      this.config.limit = limit;\n    }\n    return this;\n  }\n  /**\n   * Adds an `offset` clause to the query.\n   *\n   * Calling this method will skip a number of rows when returning results from this query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n   *\n   * @param offset the `offset` clause.\n   *\n   * @example\n   *\n   * ```ts\n   * // Get the 10th-20th people from this query.\n   * await db.select().from(people).offset(10).limit(10);\n   * ```\n   */\n  offset(offset) {\n    if (this.config.setOperators.length > 0) {\n      this.config.setOperators.at(-1).offset = offset;\n    } else {\n      this.config.offset = offset;\n    }\n    return this;\n  }\n  /**\n   * Adds a `for` clause to the query.\n   *\n   * Calling this method will specify a lock strength for this query that controls how strictly it acquires exclusive access to the rows being queried.\n   *\n   * See docs: {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FOR-UPDATE-SHARE}\n   *\n   * @param strength the lock strength.\n   * @param config the lock configuration.\n   */\n  for(strength, config = {}) {\n    this.config.lockingClause = { strength, config };\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildSelectQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  as(alias) {\n    const usedTables = [];\n    usedTables.push(...extractUsedTable(this.config.table));\n    if (this.config.joins) {\n      for (const it of this.config.joins) usedTables.push(...extractUsedTable(it.table));\n    }\n    return new Proxy(\n      new Subquery(this.getSQL(), this.config.fields, alias, false, [...new Set(usedTables)]),\n      new SelectionProxyHandler({ alias, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n    );\n  }\n  /** @internal */\n  getSelectedFields() {\n    return new Proxy(\n      this.config.fields,\n      new SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n    );\n  }\n  $dynamic() {\n    return this;\n  }\n  $withCache(config) {\n    this.cacheConfig = config === void 0 ? { config: {}, enable: true, autoInvalidate: true } : config === false ? { enable: false } : { enable: true, autoInvalidate: true, ...config };\n    return this;\n  }\n}\nclass PgSelectBase extends PgSelectQueryBuilderBase {\n  static [entityKind] = \"PgSelect\";\n  /** @internal */\n  _prepare(name) {\n    const { session, config, dialect, joinsNotNullableMap, authToken, cacheConfig, usedTables } = this;\n    if (!session) {\n      throw new Error(\"Cannot execute a query on a query builder. Please use a database instance instead.\");\n    }\n    const { fields } = config;\n    return tracer.startActiveSpan(\"drizzle.prepareQuery\", () => {\n      const fieldsList = orderSelectedFields(fields);\n      const query = session.prepareQuery(dialect.sqlToQuery(this.getSQL()), fieldsList, name, true, void 0, {\n        type: \"select\",\n        tables: [...usedTables]\n      }, cacheConfig);\n      query.joinsNotNullableMap = joinsNotNullableMap;\n      return query.setToken(authToken);\n    });\n  }\n  /**\n   * Create a prepared statement for this query. This allows\n   * the database to remember this query for the given session\n   * and call it by name, rather than specifying the full query.\n   *\n   * {@link https://www.postgresql.org/docs/current/sql-prepare.html | Postgres prepare documentation}\n   */\n  prepare(name) {\n    return this._prepare(name);\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  execute = (placeholderValues) => {\n    return tracer.startActiveSpan(\"drizzle.operation\", () => {\n      return this._prepare().execute(placeholderValues, this.authToken);\n    });\n  };\n}\napplyMixins(PgSelectBase, [QueryPromise]);\nfunction createSetOperator(type, isAll) {\n  return (leftSelect, rightSelect, ...restSelects) => {\n    const setOperators = [rightSelect, ...restSelects].map((select) => ({\n      type,\n      isAll,\n      rightSelect: select\n    }));\n    for (const setOperator of setOperators) {\n      if (!haveSameKeys(leftSelect.getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n        throw new Error(\n          \"Set operator error (union / intersect / except): selected fields are not the same or are in a different order\"\n        );\n      }\n    }\n    return leftSelect.addSetOperators(setOperators);\n  };\n}\nconst getPgSetOperators = () => ({\n  union,\n  unionAll,\n  intersect,\n  intersectAll,\n  except,\n  exceptAll\n});\nconst union = createSetOperator(\"union\", false);\nconst unionAll = createSetOperator(\"union\", true);\nconst intersect = createSetOperator(\"intersect\", false);\nconst intersectAll = createSetOperator(\"intersect\", true);\nconst except = createSetOperator(\"except\", false);\nconst exceptAll = createSetOperator(\"except\", true);\nexport {\n  PgSelectBase,\n  PgSelectBuilder,\n  PgSelectQueryBuilderBase,\n  except,\n  exceptAll,\n  intersect,\n  intersectAll,\n  union,\n  unionAll\n};\n//# sourceMappingURL=select.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { PgDialect } from \"../dialect.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { WithSubquery } from \"../../subquery.js\";\nimport { PgSelectBuilder } from \"./select.js\";\nclass QueryBuilder {\n  static [entityKind] = \"PgQueryBuilder\";\n  dialect;\n  dialectConfig;\n  constructor(dialect) {\n    this.dialect = is(dialect, PgDialect) ? dialect : void 0;\n    this.dialectConfig = is(dialect, PgDialect) ? void 0 : dialect;\n  }\n  $with = (alias, selection) => {\n    const queryBuilder = this;\n    const as = (qb) => {\n      if (typeof qb === \"function\") {\n        qb = qb(queryBuilder);\n      }\n      return new Proxy(\n        new WithSubquery(\n          qb.getSQL(),\n          selection ?? (\"getSelectedFields\" in qb ? qb.getSelectedFields() ?? {} : {}),\n          alias,\n          true\n        ),\n        new SelectionProxyHandler({ alias, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n      );\n    };\n    return { as };\n  };\n  with(...queries) {\n    const self = this;\n    function select(fields) {\n      return new PgSelectBuilder({\n        fields: fields ?? void 0,\n        session: void 0,\n        dialect: self.getDialect(),\n        withList: queries\n      });\n    }\n    function selectDistinct(fields) {\n      return new PgSelectBuilder({\n        fields: fields ?? void 0,\n        session: void 0,\n        dialect: self.getDialect(),\n        distinct: true\n      });\n    }\n    function selectDistinctOn(on, fields) {\n      return new PgSelectBuilder({\n        fields: fields ?? void 0,\n        session: void 0,\n        dialect: self.getDialect(),\n        distinct: { on }\n      });\n    }\n    return { select, selectDistinct, selectDistinctOn };\n  }\n  select(fields) {\n    return new PgSelectBuilder({\n      fields: fields ?? void 0,\n      session: void 0,\n      dialect: this.getDialect()\n    });\n  }\n  selectDistinct(fields) {\n    return new PgSelectBuilder({\n      fields: fields ?? void 0,\n      session: void 0,\n      dialect: this.getDialect(),\n      distinct: true\n    });\n  }\n  selectDistinctOn(on, fields) {\n    return new PgSelectBuilder({\n      fields: fields ?? void 0,\n      session: void 0,\n      dialect: this.getDialect(),\n      distinct: { on }\n    });\n  }\n  // Lazy load dialect to avoid circular dependency\n  getDialect() {\n    if (!this.dialect) {\n      this.dialect = new PgDialect(this.dialectConfig);\n    }\n    return this.dialect;\n  }\n}\nexport {\n  QueryBuilder\n};\n//# sourceMappingURL=query-builder.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { PgTable } from \"../table.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { SQL } from \"../../sql/sql.js\";\nimport { Subquery } from \"../../subquery.js\";\nimport { getTableName, Table } from \"../../table.js\";\nimport {\n  getTableLikeName,\n  mapUpdateSet,\n  orderSelectedFields\n} from \"../../utils.js\";\nimport { ViewBaseConfig } from \"../../view-common.js\";\nimport { extractUsedTable } from \"../utils.js\";\nclass PgUpdateBuilder {\n  constructor(table, session, dialect, withList) {\n    this.table = table;\n    this.session = session;\n    this.dialect = dialect;\n    this.withList = withList;\n  }\n  static [entityKind] = \"PgUpdateBuilder\";\n  authToken;\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  set(values) {\n    return new PgUpdateBase(\n      this.table,\n      mapUpdateSet(this.table, values),\n      this.session,\n      this.dialect,\n      this.withList\n    ).setToken(this.authToken);\n  }\n}\nclass PgUpdateBase extends QueryPromise {\n  constructor(table, set, session, dialect, withList) {\n    super();\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { set, table, withList, joins: [] };\n    this.tableName = getTableLikeName(table);\n    this.joinsNotNullableMap = typeof this.tableName === \"string\" ? { [this.tableName]: true } : {};\n  }\n  static [entityKind] = \"PgUpdate\";\n  config;\n  tableName;\n  joinsNotNullableMap;\n  cacheConfig;\n  from(source) {\n    const src = source;\n    const tableName = getTableLikeName(src);\n    if (typeof tableName === \"string\") {\n      this.joinsNotNullableMap[tableName] = true;\n    }\n    this.config.from = src;\n    return this;\n  }\n  getTableLikeFields(table) {\n    if (is(table, PgTable)) {\n      return table[Table.Symbol.Columns];\n    } else if (is(table, Subquery)) {\n      return table._.selectedFields;\n    }\n    return table[ViewBaseConfig].selectedFields;\n  }\n  createJoin(joinType) {\n    return (table, on) => {\n      const tableName = getTableLikeName(table);\n      if (typeof tableName === \"string\" && this.config.joins.some((join) => join.alias === tableName)) {\n        throw new Error(`Alias \"${tableName}\" is already used in this query`);\n      }\n      if (typeof on === \"function\") {\n        const from = this.config.from && !is(this.config.from, SQL) ? this.getTableLikeFields(this.config.from) : void 0;\n        on = on(\n          new Proxy(\n            this.config.table[Table.Symbol.Columns],\n            new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n          ),\n          from && new Proxy(\n            from,\n            new SelectionProxyHandler({ sqlAliasedBehavior: \"sql\", sqlBehavior: \"sql\" })\n          )\n        );\n      }\n      this.config.joins.push({ on, table, joinType, alias: tableName });\n      if (typeof tableName === \"string\") {\n        switch (joinType) {\n          case \"left\": {\n            this.joinsNotNullableMap[tableName] = false;\n            break;\n          }\n          case \"right\": {\n            this.joinsNotNullableMap = Object.fromEntries(\n              Object.entries(this.joinsNotNullableMap).map(([key]) => [key, false])\n            );\n            this.joinsNotNullableMap[tableName] = true;\n            break;\n          }\n          case \"inner\": {\n            this.joinsNotNullableMap[tableName] = true;\n            break;\n          }\n          case \"full\": {\n            this.joinsNotNullableMap = Object.fromEntries(\n              Object.entries(this.joinsNotNullableMap).map(([key]) => [key, false])\n            );\n            this.joinsNotNullableMap[tableName] = false;\n            break;\n          }\n        }\n      }\n      return this;\n    };\n  }\n  leftJoin = this.createJoin(\"left\");\n  rightJoin = this.createJoin(\"right\");\n  innerJoin = this.createJoin(\"inner\");\n  fullJoin = this.createJoin(\"full\");\n  /**\n   * Adds a 'where' clause to the query.\n   *\n   * Calling this method will update only those rows that fulfill a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/update}\n   *\n   * @param where the 'where' clause.\n   *\n   * @example\n   * You can use conditional operators and `sql function` to filter the rows to be updated.\n   *\n   * ```ts\n   * // Update all cars with green color\n   * await db.update(cars).set({ color: 'red' })\n   *   .where(eq(cars.color, 'green'));\n   * // or\n   * await db.update(cars).set({ color: 'red' })\n   *   .where(sql`${cars.color} = 'green'`)\n   * ```\n   *\n   * You can logically combine conditional operators with `and()` and `or()` operators:\n   *\n   * ```ts\n   * // Update all BMW cars with a green color\n   * await db.update(cars).set({ color: 'red' })\n   *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n   *\n   * // Update all cars with the green or blue color\n   * await db.update(cars).set({ color: 'red' })\n   *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n   * ```\n   */\n  where(where) {\n    this.config.where = where;\n    return this;\n  }\n  returning(fields) {\n    if (!fields) {\n      fields = Object.assign({}, this.config.table[Table.Symbol.Columns]);\n      if (this.config.from) {\n        const tableName = getTableLikeName(this.config.from);\n        if (typeof tableName === \"string\" && this.config.from && !is(this.config.from, SQL)) {\n          const fromFields = this.getTableLikeFields(this.config.from);\n          fields[tableName] = fromFields;\n        }\n        for (const join of this.config.joins) {\n          const tableName2 = getTableLikeName(join.table);\n          if (typeof tableName2 === \"string\" && !is(join.table, SQL)) {\n            const fromFields = this.getTableLikeFields(join.table);\n            fields[tableName2] = fromFields;\n          }\n        }\n      }\n    }\n    this.config.returningFields = fields;\n    this.config.returning = orderSelectedFields(fields);\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildUpdateQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(name) {\n    const query = this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, void 0, {\n      type: \"insert\",\n      tables: extractUsedTable(this.config.table)\n    }, this.cacheConfig);\n    query.joinsNotNullableMap = this.joinsNotNullableMap;\n    return query;\n  }\n  prepare(name) {\n    return this._prepare(name);\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  execute = (placeholderValues) => {\n    return this._prepare().execute(placeholderValues, this.authToken);\n  };\n  /** @internal */\n  getSelectedFields() {\n    return this.config.returningFields ? new Proxy(\n      this.config.returningFields,\n      new SelectionProxyHandler({\n        alias: getTableName(this.config.table),\n        sqlAliasedBehavior: \"alias\",\n        sqlBehavior: \"error\"\n      })\n    ) : void 0;\n  }\n  $dynamic() {\n    return this;\n  }\n}\nexport {\n  PgUpdateBase,\n  PgUpdateBuilder\n};\n//# sourceMappingURL=update.js.map", "import { entityKind, is } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { Param, SQL, sql } from \"../../sql/sql.js\";\nimport { Columns, getTableName, Table } from \"../../table.js\";\nimport { tracer } from \"../../tracing.js\";\nimport { haveSameKeys, mapUpdateSet, orderSelectedFields } from \"../../utils.js\";\nimport { extractUsedTable } from \"../utils.js\";\nimport { QueryBuilder } from \"./query-builder.js\";\nclass PgInsertBuilder {\n  constructor(table, session, dialect, withList, overridingSystemValue_) {\n    this.table = table;\n    this.session = session;\n    this.dialect = dialect;\n    this.withList = withList;\n    this.overridingSystemValue_ = overridingSystemValue_;\n  }\n  static [entityKind] = \"PgInsertBuilder\";\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  overridingSystemValue() {\n    this.overridingSystemValue_ = true;\n    return this;\n  }\n  values(values) {\n    values = Array.isArray(values) ? values : [values];\n    if (values.length === 0) {\n      throw new Error(\"values() must be called with at least one value\");\n    }\n    const mappedValues = values.map((entry) => {\n      const result = {};\n      const cols = this.table[Table.Symbol.Columns];\n      for (const colKey of Object.keys(entry)) {\n        const colValue = entry[colKey];\n        result[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n      }\n      return result;\n    });\n    return new PgInsertBase(\n      this.table,\n      mappedValues,\n      this.session,\n      this.dialect,\n      this.withList,\n      false,\n      this.overridingSystemValue_\n    ).setToken(this.authToken);\n  }\n  select(selectQuery) {\n    const select = typeof selectQuery === \"function\" ? selectQuery(new QueryBuilder()) : selectQuery;\n    if (!is(select, SQL) && !haveSameKeys(this.table[Columns], select._.selectedFields)) {\n      throw new Error(\n        \"Insert select error: selected fields are not the same or are in a different order compared to the table definition\"\n      );\n    }\n    return new PgInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n  }\n}\nclass PgInsertBase extends QueryPromise {\n  constructor(table, values, session, dialect, withList, select, overridingSystemValue_) {\n    super();\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { table, values, withList, select, overridingSystemValue_ };\n  }\n  static [entityKind] = \"PgInsert\";\n  config;\n  cacheConfig;\n  returning(fields = this.config.table[Table.Symbol.Columns]) {\n    this.config.returningFields = fields;\n    this.config.returning = orderSelectedFields(fields);\n    return this;\n  }\n  /**\n   * Adds an `on conflict do nothing` clause to the query.\n   *\n   * Calling this method simply avoids inserting a row as its alternative action.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n   *\n   * @param config The `target` and `where` clauses.\n   *\n   * @example\n   * ```ts\n   * // Insert one row and cancel the insert if there's a conflict\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoNothing();\n   *\n   * // Explicitly specify conflict target\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoNothing({ target: cars.id });\n   * ```\n   */\n  onConflictDoNothing(config = {}) {\n    if (config.target === void 0) {\n      this.config.onConflict = sql`do nothing`;\n    } else {\n      let targetColumn = \"\";\n      targetColumn = Array.isArray(config.target) ? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(\",\") : this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n      const whereSql = config.where ? sql` where ${config.where}` : void 0;\n      this.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n    }\n    return this;\n  }\n  /**\n   * Adds an `on conflict do update` clause to the query.\n   *\n   * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n   *\n   * @param config The `target`, `set` and `where` clauses.\n   *\n   * @example\n   * ```ts\n   * // Update the row if there's a conflict\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoUpdate({\n   *     target: cars.id,\n   *     set: { brand: 'Porsche' }\n   *   });\n   *\n   * // Upsert with 'where' clause\n   * await db.insert(cars)\n   *   .values({ id: 1, brand: 'BMW' })\n   *   .onConflictDoUpdate({\n   *     target: cars.id,\n   *     set: { brand: 'newBMW' },\n   *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n   *   });\n   * ```\n   */\n  onConflictDoUpdate(config) {\n    if (config.where && (config.targetWhere || config.setWhere)) {\n      throw new Error(\n        'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.'\n      );\n    }\n    const whereSql = config.where ? sql` where ${config.where}` : void 0;\n    const targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : void 0;\n    const setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : void 0;\n    const setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n    let targetColumn = \"\";\n    targetColumn = Array.isArray(config.target) ? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(\",\") : this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n    this.config.onConflict = sql`(${sql.raw(targetColumn)})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildInsertQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(name) {\n    return tracer.startActiveSpan(\"drizzle.prepareQuery\", () => {\n      return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, void 0, {\n        type: \"insert\",\n        tables: extractUsedTable(this.config.table)\n      }, this.cacheConfig);\n    });\n  }\n  prepare(name) {\n    return this._prepare(name);\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  execute = (placeholderValues) => {\n    return tracer.startActiveSpan(\"drizzle.operation\", () => {\n      return this._prepare().execute(placeholderValues, this.authToken);\n    });\n  };\n  /** @internal */\n  getSelectedFields() {\n    return this.config.returningFields ? new Proxy(\n      this.config.returningFields,\n      new SelectionProxyHandler({\n        alias: getTableName(this.config.table),\n        sqlAliasedBehavior: \"alias\",\n        sqlBehavior: \"error\"\n      })\n    ) : void 0;\n  }\n  $dynamic() {\n    return this;\n  }\n}\nexport {\n  PgInsertBase,\n  PgInsertBuilder\n};\n//# sourceMappingURL=insert.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { SelectionProxyHandler } from \"../../selection-proxy.js\";\nimport { getTableName, Table } from \"../../table.js\";\nimport { tracer } from \"../../tracing.js\";\nimport { orderSelectedFields } from \"../../utils.js\";\nimport { extractUsedTable } from \"../utils.js\";\nclass PgDeleteBase extends QueryPromise {\n  constructor(table, session, dialect, withList) {\n    super();\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { table, withList };\n  }\n  static [entityKind] = \"PgDelete\";\n  config;\n  cacheConfig;\n  /**\n   * Adds a `where` clause to the query.\n   *\n   * Calling this method will delete only those rows that fulfill a specified condition.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/delete}\n   *\n   * @param where the `where` clause.\n   *\n   * @example\n   * You can use conditional operators and `sql function` to filter the rows to be deleted.\n   *\n   * ```ts\n   * // Delete all cars with green color\n   * await db.delete(cars).where(eq(cars.color, 'green'));\n   * // or\n   * await db.delete(cars).where(sql`${cars.color} = 'green'`)\n   * ```\n   *\n   * You can logically combine conditional operators with `and()` and `or()` operators:\n   *\n   * ```ts\n   * // Delete all BMW cars with a green color\n   * await db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n   *\n   * // Delete all cars with the green or blue color\n   * await db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n   * ```\n   */\n  where(where) {\n    this.config.where = where;\n    return this;\n  }\n  returning(fields = this.config.table[Table.Symbol.Columns]) {\n    this.config.returningFields = fields;\n    this.config.returning = orderSelectedFields(fields);\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildDeleteQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(name) {\n    return tracer.startActiveSpan(\"drizzle.prepareQuery\", () => {\n      return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, void 0, {\n        type: \"delete\",\n        tables: extractUsedTable(this.config.table)\n      }, this.cacheConfig);\n    });\n  }\n  prepare(name) {\n    return this._prepare(name);\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  execute = (placeholderValues) => {\n    return tracer.startActiveSpan(\"drizzle.operation\", () => {\n      return this._prepare().execute(placeholderValues, this.authToken);\n    });\n  };\n  /** @internal */\n  getSelectedFields() {\n    return this.config.returningFields ? new Proxy(\n      this.config.returningFields,\n      new SelectionProxyHandler({\n        alias: getTableName(this.config.table),\n        sqlAliasedBehavior: \"alias\",\n        sqlBehavior: \"error\"\n      })\n    ) : void 0;\n  }\n  $dynamic() {\n    return this;\n  }\n}\nexport {\n  PgDeleteBase\n};\n//# sourceMappingURL=delete.js.map", "import { entityKind } from \"../../entity.js\";\nimport { SQL, sql } from \"../../sql/sql.js\";\nclass PgCountBuilder extends SQL {\n  constructor(params) {\n    super(PgCountBuilder.buildEmbeddedCount(params.source, params.filters).queryChunks);\n    this.params = params;\n    this.mapWith(Number);\n    this.session = params.session;\n    this.sql = PgCountBuilder.buildCount(\n      params.source,\n      params.filters\n    );\n  }\n  sql;\n  token;\n  static [entityKind] = \"PgCountBuilder\";\n  [Symbol.toStringTag] = \"PgCountBuilder\";\n  session;\n  static buildEmbeddedCount(source, filters) {\n    return sql`(select count(*) from ${source}${sql.raw(\" where \").if(filters)}${filters})`;\n  }\n  static buildCount(source, filters) {\n    return sql`select count(*) as count from ${source}${sql.raw(\" where \").if(filters)}${filters};`;\n  }\n  /** @intrnal */\n  setToken(token) {\n    this.token = token;\n    return this;\n  }\n  then(onfulfilled, onrejected) {\n    return Promise.resolve(this.session.count(this.sql, this.token)).then(\n      onfulfilled,\n      onrejected\n    );\n  }\n  catch(onRejected) {\n    return this.then(void 0, onRejected);\n  }\n  finally(onFinally) {\n    return this.then(\n      (value) => {\n        onFinally?.();\n        return value;\n      },\n      (reason) => {\n        onFinally?.();\n        throw reason;\n      }\n    );\n  }\n}\nexport {\n  PgCountBuilder\n};\n//# sourceMappingURL=count.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport {\n  mapRelationalRow\n} from \"../../relations.js\";\nimport { tracer } from \"../../tracing.js\";\nclass RelationalQueryBuilder {\n  constructor(fullSchema, schema, tableNamesMap, table, tableConfig, dialect, session) {\n    this.fullSchema = fullSchema;\n    this.schema = schema;\n    this.tableNamesMap = tableNamesMap;\n    this.table = table;\n    this.tableConfig = tableConfig;\n    this.dialect = dialect;\n    this.session = session;\n  }\n  static [entityKind] = \"PgRelationalQueryBuilder\";\n  findMany(config) {\n    return new PgRelationalQuery(\n      this.fullSchema,\n      this.schema,\n      this.tableNamesMap,\n      this.table,\n      this.tableConfig,\n      this.dialect,\n      this.session,\n      config ? config : {},\n      \"many\"\n    );\n  }\n  findFirst(config) {\n    return new PgRelationalQuery(\n      this.fullSchema,\n      this.schema,\n      this.tableNamesMap,\n      this.table,\n      this.tableConfig,\n      this.dialect,\n      this.session,\n      config ? { ...config, limit: 1 } : { limit: 1 },\n      \"first\"\n    );\n  }\n}\nclass PgRelationalQuery extends QueryPromise {\n  constructor(fullSchema, schema, tableNamesMap, table, tableConfig, dialect, session, config, mode) {\n    super();\n    this.fullSchema = fullSchema;\n    this.schema = schema;\n    this.tableNamesMap = tableNamesMap;\n    this.table = table;\n    this.tableConfig = tableConfig;\n    this.dialect = dialect;\n    this.session = session;\n    this.config = config;\n    this.mode = mode;\n  }\n  static [entityKind] = \"PgRelationalQuery\";\n  /** @internal */\n  _prepare(name) {\n    return tracer.startActiveSpan(\"drizzle.prepareQuery\", () => {\n      const { query, builtQuery } = this._toSQL();\n      return this.session.prepareQuery(\n        builtQuery,\n        void 0,\n        name,\n        true,\n        (rawRows, mapColumnValue) => {\n          const rows = rawRows.map(\n            (row) => mapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n          );\n          if (this.mode === \"first\") {\n            return rows[0];\n          }\n          return rows;\n        }\n      );\n    });\n  }\n  prepare(name) {\n    return this._prepare(name);\n  }\n  _getQuery() {\n    return this.dialect.buildRelationalQueryWithoutPK({\n      fullSchema: this.fullSchema,\n      schema: this.schema,\n      tableNamesMap: this.tableNamesMap,\n      table: this.table,\n      tableConfig: this.tableConfig,\n      queryConfig: this.config,\n      tableAlias: this.tableConfig.tsName\n    });\n  }\n  /** @internal */\n  getSQL() {\n    return this._getQuery().sql;\n  }\n  _toSQL() {\n    const query = this._getQuery();\n    const builtQuery = this.dialect.sqlToQuery(query.sql);\n    return { query, builtQuery };\n  }\n  toSQL() {\n    return this._toSQL().builtQuery;\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  execute() {\n    return tracer.startActiveSpan(\"drizzle.operation\", () => {\n      return this._prepare().execute(void 0, this.authToken);\n    });\n  }\n}\nexport {\n  PgRelationalQuery,\n  RelationalQueryBuilder\n};\n//# sourceMappingURL=query.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nclass PgRaw extends QueryPromise {\n  constructor(execute, sql, query, mapBatchResult) {\n    super();\n    this.execute = execute;\n    this.sql = sql;\n    this.query = query;\n    this.mapBatchResult = mapBatchResult;\n  }\n  static [entityKind] = \"PgRaw\";\n  /** @internal */\n  getSQL() {\n    return this.sql;\n  }\n  getQuery() {\n    return this.query;\n  }\n  mapResult(result, isFromBatch) {\n    return isFromBatch ? this.mapBatchResult(result) : result;\n  }\n  _prepare() {\n    return this;\n  }\n  /** @internal */\n  isResponseInArrayMode() {\n    return false;\n  }\n}\nexport {\n  PgRaw\n};\n//# sourceMappingURL=raw.js.map", "import { entityKind } from \"../../entity.js\";\nimport { QueryPromise } from \"../../query-promise.js\";\nimport { tracer } from \"../../tracing.js\";\nclass PgRefreshMaterializedView extends QueryPromise {\n  constructor(view, session, dialect) {\n    super();\n    this.session = session;\n    this.dialect = dialect;\n    this.config = { view };\n  }\n  static [entityKind] = \"PgRefreshMaterializedView\";\n  config;\n  concurrently() {\n    if (this.config.withNoData !== void 0) {\n      throw new Error(\"Cannot use concurrently and withNoData together\");\n    }\n    this.config.concurrently = true;\n    return this;\n  }\n  withNoData() {\n    if (this.config.concurrently !== void 0) {\n      throw new Error(\"Cannot use concurrently and withNoData together\");\n    }\n    this.config.withNoData = true;\n    return this;\n  }\n  /** @internal */\n  getSQL() {\n    return this.dialect.buildRefreshMaterializedViewQuery(this.config);\n  }\n  toSQL() {\n    const { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n    return rest;\n  }\n  /** @internal */\n  _prepare(name) {\n    return tracer.startActiveSpan(\"drizzle.prepareQuery\", () => {\n      return this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), void 0, name, true);\n    });\n  }\n  prepare(name) {\n    return this._prepare(name);\n  }\n  authToken;\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  execute = (placeholderValues) => {\n    return tracer.startActiveSpan(\"drizzle.operation\", () => {\n      return this._prepare().execute(placeholderValues, this.authToken);\n    });\n  };\n}\nexport {\n  PgRefreshMaterializedView\n};\n//# sourceMappingURL=refresh-materialized-view.js.map", "import { entityKind } from \"../entity.js\";\nimport {\n  PgDeleteBase,\n  PgInsertBuilder,\n  PgSelectBuilder,\n  PgUpdateBuilder,\n  QueryBuilder\n} from \"./query-builders/index.js\";\nimport { SelectionProxyHandler } from \"../selection-proxy.js\";\nimport { sql } from \"../sql/sql.js\";\nimport { WithSubquery } from \"../subquery.js\";\nimport { PgCountBuilder } from \"./query-builders/count.js\";\nimport { RelationalQueryBuilder } from \"./query-builders/query.js\";\nimport { PgRaw } from \"./query-builders/raw.js\";\nimport { PgRefreshMaterializedView } from \"./query-builders/refresh-materialized-view.js\";\nclass PgDatabase {\n  constructor(dialect, session, schema) {\n    this.dialect = dialect;\n    this.session = session;\n    this._ = schema ? {\n      schema: schema.schema,\n      fullSchema: schema.fullSchema,\n      tableNamesMap: schema.tableNamesMap,\n      session\n    } : {\n      schema: void 0,\n      fullSchema: {},\n      tableNamesMap: {},\n      session\n    };\n    this.query = {};\n    if (this._.schema) {\n      for (const [tableName, columns] of Object.entries(this._.schema)) {\n        this.query[tableName] = new RelationalQueryBuilder(\n          schema.fullSchema,\n          this._.schema,\n          this._.tableNamesMap,\n          schema.fullSchema[tableName],\n          columns,\n          dialect,\n          session\n        );\n      }\n    }\n    this.$cache = { invalidate: async (_params) => {\n    } };\n  }\n  static [entityKind] = \"PgDatabase\";\n  query;\n  /**\n   * Creates a subquery that defines a temporary named result set as a CTE.\n   *\n   * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n   *\n   * @param alias The alias for the subquery.\n   *\n   * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n   *\n   * @example\n   *\n   * ```ts\n   * // Create a subquery with alias 'sq' and use it in the select query\n   * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n   *\n   * const result = await db.with(sq).select().from(sq);\n   * ```\n   *\n   * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n   *\n   * ```ts\n   * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n   * const sq = db.$with('sq').as(db.select({\n   *   name: sql<string>`upper(${users.name})`.as('name'),\n   * })\n   * .from(users));\n   *\n   * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n   * ```\n   */\n  $with = (alias, selection) => {\n    const self = this;\n    const as = (qb) => {\n      if (typeof qb === \"function\") {\n        qb = qb(new QueryBuilder(self.dialect));\n      }\n      return new Proxy(\n        new WithSubquery(\n          qb.getSQL(),\n          selection ?? (\"getSelectedFields\" in qb ? qb.getSelectedFields() ?? {} : {}),\n          alias,\n          true\n        ),\n        new SelectionProxyHandler({ alias, sqlAliasedBehavior: \"alias\", sqlBehavior: \"error\" })\n      );\n    };\n    return { as };\n  };\n  $count(source, filters) {\n    return new PgCountBuilder({ source, filters, session: this.session });\n  }\n  $cache;\n  /**\n   * Incorporates a previously defined CTE (using `$with`) into the main query.\n   *\n   * This method allows the main query to reference a temporary named result set.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n   *\n   * @param queries The CTEs to incorporate into the main query.\n   *\n   * @example\n   *\n   * ```ts\n   * // Define a subquery 'sq' as a CTE using $with\n   * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n   *\n   * // Incorporate the CTE 'sq' into the main query and select from it\n   * const result = await db.with(sq).select().from(sq);\n   * ```\n   */\n  with(...queries) {\n    const self = this;\n    function select(fields) {\n      return new PgSelectBuilder({\n        fields: fields ?? void 0,\n        session: self.session,\n        dialect: self.dialect,\n        withList: queries\n      });\n    }\n    function selectDistinct(fields) {\n      return new PgSelectBuilder({\n        fields: fields ?? void 0,\n        session: self.session,\n        dialect: self.dialect,\n        withList: queries,\n        distinct: true\n      });\n    }\n    function selectDistinctOn(on, fields) {\n      return new PgSelectBuilder({\n        fields: fields ?? void 0,\n        session: self.session,\n        dialect: self.dialect,\n        withList: queries,\n        distinct: { on }\n      });\n    }\n    function update(table) {\n      return new PgUpdateBuilder(table, self.session, self.dialect, queries);\n    }\n    function insert(table) {\n      return new PgInsertBuilder(table, self.session, self.dialect, queries);\n    }\n    function delete_(table) {\n      return new PgDeleteBase(table, self.session, self.dialect, queries);\n    }\n    return { select, selectDistinct, selectDistinctOn, update, insert, delete: delete_ };\n  }\n  select(fields) {\n    return new PgSelectBuilder({\n      fields: fields ?? void 0,\n      session: this.session,\n      dialect: this.dialect\n    });\n  }\n  selectDistinct(fields) {\n    return new PgSelectBuilder({\n      fields: fields ?? void 0,\n      session: this.session,\n      dialect: this.dialect,\n      distinct: true\n    });\n  }\n  selectDistinctOn(on, fields) {\n    return new PgSelectBuilder({\n      fields: fields ?? void 0,\n      session: this.session,\n      dialect: this.dialect,\n      distinct: { on }\n    });\n  }\n  /**\n   * Creates an update query.\n   *\n   * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n   *\n   * Use `.set()` method to specify which values to update.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/update}\n   *\n   * @param table The table to update.\n   *\n   * @example\n   *\n   * ```ts\n   * // Update all rows in the 'cars' table\n   * await db.update(cars).set({ color: 'red' });\n   *\n   * // Update rows with filters and conditions\n   * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n   *\n   * // Update with returning clause\n   * const updatedCar: Car[] = await db.update(cars)\n   *   .set({ color: 'red' })\n   *   .where(eq(cars.id, 1))\n   *   .returning();\n   * ```\n   */\n  update(table) {\n    return new PgUpdateBuilder(table, this.session, this.dialect);\n  }\n  /**\n   * Creates an insert query.\n   *\n   * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/insert}\n   *\n   * @param table The table to insert into.\n   *\n   * @example\n   *\n   * ```ts\n   * // Insert one row\n   * await db.insert(cars).values({ brand: 'BMW' });\n   *\n   * // Insert multiple rows\n   * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n   *\n   * // Insert with returning clause\n   * const insertedCar: Car[] = await db.insert(cars)\n   *   .values({ brand: 'BMW' })\n   *   .returning();\n   * ```\n   */\n  insert(table) {\n    return new PgInsertBuilder(table, this.session, this.dialect);\n  }\n  /**\n   * Creates a delete query.\n   *\n   * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n   *\n   * See docs: {@link https://orm.drizzle.team/docs/delete}\n   *\n   * @param table The table to delete from.\n   *\n   * @example\n   *\n   * ```ts\n   * // Delete all rows in the 'cars' table\n   * await db.delete(cars);\n   *\n   * // Delete rows with filters and conditions\n   * await db.delete(cars).where(eq(cars.color, 'green'));\n   *\n   * // Delete with returning clause\n   * const deletedCar: Car[] = await db.delete(cars)\n   *   .where(eq(cars.id, 1))\n   *   .returning();\n   * ```\n   */\n  delete(table) {\n    return new PgDeleteBase(table, this.session, this.dialect);\n  }\n  refreshMaterializedView(view) {\n    return new PgRefreshMaterializedView(view, this.session, this.dialect);\n  }\n  authToken;\n  execute(query) {\n    const sequel = typeof query === \"string\" ? sql.raw(query) : query.getSQL();\n    const builtQuery = this.dialect.sqlToQuery(sequel);\n    const prepared = this.session.prepareQuery(\n      builtQuery,\n      void 0,\n      void 0,\n      false\n    );\n    return new PgRaw(\n      () => prepared.execute(void 0, this.authToken),\n      sequel,\n      builtQuery,\n      (result) => prepared.mapResult(result, true)\n    );\n  }\n  transaction(transaction, config) {\n    return this.session.transaction(transaction, config);\n  }\n}\nconst withReplicas = (primary, replicas, getReplica = () => replicas[Math.floor(Math.random() * replicas.length)]) => {\n  const select = (...args) => getReplica(replicas).select(...args);\n  const selectDistinct = (...args) => getReplica(replicas).selectDistinct(...args);\n  const selectDistinctOn = (...args) => getReplica(replicas).selectDistinctOn(...args);\n  const $count = (...args) => getReplica(replicas).$count(...args);\n  const _with = (...args) => getReplica(replicas).with(...args);\n  const $with = (arg) => getReplica(replicas).$with(arg);\n  const update = (...args) => primary.update(...args);\n  const insert = (...args) => primary.insert(...args);\n  const $delete = (...args) => primary.delete(...args);\n  const execute = (...args) => primary.execute(...args);\n  const transaction = (...args) => primary.transaction(...args);\n  const refreshMaterializedView = (...args) => primary.refreshMaterializedView(...args);\n  return {\n    ...primary,\n    update,\n    insert,\n    delete: $delete,\n    execute,\n    transaction,\n    refreshMaterializedView,\n    $primary: primary,\n    select,\n    selectDistinct,\n    selectDistinctOn,\n    $count,\n    $with,\n    with: _with,\n    get query() {\n      return getReplica(replicas).query;\n    }\n  };\n};\nexport {\n  PgDatabase,\n  withReplicas\n};\n//# sourceMappingURL=db.js.map", "import { entityKind } from \"../../entity.js\";\nclass Cache {\n  static [entityKind] = \"Cache\";\n}\nclass NoopCache extends Cache {\n  strategy() {\n    return \"all\";\n  }\n  static [entityKind] = \"NoopCache\";\n  async get(_key) {\n    return void 0;\n  }\n  async put(_hashedQuery, _response, _tables, _config) {\n  }\n  async onMutate(_params) {\n  }\n}\nasync function hashQuery(sql, params) {\n  const dataToHash = `${sql}-${JSON.stringify(params)}`;\n  const encoder = new TextEncoder();\n  const data = encoder.encode(dataToHash);\n  const hashBuffer = await crypto.subtle.digest(\"SHA-256\", data);\n  const hashArray = [...new Uint8Array(hashBuffer)];\n  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, \"0\")).join(\"\");\n  return hashHex;\n}\nexport {\n  Cache,\n  NoopCache,\n  hashQuery\n};\n//# sourceMappingURL=cache.js.map", "import { hashQuery, NoopCache } from \"../cache/core/cache.js\";\nimport { entityKind, is } from \"../entity.js\";\nimport { DrizzleQueryError, TransactionRollbackError } from \"../errors.js\";\nimport { sql } from \"../sql/index.js\";\nimport { tracer } from \"../tracing.js\";\nimport { PgDatabase } from \"./db.js\";\nclass PgPreparedQuery {\n  constructor(query, cache, queryMetadata, cacheConfig) {\n    this.query = query;\n    this.cache = cache;\n    this.queryMetadata = queryMetadata;\n    this.cacheConfig = cacheConfig;\n    if (cache && cache.strategy() === \"all\" && cacheConfig === void 0) {\n      this.cacheConfig = { enable: true, autoInvalidate: true };\n    }\n    if (!this.cacheConfig?.enable) {\n      this.cacheConfig = void 0;\n    }\n  }\n  authToken;\n  getQuery() {\n    return this.query;\n  }\n  mapResult(response, _isFromBatch) {\n    return response;\n  }\n  /** @internal */\n  setToken(token) {\n    this.authToken = token;\n    return this;\n  }\n  static [entityKind] = \"PgPreparedQuery\";\n  /** @internal */\n  joinsNotNullableMap;\n  /** @internal */\n  async queryWithCache(queryString, params, query) {\n    if (this.cache === void 0 || is(this.cache, NoopCache) || this.queryMetadata === void 0) {\n      try {\n        return await query();\n      } catch (e) {\n        throw new DrizzleQueryError(queryString, params, e);\n      }\n    }\n    if (this.cacheConfig && !this.cacheConfig.enable) {\n      try {\n        return await query();\n      } catch (e) {\n        throw new DrizzleQueryError(queryString, params, e);\n      }\n    }\n    if ((this.queryMetadata.type === \"insert\" || this.queryMetadata.type === \"update\" || this.queryMetadata.type === \"delete\") && this.queryMetadata.tables.length > 0) {\n      try {\n        const [res] = await Promise.all([\n          query(),\n          this.cache.onMutate({ tables: this.queryMetadata.tables })\n        ]);\n        return res;\n      } catch (e) {\n        throw new DrizzleQueryError(queryString, params, e);\n      }\n    }\n    if (!this.cacheConfig) {\n      try {\n        return await query();\n      } catch (e) {\n        throw new DrizzleQueryError(queryString, params, e);\n      }\n    }\n    if (this.queryMetadata.type === \"select\") {\n      const fromCache = await this.cache.get(\n        this.cacheConfig.tag ?? (await hashQuery(queryString, params)),\n        this.queryMetadata.tables,\n        this.cacheConfig.tag !== void 0,\n        this.cacheConfig.autoInvalidate\n      );\n      if (fromCache === void 0) {\n        let result;\n        try {\n          result = await query();\n        } catch (e) {\n          throw new DrizzleQueryError(queryString, params, e);\n        }\n        await this.cache.put(\n          this.cacheConfig.tag ?? (await hashQuery(queryString, params)),\n          result,\n          // make sure we send tables that were used in a query only if user wants to invalidate it on each write\n          this.cacheConfig.autoInvalidate ? this.queryMetadata.tables : [],\n          this.cacheConfig.tag !== void 0,\n          this.cacheConfig.config\n        );\n        return result;\n      }\n      return fromCache;\n    }\n    try {\n      return await query();\n    } catch (e) {\n      throw new DrizzleQueryError(queryString, params, e);\n    }\n  }\n}\nclass PgSession {\n  constructor(dialect) {\n    this.dialect = dialect;\n  }\n  static [entityKind] = \"PgSession\";\n  /** @internal */\n  execute(query, token) {\n    return tracer.startActiveSpan(\"drizzle.operation\", () => {\n      const prepared = tracer.startActiveSpan(\"drizzle.prepareQuery\", () => {\n        return this.prepareQuery(\n          this.dialect.sqlToQuery(query),\n          void 0,\n          void 0,\n          false\n        );\n      });\n      return prepared.setToken(token).execute(void 0, token);\n    });\n  }\n  all(query) {\n    return this.prepareQuery(\n      this.dialect.sqlToQuery(query),\n      void 0,\n      void 0,\n      false\n    ).all();\n  }\n  /** @internal */\n  async count(sql2, token) {\n    const res = await this.execute(sql2, token);\n    return Number(\n      res[0][\"count\"]\n    );\n  }\n}\nclass PgTransaction extends PgDatabase {\n  constructor(dialect, session, schema, nestedIndex = 0) {\n    super(dialect, session, schema);\n    this.schema = schema;\n    this.nestedIndex = nestedIndex;\n  }\n  static [entityKind] = \"PgTransaction\";\n  rollback() {\n    throw new TransactionRollbackError();\n  }\n  /** @internal */\n  getTransactionConfigSQL(config) {\n    const chunks = [];\n    if (config.isolationLevel) {\n      chunks.push(`isolation level ${config.isolationLevel}`);\n    }\n    if (config.accessMode) {\n      chunks.push(config.accessMode);\n    }\n    if (typeof config.deferrable === \"boolean\") {\n      chunks.push(config.deferrable ? \"deferrable\" : \"not deferrable\");\n    }\n    return sql.raw(chunks.join(\" \"));\n  }\n  setTransaction(config) {\n    return this.session.execute(sql`set transaction ${this.getTransactionConfigSQL(config)}`);\n  }\n}\nexport {\n  PgPreparedQuery,\n  PgSession,\n  PgTransaction\n};\n//# sourceMappingURL=session.js.map", "import { NoopCache } from \"../cache/core/index.js\";\nimport { entityKind } from \"../entity.js\";\nimport { NoopLogger } from \"../logger.js\";\nimport { PgTransaction } from \"../pg-core/index.js\";\nimport { PgPreparedQuery, PgSession } from \"../pg-core/session.js\";\nimport { fillPlaceholders } from \"../sql/sql.js\";\nimport { mapResultRow } from \"../utils.js\";\nconst rawQueryConfig = {\n  arrayMode: false,\n  fullResults: true\n};\nconst queryConfig = {\n  arrayMode: true,\n  fullResults: true\n};\nclass NeonHttpPreparedQuery extends PgPreparedQuery {\n  constructor(client, query, logger, cache, queryMetadata, cacheConfig, fields, _isResponseInArrayMode, customResultMapper) {\n    super(query, cache, queryMetadata, cacheConfig);\n    this.client = client;\n    this.logger = logger;\n    this.fields = fields;\n    this._isResponseInArrayMode = _isResponseInArrayMode;\n    this.customResultMapper = customResultMapper;\n    this.clientQuery = client.query ?? client;\n  }\n  static [entityKind] = \"NeonHttpPreparedQuery\";\n  clientQuery;\n  /** @internal */\n  async execute(placeholderValues = {}, token = this.authToken) {\n    const params = fillPlaceholders(this.query.params, placeholderValues);\n    this.logger.logQuery(this.query.sql, params);\n    const { fields, clientQuery, query, customResultMapper } = this;\n    if (!fields && !customResultMapper) {\n      return this.queryWithCache(query.sql, params, async () => {\n        return clientQuery(\n          query.sql,\n          params,\n          token === void 0 ? rawQueryConfig : {\n            ...rawQueryConfig,\n            authToken: token\n          }\n        );\n      });\n    }\n    const result = await this.queryWithCache(query.sql, params, async () => {\n      return await clientQuery(\n        query.sql,\n        params,\n        token === void 0 ? queryConfig : {\n          ...queryConfig,\n          authToken: token\n        }\n      );\n    });\n    return this.mapResult(result);\n  }\n  mapResult(result) {\n    if (!this.fields && !this.customResultMapper) {\n      return result;\n    }\n    const rows = result.rows;\n    if (this.customResultMapper) {\n      return this.customResultMapper(rows);\n    }\n    return rows.map((row) => mapResultRow(this.fields, row, this.joinsNotNullableMap));\n  }\n  all(placeholderValues = {}) {\n    const params = fillPlaceholders(this.query.params, placeholderValues);\n    this.logger.logQuery(this.query.sql, params);\n    return this.clientQuery(\n      this.query.sql,\n      params,\n      this.authToken === void 0 ? rawQueryConfig : {\n        ...rawQueryConfig,\n        authToken: this.authToken\n      }\n    ).then((result) => result.rows);\n  }\n  /** @internal */\n  values(placeholderValues = {}, token) {\n    const params = fillPlaceholders(this.query.params, placeholderValues);\n    this.logger.logQuery(this.query.sql, params);\n    return this.clientQuery(this.query.sql, params, { arrayMode: true, fullResults: true, authToken: token }).then((result) => result.rows);\n  }\n  /** @internal */\n  isResponseInArrayMode() {\n    return this._isResponseInArrayMode;\n  }\n}\nclass NeonHttpSession extends PgSession {\n  constructor(client, dialect, schema, options = {}) {\n    super(dialect);\n    this.client = client;\n    this.schema = schema;\n    this.options = options;\n    this.clientQuery = client.query ?? client;\n    this.logger = options.logger ?? new NoopLogger();\n    this.cache = options.cache ?? new NoopCache();\n  }\n  static [entityKind] = \"NeonHttpSession\";\n  clientQuery;\n  logger;\n  cache;\n  prepareQuery(query, fields, name, isResponseInArrayMode, customResultMapper, queryMetadata, cacheConfig) {\n    return new NeonHttpPreparedQuery(\n      this.client,\n      query,\n      this.logger,\n      this.cache,\n      queryMetadata,\n      cacheConfig,\n      fields,\n      isResponseInArrayMode,\n      customResultMapper\n    );\n  }\n  async batch(queries) {\n    const preparedQueries = [];\n    const builtQueries = [];\n    for (const query of queries) {\n      const preparedQuery = query._prepare();\n      const builtQuery = preparedQuery.getQuery();\n      preparedQueries.push(preparedQuery);\n      builtQueries.push(\n        this.clientQuery(builtQuery.sql, builtQuery.params, {\n          fullResults: true,\n          arrayMode: preparedQuery.isResponseInArrayMode()\n        })\n      );\n    }\n    const batchResults = await this.client.transaction(builtQueries, queryConfig);\n    return batchResults.map((result, i) => preparedQueries[i].mapResult(result, true));\n  }\n  // change return type to QueryRows<true>\n  async query(query, params) {\n    this.logger.logQuery(query, params);\n    const result = await this.clientQuery(query, params, { arrayMode: true, fullResults: true });\n    return result;\n  }\n  // change return type to QueryRows<false>\n  async queryObjects(query, params) {\n    return this.clientQuery(query, params, { arrayMode: false, fullResults: true });\n  }\n  /** @internal */\n  async count(sql, token) {\n    const res = await this.execute(sql, token);\n    return Number(\n      res[\"rows\"][0][\"count\"]\n    );\n  }\n  async transaction(_transaction, _config = {}) {\n    throw new Error(\"No transactions support in neon-http driver\");\n  }\n}\nclass NeonTransaction extends PgTransaction {\n  static [entityKind] = \"NeonHttpTransaction\";\n  async transaction(_transaction) {\n    throw new Error(\"No transactions support in neon-http driver\");\n  }\n}\nexport {\n  NeonHttpPreparedQuery,\n  NeonHttpSession,\n  NeonTransaction\n};\n//# sourceMappingURL=session.js.map", "import { neon, types } from \"@neondatabase/serverless\";\nimport { entityKind } from \"../entity.js\";\nimport { DefaultLogger } from \"../logger.js\";\nimport { PgDatabase } from \"../pg-core/db.js\";\nimport { PgDialect } from \"../pg-core/dialect.js\";\nimport { createTableRelationsHelpers, extractTablesRelationalConfig } from \"../relations.js\";\nimport { isConfig } from \"../utils.js\";\nimport { NeonHttpSession } from \"./session.js\";\nclass NeonHttpDriver {\n  constructor(client, dialect, options = {}) {\n    this.client = client;\n    this.dialect = dialect;\n    this.options = options;\n    this.initMappers();\n  }\n  static [entityKind] = \"NeonHttpDriver\";\n  createSession(schema) {\n    return new NeonHttpSession(this.client, this.dialect, schema, {\n      logger: this.options.logger,\n      cache: this.options.cache\n    });\n  }\n  initMappers() {\n    types.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => val);\n    types.setTypeParser(types.builtins.TIMESTAMP, (val) => val);\n    types.setTypeParser(types.builtins.DATE, (val) => val);\n    types.setTypeParser(types.builtins.INTERVAL, (val) => val);\n    types.setTypeParser(1231, (val) => val);\n    types.setTypeParser(1115, (val) => val);\n    types.setTypeParser(1185, (val) => val);\n    types.setTypeParser(1187, (val) => val);\n    types.setTypeParser(1182, (val) => val);\n  }\n}\nfunction wrap(target, token, cb, deep) {\n  return new Proxy(target, {\n    get(target2, p) {\n      const element = target2[p];\n      if (typeof element !== \"function\" && (typeof element !== \"object\" || element === null)) return element;\n      if (deep) return wrap(element, token, cb);\n      if (p === \"query\") return wrap(element, token, cb, true);\n      return new Proxy(element, {\n        apply(target3, thisArg, argArray) {\n          const res = target3.call(thisArg, ...argArray);\n          if (typeof res === \"object\" && res !== null && \"setToken\" in res && typeof res.setToken === \"function\") {\n            res.setToken(token);\n          }\n          return cb(target3, p, res);\n        }\n      });\n    }\n  });\n}\nclass NeonHttpDatabase extends PgDatabase {\n  static [entityKind] = \"NeonHttpDatabase\";\n  $withAuth(token) {\n    this.authToken = token;\n    return wrap(this, token, (target, p, res) => {\n      if (p === \"with\") {\n        return wrap(res, token, (_, __, res2) => res2);\n      }\n      return res;\n    });\n  }\n  async batch(batch) {\n    return this.session.batch(batch);\n  }\n}\nfunction construct(client, config = {}) {\n  const dialect = new PgDialect({ casing: config.casing });\n  let logger;\n  if (config.logger === true) {\n    logger = new DefaultLogger();\n  } else if (config.logger !== false) {\n    logger = config.logger;\n  }\n  let schema;\n  if (config.schema) {\n    const tablesConfig = extractTablesRelationalConfig(\n      config.schema,\n      createTableRelationsHelpers\n    );\n    schema = {\n      fullSchema: config.schema,\n      schema: tablesConfig.tables,\n      tableNamesMap: tablesConfig.tableNamesMap\n    };\n  }\n  const driver = new NeonHttpDriver(client, dialect, { logger, cache: config.cache });\n  const session = driver.createSession(schema);\n  const db = new NeonHttpDatabase(\n    dialect,\n    session,\n    schema\n  );\n  db.$client = client;\n  db.$cache = config.cache;\n  if (db.$cache) {\n    db.$cache[\"invalidate\"] = config.cache?.onMutate;\n  }\n  return db;\n}\nfunction drizzle(...params) {\n  if (typeof params[0] === \"string\") {\n    const instance = neon(params[0]);\n    return construct(instance, params[1]);\n  }\n  if (isConfig(params[0])) {\n    const { connection, client, ...drizzleConfig } = params[0];\n    if (client) return construct(client, drizzleConfig);\n    if (typeof connection === \"object\") {\n      const { connectionString, ...options } = connection;\n      const instance2 = neon(connectionString, options);\n      return construct(instance2, drizzleConfig);\n    }\n    const instance = neon(connection);\n    return construct(instance, drizzleConfig);\n  }\n  return construct(params[0], params[1]);\n}\n((drizzle2) => {\n  function mock(config) {\n    return construct({}, config);\n  }\n  drizzle2.mock = mock;\n})(drizzle || (drizzle = {}));\nexport {\n  NeonHttpDatabase,\n  NeonHttpDriver,\n  drizzle\n};\n//# sourceMappingURL=driver.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgIntColumnBaseBuilder } from \"./int.common.js\";\nclass PgBigInt53Builder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgBigInt53Builder\";\n  constructor(name) {\n    super(name, \"number\", \"PgBigInt53\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigInt53(table, this.config);\n  }\n}\nclass PgBigInt53 extends PgColumn {\n  static [entityKind] = \"PgBigInt53\";\n  getSQLType() {\n    return \"bigint\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"number\") {\n      return value;\n    }\n    return Number(value);\n  }\n}\nclass PgBigInt64Builder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgBigInt64Builder\";\n  constructor(name) {\n    super(name, \"bigint\", \"PgBigInt64\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigInt64(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBigInt64 extends PgColumn {\n  static [entityKind] = \"PgBigInt64\";\n  getSQLType() {\n    return \"bigint\";\n  }\n  // eslint-disable-next-line unicorn/prefer-native-coercion-functions\n  mapFromDriverValue(value) {\n    return BigInt(value);\n  }\n}\nfunction bigint(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config.mode === \"number\") {\n    return new PgBigInt53Builder(name);\n  }\n  return new PgBigInt64Builder(name);\n}\nexport {\n  PgBigInt53,\n  PgBigInt53Builder,\n  PgBigInt64,\n  PgBigInt64Builder,\n  bigint\n};\n//# sourceMappingURL=bigint.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgBigSerial53Builder extends PgColumnBuilder {\n  static [entityKind] = \"PgBigSerial53Builder\";\n  constructor(name) {\n    super(name, \"number\", \"PgBigSerial53\");\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigSerial53(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBigSerial53 extends PgColumn {\n  static [entityKind] = \"PgBigSerial53\";\n  getSQLType() {\n    return \"bigserial\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"number\") {\n      return value;\n    }\n    return Number(value);\n  }\n}\nclass PgBigSerial64Builder extends PgColumnBuilder {\n  static [entityKind] = \"PgBigSerial64Builder\";\n  constructor(name) {\n    super(name, \"bigint\", \"PgBigSerial64\");\n    this.config.hasDefault = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgBigSerial64(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBigSerial64 extends PgColumn {\n  static [entityKind] = \"PgBigSerial64\";\n  getSQLType() {\n    return \"bigserial\";\n  }\n  // eslint-disable-next-line unicorn/prefer-native-coercion-functions\n  mapFromDriverValue(value) {\n    return BigInt(value);\n  }\n}\nfunction bigserial(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (config.mode === \"number\") {\n    return new PgBigSerial53Builder(name);\n  }\n  return new PgBigSerial64Builder(name);\n}\nexport {\n  PgBigSerial53,\n  PgBigSerial53Builder,\n  PgBigSerial64,\n  PgBigSerial64Builder,\n  bigserial\n};\n//# sourceMappingURL=bigserial.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgCharBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgCharBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgChar\");\n    this.config.length = config.length;\n    this.config.enumValues = config.enum;\n  }\n  /** @internal */\n  build(table) {\n    return new PgChar(\n      table,\n      this.config\n    );\n  }\n}\nclass PgChar extends PgColumn {\n  static [entityKind] = \"PgChar\";\n  length = this.config.length;\n  enumValues = this.config.enumValues;\n  getSQLType() {\n    return this.length === void 0 ? `char` : `char(${this.length})`;\n  }\n}\nfunction char(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgCharBuilder(name, config);\n}\nexport {\n  PgChar,\n  PgCharBuilder,\n  char\n};\n//# sourceMappingURL=char.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgCidrBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgCidrBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgCidr\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgCidr(table, this.config);\n  }\n}\nclass PgCidr extends PgColumn {\n  static [entityKind] = \"PgCidr\";\n  getSQLType() {\n    return \"cidr\";\n  }\n}\nfunction cidr(name) {\n  return new PgCidrBuilder(name ?? \"\");\n}\nexport {\n  PgCidr,\n  PgCidrBuilder,\n  cidr\n};\n//# sourceMappingURL=cidr.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgCustomColumnBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgCustomColumnBuilder\";\n  constructor(name, fieldConfig, customTypeParams) {\n    super(name, \"custom\", \"PgCustomColumn\");\n    this.config.fieldConfig = fieldConfig;\n    this.config.customTypeParams = customTypeParams;\n  }\n  /** @internal */\n  build(table) {\n    return new PgCustomColumn(\n      table,\n      this.config\n    );\n  }\n}\nclass PgCustomColumn extends PgColumn {\n  static [entityKind] = \"PgCustomColumn\";\n  sqlName;\n  mapTo;\n  mapFrom;\n  constructor(table, config) {\n    super(table, config);\n    this.sqlName = config.customTypeParams.dataType(config.fieldConfig);\n    this.mapTo = config.customTypeParams.toDriver;\n    this.mapFrom = config.customTypeParams.fromDriver;\n  }\n  getSQLType() {\n    return this.sqlName;\n  }\n  mapFromDriverValue(value) {\n    return typeof this.mapFrom === \"function\" ? this.mapFrom(value) : value;\n  }\n  mapToDriverValue(value) {\n    return typeof this.mapTo === \"function\" ? this.mapTo(value) : value;\n  }\n}\nfunction customType(customTypeParams) {\n  return (a, b) => {\n    const { name, config } = getColumnNameAndConfig(a, b);\n    return new PgCustomColumnBuilder(name, config, customTypeParams);\n  };\n}\nexport {\n  PgCustomColumn,\n  PgCustomColumnBuilder,\n  customType\n};\n//# sourceMappingURL=custom.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgDoublePrecisionBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgDoublePrecisionBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgDoublePrecision\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgDoublePrecision(\n      table,\n      this.config\n    );\n  }\n}\nclass PgDoublePrecision extends PgColumn {\n  static [entityKind] = \"PgDoublePrecision\";\n  getSQLType() {\n    return \"double precision\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      return Number.parseFloat(value);\n    }\n    return value;\n  }\n}\nfunction doublePrecision(name) {\n  return new PgDoublePrecisionBuilder(name ?? \"\");\n}\nexport {\n  PgDoublePrecision,\n  PgDoublePrecisionBuilder,\n  doublePrecision\n};\n//# sourceMappingURL=double-precision.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgInetBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgInetBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgInet\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgInet(table, this.config);\n  }\n}\nclass PgInet extends PgColumn {\n  static [entityKind] = \"PgInet\";\n  getSQLType() {\n    return \"inet\";\n  }\n}\nfunction inet(name) {\n  return new PgInetBuilder(name ?? \"\");\n}\nexport {\n  PgInet,\n  PgInetBuilder,\n  inet\n};\n//# sourceMappingURL=inet.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgIntervalBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgIntervalBuilder\";\n  constructor(name, intervalConfig) {\n    super(name, \"string\", \"PgInterval\");\n    this.config.intervalConfig = intervalConfig;\n  }\n  /** @internal */\n  build(table) {\n    return new PgInterval(table, this.config);\n  }\n}\nclass PgInterval extends PgColumn {\n  static [entityKind] = \"PgInterval\";\n  fields = this.config.intervalConfig.fields;\n  precision = this.config.intervalConfig.precision;\n  getSQLType() {\n    const fields = this.fields ? ` ${this.fields}` : \"\";\n    const precision = this.precision ? `(${this.precision})` : \"\";\n    return `interval${fields}${precision}`;\n  }\n}\nfunction interval(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgIntervalBuilder(name, config);\n}\nexport {\n  PgInterval,\n  PgIntervalBuilder,\n  interval\n};\n//# sourceMappingURL=interval.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgLineBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgLineBuilder\";\n  constructor(name) {\n    super(name, \"array\", \"PgLine\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgLineTuple(\n      table,\n      this.config\n    );\n  }\n}\nclass PgLineTuple extends PgColumn {\n  static [entityKind] = \"PgLine\";\n  getSQLType() {\n    return \"line\";\n  }\n  mapFromDriverValue(value) {\n    const [a, b, c] = value.slice(1, -1).split(\",\");\n    return [Number.parseFloat(a), Number.parseFloat(b), Number.parseFloat(c)];\n  }\n  mapToDriverValue(value) {\n    return `{${value[0]},${value[1]},${value[2]}}`;\n  }\n}\nclass PgLineABCBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgLineABCBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgLineABC\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgLineABC(\n      table,\n      this.config\n    );\n  }\n}\nclass PgLineABC extends PgColumn {\n  static [entityKind] = \"PgLineABC\";\n  getSQLType() {\n    return \"line\";\n  }\n  mapFromDriverValue(value) {\n    const [a, b, c] = value.slice(1, -1).split(\",\");\n    return { a: Number.parseFloat(a), b: Number.parseFloat(b), c: Number.parseFloat(c) };\n  }\n  mapToDriverValue(value) {\n    return `{${value.a},${value.b},${value.c}}`;\n  }\n}\nfunction line(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (!config?.mode || config.mode === \"tuple\") {\n    return new PgLineBuilder(name);\n  }\n  return new PgLineABCBuilder(name);\n}\nexport {\n  PgLineABC,\n  PgLineABCBuilder,\n  PgLineBuilder,\n  PgLineTuple,\n  line\n};\n//# sourceMappingURL=line.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgMacaddrBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgMacaddrBuilder\";\n  constructor(name) {\n    super(name, \"string\", \"PgMacaddr\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgMacaddr(table, this.config);\n  }\n}\nclass PgMacaddr extends PgColumn {\n  static [entityKind] = \"PgMacaddr\";\n  getSQLType() {\n    return \"macaddr\";\n  }\n}\nfunction macaddr(name) {\n  return new PgMacaddrBuilder(name ?? \"\");\n}\nexport {\n  PgMacaddr,\n  PgMacaddrBuilder,\n  macaddr\n};\n//# sourceMappingURL=macaddr.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgMacaddr8Builder extends PgColumnBuilder {\n  static [entityKind] = \"PgMacaddr8Builder\";\n  constructor(name) {\n    super(name, \"string\", \"PgMacaddr8\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgMacaddr8(table, this.config);\n  }\n}\nclass PgMacaddr8 extends PgColumn {\n  static [entityKind] = \"PgMacaddr8\";\n  getSQLType() {\n    return \"macaddr8\";\n  }\n}\nfunction macaddr8(name) {\n  return new PgMacaddr8Builder(name ?? \"\");\n}\nexport {\n  PgMacaddr8,\n  PgMacaddr8Builder,\n  macaddr8\n};\n//# sourceMappingURL=macaddr8.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgPointTupleBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgPointTupleBuilder\";\n  constructor(name) {\n    super(name, \"array\", \"PgPointTuple\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgPointTuple(\n      table,\n      this.config\n    );\n  }\n}\nclass PgPointTuple extends PgColumn {\n  static [entityKind] = \"PgPointTuple\";\n  getSQLType() {\n    return \"point\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      const [x, y] = value.slice(1, -1).split(\",\");\n      return [Number.parseFloat(x), Number.parseFloat(y)];\n    }\n    return [value.x, value.y];\n  }\n  mapToDriverValue(value) {\n    return `(${value[0]},${value[1]})`;\n  }\n}\nclass PgPointObjectBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgPointObjectBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgPointObject\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgPointObject(\n      table,\n      this.config\n    );\n  }\n}\nclass PgPointObject extends PgColumn {\n  static [entityKind] = \"PgPointObject\";\n  getSQLType() {\n    return \"point\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      const [x, y] = value.slice(1, -1).split(\",\");\n      return { x: Number.parseFloat(x), y: Number.parseFloat(y) };\n    }\n    return value;\n  }\n  mapToDriverValue(value) {\n    return `(${value.x},${value.y})`;\n  }\n}\nfunction point(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (!config?.mode || config.mode === \"tuple\") {\n    return new PgPointTupleBuilder(name);\n  }\n  return new PgPointObjectBuilder(name);\n}\nexport {\n  PgPointObject,\n  PgPointObjectBuilder,\n  PgPointTuple,\n  PgPointTupleBuilder,\n  point\n};\n//# sourceMappingURL=point.js.map", "function hexToBytes(hex) {\n  const bytes = [];\n  for (let c = 0; c < hex.length; c += 2) {\n    bytes.push(Number.parseInt(hex.slice(c, c + 2), 16));\n  }\n  return new Uint8Array(bytes);\n}\nfunction bytesToFloat64(bytes, offset) {\n  const buffer = new ArrayBuffer(8);\n  const view = new DataView(buffer);\n  for (let i = 0; i < 8; i++) {\n    view.setUint8(i, bytes[offset + i]);\n  }\n  return view.getFloat64(0, true);\n}\nfunction parseEWKB(hex) {\n  const bytes = hexToBytes(hex);\n  let offset = 0;\n  const byteOrder = bytes[offset];\n  offset += 1;\n  const view = new DataView(bytes.buffer);\n  const geomType = view.getUint32(offset, byteOrder === 1);\n  offset += 4;\n  let _srid;\n  if (geomType & 536870912) {\n    _srid = view.getUint32(offset, byteOrder === 1);\n    offset += 4;\n  }\n  if ((geomType & 65535) === 1) {\n    const x = bytesToFloat64(bytes, offset);\n    offset += 8;\n    const y = bytesToFloat64(bytes, offset);\n    offset += 8;\n    return [x, y];\n  }\n  throw new Error(\"Unsupported geometry type\");\n}\nexport {\n  parseEWKB\n};\n//# sourceMappingURL=utils.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nimport { parseEWKB } from \"./utils.js\";\nclass PgGeometryBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgGeometryBuilder\";\n  constructor(name) {\n    super(name, \"array\", \"PgGeometry\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgGeometry(\n      table,\n      this.config\n    );\n  }\n}\nclass PgGeometry extends PgColumn {\n  static [entityKind] = \"PgGeometry\";\n  getSQLType() {\n    return \"geometry(point)\";\n  }\n  mapFromDriverValue(value) {\n    return parseEWKB(value);\n  }\n  mapToDriverValue(value) {\n    return `point(${value[0]} ${value[1]})`;\n  }\n}\nclass PgGeometryObjectBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgGeometryObjectBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgGeometryObject\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgGeometryObject(\n      table,\n      this.config\n    );\n  }\n}\nclass PgGeometryObject extends PgColumn {\n  static [entityKind] = \"PgGeometryObject\";\n  getSQLType() {\n    return \"geometry(point)\";\n  }\n  mapFromDriverValue(value) {\n    const parsed = parseEWKB(value);\n    return { x: parsed[0], y: parsed[1] };\n  }\n  mapToDriverValue(value) {\n    return `point(${value.x} ${value.y})`;\n  }\n}\nfunction geometry(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  if (!config?.mode || config.mode === \"tuple\") {\n    return new PgGeometryBuilder(name);\n  }\n  return new PgGeometryObjectBuilder(name);\n}\nexport {\n  PgGeometry,\n  PgGeometryBuilder,\n  PgGeometryObject,\n  PgGeometryObjectBuilder,\n  geometry\n};\n//# sourceMappingURL=geometry.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgRealBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgRealBuilder\";\n  constructor(name, length) {\n    super(name, \"number\", \"PgReal\");\n    this.config.length = length;\n  }\n  /** @internal */\n  build(table) {\n    return new PgReal(table, this.config);\n  }\n}\nclass PgReal extends PgColumn {\n  static [entityKind] = \"PgReal\";\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return \"real\";\n  }\n  mapFromDriverValue = (value) => {\n    if (typeof value === \"string\") {\n      return Number.parseFloat(value);\n    }\n    return value;\n  };\n}\nfunction real(name) {\n  return new PgRealBuilder(name ?? \"\");\n}\nexport {\n  PgReal,\n  PgRealBuilder,\n  real\n};\n//# sourceMappingURL=real.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgIntColumnBaseBuilder } from \"./int.common.js\";\nclass PgSmallIntBuilder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgSmallIntBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgSmallInt\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgSmallInt(table, this.config);\n  }\n}\nclass PgSmallInt extends PgColumn {\n  static [entityKind] = \"PgSmallInt\";\n  getSQLType() {\n    return \"smallint\";\n  }\n  mapFromDriverValue = (value) => {\n    if (typeof value === \"string\") {\n      return Number(value);\n    }\n    return value;\n  };\n}\nfunction smallint(name) {\n  return new PgSmallIntBuilder(name ?? \"\");\n}\nexport {\n  PgSmallInt,\n  PgSmallIntBuilder,\n  smallint\n};\n//# sourceMappingURL=smallint.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgSmallSerialBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgSmallSerialBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgSmallSerial\");\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgSmallSerial(\n      table,\n      this.config\n    );\n  }\n}\nclass PgSmallSerial extends PgColumn {\n  static [entityKind] = \"PgSmallSerial\";\n  getSQLType() {\n    return \"smallserial\";\n  }\n}\nfunction smallserial(name) {\n  return new PgSmallSerialBuilder(name ?? \"\");\n}\nexport {\n  PgSmallSerial,\n  PgSmallSerialBuilder,\n  smallserial\n};\n//# sourceMappingURL=smallserial.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgBinaryVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgBinaryVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgBinaryVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgBinaryVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgBinaryVector extends PgColumn {\n  static [entityKind] = \"PgBinaryVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `bit(${this.dimensions})`;\n  }\n}\nfunction bit(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgBinaryVectorBuilder(name, config);\n}\nexport {\n  PgBinaryVector,\n  PgBinaryVectorBuilder,\n  bit\n};\n//# sourceMappingURL=bit.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgHalfVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgHalfVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"array\", \"PgHalfVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgHalfVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgHalfVector extends PgColumn {\n  static [entityKind] = \"PgHalfVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `halfvec(${this.dimensions})`;\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    return value.slice(1, -1).split(\",\").map((v) => Number.parseFloat(v));\n  }\n}\nfunction halfvec(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgHalfVectorBuilder(name, config);\n}\nexport {\n  PgHalfVector,\n  PgHalfVectorBuilder,\n  halfvec\n};\n//# sourceMappingURL=halfvec.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgSparseVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgSparseVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgSparseVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgSparseVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgSparseVector extends PgColumn {\n  static [entityKind] = \"PgSparseVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `sparsevec(${this.dimensions})`;\n  }\n}\nfunction sparsevec(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgSparseVectorBuilder(name, config);\n}\nexport {\n  PgSparseVector,\n  PgSparseVectorBuilder,\n  sparsevec\n};\n//# sourceMappingURL=sparsevec.js.map", "import { entityKind } from \"../../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"../common.js\";\nclass PgVectorBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgVectorBuilder\";\n  constructor(name, config) {\n    super(name, \"array\", \"PgVector\");\n    this.config.dimensions = config.dimensions;\n  }\n  /** @internal */\n  build(table) {\n    return new PgVector(\n      table,\n      this.config\n    );\n  }\n}\nclass PgVector extends PgColumn {\n  static [entityKind] = \"PgVector\";\n  dimensions = this.config.dimensions;\n  getSQLType() {\n    return `vector(${this.dimensions})`;\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    return value.slice(1, -1).split(\",\").map((v) => Number.parseFloat(v));\n  }\n}\nfunction vector(a, b) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgVectorBuilder(name, config);\n}\nexport {\n  PgVector,\n  PgVectorBuilder,\n  vector\n};\n//# sourceMappingURL=vector.js.map", "import { bigint } from \"./bigint.js\";\nimport { bigserial } from \"./bigserial.js\";\nimport { boolean } from \"./boolean.js\";\nimport { char } from \"./char.js\";\nimport { cidr } from \"./cidr.js\";\nimport { customType } from \"./custom.js\";\nimport { date } from \"./date.js\";\nimport { doublePrecision } from \"./double-precision.js\";\nimport { inet } from \"./inet.js\";\nimport { integer } from \"./integer.js\";\nimport { interval } from \"./interval.js\";\nimport { json } from \"./json.js\";\nimport { jsonb } from \"./jsonb.js\";\nimport { line } from \"./line.js\";\nimport { macaddr } from \"./macaddr.js\";\nimport { macaddr8 } from \"./macaddr8.js\";\nimport { numeric } from \"./numeric.js\";\nimport { point } from \"./point.js\";\nimport { geometry } from \"./postgis_extension/geometry.js\";\nimport { real } from \"./real.js\";\nimport { serial } from \"./serial.js\";\nimport { smallint } from \"./smallint.js\";\nimport { smallserial } from \"./smallserial.js\";\nimport { text } from \"./text.js\";\nimport { time } from \"./time.js\";\nimport { timestamp } from \"./timestamp.js\";\nimport { uuid } from \"./uuid.js\";\nimport { varchar } from \"./varchar.js\";\nimport { bit } from \"./vector_extension/bit.js\";\nimport { halfvec } from \"./vector_extension/halfvec.js\";\nimport { sparsevec } from \"./vector_extension/sparsevec.js\";\nimport { vector } from \"./vector_extension/vector.js\";\nfunction getPgColumnBuilders() {\n  return {\n    bigint,\n    bigserial,\n    boolean,\n    char,\n    cidr,\n    customType,\n    date,\n    doublePrecision,\n    inet,\n    integer,\n    interval,\n    json,\n    jsonb,\n    line,\n    macaddr,\n    macaddr8,\n    numeric,\n    point,\n    geometry,\n    real,\n    serial,\n    smallint,\n    smallserial,\n    text,\n    time,\n    timestamp,\n    uuid,\n    varchar,\n    bit,\n    halfvec,\n    sparsevec,\n    vector\n  };\n}\nexport {\n  getPgColumnBuilders\n};\n//# sourceMappingURL=all.js.map", "import { entityKind } from \"../entity.js\";\nimport { Table } from \"../table.js\";\nimport { getPgColumnBuilders } from \"./columns/all.js\";\nconst InlineForeignKeys = Symbol.for(\"drizzle:PgInlineForeignKeys\");\nconst EnableRLS = Symbol.for(\"drizzle:EnableRLS\");\nclass PgTable extends Table {\n  static [entityKind] = \"PgTable\";\n  /** @internal */\n  static Symbol = Object.assign({}, Table.Symbol, {\n    InlineForeignKeys,\n    EnableRLS\n  });\n  /**@internal */\n  [InlineForeignKeys] = [];\n  /** @internal */\n  [EnableRLS] = false;\n  /** @internal */\n  [Table.Symbol.ExtraConfigBuilder] = void 0;\n  /** @internal */\n  [Table.Symbol.ExtraConfigColumns] = {};\n}\nfunction pgTableWithSchema(name, columns, extraConfig, schema, baseName = name) {\n  const rawTable = new PgTable(name, schema, baseName);\n  const parsedColumns = typeof columns === \"function\" ? columns(getPgColumnBuilders()) : columns;\n  const builtColumns = Object.fromEntries(\n    Object.entries(parsedColumns).map(([name2, colBuilderBase]) => {\n      const colBuilder = colBuilderBase;\n      colBuilder.setName(name2);\n      const column = colBuilder.build(rawTable);\n      rawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n      return [name2, column];\n    })\n  );\n  const builtColumnsForExtraConfig = Object.fromEntries(\n    Object.entries(parsedColumns).map(([name2, colBuilderBase]) => {\n      const colBuilder = colBuilderBase;\n      colBuilder.setName(name2);\n      const column = colBuilder.buildExtraConfigColumn(rawTable);\n      return [name2, column];\n    })\n  );\n  const table = Object.assign(rawTable, builtColumns);\n  table[Table.Symbol.Columns] = builtColumns;\n  table[Table.Symbol.ExtraConfigColumns] = builtColumnsForExtraConfig;\n  if (extraConfig) {\n    table[PgTable.Symbol.ExtraConfigBuilder] = extraConfig;\n  }\n  return Object.assign(table, {\n    enableRLS: () => {\n      table[PgTable.Symbol.EnableRLS] = true;\n      return table;\n    }\n  });\n}\nconst pgTable = (name, columns, extraConfig) => {\n  return pgTableWithSchema(name, columns, extraConfig, void 0);\n};\nfunction pgTableCreator(customizeTableName) {\n  return (name, columns, extraConfig) => {\n    return pgTableWithSchema(customizeTableName(name), columns, extraConfig, void 0, name);\n  };\n}\nexport {\n  EnableRLS,\n  InlineForeignKeys,\n  PgTable,\n  pgTable,\n  pgTableCreator,\n  pgTableWithSchema\n};\n//# sourceMappingURL=table.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumnBuilder } from \"./common.js\";\nclass PgIntColumnBaseBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgIntColumnBaseBuilder\";\n  generatedAlwaysAsIdentity(sequence) {\n    if (sequence) {\n      const { name, ...options } = sequence;\n      this.config.generatedIdentity = {\n        type: \"always\",\n        sequenceName: name,\n        sequenceOptions: options\n      };\n    } else {\n      this.config.generatedIdentity = {\n        type: \"always\"\n      };\n    }\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n    return this;\n  }\n  generatedByDefaultAsIdentity(sequence) {\n    if (sequence) {\n      const { name, ...options } = sequence;\n      this.config.generatedIdentity = {\n        type: \"byDefault\",\n        sequenceName: name,\n        sequenceOptions: options\n      };\n    } else {\n      this.config.generatedIdentity = {\n        type: \"byDefault\"\n      };\n    }\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n    return this;\n  }\n}\nexport {\n  PgIntColumnBaseBuilder\n};\n//# sourceMappingURL=int.common.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgTextBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgTextBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgText\");\n    this.config.enumValues = config.enum;\n  }\n  /** @internal */\n  build(table) {\n    return new PgText(table, this.config);\n  }\n}\nclass PgText extends PgColumn {\n  static [entityKind] = \"PgText\";\n  enumValues = this.config.enumValues;\n  getSQLType() {\n    return \"text\";\n  }\n}\nfunction text(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgTextBuilder(name, config);\n}\nexport {\n  PgText,\n  PgTextBuilder,\n  text\n};\n//# sourceMappingURL=text.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgBooleanBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgBooleanBuilder\";\n  constructor(name) {\n    super(name, \"boolean\", \"PgBoolean\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgBoolean(table, this.config);\n  }\n}\nclass PgBoolean extends PgColumn {\n  static [entityKind] = \"PgBoolean\";\n  getSQLType() {\n    return \"boolean\";\n  }\n}\nfunction boolean(name) {\n  return new PgBooleanBuilder(name ?? \"\");\n}\nexport {\n  PgBoolean,\n  PgBooleanBuilder,\n  boolean\n};\n//# sourceMappingURL=boolean.js.map", "import { entityKind } from \"../../entity.js\";\nimport { getColumnNameAndConfig } from \"../../utils.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgVarcharBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgVarcharBuilder\";\n  constructor(name, config) {\n    super(name, \"string\", \"PgVarchar\");\n    this.config.length = config.length;\n    this.config.enumValues = config.enum;\n  }\n  /** @internal */\n  build(table) {\n    return new PgVarchar(\n      table,\n      this.config\n    );\n  }\n}\nclass PgVarchar extends PgColumn {\n  static [entityKind] = \"PgVarchar\";\n  length = this.config.length;\n  enumValues = this.config.enumValues;\n  getSQLType() {\n    return this.length === void 0 ? `varchar` : `varchar(${this.length})`;\n  }\n}\nfunction varchar(a, b = {}) {\n  const { name, config } = getColumnNameAndConfig(a, b);\n  return new PgVarcharBuilder(name, config);\n}\nexport {\n  PgVarchar,\n  PgVarcharBuilder,\n  varchar\n};\n//# sourceMappingURL=varchar.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgJsonbBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgJsonbBuilder\";\n  constructor(name) {\n    super(name, \"json\", \"PgJsonb\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgJsonb(table, this.config);\n  }\n}\nclass PgJsonb extends PgColumn {\n  static [entityKind] = \"PgJsonb\";\n  constructor(table, config) {\n    super(table, config);\n  }\n  getSQLType() {\n    return \"jsonb\";\n  }\n  mapToDriverValue(value) {\n    return JSON.stringify(value);\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      try {\n        return JSON.parse(value);\n      } catch {\n        return value;\n      }\n    }\n    return value;\n  }\n}\nfunction jsonb(name) {\n  return new PgJsonbBuilder(name ?? \"\");\n}\nexport {\n  Pg<PERSON><PERSON><PERSON>,\n  PgJsonbBuilder,\n  jsonb\n};\n//# sourceMappingURL=jsonb.js.map", "import { Column } from \"./column.js\";\nimport { is } from \"./entity.js\";\nimport { Param, SQL, View } from \"./sql/sql.js\";\nimport { Subquery } from \"./subquery.js\";\nimport { getTableName, Table } from \"./table.js\";\nimport { ViewBaseConfig } from \"./view-common.js\";\nfunction mapResultRow(columns, row, joinsNotNullableMap) {\n  const nullifyMap = {};\n  const result = columns.reduce(\n    (result2, { path, field }, columnIndex) => {\n      let decoder;\n      if (is(field, Column)) {\n        decoder = field;\n      } else if (is(field, SQL)) {\n        decoder = field.decoder;\n      } else {\n        decoder = field.sql.decoder;\n      }\n      let node = result2;\n      for (const [pathChunkIndex, pathChunk] of path.entries()) {\n        if (pathChunkIndex < path.length - 1) {\n          if (!(pathChunk in node)) {\n            node[pathChunk] = {};\n          }\n          node = node[pathChunk];\n        } else {\n          const rawValue = row[columnIndex];\n          const value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);\n          if (joinsNotNullableMap && is(field, Column) && path.length === 2) {\n            const objectName = path[0];\n            if (!(objectName in nullifyMap)) {\n              nullifyMap[objectName] = value === null ? getTableName(field.table) : false;\n            } else if (typeof nullifyMap[objectName] === \"string\" && nullifyMap[objectName] !== getTableName(field.table)) {\n              nullifyMap[objectName] = false;\n            }\n          }\n        }\n      }\n      return result2;\n    },\n    {}\n  );\n  if (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {\n    for (const [objectName, tableName] of Object.entries(nullifyMap)) {\n      if (typeof tableName === \"string\" && !joinsNotNullableMap[tableName]) {\n        result[objectName] = null;\n      }\n    }\n  }\n  return result;\n}\nfunction orderSelectedFields(fields, pathPrefix) {\n  return Object.entries(fields).reduce((result, [name, field]) => {\n    if (typeof name !== \"string\") {\n      return result;\n    }\n    const newPath = pathPrefix ? [...pathPrefix, name] : [name];\n    if (is(field, Column) || is(field, SQL) || is(field, SQL.Aliased)) {\n      result.push({ path: newPath, field });\n    } else if (is(field, Table)) {\n      result.push(...orderSelectedFields(field[Table.Symbol.Columns], newPath));\n    } else {\n      result.push(...orderSelectedFields(field, newPath));\n    }\n    return result;\n  }, []);\n}\nfunction haveSameKeys(left, right) {\n  const leftKeys = Object.keys(left);\n  const rightKeys = Object.keys(right);\n  if (leftKeys.length !== rightKeys.length) {\n    return false;\n  }\n  for (const [index, key] of leftKeys.entries()) {\n    if (key !== rightKeys[index]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction mapUpdateSet(table, values) {\n  const entries = Object.entries(values).filter(([, value]) => value !== void 0).map(([key, value]) => {\n    if (is(value, SQL) || is(value, Column)) {\n      return [key, value];\n    } else {\n      return [key, new Param(value, table[Table.Symbol.Columns][key])];\n    }\n  });\n  if (entries.length === 0) {\n    throw new Error(\"No values to set\");\n  }\n  return Object.fromEntries(entries);\n}\nfunction applyMixins(baseClass, extendedClasses) {\n  for (const extendedClass of extendedClasses) {\n    for (const name of Object.getOwnPropertyNames(extendedClass.prototype)) {\n      if (name === \"constructor\") continue;\n      Object.defineProperty(\n        baseClass.prototype,\n        name,\n        Object.getOwnPropertyDescriptor(extendedClass.prototype, name) || /* @__PURE__ */ Object.create(null)\n      );\n    }\n  }\n}\nfunction getTableColumns(table) {\n  return table[Table.Symbol.Columns];\n}\nfunction getViewSelectedFields(view) {\n  return view[ViewBaseConfig].selectedFields;\n}\nfunction getTableLikeName(table) {\n  return is(table, Subquery) ? table._.alias : is(table, View) ? table[ViewBaseConfig].name : is(table, SQL) ? void 0 : table[Table.Symbol.IsAlias] ? table[Table.Symbol.Name] : table[Table.Symbol.BaseName];\n}\nfunction getColumnNameAndConfig(a, b) {\n  return {\n    name: typeof a === \"string\" && a.length > 0 ? a : \"\",\n    config: typeof a === \"object\" ? a : b\n  };\n}\nconst _ = {};\nconst __ = {};\nfunction isConfig(data) {\n  if (typeof data !== \"object\" || data === null) return false;\n  if (data.constructor.name !== \"Object\") return false;\n  if (\"logger\" in data) {\n    const type = typeof data[\"logger\"];\n    if (type !== \"boolean\" && (type !== \"object\" || typeof data[\"logger\"][\"logQuery\"] !== \"function\") && type !== \"undefined\") return false;\n    return true;\n  }\n  if (\"schema\" in data) {\n    const type = typeof data[\"schema\"];\n    if (type !== \"object\" && type !== \"undefined\") return false;\n    return true;\n  }\n  if (\"casing\" in data) {\n    const type = typeof data[\"casing\"];\n    if (type !== \"string\" && type !== \"undefined\") return false;\n    return true;\n  }\n  if (\"mode\" in data) {\n    if (data[\"mode\"] !== \"default\" || data[\"mode\"] !== \"planetscale\" || data[\"mode\"] !== void 0) return false;\n    return true;\n  }\n  if (\"connection\" in data) {\n    const type = typeof data[\"connection\"];\n    if (type !== \"string\" && type !== \"object\" && type !== \"undefined\") return false;\n    return true;\n  }\n  if (\"client\" in data) {\n    const type = typeof data[\"client\"];\n    if (type !== \"object\" && type !== \"function\" && type !== \"undefined\") return false;\n    return true;\n  }\n  if (Object.keys(data).length === 0) return true;\n  return false;\n}\nexport {\n  applyMixins,\n  getColumnNameAndConfig,\n  getTableColumns,\n  getTableLikeName,\n  getViewSelectedFields,\n  haveSameKeys,\n  isConfig,\n  mapResultRow,\n  mapUpdateSet,\n  orderSelectedFields\n};\n//# sourceMappingURL=utils.js.map", "const ViewBaseConfig = Symbol.for(\"drizzle:ViewBaseConfig\");\nexport {\n  ViewBaseConfig\n};\n//# sourceMappingURL=view-common.js.map", "import { entityKind } from \"../entity.js\";\nimport { PgTable } from \"./table.js\";\nfunction primaryKey(...config) {\n  if (config[0].columns) {\n    return new PrimaryKeyBuilder(config[0].columns, config[0].name);\n  }\n  return new PrimaryKeyBuilder(config);\n}\nclass PrimaryKeyBuilder {\n  static [entityKind] = \"PgPrimaryKeyBuilder\";\n  /** @internal */\n  columns;\n  /** @internal */\n  name;\n  constructor(columns, name) {\n    this.columns = columns;\n    this.name = name;\n  }\n  /** @internal */\n  build(table) {\n    return new PrimaryKey(table, this.columns, this.name);\n  }\n}\nclass PrimaryKey {\n  constructor(table, columns, name) {\n    this.table = table;\n    this.columns = columns;\n    this.name = name;\n  }\n  static [entityKind] = \"PgPrimaryKey\";\n  columns;\n  name;\n  getName() {\n    return this.name ?? `${this.table[PgTable.Symbol.Name]}_${this.columns.map((column) => column.name).join(\"_\")}_pk`;\n  }\n}\nexport {\n  PrimaryK<PERSON>,\n  PrimaryKeyBuilder,\n  primaryKey\n};\n//# sourceMappingURL=primary-keys.js.map", "import { getTableUniqueName, Table } from \"./table.js\";\nimport { Column } from \"./column.js\";\nimport { entityKind, is } from \"./entity.js\";\nimport { PrimaryKeyBuilder } from \"./pg-core/primary-keys.js\";\nimport {\n  and,\n  asc,\n  between,\n  desc,\n  eq,\n  exists,\n  gt,\n  gte,\n  ilike,\n  inArray,\n  isNotNull,\n  isNull,\n  like,\n  lt,\n  lte,\n  ne,\n  not,\n  notBetween,\n  notExists,\n  notIlike,\n  notInArray,\n  notLike,\n  or\n} from \"./sql/expressions/index.js\";\nimport { SQL, sql } from \"./sql/sql.js\";\nclass Relation {\n  constructor(sourceTable, referencedTable, relationName) {\n    this.sourceTable = sourceTable;\n    this.referencedTable = referencedTable;\n    this.relationName = relationName;\n    this.referencedTableName = referencedTable[Table.Symbol.Name];\n  }\n  static [entityKind] = \"Relation\";\n  referencedTableName;\n  fieldName;\n}\nclass Relations {\n  constructor(table, config) {\n    this.table = table;\n    this.config = config;\n  }\n  static [entityKind] = \"Relations\";\n}\nclass One extends Relation {\n  constructor(sourceTable, referencedTable, config, isNullable) {\n    super(sourceTable, referencedTable, config?.relationName);\n    this.config = config;\n    this.isNullable = isNullable;\n  }\n  static [entityKind] = \"One\";\n  withFieldName(fieldName) {\n    const relation = new One(\n      this.sourceTable,\n      this.referencedTable,\n      this.config,\n      this.isNullable\n    );\n    relation.fieldName = fieldName;\n    return relation;\n  }\n}\nclass Many extends Relation {\n  constructor(sourceTable, referencedTable, config) {\n    super(sourceTable, referencedTable, config?.relationName);\n    this.config = config;\n  }\n  static [entityKind] = \"Many\";\n  withFieldName(fieldName) {\n    const relation = new Many(\n      this.sourceTable,\n      this.referencedTable,\n      this.config\n    );\n    relation.fieldName = fieldName;\n    return relation;\n  }\n}\nfunction getOperators() {\n  return {\n    and,\n    between,\n    eq,\n    exists,\n    gt,\n    gte,\n    ilike,\n    inArray,\n    isNull,\n    isNotNull,\n    like,\n    lt,\n    lte,\n    ne,\n    not,\n    notBetween,\n    notExists,\n    notLike,\n    notIlike,\n    notInArray,\n    or,\n    sql\n  };\n}\nfunction getOrderByOperators() {\n  return {\n    sql,\n    asc,\n    desc\n  };\n}\nfunction extractTablesRelationalConfig(schema, configHelpers) {\n  if (Object.keys(schema).length === 1 && \"default\" in schema && !is(schema[\"default\"], Table)) {\n    schema = schema[\"default\"];\n  }\n  const tableNamesMap = {};\n  const relationsBuffer = {};\n  const tablesConfig = {};\n  for (const [key, value] of Object.entries(schema)) {\n    if (is(value, Table)) {\n      const dbName = getTableUniqueName(value);\n      const bufferedRelations = relationsBuffer[dbName];\n      tableNamesMap[dbName] = key;\n      tablesConfig[key] = {\n        tsName: key,\n        dbName: value[Table.Symbol.Name],\n        schema: value[Table.Symbol.Schema],\n        columns: value[Table.Symbol.Columns],\n        relations: bufferedRelations?.relations ?? {},\n        primaryKey: bufferedRelations?.primaryKey ?? []\n      };\n      for (const column of Object.values(\n        value[Table.Symbol.Columns]\n      )) {\n        if (column.primary) {\n          tablesConfig[key].primaryKey.push(column);\n        }\n      }\n      const extraConfig = value[Table.Symbol.ExtraConfigBuilder]?.(value[Table.Symbol.ExtraConfigColumns]);\n      if (extraConfig) {\n        for (const configEntry of Object.values(extraConfig)) {\n          if (is(configEntry, PrimaryKeyBuilder)) {\n            tablesConfig[key].primaryKey.push(...configEntry.columns);\n          }\n        }\n      }\n    } else if (is(value, Relations)) {\n      const dbName = getTableUniqueName(value.table);\n      const tableName = tableNamesMap[dbName];\n      const relations2 = value.config(\n        configHelpers(value.table)\n      );\n      let primaryKey;\n      for (const [relationName, relation] of Object.entries(relations2)) {\n        if (tableName) {\n          const tableConfig = tablesConfig[tableName];\n          tableConfig.relations[relationName] = relation;\n          if (primaryKey) {\n            tableConfig.primaryKey.push(...primaryKey);\n          }\n        } else {\n          if (!(dbName in relationsBuffer)) {\n            relationsBuffer[dbName] = {\n              relations: {},\n              primaryKey\n            };\n          }\n          relationsBuffer[dbName].relations[relationName] = relation;\n        }\n      }\n    }\n  }\n  return { tables: tablesConfig, tableNamesMap };\n}\nfunction relations(table, relations2) {\n  return new Relations(\n    table,\n    (helpers) => Object.fromEntries(\n      Object.entries(relations2(helpers)).map(([key, value]) => [\n        key,\n        value.withFieldName(key)\n      ])\n    )\n  );\n}\nfunction createOne(sourceTable) {\n  return function one(table, config) {\n    return new One(\n      sourceTable,\n      table,\n      config,\n      config?.fields.reduce((res, f) => res && f.notNull, true) ?? false\n    );\n  };\n}\nfunction createMany(sourceTable) {\n  return function many(referencedTable, config) {\n    return new Many(sourceTable, referencedTable, config);\n  };\n}\nfunction normalizeRelation(schema, tableNamesMap, relation) {\n  if (is(relation, One) && relation.config) {\n    return {\n      fields: relation.config.fields,\n      references: relation.config.references\n    };\n  }\n  const referencedTableTsName = tableNamesMap[getTableUniqueName(relation.referencedTable)];\n  if (!referencedTableTsName) {\n    throw new Error(\n      `Table \"${relation.referencedTable[Table.Symbol.Name]}\" not found in schema`\n    );\n  }\n  const referencedTableConfig = schema[referencedTableTsName];\n  if (!referencedTableConfig) {\n    throw new Error(`Table \"${referencedTableTsName}\" not found in schema`);\n  }\n  const sourceTable = relation.sourceTable;\n  const sourceTableTsName = tableNamesMap[getTableUniqueName(sourceTable)];\n  if (!sourceTableTsName) {\n    throw new Error(\n      `Table \"${sourceTable[Table.Symbol.Name]}\" not found in schema`\n    );\n  }\n  const reverseRelations = [];\n  for (const referencedTableRelation of Object.values(\n    referencedTableConfig.relations\n  )) {\n    if (relation.relationName && relation !== referencedTableRelation && referencedTableRelation.relationName === relation.relationName || !relation.relationName && referencedTableRelation.referencedTable === relation.sourceTable) {\n      reverseRelations.push(referencedTableRelation);\n    }\n  }\n  if (reverseRelations.length > 1) {\n    throw relation.relationName ? new Error(\n      `There are multiple relations with name \"${relation.relationName}\" in table \"${referencedTableTsName}\"`\n    ) : new Error(\n      `There are multiple relations between \"${referencedTableTsName}\" and \"${relation.sourceTable[Table.Symbol.Name]}\". Please specify relation name`\n    );\n  }\n  if (reverseRelations[0] && is(reverseRelations[0], One) && reverseRelations[0].config) {\n    return {\n      fields: reverseRelations[0].config.references,\n      references: reverseRelations[0].config.fields\n    };\n  }\n  throw new Error(\n    `There is not enough information to infer relation \"${sourceTableTsName}.${relation.fieldName}\"`\n  );\n}\nfunction createTableRelationsHelpers(sourceTable) {\n  return {\n    one: createOne(sourceTable),\n    many: createMany(sourceTable)\n  };\n}\nfunction mapRelationalRow(tablesConfig, tableConfig, row, buildQueryResultSelection, mapColumnValue = (value) => value) {\n  const result = {};\n  for (const [\n    selectionItemIndex,\n    selectionItem\n  ] of buildQueryResultSelection.entries()) {\n    if (selectionItem.isJson) {\n      const relation = tableConfig.relations[selectionItem.tsKey];\n      const rawSubRows = row[selectionItemIndex];\n      const subRows = typeof rawSubRows === \"string\" ? JSON.parse(rawSubRows) : rawSubRows;\n      result[selectionItem.tsKey] = is(relation, One) ? subRows && mapRelationalRow(\n        tablesConfig,\n        tablesConfig[selectionItem.relationTableTsKey],\n        subRows,\n        selectionItem.selection,\n        mapColumnValue\n      ) : subRows.map(\n        (subRow) => mapRelationalRow(\n          tablesConfig,\n          tablesConfig[selectionItem.relationTableTsKey],\n          subRow,\n          selectionItem.selection,\n          mapColumnValue\n        )\n      );\n    } else {\n      const value = mapColumnValue(row[selectionItemIndex]);\n      const field = selectionItem.field;\n      let decoder;\n      if (is(field, Column)) {\n        decoder = field;\n      } else if (is(field, SQL)) {\n        decoder = field.decoder;\n      } else {\n        decoder = field.sql.decoder;\n      }\n      result[selectionItem.tsKey] = value === null ? null : decoder.mapFromDriverValue(value);\n    }\n  }\n  return result;\n}\nexport {\n  Many,\n  One,\n  Relation,\n  Relations,\n  createMany,\n  createOne,\n  createTableRelationsHelpers,\n  extractTablesRelationalConfig,\n  getOperators,\n  getOrderByOperators,\n  mapRelationalRow,\n  normalizeRelation,\n  relations\n};\n//# sourceMappingURL=relations.js.map", "import { Column } from \"../../column.js\";\nimport { is } from \"../../entity.js\";\nimport { Table } from \"../../table.js\";\nimport {\n  isDriverValueEncoder,\n  isSQLWrapper,\n  Param,\n  Placeholder,\n  SQL,\n  sql,\n  StringChunk,\n  View\n} from \"../sql.js\";\nfunction bindIfParam(value, column) {\n  if (isDriverValueEncoder(column) && !isSQLWrapper(value) && !is(value, Param) && !is(value, Placeholder) && !is(value, Column) && !is(value, Table) && !is(value, View)) {\n    return new Param(value, column);\n  }\n  return value;\n}\nconst eq = (left, right) => {\n  return sql`${left} = ${bindIfParam(right, left)}`;\n};\nconst ne = (left, right) => {\n  return sql`${left} <> ${bindIfParam(right, left)}`;\n};\nfunction and(...unfilteredConditions) {\n  const conditions = unfilteredConditions.filter(\n    (c) => c !== void 0\n  );\n  if (conditions.length === 0) {\n    return void 0;\n  }\n  if (conditions.length === 1) {\n    return new SQL(conditions);\n  }\n  return new SQL([\n    new StringChunk(\"(\"),\n    sql.join(conditions, new StringChunk(\" and \")),\n    new StringChunk(\")\")\n  ]);\n}\nfunction or(...unfilteredConditions) {\n  const conditions = unfilteredConditions.filter(\n    (c) => c !== void 0\n  );\n  if (conditions.length === 0) {\n    return void 0;\n  }\n  if (conditions.length === 1) {\n    return new SQL(conditions);\n  }\n  return new SQL([\n    new StringChunk(\"(\"),\n    sql.join(conditions, new StringChunk(\" or \")),\n    new StringChunk(\")\")\n  ]);\n}\nfunction not(condition) {\n  return sql`not ${condition}`;\n}\nconst gt = (left, right) => {\n  return sql`${left} > ${bindIfParam(right, left)}`;\n};\nconst gte = (left, right) => {\n  return sql`${left} >= ${bindIfParam(right, left)}`;\n};\nconst lt = (left, right) => {\n  return sql`${left} < ${bindIfParam(right, left)}`;\n};\nconst lte = (left, right) => {\n  return sql`${left} <= ${bindIfParam(right, left)}`;\n};\nfunction inArray(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      return sql`false`;\n    }\n    return sql`${column} in ${values.map((v) => bindIfParam(v, column))}`;\n  }\n  return sql`${column} in ${bindIfParam(values, column)}`;\n}\nfunction notInArray(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      return sql`true`;\n    }\n    return sql`${column} not in ${values.map((v) => bindIfParam(v, column))}`;\n  }\n  return sql`${column} not in ${bindIfParam(values, column)}`;\n}\nfunction isNull(value) {\n  return sql`${value} is null`;\n}\nfunction isNotNull(value) {\n  return sql`${value} is not null`;\n}\nfunction exists(subquery) {\n  return sql`exists ${subquery}`;\n}\nfunction notExists(subquery) {\n  return sql`not exists ${subquery}`;\n}\nfunction between(column, min, max) {\n  return sql`${column} between ${bindIfParam(min, column)} and ${bindIfParam(\n    max,\n    column\n  )}`;\n}\nfunction notBetween(column, min, max) {\n  return sql`${column} not between ${bindIfParam(\n    min,\n    column\n  )} and ${bindIfParam(max, column)}`;\n}\nfunction like(column, value) {\n  return sql`${column} like ${value}`;\n}\nfunction notLike(column, value) {\n  return sql`${column} not like ${value}`;\n}\nfunction ilike(column, value) {\n  return sql`${column} ilike ${value}`;\n}\nfunction notIlike(column, value) {\n  return sql`${column} not ilike ${value}`;\n}\nfunction arrayContains(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      throw new Error(\"arrayContains requires at least one value\");\n    }\n    const array = sql`${bindIfParam(values, column)}`;\n    return sql`${column} @> ${array}`;\n  }\n  return sql`${column} @> ${bindIfParam(values, column)}`;\n}\nfunction arrayContained(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      throw new Error(\"arrayContained requires at least one value\");\n    }\n    const array = sql`${bindIfParam(values, column)}`;\n    return sql`${column} <@ ${array}`;\n  }\n  return sql`${column} <@ ${bindIfParam(values, column)}`;\n}\nfunction arrayOverlaps(column, values) {\n  if (Array.isArray(values)) {\n    if (values.length === 0) {\n      throw new Error(\"arrayOverlaps requires at least one value\");\n    }\n    const array = sql`${bindIfParam(values, column)}`;\n    return sql`${column} && ${array}`;\n  }\n  return sql`${column} && ${bindIfParam(values, column)}`;\n}\nexport {\n  and,\n  arrayContained,\n  arrayContains,\n  arrayOverlaps,\n  between,\n  bindIfParam,\n  eq,\n  exists,\n  gt,\n  gte,\n  ilike,\n  inArray,\n  isNotNull,\n  isNull,\n  like,\n  lt,\n  lte,\n  ne,\n  not,\n  notBetween,\n  notExists,\n  notIlike,\n  notInArray,\n  notLike,\n  or\n};\n//# sourceMappingURL=conditions.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgEnumObjectColumnBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgEnumObjectColumnBuilder\";\n  constructor(name, enumInstance) {\n    super(name, \"string\", \"PgEnumObjectColumn\");\n    this.config.enum = enumInstance;\n  }\n  /** @internal */\n  build(table) {\n    return new PgEnumObjectColumn(\n      table,\n      this.config\n    );\n  }\n}\nclass PgEnumObjectColumn extends PgColumn {\n  static [entityKind] = \"PgEnumObjectColumn\";\n  enum;\n  enumValues = this.config.enum.enumValues;\n  constructor(table, config) {\n    super(table, config);\n    this.enum = config.enum;\n  }\n  getSQLType() {\n    return this.enum.enumName;\n  }\n}\nconst isPgEnumSym = Symbol.for(\"drizzle:isPgEnum\");\nfunction isPgEnum(obj) {\n  return !!obj && typeof obj === \"function\" && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\nclass PgEnumColumnBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgEnumColumnBuilder\";\n  constructor(name, enumInstance) {\n    super(name, \"string\", \"PgEnumColumn\");\n    this.config.enum = enumInstance;\n  }\n  /** @internal */\n  build(table) {\n    return new PgEnumColumn(\n      table,\n      this.config\n    );\n  }\n}\nclass PgEnumColumn extends PgColumn {\n  static [entityKind] = \"PgEnumColumn\";\n  enum = this.config.enum;\n  enumValues = this.config.enum.enumValues;\n  constructor(table, config) {\n    super(table, config);\n    this.enum = config.enum;\n  }\n  getSQLType() {\n    return this.enum.enumName;\n  }\n}\nfunction pgEnum(enumName, input) {\n  return Array.isArray(input) ? pgEnumWithSchema(enumName, [...input], void 0) : pgEnumObjectWithSchema(enumName, input, void 0);\n}\nfunction pgEnumWithSchema(enumName, values, schema) {\n  const enumInstance = Object.assign(\n    (name) => new PgEnumColumnBuilder(name ?? \"\", enumInstance),\n    {\n      enumName,\n      enumValues: values,\n      schema,\n      [isPgEnumSym]: true\n    }\n  );\n  return enumInstance;\n}\nfunction pgEnumObjectWithSchema(enumName, values, schema) {\n  const enumInstance = Object.assign(\n    (name) => new PgEnumObjectColumnBuilder(name ?? \"\", enumInstance),\n    {\n      enumName,\n      enumValues: Object.values(values),\n      schema,\n      [isPgEnumSym]: true\n    }\n  );\n  return enumInstance;\n}\nexport {\n  PgEnumColumn,\n  PgEnumColumnBuilder,\n  PgEnumObjectColumn,\n  PgEnumObjectColumnBuilder,\n  isPgEnum,\n  pgEnum,\n  pgEnumObjectWithSchema,\n  pgEnumWithSchema\n};\n//# sourceMappingURL=enum.js.map", "import { entityKind } from \"./entity.js\";\nclass Subquery {\n  static [entityKind] = \"Subquery\";\n  constructor(sql, fields, alias, isWith = false, usedTables = []) {\n    this._ = {\n      brand: \"Subquery\",\n      sql,\n      selectedFields: fields,\n      alias,\n      isWith,\n      usedTables\n    };\n  }\n  // getSQL(): SQL<unknown> {\n  // \treturn new SQL([this]);\n  // }\n}\nclass WithSubquery extends Subquery {\n  static [entityKind] = \"WithSubquery\";\n}\nexport {\n  Subquery,\n  WithSubquery\n};\n//# sourceMappingURL=subquery.js.map", "const entityKind = Symbol.for(\"drizzle:entityKind\");\nconst hasOwnEntityKind = Symbol.for(\"drizzle:hasOwnEntityKind\");\nfunction is(value, type) {\n  if (!value || typeof value !== \"object\") {\n    return false;\n  }\n  if (value instanceof type) {\n    return true;\n  }\n  if (!Object.prototype.hasOwnProperty.call(type, entityKind)) {\n    throw new Error(\n      `Class \"${type.name ?? \"<unknown>\"}\" doesn't look like a Drizzle entity. If this is incorrect and the class is provided by Drizzle, please report this as a bug.`\n    );\n  }\n  let cls = Object.getPrototypeOf(value).constructor;\n  if (cls) {\n    while (cls) {\n      if (entityKind in cls && cls[entityKind] === type[entityKind]) {\n        return true;\n      }\n      cls = Object.getPrototypeOf(cls);\n    }\n  }\n  return false;\n}\nexport {\n  entityKind,\n  hasOwnEntityKind,\n  is\n};\n//# sourceMappingURL=entity.js.map", "import { sql } from \"../sql.js\";\nfunction asc(column) {\n  return sql`${column} asc`;\n}\nfunction desc(column) {\n  return sql`${column} desc`;\n}\nexport {\n  asc,\n  desc\n};\n//# sourceMappingURL=select.js.map", "// package.json\nvar version = \"0.44.4\";\n\n// src/version.ts\nvar compatibilityVersion = 10;\nexport {\n  compatibilityVersion,\n  version as npmVersion\n};\n", "import { iife } from \"./tracing-utils.js\";\nimport { npmVersion } from \"./version.js\";\nlet otel;\nlet rawTracer;\nconst tracer = {\n  startActiveSpan(name, fn) {\n    if (!otel) {\n      return fn();\n    }\n    if (!rawTracer) {\n      rawTracer = otel.trace.getTracer(\"drizzle-orm\", npmVersion);\n    }\n    return iife(\n      (otel2, rawTracer2) => rawTracer2.startActiveSpan(\n        name,\n        (span) => {\n          try {\n            return fn(span);\n          } catch (e) {\n            span.setStatus({\n              code: otel2.SpanStatusCode.ERROR,\n              message: e instanceof Error ? e.message : \"Unknown error\"\n              // eslint-disable-line no-instanceof/no-instanceof\n            });\n            throw e;\n          } finally {\n            span.end();\n          }\n        }\n      ),\n      otel,\n      rawTracer\n    );\n  }\n};\nexport {\n  tracer\n};\n//# sourceMappingURL=tracing.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn } from \"./common.js\";\nimport { PgIntColumnBaseBuilder } from \"./int.common.js\";\nclass PgIntegerBuilder extends PgIntColumnBaseBuilder {\n  static [entityKind] = \"PgIntegerBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgInteger\");\n  }\n  /** @internal */\n  build(table) {\n    return new PgInteger(table, this.config);\n  }\n}\nclass PgInteger extends PgColumn {\n  static [entityKind] = \"PgInteger\";\n  getSQLType() {\n    return \"integer\";\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      return Number.parseInt(value);\n    }\n    return value;\n  }\n}\nfunction integer(name) {\n  return new PgIntegerBuilder(name ?? \"\");\n}\nexport {\n  PgInteger,\n  PgIntegerBuilder,\n  integer\n};\n//# sourceMappingURL=integer.js.map", "import { entityKind } from \"./entity.js\";\nimport { TableName } from \"./table.utils.js\";\nconst Schema = Symbol.for(\"drizzle:Schema\");\nconst Columns = Symbol.for(\"drizzle:Columns\");\nconst ExtraConfigColumns = Symbol.for(\"drizzle:ExtraConfigColumns\");\nconst OriginalName = Symbol.for(\"drizzle:OriginalName\");\nconst BaseName = Symbol.for(\"drizzle:BaseName\");\nconst IsAlias = Symbol.for(\"drizzle:IsAlias\");\nconst ExtraConfigBuilder = Symbol.for(\"drizzle:ExtraConfigBuilder\");\nconst IsDrizzleTable = Symbol.for(\"drizzle:IsDrizzleTable\");\nclass Table {\n  static [entityKind] = \"Table\";\n  /** @internal */\n  static Symbol = {\n    Name: TableName,\n    Schema,\n    OriginalName,\n    Columns,\n    ExtraConfigColumns,\n    BaseName,\n    IsAlias,\n    ExtraConfigBuilder\n  };\n  /**\n   * @internal\n   * Can be changed if the table is aliased.\n   */\n  [TableName];\n  /**\n   * @internal\n   * Used to store the original name of the table, before any aliasing.\n   */\n  [OriginalName];\n  /** @internal */\n  [Schema];\n  /** @internal */\n  [Columns];\n  /** @internal */\n  [ExtraConfigColumns];\n  /**\n   *  @internal\n   * Used to store the table name before the transformation via the `tableCreator` functions.\n   */\n  [BaseName];\n  /** @internal */\n  [IsAlias] = false;\n  /** @internal */\n  [IsDrizzleTable] = true;\n  /** @internal */\n  [ExtraConfigBuilder] = void 0;\n  constructor(name, schema, baseName) {\n    this[TableName] = this[OriginalName] = name;\n    this[Schema] = schema;\n    this[BaseName] = baseName;\n  }\n}\nfunction isTable(table) {\n  return typeof table === \"object\" && table !== null && IsDrizzleTable in table;\n}\nfunction getTableName(table) {\n  return table[TableName];\n}\nfunction getTableUniqueName(table) {\n  return `${table[Schema] ?? \"public\"}.${table[TableName]}`;\n}\nexport {\n  BaseName,\n  Columns,\n  ExtraConfigBuilder,\n  ExtraConfigColumns,\n  IsAlias,\n  OriginalName,\n  Schema,\n  Table,\n  getTableName,\n  getTableUniqueName,\n  isTable\n};\n//# sourceMappingURL=table.js.map", "import { entityKind } from \"../../entity.js\";\nimport { PgColumn, PgColumnBuilder } from \"./common.js\";\nclass PgSerialBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgSerialBuilder\";\n  constructor(name) {\n    super(name, \"number\", \"PgSerial\");\n    this.config.hasDefault = true;\n    this.config.notNull = true;\n  }\n  /** @internal */\n  build(table) {\n    return new PgSerial(table, this.config);\n  }\n}\nclass PgSerial extends PgColumn {\n  static [entityKind] = \"PgSerial\";\n  getSQLType() {\n    return \"serial\";\n  }\n}\nfunction serial(name) {\n  return new PgSerialBuilder(name ?? \"\");\n}\nexport {\n  PgSerial,\n  PgSerialBuilder,\n  serial\n};\n//# sourceMappingURL=serial.js.map", "import { entityKind } from \"./entity.js\";\nclass ColumnBuilder {\n  static [entityKind] = \"ColumnBuilder\";\n  config;\n  constructor(name, dataType, columnType) {\n    this.config = {\n      name,\n      keyAsName: name === \"\",\n      notNull: false,\n      default: void 0,\n      hasDefault: false,\n      primaryKey: false,\n      isUnique: false,\n      uniqueName: void 0,\n      uniqueType: void 0,\n      dataType,\n      columnType,\n      generated: void 0\n    };\n  }\n  /**\n   * Changes the data type of the column. Commonly used with `json` columns. Also, useful for branded types.\n   *\n   * @example\n   * ```ts\n   * const users = pgTable('users', {\n   * \tid: integer('id').$type<UserId>().primaryKey(),\n   * \tdetails: json('details').$type<UserDetails>().notNull(),\n   * });\n   * ```\n   */\n  $type() {\n    return this;\n  }\n  /**\n   * Adds a `not null` clause to the column definition.\n   *\n   * Affects the `select` model of the table - columns *without* `not null` will be nullable on select.\n   */\n  notNull() {\n    this.config.notNull = true;\n    return this;\n  }\n  /**\n   * Adds a `default <value>` clause to the column definition.\n   *\n   * Affects the `insert` model of the table - columns *with* `default` are optional on insert.\n   *\n   * If you need to set a dynamic default value, use {@link $defaultFn} instead.\n   */\n  default(value) {\n    this.config.default = value;\n    this.config.hasDefault = true;\n    return this;\n  }\n  /**\n   * Adds a dynamic default value to the column.\n   * The function will be called when the row is inserted, and the returned value will be used as the column value.\n   *\n   * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n   */\n  $defaultFn(fn) {\n    this.config.defaultFn = fn;\n    this.config.hasDefault = true;\n    return this;\n  }\n  /**\n   * Alias for {@link $defaultFn}.\n   */\n  $default = this.$defaultFn;\n  /**\n   * Adds a dynamic update value to the column.\n   * The function will be called when the row is updated, and the returned value will be used as the column value if none is provided.\n   * If no `default` (or `$defaultFn`) value is provided, the function will be called when the row is inserted as well, and the returned value will be used as the column value.\n   *\n   * **Note:** This value does not affect the `drizzle-kit` behavior, it is only used at runtime in `drizzle-orm`.\n   */\n  $onUpdateFn(fn) {\n    this.config.onUpdateFn = fn;\n    this.config.hasDefault = true;\n    return this;\n  }\n  /**\n   * Alias for {@link $onUpdateFn}.\n   */\n  $onUpdate = this.$onUpdateFn;\n  /**\n   * Adds a `primary key` clause to the column definition. This implicitly makes the column `not null`.\n   *\n   * In SQLite, `integer primary key` implicitly makes the column auto-incrementing.\n   */\n  primaryKey() {\n    this.config.primaryKey = true;\n    this.config.notNull = true;\n    return this;\n  }\n  /** @internal Sets the name of the column to the key within the table definition if a name was not given. */\n  setName(name) {\n    if (this.config.name !== \"\") return;\n    this.config.name = name;\n  }\n}\nexport {\n  ColumnBuilder\n};\n//# sourceMappingURL=column-builder.js.map", "import { entityKind } from \"../entity.js\";\nimport { TableName } from \"../table.utils.js\";\nclass ForeignKeyBuilder {\n  static [entityKind] = \"PgForeignKeyBuilder\";\n  /** @internal */\n  reference;\n  /** @internal */\n  _onUpdate = \"no action\";\n  /** @internal */\n  _onDelete = \"no action\";\n  constructor(config, actions) {\n    this.reference = () => {\n      const { name, columns, foreignColumns } = config();\n      return { name, columns, foreignTable: foreignColumns[0].table, foreignColumns };\n    };\n    if (actions) {\n      this._onUpdate = actions.onUpdate;\n      this._onDelete = actions.onDelete;\n    }\n  }\n  onUpdate(action) {\n    this._onUpdate = action === void 0 ? \"no action\" : action;\n    return this;\n  }\n  onDelete(action) {\n    this._onDelete = action === void 0 ? \"no action\" : action;\n    return this;\n  }\n  /** @internal */\n  build(table) {\n    return new ForeignKey(table, this);\n  }\n}\nclass ForeignKey {\n  constructor(table, builder) {\n    this.table = table;\n    this.reference = builder.reference;\n    this.onUpdate = builder._onUpdate;\n    this.onDelete = builder._onDelete;\n  }\n  static [entityKind] = \"PgForeignKey\";\n  reference;\n  onUpdate;\n  onDelete;\n  getName() {\n    const { name, columns, foreignColumns } = this.reference();\n    const columnNames = columns.map((column) => column.name);\n    const foreignColumnNames = foreignColumns.map((column) => column.name);\n    const chunks = [\n      this.table[TableName],\n      ...columnNames,\n      foreignColumns[0].table[TableName],\n      ...foreignColumnNames\n    ];\n    return name ?? `${chunks.join(\"_\")}_fk`;\n  }\n}\nfunction foreignKey(config) {\n  function mappedConfig() {\n    const { name, columns, foreignColumns } = config;\n    return {\n      name,\n      columns,\n      foreignColumns\n    };\n  }\n  return new ForeignKeyBuilder(mappedConfig);\n}\nexport {\n  ForeignKey,\n  ForeignKeyBuilder,\n  foreignKey\n};\n//# sourceMappingURL=foreign-keys.js.map", "import { entityKind } from \"../entity.js\";\nimport { TableName } from \"../table.utils.js\";\nfunction unique(name) {\n  return new UniqueOnConstraintBuilder(name);\n}\nfunction uniqueKeyName(table, columns) {\n  return `${table[TableName]}_${columns.join(\"_\")}_unique`;\n}\nclass UniqueConstraintBuilder {\n  constructor(columns, name) {\n    this.name = name;\n    this.columns = columns;\n  }\n  static [entityKind] = \"PgUniqueConstraintBuilder\";\n  /** @internal */\n  columns;\n  /** @internal */\n  nullsNotDistinctConfig = false;\n  nullsNotDistinct() {\n    this.nullsNotDistinctConfig = true;\n    return this;\n  }\n  /** @internal */\n  build(table) {\n    return new UniqueConstraint(table, this.columns, this.nullsNotDistinctConfig, this.name);\n  }\n}\nclass UniqueOnConstraintBuilder {\n  static [entityKind] = \"PgUniqueOnConstraintBuilder\";\n  /** @internal */\n  name;\n  constructor(name) {\n    this.name = name;\n  }\n  on(...columns) {\n    return new UniqueConstraintBuilder(columns, this.name);\n  }\n}\nclass UniqueConstraint {\n  constructor(table, columns, nullsNotDistinct, name) {\n    this.table = table;\n    this.columns = columns;\n    this.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n    this.nullsNotDistinct = nullsNotDistinct;\n  }\n  static [entityKind] = \"PgUniqueConstraint\";\n  columns;\n  name;\n  nullsNotDistinct = false;\n  getName() {\n    return this.name;\n  }\n}\nexport {\n  UniqueConstraint,\n  UniqueConstraintBuilder,\n  UniqueOnConstraintBuilder,\n  unique,\n  uniqueKeyName\n};\n//# sourceMappingURL=unique-constraint.js.map", "function parsePgArrayValue(arrayString, startFrom, inQuotes) {\n  for (let i = startFrom; i < arrayString.length; i++) {\n    const char = arrayString[i];\n    if (char === \"\\\\\") {\n      i++;\n      continue;\n    }\n    if (char === '\"') {\n      return [arrayString.slice(startFrom, i).replace(/\\\\/g, \"\"), i + 1];\n    }\n    if (inQuotes) {\n      continue;\n    }\n    if (char === \",\" || char === \"}\") {\n      return [arrayString.slice(startFrom, i).replace(/\\\\/g, \"\"), i];\n    }\n  }\n  return [arrayString.slice(startFrom).replace(/\\\\/g, \"\"), arrayString.length];\n}\nfunction parsePgNestedArray(arrayString, startFrom = 0) {\n  const result = [];\n  let i = startFrom;\n  let lastCharIsComma = false;\n  while (i < arrayString.length) {\n    const char = arrayString[i];\n    if (char === \",\") {\n      if (lastCharIsComma || i === startFrom) {\n        result.push(\"\");\n      }\n      lastCharIsComma = true;\n      i++;\n      continue;\n    }\n    lastCharIsComma = false;\n    if (char === \"\\\\\") {\n      i += 2;\n      continue;\n    }\n    if (char === '\"') {\n      const [value2, startFrom2] = parsePgArrayValue(arrayString, i + 1, true);\n      result.push(value2);\n      i = startFrom2;\n      continue;\n    }\n    if (char === \"}\") {\n      return [result, i + 1];\n    }\n    if (char === \"{\") {\n      const [value2, startFrom2] = parsePgNestedArray(arrayString, i + 1);\n      result.push(value2);\n      i = startFrom2;\n      continue;\n    }\n    const [value, newStartFrom] = parsePgArrayValue(arrayString, i, false);\n    result.push(value);\n    i = newStartFrom;\n  }\n  return [result, i];\n}\nfunction parsePgArray(arrayString) {\n  const [result] = parsePgNestedArray(arrayString, 1);\n  return result;\n}\nfunction makePgArray(array) {\n  return `{${array.map((item) => {\n    if (Array.isArray(item)) {\n      return makePgArray(item);\n    }\n    if (typeof item === \"string\") {\n      return `\"${item.replace(/\\\\/g, \"\\\\\\\\\").replace(/\"/g, '\\\\\"')}\"`;\n    }\n    return `${item}`;\n  }).join(\",\")}}`;\n}\nexport {\n  makePgArray,\n  parsePgArray,\n  parsePgNestedArray\n};\n//# sourceMappingURL=array.js.map", "import { ColumnBuilder } from \"../../column-builder.js\";\nimport { Column } from \"../../column.js\";\nimport { entityKind, is } from \"../../entity.js\";\nimport { ForeignKeyBuilder } from \"../foreign-keys.js\";\nimport { iife } from \"../../tracing-utils.js\";\nimport { uniqueKeyName } from \"../unique-constraint.js\";\nimport { makePgArray, parsePgArray } from \"../utils/array.js\";\nclass PgColumnBuilder extends ColumnBuilder {\n  foreignKeyConfigs = [];\n  static [entityKind] = \"PgColumnBuilder\";\n  array(size) {\n    return new PgArrayBuilder(this.config.name, this, size);\n  }\n  references(ref, actions = {}) {\n    this.foreignKeyConfigs.push({ ref, actions });\n    return this;\n  }\n  unique(name, config) {\n    this.config.isUnique = true;\n    this.config.uniqueName = name;\n    this.config.uniqueType = config?.nulls;\n    return this;\n  }\n  generatedAlwaysAs(as) {\n    this.config.generated = {\n      as,\n      type: \"always\",\n      mode: \"stored\"\n    };\n    return this;\n  }\n  /** @internal */\n  buildForeignKeys(column, table) {\n    return this.foreignKeyConfigs.map(({ ref, actions }) => {\n      return iife(\n        (ref2, actions2) => {\n          const builder = new ForeignKeyBuilder(() => {\n            const foreignColumn = ref2();\n            return { columns: [column], foreignColumns: [foreignColumn] };\n          });\n          if (actions2.onUpdate) {\n            builder.onUpdate(actions2.onUpdate);\n          }\n          if (actions2.onDelete) {\n            builder.onDelete(actions2.onDelete);\n          }\n          return builder.build(table);\n        },\n        ref,\n        actions\n      );\n    });\n  }\n  /** @internal */\n  buildExtraConfigColumn(table) {\n    return new ExtraConfigColumn(table, this.config);\n  }\n}\nclass PgColumn extends Column {\n  constructor(table, config) {\n    if (!config.uniqueName) {\n      config.uniqueName = uniqueKeyName(table, [config.name]);\n    }\n    super(table, config);\n    this.table = table;\n  }\n  static [entityKind] = \"PgColumn\";\n}\nclass ExtraConfigColumn extends PgColumn {\n  static [entityKind] = \"ExtraConfigColumn\";\n  getSQLType() {\n    return this.getSQLType();\n  }\n  indexConfig = {\n    order: this.config.order ?? \"asc\",\n    nulls: this.config.nulls ?? \"last\",\n    opClass: this.config.opClass\n  };\n  defaultConfig = {\n    order: \"asc\",\n    nulls: \"last\",\n    opClass: void 0\n  };\n  asc() {\n    this.indexConfig.order = \"asc\";\n    return this;\n  }\n  desc() {\n    this.indexConfig.order = \"desc\";\n    return this;\n  }\n  nullsFirst() {\n    this.indexConfig.nulls = \"first\";\n    return this;\n  }\n  nullsLast() {\n    this.indexConfig.nulls = \"last\";\n    return this;\n  }\n  /**\n   * ### PostgreSQL documentation quote\n   *\n   * > An operator class with optional parameters can be specified for each column of an index.\n   * The operator class identifies the operators to be used by the index for that column.\n   * For example, a B-tree index on four-byte integers would use the int4_ops class;\n   * this operator class includes comparison functions for four-byte integers.\n   * In practice the default operator class for the column's data type is usually sufficient.\n   * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n   * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n   * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n   * More information about operator classes check:\n   *\n   * ### Useful links\n   * https://www.postgresql.org/docs/current/sql-createindex.html\n   *\n   * https://www.postgresql.org/docs/current/indexes-opclass.html\n   *\n   * https://www.postgresql.org/docs/current/xindex.html\n   *\n   * ### Additional types\n   * If you have the `pg_vector` extension installed in your database, you can use the\n   * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n   *\n   * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n   *\n   * @param opClass\n   * @returns\n   */\n  op(opClass) {\n    this.indexConfig.opClass = opClass;\n    return this;\n  }\n}\nclass IndexedColumn {\n  static [entityKind] = \"IndexedColumn\";\n  constructor(name, keyAsName, type, indexConfig) {\n    this.name = name;\n    this.keyAsName = keyAsName;\n    this.type = type;\n    this.indexConfig = indexConfig;\n  }\n  name;\n  keyAsName;\n  type;\n  indexConfig;\n}\nclass PgArrayBuilder extends PgColumnBuilder {\n  static [entityKind] = \"PgArrayBuilder\";\n  constructor(name, baseBuilder, size) {\n    super(name, \"array\", \"PgArray\");\n    this.config.baseBuilder = baseBuilder;\n    this.config.size = size;\n  }\n  /** @internal */\n  build(table) {\n    const baseColumn = this.config.baseBuilder.build(table);\n    return new PgArray(\n      table,\n      this.config,\n      baseColumn\n    );\n  }\n}\nclass PgArray extends PgColumn {\n  constructor(table, config, baseColumn, range) {\n    super(table, config);\n    this.baseColumn = baseColumn;\n    this.range = range;\n    this.size = config.size;\n  }\n  size;\n  static [entityKind] = \"PgArray\";\n  getSQLType() {\n    return `${this.baseColumn.getSQLType()}[${typeof this.size === \"number\" ? this.size : \"\"}]`;\n  }\n  mapFromDriverValue(value) {\n    if (typeof value === \"string\") {\n      value = parsePgArray(value);\n    }\n    return value.map((v) => this.baseColumn.mapFromDriverValue(v));\n  }\n  mapToDriverValue(value, isNestedArray = false) {\n    const a = value.map(\n      (v) => v === null ? null : is(this.baseColumn, PgArray) ? this.baseColumn.mapToDriverValue(v, true) : this.baseColumn.mapToDriverValue(v)\n    );\n    if (isNestedArray) return a;\n    return makePgArray(a);\n  }\n}\nexport {\n  ExtraConfigColumn,\n  IndexedColumn,\n  PgArray,\n  PgArrayBuilder,\n  PgColumn,\n  PgColumnBuilder\n};\n//# sourceMappingURL=common.js.map"], "names": [], "sourceRoot": ""}