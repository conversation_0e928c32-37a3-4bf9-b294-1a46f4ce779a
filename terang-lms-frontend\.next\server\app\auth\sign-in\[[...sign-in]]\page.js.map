{"version": 3, "file": "../app/auth/sign-in/[[...sign-in]]/page.js", "mappings": "ubAAA,6GCAA,6GCGA,gBACA,MAAkB,eAAmB,IACrC,MACA,aAAY,QAAuB,EACnC,EAAkB,SAAa,yBAC/B,MAA2B,SAAG,mBAAqB,aAAiB,CACpE,SACA,2BAOA,GANA,YACA,MAAoB,YAAgB,IACpC,cACA,sBACA,kBAAyB,EAAa,2BAA2B,EAAkB,IACnF,EAEA,CACA,mBACA,SAqBA,OACA,eACa,eAAmB,KAEhC,mBACA,gBACA,OAAa,SAAa,CAC1B,MAAiB,WAAW,EAAU,IAAM,aAAmC,CAC/E,MAEA,CACA,EAEA,OADA,cACA,CAjCA,cACA,MAAwB,eAAmB,IAC3C,WACA,WACA,UACA,UAAc,mBAA8B,EAC5C,iBACA,EAAoB,SAAa,yBACjC,MAA6B,SAAG,mBAAqB,aAAiB,CACtE,SACA,2BAQA,GAPA,cACA,qBACA,EAAsB,YAAgB,IACtC,cACA,sBACA,kBAA2B,EAAa,2BAA2B,EAAkB,IACrF,EAEA,EAcA,SAEA,MACA,WACA,yBACA,WACA,iBACA,aACA,sBACA,CAAK,EACL,mBACA,4BAA2D,cAAqB,IAEhF,MADA,IACA,WAAkD,EAAU,GAC5D,OAAiB,UACjB,CAAO,GAAI,EACX,OAAa,SAAa,OAAU,WAAW,YAAoB,MAAgB,KACnF,CACA,EAEA,OADA,wBACA,CACA,EArBA,2LChD+C,MAAQ,cAAC,wBAAwB,wEAAwE,WAAW,4aAA4a,WAAW,wNAAwN,IAAyB,+GCU30B,IAAMA,EAAkB,KACtB,GAAM,CAACC,EAAcC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GAC3CC,EAAW,CAAC,CAChBC,KAAMC,EAAAA,CAAQA,CACdC,MAAO,0BACPC,YAAa,uGACbC,UAAW,qBACb,EAAG,CACDJ,KAAMK,CAAQA,CACdH,MAAO,sBACPC,YAAa,8FACbC,UAAW,4BACb,EAAG,CACDJ,KAAMM,EAAAA,CAASA,CACfJ,MAAO,qBACPC,YAAa,mGACbC,UAAW,sBACb,EAAE,CACFG,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR,IAAMC,EAAQC,YAAY,KACxBZ,EAAgBa,GAAQ,CAACA,GAAO,EAAKX,EAASY,MAAM,CACtD,EAAG,KACH,MAAO,IAAMC,cAAcJ,EAC7B,EAAG,CAACT,EAASY,MAAM,CAAC,EAOpB,IAAME,EAAiBd,CAAQ,CAACH,EAAa,CACvCkB,EAAOD,EAAeb,IAAI,CAChC,MAAO,WAACe,MAAAA,CAAIC,UAAU,4DAA4DC,wBAAsB,kBAAkBC,0BAAwB,6BAC9I,WAACH,MAAAA,CAAIC,UAAU,mDACb,UAACG,SAAAA,CAAOC,QAPI,CAOKC,IANrBxB,EAAgBa,GAAQ,CAACA,EAAO,EAAIX,EAASY,MAAAA,EAAUZ,EAASY,MAAM,CACxE,EAKkCK,UAAU,4EACpC,UAACM,EAAAA,CAAeA,CAAAA,CAACN,UAAU,qBAAqBO,sBAAoB,kBAAkBL,0BAAwB,uBAGhH,UAACH,MAAAA,CAAIC,UAAU,0BACZjB,EAASyB,GAAG,CAAC,CAACC,EAAGC,IAAU,UAACP,SAAAA,CAAmBC,QAAS,IAAMvB,EAAgB6B,GAAQV,UAAW,CAAC,uCAAuC,EAAEU,IAAU9B,EAAe,WAAa,eAAe,EAAxJ8B,MAG3C,UAACP,SAAAA,CAAOC,QAlBI,CAkBKO,IAjBrB9B,EAAgBa,GAAQ,CAACA,EAAO,GAAKX,EAASY,MAAM,CACtD,EAgBkCK,UAAU,4EACpC,UAACY,EAAAA,CAAgBA,CAAAA,CAACZ,UAAU,qBAAqBO,sBAAoB,mBAAmBL,0BAAwB,0BAIpH,WAACH,MAAAA,CAAIC,UAAU,kCACb,UAACD,MAAAA,CAAIC,UAAU,+BACb,UAACD,MAAAA,CAAIC,UAAU,6EACb,UAACF,EAAAA,CAAKE,UAAU,qBAAqBO,sBAAoB,OAAOL,0BAAwB,yBAI5F,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAACa,KAAAA,CAAGb,UAAU,wCAAgCH,EAAeX,KAAK,GAClE,UAAC4B,IAAAA,CAAEd,UAAU,iDAAyCH,EAAeV,WAAW,GAChF,UAACY,MAAAA,CAAIC,UAAU,2DACb,UAACe,OAAAA,CAAKf,UAAU,0CAAkCH,EAAeT,SAAS,aAKtF,EACe,SAAS4B,IACtB,GAAM,CAACC,EAAcC,EAAgB,CAAGpC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACqC,EAASC,EAAW,CAAGtC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACuC,EAAcC,EAAgB,CAAGxC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACyC,EAAOC,EAD2C,CAC/B1C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC7B,CAAC2C,EAAUC,EAAY,CAAG5C,CAAAA,EAAAA,EAAAA,QAAAA,CAFiE,CAExD,CACvC6C,MAAO,GACPC,SAAU,GACVC,YAAY,CACd,GACMC,EAAoB,IACxB,GAAM,MACJC,CAAI,CACJC,OAAK,MACLC,CAAI,SACJC,CAAO,CACR,CAAGC,EAAEC,MAAM,CACZV,EAAYhC,GAAS,EACnB,EADmB,CAChBA,CAAI,CACP,CAACqC,EAAK,CAAW,eAAaG,EAAUF,EAC1C,EACF,EACMK,EAAe,MAAOF,IAC1BA,EAAEG,cAAc,GAChBlB,GAAW,GACXI,EAAS,IACT,GAAI,CACF,IAAMe,EAAW,MAAMC,MAAM,kBAAmB,CAC9CC,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMC,KAAKC,SAAS,CAAC,CACnBlB,MAAOF,EAASE,KAAK,CACrBC,SAAUH,EAASG,QAAQ,EAE/B,GACMkB,EAAO,MAAMP,EAASQ,IAAI,GAChC,GAAI,CAACR,EAASS,EAAE,CACd,CADgB,KACV,MAAUF,EAAKvB,KAAK,EAAI,gBAIhC0B,EAAAA,EAAWA,CAACC,OAAO,CAACJ,EAAKK,IAAI,EAC7B7B,EAAgB,IAChB8B,EAAAA,CADuB,CAClBA,CAACC,OAAO,CAAC,kBAAmB,CAC/BlE,YAAa,CAAC,wBAAwB,EAAE2D,EAAKK,IAAI,CAACpB,IAAI,EAAE,CACxDuB,SAAU,GACZ,GAGAC,WAAW,KACT,IAAMC,EAAeC,CAAAA,EAAAA,EAAAA,EAAAA,CAAeA,CAACX,EAAKK,IAAI,EAC9CO,QAAQC,GAAG,CAAC,aAAcb,EAAKK,IAAI,CAACS,IAAI,CAAE,kBAAmBJ,GAC7DK,OAAOC,QAAQ,CAACC,IAAI,CAAGP,CACzB,EAAG,KACL,CAAE,MAAOQ,EAAU,CACjBxC,EAASwC,EAAIC,OAAO,EAAI,+BAC1B,QAAU,CACR7C,GAAW,EACb,CACF,EACA,MAAO,UAACrB,MAAAA,CAAIC,UAAU,sEAAsEC,wBAAsB,iBAAiBC,0BAAwB,4BACvJ,WAACH,MAAAA,CAAIC,UAAU,0BAEb,WAACD,MAAAA,CAAIC,UAAU,sGAEb,UAACD,MAAAA,CAAIC,UAAU,uCACb,WAACkE,MAAAA,CAAIC,MAAM,OAAOC,OAAO,OAAOC,MAAM,6BAA6B9D,sBAAoB,MAAML,0BAAwB,6BACnH,UAACoE,OAAAA,CAAK/D,sBAAoB,OAAOL,0BAAwB,4BACvD,UAACqE,UAAAA,CAAQC,GAAG,OAAOL,MAAM,KAAKC,OAAO,KAAKK,aAAa,iBAAiBlE,sBAAoB,UAAUL,0BAAwB,4BAC5H,UAACwE,OAAAA,CAAKC,EAAE,oBAAoBC,KAAK,OAAOC,OAAO,eAAeC,YAAY,IAAIvE,sBAAoB,OAAOL,0BAAwB,yBAGrI,UAAC6E,OAAAA,CAAKZ,MAAM,OAAOC,OAAO,OAAOQ,KAAK,aAAarE,sBAAoB,OAAOL,0BAAwB,0BAI1G,UAACH,MAAAA,CAAIC,UAAU,mEAEb,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACD,MAAAA,CAAIC,UAAU,+BACb,UAACgF,EAAAA,OAAKA,CAAAA,CAACC,IAAI,6BAA6BC,IAAI,WAAWf,MAAO,IAAKC,OAAQ,IAAKpE,UAAU,iBAAiBO,sBAAoB,QAAQL,0BAAwB,uBAGjK,WAACH,MAAAA,CAAIC,UAAU,4BACb,WAACmF,KAAAA,CAAGnF,UAAU,6CAAmC,oBAC9B,UAACoF,KAAAA,CAAAA,GAAK,sBACJ,UAACA,KAAAA,CAAAA,GAAK,iBAG3B,UAACtE,IAAAA,CAAEd,UAAU,iDAAwC,sIAOvD,UAACD,MAAAA,CAAIC,UAAU,yBACb,UAACrB,EAAAA,CAAgB4B,sBAAoB,kBAAkBL,0BAAwB,+BAQvF,UAACH,MAAAA,CAAIC,UAAU,kCACb,UAACD,MAAAA,CAAIC,UAAU,kEACb,WAACD,MAAAA,CAAIC,UAAU,wDAEf,UAACD,MAAAA,CAAIC,UAAU,sCACb,UAACD,MAAAA,CAAIC,UAAU,oCACb,UAACgF,EAAAA,OAAKA,CAAAA,CAACC,IAAI,uBAAuBC,IAAI,WAAWf,MAAO,IAAKC,OAAQ,IAAKpE,UAAU,iBAAiBO,sBAAoB,QAAQL,0BAAwB,yBAK7J,WAACmF,EAAAA,EAAIA,CAAAA,CAACrF,UAAU,qBAAqBO,sBAAoB,OAAOL,0BAAwB,6BACtF,WAACoF,EAAAA,EAAUA,CAAAA,CAACtF,UAAU,6BAA6BO,sBAAoB,aAAaL,0BAAwB,6BAC1G,UAACqF,EAAAA,EAASA,CAAAA,CAACvF,UAAU,mCAAmCO,sBAAoB,YAAYL,0BAAwB,4BAAmB,kBACnI,UAACsF,EAAAA,EAAeA,CAAAA,CAACxF,UAAU,gBAAgBO,sBAAoB,kBAAkBL,0BAAwB,4BAAmB,+DAK9H,WAACuF,EAAAA,EAAWA,CAAAA,CAAClF,sBAAoB,cAAcL,0BAAwB,6BACpEqB,GAAS,UAACxB,MAAAA,CAAIC,UAAU,+DACrB,UAACc,IAAAA,CAAEd,UAAU,gCAAwBuB,MAGzC,WAACmE,OAAAA,CAAKC,SAAUtD,EAAcrC,UAAU,sBAEtC,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAAC4F,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,QAAQ7F,UAAU,oCAAoCO,sBAAoB,QAAQL,0BAAwB,4BAAmB,UAG5I,WAACH,MAAAA,CAAIC,UAAU,qBACb,UAAC8F,EAAAA,CAAQA,CAAAA,CAAC9F,UAAU,2EAA2EO,sBAAoB,WAAWL,0BAAwB,qBACtJ,UAAC6F,EAAAA,CAAKA,CAAAA,CAACvB,GAAG,QAAQzC,KAAK,QAAQE,KAAK,QAAQ+D,YAAY,iBAAiBhE,MAAOP,EAASE,KAAK,CAAEsE,SAAUnE,EAAmB9B,UAAU,+FAA+FkG,QAAQ,IAAC3F,sBAAoB,QAAQL,0BAAwB,2BAKvS,WAACH,MAAAA,CAAIC,UAAU,sBACb,UAAC4F,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,WAAW7F,UAAU,oCAAoCO,sBAAoB,QAAQL,0BAAwB,4BAAmB,aAG/I,WAACH,MAAAA,CAAIC,UAAU,qBACb,UAACmG,EAAAA,CAAQA,CAAAA,CAACnG,UAAU,2EAA2EO,sBAAoB,WAAWL,0BAAwB,qBACtJ,UAAC6F,EAAAA,CAAKA,CAAAA,CAACvB,GAAG,WAAWzC,KAAK,WAAWE,KAAMhB,EAAe,OAAS,WAAY+E,YAAY,oBAAoBhE,MAAOP,EAASG,QAAQ,CAAEqE,SAAUnE,EAAmB9B,UAAU,qGAAqGkG,QAAQ,IAAC3F,sBAAoB,QAAQL,0BAAwB,qBAClV,UAACC,SAAAA,CAAO8B,KAAK,SAAS7B,QAAS,IAAMc,EAAgB,CAACD,GAAejB,UAAU,iGAC5EiB,EAAe,UAACmF,EAAAA,CAAUA,CAAAA,CAACpG,UAAU,YAAe,UAACqG,EAAAA,CAAOA,CAAAA,CAACrG,UAAU,oBAM9E,WAACD,MAAAA,CAAIC,UAAU,mDACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,UAACsG,EAAAA,CAAQA,CAAAA,CAAC9B,GAAG,aAAazC,KAAK,aAAaG,QAAST,EAASI,UAAU,CAAE0E,gBAAiBrE,GAAWR,EAAYhC,GAAS,EACzH,EADyH,CACtHA,CAAI,CACPmC,WAAYK,EACd,GAAKlC,UAAU,gGAAgGO,sBAAoB,WAAWL,0BAAwB,qBACtK,UAAC0F,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,aAAa7F,UAAU,uCAAuCO,sBAAoB,QAAQL,0BAAwB,4BAAmB,kBAItJ,UAACsG,IAAIA,CAACzC,KAAK,KAANyC,mBAA8BxG,UAAU,kFAAkFO,sBAAoB,OAAOL,0BAAwB,4BAAmB,sBAMvM,UAACuG,EAAAA,CAAMA,CAAAA,CAACxE,KAAK,SAASyE,QAAQ,MAAM1G,UAAU,yCAAyC2G,SAAUxF,GAAWE,EAC5Gd,sBAAoB,SAASL,0BAAwB,4BAClDiB,EAAU,eAAiBE,EAAe,WAACN,OAAAA,CAAKf,UAAU,6CACvD,UAAC4G,EAAAA,CAAYA,CAAAA,CAAC5G,UAAU,iBAAiB,gBACjC,0BAKhB,WAACD,MAAAA,CAAIC,UAAU,0BACb,UAACD,MAAAA,CAAIC,UAAU,8CACb,UAACe,OAAAA,CAAKf,UAAU,sCAElB,UAACD,MAAAA,CAAIC,UAAU,0DACb,UAACe,OAAAA,CAAKf,UAAU,uCAA8B,cAKlD,UAACD,MAAAA,CAAIC,UAAU,qBACb,WAACyG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU1G,UAAU,2DAA2DO,sBAAoB,SAASL,0BAAwB,6BAClJ,WAACgE,MAAAA,CAAIlE,UAAU,eAAe6G,QAAQ,YAAYtG,sBAAoB,MAAML,0BAAwB,6BAClG,UAACwE,OAAAA,CAAKE,KAAK,UAAUD,EAAE,0HAA0HpE,sBAAoB,OAAOL,0BAAwB,qBACpM,UAACwE,OAAAA,CAAKE,KAAK,UAAUD,EAAE,wIAAwIpE,sBAAoB,OAAOL,0BAAwB,qBAClN,UAACwE,OAAAA,CAAKE,KAAK,UAAUD,EAAE,gIAAgIpE,sBAAoB,OAAOL,0BAAwB,qBAC1M,UAACwE,OAAAA,CAAKE,KAAK,UAAUD,EAAE,sIAAsIpE,sBAAoB,OAAOL,0BAAwB,wBAC5M,2BAMV,WAACY,IAAAA,CAAEd,UAAU,mDAAyC,oBAClC,IAClB,UAACwG,IAAIA,CAACzC,KAAK,KAANyC,WAAsBxG,UAAU,0EAA0EO,sBAAoB,OAAOL,0BAAwB,4BAAmB,6BAQ3L,WAACH,MAAAA,CAAIC,UAAU,wDACb,WAACc,IAAAA,WAAE,gCAC6B,IAC9B,UAAC0F,IAAIA,CAACzC,KAAK,KAANyC,IAAexG,UAAU,gCAAgCO,sBAAoB,OAAOL,0BAAwB,4BAAmB,uBAE5H,IAAI,MACR,IACJ,UAACsG,IAAIA,CAACzC,KAAK,KAANyC,MAAiBxG,UAAU,gCAAgCO,sBAAoB,OAAOL,0BAAwB,4BAAmB,yBAIxI,WAACH,MAAAA,CAAIC,UAAU,qEACb,UAACe,OAAAA,UAAK,mCACN,UAAC+F,MAAAA,CAAI7B,IAAI,uDAAuDC,IAAI,YAAYlF,UAAU,sCAQ1G,0BC7TA,8FCAA,uCAA2M,+BCC3M,yCAAuE,MAAkC,EAAI,EAC7G,mBAEA,GADA,OACA,4BACA,aAEA,CACA,0CCRA,mECAA,0GCAA,qDCAA,+CCAA,4CCAA,uCAA2M,wBCA3M,iDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,8sBCAA,qVCcA,OACA,UACA,GACA,CACA,UACA,OACA,CACA,UACA,UACA,CACA,UACA,iBACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAA8K,CAuBlM,6IAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CAEA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EAEA,CAAO,CACP,CACA,QAzDA,IAAsB,sCAAiJ,CAyDvK,gHACA,gBAzDA,IAAsB,uCAAuJ,CAyD7K,sHACA,aAzDA,IAAsB,uCAAoJ,CAyD1K,mHACA,WAzDA,IAAsB,4CAAgF,CAyDtG,+CACA,cAzDA,IAAsB,4CAAmF,CAyDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,gJAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,yCACA,wCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCpGD,oDCAA,mICUM,EAAoC,IACxC,GAAM,SAAE,EAAS,WAAS,CAAI,EACxB,EAAW,SAmBV,CAAY,EAAkB,QACrC,GAAM,CAAC,EAAM,EAAO,CAAU,IAAV,MAAU,CAAsB,EAC9C,EAAkB,SAAmC,IAAI,EACzD,EAAuB,SAAO,GAC9B,EAA6B,EADQ,MACR,CAAe,MAAM,EAElD,CAAC,EAAO,EAAI,GAAI,CADD,EAAU,UAAY,GACL,SCxBtC,EDwBoD,CAClD,MCxBF,EDwBW,CACP,QAAS,YACT,cAAe,kBACjB,EACA,iBAAkB,CAChB,MAAO,UACP,cAAe,WACjB,EACA,UAAW,CACT,MAAO,SACT,CACF,CAAC,CClCY,aAAW,CAAC,EAAwB,IAExC,CADY,CAAQ,EAAK,CAAU,EAAK,EAC3B,CAD2B,CAE9C,IDsIH,OArGM,CCjCS,CDiCT,UAAU,KACd,IAAM,EAAuB,EAAiB,EAAU,OAAO,EAC/D,EAAqB,QAAoB,YAAV,EAAsB,EAAuB,MAC9E,EAAG,CAAC,EAAM,EAEV,CAFS,EAET,IAAe,CAAC,KACd,IAAM,EAAS,EAAU,QACnB,EAAa,EAAe,QAGlC,GAF0B,CAEtB,GAFqC,EAElB,CACrB,IAAM,EAAoB,EAAqB,QACzC,EAAuB,EAAiB,GAE1C,EACF,CAHkD,CAG7C,KADM,EACC,EACsB,SAAzB,GAAmC,GAAQ,UAAY,OAGhE,CAHwE,CAGnE,SAAS,EAUV,GAFgB,IAAsB,EAGxC,EAAK,GADW,YACI,CADS,CAG7B,EAAK,SAAS,EAIlB,EAAe,QAAU,CAC3B,CACF,EAAG,CAAC,EAAS,EAAK,EAAD,CAEjB,MAAe,CAAC,KACd,GAAI,EAAM,CAER,IADI,EACE,EAAc,EAAK,cAAc,aAAe,OAMhD,EAAqB,IAEzB,IAAM,EADuB,EAAiB,EAAU,OAAO,EACf,SAAS,EAAM,aAAa,EAC5E,GAAI,EAAM,SAAW,GAAQ,IAW3B,EAAK,cAX0C,CAW3B,EAChB,CAAC,EAAe,SAAS,CAC3B,IAAM,EAAkB,EAAK,MAAM,kBACnC,EAAK,MAAM,kBAAoB,WAK/B,EAAY,EAAY,WAAW,KACI,YAAY,CAA7C,EAAK,MAAM,oBACb,EAAK,MAAM,kBAAoB,EAEnC,CAAC,CACH,CAEJ,EACM,EAAuB,IACvB,EAAM,SAAW,IAEnB,EAAqB,QAAU,EAAiB,EAAU,QAAO,CAErE,EAIA,OAHA,EAAK,iBAAiB,iBAAkB,GACxC,EAAK,eADuD,CACvD,CAAiB,kBAAmB,GACzC,EAAK,aADsD,GACtD,CAAiB,eAAgB,GAC/B,KACL,EAAY,QAF0C,IAE1C,CAAa,GACzB,EAAK,IAD6B,eAC7B,CAAoB,iBAAkB,GAC3C,EAAK,eAD0D,IAC1D,CAAoB,kBAAmB,GAC5C,EAAK,aADyD,MACzD,CAAoB,eAAgB,EAC3C,CACF,CAGE,EAAK,IAHA,QAFwD,GAKzC,CAExB,EAAG,CAAC,EAAM,EAAK,EAAD,CAGZ,UAAW,CAAC,UAAW,kBAAkB,EAAE,SAAS,GACpD,EADyD,EAC9C,cAAY,IACrB,EAAU,QAAU+G,EAAO,iBAAiBA,GAAQ,EAAJ,GAChD,EAAQA,EACV,EAAG,CADW,CACT,CACP,CACF,EAnJ+B,GAEvB,EACJ,EAHkC,iBAG3B,EACH,EAAS,CAAE,QAAS,EAAS,SAAU,CAAC,EAClC,WAAS,KAAK,GAGpB,EAAM,GAHsB,EAGtB,EAAe,CAAC,EAAS,IAAK,SAwJnC,CAAc,EAA2D,IAE5E,EAAS,OAAO,yBAAyB,EAAQ,MAAO,KAAK,GAAG,IAChE,EAAU,GAAU,mBAAoB,GAAU,EAAO,sBAC7D,EACU,EAAgB,IAK1B,CANa,EAMH,CADV,EAAS,OAAO,yBAAyB,EAAS,KAAK,GAAG,MACtC,mBAAoB,GAAU,EAAO,gBAEhD,EAAQ,MAAM,IAIhB,EAAQ,MAAM,KAAQ,EAAgB,KAxKW,IAExD,CAF6D,CAAC,IAEvD,YADY,OAAO,GACL,EAAS,UAAkB,eAAa,EAAO,KAAE,CAAI,CAAC,EAAI,IACjF,EA4IA,SAAS,EAAiB,GACxB,OAAO,GAAQ,eAAiB,MAClC,CA5IA,EAAS,YAAc,kCEzBvB,qDCAA,kECAA,yDCAA,uDCAA,oICM+C,MAAQ,cAAC,4BAA4B,+CAA+C,WAAW,0oBAA0oB,IAAyB,wBCNjzB,qDCAA,4DCAA,wDCAA,oHCGA,EAAyB,iBAAK,EAAL,sBAAK,oBAA8C,GAAe,CAC3F,YACA,OACA,cACA,gBACA,CAAG,CACH,SACC,EACD,oBAmCA,CACA,cACA,WACC,EACD,SAA4B,UAAc,IAC1C,EAAuB,QAAY,IACnC,EAAsB,QAAY,IAUlC,OATA,OACA,WACA,CAAG,MACD,WAAe,MACjB,gBACA,eACA,YAEA,CAAG,QACH,OACA,EApDA,CACA,cACA,UACA,CAAG,EACH,aACA,OACU,EACV,MAA4B,QAAY,aACpC,WAAe,MACnB,gBACA,UAEA,oCACA,aACA,GAAa,GAAQ,mBAAmB,EAHxC,4BAGwC,CAAM,KAAK,EAAG,4KAEtD,CACA,WACA,CAAK,OACL,CAcA,SAbmB,aAAiB,CACpC,IACA,MACA,kBA+BA,OA/BA,QACA,QACA,cAEA,EAAQ,IACR,IAEA,CAAK,CACL,WAEA,CA2BA,6FCpEO,IAAM9D,EAAc,CACzBC,QAAS,IAIT,EAEA8D,QAAS,IAKA,KAGTC,WAAY,KAIZ,EAEAC,gBAAiB,IACkB,OAA1BjE,EAAY+D,OAAO,GAG5BG,QAAS,IACP,IAAMhE,EAAOF,EAAY+D,OAAO,GAChC,OAAO7D,GAAMS,OAASA,CACxB,EAEAwD,aAAc,IACLnE,EAAYkE,OAAO,CAAC,eAG7BE,UAAW,IACFpE,EAAYkE,OAAO,CAAC,WAG7BG,UAAW,IACFrE,EAAYkE,OAAO,CAAC,UAE/B,EAAE,EAG6B,IAC7B,OAAQhE,EAAKS,IAAI,EACf,IAAK,cACH,MAAO,kBACT,KAAK,UACH,MAAO,oBACT,KAAK,UACH,MAAO,UACT,SACE,MAAO,YACX,CACF,EAAE,EAGuB,KACvB,IAAMT,EAAOF,EAAY+D,OAAO,UAChC,GAKS,CALL,EAAO,CAQb,EAGaO,EAAc,IACzB,IAAMpE,EAAOqE,WACb,GAEIrE,CAFA,CAEKS,CAFE,GAEE,GAAK6D,EAOXtE,EATW,IAUpB,EAAE,IARgC,oBC9ElC,sDCAA,6FCEA,cACA,MAAc,QAAY,QAAG,aAAwB,EACrD,OAAS,SAAa,MACtB,sBACA,mCACA,mBAEA,oBACG,IACH,0BCXA,iDCAA,kFCmBM,MAAc,cAAiB,eAhBD,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,MAAQ,EAAE,EAAG,CAAiB,mBAAK,SAAU,EAChD,0BCNA,2DCAA,iDCAA,yDCAA,sEEmBI,sBAAsB,sJDjBbuE,EAAqB,CAChCxI,KADWwI,CACJ,kBACPvI,WAAAA,CAAa,gFACf,ECGM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EATe,IASc,KAAK,CAAC,SDlBbwI,CCkBA,CDjBtB,EADsBA,ECkB4B,CDlB5BA,CACfC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAAC5G,EAAAA,OAAAA,CAAAA,CAAeT,qBAAAA,CAAoB,iBAAiBN,uBAAAA,CAAsB,OAAOC,yBAAAA,CAAwB,YACnH,ECgBsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACpC,KAAM,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,8BAA8B,CAC9C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,EAOF,KAhBkB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,0CC9E9B,uBAA8C,iBAAqB,MACnE,2GCqBA,EAnBA,CACA,IACA,SACA,MACA,OACA,KACA,KACA,MACA,QACA,QACA,KACA,MACA,KACA,IACA,SACA,OACA,MACA,KACA,CACA,eACA,MAAe,QAAU,cAAc,EAAK,GAC5C,EAAe,YAAgB,SAC/B,YAAY,QAA6B,EAKzC,MAHA,4BACA,oCAE2B,SAAG,CAJ9B,MAI8B,CAAS,WAAsC,CAC7E,CAAG,EAEH,OADA,2BAAkC,EAAK,EACvC,CAAW,WACX,CAAC,GAAI,EACL,gBACA,GAAc,WAAkB,wBAChC,0BCvCA,qGCGA,cACA,SAA0B,UAAc,SA+BxC,MA9BE,OAAe,MACjB,MACA,GAAgB,0CAA0D,EAC1E,iCAQA,EACA,EARA,sBAGA,UAFA,OAKA,WAGA,wBACA,sBACA,0BACA,eACA,eACU,IACV,gBACA,iBAEA,SAAkB,WAAe,CACjC,CAAO,EAEP,OADA,aAAwC,iBAAmB,EAC3D,kBACA,CACA,CADM,CACN,OAEA,CAAG,MACH,CACA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-context/dist/index.mjs", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/task_01_icon.js", "webpack://terang-lms-ui/./src/features/auth/components/sign-in-view.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?22bc", "webpack://terang-lms-ui/./node_modules/@radix-ui/primitive/dist/index.mjs", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/?476d", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/?7c1f", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/../src/presence.tsx", "webpack://terang-lms-ui/../src/use-state-machine.tsx", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/book_open_01_icon.js", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "webpack://terang-lms-ui/./src/lib/auth.ts", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/../../../src/icons/circle-check.ts", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/src/app/auth/sign-in/[[...sign-in]]/page.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-use-size/dist/index.mjs"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ jsx(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport {\n  createContext2 as createContext,\n  createContextScope\n};\n//# sourceMappingURL=index.mjs.map\n", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const C=r(\"Task01Icon\",[[\"path\",{d:\"M7.99805 16H11.998M7.99805 11H15.998\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M7.5 3.5C5.9442 3.54667 5.01661 3.71984 4.37477 4.36227C3.49609 5.24177 3.49609 6.6573 3.49609 9.48836L3.49609 15.9944C3.49609 18.8255 3.49609 20.241 4.37477 21.1205C5.25345 22 6.66767 22 9.49609 22L14.4961 22C17.3245 22 18.7387 22 19.6174 21.1205C20.4961 20.241 20.4961 18.8255 20.4961 15.9944V9.48836C20.4961 6.6573 20.4961 5.24177 19.6174 4.36228C18.9756 3.71984 18.048 3.54667 16.4922 3.5\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M7.49609 3.75C7.49609 2.7835 8.2796 2 9.24609 2H14.7461C15.7126 2 16.4961 2.7835 16.4961 3.75C16.4961 4.7165 15.7126 5.5 14.7461 5.5H9.24609C8.2796 5.5 7.49609 4.7165 7.49609 3.75Z\",stroke:\"currentColor\",key:\"k2\"}]]);export{C as default};\n", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { toast } from 'sonner';\nimport { Button } from '@/components/ui/button';\nimport { authStorage, getRedirectPath } from '@/lib/auth';\nimport { Input } from '@/components/ui/input';\nimport { CheckCircle2 } from 'lucide-react'; // Import CheckCircle2 icon\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { ViewIcon as EyeIcon, ViewOffIcon as EyeOffIcon, Building02Icon as BuildingIcon, BookOpen01Icon as BookIcon, UserIcon, Mail01Icon as MailIcon, LockIcon, ArrowLeft02Icon as ChevronLeftIcon, ArrowRight02Icon as ChevronRightIcon, Award01Icon as AwardIcon, Video01Icon as VideoIcon, Task01Icon as TaskIcon } from 'hugeicons-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\n// Feature Carousel Component\nconst FeatureCarousel = () => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const features = [{\n    icon: BookIcon,\n    title: 'Pembelajaran Interaktif',\n    description: 'Akses kursus video berkualitas tinggi dengan materi yang disusun oleh para ahli arsitektur Indonesia',\n    highlight: '50+ Kursus Tersedia'\n  }, {\n    icon: TaskIcon,\n    title: 'Ujian Bersertifikat',\n    description: 'Uji kompetensi Anda dengan sistem ujian online yang ketat dan dapatkan sertifikat resmi IAI',\n    highlight: 'Sertifikat Diakui Nasional'\n  }, {\n    icon: AwardIcon,\n    title: 'Pengembangan Karir',\n    description: 'Tingkatkan kredibilitas profesional dengan sertifikasi yang diakui industri arsitektur Indonesia',\n    highlight: '95% Peserta Berhasil'\n  }];\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % features.length);\n    }, 4000);\n    return () => clearInterval(timer);\n  }, [features.length]);\n  const nextSlide = () => {\n    setCurrentSlide(prev => (prev + 1) % features.length);\n  };\n  const prevSlide = () => {\n    setCurrentSlide(prev => (prev - 1 + features.length) % features.length);\n  };\n  const currentFeature = features[currentSlide];\n  const Icon = currentFeature.icon;\n  return <div className='bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]' data-sentry-component=\"FeatureCarousel\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n      <div className='flex items-center justify-between mb-4'>\r\n        <button onClick={prevSlide} className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'>\r\n          <ChevronLeftIcon className='w-4 h-4 text-white' data-sentry-element=\"ChevronLeftIcon\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n        </button>\r\n        \r\n        <div className='flex space-x-2'>\r\n          {features.map((_, index) => <button key={index} onClick={() => setCurrentSlide(index)} className={`w-2 h-2 rounded-full transition-colors ${index === currentSlide ? 'bg-white' : 'bg-white/40'}`} />)}\r\n        </div>\r\n        \r\n        <button onClick={nextSlide} className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'>\r\n          <ChevronRightIcon className='w-4 h-4 text-white' data-sentry-element=\"ChevronRightIcon\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n        </button>\r\n      </div>\r\n\r\n      <div className='text-center space-y-4'>\r\n        <div className='flex justify-center'>\r\n          <div className='w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center'>\r\n            <Icon className='w-8 h-8 text-white' data-sentry-element=\"Icon\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n          </div>\r\n        </div>\r\n        \r\n        <div className='space-y-2'>\r\n          <h3 className='text-xl font-bold text-white'>{currentFeature.title}</h3>\r\n          <p className='text-white/90 text-sm leading-relaxed'>{currentFeature.description}</p>\r\n          <div className='inline-block bg-white/20 px-3 py-1 rounded-full'>\r\n            <span className='text-xs font-medium text-white'>{currentFeature.highlight}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>;\n};\nexport default function SignInViewPage() {\n  const [showPassword, setShowPassword] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [loginSuccess, setLoginSuccess] = useState(false); // New state for login success animation\n  const [error, setError] = useState('');\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    rememberMe: false\n  });\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email: formData.email,\n          password: formData.password\n        })\n      });\n      const data = await response.json();\n      if (!response.ok) {\n        throw new Error(data.error || 'Login failed');\n      }\n\n      // Store user data using authStorage for consistency\n      authStorage.setUser(data.user);\n      setLoginSuccess(true); // Set success state to true\n      toast.success('Login berhasil!', {\n        description: `Selamat datang kembali, ${data.user.name}`,\n        duration: 3000\n      });\n\n      // Redirect to dashboard or home page based on user role after a short delay\n      setTimeout(() => {\n        const redirectPath = getRedirectPath(data.user);\n        console.log('User role:', data.user.role, 'Redirecting to:', redirectPath);\n        window.location.href = redirectPath;\n      }, 1500);\n    } catch (err: any) {\n      setError(err.message || 'Terjadi kesalahan saat login');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return <div className='h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden' data-sentry-component=\"SignInViewPage\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n      <div className='flex h-screen'>\r\n        {/* Left Side - Branding and Information */}\r\n        <div className='hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white'>\r\n          {/* Background Pattern */}\r\n          <div className='absolute inset-0 opacity-10'>\r\n            <svg width=\"100%\" height=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" data-sentry-element=\"svg\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n              <defs data-sentry-element=\"defs\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                <pattern id=\"grid\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\" data-sentry-element=\"pattern\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                  <path d=\"M 60 0 L 0 0 0 60\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"1\" data-sentry-element=\"path\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                </pattern>\r\n              </defs>\r\n              <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" data-sentry-element=\"rect\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n            </svg>\r\n          </div>\r\n\r\n          <div className='relative z-10 flex flex-col justify-between p-12 w-full'>\r\n            {/* Logo and Branding */}\r\n            <div className='space-y-4'>\r\n              <div className='flex justify-center'>\r\n                <Image src='/assets/logo-iai-putih.png' alt='IAI Logo' width={200} height={200} className='object-contain' data-sentry-element=\"Image\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n              </div>\r\n              \r\n              <div className='space-y-6 mt-12'>\r\n                <h2 className='text-3xl font-bold leading-tight'>\r\n                  Selamat Datang di<br />\r\n                  Sistem Pembelajaran<br />\r\n                  Profesional\r\n                </h2>\r\n                <p className='text-lg text-white/90 leading-relaxed'>\r\n                  Platform pembelajaran online khusus untuk para arsitek profesional. \r\n                  Tingkatkan keahlian Anda dengan kursus berkualitas tinggi.\r\n                </p>\r\n              </div>\r\n\r\n              {/* Features Carousel */}\r\n              <div className='relative mt-8'>\r\n                <FeatureCarousel data-sentry-element=\"FeatureCarousel\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n              </div>\r\n            </div>\r\n\r\n          </div>\r\n        </div>\r\n\r\n        {/* Right Side - Login Form */}\r\n        <div className='flex-1 overflow-y-auto'>\r\n          <div className='flex min-h-full items-center justify-center p-4 sm:p-8'>\r\n            <div className='w-full max-w-md space-y-4 sm:space-y-6 py-4'>\r\n            {/* Mobile Logo */}\r\n            <div className='lg:hidden text-center mb-8'>\r\n              <div className='flex justify-center mb-4'>\r\n                <Image src='/assets/logo-iai.png' alt='IAI Logo' width={200} height={200} className='object-contain' data-sentry-element=\"Image\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Login Card */}\r\n            <Card className='shadow-xl border-0' data-sentry-element=\"Card\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n              <CardHeader className='text-center space-y-1 pb-6' data-sentry-element=\"CardHeader\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                <CardTitle className='text-2xl font-bold text-gray-900' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"sign-in-view.tsx\">Masuk ke Akun</CardTitle>\r\n                <CardDescription className='text-gray-600' data-sentry-element=\"CardDescription\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                  Masukkan email dan password untuk mengakses kursus Anda\r\n                </CardDescription>\r\n              </CardHeader>\r\n              \r\n              <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                {error && <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>\r\n                    <p className='text-sm text-red-600'>{error}</p>\r\n                  </div>}\r\n\r\n                <form onSubmit={handleSubmit} className='space-y-4'>\r\n                  {/* Email Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='email' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                      Email\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <MailIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' data-sentry-element=\"MailIcon\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <Input id='email' name='email' type='email' placeholder='<EMAIL>' value={formData.email} onChange={handleInputChange} className='pl-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Password Field */}\r\n                  <div className='space-y-2'>\r\n                    <Label htmlFor='password' className='text-sm font-medium text-gray-700' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                      Password\r\n                    </Label>\r\n                    <div className='relative'>\r\n                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' data-sentry-element=\"LockIcon\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <Input id='password' name='password' type={showPassword ? 'text' : 'password'} placeholder='Masukkan password' value={formData.password} onChange={handleInputChange} className='pl-10 pr-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]' required data-sentry-element=\"Input\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <button type='button' onClick={() => setShowPassword(!showPassword)} className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'>\r\n                        {showPassword ? <EyeOffIcon className='w-4 h-4' /> : <EyeIcon className='w-4 h-4' />}\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Remember Me and Forgot Password */}\r\n                  <div className='flex items-center justify-between pt-2'>\r\n                    <div className='flex items-center space-x-2'>\r\n                      <Checkbox id='rememberMe' name='rememberMe' checked={formData.rememberMe} onCheckedChange={checked => setFormData(prev => ({\n                        ...prev,\n                        rememberMe: checked as boolean\n                      }))} className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)]' data-sentry-element=\"Checkbox\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <Label htmlFor='rememberMe' className='text-sm text-gray-600 cursor-pointer' data-sentry-element=\"Label\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                        Ingat saya\r\n                      </Label>\r\n                    </div>\r\n                    <Link href='/auth/forgot-password' className='text-sm text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                      Lupa password?\r\n                    </Link>\r\n                  </div>\r\n\r\n                  {/* Login Button */}\r\n                  <Button type='submit' variant='iai' className='w-full h-12 text-base font-medium mt-6' disabled={loading || loginSuccess} // Disable button during loading or success animation\n                  data-sentry-element=\"Button\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                    {loading ? 'Memproses...' : loginSuccess ? <span className=\"flex items-center justify-center\">\r\n                        <CheckCircle2 className=\"mr-2 h-5 w-5\" /> Berhasil!\r\n                      </span> : 'Masuk ke Dashboard'}\r\n                  </Button>\r\n                </form>\r\n\r\n                {/* Divider */}\r\n                <div className='relative my-6'>\r\n                  <div className='absolute inset-0 flex items-center'>\r\n                    <span className='w-full border-t border-gray-200' />\r\n                  </div>\r\n                  <div className='relative flex justify-center text-xs uppercase'>\r\n                    <span className='bg-white px-2 text-gray-500'>atau</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Alternative Login Methods */}\r\n                <div className='space-y-3'>\r\n                  <Button variant='outline' className='w-full h-11 font-medium border-gray-200 hover:bg-gray-50' data-sentry-element=\"Button\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                    <svg className='w-4 h-4 mr-2' viewBox='0 0 24 24' data-sentry-element=\"svg\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                      <path fill='#4285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z' data-sentry-element=\"path\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <path fill='#34A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z' data-sentry-element=\"path\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <path fill='#FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z' data-sentry-element=\"path\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                      <path fill='#EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z' data-sentry-element=\"path\" data-sentry-source-file=\"sign-in-view.tsx\" />\r\n                    </svg>\r\n                    Masuk dengan Google\r\n                  </Button>\r\n                </div>\r\n\r\n                {/* Sign Up Link */}\r\n                <p className='text-center text-sm text-gray-600 mt-6'>\r\n                  Belum punya akun?{' '}\r\n                  <Link href='/auth/sign-up' className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                    Daftar sekarang\r\n                  </Link>\r\n                </p>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            {/* Footer */}\r\n            <div className='text-center text-xs text-gray-500 space-y-1'>\r\n              <p>\r\n                Dengan masuk, Anda menyetujui{' '}\r\n                <Link href='/terms' className='underline hover:text-gray-700' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                  Syarat & Ketentuan\r\n                </Link>{' '}\r\n                dan{' '}\r\n                <Link href='/privacy' className='underline hover:text-gray-700' data-sentry-element=\"Link\" data-sentry-source-file=\"sign-in-view.tsx\">\r\n                  Kebijakan Privasi\r\n                </Link>\r\n              </p>\r\n              <div className='flex items-center justify-center space-x-2 text-gray-400'>\r\n                <span>© 2024 IAI LMS - Powered by</span>\r\n                <img src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\" alt=\"Terang AI\" className='h-4 inline-block' />\r\n              </div>\r\n            </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>;\n}", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\auth\\\\components\\\\sign-in-view.tsx\");\n", "// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\nexport {\n  composeEventHandlers\n};\n//# sourceMappingURL=index.mjs.map\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\auth\\\\components\\\\sign-in-view.tsx\");\n", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\auth\\\\sign-in\\\\[[...sign-in]]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'sign-in',\n        {\n        children: [\n        '[[...sign-in]]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\auth\\\\sign-in\\\\[[...sign-in]]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\auth\\\\sign-in\\\\[[...sign-in]]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/auth/sign-in/[[...sign-in]]/page\",\n        pathname: \"/auth/sign-in/[[...sign-in]]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration | null>(null);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      stylesRef.current = node ? getComputedStyle(node) : null;\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles: CSSStyleDeclaration | null) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport C from\"../create-hugeicon-component.js\";const o=C(\"BookOpen01Icon\",[[\"path\",{d:\"M12 6L12 20\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M5.98056 3.28544C9.32175 3.9216 11.3131 5.25231 12 6.01628C12.6869 5.25231 14.6782 3.9216 18.0194 3.28544C19.7121 2.96315 20.5584 2.80201 21.2792 3.41964C22 4.03727 22 5.04022 22 7.04612V14.255C22 16.0891 22 17.0061 21.5374 17.5787C21.0748 18.1512 20.0564 18.3451 18.0194 18.733C16.2037 19.0787 14.7866 19.6295 13.7608 20.1831C12.7516 20.7277 12.247 21 12 21C11.753 21 11.2484 20.7277 10.2392 20.1831C9.21344 19.6295 7.79633 19.0787 5.98056 18.733C3.94365 18.3451 2.9252 18.1512 2.4626 17.5787C2 17.0061 2 16.0891 2 14.255V7.04612C2 5.04022 2 4.03727 2.72078 3.41964C3.44157 2.80201 4.2879 2.96315 5.98056 3.28544Z\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport {\n  useControllableState,\n  useControllableStateReducer\n};\n//# sourceMappingURL=index.mjs.map\n", "import { AuthUser } from '@/types/database';\r\n\r\n// Client-side auth utilities\r\nexport const authStorage = {\r\n  setUser: (user: AuthUser) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_user', JSON.stringify(user));\r\n    }\r\n  },\r\n\r\n  getUser: (): AuthUser | null => {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = localStorage.getItem('auth_user');\r\n      return stored ? JSON.parse(stored) : null;\r\n    }\r\n    return null;\r\n  },\r\n\r\n  removeUser: () => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_user');\r\n    }\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    return authStorage.getUser() !== null;\r\n  },\r\n\r\n  hasRole: (role: string): boolean => {\r\n    const user = authStorage.getUser();\r\n    return user?.role === role;\r\n  },\r\n\r\n  isSuperAdmin: (): boolean => {\r\n    return authStorage.hasRole('super_admin');\r\n  },\r\n\r\n  isTeacher: (): boolean => {\r\n    return authStorage.hasRole('teacher');\r\n  },\r\n\r\n  isStudent: (): boolean => {\r\n    return authStorage.hasRole('student');\r\n  }\r\n};\r\n\r\n// Role-based redirect logic\r\nexport const getRedirectPath = (user: AuthUser): string => {\r\n  switch (user.role) {\r\n    case 'super_admin':\r\n      return '/dashboard/admin';\r\n    case 'teacher':\r\n      return '/dashboard/teacher';\r\n    case 'student':\r\n      return '/courses';\r\n    default:\r\n      return '/dashboard';\r\n  }\r\n};\r\n\r\n// Protected route checker\r\nexport const checkAuth = (): AuthUser | null => {\r\n  const user = authStorage.getUser();\r\n  if (!user) {\r\n    // Redirect to sign in if not authenticated\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = '/auth/sign-in';\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Role-based access control\r\nexport const requireRole = (requiredRole: string): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (user.role !== requiredRole) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Multiple roles checker\r\nexport const requireAnyRole = (roles: string[]): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (!roles.includes(user.role)) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm9 12 2 2 4-4', key: 'dzmm74' }],\n];\n\n/**\n * @component @name CircleCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtOSAxMiAyIDIgNC00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheck = createLucideIcon('CircleCheck', __iconNode);\n\nexport default CircleCheck;\n", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import { Metadata } from 'next';\nimport SignInViewPage from '@/features/auth/components/sign-in-view';\nexport const metadata: Metadata = {\n  title: 'IAI LMS | Masuk',\n  description: 'Masuk ke akun Anda di platform pembelajaran IAI LMS - Ikatan Arsitek Indonesia'\n};\nexport default function Page() {\n  return <SignInViewPage data-sentry-element=\"SignInViewPage\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\" />;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/auth/sign-in/[[...sign-in]]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/auth/sign-in/[[...sign-in]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/auth/sign-in/[[...sign-in]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/auth/sign-in/[[...sign-in]]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {\n};\nexport {\n  useLayoutEffect2 as useLayoutEffect\n};\n//# sourceMappingURL=index.mjs.map\n", "// src/primitive.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nimport { createSlot } from \"@radix-ui/react-slot\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ jsx(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\nexport {\n  Primitive,\n  Root,\n  dispatchDiscreteCustomEvent\n};\n//# sourceMappingURL=index.mjs.map\n", "module.exports = require(\"events\");", "// packages/react/use-size/src/use-size.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nfunction useSize(element) {\n  const [size, setSize] = React.useState(void 0);\n  useLayoutEffect(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\nexport {\n  useSize\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": ["FeatureCarousel", "currentSlide", "setCurrentSlide", "useState", "features", "icon", "BookIcon", "title", "description", "highlight", "TaskIcon", "AwardIcon", "useEffect", "timer", "setInterval", "prev", "length", "clearInterval", "currentFeature", "Icon", "div", "className", "data-sentry-component", "data-sentry-source-file", "button", "onClick", "prevSlide", "ChevronLeftIcon", "data-sentry-element", "map", "_", "index", "nextSlide", "ChevronRightIcon", "h3", "p", "span", "SignInViewPage", "showPassword", "setShowPassword", "loading", "setLoading", "loginSuccess", "setLoginSuccess", "error", "setError", "formData", "setFormData", "email", "password", "rememberMe", "handleInputChange", "name", "value", "type", "checked", "e", "target", "handleSubmit", "preventDefault", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "ok", "authStorage", "setUser", "user", "toast", "success", "duration", "setTimeout", "redirectPath", "getRedirectPath", "console", "log", "role", "window", "location", "href", "err", "message", "svg", "width", "height", "xmlns", "defs", "pattern", "id", "patternUnits", "path", "d", "fill", "stroke", "strokeWidth", "rect", "Image", "src", "alt", "h2", "br", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "form", "onSubmit", "Label", "htmlFor", "MailIcon", "Input", "placeholder", "onChange", "required", "LockIcon", "EyeOffIcon", "EyeIcon", "Checkbox", "onCheckedChange", "Link", "<PERSON><PERSON>", "variant", "disabled", "CheckCircle2", "viewBox", "img", "node", "getUser", "removeUser", "isAuthenticated", "hasRole", "isSuperAdmin", "<PERSON><PERSON><PERSON>er", "isStudent", "requireRole", "checkAuth", "requiredRole", "metadata", "Page", "_jsx"], "sourceRoot": ""}