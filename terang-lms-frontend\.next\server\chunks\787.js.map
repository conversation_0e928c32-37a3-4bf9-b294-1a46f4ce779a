{"version": 3, "file": "787.js", "mappings": "knBA6BM,EAAmB,aAGnB,CAAC,EAAyB,EAAqB,CAAI,OAAkB,CAAC,GAuBtE,CAAC,EAAoB,EAAoB,CAC7C,CAxBmD,CAwBH,GAU5C,EAlCsF,EAkCnE,QAXsB,CACmB,CAUzC,CACvB,CAAC,EAAqC,KACpC,GAAM,mBACJ,OACA,EAAO,YACP,kBACA,EAAkB,IAClB,GAAG,EACL,CAAI,EACE,CAAC,EAAY,EAAa,CAAU,UAAV,CAA6C,IAAI,EAC3E,CAAC,EAAU,EAAW,CAAU,QAAV,EAAU,CAA2C,IAAI,EAC/E,CAAC,EAAS,EAAU,CAAU,OAAV,GAAU,CAAgC,IAAI,EAClE,CAAC,EAAY,EAAa,CAAU,WAA4C,IAAI,EACpF,CAAC,EAAY,EAAa,CAAU,UAAV,CAAsD,IAAI,EACpF,CAAC,EAAa,EAAc,CAAU,WAAV,CAAoB,EAChD,CAAC,EAAc,EAAe,CAAU,WAAS,CAAC,EAClD,CAAC,EAAmB,EAAoB,CAAU,YAAS,GAC3D,CAAC,CADuC,CACpB,EAAoB,CAAU,YAAS,GAC3D,EADgE,CACjD,MAAe,CAAC,EAAc,GAAU,EAAc,IAAI,CAAC,CAC9D,QAAY,CAAC,GAAG,MAGhC,UAAC,GACC,MAAO,OACP,EACA,IAAK,kBACL,aACA,WACA,EACA,iBAAkB,UAClB,EACA,gBAAiB,aACjB,EACA,mBAAoB,oBACpB,EACA,0BAA2B,EAC3B,aACA,mBAAoB,oBACpB,EACA,0BAA2B,EAC3B,oBAAqB,EACrB,qBAAsB,EAEtB,mBAAC,IAAS,CAAC,IAAV,CACC,IAAK,EACJ,GAAG,EACJ,IAAK,EACL,MAAO,CACL,SAAU,WAET,kCAAyC,CAAG,EAAc,KAC1D,mCAA0C,CAAG,EAAe,KAC7D,GAAG,EAAM,KACX,GACF,EAGN,EAGF,GAAW,YAAc,EAMzB,IAAM,EAAgB,qBAOhB,EAA2B,aAC/B,CAAC,EAA6C,KAC5C,GAAM,mBAAE,WAAmB,QAAU,EAAO,GAAG,EAAc,CAAI,EAC3D,EAAU,EAAqB,EAAe,GADS,EAE3C,SAAkC,GADiB,CACb,EAClD,EAAe,OAAe,CAAC,EAAc,EAAK,EAAQ,gBAAgB,EAChF,MACE,uBAEE,oBAAC,SACC,wBAAyB,CACvB,OAAQ,qLACV,QACA,IAEF,UAAC,IAAS,CAAC,IAAV,CACC,kCAAgC,GAC/B,GAAG,EACJ,IAAK,EACL,MAAO,CAYL,UAAW,EAAQ,kBAAoB,SAAW,SAClD,UAAW,EAAQ,kBAAoB,SAAW,SAClD,GAAG,EAAM,OAUX,mBAAC,OAAI,IAAK,EAAQ,gBAAiB,MAAO,CAAE,SAAU,OAAQ,QAAS,OAAQ,WAC5E,EACH,IACF,CACF,CAEJ,GAGF,EAAmB,YAAc,EAMjC,IAAM,EAAiB,sBAOjB,EAA4B,aAChC,CAAC,EAA8C,KAC7C,GAAM,YAAE,EAAY,GAAG,EAAe,CAAI,EACpC,EAAU,EAAqB,EAAgB,EAAM,EADrB,eACsC,EACtE,2BAAE,4BAA2B,EAA0B,CAAI,EAC3D,EAAqC,eAAtB,EAAM,YAS3B,OAPM,YAAU,KACd,EAAe,GAA0B,GAAQ,CAAJ,EAA8B,GACpE,CADwE,IAE7E,EAAe,EAA0B,IAAS,CAAJ,EAA8B,EAC9E,GADmF,CAEjF,EAAc,EAA2B,EAA0B,EAE/C,UAAjB,EAAQ,KACb,IAHoE,CAGpE,KAAC,GAA0B,GAAG,EAAgB,IAAK,aAAc,EAAwB,EACvF,WACF,EADU,KACV,UAAC,GAA2B,GAAG,EAAgB,IAAK,aAAc,EAAwB,EACvE,SAAjB,EAAQ,KACV,UAAC,GAAyB,GAAG,EAAgB,IAAK,aAAc,EAAwB,EACrE,WAAjB,EAAQ,KACV,UAAC,GAA4B,GAAG,EAAgB,IAAK,EAAc,EACjE,IACN,EAGF,GAAoB,YAAc,EASlC,IAAM,EAAiC,aAGrC,CAAC,EAAmD,KACpD,GAAM,YAAE,EAAY,GAAG,EAAe,CAAI,EACpC,EAAU,EAAqB,EAAgB,EAAM,EADrB,eACsC,EACtE,CAAC,EAAS,EAAU,CAAU,OAAV,GAAU,EAAS,GAuB7C,EAvBkD,KAE5C,YAAU,KACd,IAAM,EAAa,EAAQ,WACvB,EAAY,EAChB,GAAI,EAAY,CACd,IAAM,EAAqB,KACzB,OAAO,aAAa,GACpB,GAAW,EACb,CAF+B,CACd,EAEU,KACzB,EAAY,OAAO,WAAW,IAAM,GAAW,GAAQ,EAAH,eAA0B,CAChF,EAGA,OAFA,EAAW,iBAAiB,eAAgB,GAC5C,EAAW,aADmD,GACnD,CAAiB,eAAgB,GACrC,KACL,OAAO,GAFqD,SAErD,CAAa,GACpB,EAAW,IADkB,eAClB,CAAoB,eAAgB,GAC/C,EAAW,aADsD,MACtD,CAAoB,eAAgB,EACjD,CACF,CACF,EAAG,CAAC,EAAQ,SAH2D,CAG3D,CAAY,EAAQ,eAAe,CAAC,EAG9C,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAC/B,mBAAC,GACC,aAAY,EAAU,UAAY,SACjC,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,CAAC,EAOK,EAAkC,aAGtC,CAAC,EAAoD,aACrD,GAAM,YAAE,EAAY,GAAG,EAAe,CAAI,EACpC,EAAU,EAAqB,EAAgB,EAAM,EADrB,eACsC,EACtE,EAAqC,eAAtB,EAAM,YACrB,EAAoB,EAAoB,IAAM,EAAK,YAAY,EAAG,GAAG,EACrE,CAAC,EAAO,EAAI,ECxRlB,CDwRsB,CAAgB,SCvRtC,EDuRgD,CAC9C,MCvRF,CDuRU,CACN,OAAQ,WACV,EACA,UAAW,CACT,WAAY,OACZ,cAAe,aACjB,EACA,YAAa,CACX,OAAQ,cACR,cAAe,MACjB,EACA,KAAM,CACJ,KAAM,SACN,OAAQ,YACR,cAAe,aACjB,CACF,CAAC,CCtSY,aAAW,CAAC,EAAwB,IAC5B,EAAQ,EAAK,CAAU,EAAV,EACZ,CAD2B,CAE9C,IDgUH,OA3BM,CCrSS,CDqST,UAAU,KACd,GAAc,SAAV,EAAkB,CACpB,IAAM,EAAY,OAAO,WAAW,IAAM,EAAK,MAAM,EAAG,EAAQ,eAAe,EAC/E,MAAO,IAAM,OAAO,aAAa,EACnC,CACF,EAAG,CAAC,EAAO,CAFmC,CAE3B,gBAAiB,EAAK,EAAD,EAElC,UAAU,KACd,IAAM,EAAW,EAAQ,SACnB,EAAkB,EAAe,aAAe,YAEtD,GAAI,EAAU,CACZ,IAAI,EAAgB,EAAS,EAAe,CACtC,EAAe,KACnB,IAAM,CAFoC,CAExB,EAAS,EAAe,CACN,IAAkB,IAEpD,EAAK,EAHmC,MAG3B,EACb,KAEF,EAAgB,CAClB,EAEA,OADA,CAJsB,CAIb,iBAAiB,SAAU,GAC7B,IAAM,EAAS,GAD0B,gBAC1B,CAAoB,SAAU,EACtD,CACF,EAAG,CAAC,EAAQ,IAFwD,IAExD,CAAU,EAAc,EAAM,EAAkB,EAG1D,UAAC,GAHwD,CAGxD,CAAS,QAAS,GAAwB,WAAV,EAC/B,mBAAC,GACC,aAAsB,aAAW,SAAW,UAC3C,GAAG,EACJ,IAAK,EACL,eAAgB,OAAoB,CAAC,EAAM,eAAgB,IAAM,EAAK,eAAe,CAAC,EACtF,eAAgB,OAAoB,CAAC,EAAM,eAAgB,IAAM,EAAK,eAAe,CAAC,GACxF,CACF,CAEJ,CAAC,EAOK,EAAgC,aAGpC,CAAC,EAAkD,KACnD,IAAM,EAAU,EAAqB,EAAgB,EAAM,iBAAiB,EACtE,YAAE,EAAY,GAAG,EAAe,CAAI,EACpC,CAAC,EAAS,EAAU,CAAU,IADE,GACZ,GAAU,CAAS,IACvC,CAD4C,CAC7B,iBAAM,YACrB,EAAe,EAAoB,KACvC,GAAI,EAAQ,SAAU,CACpB,IAAM,EAAc,EAAQ,SAAS,YAAc,EAAQ,SAAS,YAC9D,EAAc,EAAQ,SAAS,aAAe,EAAQ,SAAS,aACrE,EAAW,EAAe,EAAc,EAC1C,CACF,EAAG,EAAE,EAKL,EAPuD,KAIvD,EAAkB,EAAQ,SAAU,GACpC,EAAkB,EAAQ,KADsB,EACtB,CAAS,GAGjC,SAH6C,CAG5C,GAAQ,CAAR,CAAS,QAAS,GAAc,EAC/B,mBAAC,GACC,aAAY,EAAU,UAAY,SACjC,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,CAAC,EAUK,EAAmC,aAGvC,CAAC,EAAqD,KACtD,GAAM,aAAE,EAAc,WAAY,GAAG,EAAe,CAAI,EAClD,EAAU,EAAqB,EAAgB,EAAM,EADP,eACwB,EACtE,EAAiB,SAAsC,IAAI,EAC3D,EAAyB,SAAO,CAAC,EACjC,CAAC,EAAO,EAAQ,CAAU,KAAV,KAAU,CAAgB,CAC9C,QAAS,EACT,SAAU,EACV,UAAW,CAAE,KAAM,EAAG,aAAc,EAAG,WAAY,CAAE,CACvD,CAAC,EACK,EAAa,EAAc,EAAM,SAAU,EAAM,OAAO,EAGxD,EAAwE,CAC5E,GAAG,QACH,EACA,cAAe,EACf,UAAU,EAAQ,EAAa,GAAK,GAAa,CAAC,CAClD,cAAe,GAAY,EAAS,QAAU,EAC9C,iBAAkB,IAAO,EAAiB,QAAU,EACpD,mBAAoB,GAAiB,EAAiB,QAAU,CAClE,EAEA,SAAS,EAAkB,EAAoB,GAC7C,OA8eJ,SAAS,CACP,CACA,EACA,EACA,EAAiB,OACjB,IACM,EAAc,EAAa,GAE3B,EAAS,GADK,EAAc,EAG5B,EAAgB,EAAM,MAFI,GAEJ,CAAU,aAAe,EAC/C,EAAgB,EAAM,UAAU,KAAO,EAAM,UAAU,YAFlC,CAE+C,CAFjC,GAGnC,EAAe,EAAM,QAAU,EAAM,SAG3C,OADoB,EAAY,CAAC,EAAe,EAAa,CADjC,CACoC,OAD5C,EACyC,CADxB,CAC0D,CADvD,EAAY,CAAI,CAAgB,GAAf,EAAmB,CAAC,EAAzB,CAEjC,EACrB,EA9fwC,EAAY,EAAiB,EA6ftC,KA7fsC,CAAS,EAAO,EACnF,CADsF,MAGtF,cAAkC,CAA9B,EAEA,UAAC,GACE,GAAG,EACJ,IAAK,EACL,sBAAuB,KACrB,GAAI,EAAQ,UAAY,EAAS,QAAS,CAExC,IAAM,EAAS,EADG,EAAQ,SAAS,WACgB,CAAX,CAAkB,EAAQ,GAAG,EACrE,EAAS,QAAQ,MAAM,UAAY,eAAe,EAAM,WAE5D,EACA,cAAgB,IACV,EAAQ,SAAU,IAAQ,SAAS,WAAa,EACtD,EACA,aAAc,IACR,EAAQ,UAAU,CACpB,EAAQ,SAAS,WAAa,EAAkB,EAAY,EAAQ,IAAG,CAE3E,IAKc,YAAY,CAA5B,EAEA,UAAC,GACE,GAAG,EACJ,IAAK,EACL,sBAAuB,KACrB,GAAI,EAAQ,UAAY,EAAS,QAAS,CAExC,IAAM,EAAS,EADG,EAAQ,SAAS,UACgB,EAAX,CACxC,EADwD,OAC/C,CAAQ,MAAM,UAAY,kBAAkB,EAAM,QAE/D,EACA,cAAe,IACT,EAAQ,SAAU,IAAQ,SAAS,UAAY,EACrD,EACA,aAAe,IACT,EAAQ,SAAU,IAAQ,SAAS,UAAY,EAAkB,EAAU,CACjF,IAKC,IACT,CAAC,EAqBK,EAA6B,aAGjC,CAAC,EAAkD,KACnD,GAAM,OAAE,gBAAO,EAAe,GAAG,EAAe,CAAI,EAC9C,EAAU,EAAqB,EAAgB,EAAM,EADX,eAC4B,EACtE,CAAC,EAAe,EAAgB,CAAU,WAA8B,EAAxC,EACpB,SAAuC,IAAI,EACvD,EAAc,OAAe,CAAC,EAAc,EAAK,EAAQ,kBAAkB,EAMjF,OAJM,YAAU,KACV,EAAI,QAAS,GAAiB,iBAAiB,EAAI,OAAO,CAAC,CACjE,EAAG,CAAC,EAAI,CAAD,CAGL,UAAC,GACC,mBAAiB,aAChB,GAAG,EACJ,IAAK,EACL,QACA,MAAO,CACL,OAAQ,EACR,KAAsB,QAAhB,EAAQ,IAAgB,wCAA0C,EACxE,MAAuB,QAAhB,EAAQ,IAAgB,wCAA0C,EACxE,iCAAwC,CAAG,EAAa,GAAS,EAAJ,GAC9D,GAAG,EAAM,KACX,EACA,mBAAoB,GAAgB,EAAM,mBAAmB,EAAW,CAAC,EACzE,aAAc,GAAgB,EAAM,aAAa,EAAW,CAAC,EAC7D,cAAe,CAAC,EAAO,KACrB,GAAI,EAAQ,SAAU,CACpB,IAAM,EAAY,EAAQ,SAAS,WAAa,EAAM,OACtD,EAAM,cAAc,GAEhB,MAFyB,GA8a9B,CAAiC,EAAmB,EAC3D,iBA7a6C,EAAW,IAC9C,EAAM,MADoD,GAAG,KACvD,CAAe,CAEzB,CACF,EACA,SAAU,KACJ,EAAI,SAAW,EAAQ,UAAY,GACrC,EAAc,CACZ,QAAS,CAFyC,CAEjC,SAAS,YAC1B,SAAU,EAAQ,SAAS,YAC3B,UAAW,CACT,KAAM,EAAI,QAAQ,YAClB,aAAc,EAAM,EAAc,WAAW,EAC7C,WAAY,EAAM,EAAc,YAAY,CAC9C,CACF,CAAC,CAEL,GAGN,CAAC,EAEK,EAA6B,aAGjC,CAAC,EAAkD,KACnD,GAAM,OAAE,gBAAO,EAAe,GAAG,EAAe,CAAI,EAC9C,EAAU,EAAqB,EAAgB,EAAM,EADX,eAC4B,EACtE,CAAC,EAAe,EAAgB,CAAU,WAA8B,EAAxC,EACpB,SAAuC,IAAI,EACvD,EAAc,OAAe,CAAC,EAAc,EAAK,EAAQ,kBAAkB,EAMjF,OAJM,YAAU,KACV,EAAI,QAAS,GAAiB,iBAAiB,EAAI,OAAO,CAAC,CACjE,EAAG,CAAC,EAAI,CAAD,CAGL,UAAC,GACC,mBAAiB,WAChB,GAAG,EACJ,IAAK,QACL,EACA,MAAO,CACL,IAAK,EACL,MAAuB,QAAhB,EAAQ,IAAgB,EAAI,OACnC,KAAsB,QAAhB,EAAQ,IAAgB,EAAI,OAClC,OAAQ,yCACP,kCAAyC,CAAG,EAAa,GAAS,EAAJ,GAC/D,GAAG,EAAM,OAEX,mBAAoB,GAAgB,EAAM,mBAAmB,EAAW,CAAC,EACzE,aAAc,GAAgB,EAAM,aAAa,EAAW,CAAC,EAC7D,cAAe,CAAC,EAAO,KACrB,GAAI,EAAQ,SAAU,CACpB,IAAM,EAAY,EAAQ,SAAS,UAAY,EAAM,OACrD,EAAM,cAAc,GAEhB,MAFyB,GAqXG,EAAmB,GAAsB,OAC1E,EAAY,GAAK,EAAY,CACtC,EArX+C,EAAW,IAC9C,EAAM,MADoD,GAAG,KACvD,CAAe,CAEzB,CACF,EACA,SAAU,KACJ,EAAI,SAAW,EAAQ,UAAY,GACrC,EAAc,CACZ,QAAS,CAFyC,CAEjC,SAAS,aAC1B,SAAU,EAAQ,SAAS,aAC3B,UAAW,CACT,KAAM,EAAI,QAAQ,aAClB,aAAc,EAAM,EAAc,UAAU,EAC5C,WAAY,EAAM,EAAc,aAAa,CAC/C,CACF,CAAC,CAEL,GAGN,CAAC,EAaK,CAAC,EAAmB,EAAmB,CAC3C,EAA0C,GAkBtC,EAAgC,SAnBO,GAmBP,CAGpC,CAAC,EAAkD,KACnD,GAAM,mBACJ,QACA,EACA,WACA,iCACA,qBACA,wBACA,eACA,gBACA,WACA,EACA,GAAG,EACL,CAAI,EACE,EAAU,EAAqB,EAAgB,GAC/C,CAAC,EAAW,EAAY,CAAU,QAD8B,CACxC,CAAU,CAA4C,IAAI,EAClF,EAAc,OAAe,CAAC,EAAc,GAAU,EAAa,IAAI,CAAC,CACxD,SAAuB,IAAI,EAC3C,EAAgC,SAAe,EAAE,EACjD,EAAW,EAAQ,SACnB,EAAe,EAAM,QAAU,EAAM,SACrC,EAAoB,OAAc,CAAC,GACnC,EAA4B,OAAc,CADM,GAEhD,EAAe,EAAoB,EAAU,EAAE,EAErD,QAHsE,CAG7D,EAAiB,GAAwC,EACpD,SAGV,EAAa,CAAE,EAFL,EAAM,QAAU,EAAQ,QAAQ,KAExB,EADR,EAAM,QAAU,EAAQ,QAAQ,IACrB,CAEzB,CAwBA,OACE,EAnBI,UAAU,KACd,IAAM,EAAc,IAClB,IAAM,EAAU,EAAM,OACG,GAAW,SAAS,IACvB,EAAkB,CADY,CACL,EACjD,EAEA,OADA,CAF6D,QAEpD,iBAAiB,QAAS,EAAa,CAAE,SAAS,CAAM,CAAC,EAC3D,IAAM,SAAS,oBAAoB,QAAS,EAAa,CAAE,SAAS,CAAM,CAAQ,CAC3F,EAAG,CAAC,EAAU,EAAW,EAAc,EAAkB,EAKnD,YAAU,CALwC,CAKb,CAAC,EAAO,EAA0B,EAE7E,EAAkB,EAAW,GAC7B,EAAkB,EAAQ,KADe,EACf,CAAS,EAHyC,CAM1E,SAH6C,CAG5C,GACC,MAAO,YACP,WACA,EACA,cAAe,OAAc,CAAC,GAC9B,UAD2C,OACzB,OAAc,CAAC,GACjC,aADiD,SAC1B,EACvB,mBAAoB,OAAc,CAAC,GAEnC,eAFqD,EAErD,EAAC,IAAS,CAAC,IAAV,CACE,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,SAAU,WAAY,GAAG,EAAe,OACjD,cAAe,OAAoB,CAAC,EAAM,cAAe,IACnC,IAChB,EAAM,SACQ,EADG,MACG,CACd,MAFwB,WAExB,CAAkB,EAAM,SAAS,EACzC,EAAQ,QAAU,EAAW,sBAAsB,EAGnD,EAAwB,QAAU,SAAS,KAAK,MAAM,iBACtD,SAAS,KAAK,MAAM,iBAAmB,OACnC,EAAQ,SAAU,IAAQ,SAAS,MAAM,eAAiB,QAC9D,EAAiB,GAErB,CAAC,CAFyB,CAG1B,cAAe,OAAoB,CAAC,EAAM,cAAe,GACzD,YAAa,CAD4D,EAC5D,IAAoB,CAAC,EAAM,YAAa,IACnD,IAAM,EAAU,EAAM,OAClB,EAAQ,kBAAkB,EAAM,SAAS,GAAG,EACtC,sBAAsB,EAAM,SAAS,EAE/C,SAAS,KAAK,MAAM,iBAAmB,EAAwB,QAC3D,EAAQ,SAAU,IAAQ,SAAS,MAAM,eAAiB,IAC9D,EAAQ,QAAU,IACpB,CAAC,GACH,EAGN,CAAC,EAMK,EAAa,kBAWb,EAAwB,aAC5B,CAAC,EAA0C,KACzC,GAAM,YAAE,EAAY,GAAG,EAAW,CAAI,EAChC,EAAmB,EAAoB,EAAY,EAAM,iBAAiB,EAChF,MACE,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAiB,SAChD,mBAAC,GAAoB,IAAK,EAAe,GAAG,EAAY,EAC1D,CAEJ,GAMI,EAA4B,aAChC,CAAC,EAA8C,KAC7C,GAAM,mBAAE,QAAmB,EAAO,GAAG,EAAW,CAAI,EAC9C,EAAoB,EAAqB,EADC,GAE1C,EAAmB,EAAoB,EAAY,GACnD,KAFsE,SACF,SAClE,EAAsB,CAAI,EAC5B,EAAc,OAAe,CAAC,EAAe,GACjD,EAAiB,cAAc,IAAI,EAES,SAAmB,MAAS,EACpE,EAAoB,EAAoB,KACxC,EAAgC,SAAS,CAC3C,EAAgC,QAAQ,EACxC,EAAgC,QAAU,OAE9C,EAAG,GAAG,EA0BN,OAxBM,YAAU,KACd,IAAM,EAAW,EAAkB,SACnC,GAAI,EAAU,CAQZ,IAAM,EAAe,KACnB,IACK,EAAgC,SAAS,CAE5C,EAAgC,QADf,EAA0B,EAAU,GAErD,IAEJ,EAGA,OAFA,IACA,CAN8E,CAMrE,GAJiB,aAGJ,CACI,SAAU,GAC7B,IAAM,EAAS,GAD0B,gBAC1B,CAAoB,SAAU,EACtD,CACF,EAAG,CAAC,EAAkB,IAF8C,IAE9C,CAAU,EAAmB,EAAsB,EAGvE,UAAC,IAAS,CAAC,EAH2D,CAG3D,CAAV,CACC,aAAY,EAAiB,SAAW,UAAY,SACnD,GAAG,EACJ,IAAK,EACL,MAAO,CACL,MAAO,uCACP,OAAQ,wCACR,GAAG,GAEL,qBAAsB,OAAoB,CAAC,EAAM,qBAAuB,IAEtE,IAAM,EADQ,EAAM,OACI,sBAAsB,EACxC,EAAI,EAAM,QAAU,EAAU,KAC9B,EAAI,EAAM,QAAU,EAAU,IACpC,EAAiB,mBAAmB,GAAE,IAAG,CAAE,CAAC,CAC9C,CAAC,EACD,YAAa,OAAoB,CAAC,EAAM,YAAa,EAAiB,gBAAgB,GAG5F,GAGF,EAAgB,YAAc,EAM9B,IAAM,EAAc,mBAKd,EAAyB,aAC7B,CAAC,EAA2C,KAC1C,IAAM,EAAU,EAAqB,EAAa,EAAM,iBAAiB,EACnE,GAA2B,EAAQ,EAAQ,YAAc,EAAQ,YAEvE,MADmC,WAAjB,CACC,CADO,MAAqB,EAC5B,UAAC,GAAsB,GAAG,EAAO,IAAK,EAAc,EAAK,IAC9E,GAGF,EAAiB,YAAc,EAO/B,IAAM,EAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,mBAAE,EAAmB,GAAG,EAAY,CAAI,EACxC,EAAU,EAAqB,EAAa,CADR,EAEpC,CAAC,EAAO,EAAQ,CAAU,KAAV,GAD6C,EACnC,CAAS,CAAC,EACpC,CAAC,EAAQ,EAAS,CAAU,MAAV,IAAU,CAAS,CAAC,EACtC,GAAU,EAAQ,GAAS,EAAM,CAcvC,OAZA,EAAkB,EAAQ,WAAY,KACpC,IAAMA,EAAS,EAAQ,YAAY,cAAgB,EACnD,EAAQ,qBAAqBA,GAC7B,EAAUA,EADyB,CAEpC,EAED,EAAkB,EAAQ,WAAY,KACpC,IAAMC,EAAQ,EAAQ,YAAY,aAAe,EACjD,EAAQ,oBAAoBA,GAC5B,EAASA,CADwB,CAEnC,CAAC,EAEM,CAHS,CAId,UAAC,IAAS,CAAC,IAAV,CACE,GAAG,EACJ,IAAK,EACL,MAAO,OACL,SACA,EACA,SAAU,WACV,MAAuB,QAAhB,EAAQ,IAAgB,EAAI,OACnC,KAAsB,QAAhB,EAAQ,IAAgB,EAAI,OAClC,OAAQ,EACR,GAAG,EAAM,KACX,IAEA,IACN,CAAC,EAID,SAAS,EAAM,GAAgB,OACtB,EAAQ,SAAS,EAAO,EAAE,EAAI,CACvC,CAEA,SAAS,EAAc,EAAsB,GAAqB,IAC1D,EAAQ,EAAe,EAC7B,OAAO,MAAM,GAAS,EAAJ,CACpB,CAEA,SAAS,EAAa,GAAc,IAC5B,EAAQ,EAAc,EAAM,SAAU,EAAM,OAAO,EACnD,EAAmB,EAAM,UAAU,aAAe,EAAM,UAAU,WAGxE,OAAO,KAAK,IAFO,GAAM,UAAU,KAAO,GAAoB,EAEnC,EAAE,CAC/B,CAoBA,SAAS,EAAyB,EAAmB,EAAc,EAAiB,OAAO,IACnF,EAAc,EAAa,GAC3B,EADgC,EACP,UAAU,aAAe,EAAM,UAAU,WAClE,EAAY,EAAM,UAAU,KAAO,EACnC,EAAe,EAAM,QAAU,EAAM,SAGrC,EAAwB,OAAK,CAAC,EADH,QAAR,CACsB,CADN,CAAC,EAAG,EAAY,CAAI,CAAgB,GAAf,EAAmB,CAAC,EACC,CAEnF,OAAO,EADyB,CAAC,EAAG,EAAY,CAAG,CAAC,EAHhC,EAAY,EAGmC,EAChD,EACrB,CAGA,SAAS,EAAY,EAAkC,GAAmC,EAJhD,KAKjC,IACL,GAAI,EAAM,CAAC,IAAM,EAAM,CAAC,GAAK,EAAO,CAAC,IAAM,EAAO,CAAC,EAAG,OAAO,EAAO,CAAC,EACrE,IAAM,GAAS,EAAO,CAAC,EAAI,EAAO,IAAO,GAAM,CAAC,EAAI,EAAM,EAAC,EAC3D,OAAO,EAAO,CAAC,EAAI,GAAS,EAAQ,EAAM,GAC5C,CACF,CAQA,IAAM,EAA4B,CAAC,EAAmB,EAAU,KAAO,CAAD,IACpE,IAAI,EAAe,CAAE,KAAM,EAAK,WAAY,IAAK,EAAK,WAClD,EAAM,EASV,OARC,SAAS,IACR,GADe,CACT,EAAW,CAAE,KAAM,EAAK,WAAY,IAAK,EAAK,WAC9C,EAAqB,EAAa,OAAS,EAAS,KACpD,EAAmB,EAAa,MAAQ,EAAS,KACnD,GAAsB,GAAkB,KAC5C,EAAe,EADqC,EAE9C,OAAO,sBAAsB,GACrC,CADyC,EACtC,IACU,OAAO,qBAAqB,EAC3C,CAD8C,CAG9C,SAAS,EAAoB,EAAsB,GAAe,IAC1D,EAAiB,OAAc,CAAC,GAChC,EAAyB,GADe,KACf,CAAO,CAAC,EAEvC,OADM,YAAU,IAAM,IAAM,OAAO,aAAa,EAAiB,OAAO,EAAG,CAAC,CAAC,EAChE,cAAY,KACvB,OAAO,aAAa,EAAiB,OAAO,EAC5C,EAAiB,QAAU,OAAO,WAAW,EAAgB,EAC/D,EAAG,CADiE,EAChD,EAAM,CAC5B,CAEA,CAH2B,QAGlB,EAAkB,EAA6B,GACtD,IAAM,EAAe,OAAc,CAAC,GACpC,KAD4C,CAC5C,CAAe,CAAC,KACd,IAAI,EAAM,EACV,GAAI,EAAS,CAQX,IAAM,EAAiB,IAAI,eAAe,KACxC,qBAAqB,GAAG,EAClB,OAAO,sBAAsB,EACrC,CAAC,EAED,OAHiD,EAElC,QAAQ,GAChB,IADuB,CAE5B,OAAO,qBAAqB,GAAG,EAChB,UAAU,EAC3B,CACF,CACF,EAAG,CAHmC,EAGzB,EAAa,CAC5B,CAIA,IAAM,EAAO,EALc,EAMV,EAGX,EAAS", "sources": ["webpack://terang-lms-ui/../src/scroll-area.tsx", "webpack://terang-lms-ui/../src/use-state-machine.ts"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useStateMachine } from './use-state-machine';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\ntype Sizes = {\n  content: number;\n  viewport: number;\n  scrollbar: {\n    size: number;\n    paddingStart: number;\n    paddingEnd: number;\n  };\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollArea\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_AREA_NAME = 'ScrollArea';\n\ntype ScopedProps<P> = P & { __scopeScrollArea?: Scope };\nconst [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\n\ntype ScrollAreaContextValue = {\n  type: 'auto' | 'always' | 'scroll' | 'hover';\n  dir: Direction;\n  scrollHideDelay: number;\n  scrollArea: ScrollAreaElement | null;\n  viewport: ScrollAreaViewportElement | null;\n  onViewportChange(viewport: ScrollAreaViewportElement | null): void;\n  content: HTMLDivElement | null;\n  onContentChange(content: HTMLDivElement): void;\n  scrollbarX: ScrollAreaScrollbarElement | null;\n  onScrollbarXChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarXEnabled: boolean;\n  onScrollbarXEnabledChange(rendered: boolean): void;\n  scrollbarY: ScrollAreaScrollbarElement | null;\n  onScrollbarYChange(scrollbar: ScrollAreaScrollbarElement | null): void;\n  scrollbarYEnabled: boolean;\n  onScrollbarYEnabledChange(rendered: boolean): void;\n  onCornerWidthChange(width: number): void;\n  onCornerHeightChange(height: number): void;\n};\n\nconst [ScrollAreaProvider, useScrollAreaContext] =\n  createScrollAreaContext<ScrollAreaContextValue>(SCROLL_AREA_NAME);\n\ntype ScrollAreaElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ScrollAreaProps extends PrimitiveDivProps {\n  type?: ScrollAreaContextValue['type'];\n  dir?: ScrollAreaContextValue['dir'];\n  scrollHideDelay?: number;\n}\n\nconst ScrollArea = React.forwardRef<ScrollAreaElement, ScrollAreaProps>(\n  (props: ScopedProps<ScrollAreaProps>, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = 'hover',\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React.useState<ScrollAreaElement | null>(null);\n    const [viewport, setViewport] = React.useState<ScrollAreaViewportElement | null>(null);\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const [scrollbarX, setScrollbarX] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [scrollbarY, setScrollbarY] = React.useState<ScrollAreaScrollbarElement | null>(null);\n    const [cornerWidth, setCornerWidth] = React.useState(0);\n    const [cornerHeight, setCornerHeight] = React.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n\n    return (\n      <ScrollAreaProvider\n        scope={__scopeScrollArea}\n        type={type}\n        dir={direction}\n        scrollHideDelay={scrollHideDelay}\n        scrollArea={scrollArea}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        content={content}\n        onContentChange={setContent}\n        scrollbarX={scrollbarX}\n        onScrollbarXChange={setScrollbarX}\n        scrollbarXEnabled={scrollbarXEnabled}\n        onScrollbarXEnabledChange={setScrollbarXEnabled}\n        scrollbarY={scrollbarY}\n        onScrollbarYChange={setScrollbarY}\n        scrollbarYEnabled={scrollbarYEnabled}\n        onScrollbarYEnabledChange={setScrollbarYEnabled}\n        onCornerWidthChange={setCornerWidth}\n        onCornerHeightChange={setCornerHeight}\n      >\n        <Primitive.div\n          dir={direction}\n          {...scrollAreaProps}\n          ref={composedRefs}\n          style={{\n            position: 'relative',\n            // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n            ['--radix-scroll-area-corner-width' as any]: cornerWidth + 'px',\n            ['--radix-scroll-area-corner-height' as any]: cornerHeight + 'px',\n            ...props.style,\n          }}\n        />\n      </ScrollAreaProvider>\n    );\n  }\n);\n\nScrollArea.displayName = SCROLL_AREA_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ScrollAreaViewport';\n\ntype ScrollAreaViewportElement = React.ComponentRef<typeof Primitive.div>;\ninterface ScrollAreaViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst ScrollAreaViewport = React.forwardRef<ScrollAreaViewportElement, ScrollAreaViewportProps>(\n  (props: ScopedProps<ScrollAreaViewportProps>, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React.useRef<ScrollAreaViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Primitive.div\n          data-radix-scroll-area-viewport=\"\"\n          {...viewportProps}\n          ref={composedRefs}\n          style={{\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? 'scroll' : 'hidden',\n            overflowY: context.scrollbarYEnabled ? 'scroll' : 'hidden',\n            ...props.style,\n          }}\n        >\n          {/**\n           * `display: table` ensures our content div will match the size of its children in both\n           * horizontal and vertical axis so we can determine if scroll width/height changed and\n           * recalculate thumb sizes. This doesn't account for children with *percentage*\n           * widths that change. We'll wait to see what use-cases consumers come up with there\n           * before trying to resolve it.\n           */}\n          <div ref={context.onContentChange} style={{ minWidth: '100%', display: 'table' }}>\n            {children}\n          </div>\n        </Primitive.div>\n      </>\n    );\n  }\n);\n\nScrollAreaViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaScrollbar\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLLBAR_NAME = 'ScrollAreaScrollbar';\n\ntype ScrollAreaScrollbarElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbar = React.forwardRef<ScrollAreaScrollbarElement, ScrollAreaScrollbarProps>(\n  (props: ScopedProps<ScrollAreaScrollbarProps>, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === 'horizontal';\n\n    React.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n\n    return context.type === 'hover' ? (\n      <ScrollAreaScrollbarHover {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'scroll' ? (\n      <ScrollAreaScrollbarScroll {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'auto' ? (\n      <ScrollAreaScrollbarAuto {...scrollbarProps} ref={forwardedRef} forceMount={forceMount} />\n    ) : context.type === 'always' ? (\n      <ScrollAreaScrollbarVisible {...scrollbarProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarHoverElement = ScrollAreaScrollbarAutoElement;\ninterface ScrollAreaScrollbarHoverProps extends ScrollAreaScrollbarAutoProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarHover = React.forwardRef<\n  ScrollAreaScrollbarHoverElement,\n  ScrollAreaScrollbarHoverProps\n>((props: ScopedProps<ScrollAreaScrollbarHoverProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React.useState(false);\n\n  React.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener('pointerenter', handlePointerEnter);\n      scrollArea.addEventListener('pointerleave', handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener('pointerenter', handlePointerEnter);\n        scrollArea.removeEventListener('pointerleave', handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarAuto\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarScrollElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarScrollProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarScroll = React.forwardRef<\n  ScrollAreaScrollbarScrollElement,\n  ScrollAreaScrollbarScrollProps\n>((props: ScopedProps<ScrollAreaScrollbarScrollProps>, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === 'horizontal';\n  const debounceScrollEnd = useDebounceCallback(() => send('SCROLL_END'), 100);\n  const [state, send] = useStateMachine('hidden', {\n    hidden: {\n      SCROLL: 'scrolling',\n    },\n    scrolling: {\n      SCROLL_END: 'idle',\n      POINTER_ENTER: 'interacting',\n    },\n    interacting: {\n      SCROLL: 'interacting',\n      POINTER_LEAVE: 'idle',\n    },\n    idle: {\n      HIDE: 'hidden',\n      SCROLL: 'scrolling',\n      POINTER_ENTER: 'interacting',\n    },\n  });\n\n  React.useEffect(() => {\n    if (state === 'idle') {\n      const hideTimer = window.setTimeout(() => send('HIDE'), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n\n  React.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? 'scrollLeft' : 'scrollTop';\n\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send('SCROLL');\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n\n  return (\n    <Presence present={forceMount || state !== 'hidden'}>\n      <ScrollAreaScrollbarVisible\n        data-state={state === 'hidden' ? 'hidden' : 'visible'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, () => send('POINTER_ENTER'))}\n        onPointerLeave={composeEventHandlers(props.onPointerLeave, () => send('POINTER_LEAVE'))}\n      />\n    </Presence>\n  );\n});\n\ntype ScrollAreaScrollbarAutoElement = ScrollAreaScrollbarVisibleElement;\ninterface ScrollAreaScrollbarAutoProps extends ScrollAreaScrollbarVisibleProps {\n  forceMount?: true;\n}\n\nconst ScrollAreaScrollbarAuto = React.forwardRef<\n  ScrollAreaScrollbarAutoElement,\n  ScrollAreaScrollbarAutoProps\n>((props: ScopedProps<ScrollAreaScrollbarAutoProps>, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React.useState(false);\n  const isHorizontal = props.orientation === 'horizontal';\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <Presence present={forceMount || visible}>\n      <ScrollAreaScrollbarVisible\n        data-state={visible ? 'visible' : 'hidden'}\n        {...scrollbarProps}\n        ref={forwardedRef}\n      />\n    </Presence>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarVisibleElement = ScrollAreaScrollbarAxisElement;\ninterface ScrollAreaScrollbarVisibleProps\n  extends Omit<ScrollAreaScrollbarAxisProps, keyof ScrollAreaScrollbarAxisPrivateProps> {\n  orientation?: 'horizontal' | 'vertical';\n}\n\nconst ScrollAreaScrollbarVisible = React.forwardRef<\n  ScrollAreaScrollbarVisibleElement,\n  ScrollAreaScrollbarVisibleProps\n>((props: ScopedProps<ScrollAreaScrollbarVisibleProps>, forwardedRef) => {\n  const { orientation = 'vertical', ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React.useRef<ScrollAreaThumbElement | null>(null);\n  const pointerOffsetRef = React.useRef(0);\n  const [sizes, setSizes] = React.useState<Sizes>({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 },\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n\n  type UncommonProps = 'onThumbPositionChange' | 'onDragScroll' | 'onWheelScroll';\n  const commonProps: Omit<ScrollAreaScrollbarAxisPrivateProps, UncommonProps> = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => (thumbRef.current = thumb),\n    onThumbPointerUp: () => (pointerOffsetRef.current = 0),\n    onThumbPointerDown: (pointerPos) => (pointerOffsetRef.current = pointerPos),\n  };\n\n  function getScrollPosition(pointerPos: number, dir?: Direction) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n\n  if (orientation === 'horizontal') {\n    return (\n      <ScrollAreaScrollbarX\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }}\n      />\n    );\n  }\n\n  if (orientation === 'vertical') {\n    return (\n      <ScrollAreaScrollbarY\n        {...commonProps}\n        ref={forwardedRef}\n        onThumbPositionChange={() => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        }}\n        onWheelScroll={(scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        }}\n        onDragScroll={(pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }}\n      />\n    );\n  }\n\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaScrollbarAxisPrivateProps = {\n  hasThumb: boolean;\n  sizes: Sizes;\n  onSizesChange(sizes: Sizes): void;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerDown(pointerPos: number): void;\n  onThumbPointerUp(): void;\n  onThumbPositionChange(): void;\n  onWheelScroll(scrollPos: number): void;\n  onDragScroll(pointerPos: number): void;\n};\n\ntype ScrollAreaScrollbarAxisElement = ScrollAreaScrollbarImplElement;\ninterface ScrollAreaScrollbarAxisProps\n  extends Omit<ScrollAreaScrollbarImplProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarAxisPrivateProps {}\n\nconst ScrollAreaScrollbarX = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"horizontal\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        bottom: 0,\n        left: context.dir === 'rtl' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        right: context.dir === 'ltr' ? 'var(--radix-scroll-area-corner-width)' : 0,\n        ['--radix-scroll-area-thumb-width' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.x)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.x)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\nconst ScrollAreaScrollbarY = React.forwardRef<\n  ScrollAreaScrollbarAxisElement,\n  ScrollAreaScrollbarAxisProps\n>((props: ScopedProps<ScrollAreaScrollbarAxisProps>, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React.useState<CSSStyleDeclaration>();\n  const ref = React.useRef<ScrollAreaScrollbarAxisElement>(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n\n  React.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n\n  return (\n    <ScrollAreaScrollbarImpl\n      data-orientation=\"vertical\"\n      {...scrollbarProps}\n      ref={composeRefs}\n      sizes={sizes}\n      style={{\n        top: 0,\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 'var(--radix-scroll-area-corner-height)',\n        ['--radix-scroll-area-thumb-height' as any]: getThumbSize(sizes) + 'px',\n        ...props.style,\n      }}\n      onThumbPointerDown={(pointerPos) => props.onThumbPointerDown(pointerPos.y)}\n      onDragScroll={(pointerPos) => props.onDragScroll(pointerPos.y)}\n      onWheelScroll={(event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          // prevent window scroll when wheeling on scrollbar\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      }}\n      onResize={() => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom),\n            },\n          });\n        }\n      }}\n    />\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollbarContext = {\n  hasThumb: boolean;\n  scrollbar: ScrollAreaScrollbarElement | null;\n  onThumbChange(thumb: ScrollAreaThumbElement | null): void;\n  onThumbPointerUp(): void;\n  onThumbPointerDown(pointerPos: { x: number; y: number }): void;\n  onThumbPositionChange(): void;\n};\n\nconst [ScrollbarProvider, useScrollbarContext] =\n  createScrollAreaContext<ScrollbarContext>(SCROLLBAR_NAME);\n\ntype ScrollAreaScrollbarImplElement = React.ComponentRef<typeof Primitive.div>;\ntype ScrollAreaScrollbarImplPrivateProps = {\n  sizes: Sizes;\n  hasThumb: boolean;\n  onThumbChange: ScrollbarContext['onThumbChange'];\n  onThumbPointerUp: ScrollbarContext['onThumbPointerUp'];\n  onThumbPointerDown: ScrollbarContext['onThumbPointerDown'];\n  onThumbPositionChange: ScrollbarContext['onThumbPositionChange'];\n  onWheelScroll(event: WheelEvent, maxScrollPos: number): void;\n  onDragScroll(pointerPos: { x: number; y: number }): void;\n  onResize(): void;\n};\ninterface ScrollAreaScrollbarImplProps\n  extends Omit<PrimitiveDivProps, keyof ScrollAreaScrollbarImplPrivateProps>,\n    ScrollAreaScrollbarImplPrivateProps {}\n\nconst ScrollAreaScrollbarImpl = React.forwardRef<\n  ScrollAreaScrollbarImplElement,\n  ScrollAreaScrollbarImplProps\n>((props: ScopedProps<ScrollAreaScrollbarImplProps>, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React.useState<ScrollAreaScrollbarElement | null>(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React.useRef<DOMRect | null>(null);\n  const prevWebkitUserSelectRef = React.useRef<string>('');\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n\n  function handleDragScroll(event: React.PointerEvent<HTMLElement>) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n\n  /**\n   * We bind wheel event imperatively so we can switch off passive\n   * mode for document wheel event to allow it to be prevented\n   */\n  React.useEffect(() => {\n    const handleWheel = (event: WheelEvent) => {\n      const element = event.target as HTMLElement;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener('wheel', handleWheel, { passive: false });\n    return () => document.removeEventListener('wheel', handleWheel, { passive: false } as any);\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n\n  /**\n   * Update thumb position on sizes change\n   */\n  React.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n\n  return (\n    <ScrollbarProvider\n      scope={__scopeScrollArea}\n      scrollbar={scrollbar}\n      hasThumb={hasThumb}\n      onThumbChange={useCallbackRef(onThumbChange)}\n      onThumbPointerUp={useCallbackRef(onThumbPointerUp)}\n      onThumbPositionChange={handleThumbPositionChange}\n      onThumbPointerDown={useCallbackRef(onThumbPointerDown)}\n    >\n      <Primitive.div\n        {...scrollbarProps}\n        ref={composeRefs}\n        style={{ position: 'absolute', ...scrollbarProps.style }}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const mainPointer = 0;\n          if (event.button === mainPointer) {\n            const element = event.target as HTMLElement;\n            element.setPointerCapture(event.pointerId);\n            rectRef.current = scrollbar!.getBoundingClientRect();\n            // pointer capture doesn't prevent text selection in Safari\n            // so we remove text selection manually when scrolling\n            prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n            document.body.style.webkitUserSelect = 'none';\n            if (context.viewport) context.viewport.style.scrollBehavior = 'auto';\n            handleDragScroll(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, handleDragScroll)}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const element = event.target as HTMLElement;\n          if (element.hasPointerCapture(event.pointerId)) {\n            element.releasePointerCapture(event.pointerId);\n          }\n          document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n          if (context.viewport) context.viewport.style.scrollBehavior = '';\n          rectRef.current = null;\n        })}\n      />\n    </ScrollbarProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'ScrollAreaThumb';\n\ntype ScrollAreaThumbElement = ScrollAreaThumbImplElement;\ninterface ScrollAreaThumbProps extends ScrollAreaThumbImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ScrollAreaThumb = React.forwardRef<ScrollAreaThumbElement, ScrollAreaThumbProps>(\n  (props: ScopedProps<ScrollAreaThumbProps>, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return (\n      <Presence present={forceMount || scrollbarContext.hasThumb}>\n        <ScrollAreaThumbImpl ref={forwardedRef} {...thumbProps} />\n      </Presence>\n    );\n  }\n);\n\ntype ScrollAreaThumbImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface ScrollAreaThumbImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaThumbImpl = React.forwardRef<ScrollAreaThumbImplElement, ScrollAreaThumbImplProps>(\n  (props: ScopedProps<ScrollAreaThumbImplProps>, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(forwardedRef, (node) =>\n      scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React.useRef<() => void>(undefined);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = undefined;\n      }\n    }, 100);\n\n    React.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        /**\n         * We only bind to native scroll event so we know when scroll starts and ends.\n         * When scroll starts we start a requestAnimationFrame loop that checks for\n         * changes to scroll position. That rAF loop triggers our thumb position change\n         * when relevant to avoid scroll-linked effects. We cancel the loop when scroll ends.\n         * https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\n         */\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener('scroll', handleScroll);\n        return () => viewport.removeEventListener('scroll', handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n\n    return (\n      <Primitive.div\n        data-state={scrollbarContext.hasThumb ? 'visible' : 'hidden'}\n        {...thumbProps}\n        ref={composedRef}\n        style={{\n          width: 'var(--radix-scroll-area-thumb-width)',\n          height: 'var(--radix-scroll-area-thumb-height)',\n          ...style,\n        }}\n        onPointerDownCapture={composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target as HTMLElement;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)}\n      />\n    );\n  }\n);\n\nScrollAreaThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ScrollAreaCorner\n * -----------------------------------------------------------------------------------------------*/\n\nconst CORNER_NAME = 'ScrollAreaCorner';\n\ntype ScrollAreaCornerElement = ScrollAreaCornerImplElement;\ninterface ScrollAreaCornerProps extends ScrollAreaCornerImplProps {}\n\nconst ScrollAreaCorner = React.forwardRef<ScrollAreaCornerElement, ScrollAreaCornerProps>(\n  (props: ScopedProps<ScrollAreaCornerProps>, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== 'scroll' && hasBothScrollbarsVisible;\n    return hasCorner ? <ScrollAreaCornerImpl {...props} ref={forwardedRef} /> : null;\n  }\n);\n\nScrollAreaCorner.displayName = CORNER_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ScrollAreaCornerImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface ScrollAreaCornerImplProps extends PrimitiveDivProps {}\n\nconst ScrollAreaCornerImpl = React.forwardRef<\n  ScrollAreaCornerImplElement,\n  ScrollAreaCornerImplProps\n>((props: ScopedProps<ScrollAreaCornerImplProps>, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const hasSize = Boolean(width && height);\n\n  useResizeObserver(context.scrollbarX, () => {\n    const height = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height);\n    setHeight(height);\n  });\n\n  useResizeObserver(context.scrollbarY, () => {\n    const width = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width);\n    setWidth(width);\n  });\n\n  return hasSize ? (\n    <Primitive.div\n      {...cornerProps}\n      ref={forwardedRef}\n      style={{\n        width,\n        height,\n        position: 'absolute',\n        right: context.dir === 'ltr' ? 0 : undefined,\n        left: context.dir === 'rtl' ? 0 : undefined,\n        bottom: 0,\n        ...props.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction toInt(value?: string) {\n  return value ? parseInt(value, 10) : 0;\n}\n\nfunction getThumbRatio(viewportSize: number, contentSize: number) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\n\nfunction getThumbSize(sizes: Sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  // minimum of 18 matches macOS minimum\n  return Math.max(thumbSize, 18);\n}\n\nfunction getScrollPositionFromPointer(\n  pointerPos: number,\n  pointerOffset: number,\n  sizes: Sizes,\n  dir: Direction = 'ltr'\n) {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange as [number, number]);\n  return interpolate(pointerPos);\n}\n\nfunction getThumbOffsetFromScroll(scrollPos: number, sizes: Sizes, dir: Direction = 'ltr') {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === 'ltr' ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange as [number, number]);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction isScrollingWithinScrollbarBounds(scrollPos: number, maxScrollPos: number) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\n\n// Custom scroll handler to avoid scroll-linked effects\n// https://developer.mozilla.org/en-US/docs/Mozilla/Performance/Scroll-linked_effects\nconst addUnlinkedScrollListener = (node: HTMLElement, handler = () => {}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\n\nfunction useDebounceCallback(callback: () => void, delay: number) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React.useRef(0);\n  React.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ScrollArea;\nconst Viewport = ScrollAreaViewport;\nconst Scrollbar = ScrollAreaScrollbar;\nconst Thumb = ScrollAreaThumb;\nconst Corner = ScrollAreaCorner;\n\nexport {\n  createScrollAreaScope,\n  //\n  ScrollArea,\n  ScrollAreaViewport,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaCorner,\n  //\n  Root,\n  Viewport,\n  Scrollbar,\n  Thumb,\n  Corner,\n};\nexport type {\n  ScrollAreaProps,\n  ScrollAreaViewportProps,\n  ScrollAreaScrollbarProps,\n  ScrollAreaThumbProps,\n  ScrollAreaCornerProps,\n};\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "names": ["height", "width"], "sourceRoot": ""}