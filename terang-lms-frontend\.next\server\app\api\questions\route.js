try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="e1a5ecb0-4673-4757-8af7-8e5668af81f0",e._sentryDebugIdIdentifier="sentry-dbid-e1a5ecb0-4673-4757-8af7-8e5668af81f0")}catch(e){}"use strict";(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18490:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>b,serverHooks:()=>S,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>N});var s={};t.r(s),t.d(s,{DELETE:()=>w,GET:()=>I,HEAD:()=>v,OPTIONS:()=>A,PATCH:()=>z,POST:()=>g,PUT:()=>m});var o=t(3690),n=t(56947),i=t(75250),u=t(63033),a=t(62187),d=t(18621),p=t(32230),l=t(74683),c=t(7688);async function q(e){try{let r=e.nextUrl.searchParams,t=r.get("quizId"),s=r.get("teacherId");if(!t)return a.NextResponse.json({error:"Quiz ID required"},{status:400});if(s){let e=await d.db.select({quizId:p.quizzes.id,chapterId:p.quizzes.chapterId,teacherId:p.courses.teacherId}).from(p.quizzes).leftJoin(p.chapters,(0,l.eq)(p.quizzes.chapterId,p.chapters.id)).leftJoin(p.modules,(0,l.eq)(p.chapters.moduleId,p.modules.id)).leftJoin(p.courses,(0,l.eq)(p.modules.courseId,p.courses.id)).where((0,l.Uo)((0,l.eq)(p.quizzes.id,parseInt(t)),(0,l.eq)(p.courses.teacherId,parseInt(s)))).limit(1);if(0===e.length)return a.NextResponse.json({error:"Quiz not found or access denied"},{status:403})}let o=(await d.db.select().from(p.questions).where((0,l.eq)(p.questions.quizId,parseInt(t)))).map(e=>({...e,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation}));return a.NextResponse.json({questions:o})}catch(e){return console.error("Error fetching questions:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let{quizId:r,type:t="multiple_choice",question:s,options:o,essayAnswer:n,explanation:i,points:u=1,orderIndex:c,teacherId:q}=await e.json();if(!r||!s)return a.NextResponse.json({error:"Quiz ID and question are required"},{status:400});let x=await d.db.select({quizId:p.quizzes.id,chapterId:p.quizzes.chapterId,teacherId:p.courses.teacherId}).from(p.quizzes).leftJoin(p.chapters,(0,l.eq)(p.quizzes.chapterId,p.chapters.id)).leftJoin(p.modules,(0,l.eq)(p.chapters.moduleId,p.modules.id)).leftJoin(p.courses,(0,l.eq)(p.modules.courseId,p.courses.id)).where((0,l.eq)(p.quizzes.id,r)).limit(1);if(0===x.length)return a.NextResponse.json({error:"Quiz not found"},{status:404});if(q&&x[0].teacherId!==q)return a.NextResponse.json({error:"Not authorized to add questions to this quiz"},{status:403});let h=c;if(void 0===h){let e=await d.db.select({orderIndex:p.questions.orderIndex}).from(p.questions).where((0,l.eq)(p.questions.quizId,r));h=e.length>0?Math.max(...e.map(e=>e.orderIndex||0))+1:1}let f=await d.db.insert(p.questions).values({quizId:r,type:t,question:JSON.stringify(s),options:o?JSON.stringify(o):null,essayAnswer:""===n?null:n,explanation:i?JSON.stringify(i):null,points:u.toString(),orderIndex:h}).returning(),y={...f[0],question:f[0].question,options:f[0].options,essayAnswer:f[0].essayAnswer,explanation:f[0].explanation};return a.NextResponse.json({question:y,message:"Question created successfully"},{status:201})}catch(e){return console.error("Error creating question:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let h={...u},f="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=f?.getStore();o=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/questions",headers:o}).apply(t,s)}})}let I=y(q,"GET"),g=y(x,"POST"),m=y(void 0,"PUT"),z=y(void 0,"PATCH"),w=y(void 0,"DELETE"),v=y(void 0,"HEAD"),A=y(void 0,"OPTIONS"),b=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/questions/route",pathname:"/api/questions",filename:"route",bundlePath:"app/api/questions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\questions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:N,serverHooks:S}=b;function j(){return(0,i.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:N})}},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(18490));module.exports=s})();
//# sourceMappingURL=route.js.map