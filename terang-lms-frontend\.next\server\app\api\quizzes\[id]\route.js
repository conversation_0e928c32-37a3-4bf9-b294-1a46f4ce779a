try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="01e93a47-eafd-4239-b73d-fbd3b0502cc3",e._sentryDebugIdIdentifier="sentry-dbid-01e93a47-eafd-4239-b73d-fbd3b0502cc3")}catch(e){}"use strict";(()=>{var e={};e.id=2145,e.ids=[2145],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},27304:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>S,routeModule:()=>b,serverHooks:()=>R,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>j});var t={};s.r(t),s.d(t,{DELETE:()=>v,GET:()=>f,HEAD:()=>N,OPTIONS:()=>A,PATCH:()=>w,POST:()=>g,PUT:()=>y});var o=s(3690),i=s(56947),u=s(75250),n=s(63033),d=s(62187),a=s(18621),l=s(32230),p=s(74683),c=s(7688);async function q(e,{params:r}){try{let{id:e}=await r,s=parseInt(e);if(isNaN(s))return d.NextResponse.json({error:"Invalid quiz ID"},{status:400});let t=await a.db.select({id:l.quizzes.id,name:l.quizzes.name,description:l.quizzes.description,quizType:l.quizzes.quizType,timeLimit:l.quizzes.timeLimit,minimumScore:l.quizzes.minimumScore,isActive:l.quizzes.isActive,chapterId:l.quizzes.chapterId,moduleId:l.quizzes.moduleId,courseId:l.quizzes.courseId,createdAt:l.quizzes.createdAt,updatedAt:l.quizzes.updatedAt,chapterName:l.chapters.name,moduleName:l.modules.name,courseName:l.courses.name}).from(l.quizzes).leftJoin(l.chapters,(0,p.eq)(l.quizzes.chapterId,l.chapters.id)).leftJoin(l.modules,(0,p.or)((0,p.eq)(l.quizzes.moduleId,l.modules.id),(0,p.eq)(l.chapters.moduleId,l.modules.id))).leftJoin(l.courses,(0,p.or)((0,p.eq)(l.quizzes.courseId,l.courses.id),(0,p.eq)(l.modules.courseId,l.courses.id))).where((0,p.eq)(l.quizzes.id,s)).limit(1);if(0===t.length)return d.NextResponse.json({error:"Quiz not found"},{status:404});let o=t[0],i=(await a.db.select().from(l.questions).where((0,p.eq)(l.questions.quizId,s))).map(e=>({...e,question:e.question,options:e.options,essayAnswer:e.essayAnswer,explanation:e.explanation}));return d.NextResponse.json({quiz:{...o,questions:i}})}catch(e){return console.error("Error fetching quiz:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function z(e,{params:r}){try{let{id:s}=await r,t=parseInt(s);if(isNaN(t))return d.NextResponse.json({error:"Invalid quiz ID"},{status:400});let{name:o,description:i,quizType:u,timeLimit:n,minimumScore:c,isActive:q,teacherId:z,moduleId:m,courseId:h,questions:I}=await e.json();console.log("Updating quiz:",{quizId:t,quizType:u,moduleId:m,courseId:h,teacherId:z,name:o});let x=a.db.select({id:l.quizzes.id,quizType:l.quizzes.quizType,chapterId:l.quizzes.chapterId,moduleId:l.quizzes.moduleId,courseId:l.quizzes.courseId,teacherId:l.courses.teacherId,courseIdFromModule:l.modules.courseId,courseIdFromChapter:l.courses.id}).from(l.quizzes).leftJoin(l.chapters,(0,p.eq)(l.quizzes.chapterId,l.chapters.id)).leftJoin(l.modules,(0,p.or)((0,p.eq)(l.quizzes.moduleId,l.modules.id),(0,p.eq)(l.chapters.moduleId,l.modules.id))).leftJoin(l.courses,(0,p.or)((0,p.eq)(l.quizzes.courseId,l.courses.id),(0,p.eq)(l.modules.courseId,l.courses.id))).where((0,p.eq)(l.quizzes.id,t)).limit(1),f=await x;if(0===f.length)return d.NextResponse.json({error:"Quiz not found"},{status:404});let g=f[0];console.log("Found existing quiz:",g);let y=g.teacherId;if(z&&y!==z)return console.log("Permission denied:",{quizTeacherId:y,requestTeacherId:z}),d.NextResponse.json({error:"Not authorized to update this quiz"},{status:403});let w={...o&&{name:o},...i&&{description:i},...u&&{quizType:u},...void 0!==n&&{timeLimit:n},...void 0!==c&&{minimumScore:c.toString()},...void 0!==q&&{isActive:q},updatedAt:new Date};"module"===u&&m?(w.moduleId=m,w.chapterId=null,w.courseId=null):"final"===u&&h?(w.courseId=h,w.chapterId=null,w.moduleId=null):"chapter"===u&&(w.moduleId=null,w.courseId=null),console.log("Update data:",w);let v=await a.db.update(l.quizzes).set(w).where((0,p.eq)(l.quizzes.id,t)).returning();if(console.log("Updated quiz result:",v[0]),I&&Array.isArray(I)&&(console.log("Updating questions:",I.length),await a.db.delete(l.questions).where((0,p.eq)(l.questions.quizId,t)),I.length>0)){let e=I.map((e,r)=>({quizId:t,type:e.type||"multiple_choice",question:"string"==typeof e.question?e.question:JSON.stringify(e.question),options:e.options?JSON.stringify(e.options):null,essayAnswer:e.essayAnswer||null,explanation:e.explanation?JSON.stringify(e.explanation):null,points:e.points||1,orderIndex:void 0!==e.orderIndex?e.orderIndex:r+1}));console.log("Inserting questions:",e.length),await a.db.insert(l.questions).values(e)}return d.NextResponse.json({success:!0,quiz:v[0],message:"Quiz updated successfully"})}catch(e){return console.error("Error updating quiz:",e),d.NextResponse.json({error:"Internal server error",details:e},{status:500})}}async function m(e,{params:r}){try{let{id:s}=await r,t=parseInt(s);if(isNaN(t))return d.NextResponse.json({error:"Invalid quiz ID"},{status:400});let o=e.nextUrl.searchParams.get("teacherId"),i=a.db.select({id:l.quizzes.id,quizType:l.quizzes.quizType,chapterId:l.quizzes.chapterId,moduleId:l.quizzes.moduleId,courseId:l.quizzes.courseId,teacherId:l.courses.teacherId}).from(l.quizzes).leftJoin(l.chapters,(0,p.eq)(l.quizzes.chapterId,l.chapters.id)).leftJoin(l.modules,(0,p.or)((0,p.eq)(l.quizzes.moduleId,l.modules.id),(0,p.eq)(l.chapters.moduleId,l.modules.id))).leftJoin(l.courses,(0,p.or)((0,p.eq)(l.quizzes.courseId,l.courses.id),(0,p.eq)(l.modules.courseId,l.courses.id))).where((0,p.eq)(l.quizzes.id,t)).limit(1),u=await i;if(0===u.length)return d.NextResponse.json({error:"Quiz not found"},{status:404});if(o&&u[0].teacherId!==parseInt(o))return d.NextResponse.json({error:"Not authorized to delete this quiz"},{status:403});return await a.db.delete(l.quizAttempts).where((0,p.eq)(l.quizAttempts.quizId,t)),await a.db.delete(l.questions).where((0,p.eq)(l.questions.quizId,t)),await a.db.delete(l.quizzes).where((0,p.eq)(l.quizzes.id,t)),d.NextResponse.json({success:!0,message:"Quiz deleted successfully"})}catch(e){return console.error("Error deleting quiz:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}let h={...n},I="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;function x(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let o;try{let e=I?.getStore();o=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/quizzes/[id]",headers:o}).apply(s,t)}})}let f=x(q,"GET"),g=x(void 0,"POST"),y=x(z,"PUT"),w=x(void 0,"PATCH"),v=x(m,"DELETE"),N=x(void 0,"HEAD"),A=x(void 0,"OPTIONS"),b=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/quizzes/[id]/route",pathname:"/api/quizzes/[id]",filename:"route",bundlePath:"app/api/quizzes/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\quizzes\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:T,workUnitAsyncStorage:j,serverHooks:R}=b;function S(){return(0,u.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:j})}},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,1617,2957],()=>s(27304));module.exports=t})();
//# sourceMappingURL=route.js.map