try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="207ab6f0-563f-4818-955b-a530c1b8121b",e._sentryDebugIdIdentifier="sentry-dbid-207ab6f0-563f-4818-955b-a530c1b8121b")}catch(e){}"use strict";exports.id=5853,exports.ids=[5853],exports.modules={7953:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("ViewOffIcon",[["path",{d:"M22 8C22 8 18 14 12 14C6 14 2 8 2 8",stroke:"currentColor",key:"k0"}],["path",{d:"M15 13.5L16.5 16",stroke:"currentColor",key:"k1"}],["path",{d:"M20 11L22 13",stroke:"currentColor",key:"k2"}],["path",{d:"M2 13L4 11",stroke:"currentColor",key:"k3"}],["path",{d:"M9 13.5L7.5 16",stroke:"currentColor",key:"k4"}]])},9260:(e,t,r)=>{r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>s,Zp:()=>o,aR:()=>d,wL:()=>l});var a=r(91754);r(93491);var n=r(82233);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},13616:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("Mail01Icon",[["path",{d:"M2 6L8.91302 9.91697C11.4616 11.361 12.5384 11.361 15.087 9.91697L22 6",stroke:"currentColor",key:"k0"}],["path",{d:"M2.01577 13.4756C2.08114 16.5412 2.11383 18.0739 3.24496 19.2094C4.37608 20.3448 5.95033 20.3843 9.09883 20.4634C11.0393 20.5122 12.9607 20.5122 14.9012 20.4634C18.0497 20.3843 19.6239 20.3448 20.7551 19.2094C21.8862 18.0739 21.9189 16.5412 21.9842 13.4756C22.0053 12.4899 22.0053 11.5101 21.9842 10.5244C21.9189 7.45886 21.8862 5.92609 20.7551 4.79066C19.6239 3.65523 18.0497 3.61568 14.9012 3.53657C12.9607 3.48781 11.0393 3.48781 9.09882 3.53656C5.95033 3.61566 4.37608 3.65521 3.24495 4.79065C2.11382 5.92608 2.08114 7.45885 2.01576 10.5244C1.99474 11.5101 1.99475 12.4899 2.01577 13.4756Z",stroke:"currentColor",key:"k1"}]])},21626:(e,t,r)=>{r.d(t,{J:()=>d});var a=r(91754);r(93491);var n=r(66207),o=r(82233);function d({className:e,...t}){return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},22646:(e,t,r)=>{r.d(t,{C1:()=>w,bL:()=>v});var a=r(93491),n=r(42014),o=r(10158),d=r(18682),s=r(76322),i=r(78476),c=r(96432),l=r(55462),u=r(90604),p=r(91754),f="Checkbox",[k,C]=(0,o.A)(f),[b,y]=k(f);function x(e){let{__scopeCheckbox:t,checked:r,children:n,defaultChecked:o,disabled:d,form:i,name:c,onCheckedChange:l,required:u,value:k="on",internal_do_not_use_render:C}=e,[y,x]=(0,s.i)({prop:r,defaultProp:o??!1,onChange:l,caller:f}),[h,m]=a.useState(null),[v,g]=a.useState(null),w=a.useRef(!1),M=!h||!!i||!!h.closest("form"),j={checked:y,disabled:d,setChecked:x,control:h,setControl:m,name:c,form:i,value:k,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!A(o)&&o,isFormControl:M,bubbleInput:v,setBubbleInput:g};return(0,p.jsx)(b,{scope:t,...j,children:"function"==typeof C?C(j):n})}var h="CheckboxTrigger",m=a.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...o},s)=>{let{control:i,value:c,disabled:l,checked:f,required:k,setControl:C,setChecked:b,hasConsumerStoppedPropagationRef:x,isFormControl:m,bubbleInput:v}=y(h,e),g=(0,n.s)(s,C),w=a.useRef(f);return a.useEffect(()=>{let e=i?.form;if(e){let t=()=>b(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,b]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":A(f)?"mixed":f,"aria-required":k,"data-state":L(f),"data-disabled":l?"":void 0,disabled:l,value:c,...o,ref:g,onKeyDown:(0,d.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,d.m)(r,e=>{b(e=>!!A(e)||!e),v&&m&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});m.displayName=h;var v=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:n,defaultChecked:o,required:d,disabled:s,value:i,onCheckedChange:c,form:l,...u}=e;return(0,p.jsx)(x,{__scopeCheckbox:r,checked:n,defaultChecked:o,disabled:s,required:d,onCheckedChange:c,name:a,form:l,value:i,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(m,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(j,{__scopeCheckbox:r})]})})});v.displayName=f;var g="CheckboxIndicator",w=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,o=y(g,r);return(0,p.jsx)(l.C,{present:a||A(o.checked)||!0===o.checked,children:(0,p.jsx)(u.sG.span,{"data-state":L(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=g;var M="CheckboxBubbleInput",j=a.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:o,hasConsumerStoppedPropagationRef:d,checked:s,defaultChecked:l,required:f,disabled:k,name:C,value:b,form:x,bubbleInput:h,setBubbleInput:m}=y(M,e),v=(0,n.s)(r,m),g=(0,i.Z)(s),w=(0,c.X)(o);a.useEffect(()=>{if(!h)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!d.current;if(g!==s&&e){let r=new Event("click",{bubbles:t});h.indeterminate=A(s),e.call(h,!A(s)&&s),h.dispatchEvent(r)}},[h,g,s,d]);let j=a.useRef(!A(s)&&s);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??j.current,required:f,disabled:k,name:C,value:b,form:x,...t,tabIndex:-1,ref:v,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function L(e){return A(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=M},34305:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("LockIcon",[["path",{d:"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z",stroke:"currentColor",key:"k0"}],["path",{d:"M12 13C13.1046 13 14 12.1046 14 11C14 9.89543 13.1046 9 12 9C10.8954 9 10 9.89543 10 11C10 12.1046 10.8954 13 12 13ZM12 13L12 16",stroke:"currentColor",key:"k1"}]])},36256:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("ArrowLeft02Icon",[["path",{d:"M4 12L20 12",stroke:"currentColor",key:"k0"}],["path",{d:"M8.99996 17C8.99996 17 4.00001 13.3176 4 12C3.99999 10.6824 9 7 9 7",stroke:"currentColor",key:"k1"}]])},59672:(e,t,r)=>{r.d(t,{p:()=>o});var a=r(91754);r(93491);var n=r(82233);function o({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},66207:(e,t,r)=>{r.d(t,{b:()=>s});var a=r(93491),n=r(90604),o=r(91754),d=a.forwardRef((e,t)=>(0,o.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));d.displayName="Label";var s=d},68451:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("Award01Icon",[["path",{d:"M12 12V18",stroke:"currentColor",key:"k0"}],["path",{d:"M12 18C10.3264 18 8.86971 19.012 8.11766 20.505C7.75846 21.218 8.27389 22 8.95877 22H15.0412C15.7261 22 16.2415 21.218 15.8823 20.505C15.1303 19.012 13.6736 18 12 18Z",stroke:"currentColor",key:"k1"}],["path",{d:"M5 5H3.98471C2.99819 5 2.50493 5 2.20017 5.37053C1.89541 5.74106 1.98478 6.15597 2.16352 6.9858C2.50494 8.57086 3.24548 9.9634 4.2489 11",stroke:"currentColor",key:"k2"}],["path",{d:"M19 5H20.0153C21.0018 5 21.4951 5 21.7998 5.37053C22.1046 5.74106 22.0152 6.15597 21.8365 6.9858C21.4951 8.57086 20.7545 9.9634 19.7511 11",stroke:"currentColor",key:"k3"}],["path",{d:"M12 12C15.866 12 19 8.8831 19 5.03821C19 4.93739 18.9978 4.83707 18.9936 4.73729C18.9509 3.73806 18.9295 3.23845 18.2523 2.61922C17.5751 2 16.8247 2 15.324 2H8.67596C7.17526 2 6.42492 2 5.74772 2.61922C5.07051 3.23844 5.04915 3.73806 5.00642 4.73729C5.00215 4.83707 5 4.93739 5 5.03821C5 8.8831 8.13401 12 12 12Z",stroke:"currentColor",key:"k4"}]])},73225:(e,t,r)=>{r.d(t,{S:()=>s});var a=r(91754);r(93491);var n=r(22646),o=r(87435),d=r(82233);function s({className:e,...t}){return(0,a.jsx)(n.bL,{"data-slot":"checkbox",className:(0,d.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(o.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},77899:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("ViewIcon",[["path",{d:"M21.544 11.045C21.848 11.4713 22 11.6845 22 12C22 12.3155 21.848 12.5287 21.544 12.955C20.1779 14.8706 16.6892 19 12 19C7.31078 19 3.8221 14.8706 2.45604 12.955C2.15201 12.5287 2 12.3155 2 12C2 11.6845 2.15201 11.4713 2.45604 11.045C3.8221 9.12944 7.31078 5 12 5C16.6892 5 20.1779 9.12944 21.544 11.045Z",stroke:"currentColor",key:"k0"}],["path",{d:"M15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15C13.6569 15 15 13.6569 15 12Z",stroke:"currentColor",key:"k1"}]])},87435:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(55732).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},96805:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(15349).A)("ArrowRight02Icon",[["path",{d:"M20 12L4 12",stroke:"currentColor",key:"k0"}],["path",{d:"M15 17C15 17 20 13.3176 20 12C20 10.6824 15 7 15 7",stroke:"currentColor",key:"k1"}]])}};
//# sourceMappingURL=5853.js.map