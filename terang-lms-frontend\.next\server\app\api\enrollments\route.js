try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="5000dfc3-f50e-491c-a869-544dfb5ce291",e._sentryDebugIdIdentifier="sentry-dbid-5000dfc3-f50e-491c-a869-544dfb5ce291")}catch(e){}"use strict";(()=>{var e={};e.id=5229,e.ids=[5229],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")},96492:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>S,routeModule:()=>R,serverHooks:()=>A,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>v});var t={};s.r(t),s.d(t,{DELETE:()=>y,GET:()=>I,HEAD:()=>N,OPTIONS:()=>j,PATCH:()=>w,POST:()=>E,PUT:()=>g});var o=s(3690),n=s(56947),u=s(75250),l=s(63033),i=s(62187),d=s(18621),a=s(32230),c=s(74683),p=s(7688);async function m(e){try{let r=e.nextUrl.searchParams,s=r.get("type"),t=r.get("teacherId"),o=r.get("courseId"),n=r.get("classId");if(!t)return i.NextResponse.json({error:"Teacher ID required"},{status:400});if("course"===s){let e;if(o)e=(0,c.eq)(a.courseEnrollments.courseId,parseInt(o));else if(n){let r=(await d.db.select({id:a.courses.id}).from(a.courses).where((0,c.eq)(a.courses.teacherId,parseInt(t)))).map(e=>e.id);if(0===r.length)return i.NextResponse.json({enrollments:[]});e=(0,c.Uo)((0,c.eq)(a.courseEnrollments.classId,parseInt(n)),(0,c.or)(...r.map(e=>(0,c.eq)(a.courseEnrollments.courseId,e))))}else{let r=(await d.db.select({id:a.courses.id}).from(a.courses).where((0,c.eq)(a.courses.teacherId,parseInt(t)))).map(e=>e.id);if(0===r.length)return i.NextResponse.json({enrollments:[]});e=(0,c.or)(...r.map(e=>(0,c.eq)(a.courseEnrollments.courseId,e)))}let r=await d.db.select({id:a.courseEnrollments.id,courseId:a.courseEnrollments.courseId,classId:a.courseEnrollments.classId,enrolledAt:a.courseEnrollments.enrolledAt,courseName:a.courses.name,courseCode:a.courses.courseCode,className:a.classes.name}).from(a.courseEnrollments).leftJoin(a.courses,(0,c.eq)(a.courseEnrollments.courseId,a.courses.id)).leftJoin(a.classes,(0,c.eq)(a.courseEnrollments.classId,a.classes.id)).where(e);return i.NextResponse.json({enrollments:r})}if("student"!==s)return i.NextResponse.json({error:"Type parameter required (course or student)"},{status:400});{let e;if(o)e=(0,c.eq)(a.studentEnrollments.courseId,parseInt(o));else{let r=(await d.db.select({id:a.courses.id}).from(a.courses).where((0,c.eq)(a.courses.teacherId,parseInt(t)))).map(e=>e.id);if(0===r.length)return i.NextResponse.json({enrollments:[]});e=(0,c.or)(...r.map(e=>(0,c.eq)(a.studentEnrollments.courseId,e)))}let r=await d.db.select({id:a.studentEnrollments.id,studentId:a.studentEnrollments.studentId,courseId:a.studentEnrollments.courseId,enrolledAt:a.studentEnrollments.enrolledAt,completedAt:a.studentEnrollments.completedAt,finalScore:a.studentEnrollments.finalScore,certificateGenerated:a.studentEnrollments.certificateGenerated,studentName:a.users.name,studentEmail:a.users.email,courseName:a.courses.name,courseCode:a.courses.courseCode}).from(a.studentEnrollments).leftJoin(a.users,(0,c.eq)(a.studentEnrollments.studentId,a.users.id)).leftJoin(a.courses,(0,c.eq)(a.studentEnrollments.courseId,a.courses.id)).where(e);return i.NextResponse.json({enrollments:r})}}catch(e){return console.error("Error fetching enrollments:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e){try{let{type:r,courseId:s,classId:t,studentId:o,courseCode:n,teacherId:u}=await e.json();if(!u)return i.NextResponse.json({error:"Teacher ID required"},{status:400});if("course"===r){if(!s||!t)return i.NextResponse.json({error:"Course ID and Class ID are required"},{status:400});let e=await d.db.select().from(a.courses).where((0,c.Uo)((0,c.eq)(a.courses.id,s),(0,c.eq)(a.courses.teacherId,u))).limit(1);if(0===e.length)return i.NextResponse.json({error:"Course not found or not authorized"},{status:403});let r=await d.db.select().from(a.classes).where((0,c.Uo)((0,c.eq)(a.classes.id,t),(0,c.eq)(a.classes.institutionId,e[0].institutionId))).limit(1);if(0===r.length)return i.NextResponse.json({error:"Class not found or not in same institution"},{status:404});if((await d.db.select().from(a.courseEnrollments).where((0,c.Uo)((0,c.eq)(a.courseEnrollments.courseId,s),(0,c.eq)(a.courseEnrollments.classId,t))).limit(1)).length>0)return i.NextResponse.json({error:"Course already enrolled to this class"},{status:400});let o=await d.db.insert(a.courseEnrollments).values({courseId:s,classId:t}).returning();return i.NextResponse.json({enrollment:o[0],message:"Course enrolled to class successfully"},{status:201})}if("student"===r){if(!o||!s)return i.NextResponse.json({error:"Student ID and Course ID are required"},{status:400});let e=await d.db.select().from(a.courses).where((0,c.Uo)((0,c.eq)(a.courses.id,s),(0,c.eq)(a.courses.teacherId,u))).limit(1);if(0===e.length)return i.NextResponse.json({error:"Course not found or not authorized"},{status:403});let r=await d.db.select().from(a.users).where((0,c.Uo)((0,c.eq)(a.users.id,o),(0,c.eq)(a.users.role,"student"),(0,c.eq)(a.users.institutionId,e[0].institutionId))).limit(1);if(0===r.length)return i.NextResponse.json({error:"Student not found or not in same institution"},{status:404});if((await d.db.select().from(a.studentEnrollments).where((0,c.Uo)((0,c.eq)(a.studentEnrollments.studentId,o),(0,c.eq)(a.studentEnrollments.courseId,s))).limit(1)).length>0)return i.NextResponse.json({error:"Student already enrolled in this course"},{status:400});let t=await d.db.insert(a.studentEnrollments).values({studentId:o,courseId:s}).returning();return i.NextResponse.json({enrollment:t[0],message:"Student enrolled in course successfully"},{status:201})}{if("course_code"!==r)return i.NextResponse.json({error:"Invalid enrollment type"},{status:400});if(!o||!n)return i.NextResponse.json({error:"Student ID and Course Code are required"},{status:400});let e=await d.db.select().from(a.courses).where((0,c.eq)(a.courses.courseCode,n)).limit(1);if(0===e.length)return i.NextResponse.json({error:"Invalid course code"},{status:404});let s=await d.db.select().from(a.users).where((0,c.Uo)((0,c.eq)(a.users.id,o),(0,c.eq)(a.users.role,"student"),(0,c.eq)(a.users.institutionId,e[0].institutionId))).limit(1);if(0===s.length)return i.NextResponse.json({error:"Student not found or not in same institution"},{status:404});if((await d.db.select().from(a.studentEnrollments).where((0,c.Uo)((0,c.eq)(a.studentEnrollments.studentId,o),(0,c.eq)(a.studentEnrollments.courseId,e[0].id))).limit(1)).length>0)return i.NextResponse.json({error:"Already enrolled in this course"},{status:400});let t=await d.db.insert(a.studentEnrollments).values({studentId:o,courseId:e[0].id}).returning();return i.NextResponse.json({enrollment:t[0],course:e[0],message:"Successfully enrolled in course"},{status:201})}}catch(e){return console.error("Error creating enrollment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...l},x="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function h(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let o;try{let e=x?.getStore();o=e?.headers}catch{}return p.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/enrollments",headers:o}).apply(s,t)}})}let I=h(m,"GET"),E=h(f,"POST"),g=h(void 0,"PUT"),w=h(void 0,"PATCH"),y=h(void 0,"DELETE"),N=h(void 0,"HEAD"),j=h(void 0,"OPTIONS"),R=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/enrollments/route",pathname:"/api/enrollments",filename:"route",bundlePath:"app/api/enrollments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\enrollments\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:b,workUnitAsyncStorage:v,serverHooks:A}=R;function S(){return(0,u.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:v})}}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,1617,2957],()=>s(96492));module.exports=t})();
//# sourceMappingURL=route.js.map