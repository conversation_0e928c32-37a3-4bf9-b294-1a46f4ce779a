{"version": 3, "file": "7852.js", "mappings": "g9CAIA,SAASA,EAAW,CAClB,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,aAAW,aAAaC,YAAU,aAAc,GAAGH,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,kBACnI,CACA,SAASC,EAAe,WACtBC,CAAS,CACT,GAAGP,EACwB,EAC3B,MAAO,UAACQ,KAAAA,CAAGL,YAAU,kBAAkBI,UAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2FAA4FF,GAAa,GAAGP,CAAK,CAAEI,wBAAsB,iBAAiBC,0BAAwB,kBACzO,CACA,SAASK,EAAe,WACtBH,CAAS,CACT,GAAGP,EACwB,EAC3B,MAAO,UAACW,KAAAA,CAAGR,YAAU,kBAAkBI,UAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mCAAoCF,GAAa,GAAGP,CAAK,CAAEI,wBAAsB,iBAAiBC,0BAAwB,kBACjL,CACA,SAASO,EAAe,SACtBC,CAAO,CACPN,WAAS,CACT,GAAGP,EAGJ,EACC,IAAMc,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,IAC9B,MAAO,UAACD,EAAAA,CAAKX,YAAU,kBAAkBI,UAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CF,GAAa,GAAGP,CAAK,CAAEgB,sBAAoB,OAAOZ,wBAAsB,iBAAiBC,0BAAwB,kBACrN,CACA,SAASY,EAAe,WACtBV,CAAS,CACT,GAAGP,EAC0B,EAC7B,MAAO,UAACkB,OAAAA,CAAKf,YAAU,kBAAkBgB,KAAK,OAAOC,gBAAc,OAAOC,eAAa,OAAOd,UAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8BAA+BF,GAAa,GAAGP,CAAK,CAAEI,wBAAsB,iBAAiBC,0BAAwB,kBACnO,CACA,SAASiB,EAAoB,UAC3BC,CAAQ,WACRhB,CAAS,CACT,GAAGP,EACwB,EAC3B,MAAO,UAACW,KAAAA,CAAGR,YAAU,uBAAuBgB,KAAK,eAAeK,cAAY,OAAOjB,UAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mBAAoBF,GAAa,GAAGP,CAAK,CAAEI,wBAAsB,sBAAsBC,0BAAwB,0BAC3MkB,GAAY,UAACE,EAAAA,CAAYA,CAAAA,CAAAA,IAEhC,gBCnCA,IAAMC,EAAiD,CACrD,aAAc,CAAC,CACbC,MAAO,YACPC,KAAM,YACR,EAAE,CACF,sBAAuB,CAAC,CACtBD,MAAO,YACPC,KAAM,YACR,EAAG,CACDD,MAAO,WACPC,KAAM,qBACR,EAAE,CACF,qBAAsB,CAAC,CACrBD,MAAO,YACPC,KAAM,YACR,EAAG,CACDD,MAAO,UACPC,KAAM,oBACR,EAAE,CACF,WAAY,CAAC,CACXD,MAAO,OACPC,KAAM,GACR,EAAG,CACDD,MAAO,oBACPC,KAAM,UACR,EAAE,CACF,cAAe,CAAC,CACdD,MAAO,OACPC,KAAM,GACR,EAAG,CACDD,MAAO,aACPC,KAAM,aACR,EAAE,EC1BE,EAAQ,cAAiB,SAbK,CAaI,CAbF,MAAQ,EAAE,EAAG,YAAc,KAAK,CAAS,QAAC,CAAC,GCG1E,SAASC,IACd,IAAMC,EFsCD,SAASC,EACd,GEvC4BA,CFuCtBC,EAAWC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,GAiB5B,MAhBoBC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAE1B,GAAIR,CAAY,CAACM,EAAS,CACxB,CAD0B,MACnBN,CAAY,CAACM,EAAS,CAI/B,IAAMG,EAAWH,EAASI,KAAK,CAAC,KAAKC,MAAM,CAACC,SAC5C,OAAOH,EAASI,GAAG,CAAC,CAACC,EAASC,KAC5B,IAAMC,EAAO,CAAC,CAAC,EAAEP,EAASQ,KAAK,CAAC,EAAGF,EAAQ,GAAGG,IAAI,CAAC,MAAM,CACzD,MAAO,CACLjB,MAAOa,EAAQK,MAAM,CAAC,GAAGC,WAAW,GAAKN,EAAQG,KAAK,CAAC,GACvDf,KAAMc,CACR,CACF,EACF,EAAG,CAACV,EAAS,CAEf,WExDE,GAAwB,CAApBF,EAAMiB,MAAM,CAAe,KACxB,UAAChD,EAAUA,CAACiB,OAADjB,eAAqB,aAAaK,wBAAsB,cAAcC,0BAAwB,2BAC5G,UAACC,EAAcA,CAACU,WAADV,WAAqB,iBAAiBD,0BAAwB,2BAC1EyB,EAAMS,GAAG,CAAC,CAACS,EAAMP,IAAU,WAACQ,EAAAA,QAAQA,CAAAA,WAChCR,IAAUX,EAAMiB,MAAM,CAAG,GAAK,UAACrC,EAAcA,CAACH,UAAU,CAAXG,0BAC1C,UAACE,EAAcA,CAACsC,KAAMF,EAAKpB,IAAI,UAAGoB,EAAKrB,KAAK,KAE/Cc,EAAQX,EAAMiB,MAAM,CAAG,GAAK,UAACzB,EAAmBA,CAACf,UAAU,MAAXe,qBAC7C,UAAC6B,EAAKA,CAAAA,EAAAA,GAETV,IAAUX,EAAMiB,MAAM,CAAG,GAAK,UAAC9B,EAAcA,UAAE+B,EAAF/B,KAAY,KAPnB+B,EAAKrB,KAAK,MAW7D,iDChB+C,MAAQ,cAAC,sBAAsB,mbAAmb,IAAyB,iBCN1hB,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,CAEjN,uCAA0M,kBCR1M,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,CAEjN,uCAA0M,6GCkB1M,IAAMyB,EAAoBC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,MAAoCC,GAC9DC,EAAgB,KAC3B,IAAMC,EAAUC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACL,GAC3B,GAAI,CAACI,EACH,MAAM,CADM,KACI,2DAElB,OAAOA,CACT,EAAE,EAImE,CAAC,CACpEjC,UAAQ,CACT,IACC,GAAM,CAACmC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAASG,EAAAA,EAAkBA,EACjE,CAACC,EAAiBC,EAAmB,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7DM,EAAc,sBACdC,EAAuB,+BAI7BC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAsDRC,CArD0B,KACxB,GAAI,CAEF,IAAMC,EAAiBC,aAAaC,OAAO,CAACL,GAC5C,GAAIG,EAAgB,CAClB,IAAMG,EAAuCC,KAAKC,KAAK,CAACL,EAIpDM,CAHQC,KAAKD,GAAG,GAGVH,EAAaK,cAAc,EACnCb,EAAmBQ,EAAaT,eAAe,EAC/CL,EAAcc,EAAaT,eAAe,CAACjB,MAAM,CAAG,GAChD0B,EAAaT,eAAe,CAACjB,MAAM,CAAG,GAAG,EAC7B0B,EAAaT,eAAe,CAAC,EAAE,GAAG,aAIrCe,UAAU,CAACZ,GAE1B,GANkF,GAOpF,CAGA,IAAMa,EAAST,aAAaC,OAAO,CAACN,GACpC,GAAIc,EAAQ,CACV,IAAMC,EAAiCP,KAAKC,KAAK,CAACK,GAIlD,GAHYH,KAAKD,GAAG,GAGVK,EAAeH,cAAc,CAAE,CACvCnB,EAAcsB,EAAevB,UAAU,EACvCI,EAAcmB,EAAepB,UAAU,EACvCI,EAAmB,CAACgB,EAAepB,UAAU,CAAC,EAG9C,IAAMY,EAAuC,CAC3CT,gBAAiB,CAACiB,EAAepB,UAAU,CAAC,CAC5CqB,oBAAqBD,EAAeC,mBAAmB,CACvDJ,eAAgBG,EAAeH,cAAc,EAE/CP,aAAaY,OAAO,CAAChB,EAAsBO,KAAKU,SAAS,CAACX,IAC1DF,aAAaQ,UAAU,CAACb,EAC1B,MAEEK,CAFK,KADiC,OAGzBQ,UAAU,CAACb,EAE5B,CACF,CAAE,MAAOmB,EAAO,CACdC,QAAQD,KAAK,CAAC,kCAAmCA,GACjDd,aAAaQ,UAAU,CAACb,GACxBK,aAAaQ,UAAU,CAACZ,EAC1B,EACF,GAEF,EAAG,EAAE,EAGL,IAAMoB,EAAwB,IAC5B,IAAMX,EAAMC,KAAKD,GAAG,GACpB,GAAI,CAEFX,EAAmBuB,IAYjB,IAVIC,EAUEhB,EAAuC,CAC3CT,eAAAA,CARAyB,CAQiBA,CAZOD,EAAKE,IAAI,CAACC,GAAKA,EAAEC,EAAE,GAAKC,EAAOD,EAAE,EAIxCJ,EAAKjD,GAAG,CAACoD,GAAKA,EAAEC,EAAE,GAAKC,EAAOD,EAAE,CAAGC,EAASF,GAG5C,IAAIH,EAAMK,EAAO,CAMlCX,oBAAqBN,EACrBE,eAAgBF,KAClB,CADwBkB,CAGxB,OADAvB,aAAaY,OAAO,CAAChB,EAAsBO,KAAKU,SAAS,CAACX,IACnDgB,CACT,GAGAM,WAAW,KACTxB,aAAaQ,UAAU,CAACZ,GACxBR,GAAc,GACdM,EAAmB,EAAE,EACrBH,EAAcC,EAAAA,EAAkBA,CAClC,EA5FoB,CA4FjB+B,GACL,CAAE,KA7F8B,CA6FvBT,EAAO,CACdC,EA9FoC,MA8F5BD,KAAK,CAAC,iBA9FmD,oBA8FbA,EACtD,CACF,EAsDA,MAAO,UAACjC,EAAkB4C,QAAQ,EAACC,MAVrB,CAU4BA,WATxCvC,EACAG,aACAqC,eA9CqB,KACrBvC,EAAc,IACd,IAAMwC,EAAgB,CACpB,GAAGpC,EAAAA,EAAkB,CACrBqC,OAAQ,aACV,EACAtC,EAAcqC,GACdZ,EAAsBY,EACxB,EAuCEE,2BAtCiC,IACjC1C,GAAc,GACd,IAAMwC,EAAgB,CACpB,GAAGN,CAAM,CACTO,OAAQ,cACRE,cAAe,CACjB,EACAxC,EAAcqC,GACdZ,EAAsBY,EACxB,EA8BEI,qBA7B2B,IAEvB1C,EAAW+B,EAAE,GAAKO,EAAcP,EAAE,EAAE,EACxBO,GAIhBlC,EAAmBuB,GAAQA,EAAKjD,GAAG,CAACsD,GAAUA,EAAOD,EAAE,GAAKO,EAAcP,EAAE,CAAGO,EAAgBN,IAG3FnC,GACF6B,EAAsBY,EAE1B,KAHkB,aAoBhBnC,EACAwC,mBAf0BC,GACnBzC,EAAgB0B,IAAI,CAACG,GAAUA,EAAOD,EAAE,GAAKa,GAepDC,cAXoB,GACb1C,EAAgB2C,IAAI,CAACd,GAAUA,EAAOD,EAAE,GAAKa,EAWtD,EACiDzF,sBAAoB,6BAA6BZ,wBAAsB,qBAAqBC,0BAAwB,kCAChKkB,GAEP,EAAE,+CChM6C,MAAQ,cAAC,sBAAsB,yRAAyR,WAAW,sKAAsK,IAAyB,4GCFjjB,IAAMqF,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACb9G,CAAS,SACTwG,CAAO,CACPlG,WAAU,CAAK,CACf,GAAGb,EAGJ,EACC,IAAMc,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKX,YAAU,QAAQI,UAAWE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACmG,EAAc,SACzDG,CACF,GAAIxG,GAAa,GAAGP,CAAK,CAAEgB,sBAAoB,OAAOZ,wBAAsB,QAAQC,0BAAwB,aAC9G,oCEVI,sBAAsB,2MDZbiH,EAAqB,CAChC3F,KADW2F,CACJ,uBACPC,WAAAA,CAAa,6CACf,EACe,eAAeC,EAAe,UAC3CjG,CAAQ,CADoBiG,EAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkCxB,KAAAA,GAAU,OAChE,MAAO4B,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,kBAAAA,CAAAA,CAAmB9G,qBAAAA,CAAoB,qBAAqBZ,uBAAAA,CAAsB,iBAAiBC,yBAAAA,CAAwB,aAC/H,SAAAwH,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACE,EAAAA,OAAAA,CAAAA,CAAK/G,qBAAAA,CAAoB,OAAOX,yBAAAA,CAAwB,aACvD,SAAA2H,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBN,WAAAA,CAAaA,EAAa3G,SAAb2G,YAAa3G,CAAoB,kBAAkBX,yBAAAA,CAAwB,uBACvGwH,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACK,EAAAA,OAAAA,CAAAA,CAAWlH,qBAAAA,CAAoB,aAAaX,yBAAAA,CAAwB,eACrE2H,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAanH,qBAAAA,CAAoB,eAAeX,yBAAAA,CAAwB,uBACvEwH,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,EAAAA,OAAAA,CAAAA,CAAOpH,qBAAAA,CAAoB,SAASX,yBAAAA,CAAwB,eAE5DkB,WAMb,CCxBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZ8G,EAO8B,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,kBAAkB,CAClC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN", "sources": ["webpack://terang-lms-ui/./src/components/ui/breadcrumb.tsx", "webpack://terang-lms-ui/./src/hooks/use-breadcrumbs.tsx", "webpack://terang-lms-ui/../../../src/icons/slash.ts", "webpack://terang-lms-ui/./src/components/breadcrumbs.tsx", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/play_icon.js", "webpack://terang-lms-ui/?2b73", "webpack://terang-lms-ui/?d73a", "webpack://terang-lms-ui/./src/contexts/enrollment-context.tsx", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/user_icon.js", "webpack://terang-lms-ui/./src/components/ui/badge.tsx", "webpack://terang-lms-ui/src/app/(students-page)/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module"], "sourcesContent": ["import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Breadcrumb({\n  ...props\n}: React.ComponentProps<'nav'>) {\n  return <nav aria-label='breadcrumb' data-slot='breadcrumb' {...props} data-sentry-component=\"Breadcrumb\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbList({\n  className,\n  ...props\n}: React.ComponentProps<'ol'>) {\n  return <ol data-slot='breadcrumb-list' className={cn('text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5', className)} {...props} data-sentry-component=\"BreadcrumbList\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbItem({\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='breadcrumb-item' className={cn('inline-flex items-center gap-1.5', className)} {...props} data-sentry-component=\"BreadcrumbItem\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbLink({\n  asChild,\n  className,\n  ...props\n}: React.ComponentProps<'a'> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'a';\n  return <Comp data-slot='breadcrumb-link' className={cn('hover:text-foreground transition-colors', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"BreadcrumbLink\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbPage({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) {\n  return <span data-slot='breadcrumb-page' role='link' aria-disabled='true' aria-current='page' className={cn('text-foreground font-normal', className)} {...props} data-sentry-component=\"BreadcrumbPage\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbSeparator({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='breadcrumb-separator' role='presentation' aria-hidden='true' className={cn('[&>svg]:size-3.5', className)} {...props} data-sentry-component=\"BreadcrumbSeparator\" data-sentry-source-file=\"breadcrumb.tsx\">\r\n      {children ?? <ChevronRight />}\r\n    </li>;\n}\nfunction BreadcrumbEllipsis({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) {\n  return <span data-slot='breadcrumb-ellipsis' role='presentation' aria-hidden='true' className={cn('flex size-9 items-center justify-center', className)} {...props} data-sentry-component=\"BreadcrumbEllipsis\" data-sentry-source-file=\"breadcrumb.tsx\">\r\n      <MoreHorizontal className='size-4' data-sentry-element=\"MoreHorizontal\" data-sentry-source-file=\"breadcrumb.tsx\" />\r\n      <span className='sr-only'>More</span>\r\n    </span>;\n}\nexport { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator, BreadcrumbEllipsis };", "'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\ntype BreadcrumbItem = {\n  title: string;\n  link: string;\n};\n\n// This allows to add custom title as well\nconst routeMapping: Record<string, BreadcrumbItem[]> = {\n  '/dashboard': [{\n    title: 'Dashboard',\n    link: '/dashboard'\n  }],\n  '/dashboard/employee': [{\n    title: 'Dashboard',\n    link: '/dashboard'\n  }, {\n    title: 'Employee',\n    link: '/dashboard/employee'\n  }],\n  '/dashboard/product': [{\n    title: 'Dashboard',\n    link: '/dashboard'\n  }, {\n    title: 'Product',\n    link: '/dashboard/product'\n  }],\n  '/courses': [{\n    title: 'Home',\n    link: '/'\n  }, {\n    title: 'Available Courses',\n    link: '/courses'\n  }],\n  '/my-courses': [{\n    title: 'Home',\n    link: '/'\n  }, {\n    title: 'My Courses',\n    link: '/my-courses'\n  }]\n  // Add more custom mappings as needed\n};\nexport function useBreadcrumbs() {\n  const pathname = usePathname();\n  const breadcrumbs = useMemo(() => {\n    // Check if we have a custom mapping for this exact path\n    if (routeMapping[pathname]) {\n      return routeMapping[pathname];\n    }\n\n    // If no exact match, fall back to generating breadcrumbs from the path\n    const segments = pathname.split('/').filter(Boolean);\n    return segments.map((segment, index) => {\n      const path = `/${segments.slice(0, index + 1).join('/')}`;\n      return {\n        title: segment.charAt(0).toUpperCase() + segment.slice(1),\n        link: path\n      };\n    });\n  }, [pathname]);\n  return breadcrumbs;\n}", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M22 2 2 22', key: 'y4kqgn' }]];\n\n/**\n * @component @name Slash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMiAyIDIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/slash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Slash = createLucideIcon('Slash', __iconNode);\n\nexport default Slash;\n", "'use client';\n\nimport { Breadcrumb, Bread<PERSON>rumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';\nimport { useBreadcrumbs } from '@/hooks/use-breadcrumbs';\nimport { Slash } from 'lucide-react';\nimport { Fragment } from 'react';\nexport function Breadcrumbs() {\n  const items = useBreadcrumbs();\n  if (items.length === 0) return null;\n  return <Breadcrumb data-sentry-element=\"Breadcrumb\" data-sentry-component=\"Breadcrumbs\" data-sentry-source-file=\"breadcrumbs.tsx\">\r\n      <BreadcrumbList data-sentry-element=\"BreadcrumbList\" data-sentry-source-file=\"breadcrumbs.tsx\">\r\n        {items.map((item, index) => <Fragment key={item.title}>\r\n            {index !== items.length - 1 && <BreadcrumbItem className='hidden md:block'>\r\n                <BreadcrumbLink href={item.link}>{item.title}</BreadcrumbLink>\r\n              </BreadcrumbItem>}\r\n            {index < items.length - 1 && <BreadcrumbSeparator className='hidden md:block'>\r\n                <Slash />\r\n              </BreadcrumbSeparator>}\r\n            {index === items.length - 1 && <BreadcrumbPage>{item.title}</BreadcrumbPage>}\r\n          </Fragment>)}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>;\n}", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport C from\"../create-hugeicon-component.js\";const o=C(\"PlayIcon\",[[\"path\",{d:\"M18.8906 12.846C18.5371 14.189 16.8667 15.138 13.5257 17.0361C10.296 18.8709 8.6812 19.7884 7.37983 19.4196C6.8418 19.2671 6.35159 18.9776 5.95624 18.5787C5 17.6139 5 15.7426 5 12C5 8.2574 5 6.3861 5.95624 5.42132C6.35159 5.02245 6.8418 4.73288 7.37983 4.58042C8.6812 4.21165 10.296 5.12907 13.5257 6.96393C16.8667 8.86197 18.5371 9.811 18.8906 11.154C19.0365 11.7084 19.0365 12.2916 18.8906 12.846Z\",stroke:\"currentColor\",key:\"k0\"}]]);export{o as default};\n", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"EnrollmentProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\contexts\\\\enrollment-context.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>ja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"EnrollmentProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\contexts\\\\enrollment-context.tsx\");\n", "'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { architectureCourse } from '@/constants/shared-course-data';\nimport { Course } from '@/types/lms';\ninterface EnrollmentData {\n  isEnrolled: boolean;\n  courseData: Course;\n  enrollmentTimestamp: number;\n  expirationTime: number; // 10 minutes in milliseconds\n}\ninterface MultipleEnrollmentData {\n  enrolledCourses: Course[];\n  enrollmentTimestamp: number;\n  expirationTime: number; // 10 minutes in milliseconds\n}\ninterface EnrollmentContextType {\n  isEnrolled: boolean;\n  courseData: Course;\n  enrollInCourse: () => void;\n  enrollInCourseWithPurchase: (course: Course) => void;\n  updateCourseProgress: (updatedCourse: Course) => void;\n  enrolledCourses: Course[];\n  isEnrolledInCourse: (courseId: string) => boolean;\n  getCourseById: (courseId: string) => Course | undefined;\n}\nconst EnrollmentContext = createContext<EnrollmentContextType | undefined>(undefined);\nexport const useEnrollment = () => {\n  const context = useContext(EnrollmentContext);\n  if (!context) {\n    throw new Error('useEnrollment must be used within an EnrollmentProvider');\n  }\n  return context;\n};\ninterface EnrollmentProviderProps {\n  children: ReactNode;\n}\nexport const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({\n  children\n}) => {\n  const [isEnrolled, setIsEnrolled] = useState(false);\n  const [courseData, setCourseData] = useState<Course>(architectureCourse);\n  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);\n  const STORAGE_KEY = 'lms-enrollment-data';\n  const MULTIPLE_STORAGE_KEY = 'lms-multiple-enrollment-data';\n  const EXPIRATION_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds\n\n  // Load persisted data on component mount\n  useEffect(() => {\n    const loadPersistedData = () => {\n      try {\n        // Try to load multiple enrollment data first\n        const multipleStored = localStorage.getItem(MULTIPLE_STORAGE_KEY);\n        if (multipleStored) {\n          const multipleData: MultipleEnrollmentData = JSON.parse(multipleStored);\n          const now = Date.now();\n\n          // Check if enrollment has expired\n          if (now < multipleData.expirationTime) {\n            setEnrolledCourses(multipleData.enrolledCourses);\n            setIsEnrolled(multipleData.enrolledCourses.length > 0);\n            if (multipleData.enrolledCourses.length > 0) {\n              setCourseData(multipleData.enrolledCourses[0]); // Set first course as primary\n            }\n          } else {\n            // Clear expired data\n            localStorage.removeItem(MULTIPLE_STORAGE_KEY);\n          }\n          return;\n        }\n\n        // Fallback to old single enrollment data for backward compatibility\n        const stored = localStorage.getItem(STORAGE_KEY);\n        if (stored) {\n          const enrollmentData: EnrollmentData = JSON.parse(stored);\n          const now = Date.now();\n\n          // Check if enrollment has expired\n          if (now < enrollmentData.expirationTime) {\n            setIsEnrolled(enrollmentData.isEnrolled);\n            setCourseData(enrollmentData.courseData);\n            setEnrolledCourses([enrollmentData.courseData]);\n\n            // Migrate to new format\n            const multipleData: MultipleEnrollmentData = {\n              enrolledCourses: [enrollmentData.courseData],\n              enrollmentTimestamp: enrollmentData.enrollmentTimestamp,\n              expirationTime: enrollmentData.expirationTime\n            };\n            localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\n            localStorage.removeItem(STORAGE_KEY); // Remove old format\n          } else {\n            // Clear expired data\n            localStorage.removeItem(STORAGE_KEY);\n          }\n        }\n      } catch (error) {\n        console.error('Failed to load enrollment data:', error);\n        localStorage.removeItem(STORAGE_KEY);\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\n      }\n    };\n    loadPersistedData();\n  }, []);\n\n  // Persist enrollment data to localStorage\n  const persistEnrollmentData = (course: Course) => {\n    const now = Date.now();\n    try {\n      // Update enrolled courses state\n      setEnrolledCourses(prev => {\n        const isAlreadyEnrolled = prev.some(c => c.id === course.id);\n        let updatedCourses;\n        if (isAlreadyEnrolled) {\n          // Update existing course\n          updatedCourses = prev.map(c => c.id === course.id ? course : c);\n        } else {\n          // Add new course\n          updatedCourses = [...prev, course];\n        }\n\n        // Save to localStorage with new format\n        const multipleData: MultipleEnrollmentData = {\n          enrolledCourses: updatedCourses,\n          enrollmentTimestamp: now,\n          expirationTime: now + EXPIRATION_TIME\n        };\n        localStorage.setItem(MULTIPLE_STORAGE_KEY, JSON.stringify(multipleData));\n        return updatedCourses;\n      });\n\n      // Set up automatic cleanup after expiration\n      setTimeout(() => {\n        localStorage.removeItem(MULTIPLE_STORAGE_KEY);\n        setIsEnrolled(false);\n        setEnrolledCourses([]);\n        setCourseData(architectureCourse);\n      }, EXPIRATION_TIME);\n    } catch (error) {\n      console.error('Failed to persist enrollment data:', error);\n    }\n  };\n  const enrollInCourse = () => {\n    setIsEnrolled(true);\n    const updatedCourse = {\n      ...architectureCourse,\n      status: 'in-progress' as const\n    };\n    setCourseData(updatedCourse);\n    persistEnrollmentData(updatedCourse);\n  };\n  const enrollInCourseWithPurchase = (course: Course) => {\n    setIsEnrolled(true);\n    const updatedCourse = {\n      ...course,\n      status: 'in-progress' as const,\n      totalProgress: 0\n    };\n    setCourseData(updatedCourse);\n    persistEnrollmentData(updatedCourse);\n  };\n  const updateCourseProgress = (updatedCourse: Course) => {\n    // Update the primary courseData if it's the same course\n    if (courseData.id === updatedCourse.id) {\n      setCourseData(updatedCourse);\n    }\n\n    // Update the course in enrolledCourses array\n    setEnrolledCourses(prev => prev.map(course => course.id === updatedCourse.id ? updatedCourse : course));\n\n    // Update persisted data with new progress\n    if (isEnrolled) {\n      persistEnrollmentData(updatedCourse);\n    }\n  };\n\n  // Check if user is enrolled in a specific course\n  const isEnrolledInCourse = (courseId: string): boolean => {\n    return enrolledCourses.some(course => course.id === courseId);\n  };\n\n  // Get a specific course by ID\n  const getCourseById = (courseId: string): Course | undefined => {\n    return enrolledCourses.find(course => course.id === courseId);\n  };\n  const value = {\n    isEnrolled,\n    courseData,\n    enrollInCourse,\n    enrollInCourseWithPurchase,\n    updateCourseProgress,\n    enrolledCourses,\n    isEnrolledInCourse,\n    getCourseById\n  };\n  return <EnrollmentContext.Provider value={value} data-sentry-element=\"EnrollmentContext.Provider\" data-sentry-component=\"EnrollmentProvider\" data-sentry-source-file=\"enrollment-context.tsx\">\r\n      {children}\r\n    </EnrollmentContext.Provider>;\n};", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"UserIcon\",[[\"path\",{d:\"M6.57757 15.4816C5.1628 16.324 1.45336 18.0441 3.71266 20.1966C4.81631 21.248 6.04549 22 7.59087 22H16.4091C17.9545 22 19.1837 21.248 20.2873 20.1966C22.5466 18.0441 18.8372 16.324 17.4224 15.4816C14.1048 13.5061 9.89519 13.5061 6.57757 15.4816Z\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M16.5 6.5C16.5 8.98528 14.4853 11 12 11C9.51472 11 7.5 8.98528 7.5 6.5C7.5 4.01472 9.51472 2 12 2C14.4853 2 16.5 4.01472 16.5 6.5Z\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport { EnrollmentProvider } from '@/contexts/enrollment-context';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Student Portal - LMS',\n  description: 'Learning Management System - Student Portal'\n};\nexport default async function StudentsLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <EnrollmentProvider data-sentry-element=\"EnrollmentProvider\" data-sentry-component=\"StudentsLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <KBar data-sentry-element=\"KBar\" data-sentry-source-file=\"layout.tsx\">\r\n        <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n          <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n          <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n            <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n            {/* page main content */}\r\n            {children}\r\n            {/* page main content ends */}\r\n          </SidebarInset>\r\n        </SidebarProvider>\r\n      </KBar>\r\n    </EnrollmentProvider>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/(students-page)',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/(students-page)',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/(students-page)',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/(students-page)',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n"], "names": ["Breadcrumb", "props", "nav", "aria-label", "data-slot", "data-sentry-component", "data-sentry-source-file", "BreadcrumbList", "className", "ol", "cn", "BreadcrumbItem", "li", "BreadcrumbLink", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "data-sentry-element", "BreadcrumbPage", "span", "role", "aria-disabled", "aria-current", "BreadcrumbSeparator", "children", "aria-hidden", "ChevronRight", "routeMapping", "title", "link", "Breadcrumbs", "items", "useBreadcrumbs", "pathname", "usePathname", "useMemo", "segments", "split", "filter", "Boolean", "map", "segment", "index", "path", "slice", "join", "char<PERSON>t", "toUpperCase", "length", "item", "Fragment", "href", "Slash", "EnrollmentContext", "createContext", "undefined", "useEnrollment", "context", "useContext", "isEnrolled", "setIsEnrolled", "useState", "courseData", "setCourseData", "architectureCourse", "enrolledCourses", "setEnrolledCourses", "STORAGE_KEY", "MULTIPLE_STORAGE_KEY", "useEffect", "loadPersistedData", "multipleStored", "localStorage", "getItem", "multipleData", "JSON", "parse", "now", "Date", "expirationTime", "removeItem", "stored", "enrollmentData", "enrollmentTimestamp", "setItem", "stringify", "error", "console", "persistEnrollmentData", "prev", "updatedCourses", "some", "c", "id", "course", "EXPIRATION_TIME", "setTimeout", "Provider", "value", "enrollInCourse", "updatedCourse", "status", "enrollInCourseWithPurchase", "totalProgress", "updateCourseProgress", "isEnrolledInCourse", "courseId", "getCourseById", "find", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "metadata", "description", "StudentsLayout", "cookieStore", "cookies", "defaultOpen", "get", "_jsx", "EnrollmentProvider", "KBar", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "serverComponentModule.default"], "sourceRoot": ""}