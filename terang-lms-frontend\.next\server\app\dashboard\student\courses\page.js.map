{"version": 3, "file": "../app/dashboard/student/courses/page.js", "mappings": "yeAIe,SAASA,EAAc,UACpCC,CAAQ,CAGT,EAKC,MAAO,+BAAGA,GACZ,0CCdA,oLCKA,IAAMC,EAAWC,EAAAA,UAAgB,CAAiH,CAAC,WACjJC,CAAS,CACTC,OAAK,CACL,GAAGC,EACJ,CAAEC,IAAQ,UAACC,EAAAA,EAAsB,EAACD,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEL,GAAa,GAAGE,CAAK,UAC7I,UAACE,EAAAA,EAA2B,EAACJ,UAAU,iDAAiDM,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,IAAON,EAAAA,GAAS,EAAG,EAAE,CAAC,OAGpDH,EAASU,WAAW,CAAGJ,EAAAA,EAAsB,CAACI,WAAW,wBCdzD,4CCAA,uCAA4K,oICE5K,SAASC,EAAK,WACZT,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,OAAOX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBd,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,WACjBf,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,aAAaX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBhB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,mBAAmBX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBjB,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,eAAeX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,CAClBlB,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACQ,MAAAA,CAAIC,YAAU,cAAcX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CL,GAAa,GAAGE,CAAK,CAAEU,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,8FCAA,sCAA0L,CAE1L,uCAAkM,CAElM,sCAA6L,CAE7L,uCAAiN,yBCNjN,mECAA,yGCAA,6ECgBM,MAAO,cAAiB,QAbM,CAaE,CAAU,SAbC,EAAE,OAAQ,oBAAsB,KAAK,CAAS,QAAC,CAAC,2BCHjG,uECmBM,MAAS,cAAiB,UAhBI,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,KAAK,GAAK,UAAU,EACxD,CAAC,MAAQ,EAAE,EAAG,CAAkB,oBAAK,SAAU,EACjD,0BCNA,kDCAA,yCCAA,uCAA4K,yBCA5K,uGCAA,iECAA,kDCAA,sOCiBM,EAAY,OAGZ,CAAC,EAAmB,EAAe,CAAI,OAAkB,CAAC,EAAW,CACzE,CADuC,CACvC,EAA2B,CAC5B,EACK,EAA2B,QAA2B,CAAC,EAWvD,CAAC,EAAc,EAAc,CAAI,EAAoC,GA6BrE,EAAa,IA7BgB,QA6BhB,CACjB,CAAC,EAA+B,KAC9B,GAAM,CACJ,cACA,MAAO,gBACP,eACA,EACA,cAAc,iBACd,iBACA,EAAiB,YACjB,GAAG,EACL,CAAI,EACE,EAAY,QAAY,CAAC,GAAG,CAC3B,EAAO,EAAQ,CAAI,OAAoB,CAAC,CAC7C,KAAM,EACN,SAAU,EACV,YAAa,GAAgB,GAC7B,OAAQ,CACV,CAAC,EAED,MACE,UAAC,GACC,MAAO,EACP,OAAQ,OAAK,CAAC,QACd,EACA,cAAe,cACf,EACA,IAAK,iBACL,EAEA,mBAAC,IAAS,CAAC,IAAV,CACC,IAAK,EACL,mBAAkB,EACjB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,EAAK,YAAc,EAMnB,IAAM,EAAgB,WAOhB,EAAiB,aACrB,CAAC,EAAmC,KAClC,GAAM,aAAE,OAAa,GAAO,EAAM,GAAG,EAAU,CAAI,EAC7C,EAAU,EAAe,CADgB,CACD,GACxC,EAAwB,EAAyB,GACvD,CAFyD,KAGvD,EAFgE,CAEhE,OAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,YAAa,EAAQ,YACrB,IAAK,EAAQ,SACb,EAEA,mBAAC,IAAS,CAAC,IAAV,CACC,KAAK,UACL,mBAAkB,EAAQ,YACzB,GAAG,EACJ,IAAK,GACP,EAGN,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAe,cAQf,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,CAAE,oBAAa,WAAO,GAAW,EAAO,GAAG,EAAa,CAAI,EAC5D,EAAU,EAAe,EAAc,EADiB,CAExD,EAAwB,EAAyB,GACjD,CAFkD,CAEtC,EAAc,EAAQ,EAD0B,IAC1B,CAAQ,GAC1C,EAAY,EAAc,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MACrC,MACE,UAAkB,KAAjB,CACC,SAAO,EACN,GAAG,EACJ,UAAW,CAAC,EACZ,OAAQ,EAER,mBAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,MACL,gBAAe,EACf,gBAAe,EACf,aAAY,EAAa,SAAW,WACpC,gBAAe,EAAW,GAAK,gBAC/B,EACA,GAAI,EACH,GAAG,EACJ,IAAK,EACL,YAAa,OAAoB,CAAC,EAAM,YAAa,IAG9C,GAA6B,IAAjB,EAAM,QAAgB,CAAkB,MAAZ,CAAmB,MAAnB,CAI3C,EAAM,eAAe,EAHrB,EAAQ,cAAc,EAK1B,CAAC,EACD,UAAW,OAAoB,CAAC,EAAM,UAAW,IAC3C,CAAC,IAAK,OAAO,EAAE,SAAS,EAAM,GAAG,EAAG,GAAQ,cAAc,EAChE,CAAC,EADoE,QAE5D,OAAoB,CAAC,EAAM,QAAS,KAG3C,IAAM,EAAmD,WAA3B,EAAQ,eACjC,GAAe,IAAY,GAC9B,EAAQ,EADS,WACT,CAAc,EAE1B,CAAC,CAHwD,CAC1B,CAGjC,EAGN,EAGF,GAAY,YAAc,EAM1B,IAAM,EAAe,cAaf,EAAoB,aACxB,CAAC,EAAsC,KACrC,GAAM,aAAE,QAAa,aAAO,WAAY,EAAU,GAAG,EAAa,CAAI,EAChE,EAAU,EAAe,EAAc,EADqB,CAE5D,EAAY,EAAc,EAAQ,EADgB,IAChB,CAAQ,GAC1C,EAAY,EAAc,EAAQ,OAAQ,GAC1C,EAD+C,IACxB,EAAQ,MAC/B,EAAqC,SAAO,GAOlD,OAP4D,EAEtD,UAAU,KACd,IAAM,EAAM,sBAAsB,IAAO,EAA6B,QAAU,IAChF,CADsF,KAC/E,IAAM,qBAAqB,EACpC,CADuC,CACpC,CAAC,CAAC,EAGH,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAC9B,UAAC,SAAE,EAAQ,GACV,UAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAa,SAAW,WACpC,mBAAkB,EAAQ,YAC1B,KAAK,WACL,kBAAiB,EACjB,OAAQ,CAAC,EACT,GAAI,EACJ,SAAU,EACT,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAM,MACT,kBAAmB,EAA6B,QAAU,KAAO,MACnE,EAEC,YAAW,GACd,CAEJ,CAEJ,GAOF,SAAS,EAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EAGnC,CAHmC,QAG1B,EAAc,EAAgB,GAAe,MAC7C,GAAG,EAAM,WAAY,EAAK,EATnC,CASmC,CATvB,YAAc,EAY1B,IAAMM,EAAO,EACP,EAAO,EACP,EAAU,EACV,EAAU,0BC1RhB,gDCAA,qCAAqK,yBCArK,uDCAA,sDCAA,wDCAA,4ECmBM,MAAQ,cAAiB,SAhBK,CAClC,CAAC,QAAU,EAAE,EAAI,MAAM,CAAI,OAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,UAAY,EAAE,OAAQ,CAAoB,sBAAK,SAAU,EAC5D,yBCNA,0KCKA,IAAMC,EAAOC,EAAAA,EAAkB,CACzBC,EAAWvB,EAAAA,UAAgB,CAAyG,CAAC,CACzIC,WAAS,CACT,GAAGE,EACJ,CAAEC,IAAQ,UAACkB,EAAAA,EAAkB,EAAClB,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6FAA8FL,GAAa,GAAGE,CAAK,IAC1KoB,EAASd,WAAW,CAAGa,EAAAA,EAAkB,CAACb,WAAW,CACrD,IAAMe,EAAcxB,EAAAA,UAAgB,CAA+G,CAAC,WAClJC,CAAS,CACT,GAAGE,EACJ,CAAEC,IAAQ,UAACkB,EAAAA,EAAqB,EAAClB,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qZAAsZL,GAAa,GAAGE,CAAK,IACreqB,EAAYf,WAAW,CAAGa,EAAAA,EAAqB,CAACb,WAAW,CAC3D,IAAMgB,EAAczB,EAAAA,UAAgB,CAA+G,CAAC,CAClJC,WAAS,CACT,GAAGE,EACJ,CAAEC,IAAQ,UAACkB,EAAAA,EAAqB,EAAClB,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kIAAmIL,GAAa,GAAGE,CAAK,IAClNsB,EAAYhB,WAAW,CAAGa,EAAAA,EAAqB,CAACb,WAAW,yBCpB3D,mDCAA,mECAA,yDCAA,iEEmBI,sBAAsB,gMDbbiB,EAAqB,CAChCC,KADWD,CACJ,wBACPE,WAAAA,CAAa,6BACf,EACe,eAAeC,EAAgB,UAC5C/B,CAAQ,CAGT,CAJ6B+B,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBC,EAAcF,EAAYG,GAAG,CAAC,GAA9BD,EAAcF,aAAkC5B,KAAAA,GAAU,OAChE,MAAOgC,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACC,EAAAA,OAAAA,CAAAA,CAAKC,qBAAAA,CAAoB,OAAOvB,uBAAAA,CAAsB,kBAAkBC,yBAAAA,CAAwB,aACpG,SAAAuB,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACC,EAAAA,eAAAA,CAAAA,CAAgBN,WAAAA,CAAaA,EAAaI,SAAbJ,YAAaI,CAAoB,kBAAkBtB,yBAAAA,CAAwB,uBACvGoB,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACK,EAAAA,OAAAA,CAAAA,CAAWH,qBAAAA,CAAoB,aAAatB,yBAAAA,CAAwB,eACrEuB,CAAAA,EAAAA,EAAAA,IAAAA,CAAA,CAACG,EAAAA,YAAAA,CAAAA,CAAaJ,qBAAAA,CAAoB,eAAetB,yBAAAA,CAAwB,uBACvEoB,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACO,EAAAA,OAAAA,CAAAA,CAAOL,qBAAAA,CAAoB,SAAStB,yBAAAA,CAAwB,eAE7DoB,CAAAA,EAAAA,EAAAA,GAAAA,CAAA,CAACQ,MAAAA,CAAAA,CAAKzC,SAAAA,CAAU,kDACbH,QAAAA,CAAAA,WAMb,CCvBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAKC,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,8LCUM,EAAgB,WAIhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GAIlE,CAAC,EAAkB,EAJwB,CAK/C,EAA4C,EALuC,CAe/E,EAAiB,QAVoC,IAUpC,CACrB,CAAC,EAAmC,SAwGV,IAvGxB,GAAM,IAuGqC,aAtGzC,EACA,MAAO,EAAY,KACnB,IAAK,gBACL,EAAgB,EAChB,GAAG,EACL,CAAI,CAEJ,EAAK,GAAW,CAAY,GAAZ,IAAkB,CAAC,EAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,GAAmB,GAAG,EAAO,EA+FxC,CA/F4C,EAAJ,QAAc,CAAC,uBA+FpB,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EAAiB,GAAW,EAhCxB,EAgCoB,CAElB,GA0FoJ,EA5FpH,GAE9C,GAAuB,EAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,CA4FU,EA5FW,GAAG,EAAS,EA6F5C,CA7FgD,CA4FR,GA5FI,MAAc,CAAC,KA4FI,oBAC1B,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,EAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,EAAS,GAAS,EAAJ,EAAyB,GAAG,KAAI,EAEjE,MACE,UAAC,GAAiB,MAAO,QAAiB,MAAc,EACtD,mBAAC,IAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,EAAS,GAAS,EAAJ,KAAY,EACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,EAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAiB,oBAKjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,EAAmB,EAAgB,GACnD,CAF2C,KAGzC,MAFgE,EAEhE,EAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,EAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,EAAiB,EAAkC,GAAiC,OAC3E,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,EAAS,GAA6B,MACrB,UAAjB,OAAO,CAChB,CAEA,SAAS,EAAiB,GAAyB,OAG/C,EAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,EAAmB,EAAY,GAA8B,OAGlE,EAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,EAAkB,YAAc,EAiDhC,IAAM,EAAO,EACP,EAAY,0BCpJlB,qDCAA,2DCAA,yDCAA,0DCAA,sCAA0L,CAE1L,uCAAkM,CAElM,uCAA6L,CAE7L,sCAAiN,yBCNjN,uDCAA,sDCAA,iDCAA,2DCAA,+ICIA,IAAM6C,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,CACbnD,WAAS,SACT6C,CAAO,SACPO,GAAU,CAAK,CACf,GAAGlD,EAGJ,EACC,IAAMmD,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAK1C,YAAU,QAAQX,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACqC,EAAc,SACzDG,CACF,GAAI7C,GAAa,GAAGE,CAAK,CAAEiC,sBAAoB,OAAOvB,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,iDCAA,mSC6Be,SAAS0C,IACtB,GAAM,CAACC,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACC,EAAgBC,EAAkB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC/C,CAACG,EAAiBC,EAAmB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC7D,CAACK,EAAkBC,EAAoB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAW,EAAE,EAC/D,CAACO,EAASC,EAAW,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACS,EAAWC,EAAa,CAAGV,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAIrCW,EAAe,UACnB,GAAI,CACF,IAAMC,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAChC,GAAI,CAACF,EAAM,YACTG,EAAAA,EAAKA,CAACC,KAAK,CAAC,iCAKd,IAAMC,EAAmB,MAAMC,MAAM,CAAC,2BAA2B,EAAEN,EAAKO,EAAE,EAAE,EAC5E,GAAIF,EAAiBG,EAAE,CAAE,CACvB,IAAMC,EAAe,MAAMJ,EAAiBK,IAAI,GAChDlB,EAAmBiB,EACrB,CAGA,IAAME,EAAoB,MAAML,MAAM,gBACtC,GAAIK,EAAkBH,EAAE,CAAE,CACxB,IAAMI,EAAgB,MAAMD,EAAkBD,IAAI,GAClDhB,EAAoBkB,EACtB,CACF,CAAE,MAAOR,EAAO,CACdS,QAAQT,KAAK,CAAC,0BAA2BA,GACzCD,EAAAA,EAAKA,CAACC,KAAK,CAAC,yBACd,QAAU,CACRR,EAAW,GACb,CACF,EACMkB,EAAuB,UAC3B,GAAI,CAACzB,EAAe0B,IAAI,GAAI,OAC5B,IAAMf,EAAOC,EAAAA,EAAWA,CAACC,OAAO,GAChC,GAAI,CAACF,EAAM,YACTG,EAAAA,EAAKA,CAACC,KAAK,CAAC,sCAGdN,GAAa,GACb,GAAI,CACF,IAAMkB,EAAW,MAAMV,MAAM,mBAAoB,CAC/CW,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,EACAC,KAAMC,KAAKC,SAAS,CAAC,CACnBC,UAAWtB,EAAKO,EAAE,CAClBgB,WAAYlC,EAAe0B,IAAI,EACjC,EACF,GACA,GAAIC,EAASR,EAAE,CAAE,CACf,IAAMgB,EAAS,MAAMR,EAASN,IAAI,GAClCP,EAAAA,EAAKA,CAACsB,OAAO,CAAC,CAAC,yBAAyB,EAAED,EAAOE,UAAU,CAAC,CAAC,CAAC,EAC9DpC,EAAkB,IAClBS,GACF,KAAO,CACL,IAAMK,EAAQ,CAFE,KAEIY,EAASN,IAAI,GACjCP,EAAAA,EAAKA,CAACC,KAAK,CAACA,EAH+B,OAGlB,EAAI,6BAC/B,CACF,CAAE,MAAOA,EAAO,CACdS,QAAQT,KAAK,CAAC,oBAAqBA,GACnCD,EAAAA,EAAKA,CAACC,KAAK,CAAC,oCACd,QAAU,CACRN,GAAa,EACf,CACF,EACM6B,EAA0BpC,EAAgBqC,MAAM,CAACC,GAAUA,EAAOC,IAAI,CAACC,WAAW,GAAGC,QAAQ,CAAC9C,EAAW6C,WAAW,KAAOF,EAAOxE,WAAW,CAAC0E,WAAW,GAAGC,QAAQ,CAAC9C,EAAW6C,WAAW,KAC3LE,EAA2BxC,EAAiBmC,MAAM,CAACC,GAAUA,EAAOC,IAAI,CAACC,WAAW,GAAGC,QAAQ,CAAC9C,EAAW6C,WAAW,KAAOF,EAAOxE,WAAW,CAAC0E,WAAW,GAAGC,QAAQ,CAAC9C,EAAW6C,WAAW,KACnM,MAAO,WAAC3F,MAAAA,CAAIV,UAAU,YAAYY,wBAAsB,qBAAqBC,0BAAwB,qBACjG,UAACH,MAAAA,CAAIV,UAAU,6CACb,WAACU,MAAAA,WACC,UAAC8F,KAAAA,CAAGxG,UAAU,6CAAoC,eAClD,UAACyG,IAAAA,CAAEzG,UAAU,iCAAwB,4DAOzC,WAACS,EAAAA,EAAIA,CAAAA,CAAC0B,sBAAoB,OAAOtB,0BAAwB,qBACvD,WAACC,EAAAA,EAAUA,CAAAA,CAACqB,sBAAoB,aAAatB,0BAAwB,qBACnE,UAACE,EAAAA,EAASA,CAAAA,CAACoB,sBAAoB,YAAYtB,0BAAwB,oBAAW,qBAC9E,UAACG,EAAAA,EAAeA,CAAAA,CAACmB,sBAAoB,kBAAkBtB,0BAAwB,oBAAW,yDAI5F,UAACI,EAAAA,EAAWA,CAAAA,CAACkB,sBAAoB,cAActB,0BAAwB,oBACrE,WAACH,MAAAA,CAAIV,UAAU,2BACb,UAAC0G,EAAAA,CAAKA,CAAAA,CAACC,YAAY,oCAAoC1G,MAAO0D,EAAgBiD,SAAUC,GAAKjD,EAAkBiD,EAAEC,MAAM,CAAC7G,KAAK,CAAC8G,WAAW,IAAK/G,UAAU,SAASmC,sBAAoB,QAAQtB,0BAAwB,aACrN,UAACmG,EAAAA,CAAMA,CAAAA,CAACC,QAAS7B,EAAsB8B,SAAU,CAACvD,EAAe0B,IAAI,IAAMlB,EAAWhC,sBAAoB,SAAStB,0BAAwB,oBACxIsD,EAAY,eAAiB,mBAMtC,WAAC/C,EAAAA,EAAIA,CAAAA,CAAC+F,aAAa,WAAWnH,UAAU,YAAYmC,sBAAoB,OAAOtB,0BAAwB,qBACrG,WAACS,EAAAA,EAAQA,CAAAA,CAACa,sBAAoB,WAAWtB,0BAAwB,qBAC/D,UAACU,EAAAA,EAAWA,CAAAA,CAACtB,MAAM,WAAWkC,sBAAoB,cAActB,0BAAwB,oBAAW,eACnG,UAACU,EAAAA,EAAWA,CAAAA,CAACtB,MAAM,YAAYkC,sBAAoB,cAActB,0BAAwB,oBAAW,yBAGtG,UAACH,MAAAA,CAAIV,UAAU,uCACb,WAACU,MAAAA,CAAIV,UAAU,4BACb,UAACoH,EAAAA,CAAMA,CAAAA,CAACpH,UAAU,wDAAwDmC,sBAAoB,SAAStB,0BAAwB,aAC/H,UAAC6F,EAAAA,CAAKA,CAAAA,CAACC,YAAY,oBAAoB1G,MAAOuD,EAAYoD,SAAUC,GAAKpD,EAAcoD,EAAEC,MAAM,CAAC7G,KAAK,EAAGD,UAAU,OAAOmC,sBAAoB,QAAQtB,0BAAwB,kBAIjL,WAACW,EAAAA,EAAWA,CAAAA,CAACvB,MAAM,WAAWkC,sBAAoB,cAActB,0BAAwB,qBACrFoD,EAAU,UAACvD,MAAAA,CAAIV,UAAU,oDACrB,CAAC,EAAG,EAAG,EAAE,CAACqH,GAAG,CAACC,GAAK,WAAC7G,EAAAA,EAAIA,CAAAA,CAAST,UAAU,0BACxC,UAACc,EAAAA,EAAUA,CAAAA,UACT,WAACJ,MAAAA,CAAIV,UAAU,sBACb,UAACU,MAAAA,CAAIV,UAAU,6CACf,UAACU,MAAAA,CAAIV,UAAU,kDAGnB,UAACiB,EAAAA,EAAWA,CAAAA,UACV,WAACP,MAAAA,CAAIV,UAAU,sBACb,UAACU,MAAAA,CAAIV,UAAU,8CACf,UAACU,MAAAA,CAAIV,UAAU,oDAVQsH,MAcxB,UAAC5G,MAAAA,CAAIV,UAAU,oDACvBiG,EAAwBoB,GAAG,CAAClB,GAAU,WAAC1F,EAAAA,EAAIA,CAAAA,CAAiBT,UAAU,0BACnE,WAACc,EAAAA,EAAUA,CAAAA,WACT,WAACJ,MAAAA,CAAIV,UAAU,6CACb,WAACU,MAAAA,CAAIV,UAAU,sBACb,UAACe,EAAAA,EAASA,CAAAA,CAACf,UAAU,mBAAWmG,EAAOC,IAAI,GAC3C,UAACmB,OAAAA,CAAKvH,UAAU,8CACbmG,EAAON,UAAU,MAGtB,UAAC1C,EAAAA,CAAKA,CAAAA,CAACN,QAAyB,aAAhBsD,EAAOqB,IAAI,CAAkB,UAAY,qBACtDrB,EAAOqB,IAAI,MAGhB,UAACxG,EAAAA,EAAeA,CAAAA,UAAEmF,EAAOxE,WAAW,MAEtC,WAACV,EAAAA,EAAWA,CAAAA,CAACjB,UAAU,6BACrB,WAACU,MAAAA,CAAIV,UAAU,sBACb,WAACU,MAAAA,CAAIV,UAAU,sDACb,UAACyH,OAAAA,UAAK,aACN,WAACA,OAAAA,WAAMtB,EAAOuB,QAAQ,CAAC,UAEzB,UAAC5H,EAAAA,CAAQA,CAAAA,CAACG,MAAOkG,EAAOuB,QAAQ,CAAE1H,UAAU,QAC5C,WAACyG,IAAAA,CAAEzG,UAAU,0CACVmG,EAAOwB,gBAAgB,CAAC,OAAKxB,EAAOyB,YAAY,CAAC,2BAKnC,gBAAlBzB,EAAO0B,MAAM,EAAsB1B,EAAO2B,WAAW,EAAI,WAACpH,MAAAA,CAAIV,UAAU,sBACrE,UAACyG,IAAAA,CAAEzG,UAAU,+BAAsB,UACnC,UAACyG,IAAAA,CAAEzG,UAAU,yCACVmG,EAAO2B,WAAW,MAIzB,WAACpH,MAAAA,CAAIV,UAAU,sBACb,WAACU,MAAAA,CAAIV,UAAU,sEACb,UAAC+H,EAAAA,CAAKA,CAAAA,CAAC/H,UAAU,YACjB,WAACyH,OAAAA,WAAK,QACEtB,EAAO6B,OAAO,CAAG,IAAIC,KAAK9B,EAAO6B,OAAO,EAAEE,kBAAkB,GAAK,oBAG3E,UAAC/E,EAAAA,CAAKA,CAAAA,CAACN,QAA2B,cAAlBsD,EAAO0B,MAAM,CAAmB,UAAY,mBACzD1B,gBAAO0B,MAAM,CAAmB,iCAC7B,UAACM,EAAAA,CAAWA,CAAAA,CAACnI,UAAU,iBAAiB,eAEpC,sBAId,UAACU,MAAAA,CAAIV,UAAU,oBACb,UAACoI,IAAIA,CAACC,KAAM,CAAC,2BAA2B,EAAElC,EAAOtB,EAAE,EAAE,IAAhDuD,MACH,UAACpB,EAAAA,CAAMA,CAAAA,CAAChH,UAAU,kBACfmG,gBAAO0B,MAAM,CAAmB,iCAC7B,UAACS,EAAAA,CAAKA,CAAAA,CAACtI,UAAU,iBAAiB,sBAE9B,iCACJ,UAACuI,EAAAA,CAAIA,CAAAA,CAACvI,UAAU,iBAAiB,+BAzDGmG,EAAOtB,EAAE,KAkE5D,CAACZ,GAA8C,IAAnCgC,EAAwBuC,MAAM,EAAU,UAAC/H,EAAAA,EAAIA,CAAAA,UACtD,UAACQ,EAAAA,EAAWA,CAAAA,CAACjB,UAAU,gBACrB,WAACU,MAAAA,CAAIV,UAAU,6BACb,UAACyI,EAAAA,CAAQA,CAAAA,CAACzI,UAAU,4CACpB,UAAC0I,KAAAA,CAAG1I,UAAU,sCAA6B,wBAG3C,UAACyG,IAAAA,CAAEzG,UAAU,8CACVwD,EAAa,gCAAkC,oDAO5D,WAAChC,EAAAA,EAAWA,CAAAA,CAACvB,MAAM,YAAYkC,sBAAoB,cAActB,0BAAwB,qBACtFoD,EAAU,UAACvD,MAAAA,CAAIV,UAAU,oDACrB,CAAC,EAAG,EAAG,EAAE,CAACqH,GAAG,CAACC,GAAK,WAAC7G,EAAAA,EAAIA,CAAAA,CAAST,UAAU,0BACxC,UAACc,EAAAA,EAAUA,CAAAA,UACT,WAACJ,MAAAA,CAAIV,UAAU,sBACb,UAACU,MAAAA,CAAIV,UAAU,6CACf,UAACU,MAAAA,CAAIV,UAAU,kDAGnB,UAACiB,EAAAA,EAAWA,CAAAA,UACV,WAACP,MAAAA,CAAIV,UAAU,sBACb,UAACU,MAAAA,CAAIV,UAAU,8CACf,UAACU,MAAAA,CAAIV,UAAU,oDAVQsH,MAcxB,UAAC5G,MAAAA,CAAIV,UAAU,oDACvBuG,EAAyBc,GAAG,CAAClB,GAAU,WAAC1F,EAAAA,EAAIA,CAAAA,CAAiBT,UAAU,0BACpE,WAACc,EAAAA,EAAUA,CAAAA,WACT,WAACJ,MAAAA,CAAIV,UAAU,6CACb,WAACU,MAAAA,CAAIV,UAAU,sBACb,UAACe,EAAAA,EAASA,CAAAA,CAACf,UAAU,mBAAWmG,EAAOC,IAAI,GAC3C,UAACmB,OAAAA,CAAKvH,UAAU,8CACbmG,EAAON,UAAU,MAGtB,UAAC1C,EAAAA,CAAKA,CAAAA,CAACN,QAAyB,aAAhBsD,EAAOqB,IAAI,CAAkB,UAAY,qBACtDrB,EAAOqB,IAAI,MAGhB,UAACxG,EAAAA,EAAeA,CAAAA,UAAEmF,EAAOxE,WAAW,MAEtC,UAACV,EAAAA,EAAWA,CAAAA,CAACjB,UAAU,4BACrB,WAACU,MAAAA,CAAIV,UAAU,8BACb,WAACU,MAAAA,CAAIV,UAAU,8CACb,UAACyH,OAAAA,CAAKzH,UAAU,iCAAwB,gBACxC,UAACyH,OAAAA,UAAMtB,EAAOwC,UAAU,MAE1B,WAACjI,MAAAA,CAAIV,UAAU,8CACb,UAACyH,OAAAA,CAAKzH,UAAU,iCAAwB,cACxC,UAACyH,OAAAA,UAAMtB,EAAOyC,QAAQ,MAExB,WAAClI,MAAAA,CAAIV,UAAU,8CACb,UAACyH,OAAAA,CAAKzH,UAAU,iCAAwB,gBACxC,UAACmD,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAU7C,UAAU,mBAChCmG,EAAO0C,UAAU,WAK1B,UAACnI,MAAAA,CAAIV,UAAU,oBACb,WAACgH,EAAAA,CAAMA,CAAAA,CAAChH,UAAU,SAAS6C,QAAQ,oBACjC,UAAC4F,EAAAA,CAAQA,CAAAA,CAACzI,UAAU,iBAAiB,4BAnCMmG,EAAOtB,EAAE,KA0C7D,CAACZ,GAA+C,IAApCsC,EAAyBiC,MAAM,EAAU,UAAC/H,EAAAA,EAAIA,CAAAA,UACvD,UAACQ,EAAAA,EAAWA,CAAAA,CAACjB,UAAU,gBACrB,WAACU,MAAAA,CAAIV,UAAU,6BACb,UAACyI,EAAAA,CAAQA,CAAAA,CAACzI,UAAU,4CACpB,UAAC0I,KAAAA,CAAG1I,UAAU,sCAA6B,yBAG3C,UAACyG,IAAAA,CAAEzG,UAAU,8CACVwD,EAAa,gCAAkC,qDAQpE,0BC9TA,mFCmBM,MAAiB,cAAiB,kBAhBJ,CAgBsB,CAfvD,MAAQ,EAAE,EAAG,CAAmC,qCAAK,SAAU,EAChE,CAAC,MAAQ,EAAE,EAAG,CAAkB,oBAAK,SAAU,EACjD,uTCUA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAA4K,CAuBhM,2IAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAnCA,IAAsB,uCAAqK,CAmC3L,qIAGA,CACO,CACP,CACA,QA1CA,IAAsB,uCAA4J,CA0ClL,2HACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QA3DA,IAAsB,sCAAiJ,CA2DvK,gHACA,gBA3DA,IAAsB,uCAAuJ,CA2D7K,sHACA,aA3DA,IAAsB,uCAAoJ,CA2D1K,mHACA,WA3DA,IAAsB,4CAAgF,CA2DtG,+CACA,cA3DA,IAAsB,4CAAmF,CA2DzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,8IAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,uCACA,sCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCtGD,8DpBmBI,sBAAsB,8sBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAAE,OAAhC,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,4BAA4B,CAC5C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,CAEjB,OAAO,EACf,CAAO,CAFc,CAEZ,KAAK,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CA/BoBsF,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,iBASQ,cA7D1B,sBAAsB,8rBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAC3B,EACA,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,KAAO,CADqB,CAK7B,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CA7BEA,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,cqBvEtB,GrBgF8B,KqBhF9B,+BAAqK", "sources": ["webpack://terang-lms-ui/./src/app/dashboard/student/layout.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/?a03b", "webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/?2333", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/../../../src/icons/play.ts", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/../../../src/icons/search.ts", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/?0b9b", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/../src/tabs.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/?8902", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/../../../src/icons/clock.ts", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/./src/components/ui/tabs.tsx", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/src/app/dashboard/layout.tsx", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/progress.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/?0079", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/./src/components/ui/badge.tsx", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/./src/app/dashboard/student/courses/page.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/../../../src/icons/circle-check-big.ts", "webpack://terang-lms-ui/?b093", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/?d67a"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { requireRole } from '@/lib/auth';\nexport default function StudentLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  useEffect(() => {\n    // Check if user has student role\n    requireRole('student');\n  }, []);\n  return <>{children}</>;\n}", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "module.exports = require(\"module\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\page.tsx\");\n", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('Play', __iconNode);\n\nexport default Play;\n", "module.exports = require(\"os\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('Search', __iconNode);\n\nexport default Search;\n", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\page.tsx\");\n", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n", "module.exports = require(\"node:http\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\n", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('Clock', __iconNode);\n\nexport default Clock;\n", "module.exports = require(\"node:diagnostics_channel\");", "\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport { cn } from \"@/lib/utils\";\nconst Tabs = TabsPrimitive.Root;\nconst TabsList = React.forwardRef<React.ElementRef<typeof TabsPrimitive.List>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.List ref={ref} className={cn(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className)} {...props} />);\nTabsList.displayName = TabsPrimitive.List.displayName;\nconst TabsTrigger = React.forwardRef<React.ElementRef<typeof TabsPrimitive.Trigger>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.Trigger ref={ref} className={cn(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer\", className)} {...props} />);\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\nconst TabsContent = React.forwardRef<React.ElementRef<typeof TabsPrimitive.Content>, React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>>(({\n  className,\n  ...props\n}, ref) => <TabsPrimitive.Content ref={ref} className={cn(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className)} {...props} />);\nTabsContent.displayName = TabsPrimitive.Content.displayName;\nexport { Tabs, TabsList, TabsTrigger, TabsContent };", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Akademi IAI Dashboard',\n  description: 'LMS Sertifikasi Profesional'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get('sidebar_state')?.value === 'true';\n  return <KBar data-sentry-element=\"KBar\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n        <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n        <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n          <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n          {/* page main content */}\r\n          <main className='h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8'>\r\n            {children}\r\n          </main>\r\n          {/* page main content ends */}\r\n        </SidebarInset>\r\n      </SidebarProvider>\r\n    </KBar>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\layout\\\\header.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"SidebarProvider\",\"SidebarInset\"] */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { toast } from 'sonner';\nimport { authStorage } from '@/lib/auth';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { BookOpen, Search, Play, CheckCircle, Clock, Award } from 'lucide-react';\nimport Link from 'next/link';\ninterface Course {\n  id: number;\n  name: string;\n  description: string;\n  type: string;\n  courseCode: string;\n  progress?: number;\n  totalModules?: number;\n  completedModules?: number;\n  nextChapter?: string | null;\n  dueDate?: string;\n  status?: string;\n  instructor?: string;\n  duration?: string;\n  difficulty?: string;\n}\nexport default function StudentCoursesPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [enrollmentCode, setEnrollmentCode] = useState('');\n  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);\n  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [enrolling, setEnrolling] = useState(false);\n  useEffect(() => {\n    fetchCourses();\n  }, []);\n  const fetchCourses = async () => {\n    try {\n      const user = authStorage.getUser();\n      if (!user) {\n        toast.error('Please log in to view courses');\n        return;\n      }\n\n      // Fetch enrolled courses\n      const enrolledResponse = await fetch(`/api/enrollments?studentId=${user.id}`);\n      if (enrolledResponse.ok) {\n        const enrolledData = await enrolledResponse.json();\n        setEnrolledCourses(enrolledData);\n      }\n\n      // Fetch available courses\n      const availableResponse = await fetch('/api/courses');\n      if (availableResponse.ok) {\n        const availableData = await availableResponse.json();\n        setAvailableCourses(availableData);\n      }\n    } catch (error) {\n      console.error('Error fetching courses:', error);\n      toast.error('Failed to load courses');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleEnrollWithCode = async () => {\n    if (!enrollmentCode.trim()) return;\n    const user = authStorage.getUser();\n    if (!user) {\n      toast.error('Please log in to enroll in courses');\n      return;\n    }\n    setEnrolling(true);\n    try {\n      const response = await fetch('/api/enrollments', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          studentId: user.id,\n          courseCode: enrollmentCode.trim()\n        })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        toast.success(`Successfully enrolled in ${result.courseName}!`);\n        setEnrollmentCode('');\n        fetchCourses(); // Refresh the courses list\n      } else {\n        const error = await response.json();\n        toast.error(error.message || 'Failed to enroll in course');\n      }\n    } catch (error) {\n      console.error('Enrollment error:', error);\n      toast.error('An error occurred while enrolling');\n    } finally {\n      setEnrolling(false);\n    }\n  };\n  const filteredEnrolledCourses = enrolledCourses.filter(course => course.name.toLowerCase().includes(searchTerm.toLowerCase()) || course.description.toLowerCase().includes(searchTerm.toLowerCase()));\n  const filteredAvailableCourses = availableCourses.filter(course => course.name.toLowerCase().includes(searchTerm.toLowerCase()) || course.description.toLowerCase().includes(searchTerm.toLowerCase()));\n  return <div className='space-y-6' data-sentry-component=\"StudentCoursesPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex items-center justify-between'>\r\n        <div>\r\n          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>\r\n          <p className='text-muted-foreground'>\r\n            Access your enrolled courses and discover new ones\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quick Enrollment */}\r\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"page.tsx\">\r\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"page.tsx\">\r\n          <CardTitle data-sentry-element=\"CardTitle\" data-sentry-source-file=\"page.tsx\">Quick Enrollment</CardTitle>\r\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"page.tsx\">\r\n            Enter a course code to quickly enroll in a course\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"page.tsx\">\r\n          <div className='flex space-x-2'>\r\n            <Input placeholder='Enter course code (e.g., MATH101)' value={enrollmentCode} onChange={e => setEnrollmentCode(e.target.value.toUpperCase())} className='flex-1' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n            <Button onClick={handleEnrollWithCode} disabled={!enrollmentCode.trim() || enrolling} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\r\n              {enrolling ? 'Enrolling...' : 'Enroll'}\r\n            </Button>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Tabs defaultValue='enrolled' className='space-y-6' data-sentry-element=\"Tabs\" data-sentry-source-file=\"page.tsx\">\r\n        <TabsList data-sentry-element=\"TabsList\" data-sentry-source-file=\"page.tsx\">\r\n          <TabsTrigger value='enrolled' data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"page.tsx\">My Courses</TabsTrigger>\r\n          <TabsTrigger value='available' data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"page.tsx\">Available Courses</TabsTrigger>\r\n        </TabsList>\r\n\r\n        <div className='flex items-center space-x-2'>\r\n          <div className='relative flex-1'>\r\n            <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' data-sentry-element=\"Search\" data-sentry-source-file=\"page.tsx\" />\r\n            <Input placeholder='Search courses...' value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className='pl-8' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\r\n          </div>\r\n        </div>\r\n\r\n        <TabsContent value='enrolled' data-sentry-element=\"TabsContent\" data-sentry-source-file=\"page.tsx\">\r\n          {loading ? <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n              {[1, 2, 3].map(i => <Card key={i} className='flex flex-col'>\r\n                  <CardHeader>\r\n                    <div className='space-y-2'>\r\n                      <div className='bg-muted h-4 w-3/4 rounded animate-pulse'></div>\r\n                      <div className='bg-muted h-3 w-1/2 rounded animate-pulse'></div>\r\n                    </div>\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className='space-y-2'>\r\n                      <div className='bg-muted h-3 w-full rounded animate-pulse'></div>\r\n                      <div className='bg-muted h-3 w-2/3 rounded animate-pulse'></div>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>)}\r\n            </div> : <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n            {filteredEnrolledCourses.map(course => <Card key={course.id} className='flex flex-col'>\r\n                <CardHeader>\r\n                  <div className='flex items-start justify-between'>\r\n                    <div className='space-y-1'>\r\n                      <CardTitle className='text-lg'>{course.name}</CardTitle>\r\n                      <code className='bg-muted rounded px-2 py-1 text-sm'>\r\n                        {course.courseCode}\r\n                      </code>\r\n                    </div>\r\n                    <Badge variant={course.type === 'verified' ? 'default' : 'secondary'}>\r\n                      {course.type}\r\n                    </Badge>\r\n                  </div>\r\n                  <CardDescription>{course.description}</CardDescription>\r\n                </CardHeader>\r\n                <CardContent className='flex-1 space-y-4'>\r\n                  <div className='space-y-2'>\r\n                    <div className='flex items-center justify-between text-sm'>\r\n                      <span>Progress</span>\r\n                      <span>{course.progress}%</span>\r\n                    </div>\r\n                    <Progress value={course.progress} className='h-2' />\r\n                    <p className='text-muted-foreground text-xs'>\r\n                      {course.completedModules} of {course.totalModules} modules\r\n                      completed\r\n                    </p>\r\n                  </div>\r\n\r\n                  {course.status === 'in_progress' && course.nextChapter && <div className='space-y-2'>\r\n                      <p className='text-sm font-medium'>Next:</p>\r\n                      <p className='text-muted-foreground text-sm'>\r\n                        {course.nextChapter}\r\n                      </p>\r\n                    </div>}\r\n\r\n                  <div className='space-y-2'>\r\n                    <div className='text-muted-foreground flex items-center space-x-2 text-sm'>\r\n                      <Clock className='h-4 w-4' />\r\n                      <span>\r\n                        Due: {course.dueDate ? new Date(course.dueDate).toLocaleDateString() : 'No due date'}\r\n                      </span>\r\n                    </div>\r\n                    <Badge variant={course.status === 'completed' ? 'default' : 'outline'}>\r\n                      {course.status === 'completed' ? <>\r\n                          <CheckCircle className='mr-1 h-3 w-3' />\r\n                          Completed\r\n                        </> : 'In Progress'}\r\n                    </Badge>\r\n                  </div>\r\n                </CardContent>\r\n                <div className='p-6 pt-0'>\r\n                  <Link href={`/dashboard/student/courses/${course.id}`}>\r\n                    <Button className='w-full'>\r\n                      {course.status === 'completed' ? <>\r\n                          <Award className='mr-2 h-4 w-4' />\r\n                          View Certificate\r\n                        </> : <>\r\n                          <Play className='mr-2 h-4 w-4' />\r\n                          Continue Learning\r\n                        </>}\r\n                    </Button>\r\n                  </Link>\r\n                </div>\r\n              </Card>)}\r\n            </div>}\r\n\r\n          {!loading && filteredEnrolledCourses.length === 0 && <Card>\r\n              <CardContent className='pt-6'>\r\n                <div className='py-8 text-center'>\r\n                  <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />\r\n                  <h3 className='mt-2 text-sm font-semibold'>\r\n                    No enrolled courses\r\n                  </h3>\r\n                  <p className='text-muted-foreground mt-1 text-sm'>\r\n                    {searchTerm ? 'No courses match your search.' : 'Get started by enrolling in a course.'}\r\n                  </p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>}\r\n        </TabsContent>\r\n\r\n        <TabsContent value='available' data-sentry-element=\"TabsContent\" data-sentry-source-file=\"page.tsx\">\r\n          {loading ? <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n              {[1, 2, 3].map(i => <Card key={i} className='flex flex-col'>\r\n                  <CardHeader>\r\n                    <div className='space-y-2'>\r\n                      <div className='bg-muted h-4 w-3/4 rounded animate-pulse'></div>\r\n                      <div className='bg-muted h-3 w-1/2 rounded animate-pulse'></div>\r\n                    </div>\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className='space-y-2'>\r\n                      <div className='bg-muted h-3 w-full rounded animate-pulse'></div>\r\n                      <div className='bg-muted h-3 w-2/3 rounded animate-pulse'></div>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>)}\r\n            </div> : <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n            {filteredAvailableCourses.map(course => <Card key={course.id} className='flex flex-col'>\r\n                <CardHeader>\r\n                  <div className='flex items-start justify-between'>\r\n                    <div className='space-y-1'>\r\n                      <CardTitle className='text-lg'>{course.name}</CardTitle>\r\n                      <code className='bg-muted rounded px-2 py-1 text-sm'>\r\n                        {course.courseCode}\r\n                      </code>\r\n                    </div>\r\n                    <Badge variant={course.type === 'verified' ? 'default' : 'secondary'}>\r\n                      {course.type}\r\n                    </Badge>\r\n                  </div>\r\n                  <CardDescription>{course.description}</CardDescription>\r\n                </CardHeader>\r\n                <CardContent className='flex-1 space-y-4'>\r\n                  <div className='space-y-2 text-sm'>\r\n                    <div className='flex items-center justify-between'>\r\n                      <span className='text-muted-foreground'>Instructor:</span>\r\n                      <span>{course.instructor}</span>\r\n                    </div>\r\n                    <div className='flex items-center justify-between'>\r\n                      <span className='text-muted-foreground'>Duration:</span>\r\n                      <span>{course.duration}</span>\r\n                    </div>\r\n                    <div className='flex items-center justify-between'>\r\n                      <span className='text-muted-foreground'>Difficulty:</span>\r\n                      <Badge variant='outline' className='text-xs'>\r\n                        {course.difficulty}\r\n                      </Badge>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n                <div className='p-6 pt-0'>\r\n                  <Button className='w-full' variant='outline'>\r\n                    <BookOpen className='mr-2 h-4 w-4' />\r\n                    Request Enrollment\r\n                  </Button>\r\n                </div>\r\n              </Card>)}\r\n            </div>}\r\n\r\n          {!loading && filteredAvailableCourses.length === 0 && <Card>\r\n              <CardContent className='pt-6'>\r\n                <div className='py-8 text-center'>\r\n                  <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />\r\n                  <h3 className='mt-2 text-sm font-semibold'>\r\n                    No available courses\r\n                  </h3>\r\n                  <p className='text-muted-foreground mt-1 text-sm'>\r\n                    {searchTerm ? 'No courses match your search.' : 'Check back later for new courses.'}\r\n                  </p>\r\n                </div>\r\n              </CardContent>\r\n            </Card>}\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>;\n}", "module.exports = require(\"node:inspector\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('CircleCheckBig', __iconNode);\n\nexport default CircleCheckBig;\n", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>ffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\nconst page7 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'student',\n        {\n        children: [\n        'courses',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module6, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\courses\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/student/courses/page\",\n        pathname: \"/dashboard/student/courses\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"events\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\");\n"], "names": ["StudentLayout", "children", "Progress", "React", "className", "value", "props", "ref", "ProgressPrimitive", "cn", "style", "transform", "displayName", "Card", "div", "data-slot", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "Root", "Tabs", "TabsPrimitive", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metadata", "title", "description", "DashboardLayout", "cookieStore", "cookies", "defaultOpen", "get", "_jsx", "KBar", "data-sentry-element", "_jsxs", "SidebarProvider", "AppSidebar", "SidebarInset", "Header", "main", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "StudentCoursesPage", "searchTerm", "setSearchTerm", "useState", "enrollmentCode", "setEnrollmentCode", "enrolledCourses", "setEnrolledCourses", "availableCourses", "setAvailableCourses", "loading", "setLoading", "enrolling", "setEnrolling", "fetchCourses", "user", "authStorage", "getUser", "toast", "error", "enrolledResponse", "fetch", "id", "ok", "enrolledData", "json", "availableResponse", "availableData", "console", "handleEnrollWithCode", "trim", "response", "method", "headers", "body", "JSON", "stringify", "studentId", "courseCode", "result", "success", "courseName", "filteredEnrolledCourses", "filter", "course", "name", "toLowerCase", "includes", "filteredAvailableCourses", "h1", "p", "Input", "placeholder", "onChange", "e", "target", "toUpperCase", "<PERSON><PERSON>", "onClick", "disabled", "defaultValue", "Search", "map", "i", "code", "type", "span", "progress", "completedModules", "totalModules", "status", "nextChapter", "Clock", "dueDate", "Date", "toLocaleDateString", "CheckCircle", "Link", "href", "Award", "Play", "length", "BookOpen", "h3", "instructor", "duration", "difficulty", "serverComponentModule.default"], "sourceRoot": ""}