{"version": 3, "file": "../app/api/users/[id]/route.js", "mappings": "2dAEA,GAAI,CAACA,QAAQC,GAAG,CAACC,YAAY,CAC3B,CAD6B,KACvB,MAAU,iDAGlB,IAAMC,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAIA,CAACJ,QAAQC,GAAG,CAACC,YAAY,EAElC,eAAeG,EAAMC,CAA0B,CAAE,GAAGC,CAAa,EAEtE,OAAOC,MADcL,EAAIG,KAASC,EAEpC,yBCXA,6GCAA,oDCAA,qGCAA,mECAA,qDCAA,gDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,2CCAA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,oCCRA,sGCAA,qXCMO,eAAeE,EACpBC,CAAoB,CACpB,CAFoBD,OAElBF,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEI,EAAAA,CAAIC,CAAO,CAAE,CAAG,MAAML,EACxBI,EAAKE,EADmBN,MACnBM,CAASD,GAEpB,GAAIE,CAFgBF,CAAAA,IAEVD,EAAAA,CAAAA,EAAK,KACNI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,kBAAkB,CAC3C,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,IAAMX,EAAS,IAATA,EAAeH,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;;;;;;;;;;;;mBAaZ,EAAEM,EAAAA;IACjB,CAAC,CAED,GAAIH,GAAqB,GAArBA,MAAa,CACf,OAAOO,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,iBAAiB,CAC1C,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTG,IAAAA,CAAMZ,CAAM,CAAC,EAAE,CACfa,OAAAA,CAAS,6BACX,EACF,CAAE,MAAOH,EAAO,CAEd,EAFOA,KACPI,OAAAA,CAAQJ,KAAK,CAAC,kBAAmBA,GAC1BH,EAAAA,CAD0BG,WAC1BH,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,0BAA0B,CACnD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeI,EACpBb,CAAoB,CACpB,CAFoBa,OAElBhB,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEI,EAAAA,CAAIC,CAAO,CAAE,CAAG,MAAML,EACxBI,EAAKE,EADmBN,MACnBM,CAASD,GACdY,EAAO,EADOZ,CAAAA,GACDF,EAAQM,IAAI,CAAZN,EAEnB,GAAII,MAAMH,EAAAA,CAAAA,EAAK,KACNI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,kBAAkB,CAC3C,CAAEC,MAAAA,CAAQ,GAAI,GAIlB,GAAM,CAAEM,MAAI,OAAEC,CAAK,UAAEC,CAAQ,MAAEC,CAAI,eAAEC,CAAa,CAAE,CAAGL,EAGjDM,EAAe,MAAMzB,CAAAA,EAAAA,CAArByB,CAAqBzB,CAAAA,CAAK,CAAC;6CACQ,EAAEM,EAAAA;IAC3C,CAAC,CAED,GAA4B,GAAG,CAA3BmB,EAAaC,MAAM,CACrB,GADED,IACKf,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,iBAAiB,CAC1C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,GAAIO,GAASA,IAAUI,CAAY,CAAC,EAAE,CAACJ,KAAK,EAAE,CACzB,MAAMrB,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;2CACM,EAAEqB,EAAM,GAANA,QAAiB,EAAEf,EAAAA;OAC1D,EAEeoB,MAAM,CAAG,EACtB,CADyB,MAClBhB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,uBAAuB,CAChD,CAAEC,MAAAA,CAAQ,GAAI,GAOpB,IAAIa,EAAiB,KAEjBL,IAEFK,EAAiB,CAJfA,CAEAL,EAAU,EAEWM,EAAAA,EAAAA,CAAAA,CAAvBD,GAAkC,CAACL,EADhB,GAC0BO,CAAAA,CAG/C,CAHqCP,EAAUO,CAGzC1B,EAAS,IAATA,EAAeH,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;;wBAEP,EAAEoB,EAAK,EAALA;yBACD,EAAEC,EAAM,GAANA;4BACC,EAAEM,EAAe,YAAfA;wBACN,EAAEJ,EAAK,EAALA;yBACD,OAAoBO,IAAlBN,EAA8BA,EAAgB,CAA5BM,IAAiC,IAAnDN,EAA8BA;;iBAExC,EAAElB,EAAAA;;IAEf,CAAC,CAED,OAAOI,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTG,IAAAA,CAAMZ,CAAM,CAAC,EAAE,CACfa,OAAAA,CAAS,2BACX,EACF,CAAE,MAAOH,EAAO,CAEd,EAFOA,KACPI,OAAAA,CAAQJ,KAAK,CAAC,qBAAsBA,GAC7BH,EAAAA,CAD6BG,WAC7BH,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeiB,EACpBC,CAAqB,CACrB,IAFoBD,IAElB7B,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEI,EAAAA,CAAIC,CAAO,CAAE,CAAG,MAAML,EACxBI,EAAKE,EADmBN,MACnBM,CAASD,GAEpB,GAAIE,CAFgBF,CAAAA,IAEVD,EAAAA,CACR,EADa,KACNI,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,kBAAkB,CAC3C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMW,EAAe,MAAMzB,CAAAA,EAAAA,CAArByB,CAAqBzB,CAAAA,CAAK,CAAC;4CACO,EAAEM,EAAAA;IAC1C,CAAC,CAED,GAA4B,GAAG,CAA3BmB,EAAaC,MAAM,CACrB,GADED,IACKf,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,iBAAiB,CAC1C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,GAA6B,eAAe,CAAxCW,CAAY,CAAC,EAAE,CAACF,IAAI,CACtB,OAAOb,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,kCAAkC,CAC3D,CAAEC,MAAAA,CAAQ,GAAI,GASlB,OAJA,MAAMd,CAAAA,EAAAA,EAAAA,CAAAA,CAAK,CAAC;mCACmB,EAAEM,EAAG;IACpC,CAAC,CAEMI,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTI,OAAAA,CAAS,2BACX,EACF,CAAE,MAAOH,EAAO,CAEd,EAFOA,KACPI,OAAAA,CAAQJ,KAAK,CAAC,qBAAsBA,GAC7BH,EAD6BG,CAAAA,WAC7BH,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCrLA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACpC,KAAM,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,iBAAiB,SACrC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAC,CAMxB,IAAC,EAAM,CAAH,CAAemB,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,OAA4C,EAA9B,IAAoC,EAEtD,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAET,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,6BACA,2BACA,iBACA,qCACA,CAAK,CACL,gJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,yBC5BA,uECAA,oDCAA,kECAA,yDCAA,uDCAA,4GCAA,sDCAA,4DCAA,wDCAA,iECAA,uDCAA,mECAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://terang-lms-ui/./src/lib/db/raw.ts", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/src/app/api/users/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?1552", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["import { neon } from '@neondatabase/serverless';\r\n\r\nif (!process.env.DATABASE_URL) {\r\n  throw new Error('DATABASE_URL environment variable is required');\r\n}\r\n\r\nconst sql = neon(process.env.DATABASE_URL);\r\n\r\nexport async function query(text: TemplateStringsArray, ...params: any[]) {\r\n  const result = await sql(text, ...params);\r\n  return result;\r\n}\r\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport bcrypt from 'bcryptjs';\r\nimport { query } from '@/lib/db/raw';\r\nimport { ApiResponse } from '@/types/database';\r\n\r\n// GET /api/users/[id] - Get single user\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id: idParam } = await params;\r\n    const id = parseInt(idParam);\r\n\r\n    if (isNaN(id)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid user ID' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const result = await query`\r\n      SELECT\r\n        u.id,\r\n        u.name,\r\n        u.email,\r\n        u.role,\r\n        u.institution_id,\r\n        u.created_at,\r\n        u.updated_at,\r\n        i.name as institution_name,\r\n        i.type as institution_type\r\n      FROM users u\r\n      LEFT JOIN institutions i ON u.institution_id = i.id\r\n      WHERE u.id = ${id}\r\n    `;\r\n\r\n    if (result.length === 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'User not found' } as ApiResponse,\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: result[0],\r\n      message: 'User retrieved successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Get user error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Failed to retrieve user' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/users/[id] - Update user\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id: idParam } = await params;\r\n    const id = parseInt(idParam);\r\n    const body = await request.json();\r\n\r\n    if (isNaN(id)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid user ID' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const { name, email, password, role, institutionId } = body;\r\n\r\n    // Check if user exists\r\n    const existingUser = await query`\r\n      SELECT id, email FROM users WHERE id = ${id}\r\n    `;\r\n\r\n    if (existingUser.length === 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'User not found' } as ApiResponse,\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Check if email is being changed and if it already exists\r\n    if (email && email !== existingUser[0].email) {\r\n      const emailCheck = await query`\r\n        SELECT id FROM users WHERE email = ${email} AND id != ${id}\r\n      `;\r\n\r\n      if (emailCheck.length > 0) {\r\n        return NextResponse.json(\r\n          { success: false, error: 'Email already exists' } as ApiResponse,\r\n          { status: 400 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Prepare update fields\r\n    let updateFields = [];\r\n    let hashedPassword = null;\r\n\r\n    if (password) {\r\n      const saltRounds = 10;\r\n      hashedPassword = await bcrypt.hash(password, saltRounds);\r\n    }\r\n\r\n    const result = await query`\r\n      UPDATE users SET\r\n        name = COALESCE(${name}, name),\r\n        email = COALESCE(${email}, email),\r\n        password = COALESCE(${hashedPassword}, password),\r\n        role = COALESCE(${role}, role),\r\n        institution_id = ${institutionId !== undefined ? institutionId : null},\r\n        updated_at = NOW()\r\n      WHERE id = ${id}\r\n      RETURNING id, name, email, role, institution_id, created_at, updated_at\r\n    `;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: result[0],\r\n      message: 'User updated successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Update user error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Failed to update user' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/users/[id] - Delete user\r\nexport async function DELETE(\r\n  _request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id: idParam } = await params;\r\n    const id = parseInt(idParam);\r\n\r\n    if (isNaN(id)) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Invalid user ID' } as ApiResponse,\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if user exists\r\n    const existingUser = await query`\r\n      SELECT id, role FROM users WHERE id = ${id}\r\n    `;\r\n\r\n    if (existingUser.length === 0) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'User not found' } as ApiResponse,\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Prevent deletion of super_admin users (optional safety check)\r\n    if (existingUser[0].role === 'super_admin') {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Cannot delete super admin users' } as ApiResponse,\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Delete user\r\n    await query`\r\n      DELETE FROM users WHERE id = ${id}\r\n    `;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'User deleted successfully'\r\n    } as ApiResponse);\r\n  } catch (error) {\r\n    console.error('Delete user error:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Failed to delete user' } as ApiResponse,\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/users/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\users\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/users/[id]/route\",\n        pathname: \"/api/users/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\users\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["process", "env", "DATABASE_URL", "sql", "neon", "query", "text", "params", "result", "GET", "request", "id", "idParam", "parseInt", "isNaN", "NextResponse", "json", "success", "error", "status", "data", "message", "console", "PUT", "body", "name", "email", "password", "role", "institutionId", "existingUser", "length", "hashedPassword", "bcrypt", "saltRounds", "undefined", "DELETE", "_request", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}