{"version": 3, "file": "../app/api/modules/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,4aCMO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IAAMC,EAAeD,EAAQE,KAARF,EAAe,CAACC,YAAY,CAC3CE,EAAWF,EAAaG,GAAG,CAA3BD,MAAWF,MACXI,EAAYJ,EAAaG,GAAG,CAAC,CAA7BC,KAAYJ,OAElB,GAAI,CAACE,EACH,MADGA,CACIG,CADM,CACNA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,GAAIJ,EAAW,CACb,IAAMK,EADJL,IACIK,EAAeC,EAAAA,EAAAA,CAClBC,MAAM,GACNC,IAAI,CAACC,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGH,EAAAA,OAAOA,CAACI,EAAE,CAAEC,QAAAA,CAAShB,IACxBc,CAAAA,EAAAA,CADwBd,CAAAA,CAAAA,CACxBc,CAAAA,CAAGH,EAAAA,OAAAA,CAAQT,SAAS,CAAEc,QAAAA,CAASd,MAGlCe,GAHkCf,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAsB,GAAG,CAArBK,EAAOW,IAAPX,EAAa,CACf,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,IAAMa,EAAgB,MAAMX,EAAAA,EAAAA,CAAtBW,MACG,GACNT,IAAI,CAACU,EAAAA,OAAAA,CAAAA,CACLR,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGM,EAAAA,OAAAA,CAAQpB,QAAQ,CAAEgB,QAAAA,CAAShB,KAEjCqB,EAA0B,CAFOrB,CAAAA,CAAAA,CAAAA,EAEDsB,OAAAA,CAAQC,GAAG,CAC/CJ,EAAcK,CADVH,EACa,CAAC,MAAOI,CAAzBN,GACE,EADuBM,EACjBC,CADiBD,CACF,MAAMjB,EAAAA,EAAAA,CACxBC,MAAM,CAAC,CAAEkB,KAAAA,CAAOC,EAAAA,QAAQA,CAACb,EAAAA,CAAG,EAC5BL,IAAI,CAACkB,EAAAA,QAAAA,CAAAA,CACLhB,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACc,EAAAA,QAAAA,CAASC,QAAQ,CAAEJ,EAAOV,EAAE,EAATU,CAE/B,MAAO,CACL,GAAGA,CAAM,CACTC,YAAAA,CAAcA,EAAaR,MAAAA,CAE/B,IAGF,OAAOf,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEgB,OAAAA,CAASC,CAAwB,EAC9D,CAAE,MAAOhB,EAAO,CAEd,EAFOA,KACPyB,OAAAA,CAAQzB,KAAK,CAAC,0BAA2BA,GAClCF,EAAAA,CADkCE,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAeyB,EAAKlC,CAAoB,EAC7C,GAAI,CAEF,GAAM,MACJmC,CAAI,aACJC,CAAW,UACXjC,CAAQ,WACRE,CAAS,YACTgC,CAAU,CACX,CAPY,EAOTC,IAPetC,EAAQO,IAAI,CAAZP,EAUnB,GAAI,CAACmC,GAAQ,CAAChC,EACZ,MADYA,CACLG,CADe,CACfA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,kCAAkC,CAC3C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMC,EAAS,MAAMC,EAAAA,EAAAA,CAClBC,MAAM,GACNC,IAAI,CAACC,EAAAA,OAAAA,CAAAA,CACLC,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,EAAAA,OAAAA,CAAQI,EAAE,CAAEf,IACrBiB,IADqBjB,CAAAA,CAAAA,GAGxB,GAAsB,GAAG,CAArBO,EAAOW,IAAPX,EAAa,CACf,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGxE,GAAIJ,GAAaK,CAAM,CAAC,EAAE,CAACL,SAAS,GAAKA,EACvC,OADuCA,EAAW,YAC3CC,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,+CAA+C,CACxD,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAI8B,EAAkBF,EACtB,QADsBA,GAAlBE,CACAA,EAA+B,CACjC,EADsBC,EAChBC,EAAkB,MAAM9B,EAAAA,EAAAA,CAC3BC,EADG6B,IACG,CAAC,CAAEJ,UAAAA,CAAYd,EAAAA,OAAOA,CAACc,UAAAA,CAAW,EACxCxB,IAAI,CAACU,EAAAA,OAAAA,CAAAA,CACLR,KAAK,CAACE,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGM,EAAAA,OAAAA,CAAQpB,QAAQ,CAAEA,IAE9BoC,EAAkBE,EAAgBpB,CAFJlB,CAAAA,IAEU,CAAG,EACvCuC,EADJH,EACIG,CAAKC,GAAG,IAAIF,EAAgBd,GAAG,CAACiB,CAAAA,EAAKA,CAAAA,CAAEP,IAA3BI,MAAqC,EAAI,IAAM,EAC3D,CACN,CAGA,IAAMI,EAAY,MAAMlC,EAAAA,EAAAA,CACrBmC,MAAM,CAACvB,EAAAA,OAAAA,CAAAA,CACPwB,MAAM,CAAC,MACNZ,IAAAA,UACAC,WAAAA,EAEAC,MADAlC,IACAkC,CAAYE,CACd,GACCS,SAAS,GAEZ,OAAO1C,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEqB,MAAAA,CAAQiB,CAAS,CAAC,EAAE,CAAEI,OAAAA,CAAS,8BAA8B,CAC/D,CAAExC,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOD,EAAO,CAEd,EAFOA,KACPyB,OAAAA,CAAQzB,KAAK,CAAC,yBAA0BA,GACjCF,EAAAA,CADiCE,WACjCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCnIA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAC3B,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,cAAc,SAClC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAeyC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,4IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/modules/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?7eeb", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { modules, courses, users, chapters } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/modules - Get modules for a course\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const courseId = searchParams.get('courseId');\r\n    const teacherId = searchParams.get('teacherId');\r\n    \r\n    if (!courseId) {\r\n      return NextResponse.json({ error: 'Course ID required' }, { status: 400 });\r\n    }\r\n\r\n    // Verify course exists and teacher has access\r\n    if (teacherId) {\r\n      const course = await db\r\n        .select()\r\n        .from(courses)\r\n        .where(\r\n          and(\r\n            eq(courses.id, parseInt(courseId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (course.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Course not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Get modules with chapter count\r\n    const courseModules = await db\r\n      .select()\r\n      .from(modules)\r\n      .where(eq(modules.courseId, parseInt(courseId)));\r\n\r\n    const modulesWithChapterCount = await Promise.all(\r\n      courseModules.map(async (module) => {\r\n        const chapterCount = await db\r\n          .select({ count: chapters.id })\r\n          .from(chapters)\r\n          .where(eq(chapters.moduleId, module.id));\r\n\r\n        return {\r\n          ...module,\r\n          chapterCount: chapterCount.length\r\n        };\r\n      })\r\n    );\r\n\r\n    return NextResponse.json({ modules: modulesWithChapterCount });\r\n  } catch (error) {\r\n    console.error('Error fetching modules:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/modules - Create a new module\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      description,\r\n      courseId,\r\n      teacherId,\r\n      orderIndex\r\n    } = body;\r\n\r\n    // Validate required fields\r\n    if (!name || !courseId) {\r\n      return NextResponse.json(\r\n        { error: 'Name and course ID are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify course exists and teacher has access\r\n    const course = await db\r\n      .select()\r\n      .from(courses)\r\n      .where(eq(courses.id, courseId))\r\n      .limit(1);\r\n\r\n    if (course.length === 0) {\r\n      return NextResponse.json({ error: 'Course not found' }, { status: 404 });\r\n    }\r\n\r\n    if (teacherId && course[0].teacherId !== teacherId) {\r\n      return NextResponse.json(\r\n        { error: 'Not authorized to add modules to this course' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // If no orderIndex provided, set it to the next available\r\n    let finalOrderIndex = orderIndex;\r\n    if (finalOrderIndex === undefined) {\r\n      const existingModules = await db\r\n        .select({ orderIndex: modules.orderIndex })\r\n        .from(modules)\r\n        .where(eq(modules.courseId, courseId));\r\n      \r\n      finalOrderIndex = existingModules.length > 0 \r\n        ? Math.max(...existingModules.map(m => m.orderIndex || 0)) + 1 \r\n        : 1;\r\n    }\r\n\r\n    // Create the module\r\n    const newModule = await db\r\n      .insert(modules)\r\n      .values({\r\n        name,\r\n        description,\r\n        courseId,\r\n        orderIndex: finalOrderIndex\r\n      })\r\n      .returning();\r\n\r\n    return NextResponse.json(\r\n      { module: newModule[0], message: 'Module created successfully' },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating module:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/modules',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\modules\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/modules/route\",\n        pathname: \"/api/modules\",\n        filename: \"route\",\n        bundlePath: \"app/api/modules/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\modules\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "searchParams", "nextUrl", "courseId", "get", "teacherId", "NextResponse", "json", "error", "status", "course", "db", "select", "from", "courses", "where", "and", "eq", "id", "parseInt", "limit", "length", "courseModules", "modules", "modulesWithChapterCount", "Promise", "all", "map", "module", "chapterCount", "count", "chapters", "moduleId", "console", "POST", "name", "description", "orderIndex", "body", "finalOrderIndex", "undefined", "existingModules", "Math", "max", "m", "newModule", "insert", "values", "returning", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}