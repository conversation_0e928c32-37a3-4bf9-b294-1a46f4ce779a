try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="baeddbe0-67d0-4f37-8a43-429b258e6270",t._sentryDebugIdIdentifier="sentry-dbid-baeddbe0-67d0-4f37-8a43-429b258e6270")}catch(t){}(()=>{var t={};t.id=4012,t.ids=[4012],t.modules={3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5334:(t,e,i)=>{"use strict";i.r(e),i.d(e,{patchFetch:()=>H,routeModule:()=>A,serverHooks:()=>$,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>h});var s={};i.r(s),i.d(s,{DELETE:()=>I,GET:()=>y,HEAD:()=>C,OPTIONS:()=>L,PATCH:()=>S,POST:()=>m,PUT:()=>O});var a=i(3690),n=i(56947),u=i(75250),r=i(63033),o=i(62187),d=i(138),p=i(7688);if(!process.env.DATABASE_URL)throw Error("DATABASE_URL environment variable is required");let c=(0,d.lw)(process.env.DATABASE_URL);async function _(t){try{let e,i,{searchParams:s}=new URL(t.url),a=s.get("search")||"",n=s.get("status")||"all",u=s.get("plan")||"all",r=parseInt(s.get("limit")||"50"),d=parseInt(s.get("offset")||"0"),p=[],_=[],E=1;a&&(p.push(`(i.name ILIKE $${E} OR i.type ILIKE $${E+1})`),_.push(`%${a}%`,`%${a}%`),E+=2),"all"!==n&&(p.push(`i.payment_status = $${E}`),_.push(n),E++),"all"!==u&&(p.push(`i.subscription_plan = $${E}`),_.push(u),E++),a&&"all"!==n&&"all"!==u?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
            AND i.payment_status = ${n}
            AND i.subscription_plan = ${u}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
            AND i.payment_status = ${n}
            AND i.subscription_plan = ${u}
        `]):a&&"all"!==n?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
            AND i.payment_status = ${n}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
            AND i.payment_status = ${n}
        `]):a&&"all"!==u?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
            AND i.subscription_plan = ${u}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
            AND i.subscription_plan = ${u}
        `]):"all"!==n&&"all"!==u?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${n}
            AND i.subscription_plan = ${u}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${n}
            AND i.subscription_plan = ${u}
        `]):a?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${a}%`} OR i.type ILIKE ${`%${a}%`})
        `]):"all"!==n?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${n}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${n}
        `]):"all"!==u?[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.subscription_plan = ${u}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.subscription_plan = ${u}
        `]):[e,i]=await Promise.all([c`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${r} OFFSET ${d}
        `,c`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
        `]);let l=e,N=parseInt(i[0]?.total||"0"),T=(await c`
      SELECT 
        COUNT(*) as total_institutions,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_institutions,
        COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_institutions,
        COUNT(CASE WHEN payment_status = 'unpaid' AND payment_due_date < NOW() THEN 1 END) as overdue_institutions,
        SUM(student_count) as total_students,
        SUM(teacher_count) as total_teachers
      FROM institutions
    `)[0],y={success:!0,data:{institutions:l,total:N,summary:{totalInstitutions:parseInt(T.total_institutions||"0"),paidInstitutions:parseInt(T.paid_institutions||"0"),unpaidInstitutions:parseInt(T.unpaid_institutions||"0"),overdueInstitutions:parseInt(T.overdue_institutions||"0"),totalStudents:parseInt(T.total_students||"0"),totalTeachers:parseInt(T.total_teachers||"0")}}};return o.NextResponse.json(y)}catch(t){return console.error("Error fetching subscriptions:",t),o.NextResponse.json({success:!1,error:"Failed to fetch subscription data"},{status:500})}}async function E(t){try{let e,{institutionIds:i,paymentStatus:s,paymentDueDate:a}=await t.json();if(!i||!Array.isArray(i)||0===i.length)return o.NextResponse.json({success:!1,error:"Institution IDs are required and must be an array"},{status:400});if(!s||!["paid","unpaid"].includes(s))return o.NextResponse.json({success:!1,error:"Valid payment status is required (paid or unpaid)"},{status:400});let n=i.map(t=>{let e=parseInt(t);if(isNaN(e))throw Error(`Invalid institution ID: ${t}`);return e});if(e=a?await c`
        UPDATE institutions 
        SET 
          payment_status = ${s},
          payment_due_date = ${a},
          updated_at = NOW()
        WHERE id = ANY(${n})
      `:"paid"===s?await c`
        UPDATE institutions 
        SET 
          payment_status = ${s},
          payment_due_date = NULL,
          updated_at = NOW()
        WHERE id = ANY(${n})
      `:await c`
        UPDATE institutions 
        SET 
          payment_status = ${s},
          updated_at = NOW()
        WHERE id = ANY(${n})
      `,0===e.length)return o.NextResponse.json({success:!1,error:"No institutions found with the provided IDs"},{status:404});let u={success:!0,data:{message:`Successfully updated ${n.length} institution(s)`,updatedCount:n.length}};return o.NextResponse.json(u)}catch(e){console.error("Error updating subscription status:",e);let t={success:!1,error:e instanceof Error?e.message:"Failed to update subscription status"};return o.NextResponse.json(t,{status:500})}}let l={...r},N="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;function T(t,e){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof t?t:new Proxy(t,{apply:(t,i,s)=>{let a;try{let t=N?.getStore();a=t?.headers}catch{}return p.wrapRouteHandlerWithSentry(t,{method:e,parameterizedRoute:"/api/subscriptions",headers:a}).apply(i,s)}})}let y=T(_,"GET"),m=T(void 0,"POST"),O=T(E,"PUT"),S=T(void 0,"PATCH"),I=T(void 0,"DELETE"),C=T(void 0,"HEAD"),L=T(void 0,"OPTIONS"),A=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/subscriptions/route",pathname:"/api/subscriptions",filename:"route",bundlePath:"app/api/subscriptions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\subscriptions\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:R,workUnitAsyncStorage:h,serverHooks:$}=A;function H(){return(0,u.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:h})}},8086:t=>{"use strict";t.exports=require("module")},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:t=>{"use strict";t.exports=require("require-in-the-middle")},19771:t=>{"use strict";t.exports=require("process")},21820:t=>{"use strict";t.exports=require("os")},28354:t=>{"use strict";t.exports=require("util")},29021:t=>{"use strict";t.exports=require("fs")},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:t=>{"use strict";t.exports=require("node:child_process")},33873:t=>{"use strict";t.exports=require("path")},36686:t=>{"use strict";t.exports=require("diagnostics_channel")},37067:t=>{"use strict";t.exports=require("node:http")},38522:t=>{"use strict";t.exports=require("node:zlib")},41692:t=>{"use strict";t.exports=require("node:tls")},44708:t=>{"use strict";t.exports=require("node:https")},44725:t=>{function e(t){var e=Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}e.keys=()=>[],e.resolve=e,e.id=44725,t.exports=e},44870:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:t=>{"use strict";t.exports=require("node:os")},53053:t=>{"use strict";t.exports=require("node:diagnostics_channel")},55511:t=>{"use strict";t.exports=require("crypto")},56801:t=>{"use strict";t.exports=require("import-in-the-middle")},57075:t=>{"use strict";t.exports=require("node:stream")},57975:t=>{"use strict";t.exports=require("node:util")},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:t=>{"use strict";t.exports=require("node:fs")},73566:t=>{"use strict";t.exports=require("worker_threads")},74998:t=>{"use strict";t.exports=require("perf_hooks")},75919:t=>{"use strict";t.exports=require("node:worker_threads")},76760:t=>{"use strict";t.exports=require("node:path")},77030:t=>{"use strict";t.exports=require("node:net")},78335:()=>{},79551:t=>{"use strict";t.exports=require("url")},79646:t=>{"use strict";t.exports=require("child_process")},80481:t=>{"use strict";t.exports=require("node:readline")},83997:t=>{"use strict";t.exports=require("tty")},84297:t=>{"use strict";t.exports=require("async_hooks")},86592:t=>{"use strict";t.exports=require("node:inspector")},94735:t=>{"use strict";t.exports=require("events")},96487:()=>{}};var e=require("../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[5250,7688,8036,138],()=>i(5334));module.exports=s})();
//# sourceMappingURL=route.js.map