try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="9c7b2e46-9aef-44d8-87cd-0d5fd8086c3d",e._sentryDebugIdIdentifier="sentry-dbid-9c7b2e46-9aef-44d8-87cd-0d5fd8086c3d")}catch(e){}"use strict";(()=>{var e={};e.id=1786,e.ids=[1786],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63154:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>k,routeModule:()=>b,serverHooks:()=>j,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>P});var t={};s.r(t),s.d(t,{DELETE:()=>A,GET:()=>g,HEAD:()=>I,OPTIONS:()=>T,PATCH:()=>w,POST:()=>m,PUT:()=>v});var o=s(3690),n=s(56947),i=s(75250),a=s(63033),u=s(62187),d=s(18621),c=s(32230),p=s(74683),l=s(7688);async function x(e){try{let r=e.nextUrl.searchParams.get("teacherId");if(!r)return u.NextResponse.json({success:!1,error:"Teacher ID required"},{status:400});let s=(await d.db.select({id:c.classes.id,name:c.classes.name,description:c.classes.description,institutionId:c.classes.institutionId,teacherId:c.classes.teacherId,coverPicture:c.classes.coverPicture,createdAt:c.classes.createdAt,updatedAt:c.classes.updatedAt}).from(c.classes).where((0,p.eq)(c.classes.teacherId,parseInt(r)))).map(e=>({...e,studentCount:0,courseCount:0}));return u.NextResponse.json({success:!0,classes:s})}catch(e){return console.error("Error fetching classes:",e),u.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}async function q(e){try{let{name:r,description:s,teacherId:t,institutionId:o,coverPicture:n}=await e.json();if(!r||!t||!o)return u.NextResponse.json({error:"Name, teacher ID, and institution ID are required"},{status:400});let i=await d.db.select().from(c.users).where((0,p.Uo)((0,p.eq)(c.users.id,t),(0,p.eq)(c.users.institutionId,o),(0,p.eq)(c.users.role,"teacher"))).limit(1);if(0===i.length)return u.NextResponse.json({error:"Teacher not found or not authorized"},{status:403});let a=await d.db.insert(c.classes).values({name:r,description:s,teacherId:t,institutionId:o,coverPicture:n}).returning();return u.NextResponse.json({success:!0,class:a[0],message:"Class created successfully"},{status:201})}catch(e){return console.error("Error creating class:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let h={...a},f="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,t)=>{let o;try{let e=f?.getStore();o=e?.headers}catch{}return l.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/classes",headers:o}).apply(s,t)}})}let g=y(x,"GET"),m=y(q,"POST"),v=y(void 0,"PUT"),w=y(void 0,"PATCH"),A=y(void 0,"DELETE"),I=y(void 0,"HEAD"),T=y(void 0,"OPTIONS"),b=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/classes/route",pathname:"/api/classes",filename:"route",bundlePath:"app/api/classes/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\classes\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:E,workUnitAsyncStorage:P,serverHooks:j}=b;function k(){return(0,i.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:P})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[5250,7688,8036,138,1617,2957],()=>s(63154));module.exports=t})();
//# sourceMappingURL=route.js.map