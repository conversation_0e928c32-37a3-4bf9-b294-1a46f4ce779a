{"version": 3, "file": "../app/api/enrollments/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,oXCMO,eAAeA,EACpBC,CAAoB,CACpB,CAFoBD,OAElBE,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,CAAEC,IAAE,CAAE,CAAG,MAAMD,EACfE,EAAeC,EADAH,MACAG,CAASF,EAAAA,CAAAA,EACTF,EAAQK,KAARL,EAAe,CAACM,EACxBA,UADoC,CACvBC,GAAG,CAAC,QAE9B,GAAIC,MAAML,GACR,OAAOM,EADCN,CAAAA,EAAe,SAChBM,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,wBAAwB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAG7E,GAAa,WAATC,EAAmB,CAErB,IAAMC,EAAa,MAAMC,EAAnBD,EAAmBC,CACtBC,MAAM,CAAC,CACNd,EAAAA,CAAIe,EAAAA,iBAAiBA,CAACf,EAAE,CACxBgB,QAAAA,CAAUD,EAAAA,iBAAiBA,CAACC,QAAQ,CACpCC,OAAAA,CAASF,EAAAA,iBAAiBA,CAACE,OAAO,CAClCC,UAAAA,CAAYH,EAAAA,iBAAiBA,CAACG,UAAU,CACxCC,UAAAA,CAAYC,EAAAA,OAAOA,CAACC,IAAI,CACxBC,UAAAA,CAAYF,EAAAA,OAAOA,CAACE,UAAAA,CACtB,EACCC,IAAI,CAACR,EAAAA,iBAAAA,CAAAA,CACLS,QAAQ,CAACJ,EAAAA,OAAAA,CAASK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGV,EAAAA,iBAAiBA,CAACC,QAAQ,CAAEI,EAAAA,OAAOA,CAACpB,EAAE,GAC3D0B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGV,EAAAA,iBAAiBA,CAACf,EAAE,CAAEC,IAC/B0B,KAAK,CAAC,EADyB1B,CAAAA,CAAAA,EAGR,GAAG,CAAzBW,EAAWgB,MAAM,CACnB,CADEhB,MACKL,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,8BAA8B,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGnF,OAAOH,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEI,UAAAA,CAAYA,CAAU,CAAC,IACpD,CAAO,GAAa,YAATD,EA4BT,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,8CAA8C,CAAG,CAAEC,MAAAA,CAAQ,GAAI,EA5BpE,EAE7B,IAAME,EAAa,MAAMC,EAAnBD,EAAmBC,CACtBC,MAAM,CAAC,CACNd,EAAAA,CAAI6B,EAAAA,kBAAkBA,CAAC7B,EAAE,CACzB8B,SAAAA,CAAWD,EAAAA,kBAAkBA,CAACC,SAAS,CACvCd,QAAAA,CAAUa,EAAAA,kBAAkBA,CAACb,QAAQ,CACrCE,UAAAA,CAAYW,EAAAA,kBAAkBA,CAACX,UAAU,CACzCa,WAAAA,CAAaF,EAAAA,kBAAkBA,CAACE,WAAW,CAC3CC,UAAAA,CAAYH,EAAAA,kBAAkBA,CAACG,UAAU,CACzCC,oBAAAA,CAAsBJ,EAAAA,kBAAkBA,CAACI,oBAAoB,CAC7DC,WAAAA,CAAaC,EAAAA,KAAKA,CAACd,IAAI,CACvBe,YAAAA,CAAcD,EAAAA,KAAKA,CAACE,KAAK,CACzBlB,UAAAA,CAAYC,EAAAA,OAAOA,CAACC,IAAI,CACxBC,UAAAA,CAAYF,EAAAA,OAAOA,CAACE,UACtB,GACCC,IAAI,CAACM,EAAAA,kBAAAA,CAAAA,CACLL,QAAQ,CAACW,EAAAA,KAAAA,CAAOV,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGI,EAAAA,kBAAAA,CAAmBC,SAAS,CAAEK,EAAAA,KAAAA,CAAMnC,EAAE,GACzDwB,QAAQ,CAACJ,EAAAA,OAAAA,CAASK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGI,EAAAA,kBAAAA,CAAmBb,QAAQ,CAAEI,EAAAA,OAAAA,CAAQpB,EAAE,GAC5D0B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGI,EAAAA,kBAAAA,CAAmB7B,EAAE,CAAEC,IAChC0B,KAAK,CAAC,EAD0B1B,CAAAA,CAAAA,EAGT,GAAG,CAAzBW,EAAWgB,MAAM,CACnB,CADEhB,MACKL,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,+BAA+B,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGpF,OAAOH,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAE8B,OAAAA,EAAS,EAAM1B,UAAAA,CAAYA,CAAU,CAAC,IACnE,CAGF,CAAE,KAHO,CAGAH,EAAO,CAEd,EAFOA,KACP8B,OAAAA,CAAQ9B,KAAK,CAAC,6BAA8BA,GACrCF,EADqCE,CAAAA,WACrCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAe8B,EACpB1C,CAAoB,CACpB,IAFoB0C,IAElBzC,CAAM,CAAuC,EAE/C,GAAI,CACF,GAAM,IAAEC,CAAE,CAAE,CAAG,MAAMD,EACfE,EAAeC,EADAH,MACAG,CAASF,EAAAA,CACxBI,EAAeN,EAAQK,KAARL,EAAe,CAA9BM,YAA2C,CAC3CO,EAAOP,EAAPO,GAAuB,CAAC,MAAjBP,EACPqC,EAAYrC,EAAaC,GAAG,CAAC,CAA7BoC,KAAYrC,OAElB,GAAIE,MAAML,GACR,OAAOM,EADCN,CAAAA,EAAe,SAChBM,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,wBAAwB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAG7E,GAAI,CAAC+B,EACH,OADGA,EAAW,YACPlC,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAG3E,GAAa,WAATC,EAAmB,CAErB,IAAMC,EAAa,MAAMC,EAAnBD,EAAmBC,CACtBC,MAAM,CAAC,CACNd,EAAAA,CAAIe,EAAAA,iBAAiBA,CAACf,EAAE,CACxBgB,QAAAA,CAAUD,EAAAA,iBAAiBA,CAACC,QAAQ,CACpCC,OAAAA,CAASF,EAAAA,iBAAiBA,CAACE,OAAO,CAClCwB,SAAAA,CAAWrB,EAAAA,OAAOA,CAACqB,SAAAA,CACrB,EACClB,IAAI,CAACR,EAAAA,iBAAAA,CAAAA,CACLS,QAAQ,CAACJ,EAAAA,OAAAA,CAASK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGV,EAAAA,iBAAiBA,CAACC,QAAQ,CAAEI,EAAAA,OAAOA,CAACpB,EAAE,GAC3D0B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGV,EAAAA,iBAAiBA,CAACf,EAAE,CAAEC,IAC/B0B,KAAK,CAAC,EADyB1B,CAAAA,CAAAA,EAG9BW,GAAyB,GAAdgB,IAAXhB,EAAiB,CACnB,OAAOL,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,8BAA8B,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAInF,GAAIE,CAAU,CAAC,EAAE,CAAC6B,SAAS,GAAKvC,SAASuC,GACvC,MADuCA,CAChClC,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,2CAA2C,CACpD,CAAEC,MAAAA,CAAQ,GAAI,GAOlB,OAFA,MAAMG,EAAAA,EAAAA,CAAG6B,MAAM,CAAC3B,EAAAA,iBAAAA,CAAAA,CAAmBW,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGV,EAAAA,iBAAAA,CAAkBf,EAAE,CAAEC,IAE3DM,EAAAA,MAF2DN,CAAAA,CAAAA,IAE3DM,CAAaC,IAAI,CAAC,CAAE8B,OAAAA,EAAU,EAAMK,OAAAA,CAAS,wCAAyC,EAC/F,CAAO,GAAa,YAAThC,EA+BT,OAAOJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,8CAA8C,CAAG,CAAEC,MAAAA,CAAQ,GAAI,EA/BpE,EAE7B,IAAME,EAAa,MAAMC,EAAnBD,EAAmBC,CACtBC,MAAM,CAAC,CACNd,EAAAA,CAAI6B,EAAAA,kBAAkBA,CAAC7B,EAAE,CACzB8B,SAAAA,CAAWD,EAAAA,kBAAkBA,CAACC,SAAS,CACvCd,QAAAA,CAAUa,EAAAA,kBAAkBA,CAACb,QAAQ,CACrCyB,SAAAA,CAAWrB,EAAAA,OAAOA,CAACqB,SAAAA,CACrB,EACClB,IAAI,CAACM,EAAAA,kBAAAA,CAAAA,CACLL,QAAQ,CAACJ,EAAAA,OAAAA,CAASK,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGI,EAAAA,kBAAkBA,CAACb,QAAQ,CAAEI,EAAAA,OAAOA,CAACpB,EAAE,GAC5D0B,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGI,EAAAA,kBAAkBA,CAAC7B,EAAE,CAAEC,IAChC0B,KAAK,CAAC,EAD0B1B,CAGnC,CAHmCA,EAGT,GAAG,CAAzBW,EAAWgB,MAAM,CACnB,CADEhB,MACKL,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,+BAA+B,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAIpF,GAAIE,CAAU,CAAC,EAAE,CAAC6B,SAAS,GAAKvC,SAASuC,GACvC,MADuCA,CAAAA,EAAY,YAC5ClC,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,2CAA2C,CACpD,CAAEC,MAAAA,CAAQ,GAAI,GAOlB,OAFA,MAAMG,EAAAA,EAAAA,CAAG6B,MAAM,CAACb,EAAAA,kBAAAA,CAAAA,CAAoBH,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGI,EAAAA,kBAAAA,CAAmB7B,EAAE,CAAEC,IAE7DM,EAAAA,MAF6DN,CAAAA,CAAAA,IAE7DM,CAAaC,IAAI,CAAC,CAAE8B,OAAAA,EAAU,EAAMK,OAAAA,CAAS,yCAA0C,EAChG,CAGF,CAAE,KAHO,CAGAlC,EAAO,CAEd,EAFOA,KACP8B,OAAAA,CAAQ9B,KAAK,CAAC,6BAA8BA,GACrCF,EADqCE,CAAAA,WACrCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCjKA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAItB,UAA6B,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAIH,EAAoB,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,uBAAuB,CAC3C,OAAO,EACf,CAAO,CAAC,CAAC,KAAK,CAAC,EAAS,EACpB,CAAC,CACF,CAAC,CAIC,IAAC,EAAM,CAAH,CAAekC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,OAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,mCACA,iCACA,iBACA,2CACA,CAAK,CACL,sJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/src/app/api/enrollments/[id]/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?3e09", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { courseEnrollments, studentEnrollments, courses, users } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/enrollments/[id] - Get a specific enrollment\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const enrollmentId = parseInt(id);\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const type = searchParams.get('type'); // 'course' or 'student'\r\n    \r\n    if (isNaN(enrollmentId)) {\r\n      return NextResponse.json({ error: 'Invalid enrollment ID' }, { status: 400 });\r\n    }\r\n\r\n    if (type === 'course') {\r\n      // Get course enrollment\r\n      const enrollment = await db\r\n        .select({\r\n          id: courseEnrollments.id,\r\n          courseId: courseEnrollments.courseId,\r\n          classId: courseEnrollments.classId,\r\n          enrolledAt: courseEnrollments.enrolledAt,\r\n          courseName: courses.name,\r\n          courseCode: courses.courseCode\r\n        })\r\n        .from(courseEnrollments)\r\n        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))\r\n        .where(eq(courseEnrollments.id, enrollmentId))\r\n        .limit(1);\r\n\r\n      if (enrollment.length === 0) {\r\n        return NextResponse.json({ error: 'Course enrollment not found' }, { status: 404 });\r\n      }\r\n\r\n      return NextResponse.json({ enrollment: enrollment[0] });\r\n    } else if (type === 'student') {\r\n      // Get student enrollment\r\n      const enrollment = await db\r\n        .select({\r\n          id: studentEnrollments.id,\r\n          studentId: studentEnrollments.studentId,\r\n          courseId: studentEnrollments.courseId,\r\n          enrolledAt: studentEnrollments.enrolledAt,\r\n          completedAt: studentEnrollments.completedAt,\r\n          finalScore: studentEnrollments.finalScore,\r\n          certificateGenerated: studentEnrollments.certificateGenerated,\r\n          studentName: users.name,\r\n          studentEmail: users.email,\r\n          courseName: courses.name,\r\n          courseCode: courses.courseCode\r\n        })\r\n        .from(studentEnrollments)\r\n        .leftJoin(users, eq(studentEnrollments.studentId, users.id))\r\n        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))\r\n        .where(eq(studentEnrollments.id, enrollmentId))\r\n        .limit(1);\r\n\r\n      if (enrollment.length === 0) {\r\n        return NextResponse.json({ error: 'Student enrollment not found' }, { status: 404 });\r\n      }\r\n\r\n      return NextResponse.json({ success: true, enrollment: enrollment[0] });\r\n    } else {\r\n      return NextResponse.json({ error: 'Type parameter required (course or student)' }, { status: 400 });\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching enrollment:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/enrollments/[id] - Remove an enrollment\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n    const enrollmentId = parseInt(id);\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const type = searchParams.get('type'); // 'course' or 'student'\r\n    const teacherId = searchParams.get('teacherId');\r\n    \r\n    if (isNaN(enrollmentId)) {\r\n      return NextResponse.json({ error: 'Invalid enrollment ID' }, { status: 400 });\r\n    }\r\n\r\n    if (!teacherId) {\r\n      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });\r\n    }\r\n\r\n    if (type === 'course') {\r\n      // Remove course enrollment (course from class)\r\n      const enrollment = await db\r\n        .select({\r\n          id: courseEnrollments.id,\r\n          courseId: courseEnrollments.courseId,\r\n          classId: courseEnrollments.classId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(courseEnrollments)\r\n        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))\r\n        .where(eq(courseEnrollments.id, enrollmentId))\r\n        .limit(1);\r\n\r\n      if (enrollment.length === 0) {\r\n        return NextResponse.json({ error: 'Course enrollment not found' }, { status: 404 });\r\n      }\r\n\r\n      // Verify teacher has permission\r\n      if (enrollment[0].teacherId !== parseInt(teacherId)) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to remove this enrollment' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n\r\n      // Remove the course enrollment\r\n      await db.delete(courseEnrollments).where(eq(courseEnrollments.id, enrollmentId));\r\n\r\n      return NextResponse.json({ success : true, message: 'Course enrollment removed successfully' });\r\n    } else if (type === 'student') {\r\n      // Remove student enrollment (student from course)\r\n      const enrollment = await db\r\n        .select({\r\n          id: studentEnrollments.id,\r\n          studentId: studentEnrollments.studentId,\r\n          courseId: studentEnrollments.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(studentEnrollments)\r\n        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))\r\n        .where(eq(studentEnrollments.id, enrollmentId))\r\n        .limit(1);\r\n\r\n      if (enrollment.length === 0) {\r\n        return NextResponse.json({ error: 'Student enrollment not found' }, { status: 404 });\r\n      }\r\n\r\n      // Verify teacher has permission\r\n      if (enrollment[0].teacherId !== parseInt(teacherId)) {\r\n        return NextResponse.json(\r\n          { error: 'Not authorized to remove this enrollment' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n\r\n      // Remove the student enrollment\r\n      await db.delete(studentEnrollments).where(eq(studentEnrollments.id, enrollmentId));\r\n\r\n      return NextResponse.json({ success : true, message: 'Student enrollment removed successfully' });\r\n    } else {\r\n      return NextResponse.json({ error: 'Type parameter required (course or student)' }, { status: 400 });\r\n    }\r\n  } catch (error) {\r\n    console.error('Error removing enrollment:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/enrollments/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\enrollments\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/enrollments/[id]/route\",\n        pathname: \"/api/enrollments/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/enrollments/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\enrollments\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "params", "id", "enrollmentId", "parseInt", "nextUrl", "searchParams", "get", "isNaN", "NextResponse", "json", "error", "status", "type", "enrollment", "db", "select", "courseEnrollments", "courseId", "classId", "enrolledAt", "courseName", "courses", "name", "courseCode", "from", "leftJoin", "eq", "where", "limit", "length", "studentEnrollments", "studentId", "completedAt", "finalScore", "certificateGenerated", "studentName", "users", "studentEmail", "email", "success", "console", "DELETE", "teacherId", "delete", "message", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}