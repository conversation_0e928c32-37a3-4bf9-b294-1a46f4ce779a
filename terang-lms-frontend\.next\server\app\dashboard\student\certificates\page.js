try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f4c01c8f-c62d-490f-bd1a-317d9ab6a74a",e._sentryDebugIdIdentifier="sentry-dbid-f4c01c8f-c62d-490f-bd1a-317d9ab6a74a")}catch(e){}(()=>{var e={};e.id=4610,e.ids=[4610],e.modules={116:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(91754);function s({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}r(93491),r(76328)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var a=r(91754);r(93491);var s=r(82233);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30707:(e,t,r)=>{Promise.resolve().then(r.bind(r,40932))},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38006:(e,t,r)=>{Promise.resolve().then(r.bind(r,116))},38522:e=>{"use strict";e.exports=require("node:zlib")},40932:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>p,generateMetadata:()=>c,generateViewport:()=>u});var s=r(63033),i=r(1472),n=r(7688),o=(0,i.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\certificates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\certificates\\page.tsx","default");let d={...s},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;a="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let a,s,i;try{let e=l?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,i=e?.headers}catch{}return n.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student/certificates",componentType:"Page",sentryTraceHeader:a,baggageHeader:s,headers:i}).apply(t,r)}}):o;let c=void 0,p=void 0,u=void 0,m=a},41692:e=>{"use strict";e.exports=require("node:tls")},43308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(91754),s=r(9260),i=r(56682),n=r(80601),o=r(69622),d=r(86857),l=r(69931),c=r(80506);let p=(0,r(55732).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var u=r(79233),m=r(82763),f=r(48071);function x(){let{user:e}=(0,f.A)(),t=[{id:1,courseName:"Chemistry Basics",courseCode:"CHEM101",certificateId:"CERT-2024-001",completedAt:"2024-07-30",finalScore:95,instructor:"Prof. Johnson",status:"issued",downloadUrl:"#"}],r=[{id:2,courseName:"Introduction to Algebra",courseCode:"MATH101",progress:85,estimatedCompletion:"2024-08-15",status:"in_progress"}],x=r=>{let a=t.find(e=>e.certificateId===r);if(a&&e)return{studentName:e.name??"Student",institutionName:"Terang University",courseName:a.courseName,courseCode:a.courseCode,completionDate:new Date(a.completedAt).toLocaleDateString(),finalScore:a.finalScore,instructorName:a.instructor,certificateId:a.certificateId}},h=e=>{let t=x(e);t&&(0,m.Ct)(t)},g=e=>{let t=x(e);t&&(0,m.hr)(t)},y=e=>{let t=x(e);t&&(0,m.a7)(t)};return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"StudentCertificatesPage","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"My Certificates"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"View and download your course completion certificates"})]})}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(o.A,{className:"h-5 w-5","data-sentry-element":"Award","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Earned Certificates"})]}),(0,a.jsx)(s.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Certificates you have earned by completing courses"})]}),(0,a.jsx)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:t.length>0?(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsx)("div",{className:"rounded-lg border p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-yellow-400 to-yellow-600",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.courseName}),(0,a.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Course Code: ",e.courseCode]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Certificate ID"}),(0,a.jsx)("p",{className:"font-mono",children:e.certificateId})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Completed"}),(0,a.jsx)("p",{children:new Date(e.completedAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Final Score"}),(0,a.jsxs)("p",{className:"font-semibold",children:[e.finalScore,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-muted-foreground",children:"Instructor"}),(0,a.jsx)("p",{children:e.instructor})]})]}),(0,a.jsxs)(n.E,{variant:"default",className:"w-fit",children:[(0,a.jsx)(d.A,{className:"mr-1 h-3 w-3"}),"Verified Certificate"]})]}),(0,a.jsxs)("div",{className:"ml-4 flex flex-col space-y-2",children:[(0,a.jsxs)(i.$,{size:"sm",onClick:()=>h(e.certificateId),children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Download"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>y(e.certificateId),children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Preview"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>g(e.certificateId),children:[(0,a.jsx)(p,{className:"mr-2 h-4 w-4"}),"Share"]})]})]})},e.id))}):(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(o.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No certificates yet"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:"Complete a course to earn your first certificate."})]})})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(s.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(u.A,{className:"h-5 w-5","data-sentry-element":"Calendar","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Certificates in Progress"})]}),(0,a.jsx)(s.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"Courses you're working on that will earn certificates"})]}),(0,a.jsx)(s.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:r.length>0?(0,a.jsx)("div",{className:"space-y-4",children:r.map(e=>(0,a.jsx)("div",{className:"rounded-lg border p-6",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-gray-400 to-gray-600",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:e.courseName}),(0,a.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Course Code: ",e.courseCode]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Progress"}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)("div",{className:"bg-muted h-2 w-full rounded-full",children:(0,a.jsx)("div",{className:"bg-primary h-2 rounded-full",style:{width:`${e.progress}%`}})}),(0,a.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Estimated completion:"," ",new Date(e.estimatedCompletion).toLocaleDateString()]})]}),(0,a.jsx)(n.E,{variant:"outline",className:"w-fit",children:"In Progress"})]})})},e.id))}):(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(u.A,{className:"text-muted-foreground mx-auto h-12 w-12"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-semibold",children:"No courses in progress"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1 text-sm",children:"Enroll in a course to start working towards a certificate."})]})})]}),(0,a.jsxs)(s.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(s.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(s.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:"About Certificates"})}),(0,a.jsxs)(s.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Self-paced Courses"}),(0,a.jsxs)("ul",{className:"text-muted-foreground space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Certificates are automatically generated upon completion"}),(0,a.jsx)("li",{children:"• Must achieve minimum score on all quizzes"}),(0,a.jsx)("li",{children:"• Complete all modules and chapters"}),(0,a.jsx)("li",{children:"• Available for immediate download"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Verified Courses"}),(0,a.jsxs)("ul",{className:"text-muted-foreground space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Requires manual verification by instructor"}),(0,a.jsx)("li",{children:"• May include additional assessments"}),(0,a.jsx)("li",{children:"• Higher credibility and recognition"}),(0,a.jsx)("li",{children:"• Processing time: 1-3 business days"})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Certificate Features"}),(0,a.jsxs)("ul",{className:"text-muted-foreground space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Unique certificate ID for verification"}),(0,a.jsx)("li",{children:"• Digital signature and timestamp"}),(0,a.jsx)("li",{children:"• Shareable on professional networks"}),(0,a.jsx)("li",{children:"• PDF format for easy printing"}),(0,a.jsx)("li",{children:"• Permanent record in your profile"})]})]})]})]})]})}},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},60290:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>v,generateImageMetadata:()=>g,generateMetadata:()=>h,generateViewport:()=>y,metadata:()=>u});var s=r(63033),i=r(18188),n=r(5434),o=r(45188),d=r(67999),l=r(4590),c=r(23064),p=r(7688);let u={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,i.jsx)(n.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,i.jsxs)(l.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,i.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,i.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,i.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,i.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let f={...s},x="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;a=new Proxy(m,{apply:(e,t,r)=>{let a,s,i;try{let e=x?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,i=e?.headers}catch{}return p.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:i}).apply(t,r)}});let h=void 0,g=void 0,y=void 0,v=a},62413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>p,tree:()=>d});var a=r(95500),s=r(56947),i=r(26052),n=r(13636),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["student",{children:["certificates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40932)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\certificates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,97890)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\certificates\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/student/certificates/page",pathname:"/dashboard/student/certificates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67659:(e,t,r)=>{Promise.resolve().then(r.bind(r,43308))},69931:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79233:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80506:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},80601:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(91754);r(93491);var s=r(16435),i=r(25758),n=r(82233);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...i}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},82763:(e,t,r)=>{"use strict";r.d(t,{Ct:()=>n,K2:()=>a,a7:()=>o,hr:()=>d,nE:()=>i});let a=()=>{let e=new Date().getFullYear(),t=Math.floor(1e4*Math.random()).toString().padStart(4,"0");return`CERT-${e}-${t}`},s=e=>`
  <!DOCTYPE html>
  <html lang="id">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Sertifikat Kelulusan</title>
      <!-- Font -->
      <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
      <style>
          :root{
              --primary:#4a90e2;
              --accent:#a370e8;
              --text:#2c3e50;
              --muted:#7f8c8d;
              --light:#f0f4f8;
              --white:#ffffff;
          }
          *{box-sizing:border-box;margin:0;padding:0}
          body{
              background:linear-gradient(135deg,var(--light),#e2e8f0);
              font-family:'Montserrat',sans-serif;
              display:flex;
              align-items:center;
              justify-content:center;
              min-height:100vh;
              padding:20px;
          }
          .certificate{
              width:100%;
              max-width:900px;
              background:var(--white);
              border-radius:16px;
              box-shadow:0 20px 40px rgba(0,0,0,.08);
              position:relative;
              overflow:hidden;
              padding:80px 80px 110px;
          }
          .certificate::before,
          .certificate::after{
              content:'';
              position:absolute;
              width:300px;
              height:300px;
              border-radius:50%;
              opacity:.05;
              z-index:0;
          }
          .certificate::before{top:-80px;left:-80px;background:radial-gradient(var(--primary),transparent 70%)}
          .certificate::after{bottom:-80px;right:-80px;background:radial-gradient(var(--accent),transparent 70%)}

          .watermark{
              position:absolute;
              top:50%;left:50%;
              transform:translate(-50%,-50%) rotate(-45deg);
              font-family:'Playfair Display',serif;
              font-size:150px;
              color:rgba(0,0,0,.03);
              font-weight:700;
              pointer-events:none;
              z-index:0;
          }

          .header{text-align:center;margin-bottom:50px}
          .title{
              font-family:'Playfair Display',serif;
              font-size:44px;
              color:var(--text);
              margin:0;
          }
          .subtitle{
              font-size:16px;
              color:var(--muted);
              margin-top:8px;
          }

          .main-content{
              text-align:center;
              margin-bottom:60px;
          }
          .awarded-to{
              font-size:16px;
              color:var(--muted);
              margin-bottom:8px;
          }
          .student-name{
              font-family:'Playfair Display',serif;
              font-size:42px;
              color:var(--text);
              position:relative;
              display:inline-block;
              margin-bottom:20px;
          }
          .student-name::after{
              content:'';
              position:absolute;
              left:50%;
              bottom:-6px;
              transform:translateX(-50%);
              width:80%;
              height:3px;
              background:linear-gradient(90deg,var(--primary),var(--accent));
              border-radius:2px;
          }
          .completion-text{
              font-size:18px;
              color:#555;
              line-height:1.6;
              max-width:600px;
              margin:0 auto 25px;
          }
          .course-details{
              display:inline-block;
              background:var(--light);
              border-radius:12px;
              padding:20px 35px;
              box-shadow:0 4px 15px rgba(0,0,0,.05);
              margin-bottom:25px;
          }
          .course-name{
              font-size:24px;
              font-weight:600;
              color:var(--text);
              margin:0;
          }
          .course-code{
              font-size:15px;
              color:var(--muted);
              margin-top:4px;
          }
          .score{
              font-size:20px;
              font-weight:700;
              color:var(--primary);
          }

          .footer{
              display:flex;
              justify-content:space-around;
              align-items:flex-end;
              border-top:1px solid #ecf0f1;
              padding-top:30px;
          }
          .signature-section{
              text-align:center;
              flex:1;
          }
          .signature-line{
              width:180px;
              height:1px;
              background:var(--muted);
              margin:0 auto 8px;
          }
          .signature-label{
              font-size:14px;
              color:var(--muted);
              line-height:1.4;
          }

          .id-date-row{
              margin-top:30px;
              display:flex;
              justify-content:space-between;
              font-size:13px;
              color:#95a5a6;
          }
      </style>
  </head>
  <body>
      <div class="certificate">
          <div class="watermark">TERANG</div>

          <!-- Konten utama -->
          <div class="header">
              <h1 class="title">Sertifikat Kelulusan</h1>
              <p class="subtitle">${e.institutionName}</p>
          </div>

          <div class="main-content">
              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>
              <h2 class="student-name">${e.studentName}</h2>
              <p class="completion-text">
                  karena telah berhasil menyelesaikan dan lulus dari program
              </p>

              <div class="course-details">
                  <h3 class="course-name">${e.courseName}</h3>
                  <div class="course-code">Kode Kursus: ${e.courseCode}</div>
              </div>

              <p class="score">Nilai Akhir: ${e.finalScore}%</p>
          </div>

          <div class="footer">
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">${e.instructorName}<br>Instruktur Kursus</p>
              </div>
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">Tanggal Kelulusan<br>${e.completionDate}</p>
              </div>
          </div>

          <div class="id-date-row">
              <span>ID Sertifikat: ${e.certificateId}</span>
              <span>Diterbitkan pada: ${e.completionDate}</span>
          </div>
      </div>
  </body>
  </html>
  `,i=e=>`
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
  <style>
      .certificate-modal-container {
          --primary:#4a90e2;
          --accent:#a370e8;
          --text:#2c3e50;
          --muted:#7f8c8d;
          --light:#f0f4f8;
          --white:#ffffff;
          font-family:'Montserrat',sans-serif;
          width: 100%;
          min-height: 600px;
          background: var(--white);
          position: relative;
      }
      .certificate-modal{
          width:100%;
          background:var(--white);
          border-radius:16px;
          box-shadow:0 20px 40px rgba(0,0,0,.08);
          position:relative;
          overflow:hidden;
          padding:60px 60px 80px;
          margin: 0;
      }
      .certificate-modal *{box-sizing:border-box;}
      
      .certificate-modal::before,
      .certificate-modal::after{
          content:'';
          position:absolute;
          width:250px;
          height:250px;
          border-radius:50%;
          opacity:.05;
          z-index:0;
      }
      .certificate-modal::before{top:-60px;left:-60px;background:radial-gradient(var(--primary),transparent 70%)}
      .certificate-modal::after{bottom:-60px;right:-60px;background:radial-gradient(var(--accent),transparent 70%)}

      .certificate-modal .watermark{
          position:absolute;
          top:50%;left:50%;
          transform:translate(-50%,-50%) rotate(-45deg);
          font-family:'Playfair Display',serif;
          font-size:100px;
          color:rgba(0,0,0,.03);
          font-weight:700;
          pointer-events:none;
          z-index:0;
      }

      .certificate-modal .header{text-align:center;margin-bottom:40px;position:relative;z-index:1;}
      .certificate-modal .title{
          font-family:'Playfair Display',serif;
          font-size:32px;
          color:var(--text);
          margin:0;
      }
      .certificate-modal .subtitle{
          font-size:14px;
          color:var(--muted);
          margin-top:8px;
      }

      .certificate-modal .main-content{
          text-align:center;
          margin-bottom:50px;
          position:relative;
          z-index:1;
      }
      .certificate-modal .awarded-to{
          font-size:14px;
          color:var(--muted);
          margin-bottom:8px;
      }
      .certificate-modal .student-name{
          font-family:'Playfair Display',serif;
          font-size:28px;
          color:var(--text);
          position:relative;
          display:inline-block;
          margin-bottom:20px;
      }
      .certificate-modal .student-name::after{
          content:'';
          position:absolute;
          left:50%;
          bottom:-6px;
          transform:translateX(-50%);
          width:80%;
          height:3px;
          background:linear-gradient(90deg,var(--primary),var(--accent));
          border-radius:2px;
      }
      .certificate-modal .completion-text{
          font-size:15px;
          color:#555;
          line-height:1.6;
          max-width:500px;
          margin:0 auto 20px;
      }
      .certificate-modal .course-details{
          display:inline-block;
          background:var(--light);
          border-radius:12px;
          padding:16px 30px;
          box-shadow:0 4px 15px rgba(0,0,0,.05);
          margin-bottom:20px;
      }
      .certificate-modal .course-name{
          font-size:18px;
          font-weight:600;
          color:var(--text);
          margin:0;
      }
      .certificate-modal .course-code{
          font-size:13px;
          color:var(--muted);
          margin-top:4px;
      }
      .certificate-modal .score{
          font-size:16px;
          font-weight:700;
          color:var(--primary);
      }

      .certificate-modal .footer{
          display:flex;
          justify-content:space-around;
          align-items:flex-end;
          border-top:1px solid #ecf0f1;
          padding-top:25px;
          position:relative;
          z-index:1;
      }
      .certificate-modal .signature-section{
          text-align:center;
          flex:1;
      }
      .certificate-modal .signature-line{
          width:140px;
          height:1px;
          background:var(--muted);
          margin:0 auto 8px;
      }
      .certificate-modal .signature-label{
          font-size:12px;
          color:var(--muted);
          line-height:1.4;
      }

      .certificate-modal .id-date-row{
          margin-top:25px;
          display:flex;
          justify-content:space-between;
          font-size:11px;
          color:#95a5a6;
          position:relative;
          z-index:1;
      }
  </style>
  
  <div class="certificate-modal-container">
      <div class="certificate-modal">
          <div class="watermark">TERANG</div>

          <div class="header">
              <h1 class="title">Sertifikat Kelulusan</h1>
              <p class="subtitle">${e.institutionName}</p>
          </div>

          <div class="main-content">
              <p class="awarded-to">Dengan bangga mempersembahkan sertifikat ini kepada</p>
              <h2 class="student-name">${e.studentName}</h2>
              <p class="completion-text">
                  karena telah berhasil menyelesaikan dan lulus dari program
              </p>

              <div class="course-details">
                  <h3 class="course-name">${e.courseName}</h3>
                  <div class="course-code">Kode Kursus: ${e.courseCode}</div>
              </div>

              <p class="score">Nilai Akhir: ${e.finalScore}%</p>
          </div>

          <div class="footer">
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">${e.instructorName}<br>Instruktur Kursus</p>
              </div>
              <div class="signature-section">
                  <div class="signature-line"></div>
                  <p class="signature-label">Tanggal Kelulusan<br>${e.completionDate}</p>
              </div>
          </div>

          <div class="id-date-row">
              <span>ID Sertifikat: ${e.certificateId}</span>
              <span>Diterbitkan pada: ${e.completionDate}</span>
          </div>
      </div>
  </div>
  `,n=async e=>{try{let t=s(e),r=await fetch("/api/certificates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({htmlContent:t})});if(!r.ok)throw Error("Failed to generate PDF");let a=await r.blob(),i=URL.createObjectURL(a),n=document.createElement("a");n.href=i,n.download=`certificate-${e.certificateId}.pdf`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)}catch(i){console.error("Error generating PDF:",i);let t=new Blob([s(e)],{type:"text/html"}),r=URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download=`certificate-${e.certificateId}.html`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r)}},o=e=>{let t=s(e),r=window.open("","_blank");r&&(r.document.write(t),r.document.close())},d=async e=>{if(navigator.share)try{await navigator.share({title:`Certificate of Completion - ${e.courseName}`,text:`I've completed ${e.courseName} with a score of ${e.finalScore}%!`,url:window.location.href})}catch(t){console.error("Error sharing certificate:",t),l(`I've completed ${e.courseName} with a score of ${e.finalScore}%! Certificate ID: ${e.certificateId}`)}else l(`I've completed ${e.courseName} with a score of ${e.finalScore}%! Certificate ID: ${e.certificateId}`)},l=e=>{navigator.clipboard.writeText(e).then(()=>{console.log("Certificate details copied to clipboard")}).catch(e=>{console.error("Failed to copy to clipboard:",e)})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},86857:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(55732).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")},97890:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>m,generateImageMetadata:()=>p,generateMetadata:()=>c,generateViewport:()=>u});var s=r(63033),i=r(1472),n=r(7688),o=(0,i.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\dashboard\\\\student\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\student\\layout.tsx","default");let d={...s},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;a="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let a,s,i;try{let e=l?.getStore();a=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,i=e?.headers}catch{}return n.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/student",componentType:"Layout",sentryTraceHeader:a,baggageHeader:s,headers:i}).apply(t,r)}}):o;let c=void 0,p=void 0,u=void 0,m=a},98254:(e,t,r)=>{Promise.resolve().then(r.bind(r,97890))}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634],()=>r(62413));module.exports=a})();
//# sourceMappingURL=page.js.map