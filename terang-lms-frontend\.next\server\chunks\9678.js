try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="68ea9a2f-32b5-454a-9ac9-ef0ba59bfb96",e._sentryDebugIdIdentifier="sentry-dbid-68ea9a2f-32b5-454a-9ac9-ef0ba59bfb96")}catch(e){}"use strict";exports.id=9678,exports.ids=[9678],exports.modules={5827:(e,r,s)=>{s.d(r,{k:()=>l});var a=s(91754),n=s(93491),t=s(66536),i=s(82233);let l=n.forwardRef(({className:e,value:r,...s},n)=>(0,a.jsx)(t.bL,{ref:n,className:(0,i.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...s,children:(0,a.jsx)(t.C1,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}));l.displayName=t.bL.displayName},80601:(e,r,s)=>{s.d(r,{E:()=>d});var a=s(91754);s(93491);var n=s(16435),t=s(25758),i=s(82233);let l=(0,t.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,asChild:s=!1,...t}){let d=s?n.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),e),...t,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},93824:(e,r,s)=>{s.d(r,{vN:()=>c,b2:()=>x});var a=s(91754),n=s(93491),t=s.n(n);let i=({option:e,index:r,questionId:s,selectedAnswer:n,onAnswerChange:i,type:l="radio",disabled:d=!1,showResults:c=!1,correctAnswer:o,isCorrect:x})=>{let g="radio"===l?n===r:Array.isArray(n)&&n.includes(r),u="radio"===l?o===r:Array.isArray(o)&&o.includes(r);return(0,a.jsxs)("label",{className:`
        flex cursor-pointer items-start space-x-3 p-3 rounded-lg border-2 transition-all
        ${!c?g?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50":u?"border-green-500 bg-green-50":g?"border-red-500 bg-red-50":"border-gray-200 bg-gray-50"}
        ${d?"opacity-50 cursor-not-allowed":""}
        ${c&&u?"ring-2 ring-green-200":""}
        ${c&&g&&!u?"ring-2 ring-red-200":""}
      `,"data-sentry-component":"Option","data-sentry-source-file":"option.tsx",children:[(0,a.jsx)("input",{type:l,name:s,value:r,checked:g,onChange:()=>{if(!d)if("radio"===l)i(s,r);else{let e=Array.isArray(n)?n:[];i(s,g?e.filter(e=>e!==r):[...e,r])}},disabled:d,className:`
          mt-0.5 h-4 w-4 shrink-0
          ${"radio"===l?"text-blue-600":"text-blue-600 rounded"}
          ${d?"cursor-not-allowed":"cursor-pointer"}
        `}),(0,a.jsxs)("span",{className:`text-sm leading-relaxed flex-1 ${d?"text-gray-500":"text-gray-900"}`,children:[(0,a.jsxs)("span",{className:"font-medium mr-2",children:[String.fromCharCode(65+r),"."]}),"string"==typeof e?e:e.content.map((e,r)=>(0,a.jsxs)(t().Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Option image ${r}`,className:"inline-block max-h-8 object-contain ml-1"}),"video"===e.type&&(0,a.jsxs)("span",{children:["[Video: ",e.value,"]"]}),"pdf"===e.type&&(0,a.jsxs)("span",{children:["[PDF: ",e.value,"]"]}),"zoom-recording"===e.type&&(0,a.jsxs)("span",{children:["[Recording: ",e.value,"]"]})]},r))]}),c&&(0,a.jsxs)("div",{className:"flex items-center ml-2",children:[u&&(0,a.jsx)("span",{className:"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full",children:"✓ Benar"}),g&&!u&&(0,a.jsx)("span",{className:"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full",children:"✗ Salah"})]})]})};var l=s(9260),d=s(80601);let c=({question:e,questionNumber:r,totalQuestions:s,selectedAnswer:n,onAnswerChange:c,showResults:o=!1,isCorrect:x,disabled:g=!1})=>{let u=r=>{g||c(e.id,r)};return(0,a.jsx)(l.Zp,{className:`
      border-2 transition-all
      ${o?x?"border-green-200 bg-green-50":"border-red-200 bg-red-50":"border-gray-200"}
    `,"data-sentry-element":"Card","data-sentry-component":"Question","data-sentry-source-file":"question.tsx",children:(0,a.jsxs)(l.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"question.tsx",children:[(0,a.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(d.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200","data-sentry-element":"Badge","data-sentry-source-file":"question.tsx",children:["Soal ",r," dari ",s]}),(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs","data-sentry-element":"Badge","data-sentry-source-file":"question.tsx",children:(e=>{switch(e){case"multiple-choice":case"multiple_choice":return"Pilihan Ganda";case"true-false":case"true_false":return"Benar/Salah";case"essay":return"Esai";default:return e}})(e.type)})]}),o&&(0,a.jsx)(d.E,{variant:x?"default":"destructive",className:x?"bg-green-600 hover:bg-green-700":"",children:x?"Benar":"Salah"})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-lg leading-relaxed text-gray-900 whitespace-pre-wrap",children:"string"==typeof e.question?e.question:Array.isArray(e.question)?e.question.map((e,r)=>(0,a.jsxs)(t().Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Question image ${r}`,className:"inline-block max-h-16 object-contain ml-2"})]},r)):(0,a.jsx)("span",{children:String(e.question)})})}),("multiple-choice"===e.type||"multiple_choice"===e.type)&&e.options&&(0,a.jsx)("div",{className:"space-y-3",children:e.options.map((r,s)=>(0,a.jsx)(i,{option:r,index:s,questionId:e.id,selectedAnswer:n,onAnswerChange:c,type:"radio",disabled:g,showResults:o,correctAnswer:e.correctAnswer,isCorrect:x},s))}),("true-false"===e.type||"true_false"===e.type)&&(0,a.jsx)("div",{className:"space-y-3",children:["true","false"].map((r,s)=>{let t=n===r,i=e.correctAnswer===r;return(0,a.jsxs)("label",{className:`
                    flex cursor-pointer items-center justify-between p-3 rounded-lg border-2 transition-all
                    ${!o?t?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50":i?"border-green-500 bg-green-50":t?"border-red-500 bg-red-50":"border-gray-200 bg-gray-50"}
                    ${g?"opacity-50 cursor-not-allowed":""}
                    ${o&&i?"ring-2 ring-green-200":""}
                    ${o&&t&&!i?"ring-2 ring-red-200":""}
                  `,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:e.id,value:r,checked:t,onChange:()=>u(r),disabled:g,className:"h-4 w-4 text-blue-600"}),(0,a.jsxs)("span",{className:`font-medium ${g?"text-gray-500":"text-gray-900"}`,children:[String.fromCharCode(65+s),". ","true"===r?"Benar":"Salah"]})]}),o&&(0,a.jsxs)("div",{className:"flex items-center",children:[i&&(0,a.jsx)("span",{className:"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full",children:"✓ Benar"}),t&&!i&&(0,a.jsx)("span",{className:"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full",children:"✗ Salah"})]})]},r)})}),"essay"===e.type&&(0,a.jsx)("textarea",{className:`
              w-full resize-none rounded-lg border-2 p-4 focus:border-transparent focus:ring-2 focus:ring-blue-500
              ${g?"bg-gray-50 cursor-not-allowed":""}
            `,rows:8,placeholder:"Ketik jawaban Anda di sini...",value:n||"",onChange:r=>{g||c(e.id,r.target.value)},disabled:g}),o&&e.explanation&&(0,a.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Penjelasan:"}),(0,a.jsx)("p",{className:"text-sm text-gray-700 leading-relaxed",children:"string"==typeof e.explanation?e.explanation:Array.isArray(e.explanation)?e.explanation.map((e,r)=>(0,a.jsxs)(t().Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:`Explanation image ${r}`,className:"inline-block max-h-16 object-contain ml-2"}),"video"===e.type&&(0,a.jsxs)("span",{children:["[Video: ",e.value,"]"]}),"pdf"===e.type&&(0,a.jsxs)("span",{children:["[PDF: ",e.value,"]"]}),"zoom-recording"===e.type&&(0,a.jsxs)("span",{children:["[Recording: ",e.value,"]"]})]},r)):(0,a.jsx)("span",{children:String(e.explanation)})})]})]})})};var o=s(56682);let x=({questions:e,currentQuestion:r,answeredQuestions:s,onQuestionSelect:n,flaggedQuestions:t=new Set,onToggleFlag:i,showFlags:d=!0,onSubmit:c,canSubmit:x=!1,isSubmitting:g=!1,reviewMode:u=!1,results:b={}})=>{let m=a=>{if(u){let s=b[e[a].id];return a===r?s?"current-correct":"current-incorrect":s?"correct":"incorrect"}return a===r?"current":s.has(a)?"answered":"unanswered"},h=e=>{switch(e){case"current":return"bg-blue-600 text-white border-blue-600";case"current-correct":return"bg-green-600 text-white border-green-600 ring-2 ring-green-200";case"current-incorrect":return"bg-red-600 text-white border-red-600 ring-2 ring-red-200";case"correct":case"answered":return"bg-green-100 text-green-800 border-green-300 hover:bg-green-200";case"incorrect":return"bg-red-100 text-red-800 border-red-300 hover:bg-red-200";case"unanswered":return"bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100";default:return"bg-gray-50 text-gray-600 border-gray-300"}},p=s.size,y=e.length-p,v=u?e.filter(e=>b[e.id]).length:0,f=u?e.filter(e=>!b[e.id]).length:0;return(0,a.jsxs)(l.Zp,{className:"h-fit sticky top-4","data-sentry-element":"Card","data-sentry-component":"QuestionBank","data-sentry-source-file":"question-bank.tsx",children:[(0,a.jsxs)(l.aR,{className:"pb-4","data-sentry-element":"CardHeader","data-sentry-source-file":"question-bank.tsx",children:[(0,a.jsx)(l.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"question-bank.tsx",children:u?"Review Soal":"Nomor Soal"}),u?(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-green-100 border border-green-300 rounded"}),(0,a.jsxs)("span",{children:["Benar: ",v]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-red-100 border border-red-300 rounded"}),(0,a.jsxs)("span",{children:["Salah: ",f]})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-green-100 border border-green-300 rounded"}),(0,a.jsxs)("span",{children:["Terjawab: ",p]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-gray-50 border border-gray-300 rounded"}),(0,a.jsxs)("span",{children:["Belum: ",y]})]})]}),d&&t.size>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)("div",{className:"w-4 h-4 bg-yellow-100 border border-yellow-400 rounded relative",children:(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"})}),(0,a.jsxs)("span",{children:["Ditandai: ",t.size]})]})]}),(0,a.jsxs)(l.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"question-bank.tsx",children:[(0,a.jsx)("div",{className:"grid grid-cols-4 gap-2",children:e.map((e,r)=>{let s=m(r),i=t.has(r);return(0,a.jsxs)(o.$,{variant:"outline",size:"sm",className:`
                  relative h-12 w-12 p-0 font-medium transition-all
                  ${h(s)}
                  ${i?"ring-2 ring-yellow-400":""}
                `,onClick:()=>n(r),children:[r+1,i&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white"})]},r)})}),(0,a.jsxs)("div",{className:"pt-4 border-t space-y-2 text-xs text-gray-600",children:[u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-600 rounded"}),(0,a.jsx)("span",{children:"Soal saat ini (Benar)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-600 rounded"}),(0,a.jsx)("span",{children:"Soal saat ini (Salah)"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-100 border border-green-300 rounded"}),(0,a.jsx)("span",{children:"Jawaban benar"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-100 border border-red-300 rounded"}),(0,a.jsx)("span",{children:"Jawaban salah"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-600 rounded"}),(0,a.jsx)("span",{children:"Soal saat ini"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-100 border border-green-300 rounded"}),(0,a.jsx)("span",{children:"Sudah dijawab"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-50 border border-gray-300 rounded"}),(0,a.jsx)("span",{children:"Belum dijawab"})]})]}),d&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-yellow-100 border border-yellow-400 rounded relative",children:(0,a.jsx)("div",{className:"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-yellow-500 rounded-full"})}),(0,a.jsx)("span",{children:"Ditandai untuk review"})]})]}),d&&i&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsx)(o.$,{variant:"outline",size:"sm",className:"w-full text-xs",onClick:()=>i(r),children:t.has(r)?"Hapus Tanda":"Tandai Soal"})}),c&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsx)(o.$,{onClick:c,disabled:!x||g,className:"w-full bg-blue-600 hover:bg-blue-700 text-white",size:"lg",children:g?"Menyerahkan...":"Submit Ujian"})})]})]})}}};
//# sourceMappingURL=9678.js.map