{"version": 3, "file": "3168.js", "mappings": "kdAgBM,MAAY,cAAiB,aAbC,CAaY,CAAU,MAbZ,EAAE,EAAG,gBAAkB,KAAK,CAAS,QAAC,CAAC,qCCa/E,MAAc,cAAiB,eAbD,CAagB,CAAU,MAbhB,EAAE,EAAG,cAAgB,KAAK,CAAS,QAAC,CAAC,qCCFnF,oBACA,gCACA,mDCDA,cACA,MAAc,QAAY,QAAG,aAAwB,EACrD,OAAS,SAAa,MACtB,sBACA,mCACA,mBAEA,oBACG,IACH,+ZCkBM,EAAY,CAAC,IAAK,QAAS,UAAW,WAAW,EACjD,EAAiB,CAAC,IAAK,OAAO,EAM9B,EAAc,SAGd,CAAC,EAAY,EAAe,EAAqB,CAAI,OAAgB,CAGzE,GAGI,CAAC,EAAqB,EAAiB,CAAI,EAHpC,CAGoC,IAAkB,CAAC,EAAa,CAC/E,EACA,CAF2C,CAE3C,EAAiB,CAClB,EACK,EAAiB,QAAiB,CAAC,EAoBnC,CAAC,EAAgB,EAAgB,CAAI,EAAwC,GAQ7E,CAAC,EAA6B,EAA6B,CAC/D,EAAqD,GAoDjD,EAAgC,IACpC,EArDgE,CAqD1D,YAtDyD,GAuD7D,WACA,EACA,KAAM,cACN,eACA,EACA,MAAO,EACP,6BACA,MACA,OACA,eACA,WACA,WACA,OACA,EACF,CAAI,EACE,EAAc,EAAe,GAC7B,CAAC,EAAS,EAAU,CAAU,IADY,GACtB,GAAU,CAAsC,IAAI,EACxE,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,CAAoC,IAAI,EAC1E,CAAC,EAAsB,EAAuB,CAAU,YAAS,GACjE,EADsE,CAC1D,EADkC,EAClC,GAAY,CAAC,GACzB,CAAC,EAAM,EAAO,CAAI,IAAJ,CAAI,EAAoB,CAAC,CAC3C,KAAM,EACN,YAAa,GAAe,GAC5B,SAAU,EACV,OAAQ,CACV,CAAC,EACK,CAAC,EAAO,EAAQ,CAAI,OAAoB,CAAC,CAC7C,KAAM,EACN,YAAa,EACb,SAAU,EACV,OAAQ,CACV,CAAC,EACK,EAAiC,SAAwC,IAAI,EAG7E,GAAgB,GAAU,GAAQ,CAAC,CAAC,EAAQ,QAAQ,MAAM,EAC1D,CAAC,CAD6D,CAC3C,EAAmB,CAAU,WAAS,IAAI,CAAvB,GAAyC,CAAC,EAO9D,MAAM,KAAK,GAChC,IAAI,GAAY,EAAO,IADyB,CACzB,CAAM,KAAK,EAClC,KAAK,GAAG,EAEX,MACE,UAAiB,KAAhB,CAAsB,GAAG,EACxB,oBAAC,YACC,EACA,MAAO,EACP,UACA,gBAAiB,YACjB,EACA,kBAAmB,uBACnB,EACA,6BAA8B,EAC9B,UAAW,OAAK,CAAC,QACjB,EACA,cAAe,OACf,EACA,aAAc,EACd,IAAK,2BACL,WACA,EAEA,oBAAC,EAAW,SAAX,CAAoB,MAAO,EAC1B,mBAAC,GACC,MAAO,EAAM,cACb,kBAAyB,cAAY,IACnC,EAAoB,GAAU,IAAI,IAAI,GAAM,CAAF,EAAE,CAAI,GAClD,EAAG,CADqD,CAAC,EAEzD,qBAA4B,cAAY,IACtC,EAAoB,IAClB,IAAM,EAAa,IAAI,IAAI,GAE3B,CAF+B,MAC/B,EAAW,OAAO,GACX,CACT,CAAC,CACH,EAAG,CAAC,CAAC,WAEJ,GACH,CACF,EAEC,EACC,WAAC,GAAD,CAEE,cAAW,YACX,EACA,SAAU,QACV,eACA,QACA,EAEA,SAAU,GAAW,EAAS,EAAM,OAAO,KAAK,WAChD,OACA,EAEC,UAAU,SAAY,YAAC,UAAO,MAAM,GAAG,EAAK,KAC5C,MAAM,KAAK,GAAgB,EAbvB,GAeL,OACN,CACF,CAEJ,EAEA,EAAO,YAAc,EAMrB,IAAM,EAAe,gBAMf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,GAAM,eAAE,WAAe,EAAW,GAAO,GAAG,EAAa,CAAI,EACvD,EAAc,EAAe,GAC7B,CAFmD,CAEzC,EAAiB,EAAc,GACzC,CAF0C,CAE7B,EAAQ,MADiC,EACjC,EAAY,EACjC,EAAe,OAAe,CAAC,EAAc,EAAQ,eAAe,EACpE,EAAW,EAAc,GACzB,EAAuB,QADe,CAC2B,OAAO,EAExE,CAAC,EAAW,EAAuB,EAAc,CAAI,GAAmB,IAC5E,IAAM,EAAe,IAAW,KAAF,CAAE,CAAO,GAAU,CAAC,EAAK,QAAQ,EACzD,EAAc,EAAa,KAAK,GAAU,EAAK,QAAU,EAAQ,KAAK,EACtE,EAAW,GAAa,EAAc,EAAQ,EACnC,SAAW,CAAxB,GACF,EAAQ,cAAc,EAAS,KAAK,CAExC,CAAC,EAEK,EAAa,IACZ,IACH,EAAQ,MADO,MACP,CAAa,IAAI,KAKvB,GACF,GAAQ,IAJO,IAGC,gBACR,CAAyB,QAAU,CACzC,EAAG,KAAK,MAAM,EAAa,KAAK,EAChC,EAAG,KAAK,MAAM,EAAa,KAAK,EAClC,CAEJ,EAEA,MACE,UAAiB,KAAhB,CAAuB,SAAO,EAAE,GAAG,EAClC,mBAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,WACL,gBAAe,EAAQ,UACvB,gBAAe,EAAQ,KACvB,gBAAe,EAAQ,SACvB,oBAAkB,OAClB,IAAK,EAAQ,IACb,aAAY,EAAQ,KAAO,OAAS,SACpC,SAAU,EACV,gBAAe,EAAa,GAAK,OACjC,mBAAkB,GAAsB,EAAQ,KAAK,EAAI,GAAK,OAC7D,GAAG,EACJ,IAAK,EAEL,QAAS,OAAoB,CAAC,EAAa,QAAS,IAMlD,EAAM,cAAc,MAAM,EAGK,SAAS,CAApC,EAAe,SACjB,EAAW,EAEf,CAAC,EAFmB,cAGL,OAAoB,CAAC,EAAa,cAAe,IAC9D,EAAe,QAAU,EAAM,YAI/B,IAAM,EAAS,EAAM,OACjB,EAAO,kBAAkB,EAAM,SAAS,GAAG,EACtC,sBAAsB,EAAM,SAAS,EAMzB,IAAjB,EAAM,QAAgB,CAAkB,MAAZ,SAA2C,SAAS,CAA/B,EAAM,cACzD,EAAW,GAEX,EAAM,eAAe,EAEzB,CAAC,EACD,UAAW,OAAoB,CAAC,EAAa,UAAW,IACtD,IAAM,EAAsC,KAAtB,EAAU,OAE5B,CADkB,EAAM,SAAW,EAAM,QAAU,EAAM,SAClB,EAAG,EAAxB,EAAM,IAAI,QAAc,EAAsB,EAAM,GAAG,GACzE,IAA+B,MAAd,EAAM,KAAa,CACpC,EAAU,SAAS,EAAM,GAAG,GAAG,CACjC,IACA,EAAM,KADK,SACL,CAAe,EAEzB,CAAC,GACH,CACF,CAEJ,GAGF,EAAc,YAAc,EAM5B,IAAM,EAAa,cAQb,EAAoB,aACxB,CAAC,EAAsC,KAErC,GAAM,eAAE,YAAe,QAAW,WAAO,EAAU,cAAc,GAAI,GAAG,EAAW,CAAI,EACjF,EAAU,EAAiB,EAAY,GACvC,UADoD,oBAClD,EAA6B,CAAI,EACnC,EAAc,KAAa,MAC3B,EAAe,OAAe,CAAC,EAAc,EAAQ,iBAAiB,EAM5E,MAJA,OAAe,CAAC,KACd,EAA6B,EAC/B,EAAG,CAAC,EAA8B,EAAY,EADJ,CAIxC,MAH2C,CAG3C,EAAC,IAAS,CAAC,KAAV,CACE,GAAG,EACJ,IAAK,EAGL,MAAO,CAAE,cAAe,MAAO,EAE9B,YAAsB,EAAQ,KAAK,EAAI,sBAAG,WAAY,EAAM,GAGnE,GAGF,EAAY,YAAc,EAW1B,IAAM,EAAmB,aACvB,CAAC,EAAqC,KACpC,GAAM,eAAE,WAAe,EAAU,GAAG,EAAU,CAAI,EAClD,KAD8C,CAE5C,UAAC,IAAS,CAAC,KAAV,CAAe,eAAW,EAAE,GAAG,EAAW,IAAK,EAC7C,YAAY,IACf,CAEJ,EAGF,GAAW,YAhBO,EAgBO,WAiBzB,IAAM,EAA4C,GACzC,UAAC,GAAe,CAAf,CAAgB,QAAO,GAAE,GAAG,EAAO,EAG7C,EAAa,YAfO,EAeO,aAM3B,IAAM,EAAe,gBAKf,EAAsB,aAC1B,CAAC,EAAwC,KACvC,IAAM,EAAU,EAAiB,EAAc,EAAM,aAAa,EAC5D,CAAC,EAAU,EAAW,CAAU,QAAV,EAAU,CAA2B,QAOjE,CAJA,OAAe,CAAC,KACd,EAAY,IAAI,iBAClB,CADoC,CACjC,CAAC,CAAC,EAEA,EAAQ,MAAM,CAcZ,SAAC,GAAmB,GAAG,EAAO,IAAK,EAAc,EAZ/C,EACM,eACP,UAAC,GAAsB,MAAO,EAAM,cAClC,mBAAC,EAAW,KAAX,CAAgB,MAAO,EAAM,cAC5B,mBAAC,OAAK,WAAM,SAAS,EACvB,EACF,EAPO,CAQP,EAEF,IAIR,GAGF,EAAc,YAAc,EA2B5B,GAAM,CAAC,EAAuB,EAAuB,CACnD,EAA+C,GAgC3C,EAAO,QAAU,CAAC,IAjC6B,wBAiCD,EAE9C,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,eACJ,WACA,EAAW,gCACX,kBACA,uBACA,OAGA,aACA,QACA,cACA,eACA,EACA,oBACA,0BACA,mBACA,kBACA,EAEA,GAAG,EACL,CAAI,EACE,EAAU,EAAiB,EAAc,GACzC,CAAC,EAAS,EAAU,CAAU,IADwB,GAClC,GAAU,CAA0C,IAAI,EAC5E,CAAC,EAAU,EAAW,CAAU,QAAV,EAAU,CAAuC,IAAI,EAC3E,EAAe,OAAe,CAAC,EAAc,GAAU,EAAW,IAAI,CAAC,EACxD,EAAe,CAAU,WAAmC,CAA7C,GAAiD,EAC/E,CAAC,EAAkB,EAAmB,CAAU,WACpD,KAD0C,CAGtC,EAAW,EAAc,GACzB,CAAC,EAAc,EAAe,CAAU,IADF,MACE,EAAS,GACjD,EADsD,EACvB,OAAO,IAGtC,CAH2C,CAG3C,UAAU,KACd,GAAI,EAAS,OAAO,OAAU,CAAC,EACjC,EAAG,CAAC,EADoC,EAKxC,GAJW,EAIX,GAAc,CAAC,EAEf,IAAM,EAAmB,cACvB,IACE,GAAM,CAAC,EAAW,GAAG,EAAS,CAAI,IAAW,EAAf,CAAe,CAAI,CAAN,EAAgB,EAAK,IAAI,OAAO,EACrE,CAAC,EAAQ,CAAI,EAAU,GAAd,EAAc,CAAM,EAAE,EAE/B,EAA6B,SAAS,cAC5C,QAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,GAAW,eAAe,CAAE,IADkB,EACX,SAAU,CAAC,EAE1C,IAAc,GAAa,GAAU,GAAS,GAAT,MAAS,EAAY,EAC1D,IAAc,GAAY,GAAU,GAAS,GAAT,MAAS,CAAY,EAAS,cACtE,GAAW,MAAM,EACb,SAAS,gBAAkB,GANe,MAQlD,EACA,CAAC,EAAU,EAAQ,EAGf,EAA0B,OANiC,MAMjC,CAC9B,IAAM,EAAW,CAAC,EAAc,EAAQ,EACxC,CAAC,EADsC,EACZ,EAAO,EAK9B,GAL8B,QAK9B,CAAU,KACV,GACF,GAEJ,EAAG,CAAC,EAAc,EAAkB,CAHhB,CAOpB,GAAM,GANgB,OAEa,IAI3B,2BAAc,EAAyB,CAAI,EAC7C,YAAU,KACd,GAAI,EAAS,CACX,IAAI,EAAmB,CAAE,EAAG,EAAG,EAAG,CAAE,EAE9B,EAAoB,IACxB,EAAmB,CACjB,EAAG,KAAK,IAAI,KAAK,MAAM,EAAM,KAAK,GAAK,EAAyB,SAAS,KAAK,EAAE,EAC7E,KAAK,IAAI,KAAK,MAAM,EAAM,KAAK,GAAK,EAAyB,SAAS,KAAK,CAChF,CADkF,EAG9E,EAAkB,IAElB,EAAiB,GAAK,IAAM,EAAiB,GAAK,GACpD,CADwD,CAClD,eAAe,EAGjB,EAAS,SAAS,EAAM,MAAqB,GAC/C,GAAa,GAGjB,EAHsB,OAGb,oBAAoB,cAAe,GAC5C,EAAyB,QAAU,IAD0B,EAS/D,OALyC,MAAM,CAA3C,EAAyB,UAC3B,SAAS,iBAAiB,cAAe,GACzC,SAAS,KADiD,WACjD,CAAiB,YAAa,EAAiB,CAAE,SAAS,EAAM,MAAM,CAAK,CAAC,GAGhF,KACL,SAAS,oBAAoB,cAAe,GAC5C,SAAS,KADoD,cACpD,CAAoB,YAAa,EAAiB,CAAE,QAAS,EAAK,CAAC,CAC9E,CACF,CACF,EAAG,CAAC,EAAS,EAAc,EAAyB,EAE9C,YAAU,KACd,GAHiD,CAG3C,EAAQ,IAAM,GAAa,GAGjC,EAHsC,KACtC,OAAO,iBAAiB,OAAQ,GAChC,EADqC,KAC9B,iBAAiB,SAAU,GAC3B,EADgC,GAErC,OAAO,oBAAoB,OAAQ,GACnC,EADwC,KACjC,oBAAoB,SAAU,EACvC,CACF,EAF8C,CAE1C,EAAa,EAEjB,GAAM,CAAC,EAAW,EAAqB,CAAI,GAAmB,IAC5D,IAAM,EAAe,IAAW,CADK,IACP,CAAE,CAAO,GAAU,CAAC,EAAK,QAAQ,EACzD,EAAc,EAAa,KAAK,GAAU,EAAK,IAAI,UAAY,SAAS,aAAa,EACrF,EAAW,GAAa,EAAc,EAAQ,GAChD,GAKF,KAN6D,EACjD,IAKD,IAAO,EAAS,IAAI,QAAwB,MAAM,CAAC,CAElE,CAAC,EAEK,EAAwB,cAC5B,CAAC,EAAgC,EAAe,KAC9C,IAAM,EAAmB,CAAC,EAAuB,SAAW,CAAC,EAEzD,CADqC,SAAlB,EAAQ,OAAuB,EAAQ,QAAU,GAClD,IAAkB,CACtC,EAAgB,GACZ,CADgB,EACE,GAAuB,SAAU,EAAjC,CAE1B,EACA,CAAC,EAAQ,KAAK,GAEV,GAAwB,cAAY,IAAM,GAAS,MAAM,EAAG,CAAC,EAAQ,EACrE,GAA4B,cAChC,CAAC,EAAoC,EAAe,KAClD,IAAM,EAAmB,CAAC,EAAuB,SAAW,CAAC,CAEzD,EADqC,SAAlB,EAAQ,OAAuB,EAAQ,QAAU,GAClD,IAAkB,EAClB,EAExB,EAF4B,CAG3B,EAAQ,KAAK,GAGV,GAA8B,WAAb,EAAwB,GAAuB,EAGhE,GACJ,KAAmB,GACf,MACE,aACA,QACA,cACA,eACA,EACA,qCACA,SACA,mBACA,EACA,iBACF,EACA,CAAC,EAEP,MACE,UAAC,GACC,MAAO,UACP,WACA,EACA,iBAAkB,kBAClB,eACA,EACA,YAAa,uBACb,qBACA,mBACA,WACA,eACA,YACA,EAEA,mBAAC,GAAY,CAAZ,CAAa,GAAI,EAAM,gBAAc,EACpC,mBAAC,GAAU,CAAV,CACC,SAAO,EAGP,QAAS,EAAQ,KACjB,iBAAkB,IAEhB,EAAM,eAAe,CACvB,EACA,mBAAoB,OAAoB,CAAC,EAAkB,IACzD,EAAQ,SAAS,MAAM,CAAE,cAAe,EAAK,CAAC,EAC9C,EAAM,eAAe,CACvB,CAAC,EAED,mBAAC,IAAgB,CAAhB,CACC,SAAO,EACP,6BAA2B,kBAC3B,uBACA,EAGA,eAAgB,GAAW,EAAM,eAAe,EAChD,UAAW,IAAM,EAAQ,cAAa,GAEtC,EAF2C,OAE3C,UAAC,IACC,KAAK,UACL,GAAI,EAAQ,UACZ,aAAY,EAAQ,KAAO,OAAS,SACpC,IAAK,EAAQ,IACb,cAAgB,GAAU,EAAM,eAAe,EAC9C,GAAG,EACH,GAAG,GACJ,SAAU,IAAM,GAAgB,GAChC,CADoC,GAC/B,EACL,MAAO,CAEL,QAAS,OACT,cAAe,SAEf,QAAS,OACT,GAAG,EAAa,OAElB,UAAW,OAAoB,CAAC,EAAa,UAAW,IACtD,IAAM,EAAgB,EAAM,SAAW,EAAM,QAAU,EAAM,QAO7D,GAJI,MAAqB,IAAf,KAAe,EAAM,eAAe,EAE1C,GAAuC,EAAG,EAAxB,EAAM,IAAI,QAAc,EAAsB,EAAM,GAAG,EAEzE,CAAC,UAAW,YAAa,OAAQ,KAAK,EAAE,SAAS,EAAM,GAAG,EAAG,CAE/D,IAAI,EADU,IAAW,KAAF,CAAE,CAAO,GAAU,CAAC,EAAK,QAAQ,EAC7B,IAAI,GAAU,EAAK,IAAI,OAAQ,EAK1D,GAHI,CAAC,UAAW,KAAK,EAAE,SAAS,EAAM,GAAG,GAAG,CAC1C,EAAiB,EAAe,MAAM,EAAE,SAAQ,EAE9C,CAAC,UAAW,WAAW,EAAE,SAAS,EAAM,GAAG,EAAG,CAChD,IAAM,EAAiB,EAAM,OACvB,EAAe,EAAe,QAAQ,GAC5C,EAAiB,EAAe,MAAM,CADoB,CACL,CAAC,CACxD,CAMA,WAAW,IAAM,EAAW,IAE5B,EAAM,QAFoC,CAAC,KAErC,CAAe,CACvB,CACF,CAAC,GACH,EACF,EACF,CACF,GAGN,GAGF,EAAkB,YAvTQ,EAuTM,kBAWhC,IAAM,EAAkC,aAGtC,CAAC,EAAoD,KACrD,GAAM,eAAE,EAAe,WAAU,GAAG,EAAY,CAAI,EAC9C,EAAU,EAAiB,EAAc,CADC,EAE1C,EAAiB,EAAwB,EAAc,GACvD,CAFsD,EAErC,EAAiB,CAAU,IADwB,MACxB,CAAgC,GAA1C,CAA8C,EAChF,CAAC,EAAS,EAAU,CAAU,OAAV,GAAU,CAAkD,IAAI,EACpF,EAAe,OAAe,CAAC,EAAe,GAAS,EAAW,IAAI,CAAC,CAC5D,EAAc,GACzB,EAAgC,QADM,EACC,GACvC,EAD4C,EAChB,OAAO,IAAI,UAErC,eAAU,EAAc,qCAAkB,EAAkB,CAAI,EAClE,EAAiB,cAAY,KACjC,GACE,EAAQ,SACR,EAAQ,WACR,GACA,GACA,GACA,GACA,EACA,CACA,IAAM,EAAc,EAAQ,QAAQ,sBAAsB,EAKpD,EAAc,EAAQ,sBAAsB,EAC5C,EAAgB,EAAQ,UAAU,sBAAsB,EACxD,EAAe,EAAiB,sBAAsB,EAE5D,GAAoB,QAAhB,EAAQ,IAAe,CACzB,IAAM,EAAiB,EAAa,KAAO,EAAY,KACjD,EAAO,EAAc,KAAO,EAC5B,EAAY,EAAY,KAAO,EAC/B,EAAkB,EAAY,MAAQ,EACtC,EAAe,KAAK,IAAI,EAAiB,EAAY,KAAK,EAC1D,EAAY,OAAO,aAAa,CAChC,EAAc,OAAK,CAAC,EAAM,IAO9B,KAAK,IAAI,GAAgB,EAAY,GACtC,EAED,EAAe,KAHoC,CAG9B,SAAW,EAAkB,KAClD,EAAe,MAAM,KAAO,EAAc,IAC5C,KAAO,CACL,IAAM,EAAiB,EAAY,MAAQ,EAAa,MAClD,EAAQ,OAAO,WAAa,EAAc,MAAQ,EAClD,EAAa,OAAO,WAAa,EAAY,MAAQ,EACrD,EAAkB,EAAY,MAAQ,EACtC,EAAe,KAAK,IAAI,EAAiB,EAAY,KAAK,EAC1D,EAAW,OAAO,aAAa,CAC/B,EAAe,OAAK,CAAC,EAAO,IAEhC,KAAK,IAAI,GAAgB,EAAW,GACrC,EAED,EAAe,KAHmC,CAG7B,SAAW,EAAkB,KAClD,EAAe,MAAM,MAAQ,EAAe,IAC9C,CAKA,IAAM,EAAQ,IACR,EAAkB,GADD,IACQ,YAAc,GACvC,EAAc,EAAS,UADiC,EACjC,CAEvB,EAAgB,OAAO,iBAAiB,GACxC,EAAwB,EADuB,OACd,EAAc,eAAgB,EAAE,EACjE,EAAoB,SAAS,EAAc,WAAY,EAAE,EACzD,EAA2B,SAAS,EAAc,kBAAmB,EAAE,EAEvE,EAAoB,EAAwB,EAAoB,EADzC,SAAS,EAAc,CACgC,YADhC,CAAe,EAAE,EACsC,EACrG,EAAmB,KAAK,IAAI,IAAa,aAAkB,GAE3D,EAAiB,OAAO,KAFoD,WAEpD,CAAiB,GACzC,EAAqB,GAD4B,MACnB,EAAe,WAAY,EAAE,EAC3D,EAAwB,SAAS,EAAe,cAAe,EAAE,EAEjE,EAAyB,EAAY,IAAM,EAAY,OAAS,IAAI,CAGpE,EAAyB,EAAa,aAAe,EAErD,EAAyB,EAAwB,GAD9B,EAAa,UAAY,GAMlD,EAL2E,CAGvC,CAEhC,EAF0D,EAE7B,CAC/B,IAAM,EACJ,EAAM,OAAS,GAAK,IAAiB,EAAM,EAAM,OAAS,CAAC,EAAG,IAAI,QACpE,EAAe,MAAM,OAAS,MAG9B,IAAM,EAAmC,KAAK,IAC5C,EAhBgD,EAiBhD,GAEG,EAAa,MALhB,CAME,CANM,aAAe,EAAS,UAAY,EAAS,cAOnD,EAGJ,GAAe,MAAM,OAAS,EADU,EACD,IACzC,KAAO,CACL,IAAM,EAAc,EAAM,OAAS,GAAK,IAAiB,EAAM,CAAC,EAAG,IAAI,QACvE,EAAe,MAAM,IAAM,MAC3B,IAAM,EAAgC,KAAK,IACzC,EACA,EACE,EAAS,WAER,EAAc,IAAqB,CACpC,GAGJ,EAAe,MAAM,OADN,GA/BiB,EAAoB,GAgCb,KACvC,EAAS,UAAY,EAAyB,EAAyB,EAAS,CAFjC,QAGjD,CAEA,EAAe,MAAM,OAAS,GAAG,MACjC,EAAe,MADgC,SAC1B,CAAY,EAAmB,KACpD,EAAe,MAAM,UAAY,EAAkB,KAGnD,MAIA,KAJW,iBAIW,IAAO,EAAwB,QAAU,GACjE,CADsE,EAErE,CACD,EACA,EAAQ,QACR,EAAQ,UACR,EACA,EACA,EACA,EACA,EACA,EAAQ,IACR,EACD,EAED,OAAe,CAAC,IAAM,IAAY,CAAC,EAAS,EAG5C,GAAM,CAHqC,EAGrB,EAAgB,CAAU,WAAiB,EAA3B,CACtC,MAAe,CAAC,KACV,GAAS,EAAiB,GAAjB,IAAwB,iBAAiB,GAAS,IAAF,EAAQ,CACvE,EAAG,CAAC,EAAQ,EAMZ,GANW,CAML,EAAiC,cACrC,IACM,IAAwC,IAAhC,EAAoB,UAC9B,IACA,KADS,CAET,EAAoB,SAAU,EAElC,CAHwB,CAIxB,CAAC,EAAU,EAAiB,EAG9B,MACE,OAJ4B,CAI5B,EAAC,IACC,MAAO,iBACP,0BACA,EACA,qBAAsB,EAEtB,mBAAC,OACC,IAAK,EACL,MAAO,CACL,QAAS,OACT,cAAe,SACf,SAAU,QACV,OAAQ,CACV,EAEA,mBAAC,IAAS,CAAC,IAAV,CACE,GAAG,EACJ,IAAK,EACL,MAAO,CAGL,UAAW,aAEX,UAAW,OACX,GAAG,EAAY,MACjB,EACF,EACF,EAGN,CAAC,EAED,EAA0B,YAvNS,EAuNK,0BAYxC,IAAM,GAA6B,aAGjC,CAAC,EAA+C,KAChD,GAAM,eACJ,QACA,EAAQ,QACR,mBAAmB,GACnB,GAAG,EACL,CAAI,EACE,EAAc,EAAe,GAEnC,MACE,IAH8C,CAG9C,KAAiB,KAAhB,CACE,GAAG,EACH,GAAG,EACJ,IAAK,EACL,yBACA,EACA,MAAO,CAEL,UAAW,aACX,GAAG,EAAY,MAGb,0CAA2C,uCAC3C,yCAA0C,sCAC1C,0CAA2C,uCAC3C,+BAAgC,mCAChC,gCAAiC,mCAErC,GAGN,CAAC,EAED,GAAqB,YA1CQ,EA0CM,qBAYnC,GAAM,CAAC,GAAwB,GAAwB,CACrD,EAAgD,EAAc,CAAC,CAAC,EAE5D,GAAgB,SAHiC,QAWjD,GAAuB,aAC3B,CAAC,EAAyC,KACxC,GAAM,eAAE,QAAe,EAAO,GAAG,EAAc,CAAI,EAC7C,EAAiB,EAAwB,GAAe,EADf,CAEzC,EAAkB,GAAyB,GAAe,EADW,CAErE,EAAe,OAAe,CADyC,EAC1B,EAAe,gBAAgB,EAC5E,EAAyB,SAAO,CAAC,EACvC,MACE,uBAEE,oBAAC,SACC,wBAAyB,CACvB,OAAQ,2KACV,QACA,IAEF,UAAC,EAAW,KAAX,CAAgB,MAAO,EACtB,mBAAC,IAAS,CAAC,IAAV,CACC,6BAA2B,GAC3B,KAAK,eACJ,GAAG,EACJ,IAAK,EACL,MAAO,CAIL,SAAU,WACV,KAAM,EAKN,SAAU,cACV,GAAG,EAAc,KACnB,EACA,SAAU,OAAoB,CAAC,EAAc,SAAU,IACrD,IAAM,EAAW,EAAM,cACjB,gBAAE,0BAAgB,EAAwB,CAAI,EACpD,GAAI,GAAyB,SAAW,EAAgB,CACtD,IAAM,EAAa,KAAK,IAAI,EAAiB,QAAU,EAAS,SAAS,EACzE,GAAI,EAAa,EAAG,CAClB,IAAM,EAAkB,OAAO,YAAc,GAGvC,EAAa,KAAK,IAFH,GADyC,QAC9B,EAAe,CAEL,IAFK,CAAM,IAEF,KAFW,EAC5C,WAAW,EAAe,MAAM,MAAM,GAGxD,GAAI,EAAa,EAAiB,CAChC,IAAM,EAAa,EAAa,EAC1B,EAAoB,KAAK,IAAI,EAAiB,GAC9C,EAAa,EAAa,CAEhC,EAH8D,CAG/C,MAAM,OAAS,EAAoB,KACd,OAAO,CAAvC,EAAe,MAAM,SACvB,EAAS,UAAY,EAAa,EAAI,EAAa,EAEnD,EAAe,MAAM,eAAiB,WAE1C,CACF,CACF,CACA,EAAiB,QAAU,EAAS,UACrC,GACH,CACF,GACF,CAEJ,GAGF,GAAe,YAAc,GAM7B,IAAM,GAAa,cAIb,CAAC,GAA4B,GAAqB,CACtD,EAA6C,GAiB/C,CAZ0B,MAL+B,KADD,CAM9B,CACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAU,IADqB,CACrB,EAAK,CAAC,EACtB,MACE,UAAC,IAA2B,MAAO,EAAe,GAAI,EACpD,mBAAC,IAAS,CAAC,IAAV,CAAc,KAAK,QAAQ,kBAAiB,EAAU,GAAG,EAAY,IAAK,EAAc,EAC3F,CAEJ,GAGU,YAAc,GAM1B,IAAM,GAAa,aAanB,CAR0B,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAe,GAAsB,CADN,EACkB,GACvD,MAAO,IAD6D,CAC7D,KAAC,IAAS,CAAC,IAAV,CAAc,GAAI,EAAa,GAAK,GAAG,EAAY,IAAK,EAAc,CAChF,GAGU,YAAc,GAM1B,IAAM,GAAY,aAUZ,CAAC,GAA2B,GAAoB,CACpD,EAA4C,IASxC,GAAmB,OAV6B,KAU7B,CACvB,CAAC,EAAqC,KACpC,GAAM,eACJ,QACA,WACA,GAAW,EACX,UAAW,EACX,GAAG,EACL,CAAI,EACE,EAAU,EAAiB,GAAW,GACtC,EAAiB,EAAwB,GAAW,GADD,EAEtC,EAAQ,MAD4C,EAClC,EAC/B,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,CAAS,GAAiB,EAAE,EAC9D,CAAC,EAAW,EAAY,CAAU,SAAV,CAAU,EAAS,GAC3C,EADgD,CACjC,MAAe,CAAC,EAAc,GACjD,EAAe,kBAAkB,EAAM,EAAO,IAE1C,EAAS,EAFyC,CAEzC,IAAK,CAAC,EACf,EAAuB,SAA0C,OAAO,EAExE,EAAe,KACd,IACH,EAAQ,IADK,SACL,CAAc,GACtB,EAAQ,cAAa,GAEzB,EAF8B,GAIhB,IAAI,CAAd,EACF,MAAM,MACJ,yLAIJ,MACE,UAAC,IACC,MAAO,QACP,WACA,SACA,aACA,EACA,iBAAwB,cAAY,IAClC,EAAa,GAAmB,GAAkB,IAAM,aAAe,IAAI,KAAK,CAAC,CACnF,EAAG,CAAC,CAAC,EAEL,mBAAC,EAAW,SAAX,CACC,MAAO,QACP,EACA,qBACA,EAEA,mBAAC,IAAS,CAAC,IAAV,CACC,KAAK,SACL,kBAAiB,EACjB,mBAAkB,EAAY,GAAK,OAEnC,gBAAe,GAAc,EAC7B,aAAY,EAAa,UAAY,YACrC,gBAAe,GAAY,OAC3B,gBAAe,EAAW,GAAK,OAC/B,SAAU,EAAW,OAAY,GAChC,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAU,QAAS,IAAM,EAAa,IAAI,CACxE,OAAQ,OAAoB,CAAC,EAAU,OAAQ,IAAM,GAAa,IAClE,CADuE,CAAC,MAC/D,OAAoB,CAAC,EAAU,QAAS,KAE3C,QAAoC,IAArB,SAAqB,GAC1C,CAAC,EACD,OAFuD,KAE1C,OAAoB,CAAC,EAAU,YAAa,KAGnD,QAAoC,IAArB,SAAqB,GAC1C,CAAC,EACD,OAFuD,OAExC,OAAoB,CAAC,EAAU,cAAe,IAC3D,EAAe,QAAU,EAAM,WACjC,CAAC,EACD,cAAe,OAAoB,CAAC,EAAU,cAAe,IAE3D,EAAe,QAAU,EAAM,YAC3B,EACF,EAAe,MADH,KACG,GAAc,EACO,SAAS,CAApC,EAAe,SAGxB,EAAM,cAAc,MAAM,CAAE,eAAe,CAAK,CAAC,CAErD,CAAC,EACD,eAAgB,OAAoB,CAAC,EAAU,eAAgB,IACzD,EAAM,gBAAkB,SAAS,eAAe,EACnC,cAAc,CAEjC,CAAC,EACD,UAAW,OAAoB,CAAC,EAAU,UAAW,KAE/C,EADiC,WAAW,UAAY,IACvC,QAAM,KAAa,EACpC,EAAe,SAAS,EAAM,GAAG,EAAG,KAEtB,IAAK,EAAnB,EAAM,CAF2C,EAE3C,EAAa,EAAM,eAAe,EAC9C,CAAC,GACH,EACF,EAGN,GAGF,GAAW,YAAc,GAMzB,IAAM,GAAiB,iBAKjB,GAAuB,aAC3B,CAAC,EAAyC,KAExC,GAAM,eAAE,YAAe,QAAW,EAAO,GAAG,EAAc,CAAI,EACxD,EAAU,EAAiB,GAAgB,EADS,CAEpD,EAAiB,EAAwB,GAAgB,GADD,EAE1C,GAAqB,GAAgB,EADmB,CAEtE,EAAuB,EAA8B,GAAgB,GADL,CAE/D,EAAc,EAAe,CAAU,IAD0C,MAC1C,CAAuC,CAAjD,GAAqD,EACnF,EAAe,OAAe,CAClC,EACA,GAAU,EAAgB,GAC1B,CAD8B,CAClB,iBACZ,GAAU,EAAe,sBAAsB,EAAM,EAAY,MAAO,EAAY,QAAQ,GAGxF,EAAc,GAAc,YAC5B,EAAqB,UACzB,IACE,UAAC,UAA+B,MAAO,EAAY,MAAO,SAAU,EAAY,SAC7E,YADU,EAAY,KAEzB,EAEF,CAAC,EAAY,SAAU,EAAY,MAAO,EAAW,EAGjD,OAHiD,YAG/C,uBAAmB,EAAqB,CAAI,EAMpD,MALA,CAME,EANF,IAAe,CAAC,KACd,EAAkB,GACX,IAAM,EAAqB,GADJ,CAE7B,CAAC,EAAmB,EAAsB,EAAa,CADV,CAI9C,QAHuD,CAGvD,cACE,oBAAC,IAAS,CAAC,KAAV,CAAe,GAAI,EAAY,OAAS,GAAG,EAAe,IAAK,EAAc,EAG7E,EAAY,YAAc,EAAQ,WAAa,CAAC,EAAQ,qBAC5C,eAAa,EAAc,SAAU,EAAQ,SAAS,EAC/D,MACN,CAEJ,GAGF,GAAe,YAAc,GAM7B,IAAM,GAAsB,sBAKtB,GAA4B,aAChC,CAAC,EAA8C,KAC7C,GAAM,eAAE,EAAe,GAAG,EAAmB,CAAI,EAEjD,OADoB,GAAqB,GAAqB,CADjB,EAE1B,UADwD,CAEzE,UAAC,IAAS,CAAC,KAAV,CAAe,eAAW,EAAE,GAAG,EAAoB,IAAK,EAAc,EACrE,IACN,GAGF,GAAoB,YAAc,GAMlC,IAAM,GAAwB,uBAKxB,GAA6B,aAGjC,CAAC,EAA+C,KAChD,IAAM,EAAiB,EAAwB,GAAuB,EAAM,aAAa,EACnF,EAAkB,GAAyB,GAAuB,EAAM,aAAa,EACrF,CAAC,EAAa,EAAc,CAAU,WAAV,CAAmB,GAC/C,EAAe,OAAe,CAAC,EAAc,EAAgB,oBAAoB,EAevF,MAbA,OAAe,CAAC,KACd,GAAI,EAAe,UAAY,EAAe,aAAc,CAE1D,IAASA,EAAT,WAAwB,EACF,EAAS,UAAY,CAC1BC,CACjB,EAJM,EAAW,EAAe,KAGJ,GAHI,CAOhC,OAFAD,IACA,EAAS,QADI,QACJ,CAAiB,SAAUA,GAC7B,IAAM,EAAS,IAD0B,eAC1B,CAAoB,SAAUA,EACtD,CACF,EAAG,CAAC,EAAe,KAFiD,GAEjD,CAAU,EAAe,YAAY,CAAC,EAElD,EACL,UAAC,EAAD,CAAC,CACE,GAAG,EACJ,IAAK,EACL,aAAc,KACZ,GAAM,UAAE,EAAU,eAAa,CAAI,CAC/B,IAAY,IACd,EAAS,QADmB,CACnB,CAAY,EAAS,UAAY,EAAa,aAE3D,IAEA,IACN,CAAC,EAED,GAAqB,YAAc,GAMnC,IAAM,GAA0B,yBAK1B,GAA+B,aAGnC,CAAC,EAAiD,KAClD,IAAM,EAAiB,EAAwB,GAAyB,EAAM,aAAa,EACrF,EAAkB,GAAyB,GAAyB,EAAM,aAAa,EACvF,CAAC,EAAe,EAAgB,CAAU,YAAS,CAAnB,EAChC,EAAe,OAAe,CAAC,EAAc,EAAgB,oBAAoB,EAkBvF,MAhBA,OAAe,CAAC,KACd,GAAI,EAAe,UAAY,EAAe,aAAc,CAE1D,IAASA,EAAT,WAAwB,IAChB,EAAY,EAAS,aAAe,EAAS,aAInD,EADsB,KAAK,KAAK,EAAS,GACxBE,MADiC,EAAI,EAExD,EAPM,EAM0B,EANA,SAUhC,OAFAF,IACA,EAAS,QADI,QACJ,CAAiB,SAAUA,GAC7B,IAAM,EAAS,IAD0B,eAC1B,CAAoB,SAAUA,EACtD,CACF,EAAG,CAAC,EAAe,KAFiD,GAEjD,CAAU,EAAe,YAAY,CAAC,EAElD,EACL,UAAC,IAAD,GACM,EACJ,IAAK,EACL,aAAc,KACZ,GAAM,UAAE,eAAU,EAAa,CAAI,EAC/B,GAAY,IACd,EAAS,QADmB,CACnB,CAAY,EAAS,UAAY,EAAa,aAE3D,IAEA,IACN,CAAC,EAED,GAAuB,YAAc,GAOrC,IAAM,GAA+B,aAGnC,CAAC,EAAiD,KAClD,GAAM,eAAE,eAAe,EAAc,GAAG,EAAqB,CAAI,EAC3D,EAAiB,EAAwB,YADc,SACQ,GAC/D,EAA2B,SAAsB,IAAI,EACrD,EAAW,EAAc,GAEzB,EAA6B,QAFS,KAET,CAAY,KACV,MAAM,CAArC,EAAmB,UACrB,OAAO,cAAc,EAAmB,OAAO,EAC/C,EAAmB,QAAU,KAEjC,EAAG,CAAC,CAAC,EAeL,OAbM,YAAU,IACP,IAAM,IACZ,CAAC,EAAqB,EAMzB,OAAe,CAAC,IAPoB,CAQlC,GAPsB,CAOhB,EAAa,IAAW,KAAK,GAAU,EAAK,IAAI,UAAY,SAAS,aAAa,EACxF,GAAY,IAAI,SAAS,eAAe,CAAE,MAAO,SAAU,CAAC,CAC9D,EAAG,CAAC,EAAS,EAGX,IAHU,CAGV,KAAC,IAAS,CAAC,IAAV,CACC,eAAW,EACV,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,WAAY,EAAG,GAAG,EAAqB,OAChD,cAAe,OAAoB,CAAC,EAAqB,cAAe,KACnC,MAAM,CAArC,EAAmB,UACrB,EAAmB,QAAU,OAAO,YAAY,EAAc,GAAE,CAEpE,CAAC,EACD,cAAe,OAAoB,CAAC,EAAqB,cAAe,KACtE,EAAe,cAAc,EACM,MAAM,CAArC,EAAmB,UACrB,EAAmB,QAAU,OAAO,YAAY,EAAc,GAAE,CAEpE,CAAC,EACD,eAAgB,OAAoB,CAAC,EAAqB,eAAgB,KACxE,GACF,CAAC,GAGP,CAAC,CAkBD,CAP8B,WAfD,CAeC,CAC5B,CAAC,EAA0C,KACzC,GAAM,eAAE,EAAe,GAAG,EAAe,CAAI,EAC7C,MAAO,IADkC,CAClC,KAAC,IAAS,CAAC,IAAV,CAAc,eAAW,EAAE,GAAG,EAAgB,IAAK,EAAc,CAC3E,GAGc,YAZO,EAYO,gBAM9B,IAAM,GAAa,aAkBnB,CAZ0B,aACxB,CAAC,EAAsC,KACrC,GAAM,eAAE,EAAe,GAAG,EAAW,CAAI,EACnC,EAAc,EAAe,EADE,CAE/B,EAAU,EAAiB,GAAY,GADG,EAEzB,EAAwB,GAAY,GADD,OAEnD,EAAQ,CADyD,GACzD,EAAoC,WACjD,EADoC,SACpC,UAAiB,KAAhB,CAAuB,GAAG,EAAc,GAAG,EAAY,IAAK,EAAc,EACzE,IACN,GAGU,YAAc,GAW1B,IAAM,GAA0B,aAC9B,CAAC,eAAE,QAAe,EAAO,GAAG,EAAM,CAAwC,GAAxC,EAChC,IAAM,EAAY,SAA0B,IAAI,EAC1C,EAAe,OAAe,CAAC,EAAc,GAAG,EACpC,OAAW,CAAC,GAgC9B,EAhCmC,KAG7B,YAAU,KACd,IAAM,EAAS,EAAI,QACnB,GAAI,CAAC,EAAQ,OAOb,IAAM,EAJa,OAAO,yBADN,OAAO,kBAAkB,UAG3C,SAE0B,IAC5B,GAAI,IAAc,GAAS,EAAU,CACnC,IAAM,EAAQ,IAAI,MAAM,SAAU,CAAE,SAAS,CAAK,CAAC,EACnD,EAAS,KAAK,EAAQ,GACtB,EAD2B,aACpB,CAAc,EACvB,CACF,EAF8B,CAE1B,EAAW,EAAM,EAenB,CAfkB,EAelB,OAAC,IAAS,CAAC,OAAV,CACE,GAAG,EACJ,MAAO,CAAE,GAAG,KAAwB,GAAG,EAAM,OAC7C,IAAK,EACL,aAAc,GAGpB,GAOF,SAAS,GAAsB,GAAgB,MAC5B,KAAV,GAA0B,SAAV,CACzB,CAEA,SAAS,GAAmB,GAA0C,IAC9D,EAAqB,OAAc,CAAC,GACpC,EAAkB,SADgC,EACvB,EAC3B,EAAiB,SAAO,CAAC,EAEzB,EAA8B,cAClC,IACE,IAAM,EAAS,EAAU,QAAU,EACnC,EAAmB,GAElB,GAFwB,MAEf,EAAa,GACrB,EAAU,QAAU,EACpB,OAAO,aAAa,EAAS,OAAO,EAEhC,GAAc,QAAS,QAAU,OAAO,WAAW,IAAM,EAAa,EAAE,EAAG,IAAI,EACrF,CAAG,EACL,EACA,CAAC,CAFU,CAEQ,EAGf,EAAuB,YAHR,CAGQ,CAAY,KACvC,EAAU,QAAU,GACpB,OAAO,aAAa,EAAS,OAAO,CACtC,EAAG,CAAC,CAAC,EAML,OAJM,YAAU,IACP,IAAM,OAAO,aAAa,EAAS,OAAO,EAChD,CAAC,CAAC,EAEE,CAAC,EAAW,EAAuB,EAAc,CAoB1D,SAAS,EApBiD,CAqBxD,EACA,EACA,GACA,QAEA,IAAM,EADa,EAAO,OAAS,GAAK,MAAM,KAAK,GAAQ,GAAF,EAAE,CAAM,GAAU,IAAS,EAAO,CAAC,CAAC,EACvD,EAAO,CAAC,EAAK,EAC7C,EAAmB,EAAc,EAAM,QAAQ,GAAe,GAChE,GAagB,EAbS,EAaG,EAbI,KAAK,CAAtB,EAAsB,CAAI,CAaO,CAbW,CAAC,CAAC,CAc1D,EAAM,IAAO,CAAC,EAAG,IAAU,KAAoB,GAAS,EAAM,MAAM,CAAE,EAZzE,CADmD,IAA5B,EAAiB,QACpB,GAAe,CAAf,CAA4B,OAAO,GAAO,IAAM,EAAW,EACnF,IAAM,EAAW,EAAa,KAAK,GACjC,EAAK,UAAU,YAAY,EAAE,WAAW,EAAiB,YAAY,CAAC,GAExE,OAAO,IAAa,EAAc,EAAW,MAC/C,CAxEA,GAAkB,YApDQ,EAoDM,kBAkFhC,IAAMG,GAAO,EACP,GAAU,EACV,GAAQ,EACR,GAAO,EACP,GAAS,EACTC,GAAU,EACV,GAAW,GAGX,GAAO,GACP,GAAW,GACX,GAAgB,GAChB,GAAiB,GACjB,GAAmB", "sources": ["webpack://terang-lms-ui/../../../src/icons/chevron-up.ts", "webpack://terang-lms-ui/../../../src/icons/chevron-down.ts", "webpack://terang-lms-ui/./node_modules/@radix-ui/number/dist/index.mjs", "webpack://terang-lms-ui/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "webpack://terang-lms-ui/../src/select.tsx"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('ChevronUp', __iconNode);\n\nexport default ChevronUp;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', __iconNode);\n\nexport default ChevronDown;\n", "// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\nexport {\n  clamp\n};\n//# sourceMappingURL=index.mjs.map\n", "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({ value, previous: value });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport {\n  usePrevious\n};\n//# sourceMappingURL=index.mjs.map\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n"], "names": ["handleScroll", "canScrollUp", "canScrollDown", "Root", "Content"], "sourceRoot": ""}