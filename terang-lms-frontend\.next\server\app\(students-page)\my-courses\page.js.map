{"version": 3, "file": "../app/(students-page)/my-courses/page.js", "mappings": "ubAAA,oLCKA,IAAMA,EAAWC,EAAAA,UAAgB,CAAiH,CAAC,WACjJC,CAAS,OACTC,CAAK,CACL,GAAGC,EACJ,CAAEC,IAAQ,UAACC,EAAAA,EAAsB,EAACD,IAAKA,EAAKH,UAAWK,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEL,GAAa,GAAGE,CAAK,UAC7I,UAACE,EAAAA,EAA2B,EAACJ,UAAU,iDAAiDM,MAAO,CAC/FC,UAAW,CAAC,YAAY,EAAE,KAAON,CAAAA,GAAS,EAAG,EAAE,CAAC,OAGpDH,EAASU,WAAW,CAAGJ,EAAAA,EAAsB,CAACI,WAAW,wBCdzD,oDCAA,qGCAA,kECAA,2GCAA,qDCAA,+CCAA,mDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,sDCAA,qVCiBA,OACA,UACA,GACA,CACA,UACA,kBACA,CACA,UACA,aACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAA4K,CAoBhM,2IAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAlCA,IAAsB,uCAAkK,CAkCxL,iIACA,WAlCA,IAAsB,4CAAgF,CAkCtG,+CACA,cAlCA,IAAsB,4CAAmF,CAkCzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QArDA,IAAsB,sCAAiJ,CAqDvK,gHACA,gBArDA,IAAsB,uCAAuJ,CAqD7K,sHACA,aArDA,IAAsB,uCAAoJ,CAqD1K,mHACA,WArDA,IAAsB,4CAAgF,CAqDtG,+CACA,cArDA,IAAsB,4CAAmF,CAqDzG,kDACA,UACA,sBAAoC,wCAAyS,4BAA2C,wCAAmS,aAC3pB,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,8IAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,wCACA,uBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,mBChGD,uCAA4K,yBCA5K,sDCAA,wDCAA,+DCmBI,sBAAsB,4sBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAAE,OAAhC,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CAER,CAEA,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,6BAA6B,CAC7C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CA7BLC,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,EAOF,KA7DiD,EA+D/C,EAA2B,CAlBN,IASL,cCvEtB,GDgF8B,KChF9B,+BAA4K,yBCA5K,sECAA,oDCAA,kECAA,yDCAA,uDCAA,8LCUM,EAAgB,WAIhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GAIlE,CAAC,EAAkB,EAAkB,CACzC,EAA4C,EALuC,CAe/E,EAAiB,QAXoB,IAWpB,CACrB,CAAC,EAAmC,SAwGV,IAvGxB,GAAM,IAuGqC,aAtGzC,EACA,MAAO,EAAY,KACnB,IAAK,gBACL,EAAgB,EAChB,GAAG,EACL,CAAI,CAEJ,EAAK,GAAW,MAAY,EAAM,CAAC,EAAiB,IAClD,GADyD,GAAG,EACpD,MAAM,GAAmB,GAAG,EAAO,EA+FxC,CA/F4C,EAAJ,QAAc,CAAC,uBA+FpB,SAAS,oBAAoB,aAAa,8DAAoF,GA5FtK,IAAM,EAAM,EAAiB,GAAW,EAhCxB,EAgCoB,CAElB,GA0FoJ,EA5FpH,GAExB,GAAC,EAAmB,EAAW,GAAG,CAC1D,EAD6D,MACrD,MAAM,CA4FU,EA5FW,GAAG,EAAS,EA6F5C,CA7FgD,CA4FR,GA5FI,MAAc,CAAC,KA4FI,oBAC1B,SAAS,oBAAoB,aAAa;;;;;wBAE7B,GA5FvD,IAAM,EAAQ,EAAmB,EAAW,GAAG,EAAgB,KACzD,EAAa,EAAS,GAAS,EAAJ,EAAyB,GAAG,KAAI,EAEjE,MACE,UAAC,GAAiB,MAAO,QAAiB,MAAc,EACtD,mBAAC,IAAS,CAAC,IAAV,CACC,gBAAe,EACf,gBAAe,EACf,gBAAe,EAAS,GAAS,EAAQ,OACzC,iBAAgB,EAChB,KAAK,cACL,aAAY,EAAiB,EAAO,GAAG,aAC3B,GAAS,OACrB,WAAU,EACT,GAAG,EACJ,IAAK,GACP,CACF,CAEJ,GAGF,EAAS,YAAc,EAMvB,IAAM,EAAiB,oBAKjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,EAAiB,GAAG,EAAe,CAAI,EACzC,EAAU,EAAmB,EAAgB,GACnD,CAF2C,KAGzC,MAFgE,EAEhE,EAAC,IAAS,CAAC,IAAV,CACC,aAAY,EAAiB,EAAQ,MAAO,EAAQ,GAAG,EACvD,aAAY,EAAQ,OAAS,OAC7B,WAAU,EAAQ,IACjB,GAAG,EACJ,IAAK,GAGX,GAOF,SAAS,EAAqB,EAAe,GAAa,MACjD,GAAG,KAAK,MAAO,EAAQ,EAAO,GAAG,CAAC,IAG3C,SAAS,EAAiB,EAAkC,GAAiC,OAC3E,MAAT,EAAgB,gBAAkB,IAAU,EAAW,WAAa,SAC7E,CAEA,SAAS,EAAS,GAA6B,MACrB,UAAjB,OAAO,CAChB,CAEA,SAAS,EAAiB,GAAyB,OAG/C,EAAS,GAAG,CACZ,CAAC,MAAM,GAAG,CACV,EAAM,CAEV,CAEA,SAAS,EAAmB,EAAY,GAA8B,OAGlE,EAAS,IACT,CADc,MACP,IACP,CADY,EACH,GACT,GAAS,CAEb,CAjCA,EAAkB,YAAc,EAiDhC,IAAM,EAAO,EACP,EAAY,0BCpJlB,qDCAA,4DCAA,wDCAA,iECAA,uDCAA,sDCAA,iDCAA,0DCAA,4DCAA,gDCAA,0DCAA,mFCM+C,MAAQ,cAAC,2BAA2B,sDAAsD,WAAW,kDAAkD,IAAyB,uBCN/N,+JCM+C,MAAQ,cAAC,wBAAwB,sMAAsM,WAAW,+GAA+G,WAAW,wGAAwG,WAAW,6CAA6C,WAAW,+CAA+C,IAAyB,+EC6K9oB,MAxK8B,KAC5B,CAuKaC,EAvKP,SAuKkBA,EAAC,CAtKvBC,CAAU,YACVC,CAAU,CACVC,iBAAe,CAChB,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,GAGXC,EAAUF,EAAgBG,MAAM,CAAG,EAAIH,EAAkBF,EAAa,CAACC,EAAW,CAAG,EAAE,QAC7F,GAAsC,GAAG,CAAtBG,EAAQC,MAAM,CAoC1B,UAACC,MAAAA,CAAIjB,UAAU,8BAA8BkB,wBAAsB,cAAcC,0BAAwB,oBAC5G,WAACF,MAAAA,CAAIjB,UAAU,6CAEb,UAACoB,EAAAA,CAAWA,CAAAA,CAACC,sBAAoB,cAAcF,0BAAwB,aAGvE,WAACF,MAAAA,CAAIjB,UAAU,wCACb,UAACsB,CAAUA,CAAAA,CAACtB,UAAU,oCAAoCqB,sBAAoB,aAAaF,0BAAwB,aACnH,WAACF,MAAAA,WACC,UAACM,KAAAA,CAAGvB,UAAU,4CAAmC,gBAGjD,UAACwB,IAAAA,CAAExB,UAAU,yBAAgB,4CAKjC,WAACiB,MAAAA,CAAIjB,UAAU,iEACZe,EAAQU,GAAG,CAACC,IACb,IAAMC,EAAoBD,EAAOE,OAAO,CAACC,MAAM,CAAC,CAACC,EAAOC,IAAWD,EAAQC,EAAOC,QAAQ,CAACC,MAAM,CAACC,GAAMA,EAAGC,QAAQ,CAACC,KAAK,CAACC,GAAKA,EAAEC,WAAW,GAAKJ,EAAGK,IAAI,CAACC,QAAQ,EAAExB,MAAM,CAAE,GACrKyB,EAAgBf,EAAOE,OAAO,CAACC,MAAM,CAAC,CAACC,EAAOC,IAAWD,EAAQC,EAAOC,QAAQ,CAAChB,MAAM,CAAE,GACzF0B,EAAkBD,EAAgB,EAAId,EAAoBc,EAAgB,IAAM,EAChFE,EAAmBjB,EAAOE,OAAO,CAACK,MAAM,CAACW,GAAKA,EAAEZ,QAAQ,CAACI,KAAK,CAACF,GAAMA,EAAGC,QAAQ,CAACC,KAAK,CAACC,GAAKA,EAAEC,WAAW,GAAKJ,EAAGK,IAAI,CAACC,QAAQ,GAAKI,EAAEC,UAAU,CAACL,QAAQ,EAAExB,MAAM,CACtK,MAAO,WAAC8B,EAAAA,EAAIA,CAAAA,CAAiB9C,UAAU,oDACjC,UAAC+C,EAAAA,EAAUA,CAAAA,CAAC/C,UAAU,oBACpB,WAACiB,MAAAA,CAAIjB,UAAU,sBACb,WAACiB,MAAAA,CAAIjB,UAAU,6CACb,UAACgD,EAAAA,EAASA,CAAAA,CAAChD,UAAU,+DAClB0B,EAAOuB,IAAI,GAEd,UAACC,EAAAA,CAAKA,CAAAA,CAACC,QAA2B,cAAlBzB,EAAO0B,MAAM,CAAmB,UAAY,qBACzD1B,gBAAO0B,MAAM,CAAmB,UAAY,sBAGjD,UAAC5B,IAAAA,CAAExB,UAAU,8CACV0B,EAAO2B,WAAW,GAErB,WAACpC,MAAAA,CAAIjB,UAAU,uDACb,WAACiB,MAAAA,CAAIjB,UAAU,oCACb,UAACsD,EAAAA,CAAQA,CAAAA,CAACtD,UAAU,YACpB,UAACuD,OAAAA,UAAM7B,EAAO8B,UAAU,MAE1B,WAACvC,MAAAA,CAAIjB,UAAU,oCACb,UAACyD,EAAAA,CAAWA,CAAAA,CAACzD,UAAU,YACvB,WAACuD,OAAAA,WAAM7B,EAAOE,OAAO,CAACZ,MAAM,CAAC,uBAKrC,UAAC0C,EAAAA,EAAWA,CAAAA,CAAC1D,UAAU,gBACrB,WAACiB,MAAAA,CAAIjB,UAAU,sBAEb,WAACiB,MAAAA,WACC,WAACA,MAAAA,CAAIjB,UAAU,mDACb,UAACuD,OAAAA,CAAKvD,UAAU,+BAAsB,aACtC,WAACuD,OAAAA,CAAKvD,UAAU,kCACb2D,KAAKC,KAAK,CAAClB,GAAiB,UAGjC,UAAC5C,EAAAA,CAAQA,CAAAA,CAACG,MAAOyC,EAAiB1C,UAAU,WAI9C,WAACiB,MAAAA,CAAIjB,UAAU,+CACb,WAACiB,MAAAA,WACC,UAACA,MAAAA,CAAIjB,UAAU,2CACZ2C,IAEH,UAAC1B,MAAAA,CAAIjB,UAAU,iCAAwB,aAEzC,WAACiB,MAAAA,WACC,UAACA,MAAAA,CAAIjB,UAAU,4CACZ2B,IAEH,UAACV,MAAAA,CAAIjB,UAAU,iCAAwB,cAK3C,UAAC6D,IAAIA,CAACC,KAAM,CAAC,IAARD,QAAoB,EAAEnC,EAAOqC,EAAE,EAAE,CAAE/D,UAAU,iBAChD,WAACgE,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,MAAMnD,UAAU,SAASiE,KAAK,eAC5C,UAACC,EAAAA,CAAQA,CAAAA,CAAClE,UAAU,iBAAiB,gCA1DjC0B,EAAOqC,EAAE,CAiE7B,GAGG,oCAAa,CAACtC,GAAG,CAAC,CAAC0C,EAAGC,IAAU,WAACtB,EAAAA,EAAIA,CAAAA,CAA8B9C,UAAU,qCAC1E,UAAC+C,EAAAA,EAAUA,CAAAA,CAAC/C,UAAU,kCACpB,WAACiB,MAAAA,CAAIjB,UAAU,sBACb,WAACiB,MAAAA,CAAIjB,UAAU,6CACb,WAACgD,EAAAA,EAASA,CAAAA,CAAChD,UAAU,kCAAwB,UACnCoE,EAAQ,KAElB,UAAClB,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,mBAAU,mBAE3B,UAAC3B,IAAAA,CAAExB,UAAU,iCAAwB,8CAKzC,UAAC0D,EAAAA,EAAWA,CAAAA,CAAC1D,UAAU,gBACrB,WAACiB,MAAAA,CAAIjB,UAAU,sBACb,UAACiB,MAAAA,CAAIjB,UAAU,2CACf,WAACiB,MAAAA,CAAIjB,UAAU,mCACb,UAACiB,MAAAA,CAAIjB,UAAU,0CACf,UAACiB,MAAAA,CAAIjB,UAAU,6CAEjB,WAACgE,EAAAA,CAAMA,CAAAA,CAAChE,UAAU,SAASiE,KAAK,KAAKI,QAAQ,cAC3C,UAAC/C,CAAUA,CAAAA,CAACtB,UAAU,iBAAiB,0BAtBL,CAAC,YAAY,EAAEoE,EAAAA,CAAO,WA9HjE,UAACnD,MAAAA,CAAIjB,UAAU,uCAClB,WAACiB,MAAAA,CAAIjB,UAAU,6CAEb,UAACoB,EAAAA,CAAWA,CAAAA,CAAAA,GAGZ,WAACH,MAAAA,CAAIjB,UAAU,wCACb,UAACsB,CAAUA,CAAAA,CAACtB,UAAU,sCACtB,WAACiB,MAAAA,WACC,UAACM,KAAAA,CAAGvB,UAAU,4CAAmC,gBAGjD,UAACwB,IAAAA,CAAExB,UAAU,yBAAgB,4CAKjC,WAACiB,MAAAA,CAAIjB,UAAU,8BACb,UAACsB,CAAUA,CAAAA,CAACtB,UAAU,yCACtB,UAACsE,KAAAA,CAAGtE,UAAU,oDAA2C,8BAGzD,UAACwB,IAAAA,CAAExB,UAAU,8BAAqB,8CAGlC,UAAC6D,IAAIA,CAACC,KAAK,KAAND,eACH,WAACG,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,gBACd,UAAC7B,CAAUA,CAAAA,CAACtB,UAAU,iBAAiB,8BAkIvD", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/./src/components/ui/progress.tsx", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/?92d3", "webpack://terang-lms-ui/?2578", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?9908", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/../src/progress.tsx", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/clock_01_icon.js", "webpack://terang-lms-ui/external commonjs2 \"events\"", "webpack://terang-lms-ui/./node_modules/hugeicons-react/dist/esm/icons/book_02_icon.js", "webpack://terang-lms-ui/./src/app/(students-page)/my-courses/page.tsx"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\nimport { cn } from \"@/lib/utils\";\nconst Progress = React.forwardRef<React.ElementRef<typeof ProgressPrimitive.Root>, React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>>(({\n  className,\n  value,\n  ...props\n}, ref) => <ProgressPrimitive.Root ref={ref} className={cn(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className)} {...props}>\r\n    <ProgressPrimitive.Indicator className=\"h-full w-full flex-1 bg-primary transition-all\" style={{\n    transform: `translateX(-${100 - (value || 0)}%)`\n  }} />\r\n  </ProgressPrimitive.Root>);\nProgress.displayName = ProgressPrimitive.Root.displayName;\nexport { Progress };", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\layout.tsx\");\nconst module6 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module7 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page8 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\my-courses\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(students-page)',\n        {\n        children: [\n        'my-courses',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\my-courses\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\layout.tsx\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props)),(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=icon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\icon.png?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\my-courses\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/(students-page)/my-courses/page\",\n        pathname: \"/my-courses\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\my-courses\\\\page.tsx\");\n", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/(students-page)/my-courses',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/(students-page)/my-courses',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/(students-page)/my-courses',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/(students-page)/my-courses',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Ke<PERSON><PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\(students-page)\\\\my-courses\\\\page.tsx\");\n", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Clock01Icon\",[[\"circle\",{cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M12 8V12L14 14\",stroke:\"currentColor\",key:\"k1\"}]]);export{o as default};\n", "module.exports = require(\"events\");", "/**\n * @license hugeicons-react v0.2.0\n *\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport r from\"../create-hugeicon-component.js\";const o=r(\"Book02Icon\",[[\"path\",{d:\"M20.5 16.9286V10C20.5 6.22876 20.5 4.34315 19.3284 3.17157C18.1569 2 16.2712 2 12.5 2H11.5C7.72876 2 5.84315 2 4.67157 3.17157C3.5 4.34315 3.5 6.22876 3.5 10V19.5\",stroke:\"currentColor\",key:\"k0\"}],[\"path\",{d:\"M20.5 17H6C4.61929 17 3.5 18.1193 3.5 19.5C3.5 20.8807 4.61929 22 6 22H20.5\",stroke:\"currentColor\",key:\"k1\"}],[\"path\",{d:\"M20.5 22C19.1193 22 18 20.8807 18 19.5C18 18.1193 19.1193 17 20.5 17\",stroke:\"currentColor\",key:\"k2\"}],[\"path\",{d:\"M15 7L9 7\",stroke:\"currentColor\",key:\"k3\"}],[\"path\",{d:\"M12 11L9 11\",stroke:\"currentColor\",key:\"k4\"}]]);export{o as default};\n", "'use client';\n\nimport React from 'react';\nimport { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport { Book02Icon, UserIcon, Clock01Icon, PlayIcon } from 'hugeicons-react';\nimport Link from 'next/link';\nimport { useEnrollment } from '@/contexts/enrollment-context';\nimport { Breadcrumbs } from '@/components/breadcrumbs';\nconst ModulesPage: React.FC = () => {\n  const {\n    isEnrolled,\n    courseData,\n    enrolledCourses\n  } = useEnrollment();\n\n  // Use enrolled courses if available, fallback to empty array\n  const courses = enrolledCourses.length > 0 ? enrolledCourses : isEnrolled ? [courseData] : [];\n  if (!isEnrolled && courses.length === 0) {\n    return <div className='min-h-screen bg-gray-50 p-8'>\r\n        <div className='mx-auto max-w-6xl space-y-6 pb-8'>\r\n          {/* Breadcrumbs - Top Level */}\r\n          <Breadcrumbs />\r\n          \r\n          {/* Header Section */}\r\n          <div className='flex items-center space-x-3'>\r\n            <Book02Icon className='h-8 w-8 text-[var(--iai-primary)]' />\r\n            <div>\r\n              <h1 className='text-3xl font-bold text-gray-900'>\r\n                Kursus Saya\r\n              </h1>\r\n              <p className='text-gray-600'>Lanjutkan perjalanan belajar Anda</p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Empty State */}\r\n          <div className='py-16 text-center'>\r\n            <Book02Icon className='mx-auto mb-6 h-16 w-16 text-gray-400' />\r\n            <h3 className='mb-4 text-xl font-semibold text-gray-900'>\r\n              Tidak ada kursus tersedia\r\n            </h3>\r\n            <p className='mb-8 text-gray-600'>\r\n              Anda belum terdaftar dalam kursus apapun.\r\n            </p>\r\n            <Link href=\"/courses\">\r\n              <Button variant=\"iai\">\r\n                <Book02Icon className='mr-2 h-4 w-4' />\r\n                Jelajahi Kursus\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>;\n  }\n  return <div className='min-h-screen bg-gray-50 p-8' data-sentry-component=\"ModulesPage\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='mx-auto max-w-6xl space-y-6 pb-8'>\r\n        {/* Breadcrumbs - Top Level */}\r\n        <Breadcrumbs data-sentry-element=\"Breadcrumbs\" data-sentry-source-file=\"page.tsx\" />\r\n        \r\n        {/* Header Section */}\r\n        <div className='flex items-center space-x-3'>\r\n          <Book02Icon className='h-8 w-8 text-[var(--iai-primary)]' data-sentry-element=\"Book02Icon\" data-sentry-source-file=\"page.tsx\" />\r\n          <div>\r\n            <h1 className='text-3xl font-bold text-gray-900'>\r\n              Kursus Saya\r\n            </h1>\r\n            <p className='text-gray-600'>Lanjutkan perjalanan belajar Anda</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Course Cards Grid */}\r\n        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>\r\n          {courses.map(course => {\n          const completedChapters = course.modules.reduce((total, module) => total + module.chapters.filter(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed).length, 0);\n          const totalChapters = course.modules.reduce((total, module) => total + module.chapters.length, 0);\n          const overallProgress = totalChapters > 0 ? completedChapters / totalChapters * 100 : 0;\n          const completedModules = course.modules.filter(m => m.chapters.every(ch => ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed) && m.moduleQuiz.isPassed).length;\n          return <Card key={course.id} className='group transition-shadow hover:shadow-lg'>\r\n                <CardHeader className='border-b'>\r\n                  <div className='space-y-3'>\r\n                    <div className='flex items-start justify-between'>\r\n                      <CardTitle className='text-lg transition-colors group-hover:text-blue-600'>\r\n                        {course.name}\r\n                      </CardTitle>\r\n                      <Badge variant={course.status === 'completed' ? 'default' : 'secondary'}>\r\n                        {course.status === 'completed' ? 'Selesai' : 'Sedang Belajar'}\r\n                      </Badge>\r\n                    </div>\r\n                    <p className='line-clamp-2 text-sm text-gray-600'>\r\n                      {course.description}\r\n                    </p>\r\n                    <div className='flex flex-wrap gap-4 text-xs text-gray-500'>\r\n                      <div className='flex items-center gap-1'>\r\n                        <UserIcon className='h-3 w-3' />\r\n                        <span>{course.instructor}</span>\r\n                      </div>\r\n                      <div className='flex items-center gap-1'>\r\n                        <Clock01Icon className='h-3 w-3' />\r\n                        <span>{course.modules.length} Modul</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardHeader>\r\n                <CardContent className='pt-4'>\r\n                  <div className='space-y-4'>\r\n                    {/* Progress */}\r\n                    <div>\r\n                      <div className='mb-2 flex items-center justify-between'>\r\n                        <span className='text-sm font-medium'>Kemajuan</span>\r\n                        <span className='text-sm text-gray-500'>\r\n                          {Math.round(overallProgress)}%\r\n                        </span>\r\n                      </div>\r\n                      <Progress value={overallProgress} className='h-2' />\r\n                    </div>\r\n\r\n                    {/* Quick Stats */}\r\n                    <div className='grid grid-cols-2 gap-3 text-center'>\r\n                      <div>\r\n                        <div className='text-lg font-bold text-blue-600'>\r\n                          {completedModules}\r\n                        </div>\r\n                        <div className='text-xs text-gray-500'>Modul</div>\r\n                      </div>\r\n                      <div>\r\n                        <div className='text-lg font-bold text-green-600'>\r\n                          {completedChapters}\r\n                        </div>\r\n                        <div className='text-xs text-gray-500'>Bab</div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Action Button */}\r\n                    <Link href={`/my-courses/${course.id}`} className='block'>\r\n                      <Button variant=\"iai\" className='w-full' size='sm'>\r\n                        <PlayIcon className='mr-2 h-4 w-4' />\r\n                        Lanjutkan Belajar\r\n                      </Button>\r\n                    </Link>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>;\n        })}\r\n\r\n          {/* Add more course placeholder cards */}\r\n          {[...Array(5)].map((_, index) => <Card key={`placeholder-${index}`} className='border-dashed opacity-50'>\r\n              <CardHeader className='border-b border-dashed'>\r\n                <div className='space-y-3'>\r\n                  <div className='flex items-start justify-between'>\r\n                    <CardTitle className='text-lg text-gray-400'>\r\n                      Course {index + 2}\r\n                    </CardTitle>\r\n                    <Badge variant='outline'>Coming Soon</Badge>\r\n                  </div>\r\n                  <p className='text-sm text-gray-400'>\r\n                    More courses will be available soon.\r\n                  </p>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className='pt-4'>\r\n                <div className='space-y-4'>\r\n                  <div className='h-12 animate-pulse rounded bg-gray-100'></div>\r\n                  <div className='grid grid-cols-2 gap-3'>\r\n                    <div className='h-8 animate-pulse rounded bg-gray-100'></div>\r\n                    <div className='h-8 animate-pulse rounded bg-gray-100'></div>\r\n                  </div>\r\n                  <Button className='w-full' size='sm' disabled>\r\n                    <Book02Icon className='mr-2 h-4 w-4' />\r\n                    Not Available\r\n                  </Button>\r\n                </div>\r\n              </CardContent>\r\n            </Card>)}\r\n        </div>\r\n      </div>\r\n    </div>;\n};\nexport default ModulesPage;"], "names": ["Progress", "React", "className", "value", "props", "ref", "ProgressPrimitive", "cn", "style", "transform", "displayName", "serverComponentModule.default", "ModulesPage", "isEnrolled", "courseData", "enrolledCourses", "useEnrollment", "courses", "length", "div", "data-sentry-component", "data-sentry-source-file", "Breadcrumbs", "data-sentry-element", "Book02Icon", "h1", "p", "map", "course", "completedChapters", "modules", "reduce", "total", "module", "chapters", "filter", "ch", "contents", "every", "c", "isCompleted", "quiz", "isPassed", "totalChapters", "overallProgress", "completedModules", "m", "moduleQuiz", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "name", "Badge", "variant", "status", "description", "UserIcon", "span", "instructor", "Clock01Icon", "<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "Link", "href", "id", "<PERSON><PERSON>", "size", "PlayIcon", "_", "index", "disabled", "h3"], "sourceRoot": ""}