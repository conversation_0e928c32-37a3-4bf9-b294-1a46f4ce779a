try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="fdbdb667-eda4-471d-a2e9-5c087be45a3a",e._sentryDebugIdIdentifier="sentry-dbid-fdbdb667-eda4-471d-a2e9-5c087be45a3a")}catch(e){}(()=>{var e={};e.id=5466,e.ids=[5466],e.modules={3690:(e,r,t)=>{"use strict";e.exports=t(44870)},6953:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>T,serverHooks:()=>E,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>y,GET:()=>f,HEAD:()=>g,OPTIONS:()=>w,PATCH:()=>q,POST:()=>x,PUT:()=>h});var i=t(3690),o=t(56947),n=t(75250),u=t(63033),a=t(7688);async function p(e){try{let{htmlContent:r}=await e.json();if(!r)return new Response(JSON.stringify({error:"HTML content is required"}),{status:400,headers:{"Content-Type":"application/json"}});let t=process.env.PUPPETEER_SERVICE_URL||"http://localhost:3000",s=await fetch(`${t}/pdf`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({html:r,format:"A4",landscape:!0})});if(!s.ok){let e=await s.json();return new Response(JSON.stringify({error:"Failed to generate PDF",details:e}),{status:s.status,headers:{"Content-Type":"application/json"}})}let i=await s.arrayBuffer();return new Response(i,{status:200,headers:{"Content-Type":"application/pdf","Content-Disposition":'attachment; filename="certificate.pdf"'}})}catch(e){return console.error("Error generating PDF:",e),new Response(JSON.stringify({error:"Internal server error",details:e.message}),{status:500,headers:{"Content-Type":"application/json"}})}}let c={...u},d="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;function l(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let i;try{let e=d?.getStore();i=e?.headers}catch{}return a.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/certificates",headers:i}).apply(t,s)}})}let f=l(void 0,"GET"),x=l(p,"POST"),h=l(void 0,"PUT"),q=l(void 0,"PATCH"),y=l(void 0,"DELETE"),g=l(void 0,"HEAD"),w=l(void 0,"OPTIONS"),T=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/certificates/route",pathname:"/api/certificates",filename:"route",bundlePath:"app/api/certificates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\certificates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:m,serverHooks:E}=T;function P(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:m})}},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44725:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=44725,e.exports=r},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688],()=>t(6953));module.exports=s})();
//# sourceMappingURL=route.js.map