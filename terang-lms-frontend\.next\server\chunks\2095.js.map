{"version": 3, "file": "2095.js", "mappings": "siBAEA,SAASA,EAAK,WACZC,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,OAAOH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASC,EAAW,WAClBP,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASE,EAAU,CACjBR,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,aAAaH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASG,EAAgB,WACvBT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,mBAAmBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,YACjL,CAOA,SAASI,EAAY,WACnBV,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASK,EAAW,WAClBX,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACC,MAAAA,CAAIC,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGC,CAAK,CAAEI,wBAAsB,aAAaC,0BAAwB,YACjL,oCCxCO,IAAMM,EAA6B,CACxCC,GAAI,gBACJC,KAAM,sCACNC,KAAM,eACNC,YACE,0LACFC,WAAY,sCACZC,UAAW,aACXC,QAAS,aACTC,eAAgB,OAChBC,eAAgB,gBAChBC,gBAAiB,GACjBC,cAAe,EACfC,OAAQ,cACRC,QAAS,CACP,CACEZ,GAAI,OACJa,MAAO,kCACPV,YAAa,2DACbW,MAAO,EACPC,WAAY,GACZC,qBAAsB,EACtBC,SAAU,CACR,CACEjB,GAAI,MACJa,MAAO,4BACPC,MAAO,EACPC,YAAY,EACZC,qBAAsB,EACtBE,SAAU,CACR,CACElB,GAAI,WACJa,MAAO,kCACPM,KAAM,OACNC,QAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kGAwD0E,CAAC,CACrFC,aAAa,EACbC,UAAW,YACb,EACA,CACEtB,GAAI,WACJa,MAAO,oCACPM,KAAM,QACNC,QAAS,6CACTG,SAAU,GACVF,YAAa,GACbC,UAAW,YACb,EACA,CACEtB,GAAI,WACJa,MAAO,gCACPM,KAAM,MACNC,QAAS,+CACTC,aAAa,EACbC,UAAW,YACb,EACD,CACDE,KAAM,CACJxB,GAAI,WACJa,MAAO,iCACPM,KAAM,UACNM,UAAW,CACT,CACEzB,GAAI,KACJ0B,SACE,wEACFP,KAAM,kBACNQ,QAAS,CAAC,QAAS,QAAS,aAAc,YAAY,CACtDC,cAAe,EACfC,YACE,0EACJ,EACD,CACDC,aAAc,GACdC,UAAW,GACXC,SAAU,EACVC,YAAa,EACbC,UAAU,CACZ,CACF,EACA,CACElC,GAAI,MACJa,MAAO,gCACPC,MAAO,EACPC,YAAY,EACZC,qBAAsB,EACtBE,SAAU,CACR,CACElB,GAAI,WACJa,MAAO,qBACPM,KAAM,OACNC,QAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8FAqEsE,CAAC,CACjFC,aAAa,EACbC,UAAW,YACb,EACA,CACEtB,GAAI,WACJa,MAAO,8BACPM,KAAM,iBACNC,QAAS,2CACTG,SAAU,GACVF,aAAa,EACbC,UAAW,YACb,EACD,CACDE,KAAM,CACJxB,GAAI,WACJa,MAAO,qCACPM,KAAM,UACNM,UAAW,CACT,CACEzB,GAAI,KACJ0B,SAAU,mDACVP,KAAM,kBACNQ,QAAS,CAAC,QAAS,QAAS,QAAS,QAAQ,CAC7CC,cAAe,EACfC,YACE,4FACJ,EACA,CACE7B,GAAI,KACJ0B,SACE,oFACFP,KAAM,aACNS,cAAe,OACfC,YACE,kGACJ,EACD,CACDC,aAAc,GACdE,SAAU,EACVC,YAAa,EACbC,UAAU,CACZ,CACF,EACD,CACDC,WAAY,CACVnC,GAAI,YACJa,MAAO,qBACPM,KAAM,SACNM,UAAW,CACT,CACEzB,GAAI,MACJ0B,SACE,qFACFP,KAAM,kBACNQ,QAAS,CAAC,QAAS,QAAS,aAAc,mBAAmB,CAC7DC,cAAe,EACfC,YACE,iFACJ,EACD,CACDC,aAAc,GACdE,SAAU,EACVC,YAAa,EACbC,SAAU,EACZ,CACF,EACA,CACElC,GAAI,OACJa,MAAO,kCACPV,YAAa,qDACbW,MAAO,EACPC,YAAY,EACZC,qBAAsB,EACtBC,SAAU,CACR,CACEjB,GAAI,MACJa,MAAO,wBACPC,MAAO,EACPC,WAAY,GACZC,qBAAsB,EACtBE,SAAU,CACR,CACElB,GAAI,WACJa,MAAO,kBACPM,KAAM,OACNC,QAAS,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6IAwGqH,CAAC,CAChIC,aAAa,EACbC,UAAW,YACb,EACD,CACDE,KAAM,CACJxB,GAAI,WACJa,MAAO,aACPM,KAAM,UACNM,UAAW,CACT,CACEzB,GAAI,KACJ0B,SACE,oEACFP,KAAM,aACNS,cAAe,OACfC,YACE,kFACJ,EACD,CACDC,aAAc,GACdE,SAAU,EACVC,YAAa,EACbC,UAAU,CACZ,CACF,EACD,CACDC,WAAY,CACVnC,GAAI,YACJa,MAAO,qBACPM,KAAM,SACNM,UAAW,CACT,CACEzB,GAAI,MACJ0B,SAAU,sDACVP,KAAM,QACNS,cACE,2EACFC,YACE,+GACJ,EACD,CACDC,aAAc,GACdE,SAAU,EACVC,YAAa,EACbC,UAAU,CACZ,CACF,EACD,CACDE,UAAW,CACTpC,GAAI,aACJa,MAAO,qCACPM,KAAM,QACNM,UAAW,CACT,CACEzB,GAAI,MACJ0B,SACE,2EACFP,KAAM,kBACNQ,QAAS,CAAC,QAAS,QAAS,aAAc,SAAS,CACnDC,cAAe,EACfC,YACE,iGACJ,EACA,CACE7B,GAAI,MACJ0B,SACE,+DACFP,KAAM,aACNS,cAAe,OACfC,YACE,oHACJ,EACA,CACE7B,GAAI,MACJ0B,SACE,yEACFP,KAAM,QACNS,cACE,wJACFC,YACE,mJACJ,EACD,CACDC,aAAc,GACdC,UAAW,IACXC,SAAU,EACVC,YAAa,EACbC,UAAU,CACZ,EACAG,YAAa,CACXC,YAAY,EACZC,aAAa,EACbpB,KAAM,eACNqB,SAAU,iCACVC,eAAgB,UAChBC,aAAc,gBACdvC,YAAa,kGACf,CACF,EAAE,EAkByBF,IAAI,CACpBF,EAAmBK,UAAU,CAEtBL,EAAmBS,cAAc,CACpCT,EAAmBI,CADqB,UACV,CAChCJ,EAAmBM,SAAS,CAC9BN,EAAmBO,OAAO", "sources": ["webpack://terang-lms-ui/./src/components/ui/card.tsx", "webpack://terang-lms-ui/./src/constants/shared-course-data.ts"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "import { Course } from '@/types/lms';\r\n\r\n// Shared Architecture course data for both enroll and modules pages\r\nexport const architectureCourse: Course = {\r\n  id: 'arch-cert-001',\r\n  name: 'Sertifikasi Arsitek Profesional IAI',\r\n  code: 'IAI-CERT-001',\r\n  description:\r\n    'Program sertifikasi profesional untuk arsitek yang ingin memperoleh sertifikat Ikatan Arsitek Indonesia (IAI) dan meningkatkan kompetensi di bidang perencanaan dan pengawasan bangunan',\r\n  instructor: 'Ar. <PERSON>, IAI, AA',\r\n  startDate: '2024-08-01',\r\n  endDate: '2024-12-31',\r\n  enrollmentType: 'both',\r\n  enrollmentCode: 'IAI-CERT-2024',\r\n  minPassingScore: 80,\r\n  totalProgress: 0,\r\n  status: 'not-started',\r\n  modules: [\r\n    {\r\n      id: 'mod1',\r\n      title: 'Modul 1: Dasar-Dasar Arsitektur',\r\n      description: 'Memahami prinsip-prinsip dasar arsitektur dan sejarahnya',\r\n      order: 1,\r\n      isUnlocked: true,\r\n      completionPercentage: 0,\r\n      chapters: [\r\n        {\r\n          id: 'ch1',\r\n          title: 'Bab 1: Sejarah Arsitektur',\r\n          order: 1,\r\n          isUnlocked: true,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content1',\r\n              title: 'Prinsip-Prinsip Arsitektur Kuno',\r\n              type: 'text',\r\n              content: `# Ancient Architecture Principles\r\n\r\n## Introduction\r\n\r\nArchitecture has been a **fundamental aspect** of human civilization since ancient times. From the pyramids of Egypt to the temples of Greece, architectural principles have evolved to reflect cultural values, technological advances, and environmental considerations.\r\n\r\n## Key Civilizations and Their Contributions\r\n\r\n### 1. Egyptian Architecture\r\n- **Pyramids**: Monumental structures demonstrating advanced engineering\r\n- **Materials**: Limestone, granite, and sandstone\r\n- **Key Features**:\r\n  - Massive scale\r\n  - Precise geometric proportions\r\n  - Durability across millennia\r\n\r\n### 2. Greek Architecture\r\nThe Greeks developed the classical orders that influence architecture to this day:\r\n\r\n1. **Doric Order** - Simple and sturdy\r\n2. **Ionic Order** - Elegant with scroll-like capitals\r\n3. **Corinthian Order** - Ornate with acanthus leaf decorations\r\n\r\n### 3. Roman Architecture\r\nRomans revolutionized construction with:\r\n- **Concrete technology**\r\n- **Arches and vaults**\r\n- **Aqueducts and infrastructure**\r\n\r\n## Fundamental Principles\r\n\r\n> \"Architecture is the learned game, correct and magnificent, of forms assembled in the light.\" - Le Corbusier\r\n\r\n### Proportion and Scale\r\n- **Golden Ratio**: φ ≈ 1.618\r\n- **Human scale**: Buildings designed for human comfort\r\n- **Visual balance**: Creating harmony through proportional relationships\r\n\r\n### Materials and Structure\r\n\\`\\`\\`\r\nLoad-bearing elements:\r\n├── Walls\r\n├── Columns\r\n├── Beams\r\n└── Foundations\r\n\\`\\`\\`\r\n\r\n## Learning Objectives\r\nBy the end of this section, you will understand:\r\n- [ ] The evolution of architectural styles\r\n- [ ] Key principles of proportion and scale\r\n- [ ] Material properties and their applications\r\n- [ ] Cultural influences on architectural design\r\n\r\n---\r\n\r\n*Next: We'll explore how these ancient principles influenced Renaissance and modern architecture.*`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-15'\r\n            },\r\n            {\r\n              id: 'content2',\r\n              title: 'Tata Orde Klasik dalam Arsitektur',\r\n              type: 'video',\r\n              content: 'https://example.com/classical-orders-video',\r\n              duration: 25,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-16'\r\n            },\r\n            {\r\n              id: 'content3',\r\n              title: 'Dasar-Dasar Gambar Arsitektur',\r\n              type: 'pdf',\r\n              content: 'https://example.com/drawing-fundamentals.pdf',\r\n              isCompleted: false,\r\n              createdAt: '2024-01-17'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch1',\r\n            title: 'Kuis Bab 1: Sejarah Arsitektur',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q1',\r\n                question:\r\n                  'Which architectural order is characterized by simple, sturdy columns?',\r\n                type: 'multiple-choice',\r\n                options: ['Doric', 'Ionic', 'Corinthian', 'Composite'],\r\n                correctAnswer: 0,\r\n                explanation:\r\n                  'The Doric order is the simplest and most robust of the classical orders.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            timeLimit: 15,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        },\r\n        {\r\n          id: 'ch2',\r\n          title: 'Bab 2: Prinsip-Prinsip Desain',\r\n          order: 2,\r\n          isUnlocked: false,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content4',\r\n              title: 'Proporsi dan Skala',\r\n              type: 'text',\r\n              content: `# Proportion and Scale in Architecture\r\n\r\n## What is Proportion?\r\n\r\n**Proportion** refers to the relationship between different elements in architectural design. It's the foundation of creating visually pleasing and harmonious structures.\r\n\r\n## The Golden Ratio (φ = 1.618)\r\n\r\nThe **Golden Ratio** has been used in architecture for millennia:\r\n\r\n| Building | Golden Ratio Application |\r\n|----------|-------------------------|\r\n| Parthenon | Façade proportions |\r\n| Notre-Dame | Rose window design |\r\n| Villa Savoye | Room dimensions |\r\n\r\n### Mathematical Expression\r\n\\`\\`\\`\r\nφ = (1 + √5) / 2 ≈ 1.618033988...\r\n\\`\\`\\`\r\n\r\n## Types of Scale\r\n\r\n### 1. Human Scale\r\nBuildings should relate to human dimensions:\r\n- **Door heights**: ~2.1m (7 feet)\r\n- **Ceiling heights**: 2.4-3.0m (8-10 feet)\r\n- **Stair risers**: 15-18cm (6-7 inches)\r\n\r\n### 2. Architectural Scale\r\nHow building elements relate to each other:\r\n- Window to wall ratios\r\n- Column spacing\r\n- Room proportions\r\n\r\n### 3. Urban Scale\r\nHow buildings relate to their context:\r\n- Street width to building height\r\n- Public space dimensions\r\n- Neighborhood character\r\n\r\n## Classical Proportional Systems\r\n\r\n### Vitruvian Proportions\r\nBased on the human body:\r\n> \"The human body is a model of proportion because with arms or legs extended it fits into those 'perfect' geometrical forms, the square and the circle.\"\r\n\r\n### Modular Systems\r\n- **Le Corbusier's Modulor**: Based on human measurements\r\n- **Japanese Ken**: Traditional proportional system\r\n- **Classical Orders**: Column to entablature ratios\r\n\r\n## Design Exercise\r\n\r\nTry this proportional analysis:\r\n1. Find a building you admire\r\n2. Measure key dimensions\r\n3. Calculate the ratios\r\n4. Compare to golden ratio or other systems\r\n\r\n## Key Takeaways\r\n\r\n✅ **Good proportion creates visual harmony**  \r\n✅ **Human scale ensures comfort**  \r\n✅ **Mathematical ratios provide guidelines**  \r\n✅ **Context matters in scale decisions**  \r\n\r\n---\r\n\r\n*Remember: Proportion is felt, not calculated. Trust your eye, but understand the principles.*`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-18'\r\n            },\r\n            {\r\n              id: 'content5',\r\n              title: 'Rasio Emas dalam Arsitektur',\r\n              type: 'zoom-recording',\r\n              content: 'https://zoom.us/rec/golden-ratio-lecture',\r\n              duration: 45,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-19'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch2',\r\n            title: 'Kuis Bab 2: Prinsip-Prinsip Desain',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q2',\r\n                question: 'What is the Golden Ratio approximately equal to?',\r\n                type: 'multiple-choice',\r\n                options: ['1.414', '1.618', '1.732', '2.000'],\r\n                correctAnswer: 1,\r\n                explanation:\r\n                  'The Golden Ratio is approximately 1.618 and is commonly used in architectural proportions.'\r\n              },\r\n              {\r\n                id: 'q3',\r\n                question:\r\n                  'Proportion in architecture refers to the relationship between different elements.',\r\n                type: 'true-false',\r\n                correctAnswer: 'true',\r\n                explanation:\r\n                  'Proportion is indeed about the harmonious relationship between different architectural elements.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        }\r\n      ],\r\n      moduleQuiz: {\r\n        id: 'quiz-mod1',\r\n        title: 'Kuis Akhir Modul 1',\r\n        type: 'module',\r\n        questions: [\r\n          {\r\n            id: 'qm1',\r\n            question:\r\n              'Which of the following are classical architectural orders? (Select all that apply)',\r\n            type: 'multiple-choice',\r\n            options: ['Doric', 'Ionic', 'Corinthian', 'All of the above'],\r\n            correctAnswer: 3,\r\n            explanation:\r\n              'Doric, Ionic, and Corinthian are the three main classical architectural orders.'\r\n          }\r\n        ],\r\n        minimumScore: 75,\r\n        attempts: 0,\r\n        maxAttempts: 2,\r\n        isPassed: false\r\n      }\r\n    },\r\n    {\r\n      id: 'mod2',\r\n      title: 'Modul 2: Arsitektur Kontemporer',\r\n      description: 'Gerakan arsitektur modern dan desain berkelanjutan',\r\n      order: 2,\r\n      isUnlocked: false,\r\n      completionPercentage: 0,\r\n      chapters: [\r\n        {\r\n          id: 'ch3',\r\n          title: 'Bab 3: Gerakan Modern',\r\n          order: 1,\r\n          isUnlocked: false,\r\n          completionPercentage: 0,\r\n          contents: [\r\n            {\r\n              id: 'content6',\r\n              title: 'Gerakan Bauhaus',\r\n              type: 'text',\r\n              content: `# The Bauhaus Movement: Form Follows Function\r\n\r\n## Overview\r\n\r\nThe **Bauhaus** (1919-1933) was a revolutionary design school that fundamentally changed architecture, art, and design. Founded by Walter Gropius in Weimar, Germany, it promoted the integration of art, craft, and industrial technology.\r\n\r\n## Core Principles\r\n\r\n### 1. **Form Follows Function**\r\n- Design should be determined by purpose\r\n- No unnecessary ornamentation\r\n- Efficiency in both form and construction\r\n\r\n### 2. **Gesamtkunstwerk** (Total Work of Art)\r\n- Integration of all arts and crafts\r\n- Architecture as the master art\r\n- Unified design philosophy\r\n\r\n### 3. **Machine Aesthetic**\r\n- Embrace industrial production methods\r\n- Clean lines and geometric forms\r\n- Mass production capabilities\r\n\r\n## Key Figures\r\n\r\n| Name | Role | Contribution |\r\n|------|------|-------------|\r\n| **Walter Gropius** | Founder & Director | Established core philosophy |\r\n| **Ludwig Mies van der Rohe** | Director (1930-33) | \"Less is more\" principle |\r\n| **Marcel Breuer** | Faculty | Furniture design innovation |\r\n| **László Moholy-Nagy** | Faculty | Photography and typography |\r\n\r\n## Architectural Characteristics\r\n\r\n### Visual Elements\r\n- ✅ **Clean geometric forms**\r\n- ✅ **Flat roofs**\r\n- ✅ **Large windows**\r\n- ✅ **Open floor plans**\r\n- ❌ Historical references\r\n- ❌ Decorative elements\r\n\r\n### Materials\r\n\\`\\`\\`\r\nPrimary materials:\r\n├── Steel frame construction\r\n├── Glass curtain walls\r\n├── Reinforced concrete\r\n└── Industrial materials\r\n\\`\\`\\`\r\n\r\n## Famous Bauhaus Buildings\r\n\r\n### 1. Fagus Factory (1911-1913)\r\n- **Architect**: Walter Gropius\r\n- **Innovation**: Glass curtain wall\r\n- **Significance**: Proto-Bauhaus design\r\n\r\n### 2. Bauhaus Dessau (1925-1926)\r\n- **Architect**: Walter Gropius\r\n- **Features**: Modular design, industrial materials\r\n- **Legacy**: Became the school's permanent home\r\n\r\n### 3. Barcelona Pavilion (1929)\r\n- **Architect**: Mies van der Rohe\r\n- **Concept**: Flowing space, minimal structure\r\n- **Impact**: Redefined modern architecture\r\n\r\n## Impact on Modern Architecture\r\n\r\n> \"The ultimate goal of all visual arts is the complete building!\" - Walter Gropius\r\n\r\n### International Style\r\nThe Bauhaus influenced what became known as the **International Style**:\r\n- Emphasis on volume over mass\r\n- Regularity rather than symmetry\r\n- No applied ornamentation\r\n\r\n### Urban Planning\r\n- **Functional zoning**\r\n- **Housing for the masses**\r\n- **Integration with landscape**\r\n\r\n## Legacy and Criticism\r\n\r\n### Positive Impact\r\n- **Democratized design**: Good design for everyone\r\n- **Functional solutions**: Buildings that work efficiently  \r\n- **Modern materials**: Pioneered new construction techniques\r\n\r\n### Criticisms\r\n- **Cultural homogenization**: Loss of regional character\r\n- **Human scale**: Sometimes overlooked human needs\r\n- **Context insensitivity**: Universal solutions vs. local conditions\r\n\r\n## Contemporary Relevance\r\n\r\nThe Bauhaus principles remain relevant today:\r\n- **Sustainable design**: Efficiency and functionality\r\n- **Digital integration**: Technology serving human needs\r\n- **Social responsibility**: Design for collective benefit\r\n\r\n---\r\n\r\n*\"The Bauhaus is not a style but a principle - the principle that the artistic process and the industrial process are one.\"* - Walter Gropius`,\r\n              isCompleted: false,\r\n              createdAt: '2024-01-20'\r\n            }\r\n          ],\r\n          quiz: {\r\n            id: 'quiz-ch3',\r\n            title: 'Kuis Bab 3',\r\n            type: 'chapter',\r\n            questions: [\r\n              {\r\n                id: 'q4',\r\n                question:\r\n                  'The Bauhaus movement emphasized functionality over ornamentation.',\r\n                type: 'true-false',\r\n                correctAnswer: 'true',\r\n                explanation:\r\n                  'The Bauhaus movement was known for its emphasis on functionality and minimalism.'\r\n              }\r\n            ],\r\n            minimumScore: 70,\r\n            attempts: 0,\r\n            maxAttempts: 3,\r\n            isPassed: false\r\n          }\r\n        }\r\n      ],\r\n      moduleQuiz: {\r\n        id: 'quiz-mod2',\r\n        title: 'Kuis Akhir Modul 2',\r\n        type: 'module',\r\n        questions: [\r\n          {\r\n            id: 'qm2',\r\n            question: 'What are the key principles of modern architecture?',\r\n            type: 'essay',\r\n            correctAnswer:\r\n              'Sample answer about functionality, simplicity, and form follows function',\r\n            explanation:\r\n              'Modern architecture emphasizes functionality, simplicity, and the principle that form should follow function.'\r\n          }\r\n        ],\r\n        minimumScore: 75,\r\n        attempts: 0,\r\n        maxAttempts: 2,\r\n        isPassed: false\r\n      }\r\n    }\r\n  ],\r\n  finalExam: {\r\n    id: 'final-exam',\r\n    title: 'Ujian Akhir Sertifikasi Arsitektur',\r\n    type: 'final',\r\n    questions: [\r\n      {\r\n        id: 'qf1',\r\n        question:\r\n          'Which architectural order features ornate capitals with acanthus leaves?',\r\n        type: 'multiple-choice',\r\n        options: ['Doric', 'Ionic', 'Corinthian', 'Tuscan'],\r\n        correctAnswer: 2,\r\n        explanation:\r\n          'The Corinthian order is distinguished by its elaborate capitals decorated with acanthus leaves.'\r\n      },\r\n      {\r\n        id: 'qf2',\r\n        question:\r\n          'The Golden Ratio is frequently used in architectural design.',\r\n        type: 'true-false',\r\n        correctAnswer: 'true',\r\n        explanation:\r\n          'The Golden Ratio (approximately 1.618) has been used in architecture for centuries to create pleasing proportions.'\r\n      },\r\n      {\r\n        id: 'qf3',\r\n        question:\r\n          'Describe the influence of the Bauhaus movement on modern architecture.',\r\n        type: 'essay',\r\n        correctAnswer:\r\n          'The Bauhaus movement emphasized functionality, simplicity, and the integration of art and technology, leading to modern minimalist design principles.',\r\n        explanation:\r\n          'The Bauhaus movement revolutionized architecture by promoting functional design, clean lines, and the principle that form should follow function.'\r\n      }\r\n    ],\r\n    minimumScore: 80,\r\n    timeLimit: 120,\r\n    attempts: 0,\r\n    maxAttempts: 2,\r\n    isPassed: false\r\n  },\r\n  certificate: {\r\n    isEligible: true,\r\n    isGenerated: false,\r\n    type: 'professional',\r\n    issuedBy: 'Ikatan Arsitek Indonesia (IAI)',\r\n    validityPeriod: '5 years',\r\n    credentialId: 'IAI-CERT-2024',\r\n    description: 'Sertifikat Arsitek Profesional yang diakui secara nasional untuk praktik arsitektur di Indonesia'\r\n  }\r\n};\r\n\r\n// For enroll page compatibility, convert to class format\r\nexport interface ClassData {\r\n  id: number;\r\n  name: string;\r\n  teacher: string;\r\n  schedule: string;\r\n  enrollmentCode: string;\r\n  materials: any[];\r\n  thumbnail?: string;\r\n  description?: string;\r\n  startDate?: string;\r\n  endDate?: string;\r\n}\r\n\r\nexport const architectureClass: ClassData = {\r\n  id: 1,\r\n  name: architectureCourse.name,\r\n  teacher: architectureCourse.instructor,\r\n  schedule: 'Sen, Rab 10:00 WIB',\r\n  enrollmentCode: architectureCourse.enrollmentCode || 'ARCH2024-001',\r\n  description: architectureCourse.description,\r\n  startDate: architectureCourse.startDate,\r\n  endDate: architectureCourse.endDate,\r\n  materials: [] // Will be populated from modules when enrolled\r\n};\r\n\r\n// Institution data\r\nexport const architectureInstitution = {\r\n  id: 'iai-indonesia',\r\n  name: 'Ikatan Arsitek Indonesia',\r\n  shortName: 'IAI',\r\n  website: 'https://iai.or.id',\r\n  certificateTemplate: {\r\n    primaryColor: '#1e40af',\r\n    secondaryColor: '#f59e0b',\r\n    signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',\r\n    signatoryTitle: 'Ketua Umum IAI 2024-2027'\r\n  }\r\n};\r\n"], "names": ["Card", "className", "props", "div", "data-slot", "cn", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "architectureCourse", "id", "name", "code", "description", "instructor", "startDate", "endDate", "enrollmentType", "enrollmentCode", "minPassingScore", "totalProgress", "status", "modules", "title", "order", "isUnlocked", "completionPercentage", "chapters", "contents", "type", "content", "isCompleted", "createdAt", "duration", "quiz", "questions", "question", "options", "<PERSON><PERSON><PERSON><PERSON>", "explanation", "minimumScore", "timeLimit", "attempts", "maxAttempts", "isPassed", "moduleQuiz", "finalExam", "certificate", "isEligible", "isGenerated", "issuedBy", "validityPeriod", "credentialId"], "sourceRoot": ""}