import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import {
  BookOpen,
  Book,
  List,
  Play,
  FileText,
  BookMarked,
  Target,
  Trophy
} from 'lucide-react';
import { TableOfContentsProps } from '@/types/lms';
import { TreeNode } from './tree-node';

export const TableOfContents: React.FC<TableOfContentsProps> = ({
  course,
  onNavigate,
  expandedModules,
  expandedChapters,
  onToggleModule,
  onToggleChapter,
  currentModuleIndex
}) => {
  return (
    <Card className='sticky top-4 h-fit w-full max-w-full'>
      <CardHeader className='pb-4'>
        <CardTitle className='flex items-center text-xl'>
          <List className='mr-3 h-6 w-6' />
          Table of Contents
        </CardTitle>
      </CardHeader>
      <CardContent className='max-h-[calc(100vh-12rem)] space-y-1 overflow-y-auto overflow-x-hidden'>
        {course.modules.map((module, index) => {
          const moduleCompleted =
            module.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
            ) && module.moduleQuiz.isPassed;

          const isCurrentModule = currentModuleIndex === index;

          return (
            <TreeNode
              key={module.id}
              id={module.id}
              title={module.title}
              icon={<BookOpen className='h-5 w-5' />}
              isUnlocked={module.isUnlocked}
              isCompleted={moduleCompleted}
              level={0}
              hasChildren={true}
              isExpanded={expandedModules[module.id]}
              onToggle={() => onToggleModule(module.id)}
              isActive={isCurrentModule}
            >
              {module.chapters.map((chapter) => {
                const chapterCompleted =
                  chapter.contents.every((c) => c.isCompleted) &&
                  chapter.quiz.isPassed;

                return (
                  <TreeNode
                    key={chapter.id}
                    id={chapter.id}
                    title={chapter.title}
                    icon={<Book className='h-4 w-4' />}
                    isUnlocked={chapter.isUnlocked}
                    isCompleted={chapterCompleted}
                    level={1}
                    hasChildren={true}
                    isExpanded={expandedChapters[chapter.id]}
                    onToggle={() => onToggleChapter(chapter.id)}
                  >
                    {chapter.contents.map((content) => (
                      <TreeNode
                        key={content.id}
                        id={content.id}
                        title={content.title}
                        icon={
                          content.type === 'video' ? (
                            <Play className='h-4 w-4 text-red-500' />
                          ) : content.type === 'pdf' ? (
                            <FileText className='h-4 w-4 text-red-600' />
                          ) : content.type === 'zoom-recording' ? (
                            <Play className='h-4 w-4 text-blue-500' />
                          ) : (
                            <BookMarked className='h-4 w-4 text-blue-500' />
                          )
                        }
                        isUnlocked={chapter.isUnlocked}
                        isCompleted={content.isCompleted}
                        level={2}
                        onClick={() => onNavigate(module.id, chapter.id, content.id)}
                      />
                    ))}
                    <TreeNode
                      key={`${chapter.id}-quiz`}
                      id={`${chapter.id}-quiz`}
                      title='Chapter Quiz'
                      icon={<Target className='h-4 w-4' />}
                      isUnlocked={chapter.contents.every((c) => c.isCompleted)}
                      isCompleted={chapter.quiz.isPassed}
                      level={2}
                      onClick={() => onNavigate(module.id, chapter.id)}
                    />
                  </TreeNode>
                );
              })}
              <TreeNode
                key={`${module.id}-quiz`}
                id={`${module.id}-quiz`}
                title='Module Quiz'
                icon={<BookMarked className='h-4 w-4' />}
                isUnlocked={module.chapters.every(
                  (ch) =>
                    ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
                )}
                isCompleted={module.moduleQuiz.isPassed}
                level={1}
                onClick={() => onNavigate(module.id)}
              />
            </TreeNode>
          );
        })}

        <TreeNode
          key='final-exam'
          id='final-exam'
          title='Final Exam'
          icon={<Trophy className='h-5 w-5' />}
          isUnlocked={course.modules.every(
            (m) =>
              m.chapters.every(
                (ch) =>
                  ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
              ) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts)
          )}
          isCompleted={course.finalExam.isPassed}
          level={0}
        />
      </CardContent>
    </Card>
  );
};