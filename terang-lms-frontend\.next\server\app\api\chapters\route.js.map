{"version": 3, "file": "../app/api/chapters/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,iXCMO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IAAMC,EAAeD,EAAQE,KAARF,EAAe,CAACC,YAAY,CAC3CE,EAAWF,EAAaG,GAAG,CAA3BD,MAAWF,MACXI,EAAYJ,EAAaG,GAAG,CAAC,CAA7BC,KAAYJ,OAElB,GAAI,CAACE,EACH,MADGA,CACIG,CADM,CACNA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,qBAAqB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAI1E,GAAIJ,EAAW,CACb,IAAMK,EADJL,MAC6BM,EAAAA,EAAAA,CAC5BC,GADGF,GACG,CAAC,CACNP,QAAAA,CAAUU,EAAAA,OAAOA,CAACC,EAAE,CACpBC,QAAAA,CAAUF,EAAAA,OAAOA,CAACE,QAAQ,CAC1BV,SAAAA,CAAWW,EAAAA,OAAOA,CAACX,SACrB,GACCY,IAAI,CAACJ,EAAAA,OAAAA,CAAAA,CACLK,QAAQ,CAACF,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,OAAAA,CAAQE,QAAQ,CAAEC,EAAAA,OAAAA,CAAQF,EAAE,GACjDM,KAAK,CACJC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEF,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,OAAAA,CAAQC,EAAE,CAAEQ,QAAAA,CAASnB,IACxBgB,CAAAA,EAAAA,CADwBhB,CAAAA,CAAAA,CACxBgB,CAAAA,CAAGH,EAAAA,OAAAA,CAAQX,SAAS,CAAEiB,QAAAA,CAASjB,MAGlCkB,GAHkClB,CAAAA,CAAAA,CAAAA,CAAAA,EAKrC,GAAgC,GAAG,CAA/BK,EAAiBc,MAAM,CACzB,OAAOlB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,oCAAoC,CAC7C,CAAEC,MAAAA,CAAQ,GAAI,EAGpB,CAGA,IAAMgB,EAAiB,MAAMd,EAAAA,EAAAA,CAC1BC,CADGa,KACG,GACNR,IAAI,CAACS,EAAAA,QAAAA,CAAAA,CACLN,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,QAAAA,CAASvB,QAAQ,CAAEmB,QAAAA,CAASnB,KAElCwB,EAAwB,CAFUxB,CAAAA,CAAAA,CAAAA,EAEJyB,OAAAA,CAAQC,GAAG,CAC7CJ,CADIE,CACWG,GAAG,CAAC,MAAOC,EAA1BN,EACE,GADwBM,CAClBC,EAAY,MAAMrB,CAAlBqB,CAAkBrB,EAAAA,CACrBC,MAAM,CAAC,CAAEqB,KAAAA,CAAOC,EAAAA,OAAOA,CAACpB,EAAAA,CAAG,EAC3BG,IAAI,CAACiB,EAAAA,OAAAA,CAAAA,CACLd,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACe,EAAAA,OAAAA,CAAQC,SAAS,CAAEJ,EAAQjB,EAAE,GAEzC,MAAO,CACL,GAAGiB,CAAO,CACVK,OAAAA,CAASL,EAAQK,KAARL,EAAe,CAAGM,IAAAA,CAAKC,KAAK,CAACP,EAAQK,KAARL,EAAe,EAAc,KACnEC,SAAAA,CAAWA,EAAUR,MAAAA,CAEzB,IAGF,OAAOlB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEmB,QAAAA,CAAUC,CAAsB,EAC7D,CAAE,MAAOnB,EAAO,CAEd,EAFOA,KACP+B,OAAAA,CAAQ/B,KAAK,CAAC,2BAA4BA,GACnCF,EAAAA,CADmCE,WACnCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAe+B,EAAKxC,CAAoB,EAC7C,GAAI,CAEF,GAAM,MACJyC,CAAI,SACJL,CAAO,UACPjC,CAAQ,WACRE,CAAS,YACTqC,CAAU,CACX,CAPY,EAOTC,IAAAA,EAPuBpC,IAAI,CAAZP,EAUnB,GAAI,CAACyC,GAAQ,CAACtC,EACZ,MADYA,CACLG,CADe,CACfA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,kCAAkC,CAC3C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMC,EAAmB,MAAMC,EAAAA,EAAAA,CAC5BC,GADGF,GACG,CAAC,CACNP,QAAAA,CAAUU,EAAAA,OAAOA,CAACC,EAAE,CACpBC,QAAAA,CAAUF,EAAAA,OAAOA,CAACE,QAAQ,CAC1BV,SAAAA,CAAWW,EAAAA,OAAOA,CAACX,SAAAA,CACrB,EACCY,IAAI,CAACJ,EAAAA,OAAAA,CAAAA,CACLK,QAAQ,CAACF,EAAAA,OAAAA,CAASG,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,OAAOA,CAACE,QAAQ,CAAEC,EAAAA,OAAOA,CAACF,EAAE,GACjDM,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGN,EAAAA,OAAOA,CAACC,EAAE,CAAEX,IACrBoB,IADqBpB,CAAAA,CAAAA,GAGxB,GAAgC,GAAG,GAAdqB,MAAM,CACzB,GADEd,IACKJ,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,KAAAA,CAAO,mBAAmB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAGxE,GAAIJ,GAAaK,CAAgB,CAAC,EAAE,CAACL,SAAS,GAAKA,EACjD,OADiDA,EAAW,YACrDC,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,gDAAgD,CACzD,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAImC,EAAkBF,EACtB,QADsBA,GAAlBE,CACAA,EAA+B,CACjC,EADsBC,EAChBC,EAAmB,MAAMnC,EAAAA,EAAAA,CAC5BC,GADGkC,GACG,CAAC,CAAEJ,UAAAA,CAAYhB,EAAAA,QAAQA,CAACgB,UAAAA,CAAW,EACzCzB,IAAI,CAACS,EAAAA,QAAAA,CAAAA,CACLN,KAAK,CAACD,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGO,EAAAA,QAAAA,CAASvB,QAAQ,CAAEA,IAE/ByC,EAAkBE,EAFa3C,CAAAA,CAAAA,IAEU,CAAG,EACxC4C,EADJH,EACIG,CAAKC,GAAG,IAAIF,EAAiBhB,GAAG,CAACmB,CAAAA,EAAKA,CAAAA,CAAEP,KAA5BI,KAAsC,EAAI,IAAM,EAC5D,CACN,CAGA,IAAMI,EAAa,MAAMvC,EAAAA,EAAAA,CACtBwC,MAAM,CAACzB,EAAAA,QAAAA,CAAAA,CACP0B,MAAM,CAAC,MACNX,EACAL,EADAK,KACAL,CAASA,EAAUC,IAAAA,CAAVD,SAAwB,CAACA,GAAW,IAAXA,CAAAA,SAClCjC,EACAuC,MADAvC,IACAuC,CAAYE,CACd,GACCS,SAAS,GAEZ,OAAO/C,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEwB,OAAAA,CAAS,CACP,GAAGmB,CAAU,CAAC,EAAE,CAChBd,OAAAA,CAASc,CAAU,CAAC,EAAE,CAACd,OACzB,EACAkB,OAAAA,CAAS,+BACX,CACA,CAAE7C,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOD,EAAO,CAEd,EAFOA,KACP+B,OAAAA,CAAQ/B,KAAK,CAAC,0BAA2BA,GAClCF,EADkCE,CAAAA,WAClCF,CAAaC,IAAI,CACtB,CAAEC,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCpJA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAIH,EAAoB,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CAER,CAGA,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,eAAe,SACnC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAExB,CAIC,IAAC,EAAM,CAAH,CAAe8C,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,EAAYC,CAAf,MAA6C,EAA/B,KAAsC,EAEzD,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,2BACA,yBACA,iBACA,mCACA,CAAK,CACL,6IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/src/app/api/chapters/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?9197", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { chapters, modules, courses, quizzes } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET /api/chapters - Get chapters for a module\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const moduleId = searchParams.get('moduleId');\r\n    const teacherId = searchParams.get('teacherId');\r\n    \r\n    if (!moduleId) {\r\n      return NextResponse.json({ error: 'Module ID required' }, { status: 400 });\r\n    }\r\n\r\n    // Verify module exists and teacher has access\r\n    if (teacherId) {\r\n      const moduleWithCourse = await db\r\n        .select({\r\n          moduleId: modules.id,\r\n          courseId: modules.courseId,\r\n          teacherId: courses.teacherId\r\n        })\r\n        .from(modules)\r\n        .leftJoin(courses, eq(modules.courseId, courses.id))\r\n        .where(\r\n          and(\r\n            eq(modules.id, parseInt(moduleId)),\r\n            eq(courses.teacherId, parseInt(teacherId))\r\n          )\r\n        )\r\n        .limit(1);\r\n\r\n      if (moduleWithCourse.length === 0) {\r\n        return NextResponse.json(\r\n          { error: 'Module not found or access denied' },\r\n          { status: 403 }\r\n        );\r\n      }\r\n    }\r\n\r\n    // Get chapters with quiz count\r\n    const moduleChapters = await db\r\n      .select()\r\n      .from(chapters)\r\n      .where(eq(chapters.moduleId, parseInt(moduleId)));\r\n\r\n    const chaptersWithQuizCount = await Promise.all(\r\n      moduleChapters.map(async (chapter) => {\r\n        const quizCount = await db\r\n          .select({ count: quizzes.id })\r\n          .from(quizzes)\r\n          .where(eq(quizzes.chapterId, chapter.id));\r\n\r\n        return {\r\n          ...chapter,\r\n          content: chapter.content ? JSON.parse(chapter.content as string) : null,\r\n          quizCount: quizCount.length\r\n        };\r\n      })\r\n    );\r\n\r\n    return NextResponse.json({ chapters: chaptersWithQuizCount });\r\n  } catch (error) {\r\n    console.error('Error fetching chapters:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/chapters - Create a new chapter\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const {\r\n      name,\r\n      content,\r\n      moduleId,\r\n      teacherId,\r\n      orderIndex\r\n    } = body;\r\n\r\n    // Validate required fields\r\n    if (!name || !moduleId) {\r\n      return NextResponse.json(\r\n        { error: 'Name and module ID are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify module exists and teacher has access\r\n    const moduleWithCourse = await db\r\n      .select({\r\n        moduleId: modules.id,\r\n        courseId: modules.courseId,\r\n        teacherId: courses.teacherId\r\n      })\r\n      .from(modules)\r\n      .leftJoin(courses, eq(modules.courseId, courses.id))\r\n      .where(eq(modules.id, moduleId))\r\n      .limit(1);\r\n\r\n    if (moduleWithCourse.length === 0) {\r\n      return NextResponse.json({ error: 'Module not found' }, { status: 404 });\r\n    }\r\n\r\n    if (teacherId && moduleWithCourse[0].teacherId !== teacherId) {\r\n      return NextResponse.json(\r\n        { error: 'Not authorized to add chapters to this module' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // If no orderIndex provided, set it to the next available\r\n    let finalOrderIndex = orderIndex;\r\n    if (finalOrderIndex === undefined) {\r\n      const existingChapters = await db\r\n        .select({ orderIndex: chapters.orderIndex })\r\n        .from(chapters)\r\n        .where(eq(chapters.moduleId, moduleId));\r\n      \r\n      finalOrderIndex = existingChapters.length > 0 \r\n        ? Math.max(...existingChapters.map(c => c.orderIndex || 0)) + 1 \r\n        : 1;\r\n    }\r\n\r\n    // Create the chapter\r\n    const newChapter = await db\r\n      .insert(chapters)\r\n      .values({\r\n        name,\r\n        content: content ? JSON.stringify(content) : null,\r\n        moduleId,\r\n        orderIndex: finalOrderIndex\r\n      })\r\n      .returning();\r\n\r\n    return NextResponse.json(\r\n      {\r\n        chapter: {\r\n          ...newChapter[0],\r\n          content: newChapter[0].content, // Drizzle ORM handles JSON parsing\r\n        },\r\n        message: 'Chapter created successfully'\r\n      },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating chapter:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/chapters',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\chapters\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/chapters/route\",\n        pathname: \"/api/chapters\",\n        filename: \"route\",\n        bundlePath: \"app/api/chapters/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\chapters\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "searchParams", "nextUrl", "moduleId", "get", "teacherId", "NextResponse", "json", "error", "status", "moduleWithCourse", "db", "select", "modules", "id", "courseId", "courses", "from", "leftJoin", "eq", "where", "and", "parseInt", "limit", "length", "moduleChapters", "chapters", "chaptersWithQuizCount", "Promise", "all", "map", "chapter", "quizCount", "count", "quizzes", "chapterId", "content", "JSON", "parse", "console", "POST", "name", "orderIndex", "body", "finalOrderIndex", "undefined", "existingChapters", "Math", "max", "c", "newChapter", "insert", "values", "returning", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}