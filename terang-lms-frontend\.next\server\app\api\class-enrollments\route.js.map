{"version": 3, "file": "../app/api/class-enrollments/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,uaCMO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CACF,IAKIC,EALE,aAKFA,CALIC,CAAY,CAAE,CAAG,IAAIC,GAAAA,CAAIH,EAAQI,GAAG,EACtCC,EAAUH,EAAaI,GAAvBD,CAA2B,MAAjBH,KACVK,EAAYL,EAAaI,GAAG,CAAC,CAA7BC,KAAYL,OAIdG,GAAWE,EACbN,EAAkBO,CAAAA,EAAAA,EADLD,EAAW,CACNC,CAChBC,CAAAA,EAAAA,CADFR,CACEQ,EAAAA,CAAAA,CAAGC,EAAAA,gBAAAA,CAAiBL,OAAO,CAAEM,QAAAA,CAASN,IACtCI,CAAAA,EADsCJ,CAAAA,CAAAA,EACtCI,CAAAA,CAAGC,EAAAA,gBAAAA,CAAiBH,SAAS,CAAEI,QAAAA,CAASJ,KAEjCF,EACTJ,EAAkBQ,CAAAA,CAHwBF,CAGxBE,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,EAArBT,cAAqBS,CAAiBL,OAAO,CAAEM,QAAAA,CAASN,IAC/CE,GAD+CF,CAExDJ,CAFwDI,CAEtCI,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,EAArBT,cAAqBS,CAAiBH,SAAS,CAAEI,QAAAA,CAASJ,GAAAA,CAAAA,CAI5D,IAJ4DA,EAIxCN,EAChB,MAAMW,CADJC,CACID,EAAAA,CACHE,EAFab,IAEP,CAAC,CACNc,EAAAA,CAAIL,EAAAA,gBAAgBA,CAACK,EAAE,CACvBR,SAAAA,CAAWG,EAAAA,gBAAgBA,CAACH,SAAS,CACrCF,OAAAA,CAASK,EAAAA,gBAAgBA,CAACL,OAAO,CACjCW,UAAAA,CAAYN,EAAAA,gBAAgBA,CAACM,UAAU,CACvCC,MAAAA,CAAQP,EAAAA,gBAAgBA,CAACO,MAAM,CAC/BC,SAAAA,CAAWR,EAAAA,gBAAgBA,CAACQ,SAAS,CACrCC,SAAAA,CAAWT,EAAAA,gBAAgBA,CAACS,SAAS,CACrCC,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBC,YAAAA,CAAcF,EAAAA,KAAKA,CAACG,KAAK,CACzBC,SAAAA,CAAWC,EAAAA,OAAOA,CAACJ,IAAAA,GAEpBK,IAAI,CAACjB,EAAAA,gBAAAA,CAAAA,CACLkB,QAAQ,CAACP,EAAAA,KAAAA,CAAOZ,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,gBAAAA,CAAiBH,SAAS,CAAEc,EAAAA,KAAAA,CAAMN,EAAE,GACvDa,QAAQ,CAACF,EAAAA,OAAAA,CAASjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,gBAAAA,CAAiBL,OAAO,CAAEqB,EAAAA,OAAAA,CAAQX,EAAE,GACzDc,KAAK,CAAC5B,GACT,MAAMW,EAAAA,EAAAA,CACHE,CAFMb,CAAAA,IAEA,CAAC,CACNc,EAAAA,CAAIL,EAAAA,gBAAgBA,CAACK,EAAE,CACvBR,SAAAA,CAAWG,EAAAA,gBAAgBA,CAACH,SAAS,CACrCF,OAAAA,CAASK,EAAAA,gBAAgBA,CAACL,OAAO,CACjCW,UAAAA,CAAYN,EAAAA,gBAAgBA,CAACM,UAAU,CACvCC,MAAAA,CAAQP,EAAAA,gBAAgBA,CAACO,MAAM,CAC/BC,SAAAA,CAAWR,EAAAA,gBAAgBA,CAACQ,SAAS,CACrCC,SAAAA,CAAWT,EAAAA,gBAAgBA,CAACS,SAAS,CACrCC,WAAAA,CAAaC,EAAAA,KAAKA,CAACC,IAAI,CACvBC,YAAAA,CAAcF,EAAAA,KAAKA,CAACG,KAAK,CACzBC,SAAAA,CAAWC,EAAAA,OAAOA,CAACJ,IAAAA,CACrB,EACCK,IAAI,CAACjB,EAAAA,gBAAAA,CAAAA,CACLkB,QAAQ,CAACP,EAAAA,KAAAA,CAAOZ,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGC,EAAAA,gBAAgBA,CAACH,SAAS,CAAEc,EAAAA,KAAKA,CAACN,EAAE,GACvDa,QAAQ,CAACF,EAAAA,OAAAA,CAASjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,gBAAAA,CAAiBL,OAAO,CAAEqB,EAAAA,OAAAA,CAAQX,EAAE,GAEhE,OAAOe,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CACvBC,OAAAA,EAAS,EACTC,IAAAA,CAAMpB,CACR,EACF,CAAE,MAAOqB,EAAO,CAEd,EAFOA,KACPC,OAAAA,CAAQD,KAAK,CAAC,oCAAqCA,GAC5CJ,EAD4CI,CAAAA,WAC5CJ,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTE,KAAAA,CAAO,oCACT,CACA,CAAEjB,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAemB,EAAKpC,CAAoB,EAC7C,GAAI,CAEF,GAAM,WAAEO,CAAS,SAAEF,CAAO,QAAEY,EAAS,IAATA,IAAiB,CAAE,CADlC,EACqCoB,IAAAA,EADvBN,IAAI,CAAZ/B,EAInB,GAAI,CAACO,GAAa,CAACF,EACjB,GADGE,EAAcF,EAAS,EACnByB,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTE,KAAAA,CAAO,uCACT,CACA,CAAEjB,MAAAA,CAAQ,GAAI,GAgBlB,GAAIqB,CAXuB,MAAM1B,EAAAA,EAAAA,CAC9BE,MAAM,GACNa,IAAI,CAACjB,EAAAA,gBAAAA,CAAAA,CACLmB,KAAK,CACJrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAAA,gBAAAA,CAAiBH,SAAS,CAAEA,GAC/BE,CAAAA,EAAAA,EAAAA,CAD+BF,CAAAA,CAC/BE,CAAGC,EAAAA,gBAAgBA,CAACL,OAAO,CAAEA,KAGhCkC,EAHgClC,CAAAA,CAAAA,CAAAA,CAG1B,IAEcmC,MAAM,CAAG,EAC9B,CADiC,MAC1BV,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTE,KAAAA,CAAO,4CACT,CACA,CAAEjB,MAAAA,CAAQ,GAAI,GAKlB,IAAMwB,EAAU,KAAVA,CAAgB7B,EAAAA,EAAAA,CACnBE,MAAM,GACNa,IAAI,CAACN,EAAAA,KAAAA,CAAAA,CACLQ,KAAK,CAACrB,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAIC,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACY,EAAAA,KAAAA,CAAMN,EAAE,CAAER,GAAYE,CAAAA,EAAAA,EAAAA,CAAZF,CAAAA,CAAYE,CAAGY,EAAAA,KAAKA,CAACqB,IAAI,CAAE,aAClDH,KAAK,CAAC,GAET,GAAuB,GAAG,CAAtBE,EAAQD,KAARC,CAAc,CAChB,OAAOX,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTE,KAAAA,CAAO,6CACT,CACA,CAAEjB,MAAAA,CAAQ,GAAI,GAKlB,IAAM0B,EAAc,MAAM/B,EAAAA,EAAAA,CACvBE,MAAM,GACNa,IAAI,CAACD,EAAAA,OAAAA,CAAAA,CACLG,KAAK,CAACpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACiB,EAAAA,OAAAA,CAAQX,EAAE,CAAEV,IACrBkC,GADqBlC,CAAAA,CAAAA,CACf,GAET,GAA2B,GAAG,CAA1BsC,EAAYH,MAAM,CACpB,EADEG,KACKb,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,CAAS,GACTE,KAAAA,CAAO,kBACT,CACA,CAAEjB,MAAAA,CAAQ,GAAI,GAKlB,IAAM2B,EAAgB,MAAMhC,EAAAA,EAAAA,CACzBiC,MAAM,CAACnC,EAAAA,gBAAAA,CAAAA,CACPoC,MAAM,CAAC,CACNvC,SAAAA,WACAF,OAAAA,EACAY,EACAD,IADAC,MACAD,CAAY,IAAI+B,IAAAA,CAChB7B,CADgB6B,QAChB7B,CAAW,IAAI6B,IAAAA,CACf5B,CADe4B,QACf5B,CAAW,IAAI4B,IAAAA,GAEhBC,SAAS,GAEZ,OAAOlB,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CACEC,OAAAA,CAAS,GACTC,IAAAA,CAAMW,CAAa,CAAC,EAAE,CACtBK,OAAAA,CAAS,gCACX,CACA,CAAEhC,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOiB,EAAO,CAEd,EAFOA,KACPC,OAAAA,CAAQD,KAAK,CAAC,mCAAoCA,GAC3CJ,EAAAA,CAD2CI,WAC3CJ,CAAaC,IAAI,CACtB,CACEC,OAAAA,EAAS,EACTE,KAAAA,CAAO,oCACT,CACA,CAAEjB,MAAAA,CAAQ,GAAI,EAElB,CACF,CC5KA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGa,wBAAwB,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAAE,OAAxB,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAC3B,EAGJ,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,wBAAwB,SAC5C,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAeiC,EAA4B,GAAH,EAAQ,EAEnD,EAAO,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,oCACA,kCACA,iBACA,4CACA,CAAK,CACL,sJACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/class-enrollments/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?f7f9", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { classEnrollments, users, classes } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\n\r\n// GET - Get all class enrollments or filter by classId/studentId\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const { searchParams } = new URL(request.url);\r\n    const classId = searchParams.get('classId');\r\n    const studentId = searchParams.get('studentId');\r\n\r\n    // Build where conditions\r\n    let whereConditions;\r\n    if (classId && studentId) {\r\n      whereConditions = and(\r\n        eq(classEnrollments.classId, parseInt(classId)),\r\n        eq(classEnrollments.studentId, parseInt(studentId))\r\n      );\r\n    } else if (classId) {\r\n      whereConditions = eq(classEnrollments.classId, parseInt(classId));\r\n    } else if (studentId) {\r\n      whereConditions = eq(classEnrollments.studentId, parseInt(studentId));\r\n    }\r\n\r\n    // Execute query with or without where clause\r\n    const enrollments = whereConditions\r\n      ? await db\r\n          .select({\r\n            id: classEnrollments.id,\r\n            studentId: classEnrollments.studentId,\r\n            classId: classEnrollments.classId,\r\n            enrolledAt: classEnrollments.enrolledAt,\r\n            status: classEnrollments.status,\r\n            createdAt: classEnrollments.createdAt,\r\n            updatedAt: classEnrollments.updatedAt,\r\n            studentName: users.name,\r\n            studentEmail: users.email,\r\n            className: classes.name\r\n          })\r\n          .from(classEnrollments)\r\n          .leftJoin(users, eq(classEnrollments.studentId, users.id))\r\n          .leftJoin(classes, eq(classEnrollments.classId, classes.id))\r\n          .where(whereConditions)\r\n      : await db\r\n          .select({\r\n            id: classEnrollments.id,\r\n            studentId: classEnrollments.studentId,\r\n            classId: classEnrollments.classId,\r\n            enrolledAt: classEnrollments.enrolledAt,\r\n            status: classEnrollments.status,\r\n            createdAt: classEnrollments.createdAt,\r\n            updatedAt: classEnrollments.updatedAt,\r\n            studentName: users.name,\r\n            studentEmail: users.email,\r\n            className: classes.name\r\n          })\r\n          .from(classEnrollments)\r\n          .leftJoin(users, eq(classEnrollments.studentId, users.id))\r\n          .leftJoin(classes, eq(classEnrollments.classId, classes.id));\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: enrollments\r\n    });\r\n  } catch (error) {\r\n    console.error('Error fetching class enrollments:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to fetch class enrollments'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST - Create new class enrollment\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const { studentId, classId, status = 'active' } = body;\r\n\r\n    // Validate required fields\r\n    if (!studentId || !classId) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Student ID and Class ID are required'\r\n        },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Check if enrollment already exists\r\n    const existingEnrollment = await db\r\n      .select()\r\n      .from(classEnrollments)\r\n      .where(\r\n        and(\r\n          eq(classEnrollments.studentId, studentId),\r\n          eq(classEnrollments.classId, classId)\r\n        )\r\n      )\r\n      .limit(1);\r\n\r\n    if (existingEnrollment.length > 0) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Student is already enrolled in this class'\r\n        },\r\n        { status: 409 }\r\n      );\r\n    }\r\n\r\n    // Verify student exists and has student role\r\n    const student = await db\r\n      .select()\r\n      .from(users)\r\n      .where(and(eq(users.id, studentId), eq(users.role, 'student')))\r\n      .limit(1);\r\n\r\n    if (student.length === 0) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Student not found or user is not a student'\r\n        },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Verify class exists\r\n    const classExists = await db\r\n      .select()\r\n      .from(classes)\r\n      .where(eq(classes.id, classId))\r\n      .limit(1);\r\n\r\n    if (classExists.length === 0) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          error: 'Class not found'\r\n        },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Create enrollment\r\n    const newEnrollment = await db\r\n      .insert(classEnrollments)\r\n      .values({\r\n        studentId,\r\n        classId,\r\n        status,\r\n        enrolledAt: new Date(),\r\n        createdAt: new Date(),\r\n        updatedAt: new Date()\r\n      })\r\n      .returning();\r\n\r\n    return NextResponse.json(\r\n      {\r\n        success: true,\r\n        data: newEnrollment[0],\r\n        message: 'Student enrolled successfully'\r\n      },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating class enrollment:', error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: 'Failed to create class enrollment'\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/class-enrollments',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\class-enrollments\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/class-enrollments/route\",\n        pathname: \"/api/class-enrollments\",\n        filename: \"route\",\n        bundlePath: \"app/api/class-enrollments/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\class-enrollments\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "whereConditions", "searchParams", "URL", "url", "classId", "get", "studentId", "and", "eq", "classEnrollments", "parseInt", "db", "enrollments", "select", "id", "enrolledAt", "status", "createdAt", "updatedAt", "studentName", "users", "name", "studentEmail", "email", "className", "classes", "from", "leftJoin", "where", "NextResponse", "json", "success", "data", "error", "console", "POST", "body", "existingEnrollment", "limit", "length", "student", "role", "classExists", "newEnrollment", "insert", "values", "Date", "returning", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}