{"version": 3, "file": "8634.js", "mappings": "+hBAKA,SAASA,EAAgB,eACvBC,EAAgB,CAAC,CACjB,GAAGC,EACoD,EACvD,MAAO,UAACC,EAAAA,EAAyB,EAACC,YAAU,mBAAmBH,cAAeA,EAAgB,GAAGC,CAAK,CAAEG,sBAAoB,4BAA4BC,wBAAsB,kBAAkBC,0BAAwB,eAC1N,CACA,SAASC,EAAQ,CACf,GAAGN,EACgD,EACnD,MAAO,UAACF,EAAAA,CAAgBK,sBAAoB,kBAAkBC,wBAAsB,UAAUC,0BAAwB,uBAClH,UAACJ,EAAAA,EAAqB,EAACC,YAAU,UAAW,GAAGF,CAAK,CAAEG,sBAAoB,wBAAwBE,0BAAwB,iBAEhI,CACA,SAASE,EAAe,CACtB,GAAGP,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAwB,EAACC,YAAU,kBAAmB,GAAGF,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,eACxL,CACA,SAASG,EAAe,WACtBC,CAAS,CACTC,aAAa,CAAC,UACdC,CAAQ,CACR,GAAGX,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAuB,EAACE,sBAAoB,0BAA0BC,wBAAsB,iBAAiBC,0BAAwB,uBACzI,WAACJ,EAAAA,EAAwB,EAACC,YAAU,kBAAkBQ,WAAYA,EAAYD,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yaAA0aH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,2BAA2BE,0BAAwB,wBACjmBM,EACD,UAACV,EAAAA,EAAsB,EAACQ,UAAU,+FAA+FN,sBAAoB,yBAAyBE,0BAAwB,oBAG9M,uIC5BO,SAASQ,IACd,IAAMC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,MACJC,CAAI,SACJC,CAAO,CACR,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,UACNF,EAGE,EAHH,CAGG,CAHI,CAGJ,MAACG,EAAAA,EAAYA,CAAAA,CAAChB,sBAAoB,eAAeC,wBAAsB,UAAUC,0BAAwB,yBAC5G,UAACe,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,IAAClB,sBAAoB,sBAAsBE,0BAAwB,wBAC7F,UAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QAAQd,UAAU,+CAA+CN,sBAAoB,SAASE,0BAAwB,wBACpI,UAACmB,EAAAA,CAAiBA,CAAAA,CAACrB,sBAAoB,oBAAoBE,0BAAwB,qBAGvF,WAACoB,EAAAA,EAAmBA,CAAAA,CAAChB,UAAU,OAAOiB,MAAM,MAAMhB,WAAY,GAAIiB,UAAU,IAACxB,sBAAoB,sBAAsBE,0BAAwB,yBAC7I,UAACuB,EAAAA,EAAiBA,CAAAA,CAACnB,UAAU,cAAcN,sBAAoB,oBAAoBE,0BAAwB,wBACzG,WAACwB,MAAAA,CAAIpB,UAAU,oCACb,UAACqB,IAAAA,CAAErB,UAAU,4CAAoCO,EAAKe,IAAI,GAC1D,UAACD,IAAAA,CAAErB,UAAU,sDACVO,EAAKgB,KAAK,QAIjB,UAACC,EAAAA,EAAqBA,CAAAA,CAAC9B,sBAAoB,wBAAwBE,0BAAwB,iBAC3F,WAAC6B,EAAAA,CAAiBA,CAAAA,CAAC/B,sBAAoB,oBAAoBE,0BAAwB,yBACjF,UAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAAOuB,IAAI,CAAC,sBAAuBlC,sBAAoB,mBAAmBE,0BAAwB,wBAAe,YAGlJ,UAAC8B,EAAAA,EAAgBA,CAAAA,CAAChC,sBAAoB,mBAAmBE,0BAAwB,wBAAe,eAElG,UAAC4B,EAAAA,EAAqBA,CAAAA,CAAC9B,sBAAoB,wBAAwBE,0BAAwB,iBAC3F,UAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAASnB,EAASd,sBAAoB,mBAAmBE,0BAAwB,wBAAe,mBAzB/G,IA4BX,iDC9Be,SAASiC,IACtB,IAAMxB,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACxB,MAAO,WAACwB,SAAAA,CAAO9B,UAAU,qJAAqJL,wBAAsB,SAASC,0BAAwB,uBACjO,UAACwB,MAAAA,CAAIpB,UAAU,wCACb,UAAC+B,EAAAA,EAAcA,CAAAA,CAAC/B,UAAU,QAAQN,sBAAoB,iBAAiBE,0BAAwB,iBAGjG,WAACwB,MAAAA,CAAIpB,UAAU,yCACb,WAACU,EAAAA,EAAYA,CAAAA,CAAChB,sBAAoB,eAAeE,0BAAwB,uBACvE,UAACe,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,IAAClB,sBAAoB,sBAAsBE,0BAAwB,sBAC7F,WAACiB,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QAAQkB,KAAK,OAAOhC,UAAU,+CAA+CN,sBAAoB,SAASE,0BAAwB,uBAChJ,UAACqC,EAAAA,CAAIA,CAAAA,CAACjC,UAAU,UAAUN,sBAAoB,OAAOE,0BAAwB,eAC7E,UAACsC,OAAAA,CAAKlC,UAAU,mBAAU,sBAG9B,WAACgB,EAAAA,EAAmBA,CAAAA,CAAChB,UAAU,OAAOiB,MAAM,MAAMhB,WAAY,GAAIP,sBAAoB,sBAAsBE,0BAAwB,uBAClI,UAACuB,EAAAA,EAAiBA,CAAAA,CAACzB,sBAAoB,oBAAoBE,0BAAwB,sBAAa,uBAChG,UAAC4B,EAAAA,EAAqBA,CAAAA,CAAC9B,sBAAoB,wBAAwBE,0BAAwB,eAC3F,WAAC6B,EAAAA,CAAiBA,CAAAA,CAAC/B,sBAAoB,oBAAoBE,0BAAwB,uBACjF,WAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAAOuB,IAAI,CAAC,sBAAuBlC,sBAAoB,mBAAmBE,0BAAwB,uBACjI,UAACqC,EAAAA,CAAIA,CAAAA,CAACjC,UAAU,eAAeN,sBAAoB,OAAOE,0BAAwB,eAAe,kBAGnG,WAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAAOuB,IAAI,CAAC,uBAAwBlC,sBAAoB,mBAAmBE,0BAAwB,uBAClI,UAACuC,EAAAA,CAAQA,CAAAA,CAACnC,UAAU,eAAeN,sBAAoB,WAAWE,0BAAwB,eAAe,cAG3G,WAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAAOuB,IAAI,CAAC,mBAAoBlC,sBAAoB,mBAAmBE,0BAAwB,uBAC9H,UAACwC,EAAAA,CAAUA,CAAAA,CAACpC,UAAU,eAAeN,sBAAoB,aAAaE,0BAAwB,eAAe,oBAG/G,WAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAAOuB,IAAI,CAAC,4BAA6BlC,sBAAoB,mBAAmBE,0BAAwB,uBACvI,UAACyC,EAAAA,CAAQA,CAAAA,CAACrC,UAAU,eAAeN,sBAAoB,WAAWE,0BAAwB,eAAe,4BAMjH,UAACQ,EAAOA,CAACV,IAADU,kBAAqB,UAAUR,0BAAwB,oBAGvE,g6VCnDA,IAAM0C,EAAaC,EAAAA,UAAgB,CAAC,CAAC,QACnCC,CAAM,QACNC,CAAM,CACNC,qBAAmB,CAKpB,CAAEC,KACD,IAAMC,EAAYL,EAAAA,OAAa,CAAC,KAC9B,GAAI,CAACG,EAAqB,OAAOF,EAAOI,SAAS,CACjD,IAAMC,EAAQL,EAAOI,SAAS,CAACE,SAAS,CAACC,GAAYA,EAASC,EAAE,GAAKN,GACrE,OAAOF,EAAOI,SAAS,CAACK,KAAK,CAACJ,EAAQ,EACxC,EAAG,CAACL,EAAOI,SAAS,CAAEF,EAAoB,EAC1C,MAAO,WAACtB,MAAAA,CAAIuB,IAAKA,EAAK3C,UAAW,CAAC,wEAAwE,CAAC,WACpGyC,GAAU,UAACrB,MAAAA,CAAI4B,GAAG,mBAAmBhD,UAAU,oEAChD,WAACoB,MAAAA,CAAIpB,UAAU,kDACZwC,EAAOU,IAAI,EAAIV,EAAOU,IAAI,CAC3B,WAAC9B,MAAAA,CAAIpB,UAAU,0BACb,WAACoB,MAAAA,WACEwB,EAAUO,MAAM,CAAG,GAAKP,EAAUQ,GAAG,CAACL,GAAY,WAACR,EAAAA,QAAc,YAC5D,UAACL,OAAAA,CAAKlC,UAAU,sCACb+C,EAASzB,IAAI,GAEhB,UAACY,OAAAA,CAAKlC,UAAU,gBAAO,QAJ2C+C,EAASC,EAAE,GAMnF,UAACd,OAAAA,UAAMM,EAAOlB,IAAI,MAEnBkB,EAAOa,QAAQ,EAAI,UAACnB,OAAAA,CAAKlC,UAAU,yCAC/BwC,EAAOa,QAAQ,SAIvBb,EAAOc,QAAQ,EAAEH,OAAS,UAAC/B,MAAAA,CAAIpB,UAAU,kDACrCwC,EAAOc,QAAQ,CAACF,GAAG,CAAC,CAACG,EAAIC,IAAM,UAACC,MAAAA,CAAiBzD,UAAU,iGACvDuD,GADqCA,EAAKC,MAGxC,OAEnB,GCvCe,SAASE,IACtB,GAAM,SACJC,CAAO,cACPC,CAAY,CACb,CAAGC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,GACd,MAAO,UAACC,EAAAA,WAAWA,CAAAA,CAACC,MAAOJ,EAASK,SAAU,CAAC,MAC7CC,CAAI,QACJxB,CAAM,CACP,GAAqB,UAAhB,OAAOwB,EAAoB,UAAC7C,MAAAA,CAAIpB,UAAU,0EACrCiE,IACM,UAAC3B,EAAUA,CAACE,OAAQyB,CAAT3B,CAAeG,OAAQA,EAAQC,oBAAqBkB,GAAgB,KAAQlE,sBAAoB,cAAcC,wBAAsB,gBAAgBC,0BAAwB,qBAC1M,CD6BA0C,EAAW4B,WAAW,CAAG,gCEbzB,MA3B0B,KACxB,GAAM,CACJC,OAAK,KAyBMC,KAxBXC,CAAQ,CACT,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,EAuBkBF,CAF9BG,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CAACC,CAjBE,CACnBxB,GAAI,cACJ1B,KAAM,eACNgC,SAAU,CAAC,IAAK,IAAI,CACpBmB,QAAS,QACTC,QARkB,CAQTC,IAPTN,EAAmB,UAAVF,EAAoB,OAAS,QACxC,CAOA,EAAG,CACDnB,GAAI,gBACJ1B,KAAM,kBACNmD,QAAS,QACTC,QAAS,IAAML,EAAS,QAC1B,EAAG,CACDrB,GAAI,eACJ1B,KAAM,iBACNmD,QAAS,QACTC,QAAS,IAAML,EAAS,OAC1B,EAAE,CAC8B,CAACF,EAAM,CACzC,ECpBe,SAASS,EAAK,UAC3B1E,CAAQ,CAGT,EACC,IAAMG,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAGlBuE,EAAUC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAEtB,IAAMC,EAAa,IACjB1E,EAAOuB,IAAI,CAACoD,EACd,EACA,OAAOC,EAAAA,EAAQA,CAACC,OAAO,CAACC,IAEtB,IAAMC,EAA6B,MAAhBD,EAAQH,GAAG,CAAW,CACvChC,GAAI,GAAGmC,EAAQE,KAAK,CAACC,WAAW,GAAG,MAAM,CAAC,CAC1ChE,KAAM6D,EAAQE,KAAK,CACnB/B,SAAU6B,EAAQ7B,QAAQ,CAC1BiC,SAAUJ,EAAQE,KAAK,CAACC,WAAW,GACnCb,QAAS,aACTpB,SAAU,CAAC,MAAM,EAAE8B,EAAQE,KAAK,EAAE,CAClCX,QAAS,IAAMK,EAAWI,EAAQH,GAAG,CACvC,EAAI,KAGEQ,EAAeL,EAAQpB,KAAK,EAAEX,IAAIqC,GAAc,EACpDzC,GAAI,GAAGyC,CAD6C,CACnCJ,KAAK,CAACC,WAAW,GAAG,MAAM,CAAC,CAC5ChE,KAAMmE,EAAUJ,KAAK,CACrB/B,SAAUmC,EAAUnC,QAAQ,CAC5BiC,SAAUE,EAAUJ,KAAK,CAACC,WAAW,GACrCb,QAASU,EAAQE,KAAK,CACtBhC,SAAU,CAAC,MAAM,EAAEoC,EAAUJ,KAAK,EAAE,CACpCX,QAAS,IAAMK,EAAWU,EAAUT,GAAG,CACzC,KAAO,EAAE,CAGT,OAAOI,EAAa,CAACA,KAAeI,EAAa,CAAGA,CACtD,EACF,EAAG,CAACnF,EAAO,EACX,MAAO,UAACqF,EAAAA,YAAYA,CAAAA,CAACb,QAASA,EAASnF,sBAAoB,eAAeC,wBAAsB,OAAOC,0BAAwB,qBAC3H,UAAC+F,EAAAA,CAAcjG,sBAAoB,gBAAgBE,0BAAwB,qBAAaM,KAE9F,CACA,IAAMyF,EAAgB,CAAC,UACrBzF,CAAQ,CAGT,IACCkE,IACO,eADUA,MACV,YACH,UAACwB,EAAAA,UAAUA,CAAAA,CAAClG,sBAAoB,aAAaE,0BAAwB,qBACnE,UAACiG,EAAAA,cAAcA,CAAAA,CAAC7F,UAAU,+DAA+DN,sBAAoB,iBAAiBE,0BAAwB,qBACpJ,WAACkG,EAAAA,YAAYA,CAAAA,CAAC9F,UAAU,iIAAiIN,sBAAoB,eAAeE,0BAAwB,sBAClN,UAACwB,MAAAA,CAAIpB,UAAU,4DACb,UAAC+F,EAAAA,UAAUA,CAAAA,CAAC/F,UAAU,oHAAoHN,sBAAoB,aAAaE,0BAAwB,gBAErM,UAACwB,MAAAA,CAAIpB,UAAU,yBACb,UAAC0D,EAAaA,CAAChE,UAADgE,YAAqB,gBAAgB9D,0BAAwB,uBAKlFM,wECpEP,SAAS8F,EAAY,CACnB,GAAGzG,EACoD,EACvD,MAAO,UAAC0G,EAAAA,EAAyB,EAACxG,YAAU,cAAe,GAAGF,CAAK,CAAEG,sBAAoB,4BAA4BC,wBAAsB,cAAcC,0BAAwB,mBACnL,CACA,SAASsG,EAAmB,CAC1B,GAAG3G,EACkE,EACrE,MAAO,UAAC0G,EAAAA,EAAuC,EAACxG,YAAU,sBAAuB,GAAGF,CAAK,CAAEG,sBAAoB,0CAA0CC,wBAAsB,qBAAqBC,0BAAwB,mBAC9N,CACA,SAASuG,EAAmB,CAC1B,GAAG5G,EACkE,EACrE,MAAO,UAAC0G,EAAAA,EAAuC,EAACxG,YAAU,sBAAuB,GAAGF,CAAK,CAAEG,sBAAoB,0CAA0CC,wBAAsB,qBAAqBC,0BAAwB,mBAC9N,sCCdO,IAAMwG,EAAgC,CAC3C,CACEf,MAAO,YACPL,IAAK,mBACL9B,KAAM,YACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAEX,CACEsB,MAAO,eACPL,IAAK,gCACL9B,KAAM,WACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,CACL,CACEsB,MAAO,mBACPL,IAAK,gCACL9B,KAAM,UACR,EACA,CACEmC,MAAO,kBACPL,IAAK,oCACL9B,KAAM,MACR,EAEJ,EACA,CACEmC,MAAO,QACPL,IAAK,yBACL9B,KAAM,QACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,CACL,CACEsB,MAAO,YACPL,IAAK,yBACL9B,KAAM,OACR,EACA,CACEmC,MAAO,WACPL,IAAK,6BACL9B,KAAM,UACR,EACD,EAEH,CACEmC,MAAO,gBACPL,IAAK,iCACL9B,KAAM,aACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAEX,CACEsB,MAAO,YACPL,IAAK,6BACL9B,KAAM,WACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAEX,CACEsB,MAAO,UACPL,IAAK,IACL9B,KAAM,UACNmD,SAAU,GACVtC,MAAO,CACL,CACEsB,MAAO,UACPL,IAAK,qBACL9B,KAAM,UACNI,SAAU,CAAC,IAAK,IAAI,EAEtB,CACE+B,MAAO,WACPL,IAAK,sBACL9B,KAAM,UACR,EACD,EAEJ,CAAC,EAGwC,CACxC,CACEmC,MAAO,YACPL,IAAK,qBACL9B,KAAM,YACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAuBX,CACEsB,MAAO,UACPL,IAAK,6BACL9B,KAAM,WACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,CACL,CACEsB,MAAO,aACPL,IAAK,6BACL9B,KAAM,UACR,EACA,CACEmC,MAAO,gBACPL,IAAK,iCACL9B,KAAM,MACR,EACD,EAqEJ,CAAC,EAGwC,CACxC,CACEmC,MAAO,YACPL,IAAK,qBACL9B,KAAM,YACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAEX,CACEsB,MAAO,aACPL,IAAK,6BACL9B,KAAM,WACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EACT,EACA,CACEsB,MAAO,WACPL,IAAK,8BACL9B,KAAM,aACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAEX,CACEsB,MAAO,eACPL,IAAK,kCACL9B,KAAM,QACNmD,UAAU,EACV/C,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,EAEX,CACEsB,MAAO,UACPL,IAAK,IACL9B,KAAM,UACNmD,UAAU,EACVtC,MAAO,CACL,CACEsB,MAAO,UACPL,IAAK,qBACL9B,KAAM,UACNI,SAAU,CAAC,IAAK,IAAI,EAEtB,CACE+B,MAAO,WACPL,IAAK,sBACL9B,KAAM,UACR,EAEJ,EACD,CAAC,EAGgC,IAChC,OAAQoD,GACN,IAAK,cACH,OAAOF,CACT,KAAK,UACH,OAAOG,CACT,KAAK,UACH,OAAOC,CACT,SACE,MAAO,EACX,CACF,EAAE,0jBE9QK,IAAMC,GAAQ,CACnBC,UAAWC,EAAAA,CAAeA,CAC1BC,KAAMC,EAAAA,CAAOA,CACbC,MAAOC,EAAAA,CAAKA,CACZC,MAAOC,EAAAA,CAACA,CACRC,QAASC,EAAAA,CAAWA,CACpBC,QAASC,EAAAA,CAAOA,CAChBC,OAAQC,EAAAA,CAAMA,CACdC,YAAaC,EAAAA,CAAWA,CACxBC,aAAcC,EAAAA,CAAYA,CAC1BC,MAAOC,EAAAA,CAAMA,CACbC,SAAUC,EAAAA,CAAKA,CACfC,KAAM3F,EAAAA,CAAQA,CACd4F,KAAMC,EAAAA,CAAIA,CACVC,QAASC,EAAAA,CAAOA,CAChBC,MAAOC,EAAAA,CAAUA,CACjBC,MAAOC,EAAAA,CAAKA,CACZC,SAAUtG,EAAAA,CAAQA,CAClBuG,QAASC,EAAAA,CAAUA,CACnBC,SAAUC,EAAAA,CAAcA,CACxBC,IAAKC,EAAAA,CAAIA,CACTC,QAASC,EAAAA,CAAaA,CACtB1I,KAAM0B,EAAAA,CAAIA,CACViH,WAAYC,EAAAA,CAAUA,CACtBC,KAAMhH,EAAAA,CAAUA,CAChBiH,MAAOC,EAAAA,CAAKA,CACZC,IAAKC,EAAAA,CAAGA,CACRC,KAAMC,EAAAA,CAAIA,CACVC,OAAQC,EAAAA,CAAMA,CACdC,OAAQC,EAAAA,CAAMA,CACdC,QAASC,EAAAA,CAAOA,CAChBC,MAAOC,GAAAA,CAAKA,CAEZC,SAAUC,EAAAA,CAASA,CACnBC,MAAOC,GAAAA,CAAKA,CACZC,SAAUC,GAAAA,CAAQA,CAClBC,UAAWC,GAAAA,CAASA,CACpBC,WAAYhC,EAAAA,CAAUA,CACtBiC,SAAUC,GAAAA,CAASA,CACnBC,SAAUC,GAAAA,CAAQA,CAClBC,KAAMC,GAAAA,CAAIA,CACVC,IAAKC,GAAAA,CAAGA,CACRC,WAAYC,GAAAA,CAAUA,CACtBC,MAAOC,GAAAA,CAAKA,CACZC,KAAMzC,EAAAA,CAAIA,CAEV0C,OAAQV,GAAAA,CAAQA,CAChBW,YAAaH,GAAAA,CAAKA,CAClBI,WAAYnB,GAAAA,CAAQA,CACpBoB,WAAYC,GAAAA,CAAUA,CACtBC,cAAeC,GAAAA,CAAiBA,EAChC,gBChDF,IAAMC,GAAiB,IACrB,OAAQ1F,GACN,IAAK,UACH,MAAO,CACL2F,MAAO,UACP/I,KAAMgJ,GAAAA,CAAaA,CACnBC,QAAS,aACX,CACF,KAAK,UACH,MAAO,CACLF,MAAO,UACP/I,KAAM6H,GAAAA,CAAQA,CACdoB,QAAS,cACX,CACF,KAAK,cACH,MAAO,CACLF,MAAO,sBACP/I,KAAMkH,EAAAA,CAASA,CACf+B,QAAS,eACX,CACF,SACE,MAAO,CACLF,MAAO,OACP/I,KAAM6H,GAAAA,CAAQA,CACdoB,QAAS,aACX,CACJ,CACF,EACO,SAASC,KACd,GAAM,CAAC7L,EAAM8L,EAAQ,CAAG9J,EAAAA,QAAc,CAAC+J,EAAAA,EAAWA,CAACC,OAAO,IAK1D,GAJAhK,EAAAA,SAAe,CAAC,KAEd8J,EADoBC,EAAAA,EAAWA,CAACC,CACxBC,MAD+B,GAEzC,EAAG,EAAE,EACD,CAACjM,EACH,IADS,GACF,KAET,IAAMkM,EAAWT,GAAezL,EAAK+F,IAAI,EACnCoG,EAAOD,EAASvJ,IAAI,CAC1B,MAAO,UAACyJ,EAAAA,EAAWA,CAAAA,CAACjN,sBAAoB,cAAcC,wBAAsB,gBAAgBC,0BAAwB,8BAChH,UAACgN,EAAAA,EAAeA,CAAAA,CAAClN,sBAAoB,kBAAkBE,0BAAwB,8BAC7E,WAACiN,EAAAA,EAAiBA,CAAAA,CAAC7K,KAAK,KAAKhC,UAAU,sCAAsCN,sBAAoB,oBAAoBE,0BAAwB,+BAC3I,UAACwB,MAAAA,CAAIpB,UAAW,GAAGyM,EAASN,OAAO,CAAC,4EAA4E,CAAC,UAC/G,UAACO,EAAAA,CAAK1M,UAAU,SAASN,sBAAoB,OAAOE,0BAAwB,yBAE9E,WAACwB,MAAAA,CAAIpB,UAAU,+CACb,UAACkC,OAAAA,CAAKlC,UAAU,yBAAiBO,EAAKe,IAAI,GAC1C,WAACY,OAAAA,CAAKlC,UAAU,0CAAgC,iBAAeyM,EAASR,KAAK,aAKzF,CCnCe,SAASa,KACtB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,GACtB,QACJC,CAAM,CACP,CHzBI,SAASC,EACd,EGwBiBA,CHxBX,CAACD,EAAQE,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAcrC,MAAO,QAAEH,CAAO,CAClB,IGUQ5M,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,OACJ+M,CAAK,CACN,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAUA,GACR,CAACrI,EAAUsI,EAAY,CAAGhL,EAAAA,QAAc,CAAY,EAAE,EAC5DA,EAAAA,SAAe,CAAC,KAEd,IAAMhC,EAAO+L,EAAAA,EAAWA,CAACC,OAAO,GAC5BhM,EAIgB,IAJV,OAIqB,CAAzBA,EAAK+F,IAAI,CAEXiH,EADyBC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAACT,CACzBU,GAGZF,EADqBG,EAAmBnN,EAAK+F,CAHG,GAGC,EACrCqH,CAKdJ,EADyBC,CAAAA,EAAAA,EAAAA,CARuC,CAQvCA,CAAWA,CAACT,CACzBU,EAEhB,EAAG,CAACV,EAAS,EACb,IAAMa,EAAkB3I,EAAS9B,MAAM,CAAG,EAAI8B,EAAWuI,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAACT,GACrE,MAAO,WAACc,EAAAA,EAAOA,CAAAA,CAACC,YAAY,OAAOpO,sBAAoB,UAAUC,wBAAsB,aAAaC,0BAAwB,4BACxH,WAACmO,EAAAA,EAAaA,CAAAA,CAAC/N,UAAU,MAAMN,sBAAoB,gBAAgBE,0BAAwB,4BACzF,UAACwB,MAAAA,CAAIpB,UAAU,4CACb,UAACgO,IAAIA,CAACC,KAAK,IAAIvO,CAAVsO,qBAA8B,OAAOpO,0BAAwB,2BAChE,UAAC4I,EAAAA,OAAKA,CAAAA,CAAC0F,IAAI,uBAAuBC,IAAI,WAAWC,MAAiB,cAAVf,EAAwB,GAAK,IAAKgB,OAAkB,cAAVhB,EAAwB,GAAK,GAAIrN,UAAU,6EAA6EsO,QAAQ,IAAC5O,sBAAoB,QAAQE,0BAAwB,wBAG3R,UAACwB,MAAAA,CAAIpB,UAAW,CAAC,KAAK,EAAY,cAAVqN,EAAwB,SAAW,SAAS,UAClE,UAACjB,GAAaA,CAAC1M,SAAD0M,aAAqB,gBAAgBxM,0BAAwB,yBAG/E,UAAC2O,EAAAA,EAAcA,CAAAA,CAACvO,UAAU,oBAAoBN,sBAAoB,iBAAiBE,0BAAwB,2BACzG,WAAC4O,EAAAA,EAAYA,CAAAA,CAAC9O,sBAAoB,eAAeE,0BAAwB,4BACvE,UAAC6O,EAAAA,EAAiBA,CAAAA,CAAC/O,sBAAoB,oBAAoBE,0BAAwB,2BAAkB,aACrG,UAAC+M,EAAAA,EAAWA,CAAAA,CAAC3M,UAAU,QAAQN,sBAAoB,cAAcE,0BAAwB,2BACtFgO,EAAgBxK,GAAG,CAAC,IACrB,IAAMsJ,EAAOzI,EAAKf,IAAI,CAAGuD,EAAK,CAACxC,EAAKf,IAAI,CAAC,CAAGuD,GAAMG,EAADH,EAAK,CACtD,OAAOxC,GAAMF,OAASE,GAAMF,OAAOZ,OAAS,EAAI,UAAC6C,EAAWA,CAAkBpF,OAAO,CAAzBoF,CAAyB,EAAC0I,YAAazK,EAAKoC,QAAQ,CAAErG,UAAU,6BACtH,WAAC4M,EAAAA,EAAeA,CAAAA,WACd,UAAC1G,EAAkBA,CAACtF,OAAO,QAARsF,KACjB,WAAC2G,EAAAA,EAAiBA,CAAAA,CAAC8B,QAAS1K,EAAKoB,KAAK,CAAEgB,SAAUpC,EAAKF,KAAK,EAAE6K,KAAKC,GAAW9B,IAAa8B,EAAQ7J,GAAG,EAAGhF,UAAU,mDAChHiE,EAAKf,IAAI,EAAI,UAACwJ,EAAAA,CAAAA,GACf,UAACxK,OAAAA,UAAM+B,EAAKoB,KAAK,GACjB,UAACsC,EAAAA,CAAYA,CAAAA,CAAC3H,UAAU,iGAG5B,UAACmG,EAAkBA,UACjB,MADiBA,EACjB,EAAC2I,EAAAA,EAAcA,CAAAA,UACZ7K,EAAKF,KAAK,EAAEX,IAAKyL,GAAqB,UAACE,EAAAA,EAAkBA,CAAAA,UACtD,UAACC,EAAAA,EAAoBA,CAAAA,CAACpO,OAAO,IAACyF,SAAU0G,IAAa8B,EAAQ7J,GAAG,CAAEhF,UAAU,yCAC1E,UAACgO,IAAIA,CAACC,KAAMY,EAAQ7J,GAAfgJ,UACH,UAAC9L,OAAAA,UAAM2M,EAAQxJ,KAAK,QAHoCwJ,EAAQxJ,KAAK,WAXvBpB,EAAKoB,KAAK,EAqBvD,UAACuH,EAAAA,EAAeA,CAAAA,UAC/B,UAACC,EAAAA,EAAiBA,CAAAA,CAACjM,OAAO,IAAC+N,QAAS1K,EAAKoB,KAAK,CAAEgB,SAAU0G,IAAa9I,EAAKe,GAAG,CAAEhF,UAAU,kDACzF,WAACgO,IAAIA,CAACC,KAAMhK,EAAKe,GAAZgJ,WACH,UAACtB,EAAAA,CAAAA,GACD,UAACxK,OAAAA,UAAM+B,EAAKoB,KAAK,SAJgBpB,EAAKoB,KAAK,CAQvD,UAIJ,UAAC4J,EAAAA,EAAaA,CAAAA,CAACvP,sBAAoB,gBAAgBE,0BAAwB,2BACzE,UAAC+M,EAAAA,EAAWA,CAAAA,CAACjN,sBAAoB,cAAcE,0BAAwB,2BACrE,UAACgN,EAAAA,EAAeA,CAAAA,CAAClN,sBAAoB,kBAAkBE,0BAAwB,2BAC7E,WAACc,EAAAA,EAAYA,CAAAA,CAAChB,sBAAoB,eAAeE,0BAAwB,4BACvE,UAACe,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,IAAClB,sBAAoB,sBAAsBE,0BAAwB,2BAC7F,WAACiN,EAAAA,EAAiBA,CAAAA,CAAC7K,KAAK,KAAKhC,UAAU,8HAA8HN,sBAAoB,oBAAoBE,0BAAwB,4BACnO,UAACmB,EAAAA,CAAiBA,CAAAA,CAACf,UAAU,qBAAqBkP,QAAQ,IAACxP,sBAAoB,oBAAoBE,0BAAwB,oBAC3H,UAAC6G,GAAMiB,EAADjB,UAAa,EAACzG,UAAU,iBAAiBN,sBAAoB,qBAAqBE,0BAAwB,yBAGpH,WAACoB,EAAAA,EAAmBA,CAAAA,CAAChB,UAAU,8DAA8DmP,KAAK,SAASlO,MAAM,MAAMhB,WAAY,EAAGP,sBAAoB,sBAAsBE,0BAAwB,4BACtM,UAACuB,EAAAA,EAAiBA,CAAAA,CAACnB,UAAU,kBAAkBN,sBAAoB,oBAAoBE,0BAAwB,2BAC7G,UAACwB,MAAAA,CAAIpB,UAAU,uBACb,UAACe,EAAAA,CAAiBA,CAAAA,CAACf,UAAU,qBAAqBkP,QAAQ,IAACxP,sBAAoB,oBAAoBE,0BAAwB,wBAG/H,UAAC4B,EAAAA,EAAqBA,CAAAA,CAAC9B,sBAAoB,wBAAwBE,0BAAwB,oBAE3F,WAAC6B,EAAAA,CAAiBA,CAAAA,CAAC/B,sBAAoB,oBAAoBE,0BAAwB,4BACjF,WAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,IAAMtB,EAAOuB,IAAI,CAAC,sBAAuBlC,sBAAoB,mBAAmBE,0BAAwB,4BACjI,UAAC6G,GAAMlG,EAADkG,EAAK,EAACzG,UAAU,eAAeN,sBAAoB,aAAaE,0BAAwB,oBAAoB,aAGpH,WAAC8B,EAAAA,EAAgBA,CAAAA,CAAChC,sBAAoB,mBAAmBE,0BAAwB,4BAC/E,UAAC6G,GAAMiC,EAADjC,KAAQ,EAACzG,UAAU,eAAeN,sBAAoB,gBAAgBE,0BAAwB,oBAAoB,aAG1H,WAAC8B,EAAAA,EAAgBA,CAAAA,CAAChC,sBAAoB,mBAAmBE,0BAAwB,4BAC/E,UAAC6G,GAAM2C,EAAD3C,EAAK,EAACzG,UAAU,eAAeN,sBAAoB,aAAaE,0BAAwB,oBAAoB,sBAItH,UAAC4B,EAAAA,EAAqBA,CAAAA,CAAC9B,sBAAoB,wBAAwBE,0BAAwB,oBAC3F,WAAC8B,EAAAA,EAAgBA,CAAAA,CAACC,QAAS,KAC3B2K,EAAAA,EAAWA,CAAC8C,UAAU,GACtBC,OAAOC,QAAQ,CAACrB,IAAI,CAAG,eACzB,EAAGvO,sBAAoB,mBAAmBE,0BAAwB,4BAC9D,UAAC2P,EAAAA,CAAMA,CAAAA,CAACvP,UAAU,eAAeN,sBAAoB,SAASE,0BAAwB,oBACtF,UAACsC,OAAAA,UAAK,0BAOlB,UAACsN,EAAAA,EAAWA,CAAAA,CAAC9P,sBAAoB,cAAcE,0BAAwB,sBAE7E,CAnIQwK,EAAAA,CAER,CAAE,umCCfK,SAAS3J,IACd,GAAM,CAACF,EAAM8L,EAAQ,CAAGe,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAkB,MAC5C,CAACqC,EAASC,EAAW,CAAGtC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC/M,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAgBxB,MAAO,MAAEC,UAAMkP,EAASjP,QANR,KACd8L,EAAAA,EAAWA,CAAC8C,UAAU,GACtB/C,EAAQ,MACRhM,EAAOuB,IAAI,CAAC,gBACd,CAEgC,CAClC,2ECzBA,SAAS+N,EAAM,WACb3P,CAAS,MACT4P,CAAI,CACJ,GAAGrQ,EAC2B,EAC9B,MAAO,UAACsQ,QAAAA,CAAMD,KAAMA,EAAMnQ,YAAU,QAAQO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kcAAmc,gFAAiF,yGAA0GH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,QAAQC,0BAAwB,aACvwB,kICHA,SAASkQ,EAAO,WACd9P,CAAS,CACT,GAAGT,EAC+C,EAClD,MAAO,UAACwQ,EAAAA,EAAoB,EAACtQ,YAAU,SAASO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6DAA8DH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cACvP,CACA,SAASoQ,EAAY,CACnBhQ,WAAS,CACT,GAAGT,EACgD,EACnD,MAAO,UAACwQ,EAAAA,EAAqB,EAACtQ,YAAU,eAAeO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cACjO,CACA,SAASqQ,EAAe,CACtBjQ,WAAS,CACT,GAAGT,EACmD,EACtD,MAAO,UAACwQ,EAAAA,EAAwB,EAACtQ,YAAU,kBAAkBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mEAAoEH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,cACtR,2qBCrBA,SAASsQ,EAAS,WAChBlQ,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC6B,MAAAA,CAAI3B,YAAU,WAAWO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,WAAWC,0BAAwB,gBACvK,uDCHO,IAAM0M,EAAc,CACzBD,QAAS,IAIT,EAEAE,QAAS,IAKA,KAGT6C,WAAY,KAIZ,EAEAe,gBAAiB,IACkB,OAA1B7D,EAAYC,OAAO,GAG5B6D,QAAS,IACP,IAAM7P,EAAO+L,EAAYC,OAAO,GAChC,OAAOhM,GAAM+F,OAASA,CACxB,EAEA+J,aAAc,IACL/D,EAAY8D,OAAO,CAAC,eAG7BE,UAAW,IACFhE,EAAY8D,OAAO,CAAC,WAG7BG,UAAW,IACFjE,EAAY8D,OAAO,CAAC,UAE/B,EAAE,EAG6B,IAC7B,OAAQ7P,EAAK+F,IAAI,EACf,IAAK,cACH,MAAO,kBACT,KAAK,UACH,MAAO,oBACT,KAAK,UACH,MAAO,UACT,SACE,MAAO,YACX,CACF,EAGakK,EAAY,KACvB,IAAMjQ,EAAO+L,EAAYC,OAAO,UAChC,GAKS,CALL,EAAO,CAQb,EAAE,EAGyB,IACzB,IAAMhM,EAAOiQ,WACb,GAEIjQ,CAFA,CAEK+F,CAFE,GAEE,GAAKmK,EAOXlQ,EATW,IAUpB,EAAE,IARgC,mUExElC,SAASmQ,EAAM,CACb,GAAGnR,EAC8C,EACjD,MAAO,UAACoR,EAAAA,EAAmB,EAAClR,YAAU,QAAS,GAAGF,CAAK,CAAEG,sBAAoB,sBAAsBC,wBAAsB,QAAQC,0BAAwB,aAC3J,CAWA,SAASgR,EAAY,CACnB,GAAGrR,EACgD,EACnD,MAAO,UAACoR,EAAAA,EAAqB,EAAClR,YAAU,eAAgB,GAAGF,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,aAC5K,CACA,SAASiR,EAAa,WACpB7Q,CAAS,CACT,GAAGT,EACiD,EACpD,MAAO,UAACoR,EAAAA,EAAsB,EAAClR,YAAU,gBAAgBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,aACpW,CACA,SAASkR,EAAa,WACpB9Q,CAAS,UACTE,CAAQ,MACRiP,EAAO,OAAO,CACd,GAAG5P,EAGJ,EACC,MAAO,WAACqR,EAAAA,CAAYlR,sBAAoB,cAAcC,wBAAsB,eAAeC,0BAAwB,sBAC/G,UAACiR,EAAAA,CAAanR,sBAAoB,eAAeE,0BAAwB,cACzE,WAAC+Q,EAAAA,EAAsB,EAAClR,YAAU,gBAAgBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6MAAuN,UAATgP,GAAoB,mIAA6I,SAATA,GAAmB,gIAA0I,QAATA,GAAkB,2GAAqH,WAATA,GAAqB,oHAAqHnP,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,yBAAyBE,0BAAwB,sBAC35BM,EACD,WAACyQ,EAAAA,EAAoB,EAAC3Q,UAAU,6OAA6ON,sBAAoB,uBAAuBE,0BAAwB,sBAC9U,UAACmR,EAAAA,CAAKA,CAAAA,CAAC/Q,UAAU,SAASN,sBAAoB,QAAQE,0BAAwB,cAC9E,UAACsC,OAAAA,CAAKlC,UAAU,mBAAU,kBAIpC,CACA,SAASgR,EAAY,CACnBhR,WAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC6B,MAAAA,CAAI3B,YAAU,eAAeO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4BAA6BH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,aACrK,CAOA,SAASqR,EAAW,WAClBjR,CAAS,CACT,GAAGT,EAC+C,EAClD,MAAO,UAACoR,EAAAA,EAAoB,EAAClR,YAAU,cAAcO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,uBAAuBC,wBAAsB,aAAaC,0BAAwB,aACnO,CACA,SAASsR,EAAiB,WACxBlR,CAAS,CACT,GAAGT,EACqD,EACxD,MAAO,UAACoR,EAAAA,EAA0B,EAAClR,YAAU,oBAAoBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,6BAA6BC,wBAAsB,mBAAmBC,0BAAwB,aAC3P,wBC7CA,IAAMuR,EAAiB5O,EAAAA,aAAmB,CAA6B,MACvE,SAAS+K,IACP,IAAM8D,EAAU7O,EAAAA,UAAgB,CAAC4O,GACjC,GAAI,CAACC,EACH,MAAUC,CADE,KACI,qDAElB,OAAOD,CACT,CACA,SAASE,EAAgB,aACvB5C,GAAc,CAAI,CAClB6C,KAAMC,CAAQ,CACdC,aAAcC,CAAW,CACzB1R,WAAS,OACT2R,CAAK,CACLzR,UAAQ,CACR,GAAGX,EAKJ,EACC,IAAMqS,EFhDD,SAASC,EEgDcA,GF/CtB,CAACD,EAAUE,EAAY,CAAGvP,EAAAA,QAAc,MAAsBwP,GAUpE,OATAxP,EAAAA,SAAe,CAAC,KACd,IAAMyP,EAAM3C,OAAO4C,UAAU,CAAC,CAAC,YAAY,EAAEC,OACvCC,EAAW,KACfL,EAAYzC,IAFmD,EAAE,CAE9C+C,EAFiD,CAAC,OAExC,GAAGF,EAClC,EAGA,OAFAF,EAAIK,gBAAgB,CAAC,SAAUF,GAC/BL,EAAYzC,OAAO+C,UAAU,CATP,EASUF,GACzB,IAAMF,EAAIM,mBAAmB,CAAC,SAAUH,EACjD,EAAG,EAAE,EACE,CAAC,CAACP,CACX,IEqCQ,CAACW,EAAYC,EAAc,CAAGjQ,EAAAA,QAAc,EAAC,GAI7C,CAACkQ,EAAOC,EAAS,CAAGnQ,EAAAA,QAAc,CAACmM,GACnC6C,EAAOC,GAAYiB,EACnBE,EAAUpQ,EAAAA,WAAiB,CAAEqQ,IACjC,IAAMC,EAAY,mBAAOD,EAAuBA,EAAMrB,GAAQqB,EAC1DlB,EACFA,EAAYmB,GAEZH,EAASG,GAIXC,CAPiB,QAORC,MAAM,CAAG,GAAGC,cAAuBH,MAAH,CAAC,GAAY,iBAA4C,CAA1B,CACvE,CADyEI,EAC3D1B,EAAK,EAGhB2B,EAAgB3Q,EAAAA,WAAiB,CAAC,IAC/BqP,EAAWY,EAAcjB,GAAQ,CAACA,GAAQoB,EAAQpB,GAAQ,CAACA,GACjE,CAACK,EAAUe,EAASH,EAAc,EAGrCjQ,EAAAA,SAAe,CAAC,KACd,IAAM4Q,EAAiBC,IAzDO,MA0DxBA,EAAMC,GAAG,GAAmCD,EAA9BE,OAA2C,EAAIF,EAAMG,OAAAA,GAAU,CAC/EH,EAAMI,KADwCJ,SAC1B,GACpBF,IAEJ,EAEA,OADA7D,OAAOgD,gBAAgB,CAAC,UAAWc,GAC5B,IAAM9D,OAAOiD,mBAAmB,CAAC,UAAWa,EACrD,EAAG,CAACD,EAAc,EAIlB,IAAM7F,EAAQkE,EAAO,WAAa,YAC5BkC,EAAelR,EAAAA,OAAa,CAAsB,IAAO,QAC7D8K,EACAkE,eACAoB,WACAf,aACAW,gBACAC,gBACAU,EACF,EAAI,CAAC7F,EAAOkE,EAAMoB,EAASf,EAAUW,EAAYC,EAAeU,EAAc,EAC9E,MAAO,UAAC/B,EAAeuC,QAAQ,EAACd,MAAOa,EAAc/T,sBAAoB,0BAA0BC,wBAAsB,kBAAkBC,0BAAwB,uBAC/J,UAACP,EAAAA,EAAeA,CAAAA,CAACC,cAAe,EAAGI,sBAAoB,kBAAkBE,0BAAwB,uBAC/F,UAACwB,MAAAA,CAAI3B,YAAU,kBAAkBkS,MAAO,CACxC,kBArFc,CAqFKgC,OACnB,uBApFmB,CAoFKC,MACxB,GAAGjC,CAAK,EACgB3R,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kFAAmFH,GAAa,GAAGT,CAAK,UAC3IW,OAIX,CACA,SAAS2N,EAAQ,MACfsB,EAAO,MAAM,SACbrO,EAAU,SAAS,aACnBgN,EAAc,WAAW,WACzB9N,CAAS,UACTE,CAAQ,CACR,GAAGX,EAKJ,EACC,GAAM,UACJqS,CAAQ,OACRvE,CAAK,YACLkF,CAAU,eACVC,CAAa,CACd,CAAGlF,UACgB,QAAQ,CAAxBQ,EACK,UAAC1M,MAAAA,CAAI3B,YAAU,UAAUO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8EAA+EH,GAAa,GAAGT,CAAK,UAC7IW,IAGH0R,EACK,QADK,EACJlB,EAAKA,CAACa,EAADb,GAAO6B,EAAYd,aAAce,EAAgB,GAAGjT,CAAK,UAClE,WAACuR,EAAYA,CAAC+C,SAAD/C,MAAc,UAAUrR,YAAU,UAAUqU,cAAY,OAAO9T,UAAU,+EAA+E2R,MAAO,CAC5K,kBAvHqB,CAuHFoC,MACrB,EAA0B5E,KAAMA,YAC5B,WAAC6B,EAAWA,CAAChR,QAADgR,EAAW,oBACrB,UAACC,EAAUA,QAAAA,EAAC,YACZ,UAACC,EAAgBA,UAAC,IAADA,gCAEnB,UAAC9P,MAAAA,CAAIpB,UAAU,uCAA+BE,SAI/C,WAACkB,MAAAA,CAAIpB,UAAU,qDAAqDgU,aAAY3G,EAAO4G,mBAA4B,cAAV5G,EAAwBS,EAAc,GAAIoG,eAAcpT,EAASqT,YAAWhF,EAAM1P,YAAU,UAAUE,wBAAsB,UAAUC,0BAAwB,wBAE1Q,UAACwB,MAAAA,CAAI3B,YAAU,cAAcO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0FAA2F,yCAA0C,qCAAkD,aAAZW,GAA0BA,YAAsB,mFAAqF,4DAC3V,UAACM,MAAAA,CAAI3B,YAAU,oBAAoBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uHAAwHgP,WAAkB,iFAAmF,mFAEpQ,CADZ,YACArO,GAAsC,UAAZA,EAAsB,2BADM,gEACuF,0HAA2Hd,GAAa,GAAGT,CAAK,UACzR,UAAC6B,MAAAA,CAAIyS,eAAa,UAAUpU,YAAU,gBAAgBO,UAAU,4NAC7DE,QAIX,CACA,SAAS6B,EAAe,WACtB/B,CAAS,SACT2B,CAAO,CACP,GAAGpC,EACiC,EACpC,GAAM,eACJ2T,CAAa,CACd,CAAG5F,IACJ,MAAO,WAACzM,EAAAA,CAAMA,CAAAA,CAACgT,eAAa,UAAUpU,YAAU,kBAAkBqB,QAAQ,QAAQkB,KAAK,OAAOhC,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,SAAUH,GAAY2B,QAASyR,IACzIzR,IAAUyR,GACVF,GACF,EAAI,GAAG3T,CAAK,CAAEG,sBAAoB,SAASC,wBAAsB,iBAAiBC,0BAAwB,wBACtG,UAACwU,EAAAA,CAAaA,CAAAA,CAAC1U,sBAAoB,gBAAgBE,0BAAwB,gBAC3E,UAACsC,OAAAA,CAAKlC,UAAU,mBAAU,qBAEhC,CACA,SAASwP,EAAY,WACnBxP,CAAS,CACT,GAAGT,EAC4B,EAC/B,GAAM,eACJ2T,CAAa,CACd,CAAG5F,IACJ,MAAO,UAAC+G,SAAAA,CAAOR,eAAa,OAAOpU,YAAU,eAAe6U,aAAW,iBAAiBC,SAAU,CAAC,EAAG5S,QAASuR,EAAe7N,MAAM,iBAAiBrF,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kPAAmP,2EAA4E,yHAA0H,0JAA2J,4DAA6D,4DAA6DH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,eACt8B,CACA,SAAS4U,EAAa,WACpBxU,CAAS,CACT,GAAGT,EAC0B,EAC7B,MAAO,UAACkV,OAAAA,CAAKhV,YAAU,gBAAgBO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsD,kNAAmNH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,eACpZ,CAOA,SAASmO,EAAc,WACrB/N,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC6B,MAAAA,CAAI3B,YAAU,iBAAiBoU,eAAa,SAAS7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,gBAAgBC,0BAAwB,eAC7L,CACA,SAASqP,EAAc,CACrBjP,WAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC6B,MAAAA,CAAI3B,YAAU,iBAAiBoU,eAAa,SAAS7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,gBAAgBC,0BAAwB,eAC7L,CAOA,SAAS2O,EAAe,WACtBvO,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC6B,MAAAA,CAAI3B,YAAU,kBAAkBoU,eAAa,UAAU7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iGAAkGH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,iBAAiBC,0BAAwB,eACvQ,CACA,SAAS4O,EAAa,WACpBxO,CAAS,CACT,GAAGT,EACyB,EAC5B,MAAO,UAAC6B,MAAAA,CAAI3B,YAAU,gBAAgBoU,eAAa,QAAQ7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4CAA6CH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,eAC5M,CACA,SAAS6O,EAAkB,WACzBzO,CAAS,SACTY,EAAU,EAAK,CACf,GAAGrB,EAGJ,EACC,IAAMmV,EAAO9T,EAAU+T,EAAAA,EAAIA,CAAG,MAC9B,MAAO,UAACD,EAAAA,CAAKjV,YAAU,sBAAsBoU,eAAa,cAAc7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2OAA4O,8EAA+EH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,OAAOC,wBAAsB,oBAAoBC,0BAAwB,eACvgB,CAmBA,SAAS+M,EAAY,WACnB3M,CAAS,CACT,GAAGT,EACwB,EAC3B,MAAO,UAACqV,KAAAA,CAAGnV,YAAU,eAAeoU,eAAa,OAAO7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,cAAcC,0BAAwB,eACjM,CACA,SAASgN,EAAgB,WACvB5M,CAAS,CACT,GAAGT,EACwB,EAC3B,MAAO,UAACsV,KAAAA,CAAGpV,YAAU,oBAAoBoU,eAAa,YAAY7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,kBAAkBC,0BAAwB,eACrM,CACA,IAAMkV,EAA4BC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,m0BAAo0B,CACx2BC,SAAU,CACRlU,QAAS,CACPmU,QAAS,+DACTC,QAAS,8KACX,EACAlT,KAAM,CACJiT,QAAS,cACTE,GAAI,cACJC,GAAI,iDACN,CACF,EACAC,gBAAiB,CACfvU,QAAS,UACTkB,KAAM,SACR,CACF,GACA,SAAS6K,EAAkB,SACzBjM,EAAU,EAAK,UACfyF,GAAW,CAAK,SAChBvF,EAAU,SAAS,MACnBkB,EAAO,SAAS,SAChB2M,CAAO,WACP3O,CAAS,CACT,GAAGT,EAK6C,EAChD,IAAMmV,EAAO9T,EAAU+T,EAAAA,EAAIA,CAAG,SACxB,UACJ/C,CAAQ,OACRvE,CAAK,CACN,CAAGC,IACE+G,EAAS,UAACK,EAAAA,CAAKjV,YAAU,sBAAsBoU,eAAa,cAAcyB,YAAWtT,EAAMuT,cAAalP,EAAUrG,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC2U,EAA0B,SAC9JhU,EACAkB,MACF,GAAIhC,GAAa,GAAGT,CAAK,UACzB,GAGuB,CAHnB,KAAU,IAGV,OAAOoP,IACTA,EAAU,CACRzO,SAAUyO,EACZ,EAEK,WAAC9O,EAAAA,EAAOA,CAAAA,CAACH,sBAAoB,UAAUC,wBAAsB,oBAAoBC,0BAAwB,wBAC5G,UAACE,EAAAA,EAAcA,CAAAA,CAACc,OAAO,IAAClB,sBAAoB,iBAAiBE,0BAAwB,uBAAeyU,IACpG,UAACtU,EAAAA,EAAcA,CAAAA,CAACoP,KAAK,QAAQlO,MAAM,SAASuU,OAAkB,cAAVnI,GAAyBuE,EAAW,GAAGjD,CAAO,CAAEjP,sBAAoB,iBAAiBE,0BAAwB,oBAT5JyU,CAWX,CAuCA,SAASvF,EAAe,WACtB9O,CAAS,CACT,GAAGT,EACwB,EAC3B,MAAO,UAACqV,KAAAA,CAAGnV,YAAU,mBAAmBoU,eAAa,WAAW7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iGAAkG,uCAAwCH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,iBAAiBC,0BAAwB,eAChT,CACA,SAASmP,EAAmB,WAC1B/O,CAAS,CACT,GAAGT,EACwB,EAC3B,MAAO,UAACsV,KAAAA,CAAGpV,YAAU,wBAAwBoU,eAAa,gBAAgB7T,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+BAAgCH,GAAa,GAAGT,CAAK,CAAEI,wBAAsB,qBAAqBC,0BAAwB,eACpN,CACA,SAASoP,EAAqB,SAC5BpO,GAAU,CAAK,MACfoB,EAAO,IAAI,UACXqE,GAAW,CAAK,CAChBrG,WAAS,CACT,GAAGT,EAKJ,EACC,IAAMmV,EAAO9T,EAAU+T,EAAAA,EAAIA,CAAG,IAC9B,MAAO,UAACD,EAAAA,CAAKjV,YAAU,0BAA0BoU,eAAa,kBAAkByB,YAAWtT,EAAMuT,cAAalP,EAAUrG,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gfAAif,yFAAmG,OAAT6B,GAAiB,UAAWA,UAAiB,UAAW,uCAAwChC,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,OAAOC,wBAAsB,uBAAuBC,0BAAwB,eAC16B,yECtXO,SAASmB,EAAkB,WAChCf,CAAS,UACTkP,GAAW,CAAK,CACO,EACvB,GAAM,CACJ3O,MAAI,CACL,CAAGE,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,UACX,EAKO,EALH,CAKG,CALI,CAKJ,MAACW,MAAAA,CAAIpB,UAAU,0BAA0BL,wBAAsB,oBAAoBC,0BAAwB,oCAC9G,WAACkQ,EAAAA,MAAMA,CAAAA,CAAC9P,UAAWA,EAAWN,sBAAoB,SAASE,0BAAwB,oCACjF,UAACoQ,EAAAA,WAAWA,CAAAA,CAAC9B,IAAK,CAAC,iCAAiC,EAAE3N,EAAKe,IAAI,CAAC,kBAAkB,CAAC,CAAE6M,IAAK5N,EAAKe,IAAI,CAAE5B,sBAAoB,cAAcE,0BAAwB,4BAC/J,UAACqQ,EAAAA,cAAcA,CAAAA,CAACjQ,UAAU,aAAaN,sBAAoB,iBAAiBE,0BAAwB,mCACjGW,EAAKe,IAAI,EAAE2B,MAAM,EAAG,IAAIwS,eAAiB,SAI7CvG,GAAY,WAAC9N,MAAAA,CAAIpB,UAAU,wDACxB,UAACkC,OAAAA,CAAKlC,UAAU,kCAA0BO,EAAKe,IAAI,GACnD,UAACY,OAAAA,CAAKlC,UAAU,4BAAoBO,EAAKgB,KAAK,SAd7C,UAACuO,EAAAA,MAAMA,CAAAA,CAAC9P,UAAWA,WACtB,UAACiQ,EAAAA,cAAcA,CAAAA,UAAC,OAgBxB,2FC1BA,IAAMyF,EAAYnT,EAAAA,UAAgB,CAAmH,CAAC,WACpJvC,CAAS,aACT2V,EAAc,YAAY,YAC1BC,GAAa,CAAI,CACjB,GAAGrW,EACJ,CAAEoD,IAAQ,UAACkT,EAAAA,CAAuB,EAAClT,IAAKA,EAAKiT,WAAYA,EAAYD,YAAaA,EAAa3V,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qBAAsBwV,iBAA+B,iBAAmB,iBAAkB3V,GAAa,GAAGT,CAAK,IAC7NmW,EAAUxR,WAAW,CAAG2R,EAAAA,CAAuB,CAAC3R,WAAW,+JCL3D,SAASxD,EAAa,CACpB,GAAGnB,EACqD,EACxD,MAAO,UAACuW,EAAAA,EAA0B,EAACrW,YAAU,gBAAiB,GAAGF,CAAK,CAAEG,sBAAoB,6BAA6BC,wBAAsB,eAAeC,0BAAwB,qBACxL,CAMA,SAASe,EAAoB,CAC3B,GAAGpB,EACwD,EAC3D,MAAO,UAACuW,EAAAA,EAA6B,EAACrW,YAAU,wBAAyB,GAAGF,CAAK,CAAEG,sBAAoB,gCAAgCC,wBAAsB,sBAAsBC,0BAAwB,qBAC7M,CACA,SAASoB,EAAoB,WAC3BhB,CAAS,YACTC,EAAa,CAAC,CACd,GAAGV,EACwD,EAC3D,MAAO,UAACuW,EAAAA,EAA4B,EAACpW,sBAAoB,+BAA+BC,wBAAsB,sBAAsBC,0BAAwB,6BACxJ,UAACkW,EAAAA,EAA6B,EAACrW,YAAU,wBAAwBQ,WAAYA,EAAYD,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yjBAA0jBH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,gCAAgCE,0BAAwB,uBAE1wB,CACA,SAAS6B,EAAkB,CACzB,GAAGlC,EACsD,EACzD,MAAO,UAACuW,EAAAA,EAA2B,EAACrW,YAAU,sBAAuB,GAAGF,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,qBACrM,CACA,SAAS8B,EAAiB,WACxB1B,CAAS,OACT+V,CAAK,SACLjV,EAAU,SAAS,CACnB,GAAGvB,EAIJ,EACC,MAAO,UAACuW,EAAAA,EAA0B,EAACrW,YAAU,qBAAqBuW,aAAYD,EAAO7B,eAAcpT,EAASd,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yqBAA0qBH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,6BAA6BC,wBAAsB,mBAAmBC,0BAAwB,qBAC/6B,CACA,SAASqW,EAAyB,WAChCjW,CAAS,UACTE,CAAQ,SACRgW,CAAO,CACP,GAAG3W,EAC6D,EAChE,MAAO,WAACuW,EAAAA,EAAkC,EAACrW,YAAU,8BAA8BO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+SAAgTH,GAAYkW,QAASA,EAAU,GAAG3W,CAAK,CAAEG,sBAAoB,qCAAqCC,wBAAsB,2BAA2BC,0BAAwB,8BACxjB,UAACsC,OAAAA,CAAKlC,UAAU,yFACd,UAAC8V,EAAAA,EAAmC,EAACpW,sBAAoB,sCAAsCE,0BAAwB,6BACrH,UAACuW,EAAAA,CAASA,CAAAA,CAACnW,UAAU,SAASN,sBAAoB,YAAYE,0BAAwB,0BAGzFM,IAEP,CAoBA,SAASiB,EAAkB,WACzBnB,CAAS,OACT+V,CAAK,CACL,GAAGxW,EAGJ,EACC,MAAO,UAACuW,EAAAA,EAA2B,EAACrW,YAAU,sBAAsBuW,aAAYD,EAAO/V,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oDAAqDH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,qBACvS,CACA,SAAS4B,EAAsB,WAC7BxB,CAAS,CACT,GAAGT,EAC0D,EAC7D,MAAO,UAACuW,EAAAA,EAA+B,EAACrW,YAAU,0BAA0BO,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4BAA6BH,GAAa,GAAGT,CAAK,CAAEG,sBAAoB,kCAAkCC,wBAAsB,wBAAwBC,0BAAwB,qBAC5Q,8CChFO,IAAM4N,EAAeT,GAAgC,CAC1D,CACE1H,MAAO,oBACPL,IAAK,WACL9B,KAAM,aACNmD,SAAU0G,EAASqJ,UAAU,CAAC,YAC9B9S,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EACT,CADY,CAEZ,CACEsB,MAAO,aACPL,IAAK,cACL9B,GALoD,EAK9C,gBACNmD,SAAU0G,EAASqJ,UAAU,CAAC,eAC9B9S,SAAU,CAAC,IAAK,IAAI,CACpBS,MAAO,EAAE,CAAC,CAEb,CAAC,EAGiCyJ,EAAY,IAAI", "sources": ["webpack://terang-lms-ui/./src/components/ui/tooltip.tsx", "webpack://terang-lms-ui/./src/components/layout/user-nav.tsx", "webpack://terang-lms-ui/./src/components/layout/header.tsx", "webpack://terang-lms-ui/./src/components/kbar/result-item.tsx", "webpack://terang-lms-ui/./src/components/kbar/render-result.tsx", "webpack://terang-lms-ui/./src/components/kbar/use-theme-switching.tsx", "webpack://terang-lms-ui/./src/components/kbar/index.tsx", "webpack://terang-lms-ui/./src/components/ui/collapsible.tsx", "webpack://terang-lms-ui/./src/config/navigation.ts", "webpack://terang-lms-ui/./src/hooks/use-media-query.ts", "webpack://terang-lms-ui/./src/components/icons.tsx", "webpack://terang-lms-ui/./src/components/role-indicator.tsx", "webpack://terang-lms-ui/./src/components/layout/app-sidebar.tsx", "webpack://terang-lms-ui/./src/hooks/use-auth.ts", "webpack://terang-lms-ui/./src/components/ui/input.tsx", "webpack://terang-lms-ui/./src/components/ui/avatar.tsx", "webpack://terang-lms-ui/./src/components/ui/skeleton.tsx", "webpack://terang-lms-ui/./src/lib/auth.ts", "webpack://terang-lms-ui/./src/hooks/use-mobile.tsx", "webpack://terang-lms-ui/./src/components/ui/sheet.tsx", "webpack://terang-lms-ui/./src/components/ui/sidebar.tsx", "webpack://terang-lms-ui/./src/components/user-avatar-profile.tsx", "webpack://terang-lms-ui/./src/components/ui/separator.tsx", "webpack://terang-lms-ui/./src/components/ui/dropdown-menu.tsx", "webpack://terang-lms-ui/./src/constants/data.ts"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\nimport { cn } from '@/lib/utils';\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return <TooltipPrimitive.Provider data-slot='tooltip-provider' delayDuration={delayDuration} {...props} data-sentry-element=\"TooltipPrimitive.Provider\" data-sentry-component=\"TooltipProvider\" data-sentry-source-file=\"tooltip.tsx\" />;\n}\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return <TooltipProvider data-sentry-element=\"TooltipProvider\" data-sentry-component=\"Tooltip\" data-sentry-source-file=\"tooltip.tsx\">\r\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} data-sentry-element=\"TooltipPrimitive.Root\" data-sentry-source-file=\"tooltip.tsx\" />\r\n    </TooltipProvider>;\n}\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} data-sentry-element=\"TooltipPrimitive.Trigger\" data-sentry-component=\"TooltipTrigger\" data-sentry-source-file=\"tooltip.tsx\" />;\n}\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return <TooltipPrimitive.Portal data-sentry-element=\"TooltipPrimitive.Portal\" data-sentry-component=\"TooltipContent\" data-sentry-source-file=\"tooltip.tsx\">\r\n      <TooltipPrimitive.Content data-slot='tooltip-content' sideOffset={sideOffset} className={cn('bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance', className)} {...props} data-sentry-element=\"TooltipPrimitive.Content\" data-sentry-source-file=\"tooltip.tsx\">\r\n        {children}\r\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' data-sentry-element=\"TooltipPrimitive.Arrow\" data-sentry-source-file=\"tooltip.tsx\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>;\n}\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };", "'use client';\n\nimport { Button } from '@/components/ui/button';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/hooks/use-auth';\nexport function UserNav() {\n  const router = useRouter();\n  const {\n    user,\n    signOut\n  } = useAuth();\n  if (!user) {\n    return null;\n  }\n  return <DropdownMenu data-sentry-element=\"DropdownMenu\" data-sentry-component=\"UserNav\" data-sentry-source-file=\"user-nav.tsx\">\r\n      <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"user-nav.tsx\">\r\n        <Button variant='ghost' className='relative h-8 w-8 rounded-full cursor-pointer' data-sentry-element=\"Button\" data-sentry-source-file=\"user-nav.tsx\">\r\n          <UserAvatarProfile data-sentry-element=\"UserAvatarProfile\" data-sentry-source-file=\"user-nav.tsx\" />\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent className='w-56' align='end' sideOffset={10} forceMount data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"user-nav.tsx\">\r\n        <DropdownMenuLabel className='font-normal' data-sentry-element=\"DropdownMenuLabel\" data-sentry-source-file=\"user-nav.tsx\">\r\n          <div className='flex flex-col space-y-1'>\r\n            <p className='text-sm leading-none font-medium'>{user.name}</p>\r\n            <p className='text-muted-foreground text-xs leading-none'>\r\n              {user.email}\r\n            </p>\r\n          </div>\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"user-nav.tsx\" />\r\n        <DropdownMenuGroup data-sentry-element=\"DropdownMenuGroup\" data-sentry-source-file=\"user-nav.tsx\">\r\n          <DropdownMenuItem onClick={() => router.push('/dashboard/profile')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"user-nav.tsx\">\r\n            Profile\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"user-nav.tsx\">Billing</DropdownMenuItem>\r\n        </DropdownMenuGroup>\r\n        <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"user-nav.tsx\" />\r\n        <DropdownMenuItem onClick={signOut} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"user-nav.tsx\">Sign Out</DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>;\n}", "'use client';\n\nimport React from 'react';\nimport { SidebarTrigger } from '../ui/sidebar';\nimport { Separator } from '../ui/separator';\nimport SearchInput from '../search-input';\nimport { UserNav } from './user-nav';\nimport { But<PERSON> } from '../ui/button';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '../ui/dropdown-menu';\nimport { User, Settings, HelpCircle, FileText } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport CtaGithub from './cta-github';\nexport default function Header() {\n  const router = useRouter();\n  return <header className='flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12' data-sentry-component=\"Header\" data-sentry-source-file=\"header.tsx\">\r\n      <div className='flex items-center gap-2 px-4'>\r\n        <SidebarTrigger className='-ml-1' data-sentry-element=\"SidebarTrigger\" data-sentry-source-file=\"header.tsx\" />\r\n      </div>\r\n\r\n      <div className='flex items-center gap-2 px-4'>\r\n        <DropdownMenu data-sentry-element=\"DropdownMenu\" data-sentry-source-file=\"header.tsx\">\r\n          <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"header.tsx\">\r\n            <Button variant='ghost' size='icon' className='relative h-8 w-8 rounded-full cursor-pointer' data-sentry-element=\"Button\" data-sentry-source-file=\"header.tsx\">\r\n              <User className='h-4 w-4' data-sentry-element=\"User\" data-sentry-source-file=\"header.tsx\" />\r\n              <span className='sr-only'>Profile Menu</span>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent className='w-56' align='end' sideOffset={10} data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"header.tsx\">\r\n            <DropdownMenuLabel data-sentry-element=\"DropdownMenuLabel\" data-sentry-source-file=\"header.tsx\">Profile & Settings</DropdownMenuLabel>\r\n            <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"header.tsx\" />\r\n            <DropdownMenuGroup data-sentry-element=\"DropdownMenuGroup\" data-sentry-source-file=\"header.tsx\">\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/profile')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"header.tsx\">\r\n                <User className='mr-2 h-4 w-4' data-sentry-element=\"User\" data-sentry-source-file=\"header.tsx\" />\r\n                View Profile\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/settings')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"header.tsx\">\r\n                <Settings className='mr-2 h-4 w-4' data-sentry-element=\"Settings\" data-sentry-source-file=\"header.tsx\" />\r\n                Settings\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/help')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"header.tsx\">\r\n                <HelpCircle className='mr-2 h-4 w-4' data-sentry-element=\"HelpCircle\" data-sentry-source-file=\"header.tsx\" />\r\n                Help & Support\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => router.push('/dashboard/documentation')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"header.tsx\">\r\n                <FileText className='mr-2 h-4 w-4' data-sentry-element=\"FileText\" data-sentry-source-file=\"header.tsx\" />\r\n                Documentation\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n        <UserNav data-sentry-element=\"UserNav\" data-sentry-source-file=\"header.tsx\" />\r\n      </div>\r\n    </header>;\n}", "import type { ActionId, ActionImpl } from 'kbar';\nimport * as React from 'react';\nconst ResultItem = React.forwardRef(({\n  action,\n  active,\n  currentRootActionId\n}: {\n  action: ActionImpl;\n  active: boolean;\n  currentRootActionId: ActionId;\n}, ref: React.Ref<HTMLDivElement>) => {\n  const ancestors = React.useMemo(() => {\n    if (!currentRootActionId) return action.ancestors;\n    const index = action.ancestors.findIndex(ancestor => ancestor.id === currentRootActionId);\n    return action.ancestors.slice(index + 1);\n  }, [action.ancestors, currentRootActionId]);\n  return <div ref={ref} className={`relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`}>\r\n        {active && <div id='kbar-result-item' className='border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4'></div>}\r\n        <div className='relative z-10 flex items-center gap-2'>\r\n          {action.icon && action.icon}\r\n          <div className='flex flex-col'>\r\n            <div>\r\n              {ancestors.length > 0 && ancestors.map(ancestor => <React.Fragment key={ancestor.id}>\r\n                    <span className='text-muted-foreground mr-2'>\r\n                      {ancestor.name}\r\n                    </span>\r\n                    <span className='mr-2'>&rsaquo;</span>\r\n                  </React.Fragment>)}\r\n              <span>{action.name}</span>\r\n            </div>\r\n            {action.subtitle && <span className='text-muted-foreground text-sm'>\r\n                {action.subtitle}\r\n              </span>}\r\n          </div>\r\n        </div>\r\n        {action.shortcut?.length ? <div className='relative z-10 grid grid-flow-col gap-1'>\r\n            {action.shortcut.map((sc, i) => <kbd key={sc + i} className='bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium'>\r\n                {sc}\r\n              </kbd>)}\r\n          </div> : null}\r\n      </div>;\n});\nResultItem.displayName = 'KBarResultItem';\nexport default ResultItem;", "import { KBarResults, useMatches } from 'kbar';\nimport ResultItem from './result-item';\nexport default function RenderResults() {\n  const {\n    results,\n    rootActionId\n  } = useMatches();\n  return <KBarResults items={results} onRender={({\n    item,\n    active\n  }) => typeof item === 'string' ? <div className='text-primary-foreground px-4 py-2 text-sm uppercase opacity-50'>\r\n            {item}\r\n          </div> : <ResultItem action={item} active={active} currentRootActionId={rootActionId ?? ''} />} data-sentry-element=\"KBarResults\" data-sentry-component=\"RenderResults\" data-sentry-source-file=\"render-result.tsx\" />;\n}", "import { useRegisterActions } from 'kbar';\nimport { useTheme } from 'next-themes';\nconst useThemeSwitching = () => {\n  const {\n    theme,\n    setTheme\n  } = useTheme();\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n  const themeAction = [{\n    id: 'toggleTheme',\n    name: 'Toggle Theme',\n    shortcut: ['t', 't'],\n    section: 'Theme',\n    perform: toggleTheme\n  }, {\n    id: 'setLightTheme',\n    name: 'Set Light Theme',\n    section: 'Theme',\n    perform: () => setTheme('light')\n  }, {\n    id: 'setDarkTheme',\n    name: 'Set Dark Theme',\n    section: 'Theme',\n    perform: () => setTheme('dark')\n  }];\n  useRegisterActions(themeAction, [theme]);\n};\nexport default useThemeSwitching;", "'use client';\n\nimport { navItems } from '@/constants/data';\nimport { KBarAnimator, KBarPortal, KBarPositioner, KBarProvider, KBarSearch } from 'kbar';\nimport { useRouter } from 'next/navigation';\nimport { useMemo } from 'react';\nimport RenderResults from './render-result';\nimport useThemeSwitching from './use-theme-switching';\nexport default function KBar({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const router = useRouter();\n\n  // These action are for the navigation\n  const actions = useMemo(() => {\n    // Define navigateTo inside the useMemo callback to avoid dependency array issues\n    const navigateTo = (url: string) => {\n      router.push(url);\n    };\n    return navItems.flatMap(navItem => {\n      // Only include base action if the navItem has a real URL and is not just a container\n      const baseAction = navItem.url !== '#' ? {\n        id: `${navItem.title.toLowerCase()}Action`,\n        name: navItem.title,\n        shortcut: navItem.shortcut,\n        keywords: navItem.title.toLowerCase(),\n        section: 'Navigation',\n        subtitle: `Go to ${navItem.title}`,\n        perform: () => navigateTo(navItem.url)\n      } : null;\n\n      // Map child items into actions\n      const childActions = navItem.items?.map(childItem => ({\n        id: `${childItem.title.toLowerCase()}Action`,\n        name: childItem.title,\n        shortcut: childItem.shortcut,\n        keywords: childItem.title.toLowerCase(),\n        section: navItem.title,\n        subtitle: `Go to ${childItem.title}`,\n        perform: () => navigateTo(childItem.url)\n      })) ?? [];\n\n      // Return only valid actions (ignoring null base actions for containers)\n      return baseAction ? [baseAction, ...childActions] : childActions;\n    });\n  }, [router]);\n  return <KBarProvider actions={actions} data-sentry-element=\"KBarProvider\" data-sentry-component=\"KBar\" data-sentry-source-file=\"index.tsx\">\r\n      <KBarComponent data-sentry-element=\"KBarComponent\" data-sentry-source-file=\"index.tsx\">{children}</KBarComponent>\r\n    </KBarProvider>;\n}\nconst KBarComponent = ({\n  children\n}: {\n  children: React.ReactNode;\n}) => {\n  useThemeSwitching();\n  return <>\r\n      <KBarPortal data-sentry-element=\"KBarPortal\" data-sentry-source-file=\"index.tsx\">\r\n        <KBarPositioner className='bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm' data-sentry-element=\"KBarPositioner\" data-sentry-source-file=\"index.tsx\">\r\n          <KBarAnimator className='bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg' data-sentry-element=\"KBarAnimator\" data-sentry-source-file=\"index.tsx\">\r\n            <div className='bg-card border-border sticky top-0 z-10 border-b'>\r\n              <KBarSearch className='bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden' data-sentry-element=\"KBarSearch\" data-sentry-source-file=\"index.tsx\" />\r\n            </div>\r\n            <div className='max-h-[400px]'>\r\n              <RenderResults data-sentry-element=\"RenderResults\" data-sentry-source-file=\"index.tsx\" />\r\n            </div>\r\n          </KBarAnimator>\r\n        </KBarPositioner>\r\n      </KBarPortal>\r\n      {children}\r\n    </>;\n};", "'use client';\n\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nfunction Collapsible({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\n  return <CollapsiblePrimitive.Root data-slot='collapsible' {...props} data-sentry-element=\"CollapsiblePrimitive.Root\" data-sentry-component=\"Collapsible\" data-sentry-source-file=\"collapsible.tsx\" />;\n}\nfunction CollapsibleTrigger({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\n  return <CollapsiblePrimitive.CollapsibleTrigger data-slot='collapsible-trigger' {...props} data-sentry-element=\"CollapsiblePrimitive.CollapsibleTrigger\" data-sentry-component=\"CollapsibleTrigger\" data-sentry-source-file=\"collapsible.tsx\" />;\n}\nfunction CollapsibleContent({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\n  return <CollapsiblePrimitive.CollapsibleContent data-slot='collapsible-content' {...props} data-sentry-element=\"CollapsiblePrimitive.CollapsibleContent\" data-sentry-component=\"CollapsibleContent\" data-sentry-source-file=\"collapsible.tsx\" />;\n}\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };", "import { NavItem } from '@/types';\r\n\r\n// Super Admin Navigation\r\nexport const superAdminNavItems: NavItem[] = [\r\n  {\r\n    title: 'Dashboard',\r\n    url: '/dashboard/admin',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Institutions',\r\n    url: '/dashboard/admin/institutions',\r\n    icon: 'building',\r\n    isActive: false,\r\n    shortcut: ['i', 'i'],\r\n    items: [\r\n      {\r\n        title: 'All Institutions',\r\n        url: '/dashboard/admin/institutions',\r\n        icon: 'building'\r\n      },\r\n      {\r\n        title: 'Add Institution',\r\n        url: '/dashboard/admin/institutions/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: 'Users',\r\n    url: '/dashboard/admin/users',\r\n    icon: 'users',\r\n    isActive: false,\r\n    shortcut: ['u', 'u'],\r\n    items: [\r\n      {\r\n        title: 'All Users',\r\n        url: '/dashboard/admin/users',\r\n        icon: 'users'\r\n      },\r\n      {\r\n        title: 'Add User',\r\n        url: '/dashboard/admin/users/new',\r\n        icon: 'userPlus'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: 'Subscriptions',\r\n    url: '/dashboard/admin/subscriptions',\r\n    icon: 'creditCard',\r\n    isActive: false,\r\n    shortcut: ['s', 's'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Analytics',\r\n    url: '/dashboard/admin/analytics',\r\n    icon: 'barChart',\r\n    isActive: false,\r\n    shortcut: ['a', 'a'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Account',\r\n    url: '#',\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: 'Profile',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['p', 'p']\r\n      },\r\n      {\r\n        title: 'Settings',\r\n        url: '/dashboard/settings',\r\n        icon: 'settings'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// Teacher Navigation\r\nexport const teacherNavItems: NavItem[] = [\r\n  {\r\n    title: 'Dashboard',\r\n    url: '/dashboard/teacher',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: []\r\n  },\r\n  /*\r\n  {\r\n    title: 'Classes',\r\n    url: '/dashboard/teacher/classes',\r\n    icon: 'users',\r\n    isActive: false,\r\n    shortcut: ['c', 'c'],\r\n    items: [\r\n      {\r\n        title: 'My Classes',\r\n        url: '/dashboard/teacher/classes',\r\n        icon: 'users'\r\n      },\r\n      {\r\n        title: 'Create Class',\r\n        url: '/dashboard/teacher/classes/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n  */\r\n  {\r\n    title: 'Courses',\r\n    url: '/dashboard/teacher/courses',\r\n    icon: 'bookOpen',\r\n    isActive: false,\r\n    shortcut: ['o', 'o'],\r\n    items: [\r\n      {\r\n        title: 'My Courses',\r\n        url: '/dashboard/teacher/courses',\r\n        icon: 'bookOpen'\r\n      },\r\n      {\r\n        title: 'Create Course',\r\n        url: '/dashboard/teacher/courses/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n\r\n  /*\r\n  {\r\n    title: 'Quizzes',\r\n    url: '/dashboard/teacher/quizzes',\r\n    icon: 'post',\r\n    isActive: false,\r\n    shortcut: ['q', 'q'],\r\n    items: [\r\n      {\r\n        title: 'All Quizzes',\r\n        url: '/dashboard/teacher/quizzes',\r\n        icon: 'post'\r\n      },\r\n      {\r\n        title: 'Create Quiz',\r\n        url: '/dashboard/teacher/quizzes/new',\r\n        icon: 'plus'\r\n      }\r\n    ]\r\n  },\r\n  */\r\n  /*\r\n  {\r\n    title: 'Reports',\r\n    url: '/dashboard/teacher/reports',\r\n    icon: 'barChart',\r\n    isActive: false,\r\n    shortcut: ['r', 'r'],\r\n    items: [\r\n      {\r\n        title: 'Student Progress',\r\n        url: '/dashboard/teacher/reports/progress',\r\n        icon: 'trendingUp'\r\n      },\r\n      {\r\n        title: 'Quiz Results',\r\n        url: '/dashboard/teacher/reports/quizzes',\r\n        icon: 'post'\r\n      },\r\n      {\r\n        title: 'Certificates',\r\n        url: '/dashboard/teacher/reports/certificates',\r\n        icon: 'award'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: 'Account',\r\n    url: '#',\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: 'Profile',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['p', 'p']\r\n      },\r\n      {\r\n        title: 'Settings',\r\n        url: '/dashboard/settings',\r\n        icon: 'settings'\r\n      }\r\n    ]\r\n  }\r\n  */\r\n];\r\n\r\n// Student Navigation\r\nexport const studentNavItems: NavItem[] = [\r\n  {\r\n    title: 'Dashboard',\r\n    url: '/dashboard/student',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'My Courses',\r\n    url: '/dashboard/student/courses',\r\n    icon: 'bookOpen',\r\n    isActive: false,\r\n    shortcut: ['c', 'c'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Progress',\r\n    url: '/dashboard/student/progress',\r\n    icon: 'trendingUp',\r\n    isActive: false,\r\n    shortcut: ['p', 'p'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Certificates',\r\n    url: '/dashboard/student/certificates',\r\n    icon: 'award',\r\n    isActive: false,\r\n    shortcut: ['e', 'e'],\r\n    items: []\r\n  },\r\n  {\r\n    title: 'Account',\r\n    url: '#',\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: 'Profile',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['r', 'r']\r\n      },\r\n      {\r\n        title: 'Settings',\r\n        url: '/dashboard/settings',\r\n        icon: 'settings'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// Function to get navigation items based on user role\r\nexport const getNavigationItems = (role: string): NavItem[] => {\r\n  switch (role) {\r\n    case 'super_admin':\r\n      return superAdminNavItems;\r\n    case 'teacher':\r\n      return teacherNavItems;\r\n    case 'student':\r\n      return studentNavItems;\r\n    default:\r\n      return [];\r\n  }\r\n};\r\n", "import { useEffect, useState } from 'react';\r\n\r\nexport function useMediaQuery() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const mediaQuery = window.matchMedia('(max-width: 768px)');\r\n    setIsOpen(mediaQuery.matches);\r\n\r\n    const handler = (e: MediaQueryListEvent) => {\r\n      setIsOpen(e.matches);\r\n    };\r\n\r\n    mediaQuery.addEventListener('change', handler);\r\n    return () => mediaQuery.removeEventListener('change', handler);\r\n  }, []);\r\n\r\n  return { isOpen };\r\n}\r\n", "import { Alert<PERSON>riangle, <PERSON>R<PERSON>, Award, BarChart3, BookOpen, Book, Bot, Building2, Check, ChevronLeft, ChevronRight, Command, CreditCard, File, FileText, HelpCircle, Image, Laptop, LayoutDashboard, Loader2, LogIn, ShoppingBag, Moon, MoreHorizontal, Pizza, Plus, Settings, Sun, Trash2, TrendingUp, User, UserCheck, UserCircle, UserCog, UserPlus, Users, UserX, X, Square, ExternalLink, Github, Twitter, type LucideProps } from 'lucide-react';\nimport { Search01Icon as SearchIcon, GraduateMaleIcon as GraduationCapIcon } from 'hugeicons-react';\nexport type Icon = React.ComponentType<LucideProps>;\nexport const Icons = {\n  dashboard: LayoutDashboard,\n  logo: Command,\n  login: LogIn,\n  close: X,\n  product: ShoppingBag,\n  spinner: Loader2,\n  kanban: Square,\n  chevronLeft: ChevronLeft,\n  chevronRight: ChevronRight,\n  trash: Trash2,\n  employee: User<PERSON>,\n  post: FileText,\n  page: File,\n  userPen: UserCog,\n  user2: UserCircle,\n  media: Image,\n  settings: Settings,\n  billing: CreditCard,\n  ellipsis: MoreHorizontal,\n  add: Plus,\n  warning: AlertTriangle,\n  user: User,\n  arrowRight: ArrowRight,\n  help: HelpCircle,\n  pizza: Pizza,\n  sun: Sun,\n  moon: Moon,\n  laptop: Laptop,\n  github: Github,\n  twitter: Twitter,\n  check: Check,\n  // LMS specific icons - migrated from Tabler to Lucide equivalents\n  building: Building2,\n  users: Users,\n  userPlus: UserPlus,\n  userCheck: UserCheck,\n  creditCard: CreditCard,\n  barChart: BarChart3,\n  bookOpen: BookOpen,\n  book: Book,\n  bot: Bot,\n  trendingUp: TrendingUp,\n  award: Award,\n  plus: Plus,\n  // Additional LMS icons\n  school: BookOpen,\n  certificate: Award,\n  enrollment: UserPlus,\n  searchList: SearchIcon,\n  graduationCap: GraduationCapIcon\n};", "'use client';\n\nimport { GraduationCap, BookOpen, Building2 } from 'lucide-react';\nimport * as React from 'react';\nimport { authStorage } from '@/lib/auth';\nimport { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';\nconst getRoleDisplay = (role: string) => {\n  switch (role) {\n    case 'teacher':\n      return {\n        label: 'Teacher',\n        icon: GraduationCap,\n        bgColor: 'bg-blue-600'\n      };\n    case 'student':\n      return {\n        label: 'Student',\n        icon: BookOpen,\n        bgColor: 'bg-green-600'\n      };\n    case 'super_admin':\n      return {\n        label: 'Institution Manager',\n        icon: Building2,\n        bgColor: 'bg-purple-600'\n      };\n    default:\n      return {\n        label: 'User',\n        icon: BookOpen,\n        bgColor: 'bg-gray-600'\n      };\n  }\n};\nexport function RoleIndicator() {\n  const [user, setUser] = React.useState(authStorage.getUser());\n  React.useEffect(() => {\n    const currentUser = authStorage.getUser();\n    setUser(currentUser);\n  }, []);\n  if (!user) {\n    return null;\n  }\n  const roleInfo = getRoleDisplay(user.role);\n  const Icon = roleInfo.icon;\n  return <SidebarMenu data-sentry-element=\"SidebarMenu\" data-sentry-component=\"RoleIndicator\" data-sentry-source-file=\"role-indicator.tsx\">\r\n      <SidebarMenuItem data-sentry-element=\"SidebarMenuItem\" data-sentry-source-file=\"role-indicator.tsx\">\r\n        <SidebarMenuButton size='lg' className='cursor-default hover:bg-transparent' data-sentry-element=\"SidebarMenuButton\" data-sentry-source-file=\"role-indicator.tsx\">\r\n          <div className={`${roleInfo.bgColor} text-white flex aspect-square size-8 items-center justify-center rounded-lg`}>\r\n            <Icon className='size-4' data-sentry-element=\"Icon\" data-sentry-source-file=\"role-indicator.tsx\" />\r\n          </div>\r\n          <div className='flex flex-col gap-0.5 leading-none'>\r\n            <span className='font-semibold'>{user.name}</span>\r\n            <span className='text-sm text-muted-foreground'>Account Type: {roleInfo.label}</span>\r\n          </div>\r\n        </SidebarMenuButton>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>;\n}", "'use client';\n\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubButton, SidebarMenuSubItem, SidebarRail, useSidebar } from '@/components/ui/sidebar';\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\nimport { getNavigationItems } from '@/config/navigation';\nimport { authStorage } from '@/lib/auth';\nimport { getNavItems } from '@/constants/data';\nimport { useMediaQuery } from '@/hooks/use-media-query';\nimport { ChevronRight, LogOut, Building2 } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { usePathname, useRouter } from 'next/navigation';\nimport * as React from 'react';\nimport { NavItem } from '@/types';\nimport { Icons } from '../icons';\nimport { RoleIndicator } from '../role-indicator';\nexport const company = {\n  name: 'Acme Inc',\n  logo: Building2,\n  plan: 'Enterprise'\n};\nexport default function AppSidebar() {\n  const pathname = usePathname();\n  const {\n    isOpen\n  } = useMediaQuery();\n  const router = useRouter();\n  const {\n    state\n  } = useSidebar();\n  const [navItems, setNavItems] = React.useState<NavItem[]>([]);\n  React.useEffect(() => {\n    // Get user role and set appropriate navigation items\n    const user = authStorage.getUser();\n    if (user) {\n      // Temporary: If user is student, use fallback navigation items\n      // This is for demonstration or specific testing purposes.\n      // In a production scenario, you would typically use role-based navigation.\n      if (user.role === 'student') {\n        const fallbackNavItems = getNavItems(pathname); // (Temporary)\n        setNavItems(fallbackNavItems); // (Temporary)\n      } else {\n        const roleNavItems = getNavigationItems(user.role);\n        setNavItems(roleNavItems);\n      }\n    } else {\n      // Use fallback navigation items if no user is found\n      const fallbackNavItems = getNavItems(pathname);\n      setNavItems(fallbackNavItems);\n    }\n  }, [pathname]);\n  const displayNavItems = navItems.length > 0 ? navItems : getNavItems(pathname);\n  return <Sidebar collapsible='icon' data-sentry-element=\"Sidebar\" data-sentry-component=\"AppSidebar\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n      <SidebarHeader className=\"p-4\" data-sentry-element=\"SidebarHeader\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n        <div className=\"flex items-center justify-center\">\r\n          <Link href=\"/\" data-sentry-element=\"Link\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n            <Image src=\"/assets/logo-iai.png\" alt=\"IAI Logo\" width={state === 'collapsed' ? 48 : 160} height={state === 'collapsed' ? 48 : 60} className=\"object-contain transition-all duration-200 cursor-pointer hover:opacity-80\" priority data-sentry-element=\"Image\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n          </Link>\r\n        </div>\r\n        <div className={`mt-3 ${state === 'collapsed' ? 'hidden' : 'block'}`}>\r\n          <RoleIndicator data-sentry-element=\"RoleIndicator\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n        </div>\r\n      </SidebarHeader>\r\n      <SidebarContent className='overflow-x-hidden' data-sentry-element=\"SidebarContent\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n        <SidebarGroup data-sentry-element=\"SidebarGroup\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n          <SidebarGroupLabel data-sentry-element=\"SidebarGroupLabel\" data-sentry-source-file=\"app-sidebar.tsx\">Overview</SidebarGroupLabel>\r\n          <SidebarMenu className=\"gap-0\" data-sentry-element=\"SidebarMenu\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n            {displayNavItems.map((item: NavItem) => {\n            const Icon = item.icon ? Icons[item.icon] : Icons.logo;\n            return item?.items && item?.items?.length > 0 ? <Collapsible key={item.title} asChild defaultOpen={item.isActive} className='group/collapsible'>\r\n                  <SidebarMenuItem>\r\n                    <CollapsibleTrigger asChild>\r\n                      <SidebarMenuButton tooltip={item.title} isActive={item.items?.some(subItem => pathname === subItem.url)} className=\"py-3 px-4 text-base font-medium h-auto\">\r\n                        {item.icon && <Icon />}\r\n                        <span>{item.title}</span>\r\n                        <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />\r\n                      </SidebarMenuButton>\r\n                    </CollapsibleTrigger>\r\n                    <CollapsibleContent>\r\n                      <SidebarMenuSub>\r\n                        {item.items?.map((subItem: NavItem) => <SidebarMenuSubItem key={subItem.title}>\r\n                            <SidebarMenuSubButton asChild isActive={pathname === subItem.url} className=\"py-2 px-6 text-sm font-medium\">\r\n                              <Link href={subItem.url}>\r\n                                <span>{subItem.title}</span>\r\n                              </Link>\r\n                            </SidebarMenuSubButton>\r\n                          </SidebarMenuSubItem>)}\r\n                      </SidebarMenuSub>\r\n                    </CollapsibleContent>\r\n                  </SidebarMenuItem>\r\n                </Collapsible> : <SidebarMenuItem key={item.title}>\r\n                  <SidebarMenuButton asChild tooltip={item.title} isActive={pathname === item.url} className=\"py-3 px-4 text-base font-medium h-auto\">\r\n                    <Link href={item.url}>\r\n                      <Icon />\r\n                      <span>{item.title}</span>\r\n                    </Link>\r\n                  </SidebarMenuButton>\r\n                </SidebarMenuItem>;\n          })}\r\n          </SidebarMenu>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter data-sentry-element=\"SidebarFooter\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n        <SidebarMenu data-sentry-element=\"SidebarMenu\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n          <SidebarMenuItem data-sentry-element=\"SidebarMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n            <DropdownMenu data-sentry-element=\"DropdownMenu\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n              <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                <SidebarMenuButton size='lg' className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground py-3 px-4 text-base font-medium h-auto' data-sentry-element=\"SidebarMenuButton\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <UserAvatarProfile className='h-8 w-8 rounded-lg' showInfo data-sentry-element=\"UserAvatarProfile\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                  <Icons.chevronRight className='ml-auto size-4' data-sentry-element=\"Icons.chevronRight\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                </SidebarMenuButton>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent className='w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg' side='bottom' align='end' sideOffset={4} data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                <DropdownMenuLabel className='p-0 font-normal' data-sentry-element=\"DropdownMenuLabel\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <div className='px-1 py-1.5'>\r\n                    <UserAvatarProfile className='h-8 w-8 rounded-lg' showInfo data-sentry-element=\"UserAvatarProfile\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                  </div>\r\n                </DropdownMenuLabel>\r\n                <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n\r\n                <DropdownMenuGroup data-sentry-element=\"DropdownMenuGroup\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <DropdownMenuItem onClick={() => router.push('/dashboard/profile')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                    <Icons.user className='mr-2 h-4 w-4' data-sentry-element=\"Icons.user\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                    Profile\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                    <Icons.billing className='mr-2 h-4 w-4' data-sentry-element=\"Icons.billing\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                    Billing\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                    <Icons.help className='mr-2 h-4 w-4' data-sentry-element=\"Icons.help\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                    Notifications\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuGroup>\r\n                <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                <DropdownMenuItem onClick={() => {\n                authStorage.removeUser();\n                window.location.href = '/auth/sign-in';\n              }} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <LogOut className='mr-2 h-4 w-4' data-sentry-element=\"LogOut\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                  <span>Logout</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarFooter>\r\n      <SidebarRail data-sentry-element=\"SidebarRail\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n    </Sidebar>;\n}", "'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { AuthUser } from '@/types/database';\r\nimport { authStorage } from '@/lib/auth';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nexport function useAuth() {\r\n  const [user, setUser] = useState<AuthUser | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const storedUser = authStorage.getUser();\r\n    if (storedUser) {\r\n      setUser(storedUser);\r\n    }\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  const signOut = () => {\r\n    authStorage.removeUser();\r\n    setUser(null);\r\n    router.push('/auth/sign-in');\r\n  };\r\n\r\n  return { user, loading, signOut };\r\n}\r\n", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Input({\n  className,\n  type,\n  ...props\n}: React.ComponentProps<'input'>) {\n  return <input type={type} data-slot='input' className={cn('file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', 'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]', 'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive', className)} {...props} data-sentry-component=\"Input\" data-sentry-source-file=\"input.tsx\" />;\n}\nexport { Input };", "'use client';\n\nimport * as React from 'react';\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\nimport { cn } from '@/lib/utils';\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return <AvatarPrimitive.Root data-slot='avatar' className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)} {...props} data-sentry-element=\"AvatarPrimitive.Root\" data-sentry-component=\"Avatar\" data-sentry-source-file=\"avatar.tsx\" />;\n}\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return <AvatarPrimitive.Image data-slot='avatar-image' className={cn('aspect-square size-full', className)} {...props} data-sentry-element=\"AvatarPrimitive.Image\" data-sentry-component=\"AvatarImage\" data-sentry-source-file=\"avatar.tsx\" />;\n}\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return <AvatarPrimitive.Fallback data-slot='avatar-fallback' className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)} {...props} data-sentry-element=\"AvatarPrimitive.Fallback\" data-sentry-component=\"AvatarFallback\" data-sentry-source-file=\"avatar.tsx\" />;\n}\nexport { Avatar, AvatarImage, AvatarFallback };", "import { cn } from '@/lib/utils';\nfunction Skeleton({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='skeleton' className={cn('bg-accent animate-pulse rounded-md', className)} {...props} data-sentry-component=\"Skeleton\" data-sentry-source-file=\"skeleton.tsx\" />;\n}\nexport { Skeleton };", "import { AuthUser } from '@/types/database';\r\n\r\n// Client-side auth utilities\r\nexport const authStorage = {\r\n  setUser: (user: AuthUser) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_user', JSON.stringify(user));\r\n    }\r\n  },\r\n\r\n  getUser: (): AuthUser | null => {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = localStorage.getItem('auth_user');\r\n      return stored ? JSON.parse(stored) : null;\r\n    }\r\n    return null;\r\n  },\r\n\r\n  removeUser: () => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_user');\r\n    }\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    return authStorage.getUser() !== null;\r\n  },\r\n\r\n  hasRole: (role: string): boolean => {\r\n    const user = authStorage.getUser();\r\n    return user?.role === role;\r\n  },\r\n\r\n  isSuperAdmin: (): boolean => {\r\n    return authStorage.hasRole('super_admin');\r\n  },\r\n\r\n  isTeacher: (): boolean => {\r\n    return authStorage.hasRole('teacher');\r\n  },\r\n\r\n  isStudent: (): boolean => {\r\n    return authStorage.hasRole('student');\r\n  }\r\n};\r\n\r\n// Role-based redirect logic\r\nexport const getRedirectPath = (user: AuthUser): string => {\r\n  switch (user.role) {\r\n    case 'super_admin':\r\n      return '/dashboard/admin';\r\n    case 'teacher':\r\n      return '/dashboard/teacher';\r\n    case 'student':\r\n      return '/courses';\r\n    default:\r\n      return '/dashboard';\r\n  }\r\n};\r\n\r\n// Protected route checker\r\nexport const checkAuth = (): AuthUser | null => {\r\n  const user = authStorage.getUser();\r\n  if (!user) {\r\n    // Redirect to sign in if not authenticated\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = '/auth/sign-in';\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Role-based access control\r\nexport const requireRole = (requiredRole: string): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (user.role !== requiredRole) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Multiple roles checker\r\nexport const requireAnyRole = (roles: string[]): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (!roles.includes(user.role)) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n", "import * as React from 'react';\nconst MOBILE_BREAKPOINT = 768;\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined);\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    };\n    mql.addEventListener('change', onChange);\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    return () => mql.removeEventListener('change', onChange);\n  }, []);\n  return !!isMobile;\n}", "'use client';\n\nimport * as React from 'react';\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Sheet({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot='sheet' {...props} data-sentry-element=\"SheetPrimitive.Root\" data-sentry-component=\"Sheet\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot='sheet-trigger' {...props} data-sentry-element=\"SheetPrimitive.Trigger\" data-sentry-component=\"SheetTrigger\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot='sheet-close' {...props} data-sentry-element=\"SheetPrimitive.Close\" data-sentry-component=\"SheetClose\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot='sheet-portal' {...props} data-sentry-element=\"SheetPrimitive.Portal\" data-sentry-component=\"SheetPortal\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return <SheetPrimitive.Overlay data-slot='sheet-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"SheetPrimitive.Overlay\" data-sentry-component=\"SheetOverlay\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetContent({\n  className,\n  children,\n  side = 'right',\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: 'top' | 'right' | 'bottom' | 'left';\n}) {\n  return <SheetPortal data-sentry-element=\"SheetPortal\" data-sentry-component=\"SheetContent\" data-sentry-source-file=\"sheet.tsx\">\r\n      <SheetOverlay data-sentry-element=\"SheetOverlay\" data-sentry-source-file=\"sheet.tsx\" />\r\n      <SheetPrimitive.Content data-slot='sheet-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500', side === 'right' && 'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm', side === 'left' && 'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm', side === 'top' && 'data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b', side === 'bottom' && 'data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t', className)} {...props} data-sentry-element=\"SheetPrimitive.Content\" data-sentry-source-file=\"sheet.tsx\">\r\n        {children}\r\n        <SheetPrimitive.Close className='ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none' data-sentry-element=\"SheetPrimitive.Close\" data-sentry-source-file=\"sheet.tsx\">\r\n          <XIcon className='size-4' data-sentry-element=\"XIcon\" data-sentry-source-file=\"sheet.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>;\n}\nfunction SheetHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sheet-header' className={cn('flex flex-col gap-1.5 p-4', className)} {...props} data-sentry-component=\"SheetHeader\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sheet-footer' className={cn('mt-auto flex flex-col gap-2 p-4', className)} {...props} data-sentry-component=\"SheetFooter\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return <SheetPrimitive.Title data-slot='sheet-title' className={cn('text-foreground font-semibold', className)} {...props} data-sentry-element=\"SheetPrimitive.Title\" data-sentry-component=\"SheetTitle\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return <SheetPrimitive.Description data-slot='sheet-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"SheetPrimitive.Description\" data-sentry-component=\"SheetDescription\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nexport { Sheet, SheetTrigger, SheetClose, SheetContent, SheetHeader, SheetFooter, SheetTitle, SheetDescription };", "'use client';\n\nimport * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { VariantProps, cva } from 'class-variance-authority';\nimport { PanelLeftIcon } from 'lucide-react';\nimport { useIsMobile } from '@/hooks/use-mobile';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Separator } from '@/components/ui/separator';\nimport { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\nconst SIDEBAR_COOKIE_NAME = 'sidebar_state';\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = '16rem';\nconst SIDEBAR_WIDTH_MOBILE = '18rem';\nconst SIDEBAR_WIDTH_ICON = '3rem';\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b';\ntype SidebarContextProps = {\n  state: 'expanded' | 'collapsed';\n  open: boolean;\n  setOpen: (open: boolean) => void;\n  openMobile: boolean;\n  setOpenMobile: (open: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n};\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext);\n  if (!context) {\n    throw new Error('useSidebar must be used within a SidebarProvider.');\n  }\n  return context;\n}\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<'div'> & {\n  defaultOpen?: boolean;\n  open?: boolean;\n  onOpenChange?: (open: boolean) => void;\n}) {\n  const isMobile = useIsMobile();\n  const [openMobile, setOpenMobile] = React.useState(false);\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen);\n  const open = openProp ?? _open;\n  const setOpen = React.useCallback((value: boolean | ((value: boolean) => boolean)) => {\n    const openState = typeof value === 'function' ? value(open) : value;\n    if (setOpenProp) {\n      setOpenProp(openState);\n    } else {\n      _setOpen(openState);\n    }\n\n    // This sets the cookie to keep the sidebar state.\n    document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\n  }, [setOpenProp, open]);\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile(open => !open) : setOpen(open => !open);\n  }, [isMobile, setOpen, setOpenMobile]);\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n        event.preventDefault();\n        toggleSidebar();\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [toggleSidebar]);\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? 'expanded' : 'collapsed';\n  const contextValue = React.useMemo<SidebarContextProps>(() => ({\n    state,\n    open,\n    setOpen,\n    isMobile,\n    openMobile,\n    setOpenMobile,\n    toggleSidebar\n  }), [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]);\n  return <SidebarContext.Provider value={contextValue} data-sentry-element=\"SidebarContext.Provider\" data-sentry-component=\"SidebarProvider\" data-sentry-source-file=\"sidebar.tsx\">\r\n      <TooltipProvider delayDuration={0} data-sentry-element=\"TooltipProvider\" data-sentry-source-file=\"sidebar.tsx\">\r\n        <div data-slot='sidebar-wrapper' style={{\n        '--sidebar-width': SIDEBAR_WIDTH,\n        '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\n        ...style\n      } as React.CSSProperties} className={cn('group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full', className)} {...props}>\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>;\n}\nfunction Sidebar({\n  side = 'left',\n  variant = 'sidebar',\n  collapsible = 'offcanvas',\n  className,\n  children,\n  ...props\n}: React.ComponentProps<'div'> & {\n  side?: 'left' | 'right';\n  variant?: 'sidebar' | 'floating' | 'inset';\n  collapsible?: 'offcanvas' | 'icon' | 'none';\n}) {\n  const {\n    isMobile,\n    state,\n    openMobile,\n    setOpenMobile\n  } = useSidebar();\n  if (collapsible === 'none') {\n    return <div data-slot='sidebar' className={cn('bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col', className)} {...props}>\r\n        {children}\r\n      </div>;\n  }\n  if (isMobile) {\n    return <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent data-sidebar='sidebar' data-slot='sidebar' data-mobile='true' className='bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden' style={{\n        '--sidebar-width': SIDEBAR_WIDTH_MOBILE\n      } as React.CSSProperties} side={side}>\r\n          <SheetHeader className='sr-only'>\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className='flex h-full w-full flex-col'>{children}</div>\r\n        </SheetContent>\r\n      </Sheet>;\n  }\n  return <div className='group peer text-sidebar-foreground hidden md:block' data-state={state} data-collapsible={state === 'collapsed' ? collapsible : ''} data-variant={variant} data-side={side} data-slot='sidebar' data-sentry-component=\"Sidebar\" data-sentry-source-file=\"sidebar.tsx\">\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div data-slot='sidebar-gap' className={cn('relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear', 'group-data-[collapsible=offcanvas]:w-0', 'group-data-[side=right]:rotate-180', variant === 'floating' || variant === 'inset' ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]' : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon)')} />\r\n      <div data-slot='sidebar-container' className={cn('fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex', side === 'left' ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]' : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\n    // Adjust the padding for floating and inset variants.\n    variant === 'floating' || variant === 'inset' ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]' : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l', className)} {...props}>\r\n        <div data-sidebar='sidebar' data-slot='sidebar-inner' className='bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm'>\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>;\n}\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const {\n    toggleSidebar\n  } = useSidebar();\n  return <Button data-sidebar='trigger' data-slot='sidebar-trigger' variant='ghost' size='icon' className={cn('size-7', className)} onClick={event => {\n    onClick?.(event);\n    toggleSidebar();\n  }} {...props} data-sentry-element=\"Button\" data-sentry-component=\"SidebarTrigger\" data-sentry-source-file=\"sidebar.tsx\">\r\n      <PanelLeftIcon data-sentry-element=\"PanelLeftIcon\" data-sentry-source-file=\"sidebar.tsx\" />\r\n      <span className='sr-only'>Toggle Sidebar</span>\r\n    </Button>;\n}\nfunction SidebarRail({\n  className,\n  ...props\n}: React.ComponentProps<'button'>) {\n  const {\n    toggleSidebar\n  } = useSidebar();\n  return <button data-sidebar='rail' data-slot='sidebar-rail' aria-label='Toggle Sidebar' tabIndex={-1} onClick={toggleSidebar} title='Toggle Sidebar' className={cn('hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex', 'in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize', '[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize', 'hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full', '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2', '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2', className)} {...props} data-sentry-component=\"SidebarRail\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarInset({\n  className,\n  ...props\n}: React.ComponentProps<'main'>) {\n  return <main data-slot='sidebar-inset' className={cn('bg-background relative flex w-full flex-1 flex-col', 'md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2', className)} {...props} data-sentry-component=\"SidebarInset\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return <Input data-slot='sidebar-input' data-sidebar='input' className={cn('bg-background h-8 w-full shadow-none', className)} {...props} data-sentry-element=\"Input\" data-sentry-component=\"SidebarInput\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-header' data-sidebar='header' className={cn('flex flex-col gap-2 p-2', className)} {...props} data-sentry-component=\"SidebarHeader\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-footer' data-sidebar='footer' className={cn('flex flex-col gap-2 p-2', className)} {...props} data-sentry-component=\"SidebarFooter\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return <Separator data-slot='sidebar-separator' data-sidebar='separator' className={cn('bg-sidebar-border mx-2 w-auto', className)} {...props} data-sentry-element=\"Separator\" data-sentry-component=\"SidebarSeparator\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-content' data-sidebar='content' className={cn('flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden', className)} {...props} data-sentry-component=\"SidebarContent\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroup({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-group' data-sidebar='group' className={cn('relative flex w-full min-w-0 flex-col p-2', className)} {...props} data-sentry-component=\"SidebarGroup\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'div'> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'div';\n  return <Comp data-slot='sidebar-group-label' data-sidebar='group-label' className={cn('text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0', 'group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarGroupLabel\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='sidebar-group-action' data-sidebar='group-action' className={cn('text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\n  // Increases the hit area of the button on mobile.\n  'after:absolute after:-inset-2 md:after:hidden', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarGroupAction\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-group-content' data-sidebar='group-content' className={cn('w-full text-sm', className)} {...props} data-sentry-component=\"SidebarGroupContent\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenu({\n  className,\n  ...props\n}: React.ComponentProps<'ul'>) {\n  return <ul data-slot='sidebar-menu' data-sidebar='menu' className={cn('flex w-full min-w-0 flex-col gap-1', className)} {...props} data-sentry-component=\"SidebarMenu\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='sidebar-menu-item' data-sidebar='menu-item' className={cn('group/menu-item relative', className)} {...props} data-sentry-component=\"SidebarMenuItem\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nconst sidebarMenuButtonVariants = cva('peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer', {\n  variants: {\n    variant: {\n      default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\n      outline: 'bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]'\n    },\n    size: {\n      default: 'h-8 text-sm',\n      sm: 'h-7 text-xs',\n      lg: 'h-12 text-sm group-data-[collapsible=icon]:p-0!'\n    }\n  },\n  defaultVariants: {\n    variant: 'default',\n    size: 'default'\n  }\n});\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = 'default',\n  size = 'default',\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<'button'> & {\n  asChild?: boolean;\n  isActive?: boolean;\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : 'button';\n  const {\n    isMobile,\n    state\n  } = useSidebar();\n  const button = <Comp data-slot='sidebar-menu-button' data-sidebar='menu-button' data-size={size} data-active={isActive} className={cn(sidebarMenuButtonVariants({\n    variant,\n    size\n  }), className)} {...props} />;\n  if (!tooltip) {\n    return button;\n  }\n  if (typeof tooltip === 'string') {\n    tooltip = {\n      children: tooltip\n    };\n  }\n  return <Tooltip data-sentry-element=\"Tooltip\" data-sentry-component=\"SidebarMenuButton\" data-sentry-source-file=\"sidebar.tsx\">\r\n      <TooltipTrigger asChild data-sentry-element=\"TooltipTrigger\" data-sentry-source-file=\"sidebar.tsx\">{button}</TooltipTrigger>\r\n      <TooltipContent side='right' align='center' hidden={state !== 'collapsed' || isMobile} {...tooltip} data-sentry-element=\"TooltipContent\" data-sentry-source-file=\"sidebar.tsx\" />\r\n    </Tooltip>;\n}\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<'button'> & {\n  asChild?: boolean;\n  showOnHover?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='sidebar-menu-action' data-sidebar='menu-action' className={cn('text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\n  // Increases the hit area of the button on mobile.\n  'after:absolute after:-inset-2 md:after:hidden', 'peer-data-[size=sm]/menu-button:top-1', 'peer-data-[size=default]/menu-button:top-1.5', 'peer-data-[size=lg]/menu-button:top-2.5', 'group-data-[collapsible=icon]:hidden', showOnHover && 'peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarMenuAction\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-menu-badge' data-sidebar='menu-badge' className={cn('text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none', 'peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground', 'peer-data-[size=sm]/menu-button:top-1', 'peer-data-[size=default]/menu-button:top-1.5', 'peer-data-[size=lg]/menu-button:top-2.5', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-component=\"SidebarMenuBadge\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<'div'> & {\n  showIcon?: boolean;\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`;\n  }, []);\n  return <div data-slot='sidebar-menu-skeleton' data-sidebar='menu-skeleton' className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)} {...props} data-sentry-component=\"SidebarMenuSkeleton\" data-sentry-source-file=\"sidebar.tsx\">\r\n      {showIcon && <Skeleton className='size-4 rounded-md' data-sidebar='menu-skeleton-icon' />}\r\n      <Skeleton className='h-4 max-w-(--skeleton-width) flex-1' data-sidebar='menu-skeleton-text' style={{\n      '--skeleton-width': width\n    } as React.CSSProperties} data-sentry-element=\"Skeleton\" data-sentry-source-file=\"sidebar.tsx\" />\r\n    </div>;\n}\nfunction SidebarMenuSub({\n  className,\n  ...props\n}: React.ComponentProps<'ul'>) {\n  return <ul data-slot='sidebar-menu-sub' data-sidebar='menu-sub' className={cn('border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-component=\"SidebarMenuSub\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='sidebar-menu-sub-item' data-sidebar='menu-sub-item' className={cn('group/menu-sub-item relative', className)} {...props} data-sentry-component=\"SidebarMenuSubItem\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = 'md',\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<'a'> & {\n  asChild?: boolean;\n  size?: 'sm' | 'md';\n  isActive?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'a';\n  return <Comp data-slot='sidebar-menu-sub-button' data-sidebar='menu-sub-button' data-size={size} data-active={isActive} className={cn('text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0', 'data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground', size === 'sm' && 'text-xs', size === 'md' && 'text-sm', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarMenuSubButton\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nexport { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupAction, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarInput, SidebarInset, SidebarMenu, SidebarMenuAction, SidebarMenuBadge, SidebarMenuButton, SidebarMenuItem, SidebarMenuSkeleton, SidebarMenuSub, SidebarMenuSubButton, SidebarMenuSubItem, SidebarProvider, SidebarRail, SidebarSeparator, SidebarTrigger, useSidebar };", "import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { useAuth } from '@/hooks/use-auth';\ninterface UserAvatarProfileProps {\n  className?: string;\n  showInfo?: boolean;\n}\nexport function UserAvatarProfile({\n  className,\n  showInfo = false\n}: UserAvatarProfileProps) {\n  const {\n    user\n  } = useAuth();\n  if (!user) {\n    return <Avatar className={className}>\r\n        <AvatarFallback>G</AvatarFallback>\r\n      </Avatar>;\n  }\n  return <div className='flex items-center gap-2' data-sentry-component=\"UserAvatarProfile\" data-sentry-source-file=\"user-avatar-profile.tsx\">\r\n      <Avatar className={className} data-sentry-element=\"Avatar\" data-sentry-source-file=\"user-avatar-profile.tsx\">\r\n        <AvatarImage src={`https://ui-avatars.com/api/?name=${user.name}&background=random`} alt={user.name} data-sentry-element=\"AvatarImage\" data-sentry-source-file=\"user-avatar-profile.tsx\" />\r\n        <AvatarFallback className='rounded-lg' data-sentry-element=\"AvatarFallback\" data-sentry-source-file=\"user-avatar-profile.tsx\">\r\n          {user.name?.slice(0, 2)?.toUpperCase() || 'U'}\r\n        </AvatarFallback>\r\n      </Avatar>\r\n\r\n      {showInfo && <div className='grid flex-1 text-left text-sm leading-tight'>\r\n          <span className='truncate font-semibold'>{user.name}</span>\r\n          <span className='truncate text-xs'>{user.email}</span>\r\n        </div>}\r\n    </div>;\n}", "\"use client\";\n\nimport * as React from \"react\";\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\";\nimport { cn } from \"@/lib/utils\";\nconst Separator = React.forwardRef<React.ElementRef<typeof SeparatorPrimitive.Root>, React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>>(({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}, ref) => <SeparatorPrimitive.Root ref={ref} decorative={decorative} orientation={orientation} className={cn(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className)} {...props} />);\nSeparator.displayName = SeparatorPrimitive.Root.displayName;\nexport { Separator };", "'use client';\n\nimport * as React from 'react';\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot='dropdown-menu' {...props} data-sentry-element=\"DropdownMenuPrimitive.Root\" data-sentry-component=\"DropdownMenu\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return <DropdownMenuPrimitive.Portal data-slot='dropdown-menu-portal' {...props} data-sentry-element=\"DropdownMenuPrimitive.Portal\" data-sentry-component=\"DropdownMenuPortal\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return <DropdownMenuPrimitive.Trigger data-slot='dropdown-menu-trigger' {...props} data-sentry-element=\"DropdownMenuPrimitive.Trigger\" data-sentry-component=\"DropdownMenuTrigger\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return <DropdownMenuPrimitive.Portal data-sentry-element=\"DropdownMenuPrimitive.Portal\" data-sentry-component=\"DropdownMenuContent\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      <DropdownMenuPrimitive.Content data-slot='dropdown-menu-content' sideOffset={sideOffset} className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Content\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n    </DropdownMenuPrimitive.Portal>;\n}\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return <DropdownMenuPrimitive.Group data-slot='dropdown-menu-group' {...props} data-sentry-element=\"DropdownMenuPrimitive.Group\" data-sentry-component=\"DropdownMenuGroup\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = 'default',\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean;\n  variant?: 'default' | 'destructive';\n}) {\n  return <DropdownMenuPrimitive.Item data-slot='dropdown-menu-item' data-inset={inset} data-variant={variant} className={cn(\"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground focus:[&_svg:not([class*='text-'])]:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Item\" data-sentry-component=\"DropdownMenuItem\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return <DropdownMenuPrimitive.CheckboxItem data-slot='dropdown-menu-checkbox-item' className={cn(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} checked={checked} {...props} data-sentry-element=\"DropdownMenuPrimitive.CheckboxItem\" data-sentry-component=\"DropdownMenuCheckboxItem\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator data-sentry-element=\"DropdownMenuPrimitive.ItemIndicator\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n          <CheckIcon className='size-4' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>;\n}\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return <DropdownMenuPrimitive.RadioGroup data-slot='dropdown-menu-radio-group' {...props} data-sentry-element=\"DropdownMenuPrimitive.RadioGroup\" data-sentry-component=\"DropdownMenuRadioGroup\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return <DropdownMenuPrimitive.RadioItem data-slot='dropdown-menu-radio-item' className={cn(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.RadioItem\" data-sentry-component=\"DropdownMenuRadioItem\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator data-sentry-element=\"DropdownMenuPrimitive.ItemIndicator\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n          <CircleIcon className='size-2 fill-current' data-sentry-element=\"CircleIcon\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>;\n}\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean;\n}) {\n  return <DropdownMenuPrimitive.Label data-slot='dropdown-menu-label' data-inset={inset} className={cn('px-2 py-1.5 text-sm font-medium data-[inset]:pl-8', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Label\" data-sentry-component=\"DropdownMenuLabel\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return <DropdownMenuPrimitive.Separator data-slot='dropdown-menu-separator' className={cn('bg-border -mx-1 my-1 h-px', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Separator\" data-sentry-component=\"DropdownMenuSeparator\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) {\n  return <span data-slot='dropdown-menu-shortcut' className={cn('text-muted-foreground ml-auto text-xs tracking-widest', className)} {...props} data-sentry-component=\"DropdownMenuShortcut\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot='dropdown-menu-sub' {...props} data-sentry-element=\"DropdownMenuPrimitive.Sub\" data-sentry-component=\"DropdownMenuSub\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean;\n}) {\n  return <DropdownMenuPrimitive.SubTrigger data-slot='dropdown-menu-sub-trigger' data-inset={inset} className={cn('focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.SubTrigger\" data-sentry-component=\"DropdownMenuSubTrigger\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      {children}\r\n      <ChevronRightIcon className='ml-auto size-4' data-sentry-element=\"ChevronRightIcon\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n    </DropdownMenuPrimitive.SubTrigger>;\n}\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return <DropdownMenuPrimitive.SubContent data-slot='dropdown-menu-sub-content' className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.SubContent\" data-sentry-component=\"DropdownMenuSubContent\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nexport { DropdownMenu, DropdownMenuPortal, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuGroup, DropdownMenuLabel, DropdownMenuItem, DropdownMenuCheckboxItem, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuSub, DropdownMenuSubTrigger, DropdownMenuSubContent };", "import { NavItem } from '@/types';\r\n\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n//Info: The following data is used for the sidebar navigation and Cmd K bar.\r\nexport const getNavItems = (pathname: string): NavItem[] => [\r\n  {\r\n    title: 'Available Courses',\r\n    url: '/courses',\r\n    icon: 'searchList',\r\n    isActive: pathname.startsWith('/courses'),\r\n    shortcut: ['a', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  },\r\n  {\r\n    title: 'My Courses',\r\n    url: '/my-courses',\r\n    icon: 'graduationCap',\r\n    isActive: pathname.startsWith('/my-courses'),\r\n    shortcut: ['m', 'c'],\r\n    items: [] // Empty array as there are no child items\r\n  }\r\n];\r\n\r\n// Legacy export for backward compatibility\r\nexport const navItems: NavItem[] = getNavItems('');\r\n\r\nexport interface SaleUser {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  amount: string;\r\n  image: string;\r\n  initials: string;\r\n}\r\n\r\nexport const recentSalesData: SaleUser[] = [\r\n  {\r\n    id: 1,\r\n    name: '<PERSON>',\r\n    email: '<EMAIL>',\r\n    amount: '+$1,999.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/1.png',\r\n    initials: 'OM'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Jackson Lee',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/2.png',\r\n    initials: 'JL'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Isabella Nguyen',\r\n    email: '<EMAIL>',\r\n    amount: '+$299.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/3.png',\r\n    initials: 'IN'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'William Kim',\r\n    email: '<EMAIL>',\r\n    amount: '+$99.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/4.png',\r\n    initials: 'WK'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Sofia Davis',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/5.png',\r\n    initials: 'SD'\r\n  }\r\n];\r\n"], "names": ["TooltipProvider", "delayDuration", "props", "TooltipPrimitive", "data-slot", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "sideOffset", "children", "cn", "UserNav", "router", "useRouter", "user", "signOut", "useAuth", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "variant", "UserAvatarProfile", "DropdownMenuContent", "align", "forceMount", "DropdownMenuLabel", "div", "p", "name", "email", "DropdownMenuSeparator", "DropdownMenuGroup", "DropdownMenuItem", "onClick", "push", "Header", "header", "SidebarTrigger", "size", "User", "span", "Settings", "HelpCircle", "FileText", "ResultItem", "React", "action", "active", "currentRootActionId", "ref", "ancestors", "index", "findIndex", "ancestor", "id", "slice", "icon", "length", "map", "subtitle", "shortcut", "sc", "i", "kbd", "RenderResults", "results", "rootActionId", "useMatches", "KBarResults", "items", "onRender", "item", "displayName", "theme", "useThemeSwitching", "setTheme", "useTheme", "useRegisterActions", "themeAction", "section", "perform", "toggleTheme", "KBar", "actions", "useMemo", "navigateTo", "url", "navItems", "flatMap", "navItem", "baseAction", "title", "toLowerCase", "keywords", "childActions", "childItem", "KBarProvider", "KBarComponent", "KBarPortal", "KBarPositioner", "KBarAnimator", "KBarSearch", "Collapsible", "CollapsiblePrimitive", "CollapsibleTrigger", "Collapsible<PERSON><PERSON>nt", "superAdminNavItems", "isActive", "role", "teacherNavItems", "studentNavItems", "Icons", "dashboard", "LayoutDashboard", "logo", "Command", "login", "LogIn", "close", "X", "product", "ShoppingBag", "spinner", "Loader2", "kanban", "Square", "chevronLeft", "ChevronLeft", "chevronRight", "ChevronRight", "trash", "Trash2", "employee", "UserX", "post", "page", "File", "userPen", "UserCog", "user2", "UserCircle", "media", "Image", "settings", "billing", "CreditCard", "ellipsis", "MoreHorizontal", "add", "Plus", "warning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arrowRight", "ArrowRight", "help", "pizza", "Pizza", "sun", "Sun", "moon", "Moon", "laptop", "Laptop", "github", "<PERSON><PERSON><PERSON>", "twitter", "Twitter", "check", "Check", "building", "Building2", "users", "Users", "userPlus", "UserPlus", "userCheck", "UserCheck", "creditCard", "<PERSON><PERSON><PERSON>", "BarChart3", "bookOpen", "BookOpen", "book", "Book", "bot", "Bot", "trendingUp", "TrendingUp", "award", "Award", "plus", "school", "certificate", "enrollment", "searchList", "SearchIcon", "graduationCap", "GraduationCapIcon", "getRoleDisplay", "label", "GraduationCap", "bgColor", "RoleIndicator", "setUser", "authStorage", "getUser", "currentUser", "roleInfo", "Icon", "SidebarMenu", "SidebarMenuItem", "SidebarMenuButton", "AppSidebar", "pathname", "usePathname", "isOpen", "useMediaQuery", "setIsOpen", "useState", "state", "useSidebar", "setNavItems", "getNavItems", "fallbackNavItems", "getNavigationItems", "roleNavItems", "displayNavItems", "Sidebar", "collapsible", "SidebarHeader", "Link", "href", "src", "alt", "width", "height", "priority", "<PERSON>bar<PERSON><PERSON>nt", "SidebarGroup", "SidebarGroupLabel", "defaultOpen", "tooltip", "some", "subItem", "SidebarMenuSub", "SidebarMenuSubItem", "SidebarMenuSubButton", "SidebarFooter", "showInfo", "side", "removeUser", "window", "location", "LogOut", "SidebarRail", "loading", "setLoading", "Input", "type", "input", "Avatar", "AvatarPrimitive", "AvatarImage", "AvatarFallback", "Skeleton", "isAuthenticated", "hasRole", "isSuperAdmin", "<PERSON><PERSON><PERSON>er", "isStudent", "checkAuth", "requiredRole", "Sheet", "SheetPrimitive", "SheetPort<PERSON>", "SheetOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SheetDescription", "SidebarContext", "context", "Error", "SidebarProvider", "open", "openProp", "onOpenChange", "setOpenProp", "style", "isMobile", "useIsMobile", "setIsMobile", "undefined", "mql", "matchMedia", "MOBILE_BREAKPOINT", "onChange", "innerWidth", "addEventListener", "removeEventListener", "openMobile", "setOpenMobile", "_open", "_setOpen", "<PERSON><PERSON><PERSON>", "value", "openState", "document", "cookie", "SIDEBAR_COOKIE_NAME", "SIDEBAR_COOKIE_MAX_AGE", "toggleSidebar", "handleKeyDown", "event", "key", "SIDEBAR_KEYBOARD_SHORTCUT", "ctrl<PERSON>ey", "preventDefault", "contextValue", "Provider", "SIDEBAR_WIDTH", "SIDEBAR_WIDTH_ICON", "data-sidebar", "data-mobile", "SIDEBAR_WIDTH_MOBILE", "data-state", "data-collapsible", "data-variant", "data-side", "PanelLeftIcon", "button", "aria-label", "tabIndex", "SidebarInset", "main", "Comp", "Slot", "ul", "li", "sidebarMenuButtonVariants", "cva", "variants", "default", "outline", "sm", "lg", "defaultVariants", "data-size", "data-active", "hidden", "toUpperCase", "Separator", "orientation", "decorative", "SeparatorPrimitive", "DropdownMenuPrimitive", "inset", "data-inset", "DropdownMenuCheckboxItem", "checked", "CheckIcon", "startsWith"], "sourceRoot": ""}