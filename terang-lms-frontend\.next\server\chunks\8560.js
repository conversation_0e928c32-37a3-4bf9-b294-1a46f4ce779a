try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="06d2b751-4472-47a9-8638-2b4e142ecc81",e._sentryDebugIdIdentifier="sentry-dbid-06d2b751-4472-47a9-8638-2b4e142ecc81")}catch(e){}exports.id=8560,exports.ids=[8560],exports.modules={5434:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(1472).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>a\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\components\\\\kbar\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\components\\kbar\\index.tsx","default")},6294:(e,t,r)=>{"use strict";let o;r.r(t),r.d(t,{default:()=>h,generateImageMetadata:()=>p,generateMetadata:()=>u,generateViewport:()=>f,metadata:()=>d});var s=r(63033),n=r(18188),a=r(5434),l=r(16451),i=r(7688);let d={title:"Course - LMS",description:"Learning Management System - Course View"},c={...s},m="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;o=new Proxy(function({children:e}){return(0,n.jsx)(l.EnrollmentProvider,{"data-sentry-element":"EnrollmentProvider","data-sentry-component":"CourseViewLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsx)(a.default,{"data-sentry-element":"KBar","data-sentry-source-file":"layout.tsx",children:e})})},{apply:(e,t,r)=>{let o,s,n;try{let e=m?.getStore();o=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return i.wrapServerComponentWithSentry(e,{componentRoute:"/(course-view)",componentType:"Layout",sentryTraceHeader:o,baggageHeader:s,headers:n}).apply(t,r)}});let u=void 0,p=void 0,f=void 0,h=o},7346:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var o=r(91754),s=r(93400),n=r(97969),a=r(21372),l=r(93491);let i=l.forwardRef(({action:e,active:t,currentRootActionId:r},s)=>{let n=l.useMemo(()=>{if(!r)return e.ancestors;let t=e.ancestors.findIndex(e=>e.id===r);return e.ancestors.slice(t+1)},[e.ancestors,r]);return(0,o.jsxs)("div",{ref:s,className:"relative z-10 flex cursor-pointer items-center justify-between px-4 py-3",children:[t&&(0,o.jsx)("div",{id:"kbar-result-item",className:"border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4"}),(0,o.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[e.icon&&e.icon,(0,o.jsxs)("div",{className:"flex flex-col",children:[(0,o.jsxs)("div",{children:[n.length>0&&n.map(e=>(0,o.jsxs)(l.Fragment,{children:[(0,o.jsx)("span",{className:"text-muted-foreground mr-2",children:e.name}),(0,o.jsx)("span",{className:"mr-2",children:"›"})]},e.id)),(0,o.jsx)("span",{children:e.name})]}),e.subtitle&&(0,o.jsx)("span",{className:"text-muted-foreground text-sm",children:e.subtitle})]})]}),e.shortcut?.length?(0,o.jsx)("div",{className:"relative z-10 grid grid-flow-col gap-1",children:e.shortcut.map((e,t)=>(0,o.jsx)("kbd",{className:"bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium",children:e},e+t))}):null]})});function d(){let{results:e,rootActionId:t}=(0,n.useMatches)();return(0,o.jsx)(n.KBarResults,{items:e,onRender:({item:e,active:r})=>"string"==typeof e?(0,o.jsx)("div",{className:"text-primary-foreground px-4 py-2 text-sm uppercase opacity-50",children:e}):(0,o.jsx)(i,{action:e,active:r,currentRootActionId:t??""}),"data-sentry-element":"KBarResults","data-sentry-component":"RenderResults","data-sentry-source-file":"render-result.tsx"})}i.displayName="KBarResultItem";var c=r(73233);let m=()=>{let{theme:e,setTheme:t}=(0,c.D)();(0,n.useRegisterActions)([{id:"toggleTheme",name:"Toggle Theme",shortcut:["t","t"],section:"Theme",perform:()=>{t("light"===e?"dark":"light")}},{id:"setLightTheme",name:"Set Light Theme",section:"Theme",perform:()=>t("light")},{id:"setDarkTheme",name:"Set Dark Theme",section:"Theme",perform:()=>t("dark")}],[e])};function u({children:e}){let t=(0,a.useRouter)(),r=(0,l.useMemo)(()=>{let e=e=>{t.push(e)};return s.Cn.flatMap(t=>{let r="#"!==t.url?{id:`${t.title.toLowerCase()}Action`,name:t.title,shortcut:t.shortcut,keywords:t.title.toLowerCase(),section:"Navigation",subtitle:`Go to ${t.title}`,perform:()=>e(t.url)}:null,o=t.items?.map(r=>({id:`${r.title.toLowerCase()}Action`,name:r.title,shortcut:r.shortcut,keywords:r.title.toLowerCase(),section:t.title,subtitle:`Go to ${r.title}`,perform:()=>e(r.url)}))??[];return r?[r,...o]:o})},[t]);return(0,o.jsx)(n.KBarProvider,{actions:r,"data-sentry-element":"KBarProvider","data-sentry-component":"KBar","data-sentry-source-file":"index.tsx",children:(0,o.jsx)(p,{"data-sentry-element":"KBarComponent","data-sentry-source-file":"index.tsx",children:e})})}let p=({children:e})=>(m(),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.KBarPortal,{"data-sentry-element":"KBarPortal","data-sentry-source-file":"index.tsx",children:(0,o.jsx)(n.KBarPositioner,{className:"bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm","data-sentry-element":"KBarPositioner","data-sentry-source-file":"index.tsx",children:(0,o.jsxs)(n.KBarAnimator,{className:"bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg","data-sentry-element":"KBarAnimator","data-sentry-source-file":"index.tsx",children:[(0,o.jsx)("div",{className:"bg-card border-border sticky top-0 z-10 border-b",children:(0,o.jsx)(n.KBarSearch,{className:"bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden","data-sentry-element":"KBarSearch","data-sentry-source-file":"index.tsx"})}),(0,o.jsx)("div",{className:"max-h-[400px]",children:(0,o.jsx)(d,{"data-sentry-element":"RenderResults","data-sentry-source-file":"index.tsx"})})]})})}),e]}))},16451:(e,t,r)=>{"use strict";r.d(t,{EnrollmentProvider:()=>s});var o=r(1472);(0,o.registerClientReference)(function(){throw Error("Attempted to call useEnrollment() from the server but useEnrollment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\contexts\\enrollment-context.tsx","useEnrollment");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call EnrollmentProvider() from the server but EnrollmentProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\contexts\\enrollment-context.tsx","EnrollmentProvider")},37615:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(15349).A)("ArrowLeft01Icon",[["path",{d:"M15 6C15 6 9.00001 10.4189 9 12C8.99999 13.5812 15 18 15 18",stroke:"currentColor",key:"k0"}]])},38569:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,16451))},68451:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(15349).A)("Award01Icon",[["path",{d:"M12 12V18",stroke:"currentColor",key:"k0"}],["path",{d:"M12 18C10.3264 18 8.86971 19.012 8.11766 20.505C7.75846 21.218 8.27389 22 8.95877 22H15.0412C15.7261 22 16.2415 21.218 15.8823 20.505C15.1303 19.012 13.6736 18 12 18Z",stroke:"currentColor",key:"k1"}],["path",{d:"M5 5H3.98471C2.99819 5 2.50493 5 2.20017 5.37053C1.89541 5.74106 1.98478 6.15597 2.16352 6.9858C2.50494 8.57086 3.24548 9.9634 4.2489 11",stroke:"currentColor",key:"k2"}],["path",{d:"M19 5H20.0153C21.0018 5 21.4951 5 21.7998 5.37053C22.1046 5.74106 22.0152 6.15597 21.8365 6.9858C21.4951 8.57086 20.7545 9.9634 19.7511 11",stroke:"currentColor",key:"k3"}],["path",{d:"M12 12C15.866 12 19 8.8831 19 5.03821C19 4.93739 18.9978 4.83707 18.9936 4.73729C18.9509 3.73806 18.9295 3.23845 18.2523 2.61922C17.5751 2 16.8247 2 15.324 2H8.67596C7.17526 2 6.42492 2 5.74772 2.61922C5.07051 3.23844 5.04915 3.73806 5.00642 4.73729C5.00215 4.83707 5 4.93739 5 5.03821C5 8.8831 8.13401 12 12 12Z",stroke:"currentColor",key:"k4"}]])},74829:(e,t,r)=>{"use strict";r.d(t,{EnrollmentProvider:()=>i,q:()=>l});var o=r(91754),s=r(93491),n=r(9695);let a=(0,s.createContext)(void 0),l=()=>{let e=(0,s.useContext)(a);if(!e)throw Error("useEnrollment must be used within an EnrollmentProvider");return e},i=({children:e})=>{let[t,r]=(0,s.useState)(!1),[l,i]=(0,s.useState)(n.n4),[d,c]=(0,s.useState)([]),m="lms-enrollment-data",u="lms-multiple-enrollment-data";(0,s.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(u);if(e){let t=JSON.parse(e);Date.now()<t.expirationTime?(c(t.enrolledCourses),r(t.enrolledCourses.length>0),t.enrolledCourses.length>0&&i(t.enrolledCourses[0])):localStorage.removeItem(u);return}let t=localStorage.getItem(m);if(t){let e=JSON.parse(t);if(Date.now()<e.expirationTime){r(e.isEnrolled),i(e.courseData),c([e.courseData]);let t={enrolledCourses:[e.courseData],enrollmentTimestamp:e.enrollmentTimestamp,expirationTime:e.expirationTime};localStorage.setItem(u,JSON.stringify(t)),localStorage.removeItem(m)}else localStorage.removeItem(m)}}catch(e){console.error("Failed to load enrollment data:",e),localStorage.removeItem(m),localStorage.removeItem(u)}})()},[]);let p=e=>{let t=Date.now();try{c(r=>{let o,s={enrolledCourses:o=r.some(t=>t.id===e.id)?r.map(t=>t.id===e.id?e:t):[...r,e],enrollmentTimestamp:t,expirationTime:t+6e5};return localStorage.setItem(u,JSON.stringify(s)),o}),setTimeout(()=>{localStorage.removeItem(u),r(!1),c([]),i(n.n4)},6e5)}catch(e){console.error("Failed to persist enrollment data:",e)}};return(0,o.jsx)(a.Provider,{value:{isEnrolled:t,courseData:l,enrollInCourse:()=>{r(!0);let e={...n.n4,status:"in-progress"};i(e),p(e)},enrollInCourseWithPurchase:e=>{r(!0);let t={...e,status:"in-progress",totalProgress:0};i(t),p(t)},updateCourseProgress:e=>{l.id===e.id&&i(e),c(t=>t.map(t=>t.id===e.id?e:t)),t&&p(e)},enrolledCourses:d,isEnrolledInCourse:e=>d.some(t=>t.id===e),getCourseById:e=>d.find(t=>t.id===e)},"data-sentry-element":"EnrollmentContext.Provider","data-sentry-component":"EnrollmentProvider","data-sentry-source-file":"enrollment-context.tsx",children:e})}},80425:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,74829))},93400:(e,t,r)=>{"use strict";r.d(t,{Cn:()=>s,iI:()=>o});let o=e=>[{title:"Available Courses",url:"/courses",icon:"searchList",isActive:e.startsWith("/courses"),shortcut:["a","c"],items:[]},{title:"My Courses",url:"/my-courses",icon:"graduationCap",isActive:e.startsWith("/my-courses"),shortcut:["m","c"],items:[]}],s=o("")}};
//# sourceMappingURL=8560.js.map