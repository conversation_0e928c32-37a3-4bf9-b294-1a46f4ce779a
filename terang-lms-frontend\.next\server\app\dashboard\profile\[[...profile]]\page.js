try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ca4ab46d-808a-432d-9b6d-8c529592c764",e._sentryDebugIdIdentifier="sentry-dbid-ca4ab46d-808a-432d-9b6d-8c529592c764")}catch(e){}(()=>{var e={};e.id=3411,e.ids=[3411],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},9260:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var s=r(91754);r(93491);var a=r(82233);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11136:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(1472).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja Daffa\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\features\\\\profile\\\\components\\\\profile-view-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\features\\profile\\components\\profile-view-page.tsx","default")},11794:(e,t,r)=>{Promise.resolve().then(r.bind(r,7346)),Promise.resolve().then(r.bind(r,21444)),Promise.resolve().then(r.bind(r,3033)),Promise.resolve().then(r.bind(r,84436))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21474:(e,t,r)=>{Promise.resolve().then(r.bind(r,11136))},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},39626:(e,t,r)=>{Promise.resolve().then(r.bind(r,57550))},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57550:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(91754),a=r(48071),n=r(56682),i=r(9260),o=r(63457);function d(){let{user:e,loading:t,signOut:r}=(0,a.A)();return t?(0,s.jsx)("div",{children:"Loading..."}):e?(0,s.jsxs)("div",{className:"space-y-6 p-4 sm:p-6 lg:p-8","data-sentry-component":"ProfileViewPage","data-sentry-source-file":"profile-view-page.tsx",children:[(0,s.jsx)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"profile-view-page.tsx",children:(0,s.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"profile-view-page.tsx",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4 text-center sm:flex-row sm:text-left",children:[(0,s.jsxs)(o.Avatar,{className:"h-24 w-24 text-3xl","data-sentry-element":"Avatar","data-sentry-source-file":"profile-view-page.tsx",children:[(0,s.jsx)(o.AvatarImage,{src:`https://ui-avatars.com/api/?name=${e.name}&background=random`,"data-sentry-element":"AvatarImage","data-sentry-source-file":"profile-view-page.tsx"}),(0,s.jsx)(o.AvatarFallback,{"data-sentry-element":"AvatarFallback","data-sentry-source-file":"profile-view-page.tsx",children:e.name.charAt(0)})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(i.ZB,{className:"text-2xl","data-sentry-element":"CardTitle","data-sentry-source-file":"profile-view-page.tsx",children:e.name}),(0,s.jsx)("p",{className:"text-muted-foreground",children:e.email})]})]})})}),(0,s.jsxs)(i.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"profile-view-page.tsx",children:[(0,s.jsx)(i.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"profile-view-page.tsx",children:(0,s.jsx)(i.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"profile-view-page.tsx",children:"Account Details"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"profile-view-page.tsx",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Full Name"}),(0,s.jsx)("p",{children:e.name})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Email address"}),(0,s.jsx)("p",{children:e.email})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Role"}),(0,s.jsx)("p",{className:"capitalize",children:e.role.replace("_"," ")})]}),e.institutionId&&(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"text-muted-foreground text-sm font-medium",children:"Institution ID"}),(0,s.jsx)("p",{children:e.institutionId})]})]}),(0,s.jsx)("div",{className:"flex justify-end pt-4",children:(0,s.jsx)(n.$,{onClick:r,variant:"destructive","data-sentry-element":"Button","data-sentry-source-file":"profile-view-page.tsx",children:"Sign Out"})})]})]})]}):(0,s.jsx)("div",{children:"Please sign in to view your profile."})}},57975:e=>{"use strict";e.exports=require("node:util")},58133:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(95500),a=r(56947),n=r(26052),i=r(13636),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["profile",{children:["[[...profile]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72325)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,60290)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4082)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,26052)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,76679)),"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,98036,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,72309,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e),async e=>(await Promise.resolve().then(r.bind(r,78162))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/profile/[[...profile]]/page",pathname:"/dashboard/profile/[[...profile]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},60290:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>g,generateImageMetadata:()=>y,generateMetadata:()=>h,generateViewport:()=>v,metadata:()=>p});var a=r(63033),n=r(18188),i=r(5434),o=r(45188),d=r(67999),l=r(4590),c=r(23064),u=r(7688);let p={title:"Akademi IAI Dashboard",description:"LMS Sertifikasi Profesional"};async function m({children:e}){let t=await (0,c.UL)(),r=t.get("sidebar_state")?.value==="true";return(0,n.jsx)(i.default,{"data-sentry-element":"KBar","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,n.jsxs)(l.SidebarProvider,{defaultOpen:r,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(o.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,n.jsxs)(l.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,n.jsx)(d.default,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),(0,n.jsx)("main",{className:"h-[calc(100vh-64px)] overflow-y-auto p-4 lg:p-8",children:e})]})]})})}let f={...a},x="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;s=new Proxy(m,{apply:(e,t,r)=>{let s,a,n;try{let e=x?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}});let h=void 0,y=void 0,v=void 0,g=s},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72325:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>x,generateImageMetadata:()=>m,generateMetadata:()=>p,generateViewport:()=>f,metadata:()=>d});var a=r(63033),n=r(18188),i=r(11136),o=r(7688);let d={title:"Dashboard : Profile"};async function l(){return(0,n.jsx)(i.default,{"data-sentry-element":"ProfileViewPage","data-sentry-component":"Page","data-sentry-source-file":"page.tsx"})}let c={...a},u="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;s=new Proxy(l,{apply:(e,t,r)=>{let s,a,n;try{let e=u?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch{}return o.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/profile/[[...profile]]",componentType:"Page",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}});let p=void 0,m=void 0,f=void 0,x=s},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5434)),Promise.resolve().then(r.bind(r,45188)),Promise.resolve().then(r.bind(r,67999)),Promise.resolve().then(r.bind(r,4590))},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5250,7688,881,4836,7969,6483,3077,8428,8134,8634],()=>r(58133));module.exports=s})();
//# sourceMappingURL=page.js.map