try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="35893dbd-7447-425c-9633-65b3c8af720d",e._sentryDebugIdIdentifier="sentry-dbid-35893dbd-7447-425c-9633-65b3c8af720d")}catch(e){}"use strict";(()=>{var e={};e.id=647,e.ids=[647],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69302:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>J,routeModule:()=>v,serverHooks:()=>S,workAsyncStorage:()=>N,workUnitAsyncStorage:()=>P});var r={};s.r(r),s.d(r,{DELETE:()=>w,GET:()=>E,HEAD:()=>y,OPTIONS:()=>b,PATCH:()=>z,POST:()=>x,PUT:()=>g});var o=s(3690),n=s(56947),u=s(75250),d=s(63033),l=s(62187),i=s(18621),a=s(32230),c=s(74683),p=s(37229),m=s(93769),q=s(7688);async function I(e){try{let t=e.nextUrl.searchParams,s=t.get("type"),r=t.get("teacherId"),o=t.get("courseId"),n=t.get("classId"),u=t.get("studentId");if(!r)return l.NextResponse.json({error:"Teacher ID required"},{status:400});if("overview"===s){let e=(await i.db.select({id:a.courses.id}).from(a.courses).where((0,c.eq)(a.courses.teacherId,parseInt(r)))).map(e=>e.id);if(0===e.length)return l.NextResponse.json({totalCourses:0,totalStudents:0,totalClasses:0,averageProgress:0,recentActivity:[]});let t=e.length;await i.db.select({count:(0,p.ll)`count(${p.ll.raw("*")})`.mapWith(Number)}).from(a.studentEnrollments).where((0,c.eq)(a.studentEnrollments.courseId,e[0]));let s=await i.db.selectDistinct({studentId:a.studentEnrollments.studentId}).from(a.studentEnrollments).where((0,c.eq)(a.studentEnrollments.courseId,e[0])),o=await i.db.selectDistinct({classId:a.courseEnrollments.classId}).from(a.courseEnrollments).where((0,c.eq)(a.courseEnrollments.courseId,e[0])),n=await i.db.select({completedAt:a.studentEnrollments.completedAt}).from(a.studentEnrollments).where((0,c.or)(...e.map(e=>(0,c.eq)(a.studentEnrollments.courseId,e)))),u=await i.db.select({id:a.quizAttempts.id,studentName:a.users.name,courseName:a.courses.name,score:a.quizAttempts.score,totalPoints:a.quizAttempts.totalPoints,completedAt:a.quizAttempts.completedAt}).from(a.quizAttempts).leftJoin(a.users,(0,c.eq)(a.quizAttempts.studentId,a.users.id)).leftJoin(a.quizzes,(0,c.eq)(a.quizAttempts.quizId,a.quizzes.id)).leftJoin(a.chapters,(0,c.eq)(a.quizzes.chapterId,a.chapters.id)).leftJoin(a.modules,(0,c.eq)(a.chapters.moduleId,a.modules.id)).leftJoin(a.courses,(0,c.eq)(a.modules.courseId,a.courses.id)).where((0,c.eq)(a.courses.teacherId,parseInt(r))).orderBy((0,m.i)(a.quizAttempts.completedAt)).limit(10),d=n.filter(e=>null!==e.completedAt).length,q=n.length>0?d/n.length*100:0;return l.NextResponse.json({totalCourses:t,totalStudents:s.length,totalClasses:o.length,averageProgress:q,recentActivity:u})}if("course"===s&&o){let e=await i.db.select().from(a.courses).where((0,c.Uo)((0,c.eq)(a.courses.id,parseInt(o)),(0,c.eq)(a.courses.teacherId,parseInt(r)))).limit(1);if(0===e.length)return l.NextResponse.json({error:"Course not found or access denied"},{status:403});let t=await i.db.select({id:a.studentEnrollments.id,studentId:a.studentEnrollments.studentId,studentName:a.users.name,studentEmail:a.users.email,enrolledAt:a.studentEnrollments.enrolledAt,completedAt:a.studentEnrollments.completedAt,finalScore:a.studentEnrollments.finalScore}).from(a.studentEnrollments).leftJoin(a.users,(0,c.eq)(a.studentEnrollments.studentId,a.users.id)).where((0,c.eq)(a.studentEnrollments.courseId,parseInt(o))),s=await i.db.select({studentId:a.quizAttempts.studentId,studentName:a.users.name,quizId:a.quizAttempts.quizId,score:a.quizAttempts.score,totalPoints:a.quizAttempts.totalPoints,completedAt:a.quizAttempts.completedAt,chapterName:a.chapters.name}).from(a.quizAttempts).leftJoin(a.users,(0,c.eq)(a.quizAttempts.studentId,a.users.id)).leftJoin(a.quizzes,(0,c.eq)(a.quizAttempts.quizId,a.quizzes.id)).leftJoin(a.chapters,(0,c.eq)(a.quizzes.chapterId,a.chapters.id)).leftJoin(a.modules,(0,c.eq)(a.chapters.moduleId,a.modules.id)).where((0,c.eq)(a.modules.courseId,parseInt(o))).orderBy((0,m.i)(a.quizAttempts.completedAt)),n=t.length,u=t.filter(e=>null!==e.completedAt).length;return l.NextResponse.json({course:e[0],totalStudents:n,averageProgress:n>0?u/n*100:0,completionRate:n>0?u/n*100:0,enrolledStudents:t,quizPerformance:s})}if("class"===s&&n){let e=await i.db.select().from(a.classes).where((0,c.eq)(a.classes.id,parseInt(n))).limit(1);if(0===e.length)return l.NextResponse.json({error:"Class not found"},{status:404});let t=await i.db.select({id:a.users.id,name:a.users.name,email:a.users.email}).from(a.users).innerJoin(a.studentEnrollments,(0,c.eq)(a.users.id,a.studentEnrollments.studentId)).innerJoin(a.courseEnrollments,(0,c.eq)(a.studentEnrollments.courseId,a.courseEnrollments.courseId)).where((0,c.Uo)((0,c.eq)(a.courseEnrollments.classId,parseInt(n)),(0,c.eq)(a.users.role,"student"))),s=await i.db.select({courseId:a.courseEnrollments.courseId,courseName:a.courses.name,courseCode:a.courses.courseCode,enrolledAt:a.courseEnrollments.enrolledAt}).from(a.courseEnrollments).leftJoin(a.courses,(0,c.eq)(a.courseEnrollments.courseId,a.courses.id)).where((0,c.Uo)((0,c.eq)(a.courseEnrollments.classId,parseInt(n)),(0,c.eq)(a.courses.teacherId,parseInt(r)))),o=await i.db.select({studentId:a.studentEnrollments.studentId,studentName:a.users.name,courseId:a.studentEnrollments.courseId,courseName:a.courses.name,enrolledAt:a.studentEnrollments.enrolledAt,completedAt:a.studentEnrollments.completedAt,finalScore:a.studentEnrollments.finalScore}).from(a.studentEnrollments).leftJoin(a.users,(0,c.eq)(a.studentEnrollments.studentId,a.users.id)).leftJoin(a.courses,(0,c.eq)(a.studentEnrollments.courseId,a.courses.id)).innerJoin(a.courseEnrollments,(0,c.eq)(a.studentEnrollments.courseId,a.courseEnrollments.courseId)).where((0,c.Uo)((0,c.eq)(a.courseEnrollments.classId,parseInt(n)),(0,c.eq)(a.courses.teacherId,parseInt(r))));return l.NextResponse.json({class:e[0],totalStudents:t.length,students:t,enrolledCourses:s,studentProgress:o})}else{if("student"!==s||!u)return l.NextResponse.json({error:"Invalid report type or missing parameters"},{status:400});let e=await i.db.select().from(a.users).where((0,c.Uo)((0,c.eq)(a.users.id,parseInt(u)),(0,c.eq)(a.users.role,"student"))).limit(1);if(0===e.length)return l.NextResponse.json({error:"Student not found"},{status:404});let t=await i.db.select({enrollmentId:a.studentEnrollments.id,courseId:a.studentEnrollments.courseId,courseName:a.courses.name,courseCode:a.courses.courseCode,enrolledAt:a.studentEnrollments.enrolledAt,completedAt:a.studentEnrollments.completedAt,finalScore:a.studentEnrollments.finalScore}).from(a.studentEnrollments).leftJoin(a.courses,(0,c.eq)(a.studentEnrollments.courseId,a.courses.id)).where((0,c.Uo)((0,c.eq)(a.studentEnrollments.studentId,parseInt(u)),(0,c.eq)(a.courses.teacherId,parseInt(r)))),o=await i.db.select({id:a.quizAttempts.id,quizId:a.quizAttempts.quizId,score:a.quizAttempts.score,totalPoints:a.quizAttempts.totalPoints,completedAt:a.quizAttempts.completedAt,chapterName:a.chapters.name,courseName:a.courses.name}).from(a.quizAttempts).leftJoin(a.quizzes,(0,c.eq)(a.quizAttempts.quizId,a.quizzes.id)).leftJoin(a.chapters,(0,c.eq)(a.quizzes.chapterId,a.chapters.id)).leftJoin(a.modules,(0,c.eq)(a.chapters.moduleId,a.modules.id)).leftJoin(a.courses,(0,c.eq)(a.modules.courseId,a.courses.id)).where((0,c.Uo)((0,c.eq)(a.quizAttempts.studentId,parseInt(u)),(0,c.eq)(a.courses.teacherId,parseInt(r)))).orderBy((0,m.i)(a.quizAttempts.completedAt)),n=t.length,d=t.filter(e=>null!==e.completedAt).length,p=o.length,q=p>0?o.reduce((e,t)=>{let s=parseFloat(t.score||"0"),r=parseFloat(t.totalPoints||"1");return e+s/r*100},0)/p:0;return l.NextResponse.json({student:e[0],totalCourses:n,completedCourses:d,averageProgress:n>0?d/n*100:0,totalQuizzes:p,averageQuizScore:q,courseEnrollments:t,quizHistory:o})}}catch(e){return console.error("Error fetching reports:",e),l.NextResponse.json({error:"Internal server error"},{status:500})}}let h={...d},f="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;function A(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,s,r)=>{let o;try{let e=f?.getStore();o=e?.headers}catch{}return q.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/reports",headers:o}).apply(s,r)}})}let E=A(I,"GET"),x=A(void 0,"POST"),g=A(void 0,"PUT"),z=A(void 0,"PATCH"),w=A(void 0,"DELETE"),y=A(void 0,"HEAD"),b=A(void 0,"OPTIONS"),v=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reports/route",pathname:"/api/reports",filename:"route",bundlePath:"app/api/reports/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\reports\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:N,workUnitAsyncStorage:P,serverHooks:S}=v;function J(){return(0,u.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:P})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[5250,7688,8036,138,1617,2957],()=>s(69302));module.exports=r})();
//# sourceMappingURL=route.js.map