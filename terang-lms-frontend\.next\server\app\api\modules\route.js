try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="90fb0633-7977-4faa-a584-45500027635b",e._sentryDebugIdIdentifier="sentry-dbid-90fb0633-7977-4faa-a584-45500027635b")}catch(e){}"use strict";(()=>{var e={};e.id=2797,e.ids=[2797],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64315:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>N,routeModule:()=>j,serverHooks:()=>T,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>P});var s={};t.r(s),t.d(s,{DELETE:()=>I,GET:()=>g,HEAD:()=>b,OPTIONS:()=>E,PATCH:()=>v,POST:()=>y,PUT:()=>w});var o=t(3690),n=t(56947),i=t(75250),u=t(63033),a=t(62187),d=t(18621),p=t(32230),l=t(74683),c=t(7688);async function x(e){try{let r=e.nextUrl.searchParams,t=r.get("courseId"),s=r.get("teacherId");if(!t)return a.NextResponse.json({error:"Course ID required"},{status:400});if(s){let e=await d.db.select().from(p.courses).where((0,l.Uo)((0,l.eq)(p.courses.id,parseInt(t)),(0,l.eq)(p.courses.teacherId,parseInt(s)))).limit(1);if(0===e.length)return a.NextResponse.json({error:"Course not found or access denied"},{status:403})}let o=await d.db.select().from(p.modules).where((0,l.eq)(p.modules.courseId,parseInt(t))),n=await Promise.all(o.map(async e=>{let r=await d.db.select({count:p.chapters.id}).from(p.chapters).where((0,l.eq)(p.chapters.moduleId,e.id));return{...e,chapterCount:r.length}}));return a.NextResponse.json({modules:n})}catch(e){return console.error("Error fetching modules:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let{name:r,description:t,courseId:s,teacherId:o,orderIndex:n}=await e.json();if(!r||!s)return a.NextResponse.json({error:"Name and course ID are required"},{status:400});let i=await d.db.select().from(p.courses).where((0,l.eq)(p.courses.id,s)).limit(1);if(0===i.length)return a.NextResponse.json({error:"Course not found"},{status:404});if(o&&i[0].teacherId!==o)return a.NextResponse.json({error:"Not authorized to add modules to this course"},{status:403});let u=n;if(void 0===u){let e=await d.db.select({orderIndex:p.modules.orderIndex}).from(p.modules).where((0,l.eq)(p.modules.courseId,s));u=e.length>0?Math.max(...e.map(e=>e.orderIndex||0))+1:1}let c=await d.db.insert(p.modules).values({name:r,description:t,courseId:s,orderIndex:u}).returning();return a.NextResponse.json({module:c[0],message:"Module created successfully"},{status:201})}catch(e){return console.error("Error creating module:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let q={...u},m="workUnitAsyncStorage"in q?q.workUnitAsyncStorage:"requestAsyncStorage"in q?q.requestAsyncStorage:void 0;function f(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=m?.getStore();o=e?.headers}catch{}return c.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/modules",headers:o}).apply(t,s)}})}let g=f(x,"GET"),y=f(h,"POST"),w=f(void 0,"PUT"),v=f(void 0,"PATCH"),I=f(void 0,"DELETE"),b=f(void 0,"HEAD"),E=f(void 0,"OPTIONS"),j=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/modules/route",pathname:"/api/modules",filename:"route",bundlePath:"app/api/modules/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\File Kerja Daffa\\GAWEAN\\TERANG_LMS\\terang-lms-frontend\\src\\app\\api\\modules\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:A,workUnitAsyncStorage:P,serverHooks:T}=j;function N(){return(0,i.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:P})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5250,7688,8036,138,1617,2957],()=>t(64315));module.exports=s})();
//# sourceMappingURL=route.js.map