{"version": 3, "file": "../app/api/classes/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,sDCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,4aCOO,eAAeA,EAAIC,CAAoB,EAC5C,GAAI,CAEF,IAAMC,EADeD,EAAQE,KAARF,EAAe,CAACG,EACnBA,UAD+B,CAClBC,GAAG,CAAC,aAEnC,GAAI,CAACH,EACH,OADGA,EAAW,YACPI,CAAaC,IAAI,CAAC,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,sBAAsB,CAAG,CAAEC,MAAAA,CAAQ,GAAI,GAoB3F,IAAMC,EAAoBC,CAhBH,MAAMC,EAAAA,EAAAA,CAC1BC,EAeuBF,IAfjB,CAAC,CACNG,EAAAA,CAAIC,EAAAA,OAAOA,CAACD,EAAE,CACdE,IAAAA,CAAMD,EAAAA,OAAOA,CAACC,IAAI,CAClBC,WAAAA,CAAaF,EAAAA,OAAOA,CAACE,WAAW,CAChCC,aAAAA,CAAeH,EAAAA,OAAOA,CAACG,aAAa,CACpCjB,SAAAA,CAAWc,EAAAA,OAAOA,CAACd,SAAS,CAC5BkB,YAAAA,CAAcJ,EAAAA,OAAOA,CAACI,YAAY,CAClCC,SAAAA,CAAWL,EAAAA,OAAOA,CAACK,SAAS,CAC5BC,SAAAA,CAAWN,EAAAA,OAAOA,CAACM,SAAAA,CACrB,EACCC,IAAI,CAACP,EAAAA,OAAAA,CAAAA,CACLQ,KAAK,CAACC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGT,EAAAA,OAAAA,CAAQd,SAAS,CAAEwB,QAAAA,CAASxB,IAAAA,CAAAA,CAICyB,GAJDzB,CAIK0B,IAAQ,CACnD,GAAGA,CAAG,CACNC,YAAAA,CAAc,EACdC,WAAAA,CAAa,EACf,GAEA,OAAOxB,EAAAA,YAAAA,CAAaC,IAAI,CAAC,CAAEC,OAAAA,EAAS,EAAMQ,OAAAA,CAASL,CAAkB,EACvE,CAAE,MAAOF,EAAO,CAEd,EAFOA,KACPsB,OAAAA,CAAQtB,KAAK,CAAC,0BAA2BA,GAClCH,EADkCG,CAAAA,WAClCH,CAAaC,IAAI,CACtB,CAAEC,OAAAA,EAAS,EAAOC,KAAAA,CAAO,wBAAwB,CACjD,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CAGO,eAAesB,EAAK/B,CAAoB,EAC7C,GAAI,CAEF,GAAM,MAAEgB,CAAI,aAAEC,CAAW,WAAEhB,CAAS,eAAEiB,CAAa,cAAEC,CAAY,CAAE,CADtD,EACyDa,IAAAA,EAD3C1B,IAAI,CAAZN,EAInB,GAAI,CAACgB,GAAQ,CAARA,GAAsB,CAACE,EAC1B,GADYjB,IACLI,EAAAA,EADmBa,EAAe,QAClCb,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,oDAAoD,CAC7D,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAMwB,EAAU,KAAVA,CAAgBrB,EAAAA,EAAAA,CACnBC,MAAM,GACNS,IAAI,CAACY,EAAAA,KAAAA,CAAAA,CACLX,KAAK,CACJY,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CACEX,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGU,EAAAA,KAAAA,CAAMpB,EAAE,CAAEb,GACbuB,CAAAA,EAAAA,EAAAA,CADavB,CACbuB,CAAAA,CAAGU,EAAAA,KAAAA,CAAMhB,aAAa,CAAEA,GACxBM,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,CAAGU,CADqBhB,CAAAA,KACrBgB,CAAME,IAAI,CAAE,aAGlBC,KAAK,CAAC,GAET,GAAIJ,GAAsB,GAAdK,CAARL,KAAc,CAChB,OAAO5B,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,sCAAsC,CAC/C,CAAEC,MAAAA,CAAQ,GAAI,GAKlB,IAAM8B,EAAW,MAAM3B,EAAAA,EAAAA,CACpB4B,MAAM,CAACzB,EAAAA,OAAAA,CAAAA,CACP0B,MAAM,CAAC,CACNzB,IAAAA,GACAC,WAAAA,aACAhB,SAAAA,OACAiB,aAAAA,EACAC,CACF,GACCuB,SAAS,GAEZ,OAAOrC,EAAAA,YAAAA,CAAaC,IAAI,CACtB,CAAEC,OAAAA,CAAS,GAAMoC,KAAAA,CAAOJ,CAAQ,CAAC,EAAE,CAAEK,OAAAA,CAAS,6BAA6B,CAC3E,CAAEnC,MAAAA,CAAQ,GAAI,EAElB,CAAE,MAAOD,EAAO,CAEd,EAFOA,KACPsB,OAAAA,CAAQtB,KAAK,CAAC,wBAAyBA,GAChCH,EADgCG,CAAAA,WAChCH,CAAaC,IAAI,CACtB,CAAEE,KAAAA,CAAO,wBAAwB,CACjC,CAAEC,MAAAA,CAAQ,GAAI,EAElB,CACF,CCjGA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAIH,EAAoB,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,KAAO,CADqB,CAM7B,OAAO,4BAAiC,CAAC,EAAmB,CAC1D,MAAM,GACN,IAFuD,cAErC,CAAE,cAAc,SAClC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACpB,CAAC,CACF,CAF0B,CAMxB,IAAC,EAAM,CAAH,CAAeoC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,0BACA,wBACA,iBACA,kCACA,CAAK,CACL,4IACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://terang-lms-ui/external commonjs2 \"module\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://terang-lms-ui/external commonjs \"require-in-the-middle\"", "webpack://terang-lms-ui/external commonjs2 \"process\"", "webpack://terang-lms-ui/external commonjs2 \"os\"", "webpack://terang-lms-ui/external commonjs2 \"util\"", "webpack://terang-lms-ui/external commonjs2 \"fs\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:child_process\"", "webpack://terang-lms-ui/external commonjs2 \"path\"", "webpack://terang-lms-ui/external commonjs2 \"diagnostics_channel\"", "webpack://terang-lms-ui/external node-commonjs \"node:http\"", "webpack://terang-lms-ui/external node-commonjs \"node:zlib\"", "webpack://terang-lms-ui/external node-commonjs \"node:tls\"", "webpack://terang-lms-ui/external node-commonjs \"node:https\"", "webpack://terang-lms-ui/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://terang-lms-ui/external node-commonjs \"node:os\"", "webpack://terang-lms-ui/external node-commonjs \"node:diagnostics_channel\"", "webpack://terang-lms-ui/external commonjs2 \"crypto\"", "webpack://terang-lms-ui/external commonjs \"import-in-the-middle\"", "webpack://terang-lms-ui/external node-commonjs \"node:stream\"", "webpack://terang-lms-ui/external node-commonjs \"node:util\"", "webpack://terang-lms-ui/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://terang-lms-ui/src/app/api/classes/route.ts", "webpack://terang-lms-ui/sentry-wrapper-module", "webpack://terang-lms-ui/?f836", "webpack://terang-lms-ui/external node-commonjs \"node:fs\"", "webpack://terang-lms-ui/external commonjs2 \"worker_threads\"", "webpack://terang-lms-ui/external commonjs2 \"perf_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:worker_threads\"", "webpack://terang-lms-ui/external node-commonjs \"node:path\"", "webpack://terang-lms-ui/external node-commonjs \"node:net\"", "webpack://terang-lms-ui/external commonjs2 \"url\"", "webpack://terang-lms-ui/external commonjs2 \"child_process\"", "webpack://terang-lms-ui/external node-commonjs \"node:readline\"", "webpack://terang-lms-ui/external commonjs2 \"tty\"", "webpack://terang-lms-ui/external commonjs2 \"async_hooks\"", "webpack://terang-lms-ui/external node-commonjs \"node:inspector\"", "webpack://terang-lms-ui/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { NextRequest, NextResponse } from 'next/server';\r\nimport { db } from '@/lib/db';\r\nimport { classes, users } from '@/lib/db/schema';\r\nimport { eq, and } from 'drizzle-orm';\r\nimport { authStorage } from '@/lib/auth';\r\n\r\n// GET /api/classes - Get all classes for the current teacher\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const teacherId = searchParams.get('teacherId');\r\n    \r\n    if (!teacherId) {\r\n      return NextResponse.json({ success: false, error: 'Teacher ID required' }, { status: 400 });\r\n    }\r\n\r\n    // Get classes for the teacher\r\n    const teacherClasses = await db\r\n      .select({\r\n        id: classes.id,\r\n        name: classes.name,\r\n        description: classes.description,\r\n        institutionId: classes.institutionId,\r\n        teacherId: classes.teacherId,\r\n        coverPicture: classes.coverPicture,\r\n        createdAt: classes.createdAt,\r\n        updatedAt: classes.updatedAt\r\n      })\r\n      .from(classes)\r\n      .where(eq(classes.teacherId, parseInt(teacherId)));\r\n\r\n    // Get student count for each class (this would require a join with student enrollments)\r\n    // For now, we'll return the classes without student count\r\n    const classesWithCounts = teacherClasses.map(cls => ({\r\n      ...cls,\r\n      studentCount: 0, // TODO: Calculate actual student count\r\n      courseCount: 0   // TODO: Calculate actual course count\r\n    }));\r\n\r\n    return NextResponse.json({ success: true, classes: classesWithCounts });\r\n  } catch (error) {\r\n    console.error('Error fetching classes:', error);\r\n    return NextResponse.json(\r\n      { success: false, error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/classes - Create a new class\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const body = await request.json();\r\n    const { name, description, teacherId, institutionId, coverPicture } = body;\r\n\r\n    // Validate required fields\r\n    if (!name || !teacherId || !institutionId) {\r\n      return NextResponse.json(\r\n        { error: 'Name, teacher ID, and institution ID are required' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify teacher exists and belongs to the institution\r\n    const teacher = await db\r\n      .select()\r\n      .from(users)\r\n      .where(\r\n        and(\r\n          eq(users.id, teacherId),\r\n          eq(users.institutionId, institutionId),\r\n          eq(users.role, 'teacher')\r\n        )\r\n      )\r\n      .limit(1);\r\n\r\n    if (teacher.length === 0) {\r\n      return NextResponse.json(\r\n        { error: 'Teacher not found or not authorized' },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Create the class\r\n    const newClass = await db\r\n      .insert(classes)\r\n      .values({\r\n        name,\r\n        description,\r\n        teacherId,\r\n        institutionId,\r\n        coverPicture\r\n      })\r\n      .returning();\r\n\r\n    return NextResponse.json(\r\n      { success: true, class: newClass[0], message: 'Class created successfully' },\r\n      { status: 201 }\r\n    );\r\n  } catch (error) {\r\n    console.error('Error creating class:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/classes',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\classes\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/classes/route\",\n        pathname: \"/api/classes\",\n        filename: \"route\",\n        bundlePath: \"app/api/classes/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\File Kerja <PERSON>\\\\GAWEAN\\\\TERANG_LMS\\\\terang-lms-frontend\\\\src\\\\app\\\\api\\\\classes\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "request", "teacherId", "nextUrl", "searchParams", "get", "NextResponse", "json", "success", "error", "status", "classesWithCounts", "teacherClasses", "db", "select", "id", "classes", "name", "description", "institutionId", "coverPicture", "createdAt", "updatedAt", "from", "where", "eq", "parseInt", "map", "cls", "studentCount", "courseCount", "console", "POST", "body", "teacher", "users", "and", "role", "limit", "length", "newClass", "insert", "values", "returning", "class", "message", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}